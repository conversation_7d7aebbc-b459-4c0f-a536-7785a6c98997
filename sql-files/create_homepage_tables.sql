-- HomePage Interaction Table
CREATE TABLE IF NOT EXISTS homepage_interactions (
    id SERIAL PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(255),
    email VARCHAR(255) NOT NULL,
    linkedin VARCHAR(500),
    phonenumber VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- HomePage Metrics Table
CREATE TABLE IF NOT EXISTS homepage_metrics (
    id SERIAL PRIMARY KEY,
    sofr DECIMAL(5,3),
    sofr_30_day_avg DECIMAL(5,3),
    wsj_prime_rate DECIMAL(5,3),
    treasury_5_year DECIMAL(5,3),
    treasury_10_year DECIMAL(5,3),
    effective_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Homepage Deals Table
CREATE TABLE IF NOT EXISTS homepage_deals (
    id SERIAL PRIMARY KEY,
    position VARCHAR(255),
    financing_type VARCHAR(255),
    amount_required VARCHAR(255),
    ltv VARCHAR(100),
    property_type VARCHAR(255),
    location VARCHAR(500),
    description TEXT,
    image_url VARCHAR(500),
    external_link VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_homepage_interactions_email ON homepage_interactions(email);
CREATE INDEX IF NOT EXISTS idx_homepage_interactions_created_at ON homepage_interactions(created_at);
CREATE INDEX IF NOT EXISTS idx_homepage_metrics_effective_date ON homepage_metrics(effective_date);
CREATE INDEX IF NOT EXISTS idx_homepage_deals_created_at ON homepage_deals(created_at);

-- Add updated_at trigger for homepage_interactions
CREATE OR REPLACE FUNCTION update_homepage_interactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_homepage_interactions_updated_at
    BEFORE UPDATE ON homepage_interactions
    FOR EACH ROW
    EXECUTE FUNCTION update_homepage_interactions_updated_at();

-- Add updated_at trigger for homepage_metrics
CREATE OR REPLACE FUNCTION update_homepage_metrics_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_homepage_metrics_updated_at
    BEFORE UPDATE ON homepage_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_homepage_metrics_updated_at();

-- Add updated_at trigger for homepage_deals
CREATE OR REPLACE FUNCTION update_homepage_deals_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_homepage_deals_updated_at
    BEFORE UPDATE ON homepage_deals
    FOR EACH ROW
    EXECUTE FUNCTION update_homepage_deals_updated_at();
