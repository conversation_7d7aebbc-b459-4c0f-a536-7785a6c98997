--
-- PostgreSQL database dump
--

-- Dumped from database version 17.2 (Ubuntu 17.2-1.pgdg22.04+1)
-- Dumped by pg_dump version 17.2 (Ubuntu 17.2-1.pgdg22.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: update_conversation_timestamp(); Type: FUNCTION; Schema: public; Owner: anax_user
--

CREATE FUNCTION public.update_conversation_timestamp() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE conversations 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE conversation_id = NEW.conversation_id;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_conversation_timestamp() OWNER TO anax_user;

--
-- Name: update_modified_column(); Type: FUNCTION; Schema: public; Owner: anax_user
--

CREATE FUNCTION public.update_modified_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_modified_column() OWNER TO anax_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: action_results; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.action_results (
    result_id uuid DEFAULT gen_random_uuid() NOT NULL,
    nba_id uuid NOT NULL,
    outcome_type character varying(50) NOT NULL,
    result_data jsonb,
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.action_results OWNER TO anax_user;

--
-- Name: article_sources; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.article_sources (
    id integer NOT NULL,
    news_source character varying(50),
    url text NOT NULL,
    fetched boolean DEFAULT false,
    content text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.article_sources OWNER TO anax_user;

--
-- Name: article_sources_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.article_sources_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.article_sources_id_seq OWNER TO anax_user;

--
-- Name: article_sources_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.article_sources_id_seq OWNED BY public.article_sources.id;


--
-- Name: bizdata; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.bizdata (
    id integer NOT NULL,
    company_name text,
    address text,
    city text,
    state character(2),
    zip text,
    county text,
    phone character varying(15),
    contact_first_name text,
    contact_last_name text,
    title text,
    direct_phone text,
    email text,
    website text,
    employee_range text,
    annual_sales text,
    sic_code text,
    industry text
);


ALTER TABLE public.bizdata OWNER TO anax_user;

--
-- Name: bizdata_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.bizdata_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.bizdata_id_seq OWNER TO anax_user;

--
-- Name: bizdata_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.bizdata_id_seq OWNED BY public.bizdata.id;


--
-- Name: person_capital_type; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_capital_type (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.person_capital_type OWNER TO anax_user;

--
-- Name: capital_type_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.capital_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.capital_type_id_seq OWNER TO anax_user;

--
-- Name: capital_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.capital_type_id_seq OWNED BY public.person_capital_type.id;


--
-- Name: clean_criteria_values; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.clean_criteria_values (
    id integer NOT NULL,
    field character varying(255) NOT NULL,
    value character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.clean_criteria_values OWNER TO anax_user;

--
-- Name: clean_criteria_values_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.clean_criteria_values_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.clean_criteria_values_id_seq OWNER TO anax_user;

--
-- Name: clean_criteria_values_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.clean_criteria_values_id_seq OWNED BY public.clean_criteria_values.id;


--
-- Name: companies; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.companies (
    company_id integer NOT NULL,
    company_name text,
    company_linkedin text,
    company_address text,
    company_city text,
    company_state text,
    company_zip text,
    company_website text,
    industry text
);


ALTER TABLE public.companies OWNER TO anax_user;

--
-- Name: companies_company_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.companies_company_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.companies_company_id_seq OWNER TO anax_user;

--
-- Name: companies_company_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.companies_company_id_seq OWNED BY public.companies.company_id;


--
-- Name: company_web_data; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.company_web_data (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    company_id integer NOT NULL,
    url text NOT NULL,
    page_type character varying(50),
    content text,
    extracted_text text,
    meta_description text,
    meta_keywords text[],
    last_scraped_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.company_web_data OWNER TO anax_user;

--
-- Name: conversation_context; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.conversation_context (
    context_id uuid DEFAULT gen_random_uuid() NOT NULL,
    conversation_id uuid NOT NULL,
    context_type character varying(100) NOT NULL,
    context_data jsonb NOT NULL,
    relevance_score double precision,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    expires_at timestamp with time zone
);


ALTER TABLE public.conversation_context OWNER TO anax_user;

--
-- Name: conversation_files; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.conversation_files (
    file_id uuid DEFAULT gen_random_uuid() NOT NULL,
    conversation_id uuid NOT NULL,
    message_id uuid,
    file_name text NOT NULL,
    file_type character varying(100),
    storage_path text NOT NULL,
    size_bytes bigint,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.conversation_files OWNER TO anax_user;

--
-- Name: conversations; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.conversations (
    conversation_id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer NOT NULL,
    title text,
    status character varying(50) DEFAULT 'active'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    metadata jsonb
);


ALTER TABLE public.conversations OWNER TO anax_user;

--
-- Name: COLUMN conversations.metadata; Type: COMMENT; Schema: public; Owner: anax_user
--

COMMENT ON COLUMN public.conversations.metadata IS 'Example structure:
{
    "source": "email/web/mobile",
    "language": "en",
    "timezone": "UTC-5",
    "tags": ["investment", "property_type_discussion"],
    "user_preferences": {
        "communication_style": "formal",
        "preferred_contact_method": "email"
    }
}';


--
-- Name: engagement_workflows; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.engagement_workflows (
    workflow_id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer,
    stage character varying(50),
    status character varying(50),
    context_data jsonb,
    next_action_date timestamp with time zone,
    assigned_to character varying(100),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.engagement_workflows OWNER TO anax_user;

--
-- Name: industries; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.industries (
    industry_id integer NOT NULL,
    industry_name character varying(255) NOT NULL
);


ALTER TABLE public.industries OWNER TO anax_user;

--
-- Name: industries_industry_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.industries_industry_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.industries_industry_id_seq OWNER TO anax_user;

--
-- Name: industries_industry_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.industries_industry_id_seq OWNED BY public.industries.industry_id;


--
-- Name: investment_criteria; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria (
    id integer NOT NULL,
    criteria character varying(100) NOT NULL,
    value text NOT NULL
);


ALTER TABLE public.investment_criteria OWNER TO anax_user;

--
-- Name: investment_criteria_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_id_seq OWNED BY public.investment_criteria.id;


--
-- Name: loan_interest_rate_indices; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.loan_interest_rate_indices (
    index_id integer NOT NULL,
    index_name character varying(100) NOT NULL
);


ALTER TABLE public.loan_interest_rate_indices OWNER TO anax_user;

--
-- Name: loan_interest_rate_indices_index_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.loan_interest_rate_indices_index_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.loan_interest_rate_indices_index_id_seq OWNER TO anax_user;

--
-- Name: loan_interest_rate_indices_index_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.loan_interest_rate_indices_index_id_seq OWNED BY public.loan_interest_rate_indices.index_id;


--
-- Name: messages; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.messages (
    message_id uuid DEFAULT gen_random_uuid() NOT NULL,
    conversation_id uuid NOT NULL,
    role character varying(50) NOT NULL,
    content text NOT NULL,
    tokens_used integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    metadata jsonb
);


ALTER TABLE public.messages OWNER TO anax_user;

--
-- Name: next_best_actions; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.next_best_actions (
    nba_id uuid DEFAULT gen_random_uuid() NOT NULL,
    conversation_id uuid NOT NULL,
    action_type character varying(100) NOT NULL,
    priority integer NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying,
    due_date timestamp with time zone,
    description text,
    reasoning text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    completed_at timestamp with time zone,
    created_by character varying(100),
    metadata jsonb
);


ALTER TABLE public.next_best_actions OWNER TO anax_user;

--
-- Name: COLUMN next_best_actions.metadata; Type: COMMENT; Schema: public; Owner: anax_user
--

COMMENT ON COLUMN public.next_best_actions.metadata IS 'Example structure:
{
    "email_template": "investment_followup",
    "required_documents": ["proof_of_funds", "investment_criteria"],
    "reminder_frequency": "daily",
    "importance_factors": {
        "deal_size": "high",
        "urgency": "medium",
        "relationship_stage": "initial_contact"
    }
}';


--
-- Name: person_communications; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_communications (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer,
    channel character varying(50),
    direction character varying(20),
    content text,
    metadata jsonb,
    sent_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    status character varying(50),
    conversation_id uuid,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.person_communications OWNER TO anax_user;

--
-- Name: person_interests; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_interests (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer NOT NULL,
    interest_type character varying(50),
    interest text,
    source text,
    confidence_score double precision,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    last_confirmed_at timestamp with time zone
);


ALTER TABLE public.person_interests OWNER TO anax_user;

--
-- Name: person_investment_criteria; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_investment_criteria (
    id integer NOT NULL,
    person_id integer NOT NULL,
    investment_criteria_country text,
    investment_criteria_geographic_region text,
    investment_criteria_state text,
    investment_criteria_city text,
    investment_criteria_deal_size text,
    investment_criteria_property_type text,
    investment_criteria_property_type_subcategory text,
    investment_criteria_asset_type text,
    investment_criteria_loan_type text,
    investment_criteria_loan_type_short_term text,
    investment_criteria_loan_type_long_term text,
    investment_criteria_loan_term_years text,
    investment_criteria_loan_interest_rate_basis text,
    investment_criteria_loan_interest_rate text,
    investment_criteria_loan_to_value text,
    investment_criteria_loan_to_cost text,
    investment_criteria_loan_origination_fee_pct text,
    investment_criteria_loan_exit_fee_pct text,
    investment_criteria_recourse_loan text,
    investment_criteria_loan_dscr text,
    investment_criteria_closing_time text,
    investment_criteria_tear_sheet text,
    investment_preferences jsonb,
    geographic_preferences jsonb,
    property_preferences jsonb,
    loan_preferences jsonb,
    last_updated timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.person_investment_criteria OWNER TO anax_user;

--
-- Name: person_investment_criteria_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.person_investment_criteria_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.person_investment_criteria_id_seq OWNER TO anax_user;

--
-- Name: person_investment_criteria_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.person_investment_criteria_id_seq OWNED BY public.person_investment_criteria.id;


--
-- Name: person_linkedin_data; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_linkedin_data (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer NOT NULL,
    profile_url text,
    headline text,
    about text,
    experience jsonb[],
    education jsonb[],
    skills text[],
    certifications jsonb[],
    languages text[],
    volunteer_work jsonb[],
    last_scraped_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.person_linkedin_data OWNER TO anax_user;

--
-- Name: person_news_mentions; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_news_mentions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer NOT NULL,
    title text NOT NULL,
    content text,
    url text,
    publisher text,
    published_date timestamp with time zone,
    mention_context text,
    sentiment_score double precision,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.person_news_mentions OWNER TO anax_user;

--
-- Name: person_publications; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_publications (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer NOT NULL,
    title text NOT NULL,
    abstract text,
    authors text[],
    publication_date date,
    publisher text,
    url text,
    type character varying(50),
    topics text[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.person_publications OWNER TO anax_user;

--
-- Name: person_synthesis; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_synthesis (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer NOT NULL,
    professional_summary text,
    key_interests text[],
    investment_preferences jsonb,
    decision_making_style text,
    communication_preferences jsonb,
    relationship_strength text,
    engagement_topics text[],
    suggested_approaches text[],
    pain_points text[],
    opportunities text[],
    risk_factors text[],
    last_interaction_summary text,
    next_steps text[],
    last_updated timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    confidence_score double precision,
    data_freshness_score double precision
);


ALTER TABLE public.person_synthesis OWNER TO anax_user;

--
-- Name: TABLE person_synthesis; Type: COMMENT; Schema: public; Owner: anax_user
--

COMMENT ON TABLE public.person_synthesis IS 'Stores AI-generated synthesis of person information to guide engagement';


--
-- Name: COLUMN person_synthesis.investment_preferences; Type: COMMENT; Schema: public; Owner: anax_user
--

COMMENT ON COLUMN public.person_synthesis.investment_preferences IS '
{
    "preferred_deal_size": {
        "min": 1000000,
        "max": 5000000,
        "currency": "USD"
    },
    "preferred_sectors": ["commercial real estate", "retail"],
    "risk_tolerance": "moderate",
    "investment_style": "value-oriented",
    "geographic_preferences": ["Northeast US", "West Coast"],
    "past_investment_patterns": {
        "typical_check_size": "2-3M",
        "preferred_structures": ["equity", "mezzanine"],
        "exit_timeline": "5-7 years"
    }
}';


--
-- Name: COLUMN person_synthesis.communication_preferences; Type: COMMENT; Schema: public; Owner: anax_user
--

COMMENT ON COLUMN public.person_synthesis.communication_preferences IS '
{
    "preferred_channel": "email",
    "best_time": "morning",
    "style": "direct",
    "frequency": "weekly",
    "language_tone": "formal",
    "follow_up_preference": "phone"
}';


--
-- Name: persons; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.persons (
    capital_type text,
    contact_source text,
    email text,
    first_name text,
    last_name text,
    phone_number text,
    person_linkedin text,
    job_title text,
    notes text,
    person_id integer NOT NULL,
    guid uuid,
    active_campaign_id text,
    company_id integer
);


ALTER TABLE public.persons OWNER TO anax_user;

--
-- Name: user_interactions; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.user_interactions (
    interaction_id integer NOT NULL,
    user_id integer,
    person_id integer,
    interaction_type character varying(50),
    interaction_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    notes text
);


ALTER TABLE public.user_interactions OWNER TO anax_user;

--
-- Name: user_interactions_interaction_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.user_interactions_interaction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_interactions_interaction_id_seq OWNER TO anax_user;

--
-- Name: user_interactions_interaction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.user_interactions_interaction_id_seq OWNED BY public.user_interactions.interaction_id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.users (
    user_id integer NOT NULL,
    username character varying(50) NOT NULL,
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    role character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.users OWNER TO anax_user;

--
-- Name: users_user_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.users_user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_user_id_seq OWNER TO anax_user;

--
-- Name: users_user_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.users_user_id_seq OWNED BY public.users.user_id;


--
-- Name: article_sources id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.article_sources ALTER COLUMN id SET DEFAULT nextval('public.article_sources_id_seq'::regclass);


--
-- Name: bizdata id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.bizdata ALTER COLUMN id SET DEFAULT nextval('public.bizdata_id_seq'::regclass);


--
-- Name: clean_criteria_values id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.clean_criteria_values ALTER COLUMN id SET DEFAULT nextval('public.clean_criteria_values_id_seq'::regclass);


--
-- Name: companies company_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.companies ALTER COLUMN company_id SET DEFAULT nextval('public.companies_company_id_seq'::regclass);


--
-- Name: industries industry_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.industries ALTER COLUMN industry_id SET DEFAULT nextval('public.industries_industry_id_seq'::regclass);


--
-- Name: investment_criteria id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_id_seq'::regclass);


--
-- Name: loan_interest_rate_indices index_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.loan_interest_rate_indices ALTER COLUMN index_id SET DEFAULT nextval('public.loan_interest_rate_indices_index_id_seq'::regclass);


--
-- Name: person_capital_type id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_capital_type ALTER COLUMN id SET DEFAULT nextval('public.capital_type_id_seq'::regclass);


--
-- Name: person_investment_criteria id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_investment_criteria ALTER COLUMN id SET DEFAULT nextval('public.person_investment_criteria_id_seq'::regclass);


--
-- Name: user_interactions interaction_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.user_interactions ALTER COLUMN interaction_id SET DEFAULT nextval('public.user_interactions_interaction_id_seq'::regclass);


--
-- Name: users user_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.users ALTER COLUMN user_id SET DEFAULT nextval('public.users_user_id_seq'::regclass);


--
-- Name: action_results action_results_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.action_results
    ADD CONSTRAINT action_results_pkey PRIMARY KEY (result_id);


--
-- Name: article_sources article_sources_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.article_sources
    ADD CONSTRAINT article_sources_pkey PRIMARY KEY (id);


--
-- Name: bizdata bizdata_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.bizdata
    ADD CONSTRAINT bizdata_pkey PRIMARY KEY (id);


--
-- Name: person_capital_type capital_type_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_capital_type
    ADD CONSTRAINT capital_type_pkey PRIMARY KEY (id);


--
-- Name: person_capital_type capital_type_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_capital_type
    ADD CONSTRAINT capital_type_value_key UNIQUE (value);


--
-- Name: clean_criteria_values clean_criteria_values_field_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.clean_criteria_values
    ADD CONSTRAINT clean_criteria_values_field_value_key UNIQUE (field, value);


--
-- Name: clean_criteria_values clean_criteria_values_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.clean_criteria_values
    ADD CONSTRAINT clean_criteria_values_pkey PRIMARY KEY (id);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (company_id);



--
-- Name: company_web_data company_web_data_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.company_web_data
    ADD CONSTRAINT company_web_data_pkey PRIMARY KEY (id);


--
-- Name: conversation_context conversation_context_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_context
    ADD CONSTRAINT conversation_context_pkey PRIMARY KEY (context_id);


--
-- Name: conversation_files conversation_files_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_files
    ADD CONSTRAINT conversation_files_pkey PRIMARY KEY (file_id);


--
-- Name: conversations conversations_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_pkey PRIMARY KEY (conversation_id);


--
-- Name: engagement_workflows engagement_workflows_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.engagement_workflows
    ADD CONSTRAINT engagement_workflows_pkey PRIMARY KEY (workflow_id);


--
-- Name: industries industries_industry_name_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.industries
    ADD CONSTRAINT industries_industry_name_key UNIQUE (industry_name);


--
-- Name: industries industries_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.industries
    ADD CONSTRAINT industries_pkey PRIMARY KEY (industry_id);


--
-- Name: investment_criteria investment_criteria_criteria_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria
    ADD CONSTRAINT investment_criteria_criteria_value_key UNIQUE (criteria, value);


--
-- Name: investment_criteria investment_criteria_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria
    ADD CONSTRAINT investment_criteria_pkey PRIMARY KEY (id);


--
-- Name: loan_interest_rate_indices loan_interest_rate_indices_index_name_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.loan_interest_rate_indices
    ADD CONSTRAINT loan_interest_rate_indices_index_name_key UNIQUE (index_name);


--
-- Name: loan_interest_rate_indices loan_interest_rate_indices_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.loan_interest_rate_indices
    ADD CONSTRAINT loan_interest_rate_indices_pkey PRIMARY KEY (index_id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (message_id);


--
-- Name: next_best_actions next_best_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.next_best_actions
    ADD CONSTRAINT next_best_actions_pkey PRIMARY KEY (nba_id);


--
-- Name: person_communications person_communications_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_communications
    ADD CONSTRAINT person_communications_pkey PRIMARY KEY (id);


--
-- Name: person_interests person_interests_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_interests
    ADD CONSTRAINT person_interests_pkey PRIMARY KEY (id);


--
-- Name: person_investment_criteria person_investment_criteria_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_investment_criteria
    ADD CONSTRAINT person_investment_criteria_pkey PRIMARY KEY (id);


--
-- Name: person_linkedin_data person_linkedin_data_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_linkedin_data
    ADD CONSTRAINT person_linkedin_data_pkey PRIMARY KEY (id);


--
-- Name: person_news_mentions person_news_mentions_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_news_mentions
    ADD CONSTRAINT person_news_mentions_pkey PRIMARY KEY (id);


--
-- Name: person_publications person_publications_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_publications
    ADD CONSTRAINT person_publications_pkey PRIMARY KEY (id);


--
-- Name: person_synthesis person_synthesis_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_synthesis
    ADD CONSTRAINT person_synthesis_pkey PRIMARY KEY (id);


--
-- Name: persons persons_person_id_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.persons
    ADD CONSTRAINT persons_person_id_pkey PRIMARY KEY (person_id);


--
-- Name: user_interactions user_interactions_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.user_interactions
    ADD CONSTRAINT user_interactions_pkey PRIMARY KEY (interaction_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: idx_company_name_ci; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_company_name_ci ON public.bizdata USING btree (lower(company_name));


--
-- Name: idx_company_web_data_company; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_company_web_data_company ON public.company_web_data USING btree (company_id);


--
-- Name: idx_contact_name_ci; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_contact_name_ci ON public.bizdata USING btree (lower(contact_first_name), lower(contact_last_name));


--
-- Name: idx_context_conversation; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_context_conversation ON public.conversation_context USING btree (conversation_id);


--
-- Name: idx_conversations_person; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_conversations_person ON public.conversations USING btree (person_id);


--
-- Name: idx_email_ci; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_email_ci ON public.bizdata USING btree (lower(email));


--
-- Name: idx_engagement_workflows_person; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_engagement_workflows_person ON public.engagement_workflows USING btree (person_id);


--
-- Name: idx_engagement_workflows_stage; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_engagement_workflows_stage ON public.engagement_workflows USING btree (stage);


--
-- Name: idx_industry_ci; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_industry_ci ON public.bizdata USING btree (lower(industry));


--
-- Name: idx_messages_conversation; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_messages_conversation ON public.messages USING btree (conversation_id);


--
-- Name: idx_nba_conversation; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_nba_conversation ON public.next_best_actions USING btree (conversation_id);


--
-- Name: idx_nba_status; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_nba_status ON public.next_best_actions USING btree (status);


--
-- Name: idx_person_communications_person; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_person_communications_person ON public.person_communications USING btree (person_id);


--
-- Name: idx_person_interests_person; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_person_interests_person ON public.person_interests USING btree (person_id);


--
-- Name: idx_person_interests_type; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_person_interests_type ON public.person_interests USING btree (interest_type);


--
-- Name: idx_person_linkedin_person; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_person_linkedin_person ON public.person_linkedin_data USING btree (person_id);


--
-- Name: idx_person_news_date; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_person_news_date ON public.person_news_mentions USING btree (published_date);


--
-- Name: idx_person_news_person; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_person_news_person ON public.person_news_mentions USING btree (person_id);


--
-- Name: idx_person_publications_person; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_person_publications_person ON public.person_publications USING btree (person_id);


--
-- Name: idx_title_ci; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_title_ci ON public.bizdata USING btree (lower(title));

--
-- Name: messages update_conversation_timestamp; Type: TRIGGER; Schema: public; Owner: anax_user
--

CREATE TRIGGER update_conversation_timestamp AFTER INSERT ON public.messages FOR EACH ROW EXECUTE FUNCTION public.update_conversation_timestamp();


--
-- Name: engagement_workflows update_engagement_workflows_modtime; Type: TRIGGER; Schema: public; Owner: anax_user
--

CREATE TRIGGER update_engagement_workflows_modtime BEFORE UPDATE ON public.engagement_workflows FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();


--
-- Name: action_results action_results_nba_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.action_results
    ADD CONSTRAINT action_results_nba_id_fkey FOREIGN KEY (nba_id) REFERENCES public.next_best_actions(nba_id);



--
-- Name: company_web_data company_web_data_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.company_web_data
    ADD CONSTRAINT company_web_data_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(company_id);


--
-- Name: conversation_context conversation_context_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_context
    ADD CONSTRAINT conversation_context_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: conversation_files conversation_files_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_files
    ADD CONSTRAINT conversation_files_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: conversation_files conversation_files_message_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_files
    ADD CONSTRAINT conversation_files_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(message_id);


--
-- Name: conversations conversations_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: engagement_workflows engagement_workflows_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.engagement_workflows
    ADD CONSTRAINT engagement_workflows_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: messages messages_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: next_best_actions next_best_actions_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.next_best_actions
    ADD CONSTRAINT next_best_actions_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: person_communications person_communications_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_communications
    ADD CONSTRAINT person_communications_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: person_communications person_communications_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_communications
    ADD CONSTRAINT person_communications_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: person_interests person_interests_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_interests
    ADD CONSTRAINT person_interests_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: person_investment_criteria person_investment_criteria_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_investment_criteria
    ADD CONSTRAINT person_investment_criteria_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id) ON DELETE CASCADE;


--
-- Name: person_linkedin_data person_linkedin_data_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_linkedin_data
    ADD CONSTRAINT person_linkedin_data_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: person_news_mentions person_news_mentions_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_news_mentions
    ADD CONSTRAINT person_news_mentions_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: person_publications person_publications_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_publications
    ADD CONSTRAINT person_publications_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: person_synthesis person_synthesis_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_synthesis
    ADD CONSTRAINT person_synthesis_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: user_interactions user_interactions_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.user_interactions
    ADD CONSTRAINT user_interactions_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id) ON DELETE CASCADE;


--
-- Name: user_interactions user_interactions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.user_interactions
    ADD CONSTRAINT user_interactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE;


--
-- Name: deal_data_pivoted; Type: TABLE; Schema: public; Owner: anax_user
--


--
-- PostgreSQL database dump complete
--

