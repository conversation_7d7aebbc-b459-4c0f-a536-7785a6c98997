-- Create user edit logs table for audit trail functionality
-- This table tracks all user actions and changes for security and audit purposes

CREATE TABLE IF NOT EXISTS user_edit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    action VARCHAR(100) NOT NULL,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comment for documentation
COMMENT ON TABLE user_edit_logs IS 'Audit trail table tracking all user actions and changes';
COMMENT ON COLUMN user_edit_logs.action IS 'Type of action performed (login, profile_update, password_change, etc.)';
COMMENT ON COLUMN user_edit_logs.details IS 'JSON object containing additional action details';
COMMENT ON COLUMN user_edit_logs.ip_address IS 'IP address of the user when the action was performed';
COMMENT ON COLUMN user_edit_logs.user_agent IS 'User agent string from the browser/client';
