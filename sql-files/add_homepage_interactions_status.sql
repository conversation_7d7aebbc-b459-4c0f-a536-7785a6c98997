-- Add status field to homepage_interactions table
ALTER TABLE homepage_interactions ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'pending';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_homepage_interactions_status ON homepage_interactions(status);

-- Update existing records to have 'pending' status (if any exist)
UPDATE homepage_interactions SET status = 'pending' WHERE status IS NULL;

-- Add comment for the status field
COMMENT ON COLUMN homepage_interactions.status IS 'Status of the interaction: pending, valid, invalid';
