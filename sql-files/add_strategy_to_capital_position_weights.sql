-- Add strategy field to capital position field weights table
-- This adds strategy as a universal field that can be used across all capital positions
-- Date: 2025-01-18
-- Description: Adding strategy field to enable strategy-based matching in capital position matching

-- Add COMMON strategy field weight (universal for all capital positions)
-- Single weight entry using normalized field name - handles both 'strategy' and 'strategies' variations
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('COMMON', 'strategy', 0.15, 'Strategy/Strategies matching for all capital positions - 15% (universal weight, handles both singular and plural)');

-- Note: No need to update existing weights since COMMON is already used for universal fields
-- The strategy field will be applied to all capital positions through the COMMON weight

-- Add comment to document the strategy field addition
COMMENT ON COLUMN capital_position_field_weights.field_name IS 'Field name for scoring, now includes strategy as a universal field for all capital positions';

-- IMPLEMENTATION NOTE:
-- The matching system now uses field name normalization to handle singular/plural variations
-- with a single weight entry. The normalized field names are:
-- - 'strategy' (handles both 'strategy' and 'strategies')
-- - 'property_type' (handles both 'property_type' and 'property_types')
-- - 'subproperty_type' (handles both 'subproperty_type' and 'subproperty_types')
-- - 'num_apartment_units' (handles both 'num_apartment_units' and 'number_of_units')
-- - 'hold_period' (handles 'hold_period', 'min_hold_period_years', 'max_hold_period_years')
-- This approach reduces database entries and simplifies weight management.
