-- Create user notifications table for the authentication system
-- This table stores notifications for users including system alerts, updates, and messages

CREATE TABLE IF NOT EXISTS user_notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL
    

);

    -- Add indexes for performance
 CREATE INDEX idx_user_notifications_user_id (user_id);
    CREATE INDEX idx_user_notifications_is_read (is_read);
    CREATE INDEX idx_user_notifications_created_at (created_at);
    CREATE INDEX idx_user_notifications_type (type);

-- Add comment for documentation
COMMENT ON TABLE user_notifications IS 'Stores user notifications for the dashboard application';
COMMENT ON COLUMN user_notifications.type IS 'Notification type: info, warning, error, or success';
COMMENT ON COLUMN user_notifications.is_read IS 'Whether the user has read this notification';
COMMENT ON COLUMN user_notifications.read_at IS 'Timestamp when the notification was marked as read';
