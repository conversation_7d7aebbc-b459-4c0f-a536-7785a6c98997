-- =====================================================
-- INVESTMENT CRITERIA GEOGRAPHIC INCONSISTENCY FIX
-- =====================================================
-- This script fixes geographic inconsistencies in investment_criteria_central table
-- where region selections don't match the complete state arrays from central_mapping

-- =====================================================
-- STEP 1: ANALYSIS QUERIES
-- =====================================================

-- Query 1: Find all records with mismatched region-state relationships
WITH region_state_mapping AS (
  SELECT 
    value_1 as region_name,
    ARRAY_AGG(value_2 ORDER BY value_2) as expected_states
  FROM central_mapping 
  WHERE type = 'U.S Regions' 
    AND is_active = true 
    AND value_2 IS NOT NULL
  GROUP BY value_1
),
current_criteria AS (
  SELECT 
    investment_criteria_id,
    entity_type,
    entity_id,
    region,
    state,
    country
  FROM investment_criteria_central 
  WHERE region IS NOT NULL 
    AND state IS NOT NULL
    AND array_length(region, 1) > 0
    AND array_length(state, 1) > 0
)
SELECT 
  cc.investment_criteria_id,
  cc.entity_type,
  cc.entity_id,
  cc.region,
  cc.state as current_states,
  rsm.expected_states,
  CASE 
    WHEN rsm.expected_states IS NULL THEN 'REGION_NOT_FOUND_IN_MAPPING'
    WHEN NOT (cc.state @> rsm.expected_states AND rsm.expected_states @> cc.state) THEN 'MISMATCH'
    ELSE 'CONSISTENT'
  END as status
FROM current_criteria cc
LEFT JOIN region_state_mapping rsm ON rsm.region_name = ANY(cc.region)
WHERE rsm.expected_states IS NULL 
   OR NOT (cc.state @> rsm.expected_states AND rsm.expected_states @> cc.state)
ORDER BY cc.investment_criteria_id;

-- Query 2: Summary of inconsistencies
WITH region_state_mapping AS (
  SELECT 
    value_1 as region_name,
    ARRAY_AGG(value_2 ORDER BY value_2) as expected_states
  FROM central_mapping 
  WHERE type = 'U.S Regions' 
    AND is_active = true 
    AND value_2 IS NOT NULL
  GROUP BY value_1
),
current_criteria AS (
  SELECT 
    investment_criteria_id,
    entity_type,
    entity_id,
    region,
    state,
    country
  FROM investment_criteria_central 
  WHERE region IS NOT NULL 
    AND state IS NOT NULL
    AND array_length(region, 1) > 0
    AND array_length(state, 1) > 0
),
inconsistencies AS (
  SELECT 
    cc.investment_criteria_id,
    cc.entity_type,
    cc.entity_id,
    cc.region,
    cc.state as current_states,
    rsm.expected_states,
    CASE 
      WHEN rsm.expected_states IS NULL THEN 'REGION_NOT_FOUND_IN_MAPPING'
      WHEN NOT (cc.state @> rsm.expected_states AND rsm.expected_states @> cc.state) THEN 'MISMATCH'
      ELSE 'CONSISTENT'
    END as status
  FROM current_criteria cc
  LEFT JOIN region_state_mapping rsm ON rsm.region_name = ANY(cc.region)
  WHERE rsm.expected_states IS NULL 
     OR NOT (cc.state @> rsm.expected_states AND rsm.expected_states @> cc.state)
)
SELECT 
  status,
  COUNT(*) as count,
  COUNT(DISTINCT entity_id) as unique_entities,
  COUNT(DISTINCT entity_type) as entity_types
FROM inconsistencies
GROUP BY status
ORDER BY count DESC;

-- Query 3: Find regions not in mapping
WITH region_state_mapping AS (
  SELECT DISTINCT value_1 as region_name
  FROM central_mapping 
  WHERE type = 'U.S Regions' 
    AND is_active = true
),
current_regions AS (
  SELECT DISTINCT unnest(region) as region_name
  FROM investment_criteria_central 
  WHERE region IS NOT NULL 
    AND array_length(region, 1) > 0
)
SELECT 
  cr.region_name,
  COUNT(*) as usage_count
FROM current_regions cr
LEFT JOIN region_state_mapping rsm ON cr.region_name = rsm.region_name
WHERE rsm.region_name IS NULL
GROUP BY cr.region_name
ORDER BY usage_count DESC;

-- =====================================================
-- STEP 2: FIX QUERIES
-- =====================================================

-- Fix 1: Update records with valid regions to have complete state arrays
-- This handles cases like "Nationwide" missing some states, "Northeast" missing some states, etc.

WITH region_state_mapping AS (
  SELECT 
    value_1 as region_name,
    ARRAY_AGG(value_2 ORDER BY value_2) as expected_states
  FROM central_mapping 
  WHERE type = 'U.S Regions' 
    AND is_active = true 
    AND value_2 IS NOT NULL
  GROUP BY value_1
),
records_to_fix AS (
  SELECT DISTINCT
    icc.investment_criteria_id,
    icc.entity_type,
    icc.entity_id,
    icc.region,
    icc.state as current_states,
    -- Get all unique states that should be included based on all regions
    ARRAY_AGG(DISTINCT unnest(rsm.expected_states)) as all_expected_states
  FROM investment_criteria_central icc
  JOIN region_state_mapping rsm ON rsm.region_name = ANY(icc.region)
  WHERE icc.region IS NOT NULL 
    AND icc.state IS NOT NULL
    AND array_length(icc.region, 1) > 0
    AND array_length(icc.state, 1) > 0
    AND NOT (icc.state @> rsm.expected_states AND rsm.expected_states @> icc.state)
  GROUP BY icc.investment_criteria_id, icc.entity_type, icc.entity_id, icc.region, icc.state
)
UPDATE investment_criteria_central 
SET state = all_expected_states
FROM records_to_fix rtf
WHERE investment_criteria_central.investment_criteria_id = rtf.investment_criteria_id;

-- Fix 2: Handle specific common region mappings
-- Map common unmapped regions to existing regions

-- Example: Map "New York City Metropolitan Area" to "Northeast"
UPDATE investment_criteria_central 
SET region = ARRAY['Northeast']
WHERE 'New York City Metropolitan Area' = ANY(region);

-- Example: Map "Financial District" to "Northeast" 
UPDATE investment_criteria_central 
SET region = ARRAY['Northeast']
WHERE 'Financial District' = ANY(region);

-- Example: Map "Tri-State Area" to "Northeast"
UPDATE investment_criteria_central 
SET region = ARRAY['Northeast']
WHERE 'Tri-State Area' = ANY(region);

-- =====================================================
-- STEP 3: VALIDATION QUERIES
-- =====================================================

-- Query to verify fixes worked
WITH region_state_mapping AS (
  SELECT 
    value_1 as region_name,
    ARRAY_AGG(value_2 ORDER BY value_2) as expected_states
  FROM central_mapping 
  WHERE type = 'U.S Regions' 
    AND is_active = true 
    AND value_2 IS NOT NULL
  GROUP BY value_1
),
current_criteria AS (
  SELECT 
    investment_criteria_id,
    entity_type,
    entity_id,
    region,
    state,
    country
  FROM investment_criteria_central 
  WHERE region IS NOT NULL 
    AND state IS NOT NULL
    AND array_length(region, 1) > 0
    AND array_length(state, 1) > 0
)
SELECT 
  COUNT(*) as remaining_inconsistencies
FROM current_criteria cc
LEFT JOIN region_state_mapping rsm ON rsm.region_name = ANY(cc.region)
WHERE rsm.expected_states IS NULL 
   OR NOT (cc.state @> rsm.expected_states AND rsm.expected_states @> cc.state);

-- =====================================================
-- NOTES
-- =====================================================
-- 1. Run the analysis queries first to understand the scope
-- 2. Review the unmapped regions and decide on mappings
-- 3. Execute the fix queries in order
-- 4. Run validation queries to confirm fixes
-- 5. Consider adding constraints to prevent future inconsistencies
