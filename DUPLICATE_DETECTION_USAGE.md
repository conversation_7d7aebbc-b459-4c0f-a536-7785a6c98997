# Duplicate Detection System - Usage Guide

## Overview

The duplicate detection system has been successfully implemented with the following components:

- **Database Tables**: Normalized data tables and duplicate tracking
- **API Endpoints**: RESTful APIs for scanning, retrieving, and resolving duplicates
- **Services**: Core detection algorithms and resolution logic
- **UI Components**: Dashboard and modal components for managing duplicates
- **Utility Scripts**: Command-line tools for batch operations

## Getting Started

### 1. Populate Normalized Data

Before running duplicate detection, you need to populate the normalized data tables:

```bash
# Incremental normalization (recommended for regular updates)
npx tsx src/scripts/populateNormalizedData.ts

# Clean and rebuild all normalized data (use for first run or major changes)
npx tsx src/scripts/populateNormalizedData.ts --clean
```

**Incremental vs Clean Mode:**
- **Incremental** (default): Only processes new records and updates existing ones. Fast and safe for regular runs.
- **Clean mode** (`--clean`): Truncates all normalized tables and rebuilds from scratch. Use for first-time setup or when you want a complete refresh.

Or use the API endpoint:

```bash
curl -X POST http://localhost:3030/api/duplicates/normalize \
  -H "Content-Type: application/json" \
  -d '{"type": "both"}'
```

### 2. Run Duplicate Detection

#### Using the Script (Recommended for initial scan)

```bash
# Run comprehensive duplicate detection
npx tsx src/scripts/runDuplicateDetection.ts
```

#### Using the API

```bash
# Scan for duplicates
curl -X POST http://localhost:3030/api/duplicates/scan \
  -H "Content-Type: application/json" \
  -d '{"type": "both", "normalizeFirst": true}'
```

### 3. Access the Dashboard

Navigate to `/dashboard/duplicates` in your application to view and manage duplicates through the UI.

## API Endpoints

### Normalization

- `POST /api/duplicates/normalize` - Normalize data for duplicate detection
- `GET /api/duplicates/normalize` - Check normalization status

### Duplicate Detection

- `POST /api/duplicates/scan` - Scan for duplicates
- `GET /api/duplicates/scan` - Get scan status and recent results

### Duplicate Management

- `GET /api/duplicates` - List duplicates with filtering and pagination
- `POST /api/duplicates` - Update duplicate status
- `DELETE /api/duplicates` - Remove duplicate record

### Resolution

- `POST /api/duplicates/resolve` - Resolve duplicates (merge, separate, false positive)
- `POST /api/duplicates/merge-preview` - Preview merge results

## Detection Algorithms

### Company Matching

1. **Exact Domain Match** (95% confidence) - Same website domain
2. **Exact Name Match** (90% confidence) - Identical normalized company names
3. **Phone Match** (85% confidence) - Same phone number
4. **Name Similarity** (60-85% confidence) - Fuzzy string matching

### Contact Matching

1. **Exact Email Match** (98% confidence) - Same email address
2. **LinkedIn Match** (95% confidence) - Same LinkedIn profile
3. **Phone Match** (75% confidence) - Same phone number
4. **Email Domain + Name** (80-90% confidence) - Same domain with similar names
5. **Name + Company** (70-85% confidence) - Similar names at same company

## UI Components

### DuplicateDashboard

Main dashboard component for viewing and managing duplicates:

```tsx
import DuplicateDashboard from '@/components/duplicates/DuplicateDashboard'

// Usage
<DuplicateDashboard initialType="company" />
```

### DuplicateResolutionModal

Modal for resolving individual duplicates:

```tsx
import DuplicateResolutionModal from '@/components/duplicates/DuplicateResolutionModal'

// Usage
<DuplicateResolutionModal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  duplicate={selectedDuplicate}
  onResolve={handleResolve}
/>
```

## Configuration

### Detection Criteria

You can customize detection criteria when calling the APIs:

```typescript
// Company criteria
const companyCriteria = {
  exactDomain: true,
  exactName: true,
  phoneMatch: true,
  nameSimilarity: 0.8,
  minimumConfidence: 0.7
}

// Contact criteria
const contactCriteria = {
  exactEmail: true,
  linkedinMatch: true,
  emailDomainNameSimilarity: true,
  nameCompanyMatch: true,
  phoneMatch: true,
  minimumConfidence: 0.7
}
```

### Batch Processing

For large datasets, use batch processing:

```bash
# Normalize in batches of 100
curl -X POST http://localhost:3030/api/duplicates/normalize \
  -H "Content-Type: application/json" \
  -d '{"type": "both", "batchSize": 100}'
```

## Database Schema

### Tables Created

1. **company_normalized_data** - Normalized company data for fast matching
2. **contact_normalized_data** - Normalized contact data for fast matching  
3. **duplicate_records** - Tracks detected duplicates and their resolution status

### Indexes

Performance indexes are automatically created for:
- Company normalized names and domains
- Contact normalized emails and LinkedIn handles
- Name tokens (using GIN indexes for array searches)
- Duplicate record types and statuses

## Monitoring

### Check System Status

```bash
# Get normalization status
curl http://localhost:3030/api/duplicates/normalize

# Get recent scan results
curl http://localhost:3030/api/duplicates/scan
```

### View Statistics

```bash
# Get duplicate statistics
curl "http://localhost:3030/api/duplicates?page=1&pageSize=1"
```

## Troubleshooting

### Common Issues

1. **No duplicates found**: Ensure data is normalized first
2. **Slow performance**: Check that indexes are created properly
3. **Memory issues**: Use smaller batch sizes for large datasets

### Performance Tips

1. Run normalization during off-peak hours
2. Use appropriate confidence thresholds to balance precision vs recall
3. Regularly clean up resolved duplicates to maintain performance

## Integration

### Adding to Existing Workflows

The duplicate detection system integrates with existing conflict resolution:

1. **CSV Import**: Automatically detect duplicates during import
2. **Manual Entry**: Check for duplicates when creating new records
3. **Batch Processing**: Run periodic scans to catch new duplicates

### Extending Detection Logic

To add new matching algorithms:

1. Extend `DuplicateDetectionService` with new methods
2. Add new match types to the `DuplicateMatchType` enum
3. Update UI components to handle new match types

## Support

For issues or questions:

1. Check the console logs for detailed error messages
2. Verify database connectivity and table structure
3. Ensure all required dependencies are installed
4. Review the API response codes and error messages

The system is designed to be robust and handle edge cases, but monitoring the logs during initial deployment is recommended.
