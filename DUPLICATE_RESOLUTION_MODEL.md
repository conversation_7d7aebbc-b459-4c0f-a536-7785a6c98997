# Duplicate Resolution Model

## Overview

This document outlines the comprehensive duplicate resolution system for the Anax Dashboard application. When duplicates are detected, we need to shift data from multiple related tables to maintain data integrity.

## Why Normalization Stopped Progressing

The normalization process stopped because it only processes records that don't already exist in the normalized tables. The query uses:
```sql
WHERE c.contact_id NOT IN (SELECT contact_id FROM contact_normalized_data WHERE contact_id IS NOT NULL)
```

This means once a record is normalized, it won't be processed again. The script processed 126,162 contacts out of 252,162 total contacts, which means 126,162 contacts were already normalized.

## Tables Requiring Data Shifting During Duplicate Resolution

### Core Tables (Primary Relationships)
1. **`contacts`** - Main contact table
2. **`companies`** - Main company table  
3. **`investment_criteria_central`** - Links to both contacts and companies via `entity_id` and `entity_type`

### Contact-Related Tables (Priority Order)
| Table | ID Column | Foreign Key | Priority | Description |
|-------|-----------|-------------|----------|-------------|
| `contacts` | `contact_id` | - | 100 | Main contact data |
| `contact_enrichment` | `id` | `contact_id` | 90 | Contact enrichment data |
| `contact_normalized_data` | `id` | `contact_id` | 80 | Normalized data for duplicate detection |
| `contact_extracted_data` | `id` | `contact_id` | 70 | Extracted contact data |
| `contact_searched_data` | `id` | `contact_id` | 60 | Contact search data |
| `deal_contacts` | `id` | `contact_id` | 40 | Deal-contact relationships |
| `person_investment_criteria` | `id` | `contact_id` | 30 | Contact-specific investment criteria |
| `person_news_mentions` | `id` | `contact_id` | 20 | News mentions |

### Company-Related Tables (Priority Order)
| Table | ID Column | Foreign Key | Priority | Description |
|-------|-----------|-------------|----------|-------------|
| `companies` | `company_id` | - | 100 | Main company data |
| `company_normalized_data` | `id` | `company_id` | 90 | Normalized data for duplicate detection |
| `company_extracted_contact` | `id` | `company_id` | 70 | Contacts extracted from company data |
| `company_web_pages` | `id` | `company_id` | 60 | Company web scraping data |
| `company_web_chunks` | `id` | `company_id` | 50 | Company web content chunks |
| `deal_news_companies` | `id` | `company_id` | 10 | Deal news company relationships |

### Investment Criteria Tables
| Table | ID Column | Foreign Key | Entity Type Column | Priority | Description |
|-------|-----------|-------------|-------------------|----------|-------------|
| `investment_criteria_central` | `investment_criteria_id` | - | `entity_type` | 100 | Central investment criteria |
| `investment_criteria_debt` | `investment_criteria_debt_id` | `investment_criteria_id` | - | 90 | Debt-specific criteria |
| `investment_criteria_equity` | `investment_criteria_equity_id` | `investment_criteria_id` | - | 90 | Equity-specific criteria |

### Deal-Related Tables (May Need Shifting)
| Table | ID Column | Foreign Key | Priority | Description |
|-------|-----------|-------------|----------|-------------|
| `deals` | `deal_id` | - | 100 | Main deals table |
| `dealsv2` | `deal_id` | - | 90 | Deals v2 table |
| `deal_extractions` | `id` | `deal_id` | 80 | Deal extraction data |
| `deal_nsf_fields` | `id` | `deal_id` | 70 | Deal NSF fields |
| `deal_news_deals` | `id` | `deal_id` | 60 | Deal news deal relationships |

### News/Article Tables (May Need Shifting)
| Table | ID Column | Foreign Key | Priority | Description |
|-------|-----------|-------------|----------|-------------|
| `news` | `id` | - | 100 | News data |
| `deal_news` | `id` | - | 90 | Deal news data |
| `news_enrichment` | `id` | `news_id` | 80 | News enrichment data |
| `article` | `article_id` | - | 100 | Article data |
| `article_properties` | `article_property_id` | `article_id` | 90 | Article property data |
| `article_market_metrics` | `article_market_metric_id` | `article_id` | 90 | Article market metrics |
| `article_transactions` | `article_transaction_id` | `article_id` | 90 | Article transaction data |
| `articles_entities` | `article_entity_id` | `article_id` | 80 | Article entity relationships |

## Resolution Methods

### 1. Merge Strategy
- **Keep Primary**: Keep data from the primary record, discard duplicates
- **Combine Data**: Merge data from all records, resolving conflicts
- **Keep Most Recent**: Keep the most recently updated data

### 2. Resolution Methods
- **Merge**: Combine duplicate records into the primary record
- **Delete**: Remove duplicate records entirely
- **Mark Duplicate**: Soft delete by marking as duplicate

## Implementation

The `DuplicateResolutionService` handles all table relationships automatically:

```typescript
import { DuplicateResolutionService } from '../src/lib/services/duplicateResolutionService'

// Example usage
const resolution = {
  primaryId: 123,
  duplicateIds: [456, 789],
  entityType: 'contact',
  resolutionMethod: 'merge',
  mergeStrategy: 'keep_primary'
}

const result = await DuplicateResolutionService.resolveDuplicates(resolution)
```

## Key Features

1. **Automatic Table Discovery**: Service automatically checks which tables exist and have data
2. **Priority-Based Processing**: Tables are processed in priority order to preserve important data
3. **Transaction Safety**: All operations are wrapped in database transactions
4. **Error Handling**: Graceful error handling with rollback on failure
5. **Comprehensive Coverage**: Handles all related tables automatically

## Processor Relationships

The following processors interact with these tables:

### Contact Processors
- `ContactEnrichmentProcessorV2` → `contact_enrichment`
- `EmailGenerationProcessor` → `contacts` (updates email fields)
- `EmailValidatorProcessor` → `contacts` (updates email verification status)

### Company Processors
- `CompanyOverviewProcessorV2` → `companies` (updates overview fields)
- `CompanyWebCrawlerProcessor` → `company_web_pages`
- `CompanyInvestmentCriteriaProcessor` → `investment_criteria_central`

### Investment Criteria Processors
- `ContactInvestmentCriteriaProcessor` → `investment_criteria_central`
- `CompanyInvestmentCriteriaProcessor` → `investment_criteria_central`

### News/Article Processors
- `ArticleHTMLFetcherProcessor` → `article`
- `ArticleEnrichmentProcessor` → `article`, `article_properties`, `article_market_metrics`, `article_transactions`, `articles_entities`
- `NewsHTMLFetcherProcessor` → `news`
- `NewsEnrichmentProcessor` → `news_enrichment`

## Data Flow During Resolution

1. **Detection**: Duplicate detection identifies potential duplicates
2. **Analysis**: System analyzes all related data across tables
3. **Resolution**: User chooses resolution method and strategy
4. **Execution**: Service processes all related tables in priority order
5. **Verification**: System verifies data integrity after resolution

## Best Practices

1. **Always Backup**: Create database backups before major duplicate resolution
2. **Test First**: Test resolution on a small subset of data
3. **Monitor Progress**: Use the progress tracking features
4. **Verify Results**: Check data integrity after resolution
5. **Document Changes**: Keep records of all resolution decisions

## Troubleshooting

### Normalization Issues
If normalization stops progressing:
1. Check if records already exist in normalized tables
2. Clear normalized data and re-run: `TRUNCATE TABLE company_normalized_data, contact_normalized_data RESTART IDENTITY`
3. Check for errors in the normalization process

### Resolution Issues
If duplicate resolution fails:
1. Check database constraints and foreign keys
2. Verify table existence and permissions
3. Review error logs for specific table issues
4. Consider breaking large resolutions into smaller batches
