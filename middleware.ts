import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // Public routes that don't require authentication
    if (pathname.startsWith('/api/auth') || 
        pathname.startsWith('/api/homepage') || // Homepage API routes are public
        pathname === '/login') {
      return NextResponse.next()
    }

    // If no token, redirect to login
    if (!token) {
      const loginUrl = new URL('/login', req.url)
      loginUrl.searchParams.set('callbackUrl', pathname)
      return NextResponse.redirect(loginUrl)
    }

    const userRole = token.role as string || 'user'

    // Role-based access control
    const roleBasedRestrictions: Record<string, string[]> = {
      guest: ['/dashboard/projections',
        '/dashboard/people',
        '/dashboard/companies',
        '/dashboard/articles',
        '/dashboard/deals',
        '/dashboard/smartlead',
        '/dashboard/upload',
        '/dashboard/mapping',
        '/dashboard/homepage',
        '/dashboard/duplicates',
        '/dashboard/data-quality',
        '/dashboard/settings'
      ], // Guests can only access projections
      user: [
        '/dashboard/projections',
        '/dashboard/people', 
        '/dashboard/companies',
        '/dashboard/articles',
        '/dashboard/deals',
        '/dashboard/smartlead',
        '/dashboard/upload',
        '/dashboard/mapping',
        '/dashboard/homepage',
        '/dashboard/duplicates',
        '/dashboard/data-quality',
        '/dashboard/settings'
      ],
      admin: [
        // Admins can access everything
        '/dashboard/projections',
        '/dashboard/people', 
        '/dashboard/companies',
        '/dashboard/articles',
        '/dashboard/deals',
        '/dashboard/smartlead',
        '/dashboard/upload',
        '/dashboard/mapping',
        '/dashboard/homepage',
        '/dashboard/duplicates',
        '/dashboard/data-quality',
        '/dashboard/configuration',
        '/dashboard/processing',
        '/dashboard/settings',
        '/admin'
      ]
    }

    const allowedRoutes = roleBasedRestrictions[userRole] || roleBasedRestrictions.user

    // Check if user can access the current route
    const hasAccess = allowedRoutes.some(route => 
      pathname.startsWith(route) || 
      pathname === '/dashboard' || 
      pathname.startsWith('/dashboard/entity') // Allow entity page for all roles
    )

    if (!hasAccess) {
      // Redirect to appropriate default page based on role
      const defaultPage = userRole === 'guest' ? '/dashboard/projections' : '/dashboard/people'
      return NextResponse.redirect(new URL(defaultPage, req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to public endpoints without token
        if (req.nextUrl.pathname.startsWith('/api/auth') || 
            req.nextUrl.pathname.startsWith('/api/homepage') || // Homepage API routes are public
            req.nextUrl.pathname === '/login') {
          return true
        }
        // Require token for all other protected routes
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/admin/:path*',
    '/api/((?!auth|homepage).)*', // Exclude both auth and homepage routes
    '/settings/:path*'
  ]
}
