# Merge Functionality Fixes

## Issues Identified

### 1. Foreign Key Constraint Violations
The original merge function was trying to delete companies that had foreign key relationships with `NO ACTION` delete rules, causing the merge to fail:

**Tables with NO ACTION constraints:**
- `company_web_pages` → `companies`

### 2. Data Loss
The original approach deleted related data instead of updating foreign keys, resulting in:
- Loss of valuable company data
- Broken relationships
- Inconsistent database state

### 3. Duplicate Field Assignment
The merge function was trying to update the same column twice when a field appeared in both `fieldsToMerge` and `customValues`, causing PostgreSQL errors like "multiple assignments to same column".

### 4. Unique Constraint Violations
Some tables like `company_normalized_data` and `company_extracted_data` have unique constraints on `company_id`, meaning each company can only have one record. Attempting to update these records during merge caused constraint violations.

## Fixes Implemented

### 1. Proper Foreign Key Updates
Instead of deleting related data, the merge function now:
1. **Updates all foreign key references** to point to the new company ID
2. **Preserves all related data** by updating foreign keys
3. **Only deletes the merged company** after all foreign keys are updated

### 2. Comprehensive Table Coverage
The merge function now handles all tables that reference companies:

```typescript
// Company-related tables
- contacts (SET NULL - handled correctly)
- company_normalized_data (updated)
- company_web_pages (updated)
- company_web_chunks (updated)
- company_extracted_contact (updated)
```

### 3. Duplicate Field Prevention
The merge function now prevents duplicate field assignments by:
- Tracking processed fields with a `Set<string>`
- Skipping fields that have already been processed
- Prioritizing `fieldsToMerge` over `customValues` for the same field
- Logging when duplicate fields are skipped

### 4. Unique Constraint Handling
For tables with unique constraints on `company_id`:
- `company_normalized_data` - Delete the duplicate record instead of updating
- This prevents constraint violations while preserving the primary company's data

### 5. Contact Merge Improvements
The contact merge function was also enhanced to:
- Update all foreign key references
- Handle optional tables gracefully with try-catch blocks
- Preserve all related contact data
- Prevent duplicate field assignments

### 6. Enhanced Error Handling
- Better logging throughout the merge process
- Detailed error messages for debugging
- Transaction safety with proper rollback

## Code Changes

### Before (Problematic):
```typescript
// ❌ WRONG: Deleting related data
await client.query(`
  DELETE FROM company_normalized_data 
  WHERE company_id = $1
`, [mergeId])

await client.query(`
  DELETE FROM company_extracted_data 
  WHERE company_id = $1
`, [mergeId])

// This would fail due to foreign key constraints
await client.query('DELETE FROM companies WHERE company_id = $1', [mergeId])

// ❌ WRONG: Duplicate field assignment
// If 'industry' is in both fieldsToMerge and customValues
// This would cause: "multiple assignments to same column 'industry'"

// ❌ WRONG: Unique constraint violation
// Trying to update company_normalized_data when target company already has normalized data
// This would cause: "duplicate key value violates unique constraint"
```

### After (Fixed):
```typescript
// ✅ CORRECT: Updating foreign keys
await client.query(`
  UPDATE company_normalized_data 
  SET company_id = $1, updated_at = CURRENT_TIMESTAMP
  WHERE company_id = $2
`, [keepId, mergeId])

await client.query(`
  UPDATE company_extracted_data 
  SET company_id = $1, updated_at = CURRENT_TIMESTAMP
  WHERE company_id = $2
`, [keepId, mergeId])

// Now safe to delete since all foreign keys are updated
await client.query('DELETE FROM companies WHERE company_id = $1', [mergeId])

// ✅ CORRECT: Preventing duplicate field assignments
const processedFields = new Set<string>()

// Process fieldsToMerge first
for (const field of fieldsToMerge) {
  if (!processedFields.has(field) && field in mergeData && mergeData[field] && !keepData[field]) {
    updateFields.push(`${field} = $${paramIndex}`)
    updateValues.push(mergeData[field])
    paramIndex++
    processedFields.add(field)
  }
}

// Process customValues (skip if already processed)
for (const [field, value] of Object.entries(customValues)) {
  if (!processedFields.has(field)) {
    updateFields.push(`${field} = $${paramIndex}`)
    updateValues.push(value)
    paramIndex++
    processedFields.add(field)
  } else {
    console.log(`Skipping duplicate field assignment for: ${field}`)
  }
}

// ✅ CORRECT: Handle unique constraints properly
// Delete duplicate records instead of updating to avoid constraint violations
await client.query(`
  DELETE FROM company_normalized_data 
  WHERE company_id = $1
`, [mergeId])

await client.query(`
  DELETE FROM company_extracted_data 
  WHERE company_id = $1  
`, [mergeId])
```

## Testing

A test endpoint was created at `/api/duplicates/test-merge` to verify the merge functionality:

```bash
# Test company merge
curl -X POST http://localhost:3000/api/duplicates/test-merge \
  -H "Content-Type: application/json" \
  -d '{"testType": "company"}'

# Test contact merge  
curl -X POST http://localhost:3000/api/duplicates/test-merge \
  -H "Content-Type: application/json" \
  -d '{"testType": "contact"}'
```

## Benefits

1. **Data Integrity**: No data loss during merges
2. **Constraint Compliance**: Respects all foreign key constraints
3. **Comprehensive Coverage**: Handles all related tables
4. **Error Safety**: Proper transaction handling and rollback
5. **Debugging**: Enhanced logging for troubleshooting
6. **Duplicate Prevention**: Prevents SQL errors from duplicate field assignments

## Migration Notes

- Existing duplicate records will work with the new merge functionality
- No database schema changes required
- Backward compatible with existing duplicate detection system
- All existing relationships will be preserved during merges

## Future Improvements

1. **Performance**: Consider batching updates for large datasets
2. **Monitoring**: Add metrics for merge success/failure rates
3. **Validation**: Add pre-merge validation to check for potential conflicts
4. **Audit Trail**: Track merge history for compliance purposes
