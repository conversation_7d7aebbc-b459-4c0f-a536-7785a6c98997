-- Add capital_position field to article_transactions table
-- This field will store the capital position (e.g., Senior Debt, Mezzanine, Preferred Equity, etc.)

ALTER TABLE article_transactions 
ADD COLUMN IF NOT EXISTS capital_position TEXT;

-- Add comment to describe the field
COMMENT ON COLUMN article_transactions.capital_position IS 'Capital position in the transaction (e.g., Senior Debt, Mezzanine, Preferred Equity, Common Equity, GP, LP, JV, etc.)';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_article_transactions_capital_position ON article_transactions(capital_position);

-- Update existing records to set a default value if needed
-- UPDATE article_transactions SET capital_position = 'Undetectable' WHERE capital_position IS NULL;
