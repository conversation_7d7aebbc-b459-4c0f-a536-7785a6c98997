# ArticleHTMLFetcherProcessor Issues and Fixes

## Problem Analysis

The curl command `curl -X POST "http://localhost:3030/api/processing/trigger" -H "Content-Type: application/json" -d '{"action": "execute_manual", "stage": "article_fetch", "options": {"singleId": 963, "limit": 1}}' | jq '.success'` was failing because the ArticleHTMLFetcherProcessor had several critical issues compared to the working NewsHTMLFetcherProcessor.

## Issues Identified

### 1. ❌ Missing Method in BaseProcessor
**Issue**: ArticleHTMLFetcherProcessor was calling `updateArticleHTMLFetchingStatus()` which didn't exist in BaseProcessor.
**Impact**: Runtime errors when trying to update article status.

### 2. ❌ Stuck Articles in Database
**Issue**: 4 articles were stuck in 'running' status, including article ID 963 with a "Connection closed" error.
**Impact**: These articles couldn't be reprocessed because the system thought they were still being processed.

### 3. ❌ Missing Raw HTML Storage
**Issue**: Unlike NewsHTMLFetcherProcessor, ArticleHTMLFetcherProcessor wasn't saving raw HTML content.
**Impact**: No way to debug content extraction issues or reprocess content without refetching.

### 4. ❌ Content Validation Issues
**Issue**: Even "completed" articles had 0 content length, indicating extraction wasn't working properly.
**Impact**: Articles were marked as processed but contained no useful content.

## Fixes Implemented

### ✅ Fix 1: Added Missing Method to BaseProcessor
**File**: `src/lib/processors/BaseProcessor.ts`
**Change**: Added `updateArticleHTMLFetchingStatus()` method that properly updates article fetch status with error handling.

```typescript
protected async updateArticleHTMLFetchingStatus(
  articleId: number,
  status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
  error?: string
): Promise<void>
```

### ✅ Fix 2: Removed Duplicate Method
**File**: `src/lib/processors/ArticleHTMLFetcherProcessor.ts`
**Change**: Removed the duplicate `updateArticleHTMLFetchingStatus()` method since it now inherits from BaseProcessor.

### ✅ Fix 3: Database Schema Enhancement
**File**: `add_raw_html_to_article.sql`
**Change**: Added `raw_html` column to article table to match news table structure.

```sql
ALTER TABLE article 
ADD COLUMN IF NOT EXISTS raw_html TEXT;
```

### ✅ Fix 4: Enhanced Content Storage
**File**: `src/lib/processors/ArticleHTMLFetcherProcessor.ts`
**Change**: Updated `storeHTMLContent()` to save raw HTML alongside extracted text and title.

```typescript
const sql = `
  UPDATE article
  SET raw_html = $1,           -- New: Save raw HTML
      article_body_text = $2,   -- Extracted text
      headline = $3,            -- Extracted title
      fetch_status = 'completed',
      fetch_date = NOW(),
      fetch_error = NULL,
      updated_at = NOW()
  WHERE article_id = $4
`
```

### ✅ Fix 5: Improved Content Validation
**File**: `src/lib/processors/ArticleHTMLFetcherProcessor.ts`
**Change**: Enhanced `updateEntityStatus()` to check both HTML and text content for proper validation.

```typescript
const checkSql = `
  SELECT LENGTH(raw_html) as html_length, 
         LENGTH(article_body_text) as text_length, 
         LENGTH(headline) as title_length, 
         fetch_status
  FROM article
  WHERE article_id = $1
`
```

### ✅ Fix 6: Reset Stuck Articles
**File**: `reset_stuck_articles.sql`
**Change**: Created SQL script to reset stuck articles back to 'pending' status.

```sql
UPDATE article 
SET 
    fetch_status = 'pending',
    fetch_error = NULL,
    updated_at = NOW() 
WHERE fetch_status = 'running';
```

## Required Actions Before Testing

1. **Run Database Migrations**:
   ```bash
   # Add raw_html column to article table
   psql your_database < add_raw_html_to_article.sql
   
   # Reset stuck articles
   psql your_database < reset_stuck_articles.sql
   ```

2. **Start the Development Server**:
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

3. **Test the curl command**:
   ```bash
   # Test with the original article ID 963
   curl -X POST "http://localhost:3030/api/processing/trigger" \
     -H "Content-Type: application/json" \
     -d '{"action": "execute_manual", "stage": "article_fetch", "options": {"singleId": 963, "limit": 1}}' \
     | jq '.success'
   
   # Should return: true
   ```

## Expected Behavior After Fixes

1. **ArticleHTMLFetcherProcessor should now**:
   - ✅ Successfully process articles without runtime errors
   - ✅ Save both raw HTML and extracted content
   - ✅ Properly validate content before marking as completed
   - ✅ Handle errors gracefully and update status correctly

2. **Database should show**:
   - ✅ Articles with both `raw_html` content and `article_body_text`
   - ✅ No articles stuck in 'running' status indefinitely
   - ✅ Proper error tracking in `fetch_error` field

3. **Processing should work like NewsHTMLFetcherProcessor**:
   - ✅ Fetch HTML content from URLs
   - ✅ Extract title and text using Cheerio
   - ✅ Handle login walls and blocking
   - ✅ Retry failed requests
   - ✅ Store all content reliably

## Current Status

| Component | Status | Notes |
|-----------|---------|-------|
| BaseProcessor Method | ✅ Fixed | Added updateArticleHTMLFetchingStatus() |
| ArticleHTMLFetcherProcessor | ✅ Fixed | Removed duplicate method, enhanced storage |
| Database Schema | ⚠️ Pending | Need to run add_raw_html_to_article.sql |
| Stuck Articles | ⚠️ Pending | Need to run reset_stuck_articles.sql |
| Testing | ⚠️ Pending | Need to start server and test curl command |

The ArticleHTMLFetcherProcessor should now work as well as the NewsHTMLFetcherProcessor!
