#!/usr/bin/env tsx

import { pool } from '../src/lib/db'

async function clearDuplicates() {
  const client = await pool.connect()
  
  try {
    console.log('🗑️ Clearing all duplicate records...')
    
    // Get count before deletion
    const beforeCount = await client.query('SELECT COUNT(*) as count FROM duplicate_records')
    console.log(`📊 Found ${beforeCount.rows[0].count} duplicate records to clear`)
    
    // Clear all duplicate records
    await client.query('DELETE FROM duplicate_records')
    
    // Reset sequence
    await client.query('ALTER SEQUENCE duplicate_records_id_seq RESTART WITH 1')
    
    console.log('✅ All duplicate records cleared successfully!')
    console.log('🔄 You can now run a new duplicate scan from the dashboard.')
    
  } catch (error) {
    console.error('❌ Error clearing duplicates:', error)
    throw error
  } finally {
    client.release()
  }
}

// Run if executed directly
if (require.main === module) {
  clearDuplicates()
    .then(() => {
      console.log('🎉 Clear operation completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Clear operation failed:', error)
      process.exit(1)
    })
}

export { clearDuplicates }
