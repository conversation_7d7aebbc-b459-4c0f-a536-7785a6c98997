#!/usr/bin/env tsx

// Load environment variables
import dotenv from 'dotenv'
dotenv.config()

import { unifiedWorkerManager } from '../src/lib/queue/UnifiedWorkerManager'

async function startAllWorkers() {
  try {
    console.log('[StartAllWorkers] Starting unified worker system...')
    
    // Start all workers based on processor registry (includes BullMQ cron scheduler)
    await unifiedWorkerManager.startAllWorkers()
    
    console.log('[StartAllWorkers] All systems started successfully!')
    console.log('[StartAllWorkers] Press Ctrl+C to stop all workers')
    
    // Keep process alive
    process.stdin.resume()
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n[StartAllWorkers] Shutting down all workers...')
      await unifiedWorkerManager.stopAllWorkers()
      console.log('[StartAllWorkers] All workers stopped')
      process.exit(0)
    })
    
    process.on('SIGTERM', async () => {
      console.log('\n[StartAllWorkers] Shutting down all workers...')
      await unifiedWorkerManager.stopAllWorkers()
      console.log('[StartAllWorkers] All workers stopped')
      process.exit(0)
    })
    
  } catch (error) {
    console.error('[StartAllWorkers] Failed to start workers:', error)
    process.exit(1)
  }
}

// Start the system
startAllWorkers()
