import { DataNormalizationService } from '../src/lib/services/dataNormalizationService'

async function runNormalization() {
  console.log('Starting data normalization...')
  
  try {
    // No need to clear data - using UPSERT approach
    console.log('Using UPSERT approach - will update existing records and insert new ones')
    
    // Normalize companies with batch size 1000
    console.log('Normalizing companies...')
    const companyResult = await DataNormalizationService.normalizeAllCompaniesFast(1000)
    console.log(`Company normalization completed: ${companyResult.processed} processed, ${companyResult.errors} errors`)
    
    // Normalize contacts with batch size 1000
    console.log('Normalizing contacts...')
    const contactResult = await DataNormalizationService.normalizeAllContactsFast(1000)
    console.log(`Contact normalization completed: ${contactResult.processed} processed, ${contactResult.errors} errors`)
    
    console.log('Data normalization completed successfully!')
  } catch (error) {
    console.error('Error during normalization:', error)
  }
}

runNormalization()
