#!/usr/bin/env tsx

import { cronScheduler } from '../src/lib/scheduler/CronScheduler'

async function startCronScheduler() {
  try {
    console.log('[CronScheduler] Starting cron scheduler...')
    
    // Start the cron scheduler
    cronScheduler.start()
    
    console.log('[CronScheduler] Cron scheduler started successfully')
    console.log('[CronScheduler] Press Ctrl+C to stop')
    
    // Keep process alive
    process.stdin.resume()
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n[CronScheduler] Shutting down cron scheduler...')
      cronScheduler.stop()
      process.exit(0)
    })
    
    process.on('SIGTERM', () => {
      console.log('\n[CronScheduler] Shutting down cron scheduler...')
      cronScheduler.stop()
      process.exit(0)
    })
    
  } catch (error) {
    console.error('[CronScheduler] Failed to start cron scheduler:', error)
    process.exit(1)
  }
}

startCronScheduler()
