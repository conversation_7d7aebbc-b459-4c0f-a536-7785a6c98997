import { pool } from '../src/lib/db'
import bcrypt from 'bcryptjs'

async function createTestAdmin() {
  try {
    console.log('Creating test admin user...')

    // Check if admin user already exists
    const existingUser = await pool.query(
      'SELECT user_id, email FROM users WHERE email = $1',
      ['<EMAIL>']
    )

    if (existingUser.rows.length > 0) {
      console.log('Admin user already exists:', existingUser.rows[0].email)
      return
    }

    // Hash the password
    const password = 'admin123'
    const saltRounds = 12
    const passwordHash = await bcrypt.hash(password, saltRounds)

    // Create admin user
    const result = await pool.query(
      `INSERT INTO users (username, email, password_hash, role, display_name, created_at, updated_at) 
       VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP) 
       RETURNING user_id, username, email, role`,
      ['admin', '<EMAIL>', passwordHash, 'admin', 'Admin User']
    )

    const newUser = result.rows[0]
    console.log('✅ Admin user created successfully:')
    console.log('  User ID:', newUser.user_id)
    console.log('  Username:', newUser.username)
    console.log('  Email:', newUser.email)
    console.log('  Role:', newUser.role)
    console.log('  Password: admin123')
    console.log('')
    
    // Create a welcome notification for the admin
    await pool.query(
      `INSERT INTO user_notifications (user_id, title, message, type, created_at)
       VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)`,
      [
        newUser.user_id,
        'Welcome to ANAX Dashboard!',
        'Your account has been created successfully. You can now access all admin features and manage users.',
        'success'
      ]
    )

    console.log('✅ Welcome notification created for admin user')

    // Also create a regular test user
    const regularUserResult = await pool.query(
      'SELECT user_id, email FROM users WHERE email = $1',
      ['<EMAIL>']
    )

    if (regularUserResult.rows.length === 0) {
      const userPasswordHash = await bcrypt.hash('user123', saltRounds)
      
      const regularUser = await pool.query(
        `INSERT INTO users (username, email, password_hash, role, display_name, created_at, updated_at) 
         VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP) 
         RETURNING user_id, username, email, role`,
        ['testuser', '<EMAIL>', userPasswordHash, 'user', 'Test User']
      )

      console.log('✅ Regular test user created:')
      console.log('  Email: <EMAIL>')
      console.log('  Password: user123')
      console.log('  Role: user')

      // Create notification for regular user
      await pool.query(
        `INSERT INTO user_notifications (user_id, title, message, type, created_at)
         VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)`,
        [
          regularUser.rows[0].user_id,
          'Welcome to ANAX!',
          'Your account has been created. Explore the dashboard features available to you.',
          'info'
        ]
      )
    }

    // Create a guest user as well
    const guestUserResult = await pool.query(
      'SELECT user_id, email FROM users WHERE email = $1',
      ['<EMAIL>']
    )

    if (guestUserResult.rows.length === 0) {
      const guestPasswordHash = await bcrypt.hash('guest123', saltRounds)
      
      await pool.query(
        `INSERT INTO users (username, email, password_hash, role, display_name, created_at, updated_at) 
         VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        ['guest', '<EMAIL>', guestPasswordHash, 'guest', 'Guest User']
      )

      console.log('✅ Guest test user created:')
      console.log('  Email: <EMAIL>')
      console.log('  Password: guest123')
      console.log('  Role: guest')
    }

    console.log('\n🎉 Test users setup completed!')
    console.log('\nYou can now login with any of these accounts:')
    console.log('1. Admin: <EMAIL> / admin123 (full access)')
    console.log('2. User: <EMAIL> / user123 (standard access)')
    console.log('3. Guest: <EMAIL> / guest123 (limited access)')

  } catch (error) {
    console.error('❌ Error creating test admin user:', error)
  } finally {
    await pool.end()
  }
}

// Run the script
createTestAdmin()
