#!/usr/bin/env ts-node

/**
 * <PERSON><PERSON><PERSON> to fix corrupted capital position data in article_transactions table
 * 
 * The bug was in ArticleEnrichmentProcessor.ts where capitalPosition[0] was getting
 * the first CHARACTER instead of the first ELEMENT from the array.
 * 
 * This script maps the single letters back to their full names:
 * - 'U' -> 'Undetectable'
 * - 'S' -> 'Senior Debt'
 * - 'C' -> 'Common Equity' 
 * - etc.
 */

import { pool } from '../src/lib/db';

const CAPITAL_POSITION_MAPPING: Record<string, string> = {
  'U': 'Undetectable',
  'S': 'Senior Debt',
  'C': 'Common Equity',
  'J': 'Joint Venture',
  'L': 'Limited Partner',
  'G': 'General Partner',
  'M': 'Mezzanine',
  'P': 'Preferred Equity'
};

async function fixCapitalPositionData() {
  console.log('🔧 Starting capital position data fix...');
  
  try {
    // First, get the count of records that need fixing
    const countQuery = `
      SELECT capital_position, COUNT(*) as count 
      FROM article_transactions 
      WHERE capital_position IN ($1, $2, $3, $4, $5, $6, $7, $8)
      GROUP BY capital_position 
      ORDER BY count DESC
    `;
    
    const singleLetters = Object.keys(CAPITAL_POSITION_MAPPING);
    const countResult = await pool.query(countQuery, singleLetters);
    
    console.log('📊 Records to fix:');
    let totalToFix = 0;
    for (const row of countResult.rows) {
      const fullName = CAPITAL_POSITION_MAPPING[row.capital_position];
      console.log(`  ${row.capital_position} -> ${fullName}: ${row.count} records`);
      totalToFix += parseInt(row.count);
    }
    
    if (totalToFix === 0) {
      console.log('✅ No corrupted capital position data found!');
      return;
    }
    
    console.log(`\n🔄 Fixing ${totalToFix} total records...`);
    
    // Fix each single letter mapping
    let totalFixed = 0;
    for (const [singleLetter, fullName] of Object.entries(CAPITAL_POSITION_MAPPING)) {
      const updateQuery = `
        UPDATE article_transactions 
        SET capital_position = $1 
        WHERE capital_position = $2
      `;
      
      const result = await pool.query(updateQuery, [fullName, singleLetter]);
      const updatedCount = result.rowCount || 0;
      
      if (updatedCount > 0) {
        console.log(`  ✅ Fixed ${updatedCount} records: '${singleLetter}' -> '${fullName}'`);
        totalFixed += updatedCount;
      }
    }
    
    console.log(`\n🎉 Successfully fixed ${totalFixed} capital position records!`);
    
    // Verify the fix
    console.log('\n🔍 Verification - checking for remaining single letters:');
    const verifyResult = await pool.query(countQuery, singleLetters);
    
    if (verifyResult.rows.length === 0) {
      console.log('✅ All single letter capital positions have been fixed!');
    } else {
      console.log('⚠️  Some single letters still remain:');
      for (const row of verifyResult.rows) {
        console.log(`  ${row.capital_position}: ${row.count} records`);
      }
    }
    
    // Show current distribution
    console.log('\n📈 Current capital position distribution:');
    const distributionQuery = `
      SELECT capital_position, COUNT(*) as count 
      FROM article_transactions 
      WHERE capital_position IS NOT NULL AND capital_position != ''
      GROUP BY capital_position 
      ORDER BY count DESC 
      LIMIT 10
    `;
    
    const distResult = await pool.query(distributionQuery);
    for (const row of distResult.rows) {
      console.log(`  ${row.capital_position}: ${row.count} records`);
    }
    
  } catch (error) {
    console.error('❌ Error fixing capital position data:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  fixCapitalPositionData()
    .then(() => {
      console.log('\n✨ Capital position data fix completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

export { fixCapitalPositionData };
