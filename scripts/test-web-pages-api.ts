#!/usr/bin/env tsx
/**
 * Test script for the web pages API
 * Tests the API endpoint that fetches crawled website pages with rankings
 * 
 * Usage: npx tsx scripts/test-web-pages-api.ts
 */

async function testWebPagesAPI() {
  console.log('🧪 Testing Web Pages API');
  console.log('==========================');
  console.log('');

  // Test with a mock company ID (replace with actual company ID from your database)
  const testCompanyId = 1; // Change this to a real company ID
  
  try {
    console.log(`📡 Testing API endpoint: /api/companies/${testCompanyId}/web-pages`);
    
    const response = await fetch(`http://localhost:3030/api/companies/${testCompanyId}/web-pages`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    console.log('✅ API Response received');
    console.log('');
    
    if (data.success) {
      console.log('📊 Response Data:');
      console.log(`   Success: ${data.success}`);
      console.log(`   Total Pages: ${data.data?.total_pages || 0}`);
      console.log(`   Top Pages: ${data.data?.top_pages?.length || 0}`);
      
      if (data.data?.ranking_summary) {
        console.log('');
        console.log('📈 Ranking Summary:');
        console.log(`   Highest Score: ${data.data.ranking_summary.highest_score}`);
        console.log(`   Average Score: ${data.data.ranking_summary.average_score}`);
        console.log(`   Total Pages: ${data.data.ranking_summary.total_pages}`);
      }
      
      if (data.data?.top_pages && data.data.top_pages.length > 0) {
        console.log('');
        console.log('🏆 Top Pages:');
        data.data.top_pages.slice(0, 3).forEach((page: any, index: number) => {
          console.log(`   ${index + 1}. ${page.url}`);
          console.log(`      Rank: #${page.relevance_rank}`);
          console.log(`      Score: ${page.rank_factors?.total_score || 0}/125`);
          console.log(`      Content Length: ${page.extracted_text?.length || 0} chars`);
          console.log('');
        });
      } else {
        console.log('');
        console.log('ℹ️  No web pages found for this company');
        console.log('   This could mean:');
        console.log('   - The company hasn\'t been crawled yet');
        console.log('   - The company ID doesn\'t exist');
        console.log('   - The database schema hasn\'t been updated');
      }
      
    } else {
      console.log('❌ API returned error:');
      console.log(`   Error: ${data.error}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
      console.log('');
      console.log('💡 Troubleshooting:');
      console.log('   1. Make sure your Next.js development server is running');
      console.log('   2. Run: npm run dev');
      console.log('   3. Then run this test again');
    }
  }
}

// Test the API structure
async function testAPIStructure() {
  console.log('🔍 Testing API Structure');
  console.log('========================');
  console.log('');
  
  const expectedFields = [
    'success',
    'data.pages',
    'data.total_pages', 
    'data.top_pages',
    'data.ranking_summary'
  ];
  
  const expectedPageFields = [
    'id',
    'url',
    'parent_url',
    'crawl_depth',
    'relevance_rank',
    'rank_factors',
    'extracted_text',
    'last_scraped_at',
    'created_at'
  ];
  
  const expectedRankFactors = [
    'url_structure_score',
    'content_quality_score',
    'page_type_score',
    'business_keywords_score',
    'geographic_relevance_score',
    'url_quality_bonus',
    'total_score'
  ];
  
  console.log('📋 Expected API Response Structure:');
  console.log('   Response Fields:', expectedFields.join(', '));
  console.log('');
  console.log('📋 Expected Page Object Structure:');
  console.log('   Page Fields:', expectedPageFields.join(', '));
  console.log('');
  console.log('📋 Expected Rank Factors Structure:');
  console.log('   Rank Factors:', expectedRankFactors.join(', '));
  console.log('');
}

// Main test runner
async function runTests() {
  console.log('🚀 Web Pages API Test Suite');
  console.log('============================');
  console.log('');
  
  try {
    await testAPIStructure();
    await testWebPagesAPI();
    
    console.log('✅ All tests completed!');
    console.log('');
    console.log('📝 Next Steps:');
    console.log('   1. Run the SQL schema update: psql -d your_database -f final_webpage_ranking.sql');
    console.log('   2. Crawl some company websites using the unlimited crawler');
    console.log('   3. Test the API with real company IDs');
    console.log('   4. View the results in the CompanyOverviewV2 component');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

export { runTests, testWebPagesAPI, testAPIStructure };
