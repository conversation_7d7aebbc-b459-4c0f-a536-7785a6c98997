#!/usr/bin/env tsx

// Load environment variables
import dotenv from 'dotenv'
dotenv.config()

import { processorQueueManager } from '../src/lib/queue/ProcessorQueueManager'
import { cronScheduler } from '../src/lib/scheduler/CronScheduler'

// Get queue type from command line args
const queueType = process.argv[2] as 'SHORT' | 'MEDIUM' | 'LONG' | string

if (!queueType) {
  console.error('Usage: tsx scripts/processor-workers.ts <QUEUE_TYPE>')
  console.error('Queue types: SHORT, MEDIUM, LONG, or custom queue name')
  process.exit(1)
}

async function startWorker() {
  try {
    console.log(`[Worker] Starting ${queueType} queue worker...`)
    
    // Start the worker for the specified queue
    await processorQueueManager.startWorker(queueType)
    
    console.log(`[Worker] ${queueType} worker started successfully`)
    console.log(`[Worker] Press Ctrl+C to stop`)
    
    // Keep process alive
    process.stdin.resume()
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log(`\n[Worker] Shutting down ${queueType} worker...`)
      await processorQueueManager.stopAllWorkers()
      process.exit(0)
    })
    
    process.on('SIGTERM', async () => {
      console.log(`\n[Worker] Shutting down ${queueType} worker...`)
      await processorQueueManager.stopAllWorkers()
      process.exit(0)
    })
    
  } catch (error) {
    console.error(`[Worker] Failed to start ${queueType} worker:`, error)
    process.exit(1)
  }
}

startWorker()
