#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to reprocess company overview data using existing LLM responses
 * without calling Perplexity API again
 * 
 * Usage:
 *   npx tsx scripts/reprocess-company-overview.ts --company-id=280472
 *   npx tsx scripts/reprocess-company-overview.ts --company-id=280472,280473,280474
 */

import { CompanyOverviewProcessorV2 } from '../src/lib/processors/CompanyOverviewProcessorV2'

async function main() {
  const args = process.argv.slice(2);
  const companyIdArg = args.find(arg => arg.startsWith('--company-id='));
  
  if (!companyIdArg) {
    console.error('Usage: npx tsx scripts/reprocess-company-overview.ts --company-id=COMPANY_ID[,COMPANY_ID2,...]');
    console.error('Example: npx tsx scripts/reprocess-company-overview.ts --company-id=280472');
    process.exit(1);
  }
  
  const companyIdsStr = companyIdArg.split('=')[1];
  const companyIds = companyIdsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
  
  if (companyIds.length === 0) {
    console.error('Error: No valid company IDs provided');
    process.exit(1);
  }
  
  console.log(`🔄 Starting reprocessing for ${companyIds.length} company/companies: ${companyIds.join(', ')}`);
  
  console.log('✅ Company Overview Processor V2 initialized with reprocessing mode enabled');
  console.log('📄 Will use existing LLM responses from database instead of calling Perplexity API');
  
  try {
    // Process each company ID
    for (const companyId of companyIds) {
      console.log(`\n🏢 Processing company ID: ${companyId}`);
      
      // Create processor with reprocessing enabled for each company
      const processor = new CompanyOverviewProcessorV2({
        reprocessExisting: true,  // This flag enables reprocessing existing LLM responses
        entityType: 'company',
        singleId: companyId,      // Process this specific company
        batchSize: 1
      });
      
      const result = await processor.process();
      
      if (result.successful > 0) {
        console.log(`✅ Successfully reprocessed company ${companyId}`);
        console.log(`   - Processed: ${result.processed}`);
        console.log(`   - Successful: ${result.successful}`);
        console.log(`   - Failed: ${result.failed}`);
      } else {
        console.log(`❌ Failed to reprocess company ${companyId}`);
        console.log(`   - Processed: ${result.processed}`);
        console.log(`   - Failed: ${result.failed}`);
        
        // Log any errors
        if (result.errors && result.errors.length > 0) {
          console.log(`   - Errors: ${result.errors.join(', ')}`);
        }
      }
    }
    
    console.log('\n🎉 Reprocessing completed!');
    console.log('\n💡 Benefits of reprocessing:');
    console.log('   - ✅ Fixed contact creation issues');
    console.log('   - ✅ Improved evidence-grounded data transformation');
    console.log('   - ✅ Better error handling and logging');
    console.log('   - ✅ No additional API calls to Perplexity (saves costs)');
    
  } catch (error) {
    console.error('❌ Error during reprocessing:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⏹️ Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n⏹️ Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

main().catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
