#!/usr/bin/env tsx

import { pool } from '../src/lib/db'

async function refreshDuplicateAnalysis() {
  const client = await pool.connect()
  
  try {
    console.log('🔄 Starting complete duplicate analysis refresh...')
    
    // Step 1: Clear all duplicate records
    console.log('📋 Step 1: Clearing duplicate records...')
    await client.query('DELETE FROM duplicate_records')
    console.log('✅ Duplicate records cleared')
    
    // Step 2: Clear normalized data (optional - uncomment if you want to regenerate)
    console.log('📋 Step 2: Clearing normalized data...')
    await client.query('DELETE FROM company_normalized_data')
    await client.query('DELETE FROM contact_normalized_data')
    console.log('✅ Normalized data cleared')
    
    // Step 3: Reset auto-increment sequences
    console.log('📋 Step 3: Resetting sequences...')
    await client.query('ALTER SEQUENCE duplicate_records_id_seq RESTART WITH 1')
    await client.query('ALTER SEQUENCE company_normalized_data_id_seq RESTART WITH 1')
    await client.query('ALTER SEQUENCE contact_normalized_data_id_seq RESTART WITH 1')
    console.log('✅ Sequences reset')
    
    // Step 4: Regenerate normalized data
    console.log('📋 Step 4: Regenerating normalized data...')
    
    // For companies
    await client.query(`
      INSERT INTO company_normalized_data (company_id, normalized_name, created_at, updated_at)
      SELECT 
        company_id,
        LOWER(REGEXP_REPLACE(company_name, '[^a-zA-Z0-9\\s]', '', 'g')) as normalized_name,
        NOW() as created_at,
        NOW() as updated_at
      FROM companies 
      WHERE company_name IS NOT NULL AND company_name != ''
    `)
    console.log('✅ Company normalized data regenerated')
    
    // For contacts
    await client.query(`
      INSERT INTO contact_normalized_data (contact_id, normalized_name, created_at, updated_at)
      SELECT 
        contact_id,
        LOWER(REGEXP_REPLACE(full_name, '[^a-zA-Z0-9\\s]', '', 'g')) as normalized_name,
        NOW() as created_at,
        NOW() as updated_at
      FROM contacts 
      WHERE full_name IS NOT NULL AND full_name != ''
    `)
    console.log('✅ Contact normalized data regenerated')
    
    // Step 5: Run duplicate detection
    console.log('📋 Step 5: Running duplicate detection...')
    
    // Company duplicates based on normalized names
    await client.query(`
      INSERT INTO duplicate_records (primary_record_id, duplicate_record_id, record_type, confidence_score, match_type, match_details, status, created_at)
      SELECT 
        c1.company_id as primary_record_id,
        c2.company_id as duplicate_record_id,
        'company' as record_type,
        95.0 as confidence_score,
        'exact_name' as match_type,
        jsonb_build_object(
          'normalizedName', c1.normalized_name,
          'primaryName', c1.company_name,
          'duplicateName', c2.company_name
        ) as match_details,
        'pending' as status,
        NOW() as created_at
      FROM company_normalized_data c1
      JOIN company_normalized_data c2 ON c1.normalized_name = c2.normalized_name
      WHERE c1.company_id < c2.company_id
    `)
    console.log('✅ Company duplicates detected')
    
    // Contact duplicates based on normalized names
    await client.query(`
      INSERT INTO duplicate_records (primary_record_id, duplicate_record_id, record_type, confidence_score, match_type, match_details, status, created_at)
      SELECT 
        c1.contact_id as primary_record_id,
        c2.contact_id as duplicate_record_id,
        'contact' as record_type,
        95.0 as confidence_score,
        'exact_name' as match_type,
        jsonb_build_object(
          'normalizedName', c1.normalized_name,
          'primaryName', c1.full_name,
          'duplicateName', c2.full_name
        ) as match_details,
        'pending' as status,
        NOW() as created_at
      FROM contact_normalized_data c1
      JOIN contact_normalized_data c2 ON c1.normalized_name = c2.normalized_name
      WHERE c1.contact_id < c2.contact_id
    `)
    console.log('✅ Contact duplicates detected')
    
    // Step 6: Get final statistics
    console.log('📋 Step 6: Generating statistics...')
    
    const duplicateStats = await client.query(`
      SELECT 
        record_type,
        COUNT(*) as total_duplicates,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_duplicates,
        COUNT(CASE WHEN status = 'merged' THEN 1 END) as merged_duplicates,
        COUNT(CASE WHEN status = 'false_positive' THEN 1 END) as false_positives
      FROM duplicate_records 
      GROUP BY record_type
    `)
    
    const normalizedStats = await client.query(`
      SELECT 
        'company_normalized_data' as table_name,
        COUNT(*) as total_records
      FROM company_normalized_data
      UNION ALL
      SELECT 
        'contact_normalized_data' as table_name,
        COUNT(*) as total_records
      FROM contact_normalized_data
    `)
    
    console.log('\n📊 REFRESH COMPLETE - STATISTICS:')
    console.log('=====================================')
    
    console.log('\n🔍 Duplicate Records:')
    duplicateStats.rows.forEach(row => {
      console.log(`  ${row.record_type}:`)
      console.log(`    Total: ${row.total_duplicates}`)
      console.log(`    Pending: ${row.pending_duplicates}`)
      console.log(`    Merged: ${row.merged_duplicates}`)
      console.log(`    False Positives: ${row.false_positives}`)
    })
    
    console.log('\n📋 Normalized Data:')
    normalizedStats.rows.forEach(row => {
      console.log(`  ${row.table_name}: ${row.total_records} records`)
    })
    
    console.log('\n✅ Complete duplicate analysis refresh finished!')
    console.log('🔄 You can now access the duplicates dashboard to review the new results.')
    
  } catch (error) {
    console.error('❌ Error during refresh:', error)
    throw error
  } finally {
    client.release()
  }
}

// Run the refresh if this script is executed directly
if (require.main === module) {
  refreshDuplicateAnalysis()
    .then(() => {
      console.log('🎉 Refresh completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Refresh failed:', error)
      process.exit(1)
    })
}

export { refreshDuplicateAnalysis }
