# Website Pages API Implementation

## Overview
I've created a comprehensive API system to display crawled website pages with intelligent ranking data in the CompanyOverviewV2 component. This integrates seamlessly with the unlimited crawling system we built earlier.

## 🗃️ **Files Created:**

### **1. API Endpoint**
**File:** `src/app/api/companies/[id]/web-pages/route.ts`
- **GET** endpoint to fetch web pages for a specific company
- Returns pages with ranking data, content, and metadata
- Includes ranking summary statistics

### **2. React Hook**
**File:** `src/hooks/useCompanyWebPages.ts`
- Custom hook to fetch and manage web pages data
- Handles loading states and error management
- Provides typed interfaces for TypeScript safety

### **3. UI Component**
**File:** `src/components/dashboard/companies/detail-components/CompanyWebPages.tsx`
- Comprehensive display of crawled website pages
- Shows ranking scores, reasons, and content previews
- Interactive expansion for full content viewing
- Score breakdown visualization

### **4. Integration**
**File:** `src/components/dashboard/companies/detail-components/CompanyOverviewV2.tsx`
- Integrated the web pages component into the main company view
- Added to the data sources section

### **5. Testing**
**File:** `scripts/test-web-pages-api.ts`
- Test script to verify API functionality
- Structure validation and error handling

## 🎯 **API Response Structure:**

```json
{
  "success": true,
  "data": {
    "pages": [
      {
        "id": "uuid",
        "url": "https://example.com/page",
        "parent_url": "https://example.com/",
        "crawl_depth": 1,
        "relevance_rank": 1,
        "rank_factors": {
          "url_structure_score": 25,
          "content_quality_score": 20,
          "page_type_score": 15,
          "business_keywords_score": 35,
          "geographic_relevance_score": 10,
          "url_quality_bonus": 5,
          "total_score": 110
        },
        "extracted_text": "Page content...",
        "last_scraped_at": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total_pages": 18,
    "top_pages": [...],
    "ranking_summary": {
      "highest_score": 110,
      "average_score": 67.5,
      "total_pages": 18
    }
  }
}
```

## 🎨 **UI Features:**

### **Ranking Summary Dashboard**
- Highest score achieved
- Average score across all pages
- Total pages crawled
- Visual score indicators

### **Page Details**
- **Ranking Information:**
  - Relevance rank (#1, #2, etc.)
  - Total score out of 125 points
  - Detailed score breakdown by category
  - Ranking reason explanation

- **Content Display:**
  - Full URL with external link
  - Content preview (expandable)
  - Character count
  - Scraping metadata

- **Score Breakdown:**
  - URL Structure (25 pts)
  - Content Quality (20 pts)
  - Page Type (15 pts)
  - Business Keywords (40 pts)
  - Geographic Relevance (15 pts)
  - URL Quality Bonus (10 pts)

### **Interactive Features**
- Expandable content previews
- Show all pages vs. top 5 toggle
- Color-coded score indicators
- Hover effects and transitions

## 🔧 **Usage:**

### **1. Database Setup**
```bash
# Run the schema update
psql -d your_database -f final_webpage_ranking.sql
```

### **2. API Testing**
```bash
# Test the API endpoint
npx tsx scripts/test-web-pages-api.ts
```

### **3. Component Integration**
The component automatically loads when viewing a company in CompanyOverviewV2:
```tsx
<CompanyWebPages
  companyId={company.id}
  webPages={webPages}
  topPages={topPages}
  rankingSummary={rankingSummary}
  loading={webPagesLoading}
  error={webPagesError}
/>
```

## 📊 **Data Flow:**

1. **Crawling:** Unlimited crawler discovers and ranks pages
2. **Storage:** Pages stored with ranking data in database
3. **API:** Endpoint fetches and returns structured data
4. **Hook:** React hook manages data fetching and state
5. **UI:** Component displays rich ranking information

## 🎯 **Key Benefits:**

### **For Users:**
- **Transparency:** See exactly why pages are ranked highly
- **Content Access:** Preview and access all crawled content
- **Intelligence:** Understand business relevance scoring
- **Navigation:** Direct links to source pages

### **For Developers:**
- **Type Safety:** Full TypeScript interfaces
- **Error Handling:** Comprehensive error states
- **Performance:** Efficient data loading and caching
- **Extensibility:** Easy to add new ranking factors

## 🔍 **Ranking Intelligence:**

The system explains ranking decisions:
- **"High business keyword relevance"** - Pages with many business terms
- **"Strong URL structure"** - Clean, semantic URLs
- **"High content quality"** - Optimal content length and quality
- **"Important page type"** - Homepage, about, services pages
- **"Geographic relevance"** - Location-specific content

## 🚀 **Next Steps:**

1. **Run the database schema update**
2. **Test with real company data**
3. **Customize ranking factors if needed**
4. **Add additional UI features (filtering, sorting)**
5. **Integrate with AI processing workflows**

The system is now ready to display comprehensive website crawling results with intelligent ranking explanations in your company overview interface!
