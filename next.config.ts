import type { NextConfig } from "next";
import dotenv from 'dotenv';

// Load environment variables from .env files
dotenv.config();

// Force source maps for debugging
const FORCE_SOURCE_MAPS = process.env.FORCE_SOURCE_MAPS === 'true';

// Check if we're in a build environment that needs source maps
const isBuildEnvironment = process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'development';

const config: NextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  
  // Enable source maps for better error debugging
  productionBrowserSourceMaps: true,
  
  // Note: swcMinify is deprecated in Next.js 15, minification is handled automatically
  // swcMinify: true, // Removed - deprecated in Next.js 15
  
  // Enable more verbose logging in production
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  
  experimental: {
    serverActions: {
      bodySizeLimit: "1gb",
    },
  },

  webpack: (config, { isServer, dev }) => {
    // Always enable source maps for debugging (both client and server)
    config.devtool = 'source-map';
    
    // Force source maps even in production builds
    if (process.env.NODE_ENV === 'production') {
      config.devtool = 'source-map';
      config.optimization = config.optimization || {};
      config.optimization.minimize = false; // Disable minification to preserve source maps
    }
    
    if (isServer) {
      // Suppress TypeORM warnings about missing optional dependencies
      config.ignoreWarnings = [
        /Critical dependency: the request of a dependency is an expression/,
        /Module not found: Can't resolve 'react-native-sqlite-storage'/,
        /Module not found: Can't resolve '@sap\/hana-client\/extension\/Stream'/,
        /Module not found: Can't resolve 'mysql'/,
        /Module not found: Can't resolve 'pg-native'/,
        /Module not found: Can't resolve 'pg-query-stream'/,
      ];
      
      // More aggressive approach: completely ignore TypeORM driver warnings
      config.externals = config.externals || [];
      if (Array.isArray(config.externals)) {
        config.externals.push({
          'react-native-sqlite-storage': 'commonjs react-native-sqlite-storage',
          '@sap/hana-client': 'commonjs @sap/hana-client',
          'mysql': 'commonjs mysql',
          'pg-native': 'commonjs pg-native',
        });
      }
    }
    return config;
  },
};

export default config;
