# CompanyOverviewProcessorV2 Fixes and Improvements

## Issues Fixed

### 1. Evidence-Grounded Data Transformation Issues
**Problem**: Many fields were showing warnings like `Field secondary_email appears to be evidence-grounded but missing 'value' property`

**Root Cause**: The LLM response was using the NEW format (with just `value` and `sources`), but the processor was expecting both old and new formats and was complaining about missing fields like `confidence_score`.

**Solution**: 
- Enhanced `transformEvidenceGroundedData()` method to properly handle the new format
- Added logic to calculate confidence scores from sources when not explicitly provided
- Added proper filtering for malformed evidence-grounded fields

### 2. Contact Creation Failures
**Problem**: 11 out of 14 executives failed to create contacts with errors like `Failed to create contact for executive: <PERSON><PERSON>`

**Root Cause**: 
- Poor error handling in contact creation
- Missing data validation
- Database constraint violations not properly handled

**Solution**:
- Improved `createContactFromExecutive()` method with better error handling
- Added comprehensive data validation before contact creation
- Enhanced `processExecutiveContacts()` with detailed logging and error tracking
- Added graceful handling of duplicate contacts and constraint violations

### 3. No Reprocessing Capability
**Problem**: No way to reprocess existing LLM responses without calling Perplexity API again

**Solution**:
- Added `reprocessExisting` flag to `ProcessorOptions` interface
- Implemented `getExistingLLMResponse()` method to fetch stored LLM responses
- Added `setReprocessingMode()` public method for dynamic control
- Enhanced `processEntity()` to check for existing responses when flag is enabled

### 4. Improved Logging and Debugging
**Problem**: Limited visibility into processing failures

**Solution**:
- Enhanced logging throughout the processor with more detailed messages
- Added success/failure counters in contact processing
- Improved error messages with specific database constraint information
- Added debug logging for data transformation steps

## How to Use the Reprocessing Feature

### Option 1: Using the Processor Directly

```typescript
import { CompanyOverviewProcessorV2 } from '../src/lib/processors/CompanyOverviewProcessorV2'

// Create processor with reprocessing enabled
const processor = new CompanyOverviewProcessorV2({
  reprocessExisting: true,  // Enable reprocessing mode
  entityType: 'company',
  singleId: 280472,        // Specify the company ID to process
  batchSize: 1
});

// Process the company
const result = await processor.process();
```

### Option 2: Using the Convenience Script

```bash
# Reprocess a single company
npx tsx scripts/reprocess-company-overview.ts --company-id=280472

# Reprocess multiple companies
npx tsx scripts/reprocess-company-overview.ts --company-id=280472,280473,280474
```

### Option 3: Dynamic Mode Switching

```typescript
const processor = new CompanyOverviewProcessorV2({
  entityType: 'company',
  singleId: 280472
});

// Enable reprocessing mode
processor.setReprocessingMode(true);

// Process entities (will use existing LLM responses)
const result = await processor.process();

// Disable reprocessing mode for new API calls
processor.setReprocessingMode(false);
```

## Benefits of Reprocessing

1. **Cost Savings**: No additional API calls to Perplexity
2. **Fixed Contact Creation**: All executive contacts now created properly
3. **Better Data Quality**: Improved data transformation and validation
4. **Enhanced Debugging**: Detailed logging for troubleshooting
5. **Flexible Processing**: Can switch between reprocessing and new API calls

## Database Requirements

The reprocessing feature requires:
- `companies.llm_response` field contains the stored LLM response JSON
- `companies.overview_v2_status = 'completed'` for the company
- `companies.llm_token_usage` for usage tracking (optional)

## Error Handling Improvements

### Before
```
[CompanyOverviewV2] [ERROR] Failed to create contact for executive: Yoni Tal
```

### After
```
[CompanyOverviewV2] [DEBUG] Skipping executive Yoni Tal - insufficient data for contact creation
[CompanyOverviewV2] [INFO] Completed processing executive contacts for company 280472: 3 created, 8 skipped, 3 errors
```

## Data Transformation Improvements

### Before
```
[CompanyOverviewV2] [WARN] Field secondary_email appears to be evidence-grounded but missing 'value' property
```

### After
```
[CompanyOverviewV2] [DEBUG] Transformed field secondary_email with new format, calculated confidence 0.80
[CompanyOverviewV2] [DEBUG] Skipping malformed field fund_size - has confidence_score but no value
```

## Testing the Fixes

To test the improvements, run the reprocessing script on a company that previously had issues:

```bash
npx tsx scripts/reprocess-company-overview.ts --company-id=280472
```

Expected output:
- ✅ No more "missing value property" warnings
- ✅ Proper contact creation (or meaningful skip messages)
- ✅ Detailed processing statistics
- ✅ No Perplexity API calls (cost savings)
