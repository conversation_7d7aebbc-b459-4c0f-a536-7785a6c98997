# Duplicate Analysis Refresh Guide

This guide explains how to completely refresh the duplicate analysis system, clearing all existing duplicates and regenerating them from scratch.

## 🎯 When to Refresh

You should refresh the duplicate analysis when:

- **New data has been uploaded** and you want to detect duplicates across all records
- **Duplicate detection rules have changed** and you want to apply new logic
- **Data has been cleaned/normalized** and you want to re-run detection
- **You want to start fresh** with duplicate resolution

## 🚀 Quick Commands

### Option 1: Clear Only Duplicates (Fast)
```bash
pnpm run duplicates:clear
```
This clears all duplicate records but keeps normalized data. You can then run a new scan from the dashboard.

### Option 2: Complete Refresh (Comprehensive)
```bash
pnpm run duplicates:refresh
```
This performs a complete refresh:
- Clears all duplicate records
- Clears and regenerates normalized data
- Runs new duplicate detection
- Provides detailed statistics

## 📋 What Gets Refreshed

### Tables Cleared:
- `duplicate_records` - All duplicate detection results
- `company_normalized_data` - Normalized company names (regenerated)
- `contact_normalized_data` - Normalized contact names (regenerated)

### What's Preserved:
- **Original data** (`companies`, `contacts` tables) - Never touched
- **Merged records** - Already resolved duplicates remain merged
- **System metadata** - Processing states, timestamps, etc.

## 🔄 Refresh Process

### Step 1: Clear Existing Data
- Delete all records from `duplicate_records`
- Delete all records from normalized data tables
- Reset auto-increment sequences

### Step 2: Regenerate Normalized Data
- **Companies**: Create normalized names (lowercase, no special chars)
- **Contacts**: Create normalized names (lowercase, no special chars)

### Step 3: Detect Duplicates
- **Exact Name Matches**: 95% confidence for identical normalized names
- **Company Duplicates**: Based on normalized company names
- **Contact Duplicates**: Based on normalized contact names

### Step 4: Generate Statistics
- Total duplicates found
- Breakdown by record type
- Status distribution

## 📊 Expected Output

After running `pnpm run duplicates:refresh`, you'll see:

```
🔄 Starting complete duplicate analysis refresh...
📋 Step 1: Clearing duplicate records...
✅ Duplicate records cleared
📋 Step 2: Clearing normalized data...
✅ Normalized data cleared
📋 Step 3: Resetting sequences...
✅ Sequences reset
📋 Step 4: Regenerating normalized data...
✅ Company normalized data regenerated
✅ Contact normalized data regenerated
📋 Step 5: Running duplicate detection...
✅ Company duplicates detected
✅ Contact duplicates detected
📋 Step 6: Generating statistics...

📊 REFRESH COMPLETE - STATISTICS:
=====================================

🔍 Duplicate Records:
  company:
    Total: 150
    Pending: 150
    Merged: 0
    False Positives: 0
  contact:
    Total: 75
    Pending: 75
    Merged: 0
    False Positives: 0

📋 Normalized Data:
  company_normalized_data: 5000 records
  contact_normalized_data: 2500 records

✅ Complete duplicate analysis refresh finished!
🔄 You can now access the duplicates dashboard to review the new results.
```

## ⚠️ Important Notes

### Data Safety
- **Original records are never deleted** - only duplicate detection results are cleared
- **Merged companies/contacts remain merged** - the merge results are permanent
- **System fields are preserved** - processing states, timestamps, etc.

### Performance
- **Large datasets**: Refresh may take several minutes for large datasets
- **Database load**: High CPU/memory usage during refresh
- **Recommended**: Run during off-peak hours for production systems

### After Refresh
1. **Check the dashboard** - All duplicates will show as "pending"
2. **Review results** - New duplicates may be found
3. **Use auto-merge** - Leverage the new smart auto-merge functionality
4. **Monitor quality** - Verify duplicate detection accuracy

## 🔧 Manual Database Commands

If you prefer to run commands directly:

### Clear Only Duplicates
```sql
DELETE FROM duplicate_records;
ALTER SEQUENCE duplicate_records_id_seq RESTART WITH 1;
```

### Complete Refresh (Manual)
```sql
-- Clear all data
DELETE FROM duplicate_records;
DELETE FROM company_normalized_data;
DELETE FROM contact_normalized_data;

-- Reset sequences
ALTER SEQUENCE duplicate_records_id_seq RESTART WITH 1;
ALTER SEQUENCE company_normalized_data_id_seq RESTART WITH 1;
ALTER SEQUENCE contact_normalized_data_id_seq RESTART WITH 1;

-- Regenerate normalized data (run the full script for this)
```

## 🎯 Best Practices

1. **Backup first**: Consider backing up the database before large refreshes
2. **Test on staging**: Run refresh on test data first
3. **Monitor performance**: Watch database performance during refresh
4. **Review results**: Always check the quality of new duplicates
5. **Use auto-merge**: Take advantage of the smart auto-merge feature

## 🆘 Troubleshooting

### Common Issues:

**"Connection timeout"**
- Database is under heavy load
- Try running during off-peak hours

**"Sequence not found"**
- Database schema may be different
- Check if sequences exist in your database

**"Permission denied"**
- Database user needs DELETE and INSERT permissions
- Contact database administrator

**"No duplicates found"**
- Check if source data exists
- Verify normalized data generation worked
- Review duplicate detection logic

### Getting Help:
- Check the console output for specific error messages
- Review database logs for connection issues
- Ensure all required tables exist in your database
