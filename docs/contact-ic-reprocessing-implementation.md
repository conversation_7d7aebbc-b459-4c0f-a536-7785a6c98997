# Contact Investment Criteria Re-processing Implementation

## Overview

This document describes the implementation of automatic contact investment criteria (IC) re-processing when company investment criteria is updated. The system ensures that when a company's IC changes, all associated contacts that have already completed IC processing will be automatically re-processed to reflect the updated company information.

## Architecture

### Components Involved

1. **API Endpoint**: `/api/investment-criteria/reprocess-contacts/route.ts`
2. **UI Components**: 
   - `InvestmentCriteriaEditModal.tsx` (for editing existing IC)
   - `InvestmentCriteriaForm.tsx` (for creating new IC)
3. **Processor**: `ContactInvestmentCriteriaProcessor.ts`
4. **Database Tables**: 
   - `investment_criteria_central` (stores IC for companies and contacts)
   - `contacts` (tracks processing status)

### Flow Diagram

```mermaid
graph TB
    A[Company IC Updated] --> B[InvestmentCriteriaEditModal/Form]
    B --> C[Call triggerContactReprocessing]
    C --> D[API: /reprocess-contacts]
    D --> E[Find Related Contacts]
    E --> F{Contacts with Completed IC?}
    F -->|Yes| G[Reset Status to Pending]
    F -->|No| H[No Action Needed]
    G --> I[Delete Existing Contact IC]
    I --> J[Trigger Processing]
    J --> K[ContactInvestmentCriteriaProcessor]
    K --> L[Re-process Using Updated Company IC]
    L --> M[Update Contact Status to Completed]
```

## Implementation Details

### 1. API Endpoint (`/api/investment-criteria/reprocess-contacts/route.ts`)

**Purpose**: Handles the logic for finding and queuing contacts for re-processing.

**Key Features**:
- Finds contacts with `contact_investment_criteria_status = 'completed'` for the given company
- Resets their status to `'pending'` to trigger re-processing
- Deletes existing contact IC records to force fresh processing
- Optionally triggers immediate processing
- Returns summary of actions taken

**Parameters**:
```typescript
{
  companyId: number,
  triggerProcessing?: boolean
}
```

**Response**:
```typescript
{
  success: boolean,
  contactsReprocessed: number,
  contacts: Array<{id: number, name: string, email: string}>,
  processingTriggered?: boolean
}
```

### 2. UI Integration

**InvestmentCriteriaEditModal.tsx**:
- Added `triggerContactReprocessing()` function
- Modified `handleSave()` to call re-processing API after successful save
- Only triggers for company IC (`entity_type === 'company'`)
- Shows success message with count of contacts re-processed

**InvestmentCriteriaForm.tsx**:
- Added new props: `entityType` and `entityId` to identify company vs contact IC
- Added same re-processing logic as the edit modal
- Requires callers to pass entity type and ID when using for company IC

### 3. Processor Enhancement

**ContactInvestmentCriteriaProcessor.ts**:
- Added `reprocessContacts()` method for direct batch re-processing
- Added `isContactReprocessing()` to detect re-processing scenarios
- Enhanced logging to distinguish between new processing and re-processing
- Added `getContactData()` helper method

### 4. Database Operations

**Contact Status Management**:
```sql
-- Reset contacts to pending status
UPDATE contacts 
SET 
  contact_investment_criteria_status = 'pending',
  contact_investment_criteria_error = NULL,
  contact_investment_criteria_error_count = 0,
  updated_at = NOW()
WHERE company_id = $companyId
  AND contact_investment_criteria_status = 'completed'
```

**Clean Existing Records**:
```sql
-- Delete existing contact IC to force fresh processing
DELETE FROM investment_criteria_central
WHERE entity_id IN (contact_ids) AND entity_type = 'contact'
```

## Usage Examples

### 1. Using the API Directly

```javascript
const response = await fetch('/api/investment-criteria/reprocess-contacts', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    companyId: 123,
    triggerProcessing: true
  })
})

const result = await response.json()
// Result: { success: true, contactsReprocessed: 5, contacts: [...] }
```

### 2. Using in UI Components

**For Company IC Form**:
```tsx
<InvestmentCriteriaForm
  entityType="company"
  entityId={companyId}
  onSave={handleSave}
  // ... other props
/>
```

**For Contact IC Form** (no re-processing):
```tsx
<InvestmentCriteriaForm
  entityType="contact"
  entityId={contactId}
  onSave={handleSave}
  // ... other props
/>
```

## Error Handling

### API Level
- Transaction rollback on database errors
- Graceful handling of processing trigger failures
- Detailed error logging with contact context

### UI Level
- Non-blocking: Main save operation succeeds even if re-processing fails
- User sees success message regardless of re-processing outcome
- Errors logged to console for debugging

### Processor Level
- Individual contact failures don't stop batch processing
- Detailed logging for each contact processing attempt
- Status tracking for failed re-processing attempts

## Monitoring and Logging

### Key Log Messages
- `"Starting re-processing for X contacts due to company IC update"`
- `"Re-processing contact X using COMPANY IC MATCHING approach"`
- `"Re-processing completed: X/Y successful"`

### Database Status Tracking
- Contact processing status stored in `contacts.contact_investment_criteria_status`
- Error counts tracked in `contacts.contact_investment_criteria_error_count`
- Processing dates in `contacts.contact_investment_criteria_date`

## Testing Scenarios

### 1. Happy Path
1. Update company IC via edit modal
2. Verify contacts are reset to pending status
3. Verify processing is triggered
4. Verify contacts get new IC based on updated company data

### 2. Edge Cases
- Company with no contacts with completed IC
- Company with contacts in various processing states
- Database transaction failures
- Processing trigger failures

### 3. Performance
- Large numbers of contacts (100+) for single company
- Multiple concurrent company IC updates
- Processing queue overflow scenarios

## Configuration

### Rate Limiting
The ContactInvestmentCriteriaProcessor uses specific bottleneck configuration for re-processing:

```typescript
const contactICBottleneckConfig = {
  maxConcurrent: 10,
  minTime: 1000,
  retryAttempts: 3,
  retryDelayBase: 2500,
  retryDelayMax: 40000,
  timeout: 300000,
  highWater: 200,
  strategy: 'OVERFLOW'
}
```

### Environment Variables
- `PERPLEXITY_API_KEY`: For LLM processing
- `NEXTAUTH_URL`: For internal API calls

## Future Enhancements

1. **Real-time Notifications**: WebSocket updates for re-processing progress
2. **Batch UI**: Interface for bulk company IC updates
3. **Processing Metrics**: Dashboard for re-processing statistics
4. **Selective Re-processing**: Choose which contacts to re-process
5. **Processing History**: Audit trail for re-processing events

## Troubleshooting

### Common Issues

**Re-processing not triggered**:
- Check if `entityType` and `entityId` props are correctly passed
- Verify company IC is being saved with `entity_type = 'company'`

**Contacts not found for re-processing**:
- Ensure contacts have `contact_investment_criteria_status = 'completed'`
- Verify company_id relationship in contacts table

**Processing fails**:
- Check processor logs for specific error messages
- Verify Perplexity API key and rate limits
- Check database connection and permissions

### Debug Steps

1. Check API response for contact count and IDs
2. Verify database status changes
3. Monitor processor logs for individual contact processing
4. Check error counts and status in contacts table
