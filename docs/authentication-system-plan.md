# Authentication System Implementation Plan

## Current State Analysis

### Existing Infrastructure
- **Database**: Users table already exists with proper structure
  - `user_id` (primary key, auto-increment)
  - `username` (varchar, not null)
  - `email` (varchar, not null)
  - `password_hash` (varchar, not null)
  - `role` (varchar, nullable)
  - `created_at`, `updated_at` (timestamps)
  - `display_name` (varchar, nullable)
  - `avatar_url` (text, nullable)

- **Current Auth**: Simple password-based system with localStorage (non-secure)
- **Libraries**: NextAuth v4.24.11 already installed
- **Framework**: Next.js 15.3.5 with App Router

### Problems with Current System
1. Uses hardcoded passwords stored in client-side code
2. Uses localStorage for session management (insecure)
3. No proper user management
4. No session validation
5. No password hashing verification

## Recommended Solution: NextAuth Implementation

### Why NextAuth?
✅ **Already installed** - No additional dependencies needed  
✅ **Industry standard** - Battle-tested authentication solution  
✅ **JWT + Database sessions** - Secure session management  
✅ **Built-in RBAC** - Role-based access control support  
✅ **Middleware support** - Route protection out of the box  
✅ **TypeScript support** - Full type safety  

### Alternative Libraries Considered
- **Clerk**: Excellent but requires subscription for production usage
- **Auth0**: Overkill for this use case, external dependency
- **Supabase Auth**: Would require additional setup
- **Custom implementation**: Time-consuming and security-prone

## Implementation Plan

### Phase 1: Database Setup
1. **Update Users Table** (if needed)
   - Add `email_verified` column
   - Add `last_login` timestamp
   - Ensure password hashing compatibility

2. **Create User Notifications Table**
   ```sql
   CREATE TABLE user_notifications (
     id SERIAL PRIMARY KEY,
     user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
     title VARCHAR(255) NOT NULL,
     message TEXT NOT NULL,
     type VARCHAR(50) DEFAULT 'info', -- info, warning, error, success
     is_read BOOLEAN DEFAULT FALSE,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     read_at TIMESTAMP NULL
   );
   ```

3. **Create User Edit Log Table**
   ```sql
   CREATE TABLE user_edit_logs (
     id SERIAL PRIMARY KEY,
     user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
     action VARCHAR(100) NOT NULL, -- login, profile_update, password_change, etc.
     details JSONB, -- Store additional action details
     ip_address INET,
     user_agent TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

### Phase 2: NextAuth Configuration
1. **Configure NextAuth** (`src/lib/auth.ts`)
   - Database adapter for PostgreSQL
   - Credentials provider with bcrypt password verification
   - JWT strategy with role-based claims
   - Session callbacks for user data

2. **Create Authentication API Route** (`src/app/api/auth/[...nextauth]/route.ts`)
   - Handle all auth operations
   - Custom sign-in logic
   - Session management

3. **Create Middleware** (`middleware.ts`)
   - Protect all dashboard routes
   - Role-based route access control
   - Redirect unauthenticated users to login

### Phase 3: User Interface Updates
1. **Replace PasswordCheck Component**
   - Create proper login form with email/password
   - Add registration form (if needed)
   - Implement proper error handling

2. **Update Navigation Component**
   - Add user profile dropdown
   - Display user name/avatar
   - Add logout functionality
   - Role-based menu visibility

3. **Create User Settings Page**
   - Profile picture upload (Gravatar integration)
   - Name/email editing
   - Password change form
   - Notification preferences

### Phase 4: Notification System
1. **Notification API Routes**
   - GET `/api/notifications` - Fetch user notifications
   - POST `/api/notifications/mark-read` - Mark as read
   - POST `/api/notifications/create` - Create notifications (admin)

2. **Notification Components**
   - Notification bell with unread count
   - Notification dropdown/panel
   - Toast notifications for real-time updates

### Phase 5: Audit Logging
1. **Logging Utilities**
   - Helper functions to log user actions
   - IP address and user agent tracking
   - Integration with existing API routes

2. **Admin Dashboard for Logs**
   - View user activity logs
   - Filter by user, action, date range
   - Export functionality

## Role-Based Access Control

### Roles Definition
- **guest**: Read-only access to specific sections
- **user**: Standard user access
- **admin**: Full access to all features

### Route Protection Matrix
| Route | Guest | User | Admin |
|-------|-------|------|-------|
| `/dashboard/projections` | ✅ | ✅ | ✅ |
| `/dashboard/people` | ❌ | ✅ | ✅ |
| `/dashboard/companies` | ❌ | ✅ | ✅ |
| `/dashboard/articles` | ❌ | ✅ | ✅ |
| `/dashboard/deals` | ❌ | ✅ | ✅ |
| `/dashboard/configuration` | ❌ | ❌ | ✅ |
| `/dashboard/processing` | ❌ | ❌ | ✅ |

## Security Considerations

### Password Security
- Use bcrypt for password hashing (already in database)
- Implement password strength requirements
- Add password reset functionality

### Session Security
- JWT tokens with short expiration
- Secure HTTP-only cookies
- CSRF protection via NextAuth

### Data Protection
- Input validation on all forms
- SQL injection prevention (already using parameterized queries)
- Rate limiting on authentication endpoints

## Migration Strategy

### Phase 1: Backward Compatibility
1. Keep existing password system working
2. Implement new NextAuth system alongside
3. Add migration path for existing users

### Phase 2: Data Migration
1. Create admin user accounts in database
2. Hash existing passwords (if any plain text exists)
3. Set appropriate roles for existing users

### Phase 3: Gradual Rollout
1. Test new system in development
2. Deploy with feature flags
3. Gradually migrate users
4. Remove old authentication system

## Implementation Timeline

### Week 1: Database & Backend
- [ ] Create database migrations
- [ ] Configure NextAuth
- [ ] Set up API routes
- [ ] Create middleware

### Week 2: Frontend Components
- [ ] Replace login system
- [ ] Update navigation
- [ ] Create user settings page
- [ ] Implement logout functionality

### Week 3: Notifications & Logging
- [ ] Implement notification system
- [ ] Add audit logging
- [ ] Create admin interfaces
- [ ] Testing and bug fixes

### Week 4: Testing & Deployment
- [ ] End-to-end testing
- [ ] Security testing
- [ ] Performance optimization
- [ ] Production deployment

## File Structure After Implementation

```
src/
├── app/
│   ├── api/
│   │   ├── auth/
│   │   │   └── [...nextauth]/
│   │   │       └── route.ts
│   │   ├── notifications/
│   │   │   ├── route.ts
│   │   │   └── mark-read/
│   │   │       └── route.ts
│   │   └── user/
│   │       ├── profile/
│   │       │   └── route.ts
│   │       └── logs/
│   │           └── route.ts
│   ├── login/
│   │   └── page.tsx
│   └── settings/
│       └── page.tsx
├── components/
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   ├── LogoutButton.tsx
│   │   └── ProtectedRoute.tsx
│   ├── notifications/
│   │   ├── NotificationBell.tsx
│   │   ├── NotificationPanel.tsx
│   │   └── NotificationItem.tsx
│   └── settings/
│       ├── ProfileSettings.tsx
│       ├── PasswordChange.tsx
│       └── NotificationSettings.tsx
├── lib/
│   ├── auth.ts (NextAuth config)
│   ├── auth-utils.ts
│   └── audit-logger.ts
├── types/
│   ├── auth.ts
│   └── notifications.ts
└── middleware.ts
```

## Environment Variables Required

```bash
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3030
NEXTAUTH_SECRET=your-secret-key-here

# Database (already configured)
DATABASE_URL=your-postgres-connection-string
```

## Conclusion

This implementation provides a robust, secure, and scalable authentication system that leverages existing infrastructure while adding modern features like notifications and audit logging. The use of NextAuth ensures industry-standard security practices and reduces development time significantly.

The phased approach allows for smooth migration without disrupting existing functionality, while the role-based access control provides the flexibility needed for different user types.
