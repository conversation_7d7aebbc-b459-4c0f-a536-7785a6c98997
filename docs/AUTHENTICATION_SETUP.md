# Authentication System Setup Guide

## Overview

This guide covers the setup and configuration of the NextAuth-based authentication system implemented in the ANAX Dashboard. The system includes user management, role-based access control, notifications, and audit logging.

## Prerequisites

Before setting up authentication, ensure you have:

1. PostgreSQL database running and accessible
2. Node.js and pnpm installed
3. Environment variables configured (see below)

## Environment Variables

Add the following environment variables to your `.env` file:

```bash
# NextAuth Configuration (Required)
NEXTAUTH_URL=http://localhost:3030
NEXTAUTH_SECRET=your-super-secret-key-here-change-this-in-production

# Database Connection (Already configured)
DATABASE_URL=your-postgres-connection-string

# Optional: For deployment
NODE_ENV=production  # or development
```

### Important Notes:

1. **NEXTAUTH_SECRET**: Generate a secure random string for this. In production, use a cryptographically secure secret.
2. **NEXTAUTH_URL**: Update this to match your deployment URL in production.

## Database Setup

### 1. Run Database Migrations

First, create the necessary tables by running the SQL migrations:

```bash
pnpm run migrate:local
```

This will create the following tables:
- `user_notifications` - Stores user notifications
- `user_edit_logs` - Stores audit trail of user actions

### 2. Create Test Users

Run the script to create test users for different access levels:

```bash
pnpm run auth:create-test-users
```

This creates three test accounts:

| Role  | Email            | Password  | Access Level |
|-------|------------------|-----------|--------------|
| Admin | <EMAIL>   | admin123  | Full access to all features |
| User  | <EMAIL>    | user123   | Standard user access |
| Guest | <EMAIL>   | guest123  | Limited access (projections only) |

## Features Implemented

### 1. Authentication & Authorization
- ✅ Email/password login with NextAuth
- ✅ JWT-based session management
- ✅ Password hashing with bcrypt
- ✅ Role-based access control (admin/user/guest)
- ✅ Route protection middleware
- ✅ Automatic session validation

### 2. User Management
- ✅ User profile management
- ✅ Password change functionality
- ✅ Gravatar integration for profile pictures
- ✅ User settings page

### 3. Notifications System
- ✅ In-app notifications with bell icon
- ✅ Notification types (info, warning, error, success)
- ✅ Mark as read/unread functionality
- ✅ Real-time notification polling
- ✅ Admin can create system-wide notifications

### 4. Audit Logging
- ✅ User action logging (login, logout, profile updates)
- ✅ IP address and user agent tracking
- ✅ JSON-based detail storage for audit trails

## File Structure

```
src/
├── app/
│   ├── api/
│   │   ├── auth/
│   │   │   └── [...nextauth]/route.ts      # NextAuth API route
│   │   ├── notifications/
│   │   │   ├── route.ts                    # Notifications API
│   │   │   └── mark-read/route.ts          # Mark notifications as read
│   │   └── user/
│   │       ├── profile/route.ts            # User profile API
│   │       └── change-password/route.ts    # Password change API
│   ├── login/
│   │   └── page.tsx                        # Login page
│   └── dashboard/
│       └── settings/
│           └── page.tsx                    # User settings page
├── components/
│   ├── auth/
│   ├── notifications/
│   │   ├── NotificationBell.tsx            # Notification bell component
│   │   └── NotificationPanel.tsx           # Notification dropdown panel
│   └── dashboard/
│       ├── Dashboard.tsx                   # Updated with NextAuth
│       └── Navigation.tsx                  # Updated with user info & logout
├── lib/
│   ├── auth-config.ts                      # NextAuth configuration
│   └── db.ts                               # Database connection (existing)
├── types/
│   └── next-auth.d.ts                      # NextAuth type extensions
└── middleware.ts                           # Route protection middleware
```

## Usage Instructions

### 1. Starting the Application

1. Ensure your PostgreSQL database is running
2. Run migrations: `pnpm run migrate:local`
3. Create test users: `pnpm run auth:create-test-users`
4. Start the development server: `pnpm run dev`
5. Navigate to `http://localhost:3030`

### 2. Login Process

1. You'll be automatically redirected to `/login` if not authenticated
2. Use any of the test accounts created above
3. After successful login, you'll be redirected to the appropriate dashboard section based on your role

### 3. Role-Based Access

- **Admin**: Full access to all features including configuration and processing
- **User**: Access to most features except admin-only sections
- **Guest**: Limited access to projections and whitepapers only

### 4. User Settings

Users can access settings via:
1. Click the settings icon in the navigation
2. Navigate to `/dashboard/settings`
3. Available options:
   - Update display name
   - Change password
   - Configure notification preferences
   - View account information

### 5. Notifications

- Bell icon in navigation shows unread count
- Click to view notification panel
- Mark individual notifications as read
- Mark all notifications as read option
- Real-time polling for new notifications (every 30 seconds)

## API Endpoints

### Authentication
- `GET/POST /api/auth/[...nextauth]` - NextAuth endpoints
- `POST /api/user/change-password` - Change user password
- `GET/PUT /api/user/profile` - Get/update user profile

### Notifications
- `GET /api/notifications` - Get user notifications
- `POST /api/notifications` - Create notification (admin only)
- `POST /api/notifications/mark-read` - Mark notifications as read

## Database Schema

### Users Table (existing, enhanced)
```sql
CREATE TABLE users (
  user_id SERIAL PRIMARY KEY,
  username VARCHAR NOT NULL,
  email VARCHAR NOT NULL UNIQUE,
  password_hash VARCHAR NOT NULL,
  role VARCHAR DEFAULT 'user',
  display_name VARCHAR,
  avatar_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### User Notifications Table (new)
```sql
CREATE TABLE user_notifications (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(50) DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  read_at TIMESTAMP NULL
);
```

### User Edit Logs Table (new)
```sql
CREATE TABLE user_edit_logs (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
  action VARCHAR(100) NOT NULL,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Security Features

1. **Password Security**: bcrypt hashing with 12 salt rounds
2. **Session Security**: JWT tokens with secure HTTP-only cookies
3. **Route Protection**: Middleware-based authentication checks
4. **CSRF Protection**: Built into NextAuth
5. **Input Validation**: Server-side validation on all endpoints
6. **Audit Logging**: Complete action tracking with IP addresses

## Troubleshooting

### Common Issues

1. **"Unauthorized" errors**
   - Check if NEXTAUTH_SECRET is set
   - Verify database connection
   - Ensure users table has required columns

2. **Login redirects not working**
   - Check NEXTAUTH_URL matches your domain
   - Verify middleware configuration

3. **Database connection errors**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL environment variable
   - Run migrations if tables don't exist

4. **Notifications not loading**
   - Check if user_notifications table exists
   - Verify API endpoints are accessible
   - Check browser network tab for errors

### Logs and Debugging

- Authentication logs: Check server console
- Database queries: Enable PostgreSQL query logging
- Client-side errors: Check browser developer tools
- API errors: Monitor network requests in browser

## Migration from Old System

The old password-based authentication system has been completely replaced. The key changes:

1. **Removed**: `PasswordCheck` component and localStorage-based auth
2. **Added**: NextAuth-based login with database users
3. **Enhanced**: Role-based access control with proper session management
4. **New**: User profile management and notifications system

Existing users will need to be migrated to the new system or new accounts created using the test user script.

## Production Deployment

For production deployment:

1. Set secure environment variables
2. Use HTTPS (update NEXTAUTH_URL)
3. Configure database with connection pooling
4. Set up proper logging and monitoring
5. Implement backup strategies for user data
6. Consider implementing rate limiting
7. Set up email notifications for security events

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review server logs for error details
3. Verify all environment variables are set
4. Ensure database migrations have run successfully
5. Test with the provided test user accounts first
