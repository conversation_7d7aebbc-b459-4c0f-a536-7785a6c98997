# Admin System & User Impersonation Guide

## Overview

This document describes the comprehensive admin system and user impersonation features that have been implemented in the Anax Dashboard application. The system provides secure user management, role-based access control, and admin impersonation capabilities.

## Features

### 🔐 User Management
- **Database-driven authentication** (replaces hardcoded passwords)
- **Role-based access control** (admin, manager, user, guest)
- **Permission-based authorization** (granular permission system)
- **Account lockout protection** (after 5 failed attempts)
- **Password change enforcement** (force password change on first login)
- **User activity logging** (complete audit trail)

### 👤 User Impersonation
- **Secure impersonation** for admins to troubleshoot user issues
- **Session isolation** with proper tracking
- **Activity logging** for all impersonated actions
- **Time-limited sessions** (2-hour expiry)
- **Easy session termination**

### 🛡️ Admin Panel
- **User CRUD operations** (create, read, update, delete)
- **Password management** (admin can reset any user password)
- **Role and permission management**
- **Real-time impersonation monitoring**
- **User activity dashboard**
- **Search and filtering capabilities**

## Setup Instructions

### 1. Database Setup

Run the setup script to initialize the admin system:

```bash
# Using ts-node (recommended)
npx ts-node scripts/setup-admin-system.ts

# Or compile and run
npm run build
node dist/scripts/setup-admin-system.js
```

This will:
- Create the `users` and `user_activity_log` tables
- Set up indexes for optimal performance
- Create a default admin user
- Create sample users for testing

### 2. Environment Variables

Ensure your `.env.local` file includes:

```env
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000
# Your existing database connection variables
```

### 3. Default Credentials

After setup, use these credentials to access the admin panel:

**Default Admin:**
- Email: `<EMAIL>`
- Password: `admin123`

**Sample Users:** (for testing)
- Manager: `<EMAIL>` / `demo123`
- User: `<EMAIL>` / `demo123`

⚠️ **Important:** Change all default passwords immediately in production!

## Usage Guide

### Accessing the Admin Panel

1. Log in as an admin user
2. Navigate to `/admin` 
3. The admin panel will be accessible if you have admin privileges

### User Management

#### Creating a New User
1. Click "Add User" in the admin panel
2. Fill in the required information:
   - **Basic Info:** First name, last name, email
   - **Credentials:** Username (optional), password
   - **Permissions:** Role and admin status
   - **Settings:** Force password change, active status
3. Click "Create User"

#### Editing a User
1. Find the user in the users list
2. Click the more options menu (⋮) for that user
3. Select "Edit User"
4. Modify the desired fields
5. Save changes

#### Changing User Passwords
1. In the user options menu, select "Change Password"
2. Enter a new password (minimum 8 characters)
3. Choose whether to force password change on next login
4. Confirm the change

#### Deactivating/Activating Users
1. Use the toggle switch in the user card
2. Or select "Deactivate/Activate" from the options menu
3. Deactivated users cannot log in

### User Impersonation

#### Starting Impersonation
1. In the admin panel, find the target user
2. Click the options menu (⋮) for that user
3. Select "Impersonate"
4. Confirm the action (optional reason can be provided)
5. You'll be logged in as that user

#### While Impersonating
- A prominent orange banner shows impersonation status
- All actions are logged with impersonation tracking
- Session automatically expires after 2 hours
- You can end impersonation at any time

#### Ending Impersonation
1. Click "Stop Impersonation" in the banner
2. Or use the admin panel's impersonation management
3. You'll return to your original admin session

## Security Features

### Authentication Security
- **Bcrypt password hashing** with salt
- **Account lockout** after 5 failed attempts (30-minute lockout)
- **Session management** with JWT tokens
- **Password complexity** enforcement (minimum 8 characters)

### Impersonation Security
- **Admin-only access** with proper permission checks
- **Cannot impersonate other admins** (security measure)
- **Session isolation** prevents privilege escalation
- **Complete audit trail** of all impersonated actions
- **Time-limited sessions** (2-hour expiry)

### Activity Logging
All user actions are logged with:
- User ID and action type
- Timestamp and IP address
- Browser/user agent information
- Impersonation status
- Before/after values for changes

## API Endpoints

### User Management
```
GET    /api/admin/users              # List users (paginated)
POST   /api/admin/users              # Create new user
GET    /api/admin/users/[id]         # Get user details
PUT    /api/admin/users/[id]         # Update user
DELETE /api/admin/users/[id]         # Delete user (soft delete)
PUT    /api/admin/users/[id]/password # Change user password
```

### Impersonation
```
POST   /api/admin/impersonate        # Start impersonating user
DELETE /api/admin/impersonate        # Stop impersonating user
GET    /api/admin/impersonate        # Get active impersonations
```

## Permissions System

### Default Permissions by Role

#### Admin
- All permissions (full access)

#### Manager
- `user:read`, `user:write`
- `data:export`, `data:import`
- `campaign:manage`, `campaign:send`

#### User
- `user:read`
- `data:export`

#### Guest
- No default permissions

### Adding Custom Permissions

1. Define new permissions in `src/lib/auth/admin.ts`:
```typescript
export const PERMISSIONS = {
  // ... existing permissions
  CUSTOM_FEATURE: 'feature:custom',
} as const;
```

2. Add to role permissions:
```typescript
export const ROLE_PERMISSIONS = {
  admin: [...Object.values(PERMISSIONS)],
  // ... other roles
}
```

3. Use in components:
```typescript
import { hasPermission } from '@/lib/auth/admin';

if (hasPermission(user, PERMISSIONS.CUSTOM_FEATURE)) {
  // Show feature
}
```

## Component Usage

### Protecting Routes
Use the `AdminRoute` component to protect admin-only pages:

```tsx
import AdminRoute from '@/components/auth/AdminRoute';

export default function ProtectedPage() {
  return (
    <AdminRoute requiredPermission="admin:manage_users">
      <YourProtectedContent />
    </AdminRoute>
  );
}
```

### Showing Impersonation Status
The `ImpersonationBanner` component automatically shows when impersonating:

```tsx
import ImpersonationBanner from '@/components/auth/ImpersonationBanner';

export default function Layout({ children }) {
  return (
    <div>
      <ImpersonationBanner />
      {children}
    </div>
  );
}
```

### Using Session Data
Access extended session data with type safety:

```tsx
import { useSession } from 'next-auth/react';

interface ExtendedSession {
  user: {
    user_id: number;
    role: string;
    is_admin: boolean;
    permissions: string[];
  };
  isImpersonating?: boolean;
}

const { data: session } = useSession() as { data: ExtendedSession | null };

if (session?.user.is_admin) {
  // Show admin features
}
```

## Database Schema

### Users Table
Key fields:
- `user_id` (Primary Key)
- `email` (Unique, authentication)
- `password_hash` / `salt` (Secure password storage)
- `role` / `is_admin` / `permissions` (Authorization)
- `is_active` (Account status)
- `impersonated_by` / `impersonation_started_at` (Impersonation tracking)
- Audit fields: `created_at`, `updated_at`, `created_by`, etc.

### User Activity Log Table
Tracks all user actions:
- `user_id` (Who performed the action)
- `performed_by` (Who actually performed it, if different)
- `action` / `description` (What was done)
- `impersonation_active` (Whether it was during impersonation)
- `ip_address` / `user_agent` (Client information)
- `old_values` / `new_values` (Change tracking)

## Troubleshooting

### Common Issues

#### "Admin access required" error
- Verify the user has `is_admin = true` in the database
- Check that the user account is active (`is_active = true`)
- Ensure the user hasn't been soft-deleted (`deleted_at IS NULL`)

#### Impersonation not working
- Confirm admin has `admin:impersonate` permission
- Check that target user is active and not deleted
- Verify target user is not already being impersonated
- Check browser console for token-related errors

#### Database connection errors
- Verify database connection settings in `.env.local`
- Ensure the users tables exist (run setup script)
- Check database user permissions

#### Password authentication failing
- Verify bcrypt is properly installed (`npm install bcrypt @types/bcrypt`)
- Check that password hashing is working in the setup script
- Look for failed login attempt lockouts in user records

### Logs and Debugging

1. **Check user activity logs:**
```sql
SELECT * FROM user_activity_log 
WHERE user_id = [USER_ID] 
ORDER BY created_at DESC;
```

2. **Verify user permissions:**
```sql
SELECT user_id, email, role, is_admin, permissions, is_active
FROM users 
WHERE email = '[EMAIL]';
```

3. **Check impersonation status:**
```sql
SELECT u1.email as target_user, u2.email as admin_user, u1.impersonation_started_at
FROM users u1 
JOIN users u2 ON u1.impersonated_by = u2.user_id
WHERE u1.impersonated_by IS NOT NULL;
```

## Best Practices

### Security
1. **Change default passwords** immediately in production
2. **Use strong passwords** for all admin accounts
3. **Review user permissions** regularly
4. **Monitor activity logs** for suspicious behavior
5. **Limit admin accounts** to necessary personnel only
6. **Implement 2FA** for admin accounts (future enhancement)

### User Management
1. **Use meaningful user roles** that match business needs
2. **Follow principle of least privilege** when assigning permissions
3. **Regularly audit user accounts** and remove inactive users
4. **Document permission changes** with reasons
5. **Use impersonation sparingly** and only for support purposes

### Development
1. **Test permission changes** thoroughly
2. **Use type-safe session interfaces** in components
3. **Log important security events** in user activity
4. **Handle edge cases** (locked accounts, expired sessions)
5. **Keep security dependencies updated** (bcrypt, jose, etc.)

## Future Enhancements

### Planned Features
- **Two-Factor Authentication** (TOTP/SMS)
- **Single Sign-On** (SSO) integration
- **Advanced permission groups** and inheritance
- **User invitation system** with email verification
- **API key management** for programmatic access
- **Advanced audit reporting** and analytics

### Integration Opportunities
- **Webhook notifications** for user events
- **Slack/Discord integration** for admin notifications
- **LDAP/Active Directory** synchronization
- **Compliance reporting** (SOX, HIPAA, etc.)

For questions or issues, please check the troubleshooting section or contact the development team.
