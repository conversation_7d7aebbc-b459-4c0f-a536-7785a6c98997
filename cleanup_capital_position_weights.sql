-- Clean up capital position weights table to match specified criteria
-- Remove extra fields and keep only the specified ones

-- First, let's see what we're removing
-- Fields to REMOVE (not in the specified list):
-- - occupancy_rate (dealsv2)
-- - recourse_loan (investment_criteria_debt) 
-- - yield_maintenance (investment_criteria_debt)
-- - occupancy_requirements (investment_criteria_debt)
-- - rate_type (investment_criteria_debt)
-- - structured_loan_tranche (investment_criteria_debt)
-- - target_cash_on_cash_min (investment_criteria_equity)
-- - position_specific_irr (investment_criteria_equity)
-- - typical_closing_timeline_days (investment_criteria_equity)

-- Fields to KEEP (in the specified list):
-- DEALS + PROPERTIES:
-- - deal_amount (as Deal Size)
-- - location (as Location/Region/State/City)
-- - property_type (as Property Type)
-- - subproperty_type (as Sub property Type)
-- - strategy (as Strategies)

-- DEBT:
-- - lien_position (as Lien Position)
-- - loan_to_cost_max (as Loan-To-Cost Maximum)
-- - loan_to_value_max (as Loan-To-value Maximum)
-- - loan_origination_min_fee (as Origination Fee)
-- - loan_min_debt_yield (as Debt Yield)
-- - min_loan_term (as Loan Term Min)
-- - max_loan_term (as Loan Term Max)
-- - min_loan_dscr (as DSCR Minimum)

-- EQUITY:
-- - minimum_internal_rate_of_return (as IRR Minimum)
-- - minimum_yield_on_cost (as Yield on cost minimum)
-- - minimum_equity_multiple (as Equity Multiple)
-- - target_return (as IRR Minimum - this might be duplicate with minimum_internal_rate_of_return)

-- Step 1: Remove extra fields that are not in the specified list
DELETE FROM capital_position_field_weights 
WHERE field_name IN (
    'occupancy_rate',
    'recourse_loan', 
    'yield_maintenance',
    'occupancy_requirements',
    'rate_type',
    'structured_loan_tranche',
    'target_cash_on_cash_min',
    'position_specific_irr',
    'typical_closing_timeline_days'
);

-- Step 2: Check for potential duplicates in equity section
-- We have both 'minimum_internal_rate_of_return' and 'target_return' which might be duplicates
-- Let's keep 'minimum_internal_rate_of_return' as the primary IRR field and remove 'target_return'
-- since they serve the same purpose (IRR Minimum)

DELETE FROM capital_position_field_weights 
WHERE field_name = 'target_return' AND table_name = 'investment_criteria_equity';

-- Step 3: Update descriptions to match the specified naming
UPDATE capital_position_field_weights 
SET description = 'Deal Size matching for all positions'
WHERE field_name = 'deal_amount' AND table_name = 'dealsv2';

UPDATE capital_position_field_weights 
SET description = 'Location field (auto-split into state, city, region) for all positions'
WHERE field_name = 'location' AND table_name = 'properties';

UPDATE capital_position_field_weights 
SET description = 'Property Type matching for all positions'
WHERE field_name = 'property_type' AND table_name = 'properties';

UPDATE capital_position_field_weights 
SET description = 'Sub Property Type matching for all positions'
WHERE field_name = 'subproperty_type' AND table_name = 'properties';

UPDATE capital_position_field_weights 
SET description = 'Strategies matching for all positions'
WHERE field_name = 'strategy' AND table_name = 'dealsv2';

-- Debt field descriptions
UPDATE capital_position_field_weights 
SET description = 'Lien Position matching for debt positions'
WHERE field_name = 'lien_position' AND table_name = 'investment_criteria_debt';

UPDATE capital_position_field_weights 
SET description = 'Loan-To-Cost Maximum matching for debt positions'
WHERE field_name = 'loan_to_cost_max' AND table_name = 'investment_criteria_debt';

UPDATE capital_position_field_weights 
SET description = 'Loan-To-Value Maximum matching for debt positions'
WHERE field_name = 'loan_to_value_max' AND table_name = 'investment_criteria_debt';

UPDATE capital_position_field_weights 
SET description = 'Origination Fee matching for debt positions'
WHERE field_name = 'loan_origination_min_fee' AND table_name = 'investment_criteria_debt';

UPDATE capital_position_field_weights 
SET description = 'Debt Yield matching for debt positions'
WHERE field_name = 'loan_min_debt_yield' AND table_name = 'investment_criteria_debt';

UPDATE capital_position_field_weights 
SET description = 'Loan Term Minimum matching for debt positions'
WHERE field_name = 'min_loan_term' AND table_name = 'investment_criteria_debt';

UPDATE capital_position_field_weights 
SET description = 'Loan Term Maximum matching for debt positions'
WHERE field_name = 'max_loan_term' AND table_name = 'investment_criteria_debt';

UPDATE capital_position_field_weights 
SET description = 'DSCR Minimum matching for debt positions (deal DSCR must be equal to or above minimum)'
WHERE field_name = 'min_loan_dscr' AND table_name = 'investment_criteria_debt';

-- Equity field descriptions
UPDATE capital_position_field_weights 
SET description = 'IRR Minimum matching for equity positions (deal IRR must be equal to or above minimum)'
WHERE field_name = 'minimum_internal_rate_of_return' AND table_name = 'investment_criteria_equity';

UPDATE capital_position_field_weights 
SET description = 'Yield on Cost minimum matching for equity positions (deal YOC must be equal to or above minimum)'
WHERE field_name = 'minimum_yield_on_cost' AND table_name = 'investment_criteria_equity';

UPDATE capital_position_field_weights 
SET description = 'Equity Multiple matching for equity positions (deal EM must be equal to or above minimum)'
WHERE field_name = 'minimum_equity_multiple' AND table_name = 'investment_criteria_equity';

-- Step 4: Verify the final result
SELECT 
    capital_position,
    field_name,
    table_name,
    field_category,
    weight,
    description
FROM capital_position_field_weights 
WHERE is_active = true 
ORDER BY 
    CASE table_name 
        WHEN 'dealsv2' THEN 1
        WHEN 'properties' THEN 2  
        WHEN 'investment_criteria_debt' THEN 3
        WHEN 'investment_criteria_equity' THEN 4
        ELSE 5
    END,
    field_name;
