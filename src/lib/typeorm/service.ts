import { AppDataSource } from "./config";
// Import entities directly so TypeORM can discover them
import { DealsV2 } from "./entities/DealsV2";
import { DealNsfField } from "./entities/DealNsfField";
import { Property } from "./entities/Property";
import { Owner } from "./entities/Owner";
import { InvestmentCriteriaDebt } from "./entities/InvestmentCriteriaDebt";
import { InvestmentCriteriaEquity } from "./entities/InvestmentCriteriaEquity";
import { DealContact } from "./entities/DealContact";
import type { IDealsV2, IDealNsfField, IProperty, IOwner } from "./types";

class TypeORMService {  
  private static instance: TypeORMService;

  private constructor() {}

  public static getInstance(): TypeORMService {
    if (!TypeORMService.instance) {
      TypeORMService.instance = new TypeORMService();
    }
    return TypeORMService.instance;
  }

  /**
   * Initializes the DataSource connection.
   * This method is idempotent and safe to call multiple times.
   */
  public async initialize(): Promise<void> {
    // Check TypeORM's own state to prevent re-initialization.
    if (AppDataSource.isInitialized) {
      return;
    }

    try {
      await AppDataSource.initialize();
      console.log("✅ TypeORM DataSource initialized successfully");
    } catch (error) {
      console.error("❌ Error initializing TypeORM DataSource:", error);
      throw error;
    }
  }

  /**
   * Closes the DataSource connection if it is open.
   */
  public async close(): Promise<void> {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log("✅ TypeORM DataSource closed successfully");
    }
  }
  
  /**
   * Private helper to ensure the DataSource is initialized before use.
   */
  private checkInitialized(): void {
    if (!AppDataSource.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
  }
  public getInitialized(): boolean {
    if (!AppDataSource.isInitialized) {
     return false
    }
    return true
  }
  // --- Repository Getters ---

  public getDealsRepository() {
    this.checkInitialized();
    return AppDataSource.getRepository(DealsV2);
  }

  public getNsfRepository() {
    this.checkInitialized();
    return AppDataSource.getRepository(DealNsfField);
  }

  public getPropertyRepository() {
    this.checkInitialized();
    return AppDataSource.getRepository(Property);
  }

  public getOwnerRepository() {
    this.checkInitialized();
    return AppDataSource.getRepository(Owner);
  }

  public getInvestmentCriteriaDebtRepository() {
    this.checkInitialized();
    return AppDataSource.getRepository(InvestmentCriteriaDebt);
  }

  public getInvestmentCriteriaEquityRepository() {
    this.checkInitialized();
    return AppDataSource.getRepository(InvestmentCriteriaEquity);
  }

  public getDealContactRepository() {
    this.checkInitialized();
    return AppDataSource.getRepository(DealContact);
  }

  /**
   * Gets the raw DataSource instance if needed for advanced operations.
   */
  public getDataSource() {
    this.checkInitialized();
    return AppDataSource;
  }
}

// Export the singleton instance for use throughout the application
export const typeORMService = TypeORMService.getInstance();

// Export the class itself for testing or type-hinting purposes
export { TypeORMService };