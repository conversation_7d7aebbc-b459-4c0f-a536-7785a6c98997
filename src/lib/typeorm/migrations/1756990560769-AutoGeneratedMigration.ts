import { MigrationInterface, QueryRunner } from "typeorm";

export class AutoGeneratedMigration1756990560769 implements MigrationInterface {
    name = 'AutoGeneratedMigration1756990560769'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."idx_dealsv2_published"`);
        await queryRunner.query(`DROP INDEX "public"."idx_dealsv2_published_time"`);
        await queryRunner.query(`ALTER TABLE "dealsv2" ALTER COLUMN "published" SET NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "dealsv2"."published" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "dealsv2"."published_time" IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`COMMENT ON COLUMN "dealsv2"."published_time" IS 'Timestamp when the deal was published to homepage'`);
        await queryRunner.query(`COMMENT ON COLUMN "dealsv2"."published" IS 'Whether the deal is published to homepage (max 10 deals)'`);
        await queryRunner.query(`ALTER TABLE "dealsv2" ALTER COLUMN "published" DROP NOT NULL`);
        await queryRunner.query(`CREATE INDEX "idx_dealsv2_published_time" ON "dealsv2" ("published_time") `);
        await queryRunner.query(`CREATE INDEX "idx_dealsv2_published" ON "dealsv2" ("published") `);
    }

}
