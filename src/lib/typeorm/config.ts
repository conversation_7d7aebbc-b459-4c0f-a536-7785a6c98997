/**
 * TypeORM Configuration - PostgreSQL Only
 * 
 * This configuration is specifically designed to:
 * 1. Only load the PostgreSQL driver
 * 2. Suppress warnings about missing optional database drivers
 * 3. Optimize for PostgreSQL performance
 * 4. Avoid loading unnecessary drivers like MySQL, SQLite, etc.
 */
import { DataSource } from "typeorm";
import * as dotenv from "dotenv";
import * as path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";

// ES module __dirname workaround
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import entities to ensure they're loaded before TypeORM initialization
// This is necessary for entity metadata discovery
import { DealsV2 } from "./entities/DealsV2";
import { DealNsfField } from "./entities/DealNsfField";
import { Property } from "./entities/Property";
import { Owner } from "./entities/Owner";
import { InvestmentCriteriaDebt } from "./entities/InvestmentCriteriaDebt";
import { InvestmentCriteriaEquity } from "./entities/InvestmentCriteriaEquity";
import { DealContact } from "./entities/DealContact";

// Suppress TypeORM warnings about missing optional dependencies
process.env.TYPEORM_SKIP_DRIVER_IMPORTS = "true";
process.env.TYPEORM_DRIVER_EXTENSIONS = "false";
process.env.TYPEORM_LOAD_DRIVERS = "postgres";

// Force PostgreSQL driver only - prevent loading of other drivers
process.env.TYPEORM_DRIVER = "postgres";
process.env.TYPEORM_SKIP_OPTIONAL_DRIVERS = "true";

dotenv.config();

// TypeORM configuration with auto-discovery of entities
export const AppDataSource = new DataSource({
  type: "postgres",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "5432"),
  username: process.env.DB_USER || "postgres",
  password: process.env.DB_PASSWORD || "password",
  database: process.env.DB_DATABASE || "anax_dashboard",
  synchronize: false, // Don't auto-sync to avoid affecting other tables
  logging: process.env.NODE_ENV === "development",
  // Explicitly list entities to ensure they're loaded
  entities: [DealsV2, DealNsfField, Property, Owner, InvestmentCriteriaDebt, InvestmentCriteriaEquity, DealContact],
  // Disable migrations for now to avoid compatibility issues
  // migrations: [path.join(__dirname, "migrations/*.{ts,js}")],
  subscribers: [],
  // Only connect to specific tables, don't touch others
  schema: "public",
  // Migration settings
  migrationsRun: false,
  migrationsTableName: "typeorm_migrations",
  // TypeORM 0.3+ specific configuration
  // Remove driver property as it's not needed in newer versions
  extra: {
    // Connection pooling settings
    max: 20,
    idleTimeoutMillis: 10000, // 2 minute
    connectionTimeoutMillis: 20000, // 20 seconds
    // PostgreSQL-specific options
    application_name: "anax_dashboard",
    // Disable SSL for local development (enable in production)
    ssl: process.env.NODE_ENV === "production" ? { rejectUnauthorized: false } : false,
    // Additional PostgreSQL optimizations
    statement_timeout: 120000, // 60 seconds
    query_timeout: 120000, // 60 seconds
  },

});

// Initialize the data source
export const initializeTypeORM = async () => {
  try {
    // Debug: Log the entities that TypeORM will try to load
    console.log("🔍 TypeORM entities to be loaded:", AppDataSource.options.entities);
    
    await AppDataSource.initialize();
    console.log("✅ TypeORM Data Source has been initialized for deals and nsf tables");
    
    // Debug: Log the actual loaded entities
    const loadedEntities = AppDataSource.entityMetadatas.map(meta => meta.name);
    console.log("📋 Loaded entity metadata:", loadedEntities);
    
    return AppDataSource;
  } catch (error) {
    console.error("❌ Error during TypeORM Data Source initialization:", error);
    throw error;
  }
};

// Get the data source instance
export const getDataSource = () => AppDataSource;

// Close the data source
export const closeTypeORM = async () => {
  if (AppDataSource.isInitialized) {
    await AppDataSource.destroy();
    console.log("TypeORM Data Source has been closed");
  }
}; 