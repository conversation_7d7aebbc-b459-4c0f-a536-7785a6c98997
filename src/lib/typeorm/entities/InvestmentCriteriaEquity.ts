import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from "typeorm";
import type { IInvestmentCriteria } from "../types";

@Entity({ name: "investment_criteria_equity" })
export class InvestmentCriteriaEquity {
  @PrimaryGeneratedColumn({ name: "investment_criteria_equity_id" })
  investmentCriteriaEquityId: number;

  @Column({ name: "investment_criteria_id", type: "integer" })
  investmentCriteriaId: number;

  // Target Returns
  @Column({ name: "target_return", type: "numeric", nullable: true })
  targetReturn: number;

  @Column({ name: "minimum_internal_rate_of_return", type: "numeric", nullable: true })
  minimumInternalRateOfReturn: number;

  @Column({ name: "minimum_yield_on_cost", type: "numeric", nullable: true })
  minimumYieldOnCost: number;

  @Column({ name: "minimum_equity_multiple", type: "numeric", nullable: true })
  minimumEquityMultiple: number;

  @Column({ name: "target_cash_on_cash_min", type: "numeric", nullable: true })
  targetCashOnCashMin: number;

  // Hold Period
  @Column({ name: "min_hold_period_years", type: "integer", nullable: true })
  minHoldPeriodYears: number;

  @Column({ name: "max_hold_period_years", type: "integer", nullable: true })
  maxHoldPeriodYears: number;

  // Ownership Control
  @Column({ name: "ownership_requirement", type: "text", nullable: true })
  ownershipRequirement: string;

  @Column({ name: "attachment_point", type: "numeric", nullable: true })
  attachmentPoint: number;

  @Column({ name: "max_leverage_tolerance", type: "numeric", nullable: true })
  maxLeverageTolerance: number;

  // Closing & Diligence
  @Column({ name: "typical_closing_timeline_days", type: "integer", nullable: true })
  typicalClosingTimelineDays: number;

  @Column({ name: "proof_of_funds_requirement", type: "boolean", nullable: true })
  proofOfFundsRequirement: boolean;

  // Additional info
  @Column({ name: "notes", type: "text", nullable: true })
  notes: string;

  // Program Detail
  @Column({ name: "equity_program_overview", type: "text", nullable: true })
  equityProgramOverview: string;

  @Column({ name: "occupancy_requirements", type: "text", nullable: true })
  occupancyRequirements: string;

  // Additional fields for backward compatibility
  @Column({ name: "yield_on_cost", type: "numeric", nullable: true })
  yieldOnCost: number;

  @Column({ name: "target_return_irr_on_equity", type: "numeric", nullable: true })
  targetReturnIrrOnEquity: number;

  @Column({ name: "equity_multiple", type: "numeric", nullable: true })
  equityMultiple: number;

  // EQUITY POSITION SPECIFIC FIELDS - Automatically determined by capital position
  @Column({ name: "position_specific_irr", type: "numeric", nullable: true })
  positionSpecificIrr: number;

  @Column({ name: "position_specific_equity_multiple", type: "numeric", nullable: true })
  positionSpecificEquityMultiple: number;

  @CreateDateColumn({ name: "created_at", type: "timestamp with time zone" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamp with time zone" })
  updatedAt: Date;


}
