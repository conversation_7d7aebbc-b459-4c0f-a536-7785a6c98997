import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from "typeorm";
// Use interfaces to avoid circular dependencies
import type { IDealsV2, IOwner } from "../types";

/**
 * Property Entity
 * 
 * NOTE: This entity currently only tracks the current state of property fields.
 * For proper audit trail and history tracking, consider implementing:
 * 1. A property_history table to track field changes over time
 * 2. Original vs current value tracking
 * 3. Change timestamps and user attribution
 * 
 * Current implementation updates fields directly without maintaining history.
 */
@Entity({ name: "properties" })
export class Property {
  @PrimaryGeneratedColumn({ name: "property_id" })
  propertyId: number;

  @Column({ name: "owner_id", type: "integer", nullable: true })
  ownerId: number;

  @Column({ name: "address", type: "varchar", nullable: true })
  address: string;

  @Column({ name: "city", type: "varchar", nullable: true })
  city: string;

  @Column({ name: "state", type: "varchar", nullable: true })
  state: string;

  @Column({ name: "zipcode", type: "varchar", nullable: true })
  zipcode: string;

  @Column({ name: "region", type: "varchar", nullable: true })
  region: string;

  @Column({ name: "country", type: "varchar", nullable: true })
  country: string;

  @Column({ name: "market", type: "varchar", nullable: true })
  market: string;

  @Column({ name: "submarket", type: "varchar", nullable: true })
  submarket: string;

  @Column({ name: "neighborhood", type: "varchar", nullable: true })
  neighborhood: string;

  @Column({ name: "property_type", type: "varchar", nullable: true })
  propertyType: string;

  @Column({ name: "subproperty_type", type: "varchar", nullable: true })
  subpropertyType: string;

  @Column({ name: "building_sqft", type: "numeric", nullable: true })
  buildingSqft: number;

  @Column({ name: "land_acres", type: "numeric", nullable: true })
  landAcres: number;

  @Column({ name: "lot_area", type: "numeric", nullable: true })
  lotArea: number;

  @Column({ name: "year_built", type: "integer", nullable: true })
  yearBuilt: number;

  @Column({ name: "year_renovated", type: "integer", nullable: true })
  yearRenovated: number;

  @Column({ name: "latitude", type: "numeric", nullable: true })
  latitude: number;

  @Column({ name: "longitude", type: "numeric", nullable: true })
  longitude: number;

  @Column({ name: "property_status", type: "varchar", nullable: true })
  propertyStatus: string;

  @Column({ name: "number_of_units", type: "integer", nullable: true })
  numberOfUnits: number;

  @Column({ name: "appraisal_value", type: "numeric", nullable: true })
  appraisalValue: number;

  @Column({ name: "appraisal_value_date", type: "date", nullable: true })
  appraisalValueDate: Date;

  @Column({ name: "gsf_gross_square_foot", type: "numeric", nullable: true })
  gsfGrossSquareFoot: number;

  @Column({ name: "zfa_zoning_floor_area", type: "numeric", nullable: true })
  zfaZoningFloorArea: number;

  @Column({ name: "total_nsf_net_square_foot", type: "numeric", nullable: true })
  totalNsfNetSquareFoot: number;

  @Column({ name: "far", type: "numeric", nullable: true })
  far: number;

  @Column({ name: "historical_occupancy_trend", type: "jsonb", nullable: true })
  historicalOccupancyTrend: any;

  @Column({ name: "environmental_risk_score", type: "numeric", nullable: true })
  environmentalRiskScore: number;

  @Column({ name: "property_description", type: "text", nullable: true })
  propertyDescription: string;

  @Column({ name: "floor_area_ratio", type: "numeric", nullable: true })
  floorAreaRatio: number;

  @Column({ name: "zoning_square_footage", type: "numeric", nullable: true })
  zoningSquareFootage: number;

  // Owner relationship
  @ManyToOne("owners", "properties")
  @JoinColumn({ name: "owner_id" })
  owner: IOwner;

  // Deals relationship
  @OneToMany("dealsv2", "property")
  deals: IDealsV2[];

  @CreateDateColumn({ name: "created_at", type: "timestamp with time zone" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamp with time zone" })
  updatedAt: Date;
} 