import { pool } from '@/lib/db';

/**
 * Geographic validation utility for investment criteria
 * Ensures region-state consistency based on central_mapping table
 */
export class GeographicValidator {
  /**
   * Get expected states for given regions from central_mapping
   */
  static async getExpectedStatesForRegions(regions: string[]): Promise<string[]> {
    if (!regions || regions.length === 0) return [];
    
    const client = await pool.connect();
    try {
      const result = await client.query(`
        SELECT ARRAY_AGG(DISTINCT value_2 ORDER BY value_2) as expected_states
        FROM central_mapping 
        WHERE type = 'U.S Regions' 
          AND is_active = true 
          AND value_1 = ANY($1)
          AND value_2 IS NOT NULL
      `, [regions]);
      
      return result.rows[0]?.expected_states || [];
    } catch (error) {
      console.error('Error getting expected states for regions:', error);
      return [];
    } finally {
      client.release();
    }
  }

  /**
   * Validate if states array matches expected states for regions
   */
  static async validateGeographicConsistency(
    regions: string[], 
    states: string[]
  ): Promise<boolean> {
    if (!regions || !states || regions.length === 0 || states.length === 0) {
      return true; // Consider empty as valid
    }

    const expectedStates = await this.getExpectedStatesForRegions(regions);
    
    if (expectedStates.length === 0) {
      return true; // No mapping found, consider valid (might be international)
    }

    // Check if states array contains all expected states and vice versa
    const statesSet = new Set(states);
    const expectedSet = new Set(expectedStates);
    
    return expectedStates.every(state => statesSet.has(state)) &&
           states.every(state => expectedSet.has(state));
  }

  /**
   * Auto-fix geographic data by replacing states with expected states
   */
  static async autoFixGeographicData(
    regions: string[], 
    states: string[]
  ): Promise<string[]> {
    const expectedStates = await this.getExpectedStatesForRegions(regions);
    
    if (expectedStates.length > 0) {
      return expectedStates;
    }
    
    return states; // Return original if no mapping found
  }

  /**
   * Validate and fix geographic data
   */
  static async validateAndFixGeographicData(
    regions: string[], 
    states: string[]
  ): Promise<{ isValid: boolean; fixedStates: string[]; wasFixed: boolean }> {
    const isValid = await this.validateGeographicConsistency(regions, states);
    
    if (isValid) {
      return { isValid: true, fixedStates: states, wasFixed: false };
    }
    
    const fixedStates = await this.autoFixGeographicData(regions, states);
    return { isValid: true, fixedStates, wasFixed: true };
  }

  /**
   * Map common unmapped regions to existing mapped regions
   */
  static mapUnmappedRegions(regions: string[]): string[] {
    const regionMapping: Record<string, string> = {
      'New York City Metropolitan Area': 'Northeast',
      'Financial District': 'Northeast',
      'Tri-State Area': 'Northeast',
      'Tri-State Area (NY, NJ, CT)': 'Northeast',
      'New York Tri-State Area': 'Northeast',
      'New York Tri-State area': 'Northeast',
      'Tri-state area': 'Northeast',
      'Tri-state': 'Northeast',
      'Tri-State region': 'Northeast',
      'New York Metro Area': 'Northeast',
      'New York Metropolitan Area': 'Northeast',
      'New York Metropolitan region': 'Northeast',
      'New York City Metro Area': 'Northeast',
      'New York City metro area': 'Northeast',
      'New York City Area': 'Northeast',
      'New York City and surrounding areas': 'Northeast',
      'Greater New York Metro Area': 'Northeast',
      'Greater New York City area': 'Northeast',
      'New York/New Jersey metro area': 'Northeast',
      'Sun Belt': 'Southeast',
      'Sunbelt': 'Southeast',
      'U.S. Sun Belt': 'Southeast',
      'Major Sun Belt Markets': 'Southeast',
      'Western United States': 'West Coast',
      'Western U.S.': 'West Coast',
      'Western US': 'West Coast',
      'West Coast Region': 'West Coast'
    };

    return regions.map(region => regionMapping[region] || region);
  }

  /**
   * Complete validation and fixing pipeline
   */
  static async processGeographicData(
    regions: string[], 
    states: string[]
  ): Promise<{ processedRegions: string[]; processedStates: string[]; wasFixed: boolean }> {
    // Handle case where we have regions but no states
    if (!regions || regions.length === 0) {
      return { processedRegions: regions || [], processedStates: states || [], wasFixed: false };
    }

    // First, map unmapped regions
    const mappedRegions = this.mapUnmappedRegions(regions);
    const regionsChanged = JSON.stringify(regions) !== JSON.stringify(mappedRegions);

    // Handle case where we have regions but no states - auto-generate states from regions
    if (!states || states.length === 0) {
      const generatedStates = await this.getExpectedStatesForRegions(mappedRegions);
      return {
        processedRegions: mappedRegions,
        processedStates: generatedStates,
        wasFixed: regionsChanged || generatedStates.length > 0
      };
    }

    // Then validate and fix states
    const { isValid, fixedStates, wasFixed } = await this.validateAndFixGeographicData(
      mappedRegions, 
      states
    );

    return {
      processedRegions: mappedRegions,
      processedStates: fixedStates,
      wasFixed: regionsChanged || wasFixed
    };
  }
}
