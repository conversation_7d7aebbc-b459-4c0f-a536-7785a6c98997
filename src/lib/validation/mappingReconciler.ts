/**
 * Mapping reconciliation utilities (exactly two functions as requested)
 */

export type ConsolidatedMappings = {
  PROPERTY_TYPES?: string[]
  STRATEGIES?: string[]
  LOAN_PROGRAMS?: string[]
  LOAN_TYPES?: string[]
  STRUCTURED_LOAN_TRANCHES?: string[]
  RECOURSE_LOAN_TYPES?: string[]
  US_REGIONS?: string[]
  PROPERTY_TYPE_SUBCATEGORY_MAP?: Record<string, string[]>
  US_REGIONS_MAP?: Record<string, string[]>
}


// 1) Process regions and states together
export function processRegions(
  regions: string[] | null | undefined,
  states: string[] | null | undefined,
  usRegionsMap: Record<string, string[]>
): { regions: string[]; states: string[] } {
  const regionKeys = Object.keys(usRegionsMap || {})
  const validRegions = processValidMapping(regions || [], regionKeys)
  const stateKeys = new Set(Object.keys(usRegionsMap || {}))
  const allowedStates = processValidMapping(states || [], Array.from(stateKeys))


  // States from valid regions
  const fromRegions = new Set<string>()
  for (const r of validRegions) {
    const list = Array.isArray(usRegionsMap[r]) ? usRegionsMap[r] : []
    for (const s of list) {
      if (typeof s === 'string') {
        const v = s.trim()
        if (v) fromRegions.add(v)
      }
    }
  }

  // Merge allowed states with region-derived states
  const mergedStates = Array.from(new Set<string>([...allowedStates, ...Array.from(fromRegions)]))
  return { regions: validRegions, states: mergedStates }
}

// 2) Remove out-of-mapping values across the dataset
// 2) Clean a provided list based on allowed list (narrow responsibility)
export function processValidMapping(
  values: string[] | null | undefined,
  allowed: string[] | null | undefined
): string[] {
  if (!Array.isArray(values) || values.length === 0) return []
  if (!Array.isArray(allowed) || allowed.length === 0) return []
  const allowedSet = new Set(
    allowed
      .filter(v => typeof v === 'string')
      .map(v => v.trim())
      .filter(Boolean)
  )
  const cleaned = values
    .filter(v => typeof v === 'string')
    .map(v => v.trim())
    .filter(v => v && allowedSet.has(v))
  return Array.from(new Set(cleaned))
}



