import {
  BaseLLMProvider,
  LLMRequestOptions,
  LoggerInterface,
} from "./BaseLLMProvider";
import { OpenAIProvider } from "./OpenAIProvider";
import { PerplexityProvider } from "./PerplexityProvider";
import { GeminiProvider } from "./GeminiProvider";

export type LLMProviderType = "openai" | "perplexity" | "gemini";

export interface LLMProviderOptions {
  apiKey?: string;
  defaultOptions?: LLMRequestOptions;
  enableTracing?: boolean;
}

/**
 * Factory class for creating LLM providers with optional LangSmith tracing
 */
export class LLMFactory {
  /**
   * Create an LLM provider based on the specified type
   */
  public static createProvider(
    type: LLMProviderType,
    logger: LoggerInterface,
    options: LLMProviderOptions = {}
  ): BaseLLMProvider {
    // Check LANGSMITH_TRACING environment variable
    const envTracingEnabled = process.env.LANGSMITH_TRACING === 'true';
    const { apiKey, defaultOptions, enableTracing = envTracingEnabled } = options;

    switch (type) {
      case "openai":
        return new OpenAIProvider(
          logger,
          apiKey,
          defaultOptions,
          enableTracing
        );
      case "perplexity":
        return new PerplexityProvider(
          logger,
          apiKey,
          defaultOptions,
          enableTracing
        );
      case "gemini":
        return new GeminiProvider(
          logger,
          apiKey,
          defaultOptions,
          enableTracing
        );
      default:
        throw new Error(`Unsupported LLM provider type: ${type}`);
    }
  }

  /**
   * Create an OpenAI provider with LangSmith tracing enabled
   */
  public static createTracedOpenAI(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions?: LLMRequestOptions
  ): OpenAIProvider {
    return new OpenAIProvider(logger, apiKey, defaultOptions, true);
  }

  /**
   * Create an OpenAI provider without tracing
   */
  public static createOpenAIWithoutTracing(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions?: LLMRequestOptions
  ): OpenAIProvider {
    return new OpenAIProvider(logger, apiKey, defaultOptions, false);
  }

  /**
   * Create a Perplexity provider with LangSmith tracing enabled
   */
  public static createTracedPerplexity(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions?: LLMRequestOptions
  ): PerplexityProvider {
    return new PerplexityProvider(logger, apiKey, defaultOptions, true);
  }

  /**
   * Create a Perplexity provider without tracing
   */
  public static createPerplexityWithoutTracing(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions?: LLMRequestOptions
  ): PerplexityProvider {
    return new PerplexityProvider(logger, apiKey, defaultOptions, false);
  }

  /**
   * Create a Gemini provider with LangSmith tracing enabled
   */
  public static createTracedGemini(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions?: LLMRequestOptions
  ): GeminiProvider {
    return new GeminiProvider(logger, apiKey, defaultOptions, true);
  }

  /**
   * Create a Gemini provider without tracing
   */
  public static createGeminiWithoutTracing(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions?: LLMRequestOptions
  ): GeminiProvider {
    return new GeminiProvider(logger, apiKey, defaultOptions, false);
  }

  /**
   * Create an LLM provider with fallback capability
   * If the primary provider fails, the fallback will be used
   */
  public static createWithFallback(
    primaryType: LLMProviderType,
    fallbackType: LLMProviderType,
    logger: LoggerInterface,
    primaryOptions: LLMProviderOptions = {},
    fallbackOptions: LLMProviderOptions = {}
  ): FallbackLLMProvider {
    const primary = this.createProvider(primaryType, logger, primaryOptions);
    const fallback = this.createProvider(fallbackType, logger, fallbackOptions);

    return new FallbackLLMProvider(primary, fallback, logger);
  }
}

/**
 * LLM provider with fallback capability
 * If the primary provider fails, the fallback will be used
 */
export class FallbackLLMProvider extends BaseLLMProvider {
  private primaryProvider: BaseLLMProvider;
  private fallbackProvider: BaseLLMProvider;

  constructor(
    primaryProvider: BaseLLMProvider,
    fallbackProvider: BaseLLMProvider,
    logger: LoggerInterface
  ) {
    super(
      `Fallback(${primaryProvider.getName()}->${fallbackProvider.getName()})`,
      logger
    );
    this.primaryProvider = primaryProvider;
    this.fallbackProvider = fallbackProvider;
  }

  /**
   * Call the primary provider, falling back to the secondary if needed
   */
  public async callLLM(messages: any[], options?: LLMRequestOptions) {
    try {
      // Try the primary provider first
      return await this.primaryProvider.callLLM(messages, options);
    } catch (primaryError) {
      // Log the primary error
      this.logger.log(
        "warn",
        `Primary LLM provider (${this.primaryProvider.getName()}) failed: ${primaryError}. Falling back to ${this.fallbackProvider.getName()}`
      );

      try {
        // Try the fallback provider
        return await this.fallbackProvider.callLLM(messages, options);
      } catch (fallbackError) {
        // If both fail, log and throw
        this.logger.log(
          "error",
          `Fallback LLM provider (${this.fallbackProvider.getName()}) also failed: ${fallbackError}`
        );
        throw new Error(
          `Both LLM providers failed. Primary error: ${primaryError}. Fallback error: ${fallbackError}`
        );
      }
    }
  }

  /**
   * Parse JSON response using the primary provider's method, with fallback
   */
  public parseJsonResponse(response: string): any {
    try {
      // Try the primary provider's parsing method first
      return this.primaryProvider.parseJsonResponse(response);
    } catch (primaryError) {
      // If primary parsing fails, try the fallback
      try {
        return this.fallbackProvider.parseJsonResponse(response);
      } catch (fallbackError) {
        // If both fail, throw an error
        throw new Error(
          `Both providers failed to parse JSON. Primary error: ${primaryError}. Fallback error: ${fallbackError}`
        );
      }
    }
  }
}
