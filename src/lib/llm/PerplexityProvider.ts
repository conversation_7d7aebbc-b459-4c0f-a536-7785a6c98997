import {
  Base<PERSON><PERSON>rovider,
  LLMMessage,
  LLMRequestOptions,
  LLMResponse,
  LoggerInterface,
} from "./BaseLLMProvider";
import { traceable } from "langsmith/traceable";
import * as fs from 'fs';
/**
 * Perplexity AI implementation of the LLM provider with LangSmith tracing
 */
export class PerplexityProvider extends BaseLLMProvider {
  private apiKey: string;
  private readonly defaultPerplexityModel = "sonar";
  private readonly apiUrl = "https://api.perplexity.ai/chat/completions";
  private isTracingEnabled: boolean;

  constructor(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions: LLMRequestOptions = {},
    enableTracing: boolean = true
  ) {
    super("Perplexity", logger, {
      model: defaultOptions.model || "sonar",
      ...defaultOptions,
    });

    // Use API key from env if not provided
    const perplexityApiKey = apiKey || process.env.PERPLEXITY_API_KEY;

    if (!perplexityApiKey) {
      throw new Error(
        "Perplexity API key is required. Set PERPLEXITY_API_KEY environment variable or pass as parameter"
      );
    }

    this.apiKey = perplexityApiKey;
    this.isTracingEnabled = enableTracing;
  }

  /**
   * Call Perplexity API with optional tracing
   */
  public async callLLM(
    messages: LLMMessage[],
    options?: LLMRequestOptions
  ): Promise<LLMResponse> {
    // Create the core implementation function
    const coreImplementation = async (
      messages: LLMMessage[],
      options?: LLMRequestOptions
    ): Promise<LLMResponse> => {
      try {
        const mergedOptions = {
          ...this.defaultOptions,
          ...options,
        };

        const model = mergedOptions.model || this.defaultPerplexityModel;
        this.logger.log("info", `Calling Perplexity with model ${model}`);

        // Convert our generic messages to Perplexity format (similar to OpenAI)
        const perplexityMessages = messages.map((message) => ({
          role: message.role,
          content: message.content,
        }));

        // Build request payload
        const payload = {
          model,
          messages: perplexityMessages,
          temperature: mergedOptions.temperature,
          max_tokens: mergedOptions.maxTokens,
          "search_after_date_filter": "1/1/2021",
          "web_search_options": {
            "search_context_size": "high",
            "search_depth": "comprehensive",
            "include_domains": [
              "google.com",
              "bing.com",
              "yahoo.com",
              "duckduckgo.com",
              "ecosia.org",
              "startpage.com",
              "qwant.com",
              "linkedin.com",
              "bloomberg.com",
              "reuters.com",
              "wsj.com",
              "bisnow.com",
              "globest.com",
              "sec.gov",
              "crunchbase.com",
              "pitchbook.com",
              "prnewswire.com",
              "businesswire.com",
              "marketwatch.com"
            ],
            "search_recency_bias": "recent"
          }
        };
        // Persist input (prompt) payload to disk for traceability
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        // try {
        //   const outDir = 'data/llm-logs'
        //   if (!fs.existsSync(outDir)) {
        //     fs.mkdirSync(outDir, { recursive: true })
        //   }
        //   const inputRecord = {
        //     provider: this.name,
        //     model,
        //     timestamp,
        //     options: mergedOptions,
        //     messages,
        //     payload
        //   }
        //   fs.writeFileSync(`${outDir}/${timestamp}_perplexity_${model}_request.json`, JSON.stringify(inputRecord, null, 2))
        // } catch (e) {
        //   this.logger.log('warn', `Failed to persist LLM input: ${e}`)
        // }

        // Call Perplexity with rate limiting
        const responseData = await this.rateLimitedCall(async () => {
          const response = await fetch(this.apiUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${this.apiKey}`,
            },
            body: JSON.stringify(payload),
          });

          this.logger.log(
            "debug",
            `PerplexityProvider: Response status: ${response.status}`
          );

          if (!response.ok) {
            const errorData = await response.text();
            throw new Error(
              `Perplexity API error (${response.status}): ${errorData}`
            );
          }

          const responseData = await response.json();
          // this.logger.log(
          //   "debug",
          //   `PerplexityProvider: Response data: ${JSON.stringify(responseData)}`
          // );

          // Validate response structure
          if (
            !responseData.choices ||
            !responseData.choices[0] ||
            !responseData.choices[0].message
          ) {
            throw new Error(
              `Invalid response structure from Perplexity: ${JSON.stringify(
                responseData
              )}`
            );
          }

          // Persist output (response) to disk for traceability
          // try {
          //   const outDir = 'data/llm-logs'
          //   if (!fs.existsSync(outDir)) {
          //     fs.mkdirSync(outDir, { recursive: true })
          //   }
          //   const outputRecord = {
          //     provider: this.name,
          //     model: responseData.model || model,
          //     timestamp,
          //     usage: responseData.usage,
          //     raw: responseData
          //   }
          //   fs.writeFileSync(`${outDir}/${timestamp}_perplexity_${model}_response.json`, JSON.stringify(outputRecord, null, 2))
          // } catch (e) {
          //   this.logger.log('warn', `Failed to persist LLM output: ${e}`)
          // }

          return responseData;
        }, 3000); // Use 3s rate limit for Perplexity

        // Extract the content from the response
        const content = responseData.choices[0].message.content;

        if (!content || content.trim() === "") {
          throw new Error("Empty response content from Perplexity");
        }

        // console.log("PerplexityProvider: Response data:", responseData);

        // Return in our standardized format
        return {
          content,
          usage: {
            promptTokens: responseData.usage?.prompt_tokens,
            completionTokens: responseData.usage?.completion_tokens,
            totalTokens: responseData.usage?.total_tokens,
          },
          provider: this.name,
          model: responseData.model || model,
        };
      } catch (error) {
        return this.handleError(error);
      }
    };

    // If tracing is enabled, wrap the function with traceable
    if (this.isTracingEnabled) {
      const traceableCallLLM = traceable(coreImplementation, {
        name: `${this.name}_chat_completion`,
        tags: ["llm", "perplexity", "chat"],
        metadata: {
          provider: this.name,
          model: options?.model || this.defaultPerplexityModel,
          api_url: this.apiUrl,
        },
      });

      return await traceableCallLLM(messages, options);
    } else {
      return await coreImplementation(messages, options);
    }
  }
  /**
   * Clean control characters that cause JSON parsing issues
   */
  private cleanControlCharacters(content: string): string {
    try {
      this.logger.log('debug', 'Cleaning control characters from content')
      
      // First try to extract just the JSON part if it's wrapped in markdown
      let jsonContent = content
      
      // Extract from markdown code blocks
      const markdownMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/)
      if (markdownMatch) {
        jsonContent = markdownMatch[1]
        this.logger.log('debug', 'Extracted from markdown block')
      }
      
      // More targeted control character cleaning that preserves JSON structure
      const cleaned = jsonContent
        // Remove dangerous control characters but preserve structural whitespace
        .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g, '') // Remove control chars but keep \n and \t
        .replace(/[\u0080-\u009F]/g, '') // Remove additional control characters
        .replace(/[\u2000-\u200F]/g, '') // Remove various Unicode spaces
        .replace(/[\u2028-\u2029]/g, '') // Remove line/paragraph separators  
        .replace(/[\uFEFF]/g, '') // Remove byte order mark
        // Only normalize problematic line ending combinations
        .replace(/\r\n/g, '\n') // Convert CRLF to LF
        .replace(/\r/g, '\n') // Convert CR to LF
        // Clean up any malformed quotes or escape sequences within strings
        .replace(/([^\\]|^)\\([^"\\\/bfnrt])/g, '$1\\\\$2') // Fix unescaped backslashes
        .trim()
        
      this.logger.log('debug', `Cleaned content length: ${cleaned.length}`)
      return cleaned
      
    } catch (error) {
      this.logger.log('error', `Error cleaning control characters: ${error}`)
      return content // Return original if cleaning fails
    }
  }

  /**
   * Parse JSON response from Perplexity with robust error handling and cleanup
   * Handles Perplexity-specific patterns like <think> tags and markdown formatting
   */
  public parseJsonResponse<T = any>(content: string): T | null {
    try {
      const cleanedContent = this.cleanControlCharacters(content);
      this.logger.log('debug', `Parsing Perplexity response content (length: ${content.length})`);
      
      let jsonContent = cleanedContent.trim();
      
      // Handle Perplexity's <think> tag format - extract content AFTER </think> tag
      const thinkMatch = jsonContent.match(/<think>[\s\S]*?<\/think>\s*([\s\S]*)/);
      if (thinkMatch) {
        // Extract content that comes AFTER the </think> tag
        const afterThinkContent = thinkMatch[1].trim();
        this.logger.log('debug', `Extracted content after <think> tags (${afterThinkContent.length} chars)`);
        
        if (afterThinkContent) {
          jsonContent = afterThinkContent;
        } else {
          throw new Error('No content found after </think> tag');
        }
      } else {
        // Remove any thinking tags if they appear without content
        jsonContent = jsonContent.replace(/<think>[\s\S]*?<\/think>/g, '');
      }
      
      // Try to extract from markdown code blocks first
      const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
      if (markdownJsonMatch) {
        jsonContent = markdownJsonMatch[1].trim();
        this.logger.log('debug', `Extracted JSON from markdown code block`);
      } else {
        // Try to find JSON object in the content
        const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonContent = jsonMatch[0];
          this.logger.log('debug', `Extracted JSON using regex match`);
        } else {
          // Remove any leading/trailing text that isn't JSON
          jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '');
        }
      }
      
      if (!jsonContent) {
        throw new Error('No JSON content found in response');
      }
      
      // Clean up common JSON formatting issues specific to Perplexity responses
      jsonContent = jsonContent
        .replace(/^\s*```json\s*/, '') // Remove leading ```json
        .replace(/\s*```\s*$/, '') // Remove trailing ```
        .replace(/,\s*}/g, '}') // Remove trailing commas before closing braces
        .replace(/,\s*]/g, ']') // Remove trailing commas before closing brackets
        .replace(/\s*\([^)]*\)\s*,?/g, '') // Remove comments in parentheses
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove /* */ comments
        .replace(/\/\/.*$/gm, '') // Remove // comments
        .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
        .trim();
      
      // Try to parse the JSON
      let parsed: T | null = null;
      try {
        parsed = JSON.parse(jsonContent);
      } catch (parseError) {
        const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
        this.logger.log('warn', `Initial JSON parse failed: ${errorMessage}. Attempting to fix truncated JSON...`);
        
        // Try multiple repair strategies
        const repairAttempts = [
          // Strategy 1: Use base class repair methods
          () => this.repairTruncatedJson(jsonContent),
          // Strategy 2: Try complex repair
          () => this.repairComplexTruncatedJson(jsonContent),
          // Strategy 3: Fix common string termination issues
          () => {
            let fixed = jsonContent;
            // If there's an unterminated string, try to close it
            if (errorMessage.includes('Unterminated string')) {
              // Find the last quote and ensure it's properly closed
              const lastQuoteIndex = fixed.lastIndexOf('"');
              if (lastQuoteIndex > 0) {
                const beforeQuote = fixed.substring(0, lastQuoteIndex + 1);
                // Try to close the JSON structure
                let afterQuote = '';
                const openBraces = (beforeQuote.match(/{/g) || []).length;
                const closeBraces = (beforeQuote.match(/}/g) || []).length;
                const openBrackets = (beforeQuote.match(/\[/g) || []).length;
                const closeBrackets = (beforeQuote.match(/\]/g) || []).length;
                
                // Close missing brackets and braces
                for (let i = 0; i < openBrackets - closeBrackets; i++) {
                  afterQuote += ']';
                }
                for (let i = 0; i < openBraces - closeBraces; i++) {
                  afterQuote += '}';
                }
                
                fixed = beforeQuote + afterQuote;
              }
            }
            return fixed !== jsonContent ? fixed : null;
          }
        ];
        
        for (let i = 0; i < repairAttempts.length; i++) {
          try {
            const fixedJson = repairAttempts[i]();
            if (fixedJson) {
              parsed = JSON.parse(fixedJson);
              this.logger.log('info', `Successfully parsed JSON using repair strategy ${i + 1}`);
              break;
            }
          } catch (repairError) {
            this.logger.log('debug', `Repair strategy ${i + 1} failed: ${repairError}`);
            continue;
          }
        }
        
        // If all repair attempts failed, throw the original error
        if (!parsed) {
          throw parseError;
        }
      }
      
      this.logger.log('debug', 'Successfully parsed Perplexity JSON response');
      return parsed;
    } catch (error) {
      this.logger.log('error', `Failed to parse Perplexity JSON response: ${error}`);
      this.logger.log('debug', `Raw content preview: ${content.substring(0, 1000)}...`);
      return null;
    }
  }

  /**
   * Create a traceable pipeline function for complex workflows
   */
  public createTraceablePipeline<T>(
    name: string,
    pipelineFunction: (
      input: T,
      provider: PerplexityProvider
    ) => Promise<LLMResponse>,
    tags: string[] = []
  ) {
    if (!this.isTracingEnabled) {
      return async (input: T) => pipelineFunction(input, this);
    }

    return traceable(async (input: T) => pipelineFunction(input, this), {
      name,
      tags: ["pipeline", "perplexity", ...tags],
      metadata: {
        provider: this.name,
      },
    });
  }

  /**
   * Get the tracing status
   */
  public isTracingEnabledStatus(): boolean {
    return this.isTracingEnabled;
  }

  /**
   * Enable or disable tracing
   */
  public setTracingEnabled(enabled: boolean) {
    if (this.isTracingEnabled === enabled) {
      return; // No change needed
    }

    this.isTracingEnabled = enabled;
    this.logger.log(
      "info",
      `Perplexity provider tracing ${enabled ? "enabled" : "disabled"}`
    );
  }
}
