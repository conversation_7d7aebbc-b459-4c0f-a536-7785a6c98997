import { pool } from '../db';
import { ProcessorQueueManager } from '../queue/ProcessorQueueManager';

export interface MigrationRequest {
  fileIds: string | string[];
  targetProvider: string;
  priority?: number;
  batchSize?: number;
}

export interface MigrationStatus {
  migrationId: string;
  fileId: string;
  sourceProvider: string;
  targetProvider: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  jobId?: string;
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
}

export class StorageMigrationService {
  private static processorQueueManager = ProcessorQueueManager.getInstance();

  /**
   * Migrate a single file
   */
  static async migrateFile(
    fileId: string,
    targetProvider: string = 'azure',
    priority: number = 0
  ): Promise<string> {
    try {
      // Get file info
      const file = await pool.query(
        'SELECT file_id, storage_provider FROM files WHERE file_id = $1',
        [fileId]
      );

      if (file.rows.length === 0) {
        throw new Error('File not found');
      }

      const sourceProvider = file.rows[0].storage_provider;

      // Create migration record
      const migrationResult = await pool.query(
        'INSERT INTO storage_migrations (file_id, source_provider, target_provider, status) VALUES ($1, $2, $3, $4) RETURNING migration_id',
        [fileId, sourceProvider, targetProvider, 'pending']
      );

      const migrationId = migrationResult.rows[0].migration_id;

      // Add to BullMQ queue
      const jobId = await this.processorQueueManager.addProcessorJob(
        'storage_migration_single',
        {
          fileId,
          sourceProvider,
          targetProvider,
          priority
        },
        {
          priority,
          delay: 0
        }
      );

      // Update migration record with job ID
      await pool.query(
        'UPDATE storage_migrations SET job_id = $1 WHERE migration_id = $2',
        [jobId, migrationId]
      );

      return migrationId;
    } catch (error) {
      console.error('Error starting file migration:', error);
      throw error;
    }
  }

  /**
   * Migrate multiple files
   */
  static async migrateBulkFiles(
    fileIds: string[],
    targetProvider: string = 'azure',
    batchSize: number = 10,
    priority: number = 0
  ): Promise<string[]> {
    try {
      const migrationIds: string[] = [];

      // Process files in batches
      for (let i = 0; i < fileIds.length; i += batchSize) {
        const batch = fileIds.slice(i, i + batchSize);
        
        // Get source providers for batch
        const files = await pool.query(
          'SELECT file_id, storage_provider FROM files WHERE file_id = ANY($1)',
          [batch]
        );

        // Create migration records for batch
        const migrationPromises = files.rows.map(async (file) => {
          const migrationResult = await pool.query(
            'INSERT INTO storage_migrations (file_id, source_provider, target_provider, status) VALUES ($1, $2, $3, $4) RETURNING migration_id',
            [file.file_id, file.storage_provider, targetProvider, 'pending']
          );
          return migrationResult.rows[0].migration_id;
        });

        const batchMigrationIds = await Promise.all(migrationPromises);
        migrationIds.push(...batchMigrationIds);

        // Add batch to BullMQ queue
        const jobId = await this.processorQueueManager.addProcessorJob(
          'storage_migration_bulk',
          {
            fileIds: batch,
            targetProvider,
            batchSize: Math.min(batchSize, batch.length),
            priority
          },
          {
            priority,
            delay: 0
          }
        );

        // Update migration records with job ID
        await pool.query(
          'UPDATE storage_migrations SET job_id = $1 WHERE migration_id = ANY($2)',
          [jobId, batchMigrationIds]
        );
      }

      return migrationIds;
    } catch (error) {
      console.error('Error starting bulk migration:', error);
      throw error;
    }
  }

  /**
   * Get migration status
   */
  static async getMigrationStatus(migrationId: string): Promise<MigrationStatus | null> {
    try {
      const result = await pool.query(
        'SELECT * FROM storage_migrations WHERE migration_id = $1',
        [migrationId]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        migrationId: row.migration_id,
        fileId: row.file_id,
        sourceProvider: row.source_provider,
        targetProvider: row.target_provider,
        status: row.status,
        jobId: row.job_id,
        errorMessage: row.error_message,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    } catch (error) {
      console.error('Error getting migration status:', error);
      return null;
    }
  }

  /**
   * Get migration status for a file
   */
  static async getFileMigrationStatus(fileId: string): Promise<MigrationStatus | null> {
    try {
      const result = await pool.query(
        'SELECT * FROM storage_migrations WHERE file_id = $1 ORDER BY created_at DESC LIMIT 1',
        [fileId]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        migrationId: row.migration_id,
        fileId: row.file_id,
        sourceProvider: row.source_provider,
        targetProvider: row.target_provider,
        status: row.status,
        jobId: row.job_id,
        errorMessage: row.error_message,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    } catch (error) {
      console.error('Error getting file migration status:', error);
      return null;
    }
  }

  /**
   * Get migration statistics
   */
  static async getMigrationStats(): Promise<any> {
    try {
      const result = await pool.query(`
        SELECT 
          source_provider,
          target_provider,
          status,
          COUNT(*) as count,
          MIN(created_at) as first_created,
          MAX(created_at) as last_created
        FROM storage_migrations
        GROUP BY source_provider, target_provider, status
        ORDER BY source_provider, target_provider, status
      `);

      return result.rows;
    } catch (error) {
      console.error('Error getting migration stats:', error);
      return [];
    }
  }

  /**
   * Cancel a migration
   */
  static async cancelMigration(migrationId: string): Promise<boolean> {
    try {
      const result = await pool.query(
        'UPDATE storage_migrations SET status = $1, updated_at = NOW() WHERE migration_id = $2 AND status IN ($3, $4)',
        ['cancelled', migrationId, 'pending', 'processing']
      );

      return result.rowCount > 0;
    } catch (error) {
      console.error('Error cancelling migration:', error);
      return false;
    }
  }

  /**
   * Get pending migrations
   */
  static async getPendingMigrations(limit: number = 100): Promise<MigrationStatus[]> {
    try {
      const result = await pool.query(
        'SELECT * FROM storage_migrations WHERE status = $1 ORDER BY created_at ASC LIMIT $2',
        ['pending', limit]
      );

      return result.rows.map(row => ({
        migrationId: row.migration_id,
        fileId: row.file_id,
        sourceProvider: row.source_provider,
        targetProvider: row.target_provider,
        status: row.status,
        jobId: row.job_id,
        errorMessage: row.error_message,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } catch (error) {
      console.error('Error getting pending migrations:', error);
      return [];
    }
  }

  /**
   * Retry failed migration
   */
  static async retryMigration(migrationId: string): Promise<string | null> {
    try {
      const migration = await this.getMigrationStatus(migrationId);
      if (!migration || migration.status !== 'failed') {
        throw new Error('Migration not found or not in failed state');
      }

      // Reset migration status
      await pool.query(
        'UPDATE storage_migrations SET status = $1, error_message = NULL, updated_at = NOW() WHERE migration_id = $2',
        ['pending', migrationId]
      );

      // Retry migration
      return await this.migrateFile(migration.fileId, migration.targetProvider);
    } catch (error) {
      console.error('Error retrying migration:', error);
      return null;
    }
  }
}
