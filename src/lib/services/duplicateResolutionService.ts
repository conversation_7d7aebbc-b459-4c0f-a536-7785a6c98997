import { pool } from '../db'

interface DuplicateResolution {
  primaryId: number
  duplicateIds: number[]
  entityType: 'contact' | 'company'
  resolutionMethod: 'merge' | 'delete' | 'mark_duplicate'
  mergeStrategy?: 'keep_primary' | 'combine_data' | 'keep_most_recent'
}

// Add missing interfaces that are being imported
export interface MergePreview {
  primaryRecord: Record<string, any>
  duplicateRecord: Record<string, any>
  conflictingFields: string[]
  mergedRecord: Record<string, any>
}

export interface MergeOptions {
  primaryRecordId: number
  duplicateRecordId: number
  recordType: 'company' | 'contact'
  fieldSelections: Record<string, 'primary' | 'duplicate' | 'custom'>
  customValues: Record<string, any>
  mergeRelatedRecords: boolean
}

interface TableMapping {
  tableName: string
  idColumn: string
  foreignKeyColumn?: string
  entityTypeColumn?: string
  priority: number // Higher priority = more important data to preserve
}

export class DuplicateResolutionService {
  // Define all tables that need to be shifted during duplicate resolution
  private static readonly CONTACT_TABLES: TableMapping[] = [
    { tableName: 'contacts', idColumn: 'contact_id', priority: 100 },
    { tableName: 'contact_enrichment', idColumn: 'id', foreignKeyColumn: 'contact_id', priority: 90 },
    { tableName: 'contact_normalized_data', idColumn: 'id', foreignKeyColumn: 'contact_id', priority: 80 },
    { tableName: 'deal_contacts', idColumn: 'id', foreignKeyColumn: 'contact_id', priority: 40 },
    { tableName: 'person_investment_criteria', idColumn: 'id', foreignKeyColumn: 'contact_id', priority: 30 },
    { tableName: 'person_news_mentions', idColumn: 'id', foreignKeyColumn: 'contact_id', priority: 20 }
  ]

  private static readonly COMPANY_TABLES: TableMapping[] = [
    { tableName: 'companies', idColumn: 'company_id', priority: 100 },
    { tableName: 'company_normalized_data', idColumn: 'id', foreignKeyColumn: 'company_id', priority: 90 },
    { tableName: 'company_web_pages', idColumn: 'id', foreignKeyColumn: 'company_id', priority: 60 },
    { tableName: 'company_web_chunks', idColumn: 'id', foreignKeyColumn: 'company_id', priority: 50 },
    { tableName: 'deal_news_companies', idColumn: 'id', foreignKeyColumn: 'company_id', priority: 10 }
  ]

  private static readonly INVESTMENT_CRITERIA_TABLES: TableMapping[] = [
    { tableName: 'investment_criteria_central', idColumn: 'investment_criteria_id', entityTypeColumn: 'entity_type', priority: 100 },
    { tableName: 'investment_criteria_debt', idColumn: 'investment_criteria_debt_id', foreignKeyColumn: 'investment_criteria_id', priority: 90 },
    { tableName: 'investment_criteria_equity', idColumn: 'investment_criteria_equity_id', foreignKeyColumn: 'investment_criteria_id', priority: 90 }
  ]

  private static readonly DEAL_TABLES: TableMapping[] = [
    { tableName: 'deals', idColumn: 'deal_id', priority: 100 },
    { tableName: 'dealsv2', idColumn: 'deal_id', priority: 90 },
    { tableName: 'deal_extractions', idColumn: 'id', foreignKeyColumn: 'deal_id', priority: 80 },
    { tableName: 'deal_nsf_fields', idColumn: 'id', foreignKeyColumn: 'deal_id', priority: 70 },
    { tableName: 'deal_news_deals', idColumn: 'id', foreignKeyColumn: 'deal_id', priority: 60 }
  ]

  private static readonly NEWS_ARTICLE_TABLES: TableMapping[] = [
    { tableName: 'news', idColumn: 'id', priority: 100 },
    { tableName: 'deal_news', idColumn: 'id', priority: 90 },
    { tableName: 'news_enrichment', idColumn: 'id', foreignKeyColumn: 'news_id', priority: 80 },
    { tableName: 'article', idColumn: 'article_id', priority: 100 },
    { tableName: 'article_properties', idColumn: 'article_property_id', foreignKeyColumn: 'article_id', priority: 90 },
    { tableName: 'article_market_metrics', idColumn: 'article_market_metric_id', foreignKeyColumn: 'article_id', priority: 90 },
    { tableName: 'article_transactions', idColumn: 'article_transaction_id', foreignKeyColumn: 'article_id', priority: 90 },
    { tableName: 'articles_entities', idColumn: 'article_entity_id', foreignKeyColumn: 'article_id', priority: 80 }
  ]

  /**
   * Resolve duplicates by merging or deleting duplicate records
   */
  static async resolveDuplicates(resolution: DuplicateResolution): Promise<{ success: boolean; error?: string; recordsProcessed: number }> {
    const client = await pool.connect()
    let recordsProcessed = 0

    try {
      await client.query('BEGIN')

      console.log(`Starting duplicate resolution for ${resolution.entityType} ${resolution.primaryId} with ${resolution.duplicateIds.length} duplicates`)

      if (resolution.resolutionMethod === 'merge') {
        recordsProcessed = await this.mergeDuplicates(client, resolution)
      } else if (resolution.resolutionMethod === 'delete') {
        recordsProcessed = await this.deleteDuplicates(client, resolution)
      } else if (resolution.resolutionMethod === 'mark_duplicate') {
        recordsProcessed = await this.markDuplicates(client, resolution)
      }

      await client.query('COMMIT')
      console.log(`Successfully resolved duplicates. Records processed: ${recordsProcessed}`)

      return { success: true, recordsProcessed }
    } catch (error) {
      await client.query('ROLLBACK')
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error(`Error resolving duplicates: ${errorMessage}`)
      return { success: false, error: errorMessage, recordsProcessed }
    } finally {
      client.release()
    }
  }

  /**
   * Merge duplicate records into the primary record
   */
  private static async mergeDuplicates(client: any, resolution: DuplicateResolution): Promise<number> {
    let totalProcessed = 0

    if (resolution.entityType === 'contact') {
      totalProcessed += await this.mergeContactDuplicates(client, resolution)
    } else if (resolution.entityType === 'company') {
      totalProcessed += await this.mergeCompanyDuplicates(client, resolution)
    }

    return totalProcessed
  }

  /**
   * Merge contact duplicates
   */
  private static async mergeContactDuplicates(client: any, resolution: DuplicateResolution): Promise<number> {
    let processed = 0

    // Process tables in priority order
    for (const tableMapping of this.CONTACT_TABLES.sort((a, b) => b.priority - a.priority)) {
      try {
        const result = await this.mergeTableRecords(
          client,
          tableMapping,
          resolution.primaryId,
          resolution.duplicateIds,
          'contact'
        )
        processed += result
        console.log(`Merged ${result} records from ${tableMapping.tableName}`)
      } catch (error) {
        console.error(`Error merging ${tableMapping.tableName}: ${error}`)
      }
    }

    // Handle investment criteria separately
    for (const tableMapping of this.INVESTMENT_CRITERIA_TABLES) {
      try {
        const result = await this.mergeInvestmentCriteriaRecords(
          client,
          tableMapping,
          resolution.primaryId,
          resolution.duplicateIds,
          'contact'
        )
        processed += result
        console.log(`Merged ${result} investment criteria records from ${tableMapping.tableName}`)
      } catch (error) {
        console.error(`Error merging investment criteria ${tableMapping.tableName}: ${error}`)
      }
    }

    return processed
  }

  /**
   * Merge company duplicates
   */
  private static async mergeCompanyDuplicates(client: any, resolution: DuplicateResolution): Promise<number> {
    let processed = 0

    // Process tables in priority order
    for (const tableMapping of this.COMPANY_TABLES.sort((a, b) => b.priority - a.priority)) {
      try {
        const result = await this.mergeTableRecords(
          client,
          tableMapping,
          resolution.primaryId,
          resolution.duplicateIds,
          'company'
        )
        processed += result
        console.log(`Merged ${result} records from ${tableMapping.tableName}`)
      } catch (error) {
        console.error(`Error merging ${tableMapping.tableName}: ${error}`)
      }
    }

    // Handle investment criteria separately
    for (const tableMapping of this.INVESTMENT_CRITERIA_TABLES) {
      try {
        const result = await this.mergeInvestmentCriteriaRecords(
          client,
          tableMapping,
          resolution.primaryId,
          resolution.duplicateIds,
          'company'
        )
        processed += result
        console.log(`Merged ${result} investment criteria records from ${tableMapping.tableName}`)
      } catch (error) {
        console.error(`Error merging investment criteria ${tableMapping.tableName}: ${error}`)
      }
    }

    return processed
  }

  /**
   * Merge records in a specific table
   */
  private static async mergeTableRecords(
    client: any,
    tableMapping: TableMapping,
    primaryId: number,
    duplicateIds: number[],
    entityType: 'contact' | 'company'
  ): Promise<number> {
    const foreignKeyColumn = tableMapping.foreignKeyColumn || tableMapping.idColumn
    const idColumn = tableMapping.idColumn

    // Check if table exists and has records
    const tableExists = await this.tableExists(client, tableMapping.tableName)
    if (!tableExists) {
      console.log(`Table ${tableMapping.tableName} does not exist, skipping`)
      return 0
    }

    // Get all records for the primary and duplicate IDs
    const allIds = [primaryId, ...duplicateIds]
    const records = await client.query(`
      SELECT * FROM ${tableMapping.tableName} 
      WHERE ${foreignKeyColumn} = ANY($1)
      ORDER BY ${foreignKeyColumn}, updated_at DESC
    `, [allIds])

    if (records.rows.length === 0) {
      return 0
    }

    // Group records by the foreign key
    const recordsByEntity: { [key: number]: any[] } = {}
    records.rows.forEach((row: any) => {
      const entityId = row[foreignKeyColumn]
      if (!recordsByEntity[entityId]) {
        recordsByEntity[entityId] = []
      }
      recordsByEntity[entityId].push(row)
    })

    let processed = 0

    // For each duplicate entity, merge its records into the primary entity
    for (const duplicateId of duplicateIds) {
      const duplicateRecords = recordsByEntity[duplicateId] || []
      const primaryRecords = recordsByEntity[primaryId] || []

      for (const duplicateRecord of duplicateRecords) {
        // Check if we should merge or skip based on merge strategy
        const shouldMerge = this.shouldMergeRecord(duplicateRecord, primaryRecords, tableMapping)
        
        if (shouldMerge) {
          // Update the foreign key to point to the primary entity
          await client.query(`
            UPDATE ${tableMapping.tableName} 
            SET ${foreignKeyColumn} = $1, updated_at = CURRENT_TIMESTAMP
            WHERE ${idColumn} = $2
          `, [primaryId, duplicateRecord[idColumn]])
          
          processed++
        } else {
          // Delete the duplicate record if it shouldn't be merged
          await client.query(`
            DELETE FROM ${tableMapping.tableName} 
            WHERE ${idColumn} = $1
          `, [duplicateRecord[idColumn]])
          
          processed++
        }
      }
    }

    return processed
  }

  /**
   * Merge investment criteria records
   */
  private static async mergeInvestmentCriteriaRecords(
    client: any,
    tableMapping: TableMapping,
    primaryId: number,
    duplicateIds: number[],
    entityType: 'contact' | 'company'
  ): Promise<number> {
    const entityTypeColumn = tableMapping.entityTypeColumn || 'entity_type'
    const idColumn = tableMapping.idColumn

    // Check if table exists
    const tableExists = await this.tableExists(client, tableMapping.tableName)
    if (!tableExists) {
      console.log(`Table ${tableMapping.tableName} does not exist, skipping`)
      return 0
    }

    let processed = 0

    // For each duplicate entity, update its investment criteria to point to the primary entity
    for (const duplicateId of duplicateIds) {
      const result = await client.query(`
        UPDATE ${tableMapping.tableName} 
        SET entity_id = $1, updated_at = CURRENT_TIMESTAMP
        WHERE entity_id = $2 AND ${entityTypeColumn} = $3
      `, [primaryId, duplicateId, entityType])
      
      processed += result.rowCount || 0
    }

    return processed
  }

  /**
   * Delete duplicate records
   */
  private static async deleteDuplicates(client: any, resolution: DuplicateResolution): Promise<number> {
    let totalProcessed = 0

    if (resolution.entityType === 'contact') {
      totalProcessed += await this.deleteContactDuplicates(client, resolution)
    } else if (resolution.entityType === 'company') {
      totalProcessed += await this.deleteCompanyDuplicates(client, resolution)
    }

    return totalProcessed
  }

  /**
   * Delete contact duplicates
   */
  private static async deleteContactDuplicates(client: any, resolution: DuplicateResolution): Promise<number> {
    let processed = 0

    // Process tables in reverse priority order (delete from least important first)
    for (const tableMapping of this.CONTACT_TABLES.sort((a, b) => a.priority - b.priority)) {
      try {
        const result = await this.deleteTableRecords(
          client,
          tableMapping,
          resolution.duplicateIds,
          'contact'
        )
        processed += result
        console.log(`Deleted ${result} records from ${tableMapping.tableName}`)
      } catch (error) {
        console.error(`Error deleting from ${tableMapping.tableName}: ${error}`)
      }
    }

    // Handle investment criteria
    for (const tableMapping of this.INVESTMENT_CRITERIA_TABLES) {
      try {
        const result = await this.deleteInvestmentCriteriaRecords(
          client,
          tableMapping,
          resolution.duplicateIds,
          'contact'
        )
        processed += result
        console.log(`Deleted ${result} investment criteria records from ${tableMapping.tableName}`)
      } catch (error) {
        console.error(`Error deleting investment criteria from ${tableMapping.tableName}: ${error}`)
      }
    }

    return processed
  }

  /**
   * Delete company duplicates
   */
  private static async deleteCompanyDuplicates(client: any, resolution: DuplicateResolution): Promise<number> {
    let processed = 0

    // Process tables in reverse priority order
    for (const tableMapping of this.COMPANY_TABLES.sort((a, b) => a.priority - b.priority)) {
      try {
        const result = await this.deleteTableRecords(
          client,
          tableMapping,
          resolution.duplicateIds,
          'company'
        )
        processed += result
        console.log(`Deleted ${result} records from ${tableMapping.tableName}`)
      } catch (error) {
        console.error(`Error deleting from ${tableMapping.tableName}: ${error}`)
      }
    }

    // Handle investment criteria
    for (const tableMapping of this.INVESTMENT_CRITERIA_TABLES) {
      try {
        const result = await this.deleteInvestmentCriteriaRecords(
          client,
          tableMapping,
          resolution.duplicateIds,
          'company'
        )
        processed += result
        console.log(`Deleted ${result} investment criteria records from ${tableMapping.tableName}`)
      } catch (error) {
        console.error(`Error deleting investment criteria from ${tableMapping.tableName}: ${error}`)
      }
    }

    return processed
  }

  /**
   * Delete records from a specific table
   */
  private static async deleteTableRecords(
    client: any,
    tableMapping: TableMapping,
    duplicateIds: number[],
    entityType: 'contact' | 'company'
  ): Promise<number> {
    const foreignKeyColumn = tableMapping.foreignKeyColumn || tableMapping.idColumn

    // Check if table exists
    const tableExists = await this.tableExists(client, tableMapping.tableName)
    if (!tableExists) {
      return 0
    }

    const result = await client.query(`
      DELETE FROM ${tableMapping.tableName} 
      WHERE ${foreignKeyColumn} = ANY($1)
    `, [duplicateIds])

    return result.rowCount || 0
  }

  /**
   * Delete investment criteria records
   */
  private static async deleteInvestmentCriteriaRecords(
    client: any,
    tableMapping: TableMapping,
    duplicateIds: number[],
    entityType: 'contact' | 'company'
  ): Promise<number> {
    const entityTypeColumn = tableMapping.entityTypeColumn || 'entity_type'

    // Check if table exists
    const tableExists = await this.tableExists(client, tableMapping.tableName)
    if (!tableExists) {
      return 0
    }

    const result = await client.query(`
      DELETE FROM ${tableMapping.tableName} 
      WHERE entity_id = ANY($1) AND ${entityTypeColumn} = $2
    `, [duplicateIds, entityType])

    return result.rowCount || 0
  }

  /**
   * Mark duplicates (soft delete)
   */
  private static async markDuplicates(client: any, resolution: DuplicateResolution): Promise<number> {
    let processed = 0

    if (resolution.entityType === 'contact') {
      // Mark contacts as duplicates
      const result = await client.query(`
        UPDATE contacts 
        SET conflict_status = 'duplicate', 
            conflict_created_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE contact_id = ANY($1)
      `, [resolution.duplicateIds])
      
      processed += result.rowCount || 0
    } else if (resolution.entityType === 'company') {
      // Mark companies as duplicates
      const result = await client.query(`
        UPDATE companies 
        SET conflict_status = 'duplicate', 
            conflict_created_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE company_id = ANY($1)
      `, [resolution.duplicateIds])
      
      processed += result.rowCount || 0
    }

    return processed
  }

  /**
   * Determine if a record should be merged based on merge strategy
   */
  private static shouldMergeRecord(duplicateRecord: any, primaryRecords: any[], tableMapping: TableMapping): boolean {
    // For now, always merge. In the future, implement more sophisticated merge strategies
    return true
  }

  /**
   * Check if a table exists
   */
  private static async tableExists(client: any, tableName: string): Promise<boolean> {
    try {
      const result = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [tableName])
      
      return result.rows[0].exists
    } catch (error) {
      console.error(`Error checking if table ${tableName} exists: ${error}`)
      return false
    }
  }

  /**
   * Get all tables that reference a specific entity
   */
  static async getEntityReferences(entityId: number, entityType: 'contact' | 'company'): Promise<{ tableName: string; recordCount: number }[]> {
    const client = await pool.connect()
    const references: { tableName: string; recordCount: number }[] = []

    try {
      const tables = entityType === 'contact' ? this.CONTACT_TABLES : this.COMPANY_TABLES

      for (const tableMapping of tables) {
        const tableExists = await this.tableExists(client, tableMapping.tableName)
        if (!tableExists) continue

        const foreignKeyColumn = tableMapping.foreignKeyColumn || tableMapping.idColumn
        
        const result = await client.query(`
          SELECT COUNT(*) as count 
          FROM ${tableMapping.tableName} 
          WHERE ${foreignKeyColumn} = $1
        `, [entityId])

        const count = parseInt(result.rows[0].count)
        if (count > 0) {
          references.push({ tableName: tableMapping.tableName, recordCount: count })
        }
      }

      // Check investment criteria tables
      for (const tableMapping of this.INVESTMENT_CRITERIA_TABLES) {
        const tableExists = await this.tableExists(client, tableMapping.tableName)
        if (!tableExists) continue

        const result = await client.query(`
          SELECT COUNT(*) as count 
          FROM ${tableMapping.tableName} 
          WHERE entity_id = $1 AND entity_type = $2
        `, [entityId, entityType])

        const count = parseInt(result.rows[0].count)
        if (count > 0) {
          references.push({ tableName: tableMapping.tableName, recordCount: count })
        }
      }

    } finally {
      client.release()
    }

    return references
  }

  /**
   * Generate a preview of what the merged record would look like
   * Optimized to only fetch fields that have data and are different
   */
  static async generateMergePreview(
    primaryId: number,
    duplicateId: number,
    recordType: 'company' | 'contact'
  ): Promise<MergePreview> {
    const client = await pool.connect()
    
    try {
      let primaryRecord: any
      let duplicateRecord: any
      
      if (recordType === 'company') {
        // Optimized query - only fetch essential fields first, then fetch specific fields that differ
        const primaryResult = await client.query(`
          SELECT 
            c.company_id,
            c.company_name,
            c.company_website,
            c.industry,
            c.company_phone,
            c.main_phone,
            c.main_email,
            c.secondary_email,
            c.company_address,
            c.company_city,
            c.company_state,
            c.company_country,
            c.company_zip,
            c.summary,
            c.founded_year,
            c.number_of_employees,
            c.annual_revenue,
            c.fund_size,
            c.aum,
            c.company_linkedin,
            c.twitter,
            c.facebook,
            c.instagram,
            c.youtube,
            c.business_model,
            c.investment_focus,
            c.investment_strategy_mission,
            c.investment_strategy_approach,
            c.fund_size_active_fund,
            c.active_fund_name_series,
            c.fundraising_status,
            c.dry_powder,
            c.annual_deployment_target,
            c.transactions_completed_last_12m,
            c.total_transaction_volume_ytd,
            c.deal_count_ytd,
            c.average_deal_size,
            c.portfolio_size_sqft,
            c.portfolio_asset_count,
            c.number_of_properties,
            c.number_of_offices,
            c.office_locations,
            c.partnerships,
            c.key_equity_partners,
            c.key_debt_partners,
            c.major_competitors,
            c.board_of_directors,
            c.key_executives,
            c.founder_background,
            c.company_history,
            c.products_services_description,
            c.target_customer_profile,
            c.unique_selling_proposition,
            c.market_share_percentage,
            c.industry_awards_recognitions,
            c.corporate_structure,
            c.parent_company,
            c.subsidiaries,
            c.stock_ticker_symbol,
            c.stock_exchange,
            c.market_capitalization,
            c.net_income,
            c.ebitda,
            c.profit_margin,
            c.credit_rating,
            c.quarterly_earnings_link,
            c.headquarters_address,
            c.headquarters_city,
            c.headquarters_state,
            c.headquarters_zipcode,
            c.headquarters_country,
            c.additional_address,
            c.additional_city,
            c.additional_state,
            c.additional_zipcode,
            c.additional_country,
            c.sustainability_esg_focus,
            c.technology_proptech_adoption,
            c.adaptive_reuse_experience,
            c.regulatory_zoning_expertise,
            c.market_cycle_positioning,
            c.urban_vs_suburban_preference,
            c.lender_type,
            c.annual_loan_volume,
            c.lending_origin,
            c.portfolio_health,
            c.recent_news_sentiment,
            c.data_source,
            c.internal_relationship_manager,
            c.last_contact_date,
            c.pipeline_status,
            c.role_in_previous_deal
          FROM companies c
          WHERE c.company_id = $1
        `, [primaryId])
        
        const duplicateResult = await client.query(`
          SELECT 
            c.company_id,
            c.company_name,
            c.company_website,
            c.industry,
            c.company_phone,
            c.main_phone,
            c.main_email,
            c.secondary_email,
            c.company_address,
            c.company_city,
            c.company_state,
            c.company_country,
            c.company_zip,
            c.summary,
            c.founded_year,
            c.number_of_employees,
            c.annual_revenue,
            c.fund_size,
            c.aum,
            c.company_linkedin,
            c.twitter,
            c.facebook,
            c.instagram,
            c.youtube,
            c.business_model,
            c.investment_focus,
            c.investment_strategy_mission,
            c.investment_strategy_approach,
            c.fund_size_active_fund,
            c.active_fund_name_series,
            c.fundraising_status,
            c.dry_powder,
            c.annual_deployment_target,
            c.transactions_completed_last_12m,
            c.total_transaction_volume_ytd,
            c.deal_count_ytd,
            c.average_deal_size,
            c.portfolio_size_sqft,
            c.portfolio_asset_count,
            c.number_of_properties,
            c.number_of_offices,
            c.office_locations,
            c.partnerships,
            c.key_equity_partners,
            c.key_debt_partners,
            c.major_competitors,
            c.board_of_directors,
            c.key_executives,
            c.founder_background,
            c.company_history,
            c.products_services_description,
            c.target_customer_profile,
            c.unique_selling_proposition,
            c.market_share_percentage,
            c.industry_awards_recognitions,
            c.corporate_structure,
            c.parent_company,
            c.subsidiaries,
            c.stock_ticker_symbol,
            c.stock_exchange,
            c.market_capitalization,
            c.net_income,
            c.ebitda,
            c.profit_margin,
            c.credit_rating,
            c.quarterly_earnings_link,
            c.headquarters_address,
            c.headquarters_city,
            c.headquarters_state,
            c.headquarters_zipcode,
            c.headquarters_country,
            c.additional_address,
            c.additional_city,
            c.additional_state,
            c.additional_zipcode,
            c.additional_country,
            c.sustainability_esg_focus,
            c.technology_proptech_adoption,
            c.adaptive_reuse_experience,
            c.regulatory_zoning_expertise,
            c.market_cycle_positioning,
            c.urban_vs_suburban_preference,
            c.lender_type,
            c.annual_loan_volume,
            c.lending_origin,
            c.portfolio_health,
            c.recent_news_sentiment,
            c.data_source,
            c.internal_relationship_manager,
            c.last_contact_date,
            c.pipeline_status,
            c.role_in_previous_deal
          FROM companies c
          WHERE c.company_id = $1
        `, [duplicateId])
        
        primaryRecord = primaryResult.rows[0] || {}
        duplicateRecord = duplicateResult.rows[0] || {}
      } else {
        // Optimized contact query - only fetch essential fields that actually exist
        const primaryResult = await client.query(`
          SELECT 
            c.contact_id,
            c.full_name,
            c.first_name,
            c.last_name,
            c.email,
            c.personal_email,
            c.additional_email,
            c.phone_number,
            c.contact_phone,
            c.phone_number_secondary,
            c.linkedin_url,
            c.title,
            c.headline,
            c.seniority,
            c.company_id,
            c.company_name,
            c.notes,
            c.source,
            c.contact_city,
            c.contact_state,
            c.contact_country,
            c.contact_address,
            c.contact_zip_code,
            c.contact_category,
            c.capital_type,
            c.region,
            c.contact_type,
            c.relationship_owner,
            c.role_in_decision_making,
            c.last_contact_date,
            c.source_of_introduction,
            c.accredited_investor_status,
            c.kyc_status,
            c.twitter,
            c.facebook,
            c.instagram,
            c.youtube,
            c.executive_summary,
            c.career_timeline,
            c.education_college,
            c.education_college_year_graduated,
            c.education_high_school,
            c.education_high_school_year_graduated,
            c.honorable_achievements,
            c.hobbies,
            c.age,
            c.capital_position,
            c.job_tier,
            c.classification_confidence,
            c.classification_reasoning,
            c.extra_attrs,
            c.created_at,
            c.updated_at
          FROM contacts c
          WHERE c.contact_id = $1
        `, [primaryId])
        
        const duplicateResult = await client.query(`
          SELECT 
            c.contact_id,
            c.full_name,
            c.first_name,
            c.last_name,
            c.email,
            c.personal_email,
            c.additional_email,
            c.phone_number,
            c.contact_phone,
            c.phone_number_secondary,
            c.linkedin_url,
            c.title,
            c.headline,
            c.seniority,
            c.company_id,
            c.company_name,
            c.notes,
            c.source,
            c.contact_city,
            c.contact_state,
            c.contact_country,
            c.contact_address,
            c.contact_zip_code,
            c.contact_category,
            c.capital_type,
            c.region,
            c.contact_type,
            c.relationship_owner,
            c.role_in_decision_making,
            c.last_contact_date,
            c.source_of_introduction,
            c.accredited_investor_status,
            c.kyc_status,
            c.twitter,
            c.facebook,
            c.instagram,
            c.youtube,
            c.executive_summary,
            c.career_timeline,
            c.education_college,
            c.education_college_year_graduated,
            c.education_high_school,
            c.education_high_school_year_graduated,
            c.honorable_achievements,
            c.hobbies,
            c.age,
            c.capital_position,
            c.job_tier,
            c.classification_confidence,
            c.classification_reasoning,
            c.extra_attrs,
            c.created_at,
            c.updated_at
          FROM contacts c
          WHERE c.contact_id = $1
        `, [duplicateId])
        
        primaryRecord = primaryResult.rows[0] || {}
        duplicateRecord = duplicateResult.rows[0] || {}
      }
      
      // Identify conflicting fields - only process fields that actually have data
      const conflictingFields: string[] = []
      const mergedRecord: Record<string, any> = { ...primaryRecord }
      
      // Check for conflicts in common fields - only process fields with actual data
      const commonFields = Object.keys(primaryRecord).filter(key => 
        key in duplicateRecord && 
        !key.includes('_id') && 
        !key.includes('created_at') && 
        !key.includes('updated_at') &&
        !key.includes('normalized_') && // Skip normalized fields as they're internal
        (primaryRecord[key] !== null && primaryRecord[key] !== undefined && primaryRecord[key] !== '') ||
        (duplicateRecord[key] !== null && duplicateRecord[key] !== undefined && duplicateRecord[key] !== '')
      )
      
      for (const field of commonFields) {
        const primaryValue = primaryRecord[field]
        const duplicateValue = duplicateRecord[field]
        
        // Consider it conflicting if both have values but they're different
        if (primaryValue && duplicateValue && primaryValue !== duplicateValue) {
          conflictingFields.push(field)
        }
        
        // For non-conflicting fields, prefer primary value
        if (!primaryValue && duplicateValue) {
          mergedRecord[field] = duplicateValue
        }
      }
      
      return {
        primaryRecord,
        duplicateRecord,
        conflictingFields,
        mergedRecord
      }
      
    } finally {
      client.release()
    }
  }
}
