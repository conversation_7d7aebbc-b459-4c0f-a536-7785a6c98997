import Bottleneck from 'bottleneck'

// Create a bottleneck instance to limit concurrent API calls
// Reduce concurrency to prevent database deadlocks and add retry logic
const duplicateBottleneck = new Bottleneck({
  maxConcurrent: 2, // Reduced from 5 to 2 to minimize database conflicts
  minTime: 200,      // Increased from 100ms to 200ms for better spacing
  reservoir: 10,     // Allow bursts of up to 10 requests
  reservoirRefreshAmount: 10,
  reservoirRefreshInterval: 60 * 1000, // Refill every minute
  // Add retry logic for database deadlocks
  retryCount: 3,
  retryDelay: function(retryCount, error) {
    // Exponential backoff for retries: 1s, 2s, 4s
    return Math.pow(2, retryCount) * 1000
  },
  // Only retry on specific database errors
  retryFilter: function(error) {
    // Retry on deadlocks, foreign key violations, and connection errors
    return error.message && (
      error.message.includes('deadlock detected') ||
      error.message.includes('foreign key constraint') ||
      error.message.includes('connection') ||
      error.code === '40P01' || // Deadlock
      error.code === '23503'    // Foreign key violation
    )
  }
})

export interface MergeOptions {
  duplicateId: number
  primaryRecordId: number | string
  autoMerge?: boolean
  resolvedBy?: string
}

export interface MergeResult {
  success: boolean
  duplicateId: number
  error?: string
}

// Common merge function that can be throttled by bottleneck
const performMerge = async (options: MergeOptions): Promise<MergeResult> => {
  try {
    console.log(`🔄 Attempting merge for duplicate ${options.duplicateId}`)
    
    const response = await fetch('/api/duplicates/resolve', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        duplicateId: options.duplicateId,
        action: 'merge',
        mergeStrategy: {
          primaryRecordId: options.primaryRecordId,
          fieldsToMerge: [],
          customValues: {},
          autoMerge: options.autoMerge || true
        },
        resolvedBy: options.resolvedBy || 'automated_system'
      })
    })

    // Handle 404 errors (duplicate already resolved)
    if (response.status === 404) {
      console.log(`⚠️  Duplicate ${options.duplicateId} already resolved (404)`)
      return {
        success: true, // Treat as success since duplicate is already resolved
        duplicateId: options.duplicateId,
        error: 'Duplicate already resolved'
      }
    }

    // Handle 500 errors (server errors like deadlocks)
    if (response.status === 500) {
      const errorText = await response.text()
      console.log(`💥 Server error for duplicate ${options.duplicateId}: ${errorText}`)
      throw new Error(`Server error: ${errorText}`)
    }

    const data = await response.json()
    
    if (data.success) {
      console.log(`✅ Successfully merged duplicate ${options.duplicateId}`)
    } else {
      console.log(`❌ Failed to merge duplicate ${options.duplicateId}: ${data.error}`)
    }
    
    return {
      success: data.success,
      duplicateId: options.duplicateId,
      error: data.success ? undefined : data.error
    }
  } catch (error) {
    console.log(`🚨 Exception during merge for duplicate ${options.duplicateId}: ${(error as Error).message}`)
    return {
      success: false,
      duplicateId: options.duplicateId,
      error: (error as Error).message
    }
  }
}

// Throttled merge function using bottleneck
export const throttledMerge = duplicateBottleneck.wrap(performMerge)

export interface BatchMergeOptions {
  duplicates: Array<{
    id: number
    primary_record_id: number | string
    record_type?: string
    match_type?: string
  }>
  resolvedBy?: string
  smartFilter?: boolean // Apply smart filtering based on match type
  markBlocked?: boolean // Mark filtered-out records as blocked for manual processing
  batchSize?: number // Size of sub-batches for processing (default: 10)
  adaptiveThrottling?: boolean // Enable adaptive throttling based on error rates
}

export interface BatchMergeResult {
  totalProcessed: number
  successCount: number
  errorCount: number
  skippedCount: number
  blockedCount: number
  retriedCount: number
  processingTimeMs: number
  averageTimePerRecord: number
  errors: Array<{ 
    duplicateId: number; 
    error: string; 
    errorType: 'network' | 'database' | 'validation' | 'unknown';
    retryCount?: number;
  }>
  performanceMetrics: {
    totalDuration: number
    averageBatchDuration: number
    fastestBatch: number
    slowestBatch: number
    errorRate: number
  }
}

// Smart filtering logic - CONSERVATIVE approach for safety
// Only merge when we have STRONG, unambiguous identifiers
const canAutoMerge = (duplicate: { record_type?: string; match_type?: string }): boolean => {
  if (duplicate.record_type === 'company') {
    // For companies: ONLY merge on exact domain match
    // ❌ Company names are UNRELIABLE identifiers due to:
    //    - Legal name vs DBA variations
    //    - Abbreviations (Inc, LLC, Corp)
    //    - Spacing and punctuation differences
    //    - Subsidiary vs parent company names
    // ✅ Domain is the ONLY reliable company identifier
    return duplicate.match_type === 'exact_domain'
  } else if (duplicate.record_type === 'contact') {
    // For contacts: Only merge on exact email or LinkedIn match
    // ❌ Domain alone is insufficient (multiple people per domain)
    // ✅ Email or LinkedIn provides unique person identification
    return duplicate.match_type === 'exact_email' || duplicate.match_type === 'linkedin_match'
  }
  return false
}

// Enhanced function to mark duplicate as blocked with detailed reasoning
const markAsBlocked = async (duplicateId: number, reason: string, recordType: string, matchType: string): Promise<boolean> => {
  try {
    console.log(`🚫 Marking duplicate ${duplicateId} as blocked: ${reason}`)
    
    // Note: We can't store detailed notes in the current database schema
    // The reason is logged to console for debugging purposes
    console.log(`📝 Blocking details:
- Record Type: ${recordType}
- Match Type: ${matchType}
- Reason: ${reason}
- Decision: Does not meet auto-merge criteria
- Action Required: Manual review needed`)
    
    const response = await fetch('/api/duplicates/status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        duplicateId,
        status: 'blocked',
        resolvedBy: 'smart_filter_system'
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`Failed to mark duplicate ${duplicateId} as blocked: ${response.status} - ${errorText}`)
      return false
    }

    const data = await response.json()
    if (data.success) {
      console.log(`✅ Successfully marked duplicate ${duplicateId} as blocked`)
      return true
    } else {
      console.error(`Failed to mark duplicate ${duplicateId} as blocked: ${data.error}`)
      return false
    }
  } catch (error) {
    console.error(`Error marking duplicate ${duplicateId} as blocked:`, error)
    return false
  }
}



// Helper function to categorize errors for better handling
const categorizeError = (error: string): 'network' | 'database' | 'validation' | 'unknown' => {
  const errorLower = error.toLowerCase()
  
  if (errorLower.includes('network') || errorLower.includes('fetch') || errorLower.includes('connection')) {
    return 'network'
  }
  if (errorLower.includes('deadlock') || errorLower.includes('foreign key') || errorLower.includes('constraint')) {
    return 'database'
  }
  if (errorLower.includes('validation') || errorLower.includes('invalid') || errorLower.includes('required')) {
    return 'validation'
  }
  return 'unknown'
}

// Performance monitoring class
class BatchPerformanceTracker {
  private startTime: number
  private batchDurations: number[] = []
  private currentBatchStart: number = 0

  constructor() {
    this.startTime = Date.now()
  }

  startBatch() {
    this.currentBatchStart = Date.now()
  }

  endBatch() {
    if (this.currentBatchStart > 0) {
      const duration = Date.now() - this.currentBatchStart
      this.batchDurations.push(duration)
      this.currentBatchStart = 0
      return duration
    }
    return 0
  }

  getTotalDuration(): number {
    return Date.now() - this.startTime
  }

  getMetrics(errorCount: number, totalCount: number) {
    const totalDuration = this.getTotalDuration()
    const avgBatchDuration = this.batchDurations.length > 0 
      ? this.batchDurations.reduce((a, b) => a + b, 0) / this.batchDurations.length 
      : 0
    
    return {
      totalDuration,
      averageBatchDuration: avgBatchDuration,
      fastestBatch: this.batchDurations.length > 0 ? Math.min(...this.batchDurations) : 0,
      slowestBatch: this.batchDurations.length > 0 ? Math.max(...this.batchDurations) : 0,
      errorRate: totalCount > 0 ? errorCount / totalCount : 0
    }
  }
}

// Enhanced batch merge function using bottleneck for controlled concurrency
export const batchMergeWithBottleneck = async (
  options: BatchMergeOptions,
  onProgress?: (current: number, total: number, eta?: number, currentRate?: number) => void
): Promise<BatchMergeResult> => {
  const { 
    duplicates, 
    resolvedBy = 'batch_automated_system', 
    smartFilter = false, 
    markBlocked = false,
    batchSize = 10,
    adaptiveThrottling = true
  } = options
  
  const performanceTracker = new BatchPerformanceTracker()
  let processedCount = 0
  let successCount = 0
  let errorCount = 0
  let skippedCount = 0
  let blockedCount = 0
  let retriedCount = 0
  const errors: Array<{ 
    duplicateId: number; 
    error: string; 
    errorType: 'network' | 'database' | 'validation' | 'unknown';
    retryCount?: number;
  }> = []
  
  console.log(`🚀 Starting enhanced batch merge: ${duplicates.length} duplicates`)
  console.log(`⚙️  Configuration: smartFilter=${smartFilter}, markBlocked=${markBlocked}, batchSize=${batchSize}, adaptiveThrottling=${adaptiveThrottling}`)
  
  // Filter duplicates if smart filtering is enabled, optionally marking blocked ones
  const duplicatesToProcess: typeof duplicates = []
  const blockedDuplicates: Array<{ 
    id: number; 
    reason: string; 
    recordType: string; 
    matchType: string 
  }> = []
  
  if (smartFilter) {
    for (const duplicate of duplicates) {
      if (canAutoMerge(duplicate)) {
        duplicatesToProcess.push(duplicate)
      } else {
        const reason = `${duplicate.record_type}:${duplicate.match_type} doesn't meet auto-merge criteria`
        skippedCount++
        console.log(`⏭️  Skipping duplicate ${duplicate.id} - ${reason}`)
        
        if (markBlocked) {
          blockedDuplicates.push({ 
            id: duplicate.id, 
            reason,
            recordType: duplicate.record_type || 'unknown',
            matchType: duplicate.match_type || 'unknown'
          })
        }
      }
    }
  } else {
    duplicatesToProcess.push(...duplicates)
  }
  
  console.log(`📊 Processing ${duplicatesToProcess.length} duplicates (${skippedCount} skipped by smart filter)`)
  
  // Mark blocked duplicates if requested
  if (markBlocked && blockedDuplicates.length > 0) {
    console.log(`🚫 Marking ${blockedDuplicates.length} duplicates as blocked...`)
    const blockingPromises = blockedDuplicates.map(async ({ id, reason, recordType, matchType }) => {
      try {
        const success = await markAsBlocked(id, reason, recordType, matchType)
        if (success) {
          blockedCount++
        } else {
          console.error(`Failed to mark duplicate ${id} as blocked`)
        }
      } catch (error) {
        console.error(`Error marking duplicate ${id} as blocked:`, error)
      }
    })
    
    // Wait for all blocking operations to complete
    await Promise.allSettled(blockingPromises)
    console.log(`✅ Marked ${blockedCount} duplicates as blocked`)
  }
  
  // Process in batches for better control and progress tracking
  const totalBatches = Math.ceil(duplicatesToProcess.length / batchSize)
  let currentErrorRate = 0
  const startTime = Date.now()
  
  for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
    const batchStart = batchIndex * batchSize
    const batchEnd = Math.min(batchStart + batchSize, duplicatesToProcess.length)
    const currentBatch = duplicatesToProcess.slice(batchStart, batchEnd)
    
    performanceTracker.startBatch()
    console.log(`🔄 Processing batch ${batchIndex + 1}/${totalBatches} (${currentBatch.length} items)`)
    
    // Create promises for current batch
    const batchPromises = currentBatch.map(async (duplicate, index) => {
      const globalIndex = batchStart + index
      
      try {
        const result = await throttledMerge({
          duplicateId: duplicate.id,
          primaryRecordId: duplicate.primary_record_id,
          autoMerge: true,
          resolvedBy
        })
        
        processedCount++
        
        if (result.success) {
          successCount++
          if (result.error && !result.error.includes('already resolved')) {
            console.log(`⚠️  Duplicate ${result.duplicateId} succeeded with warning: ${result.error}`)
          }
        } else {
          errorCount++
          const errorType = categorizeError(result.error || 'unknown')
          console.log(`❌ Duplicate ${result.duplicateId} failed (${errorType}): ${result.error}`)
          
          errors.push({ 
            duplicateId: result.duplicateId, 
            error: result.error || 'Unknown error',
            errorType
          })
        }
        
        // Calculate progress and ETA
        if (onProgress) {
          const elapsed = Date.now() - startTime
          const currentRate = processedCount / (elapsed / 1000) // items per second
          const remaining = duplicatesToProcess.length - processedCount
          const eta = remaining > 0 ? (remaining / currentRate) * 1000 : 0 // milliseconds
          
          onProgress(processedCount, duplicatesToProcess.length, eta, currentRate)
        }
        
        return result
      } catch (error) {
        processedCount++
        errorCount++
        const errorMessage = (error as Error).message
        const errorType = categorizeError(errorMessage)
        
        console.log(`🚨 Unexpected error processing duplicate ${duplicate.id} (${errorType}): ${errorMessage}`)
        
        errors.push({ 
          duplicateId: duplicate.id, 
          error: errorMessage,
          errorType
        })
        
        return { success: false, duplicateId: duplicate.id, error: errorMessage }
      }
    })
    
    // Wait for current batch to complete
    await Promise.allSettled(batchPromises)
    
    const batchDuration = performanceTracker.endBatch()
    const batchErrorRate = errorCount / Math.max(processedCount, 1)
    
    console.log(`✅ Batch ${batchIndex + 1} complete in ${batchDuration}ms (${currentBatch.length} items, error rate: ${(batchErrorRate * 100).toFixed(1)}%)`)
    
    // Adaptive throttling based on error rate
    if (adaptiveThrottling && batchErrorRate > 0.2 && batchIndex < totalBatches - 1) {
      const delayMs = Math.min(batchErrorRate * 2000, 5000) // Max 5 second delay
      console.log(`⏸️  High error rate detected (${(batchErrorRate * 100).toFixed(1)}%), adding ${delayMs}ms delay...`)
      await new Promise(resolve => setTimeout(resolve, delayMs))
    }
    
    // Log overall progress every few batches
    if ((batchIndex + 1) % 3 === 0 || batchIndex === totalBatches - 1) {
      const overallErrorRate = errorCount / Math.max(processedCount, 1)
      console.log(`📈 Overall progress: ${processedCount}/${duplicatesToProcess.length} (${successCount} success, ${errorCount} errors, ${(overallErrorRate * 100).toFixed(1)}% error rate)`)
    }
  }
  
  const totalDuration = performanceTracker.getTotalDuration()
  const performanceMetrics = performanceTracker.getMetrics(errorCount, processedCount)
  const averageTimePerRecord = processedCount > 0 ? totalDuration / processedCount : 0
  
  console.log(`🏁 Enhanced batch merge complete in ${totalDuration}ms`)
  console.log(`📊 Results: ${successCount} success, ${errorCount} errors, ${skippedCount} skipped, ${blockedCount} blocked, ${retriedCount} retried`)
  console.log(`⚡ Performance: ${averageTimePerRecord.toFixed(1)}ms/record, ${(performanceMetrics.errorRate * 100).toFixed(1)}% error rate`)
  
  return {
    totalProcessed: processedCount,
    successCount,
    errorCount,
    skippedCount,
    blockedCount,
    retriedCount,
    processingTimeMs: totalDuration,
    averageTimePerRecord,
    errors,
    performanceMetrics
  }
}

export default {
  throttledMerge,
  batchMergeWithBottleneck,
  canAutoMerge
}

