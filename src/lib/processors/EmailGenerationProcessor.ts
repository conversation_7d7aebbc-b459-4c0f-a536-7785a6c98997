import { BaseProcessor } from './BaseProcessor'
import { ProcessorOptions, UnifiedEntityData } from '../../types/processing'
import { EMAIL_GENERATION_SYSTEM_PROMPT, EMAIL_GENERATION_USER_TEMPLATE } from '../prompts/email-generation'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'
import { formatNumber } from '../utils'

interface GeneratedEmail {
  campaign_id: string
  subject1: string
  body1: string
  subject2: string
  body2: string
  subject3: string
  body3: string
}

// V2 Contact Enrichment Data - fetched directly from contacts table
interface ContactEnrichmentV2Data {
  // Personal Information from V2 enrichment
  executive_summary?: string
  career_timeline?: string[] | any[]
  
  // Contact Information  
  additional_email?: string
  phone_number_secondary?: string
  
  // Social Media
  twitter?: string
  facebook?: string
  instagram?: string
  youtube?: string
  
  // Education
  education_college?: string
  education_college_year_graduated?: string
  education_high_school?: string
  education_high_school_year_graduated?: string
  
  // Personal Details
  honorable_achievements?: string[] | any[]
  hobbies?: string[] | any[]
  age?: string
  
  // Location Details
  contact_address?: string
  contact_zip_code?: string
  
  // Contact Metadata
  contact_type?: string
  relationship_owner?: string
  role_in_decision_making?: string
  
  // Interaction Tracking
  source_of_introduction?: string
  
  // Compliance
  accredited_investor_status?: boolean
  
  // Legacy fields from old enrichment (contact_enrichment table)
  notable_activities?: any[]
  education?: any[]
  personal_tidbits?: any[]
  conversation_hooks?: any[]
  sources?: any[]
  osint_profile?: string
  company_type?: string
  capital_positions?: any[]
  confidence?: number
  reasoning?: string
}

interface CompanyOverviewData {
  companyname?: string
  companytype?: string
  businessmodel?: string
  fundsize?: string
  aum?: string
  numberofproperties?: number
  headquarters?: string
  numberofoffices?: number
  foundedyear?: number
  numberofemployees?: string
  investmentfocus?: any[]
  geographicfocus?: any[]
  recentdeals?: any[]
  mission?: string
  approach?: string
  targetreturn?: string
  propertytypes?: any[]
  strategies?: any[]
}

// V2 Investment Criteria Data - from investment_criteria_central table
interface InvestmentCriteriaV2Data {
  investment_criteria_id?: number
  entity_id?: number
  entity_type?: string
  
  // Relationships to company investment criteria (if applicable)
  investment_criteria_debt_id?: number | null
  investment_criteria_equity_id?: number | null
  
  // Deal Scope
  capital_position?: string
  minimum_deal_size?: number | null
  maximum_deal_size?: number | null
  
  // Geography
  country?: string[]
  region?: string[]
  state?: string[]
  city?: string[]
  
  // Asset Strategy
  property_types?: string[]
  property_subcategories?: string[]
  strategies?: string[]
  decision_making_process?: string | null
  
  // Additional information
  notes?: string | null
  
  // Timestamps
  created_at?: string
  updated_at?: string
}

interface MatchingDeal {
  deal_id: string | number
  deal_name?: string
  score?: number
  breakdown?: Array<{
    field: string
    score: number
    reason: string
    weight?: number
  }>
  reasons?: string[]
  scoring_method?: string
  location?: string
  deal_size?: number
  property_type?: string
  capital_position?: string
  city?: string
  state?: string
  ltc_ratio?: number
  ltv_ratio?: number
  dscr?: number
  loan_term?: number
  target_close_date?: string
  irr?: number
  equity_multiple?: number
  hold_period?: number
  deal_strategy?: string
  // Additional fields from the API
  minimum_deal_size?: number
  maximum_deal_size?: number
  historical_irr?: number
  historical_em?: number
  deal_region?: string
  deal_capital_position?: string
  deal_property_types?: string[]
  deal_strategies?: string[]
  deal_financial_products?: string[]
  deal_loan_type?: string
  deal_loan_program?: string
  deal_recourse_loan?: string
  min_hold_period?: number
  max_hold_period?: number
  min_loan_term?: number
  max_loan_term?: number
  interest_rate?: number
  loan_to_value_min?: number
  loan_to_value_max?: number
  loan_to_cost_min?: number
  loan_to_cost_max?: number
  loan_origination_fee_min?: number
  loan_origination_fee_max?: number
  loan_exit_fee_min?: number
  loan_exit_fee_max?: number
  min_loan_dscr?: number
  max_loan_dscr?: number
  extra_fields?: any
  // New fields from enhanced API with investment criteria
  matching_criteria_count?: number
  all_criteria_matches?: Array<{
    criteria_id: number
    score: number
    breakdown: Array<{
      field: string
      score: number
      weight: number
      reason: string
      confidence?: number
    }>
    reasons: string[]
    weightSum: number
    investment_criteria?: {
      target_return?: number
      deal_size?: {
        min?: string
        max?: string
        unit: string
      }
      historical_performance?: {
        irr?: number
        equity_multiple?: number
      }
      location?: {
        country?: string[]
        region?: string[]
        state?: string[]
        city?: string[]
      }
      capital_position?: string[]
      property_types?: string[]
      property_sub_categories?: string[]
      strategies?: string[]
      financial_products?: string[]
      loan_terms?: {
        type?: string[]
        program?: string[]
        recourse?: string[]
        min_hold_period?: number
        max_hold_period?: number
        min_loan_term?: string
        max_loan_term?: string
      }
      loan_metrics?: {
        interest_rate?: number
        ltv_min?: string
        ltv_max?: string
        ltc_min?: string
        ltc_max?: string
        origination_fee_min?: number
        origination_fee_max?: number
        exit_fee_min?: number
        exit_fee_max?: number
        dscr_min?: number
        dscr_max?: number
      }
    }
  }>
  // Additional fields from actual deals table
  sponsor_name?: string
  deal_stage?: string
  priority?: string
  property_description?: string
  yield_on_cost?: number
  projected_gp_equity_multiple?: number
  projected_gp_irr?: number
  projected_lp_equity_multiple?: number
  projected_lp_irr?: number
  projected_total_equity_multiple?: number
  projected_total_irr?: number
  extraction_confidence?: string
  
  // Fields expected by ContactDetail component
  ask_capital_position?: string | string[]
  ask_amount?: number | string
  match_score?: number
  best_score?: number
  fallback_used?: boolean
  location_fallback_used?: boolean
  location_match_type?: 'city' | 'state' | 'location'
  
  // Deal data object from V2 API
  deal_data?: {
    deal_id?: number
    deal_name?: string
    summary?: string
    ask_capital_position?: string[]
    ask_amount?: number[]
    strategy?: string
    deal_stage?: string
    deal_status?: string
    hold_period?: string
    yield_on_cost?: number
    projected_total_irr?: number
    total_equity_multiple?: number
    projected_gp_irr?: number
    projected_lp_irr?: number
    // V2 API fields from dealsv2 table
    total_internal_rate_of_return_irr?: number
    common_equity_internal_rate_of_return_irr?: number
    gp_internal_rate_of_return_irr?: number
    lp_internal_rate_of_return_irr?: number
    preferred_equity_internal_rate_of_return_irr?: number
    property_address?: string
    property_city?: string
    property_state?: string
    property_region?: string
    property_country?: string
    property_type?: string
    property_subtype?: string
    building_sqft?: string
    number_of_units?: number
    [key: string]: any // Allow additional fields
  }
}

interface ArticleData {
  article_id: number
  headline?: string
  summary?: string
  sentiment?: string
  key_takeaways?: string | string[]
  market_trend_tags?: string[]
  publication_date?: string
  publication_name?: string
  author?: string
  article_url?: string
  source_url?: string
  is_relevant?: boolean
  is_distressed?: boolean
  
  // Fields expected by ArticleTab component
  deal_sizes?: string[]
  property_type?: string | string[]
  location_city?: string | string[]
  location_state?: string | string[]
  score?: number
  breakdown?: Array<{
    field: string
    score: number
    reason?: string
  }>
  reasons?: string[]
  
  // Related entities found in the article
  entities?: Array<{
    entity_id?: number
    entity_type: string
    entity_name: string
    entity_role: string
    entity_description?: string
    confidence?: number
  }>

  // Properties mentioned in the article
  properties?: Array<{
    property_name?: string
    address?: string
    city?: string
    state?: string
    square_footage?: number
    construction_type?: string
  }>

  // Transactions mentioned in the article
  transactions?: Array<{
    deal_type?: string
    deal_size?: string
    cap_rate?: string
    capital_position?: string
  property_types?: string[]
  }>

  // Market metrics from the article
  market_metrics?: Array<{
    market_city?: string
    market_state?: string
    vacancy_rate?: number
    rental_rate?: number
    cap_rate_avg?: number
    rental_rate_trend?: string
    demand_trend?: string
  }>
}

interface CategorizedCriteria {
  criteria_id: number
  capital_position: string
  state: string[]
  deal_size: string
  property_type: string[]
  strategy: string[]
  target_return?: number
}

export class EmailGenerationProcessor extends BaseProcessor {
  private llmProvider;

  constructor(options: ProcessorOptions = {}) {

    const emailGenerationBottleneckConfig = {
      maxConcurrent: 20,                   // Reduced from 5 to prevent queue overflow
      minTime: 1000,                      // 1 second between requests
      retryAttempts: 3,                   // Standard retries for LLM API
      retryDelayBase: 2500,               // 2.5 second base delay for retries
      retryDelayMax: 40000,               // Max 40 second retry delay
      timeout: 300000,                    // 5 minutes timeout for complex LLM processing
      highWater: 200,                     // Lower queue limit for complex tasks
      strategy: 'OVERFLOW' as any,        // Use OVERFLOW strategy
      defaultPriority: 5,                 // Normal priority for contact IC
      enableJobMetrics: true              // Track LLM API performance
    }
    super('EmailGeneration', {
      ...options,
      bottleneckConfig: options.bottleneckConfig || emailGenerationBottleneckConfig
    })

    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));

    // Create LLM provider with fallback support for missing API keys
    try {
      // Try OpenAI first
      this.llmProvider = LLMFactory.createProvider(
        'openai',
        loggerAdapter,
        {
          apiKey: process.env.OPENAI_API_KEY,
        }
      );
      this.log('info', 'EmailGenerationProcessor initialized with OpenAI provider');
    } catch (error) {
      this.log('error', `Error initializing EmailGenerationProcessor: ${error}`)
    }
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    // V2 Pipeline: email_validation -> contact_enrichment_v2 -> contact_investment_criteria -> email_generation
    const baseFilters = {
      // Base filters that should always apply (none for email generation)
    }
    const specificFilters = {
      emailVerificationStatus: 'completed',            // Email must be verified
      contactInvestmentCriteriaStatus: 'completed',   // Investment criteria must be processed
      emailGenerationStatus: 'pending'               // Email generation is pending
    }
    
    this.log('info', `Getting unprocessed entities for email generation with base filters: ${JSON.stringify(baseFilters)} and specific filters: ${JSON.stringify(specificFilters)}`)
    
    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'contact')
  }



  async processEntity(entity: UnifiedEntityData): Promise<{ success: boolean; error?: string }> {
    // Only process contacts for email generation
    if (entity.entity_type !== 'contact') {
      return { success: false, error: 'Email generation only supports contacts' }
    }

    try {
      this.log('info', `Generating email for contact: ${entity.first_name} ${entity.last_name} (${entity.email})`)

      // Set status to running
      await this.updateEmailGenerationStatus(entity.contact_id!, 'running')

      // Get comprehensive context data for the contact using V2 APIs
      const contactEnrichmentData = await this.getContactEnrichmentV2Data(entity.contact_id!)


      // Get company overview data
      const companyOverviewData = await this.getCompanyOverviewData(entity.company_id!)

      // Get investment criteria data
      const investmentCriteriaData = await this.getInvestmentCriteriaV2Data(entity.contact_id!, entity.company_id!)

      // Get relevant article data and market insights (V2 enriched articles)
      const articleData = await this.getArticleData(entity.contact_id!, entity.company_id!)


      // Get matching deals using V2 API with enhanced scoring
      const matchingDeals = await this.getMatchingDealsV2ForContact(entity.contact_id!)

    
      

      if (!contactEnrichmentData) {
        return { success: false, error: 'No contact enrichment data available' }
      }
      // Check if we have relevant content (V2 deals, articles, news, categorized criteria)
      this.log('debug', `Checking relevance for contact ${entity.contact_id}: ${matchingDeals.length} V2 deals, ${articleData.length} articles`)

      const hasRelevantDeals = matchingDeals.length > 0
      const hasRelevantArticles = articleData.length > 0

      this.log('debug', `Relevance check for contact ${entity.contact_id}: hasRelevantDeals=${hasRelevantDeals}, hasRelevantArticles=${hasRelevantArticles}`)

      // Skip email generation if no relevant content found
      if (!hasRelevantDeals && !hasRelevantArticles) {
        this.log('info', `Skipping email generation for contact ${entity.contact_id}: No relevant deals, articles, or investment criteria found`)
        await this.updateEmailGenerationStatus(entity.contact_id!, 'skipped', 'No relevant content found for email generation')
        return { success: true } // Mark as successful but skipped
      }

      // Generate personalized email using V2 data
      const generatedEmail = await this.generatePersonalizedEmailV2(
        entity,
        contactEnrichmentData,
        companyOverviewData,
        investmentCriteriaData,
        matchingDeals.slice(0, 10),     // V2 deals with enhanced scoring
        articleData.slice(0, 10),         // V2 enriched articles with market insights
        this.options.campaignId || ''
      )

      if (!generatedEmail) {
        return { success: false, error: 'Failed to generate email' }
      }
      this.log('info', `Generated email: ${JSON.stringify(generatedEmail, null, 2)}`)
      // Store generated email in messages table
      await this.storeGeneratedEmail(entity.contact_id!, generatedEmail, this.options.campaignId || '')

      // Auto-sync to Smartlead if configured
      await this.syncToSmartlead(entity.contact_id!, generatedEmail, entity, this.options.campaignId || '')

      this.log('info', `Successfully generated email for contact ${entity.contact_id}`)

      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error generating email for contact ${entity.contact_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      // Check if the status was already set to 'skipped' in processEntity
      const currentStatus = await this.getCurrentEmailGenerationStatus(entityId)
      if (currentStatus !== 'skipped') {
        // Email generation completed successfully
        await this.updateEmailGenerationStatus(entityId, 'completed')
      }
      // If status is 'skipped', leave it as is (already set in processEntity)
    } else {
      // Email generation failed
      await this.updateEmailGenerationStatus(entityId, 'failed', error)
      await this.incrementProcessingErrorCount('contact', entityId)
    }
  }

  /**
   * Get contact enrichment V2 data from contacts table and contact_enrichment table (legacy fallback)
   */
  private async getContactEnrichmentV2Data(contactId: number): Promise<ContactEnrichmentV2Data | null> {
    try {
      // First fetch V2 enrichment data directly from contacts table
      const contactSql = `
        SELECT 
          -- V2 Enrichment fields from contacts table
          executive_summary,
          career_timeline,
          additional_email,
          phone_number_secondary,
          twitter,
          facebook,
          instagram,
          youtube,
          education_college,
          education_college_year_graduated,
          education_high_school,
          education_high_school_year_graduated,
          honorable_achievements,
          hobbies,
          age,
          contact_address,
          contact_zip_code,
          contact_type,
          relationship_owner,
          role_in_decision_making,
          source_of_introduction,
          accredited_investor_status,
          
          -- Processing status
          contact_enrichment_v2_status
        FROM contacts
        WHERE contact_id = $1
      `
      const contactResult = await this.query(contactSql, [contactId])

      if (contactResult.length === 0) {
        this.log('warn', `No contact found for ID ${contactId}`)
        return null
      }

      const contactData: any = contactResult[0]

      // If V2 enrichment is not completed, try to get legacy enrichment data for fallback
      let legacyData: any = {}
      if (contactData.contact_enrichment_v2_status !== 'completed') {
        const legacySql = `
          SELECT 
            osint_profile,
            executive_summary,
            career_timeline,
            notable_activities,
            education,
            personal_tidbits,
            conversation_hooks,
            sources,
            company_type,
            capital_positions,
            confidence,
            reasoning
          FROM contact_enrichment
          WHERE contact_id = $1 AND status = 'completed'
          ORDER BY created_at DESC
          LIMIT 1
        `
        const legacyResult = await this.query(legacySql, [contactId])
        if (legacyResult.length > 0) {
          legacyData = legacyResult[0]
          this.log('info', `Using legacy enrichment data for contact ${contactId} (V2 status: ${contactData.contact_enrichment_v2_status})`)
        }
      }

      // Parse JSONB fields that might be stored as strings
      const jsonFields = ['career_timeline', 'honorable_achievements', 'hobbies']
      jsonFields.forEach(field => {
        const value = contactData[field] || legacyData[field]
        if (typeof value === 'string') {
          try {
            contactData[field] = JSON.parse(value)
          } catch (parseError) {
            this.log('warn', `Error parsing JSON field ${field}: ${parseError}`)
            contactData[field] = []
          }
        }
      })

      // Merge V2 data with legacy fallback
      const enrichmentData: ContactEnrichmentV2Data = {
        // V2 fields (prioritized)
        executive_summary: contactData.executive_summary || legacyData.executive_summary,
        career_timeline: contactData.career_timeline || legacyData.career_timeline || [],
        additional_email: contactData.additional_email,
        phone_number_secondary: contactData.phone_number_secondary,
        twitter: contactData.twitter,
        facebook: contactData.facebook,
        instagram: contactData.instagram,
        youtube: contactData.youtube,
        education_college: contactData.education_college,
        education_college_year_graduated: contactData.education_college_year_graduated,
        education_high_school: contactData.education_high_school,
        education_high_school_year_graduated: contactData.education_high_school_year_graduated,
        honorable_achievements: contactData.honorable_achievements || [],
        hobbies: contactData.hobbies || [],
        age: contactData.age,
        contact_address: contactData.contact_address,
        contact_zip_code: contactData.contact_zip_code,
        contact_type: contactData.contact_type,
        relationship_owner: contactData.relationship_owner,
        role_in_decision_making: contactData.role_in_decision_making,
        source_of_introduction: contactData.source_of_introduction,
        accredited_investor_status: contactData.accredited_investor_status,

        // Legacy fallback fields
        notable_activities: legacyData.notable_activities || [],
        education: legacyData.education || [],
        personal_tidbits: legacyData.personal_tidbits || [],
        conversation_hooks: legacyData.conversation_hooks || [],
        sources: legacyData.sources || [],
        osint_profile: legacyData.osint_profile,
        company_type: legacyData.company_type,
        capital_positions: legacyData.capital_positions || [],
        confidence: legacyData.confidence,
        reasoning: legacyData.reasoning
      }

      this.log('info', `Successfully fetched enrichment data for contact ${contactId} (V2: ${!!contactData.executive_summary}, legacy: ${!!legacyData.executive_summary})`)
      return enrichmentData

    } catch (error) {
      this.log('error', `Error fetching contact enrichment V2 data for ${contactId}: ${error}`)
      return null
    }
  }

  /**
   * Get company overview data from companies table (V2 data stored directly in companies table)
   */
  private async getCompanyOverviewData(companyId: number): Promise<CompanyOverviewData | null> {
    try {
      // Get V2 company overview data directly from companies table
      const sql = `
        SELECT 
          -- Core Company Information
          company_name as companyname,
          company_type as companytype,
          business_model as businessmodel,
          fund_size as fundsize,
          aum,
          number_of_properties as numberofproperties,
          
          -- Location Information
          COALESCE(headquarters_address, company_address) as headquarters,
          number_of_offices as numberofoffices,
          founded_year as foundedyear,
          number_of_employees as numberofemployees,
          
          -- Investment Strategy
          investment_focus as investmentfocus,
          investment_strategy_mission as mission,
          investment_strategy_approach as approach,
          
          -- Financial Information
          annual_revenue,
          market_capitalization,
          credit_rating,
          dry_powder,
          annual_deployment_target,
          balance_sheet_strength,
          funding_sources,
          
          -- Strategic Information
          market_cycle_positioning,
          sustainability_esg_focus,
          key_equity_partners,
          key_debt_partners,
          partnerships,
          
          -- Business Information  
          products_services_description,
          target_customer_profile,
          major_competitors,
          unique_selling_proposition,
          
          -- Processing status
          overview_v2_status
        FROM companies
        WHERE company_id = $1
      `
      const result = await this.query(sql, [companyId])

      if (result.length === 0) {
        this.log('warn', `No company found for ID ${companyId}`)
        return null
      }

      const companyData = result[0]

      // Parse array fields that might be stored as PostgreSQL arrays or JSON
      const parseArrayField = (field: any): any[] => {
        if (!field) return []
        if (Array.isArray(field)) return field
        if (typeof field === 'string') {
          // Handle PostgreSQL array format: {value1,value2,value3}
          if (field.startsWith('{') && field.endsWith('}')) {
            const arrayStr = field.slice(1, -1)
            if (arrayStr.length === 0) return []
            return arrayStr.split(',').map((item: string) => 
              item.replace(/^"/, '').replace(/"$/, '').trim()
            ).filter((item: string) => item.length > 0)
          }
          // Try parsing as JSON
          try {
            const parsed = JSON.parse(field)
            return Array.isArray(parsed) ? parsed : [parsed]
          } catch {
            return []
          }
        }
        return []
      }

      // Map V2 data to the expected CompanyOverviewData format
      const overviewData: CompanyOverviewData = {
        companyname: companyData.companyname as string,
        companytype: companyData.companytype as string,
        businessmodel: companyData.businessmodel as string,
        fundsize: companyData.fundsize ? companyData.fundsize.toString() : undefined,
        aum: companyData.aum ? companyData.aum.toString() : undefined,
        numberofproperties: companyData.numberofproperties as number,
        headquarters: companyData.headquarters as string,
        numberofoffices: companyData.numberofoffices as number,
        foundedyear: companyData.foundedyear as number,
        numberofemployees: companyData.numberofemployees ? companyData.numberofemployees.toString() : undefined,
        
        // Investment focus and strategy
        investmentfocus: parseArrayField(companyData.investmentfocus),
        mission: companyData.mission as string,
        approach: companyData.approach as string,
        
        // Additional V2 fields not in original interface
        geographicfocus: [], // This would need to be derived from other fields if needed
        recentdeals: [], // This would need to be fetched from deals data
        targetreturn: undefined, // This would come from investment criteria
        propertytypes: [], // This would come from investment criteria
        strategies: [] // This would come from investment criteria
      }

      const hasV2Data = companyData.overview_v2_status === 'completed'
      this.log('info', `Retrieved company overview data for company ${companyId} (V2 status: ${companyData.overview_v2_status})`)
      
      return overviewData

    } catch (error) {
      this.log('error', `Error fetching company overview data for ${companyId}: ${error}`)
      return null
    }
  }

  /**
   * Get relevant article data and market insights for contact/company
   */
  private async getArticleData(contactId: number, companyId: number): Promise<ArticleData[]> {
    try {
      const apiUrl = `${process.env.API_BASE_URL || 'http://localhost:3031'}/api/matching/articles-for-contact/${contactId}`
      
      const response = await fetch(apiUrl)
      
      if (!response.ok) {
        this.log('warn', `Articles API returned ${response.status} for contact ${contactId}`)
        return []
      }

      const data = await response.json()
      const articles = data.matches || []
      
      // Transform API response to match our ArticleData interface and ArticleTab expectations
      const articlesData: ArticleData[] = articles.map((article: any) => ({
        article_id: article.article_id,
        headline: article.headline,
        summary: article.summary,
        sentiment: article.sentiment,
        key_takeaways: article.key_takeaways || [],
        market_trend_tags: article.market_trend_tags || [],
        publication_date: article.publication_date,
        publication_name: article.publication_name,
        author: article.author,
        article_url: article.article_url,
        source_url: article.source_url,
        is_relevant: article.is_relevant,
        is_distressed: article.is_distressed,
        
        // Fields expected by ArticleTab component
        deal_sizes: article.deal_sizes || [],
        property_type: article.property_type || article.property_types,
        location_city: article.location_city || article.cities,
        location_state: article.location_state || article.states,
        score: article.score,
        breakdown: article.breakdown || [],
        reasons: article.reasons || [],
        
        entities: article.entities || [],
        properties: article.properties || [],
        transactions: article.transactions || [],
        market_metrics: article.market_data || []
      }))

      this.log('info', `Retrieved ${articlesData.length} relevant articles from API for contact ${contactId}`)
      return articlesData

    } catch (error) {
      this.log('error', `Error retrieving article data from API for contact ${contactId}: ${error}`)
      return []
    }
  }

  /**
   * Get investment criteria V2 data from investment_criteria_central table
   */
  private async getInvestmentCriteriaV2Data(contactId: number, companyId: number): Promise<InvestmentCriteriaV2Data[]> {
    try {
      const sql = `
        SELECT 
          investment_criteria_id,
          entity_id,
          entity_type,
          investment_criteria_debt_id,
          investment_criteria_equity_id,
          capital_position,
          minimum_deal_size,
          maximum_deal_size,
          country,
          region,
          state,
          city,
          property_types,
          property_subcategories,
          strategies,
          decision_making_process,
          notes,
          created_at,
          updated_at
        FROM investment_criteria_central
        WHERE (
          (entity_type = 'contact' AND entity_id = $1)
        )
        ORDER BY created_at DESC
      `
      const result = await this.query(sql, [contactId])

      if (result.length === 0) {
        this.log('info', `No investment criteria V2 data found for contact ${contactId}`)
        return []
      }

      // Parse array fields that might be stored as PostgreSQL arrays
      const parsedResult = result.map(row => {
        const arrayFields = ['country', 'region', 'state', 'city', 'property_types', 'property_subcategories', 'strategies']
        arrayFields.forEach(field => {
          if (row[field] && typeof row[field] === 'string') {
            try {
              // Handle PostgreSQL array format: {value1,value2,value3}
              if (row[field].startsWith('{') && row[field].endsWith('}')) {
                const arrayStr = row[field].slice(1, -1) // Remove { }
                if (arrayStr.length > 0) {
                  // Split by comma and clean up quotes
                  row[field] = arrayStr.split(',').map((item: string) => 
                    item.replace(/^"/, '').replace(/"$/, '').trim()
                  ).filter((item: string) => item.length > 0)
                } else {
                  row[field] = []
                }
              } else {
                // Try parsing as JSON
                row[field] = JSON.parse(row[field])
              }
            } catch (parseError) {
              this.log('warn', `Error parsing array field ${field}: ${parseError}`)
              row[field] = []
            }
          } else if (!row[field]) {
            row[field] = []
          }
        })
        return row
      })

      this.log('info', `Found ${parsedResult.length} investment criteria V2 records for contact ${contactId}`)
      return parsedResult as InvestmentCriteriaV2Data[]
    } catch (error) {
      this.log('error', `Error fetching investment criteria V2 data for contact ${contactId}: ${error}`)
      return []
    }
  }

  /**
   * Get matching deals V2 for contact using the new V2 matching API with enhanced scoring
   */
  private async getMatchingDealsV2ForContact(contactId: number): Promise<MatchingDeal[]> {
    try {
      this.log('debug', `Fetching V2 matching deals for contact ${contactId} with enhanced scoring and CRM mode`)

      // Use the V2 API endpoint with enhanced scoring and CRM mode enabled
      const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3031'}/api/matching-v2/deals-for-contact/${contactId}?crm_mode=true&show_all=false`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.matches || !Array.isArray(data.matches)) {
        this.log('debug', `No matching deals V2 found for contact ${contactId}`)
        return []
      }

      // The API already returns all the needed deal data with proper scoring and CRM filtering
      // Transform the data to match our MatchingDeal interface
      const matchingDeals = data.matches.map((deal: any) => ({
        ...deal,
        // Ensure consistent field naming
        match_score: deal.match_score || deal.score,
        capital_position: deal.capital_position,
        score: deal.score || deal.match_score,
        breakdown: deal.breakdown || [],
        reasons: deal.reasons || []
      }))

      this.log('info', `Found ${matchingDeals.length} V2 matching deals (CRM filtered, high quality) for contact ${contactId}`)
      return matchingDeals as MatchingDeal[]
    } catch (error) {
      this.log('error', `Error fetching V2 matching deals for contact ${contactId}: ${error}`)
      return []
    }
  }

  /**
   * Get matching deals for contact using the existing API endpoint with investment criteria data (legacy method)
   */
  private async getMatchingDealsForContact(contactId: number): Promise<MatchingDeal[]> {
    try {
      this.log('debug', `Fetching matching deals for contact ${contactId} with investment criteria data and CRM mode`)

      // Use the API endpoint with investment criteria data and CRM mode enabled
      const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3030'}/api/matching/deals-for-contact/${contactId}?add_ic_data=true&is_crm=true`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.matches || !Array.isArray(data.matches)) {
        this.log('debug', `No matching deals found for contact ${contactId}`)
        return []
      }

      // Get actual deal data from the deals table to ensure unique deal information
      const dealIds = data.matches.map((deal: any) => deal.deal_id).filter(Boolean)

      if (dealIds.length === 0) {
        this.log('debug', `No valid deal IDs found for contact ${contactId}`)
        return []
      }

      // Fetch only essential deal data from the deals table
      const dealsSql = `
        SELECT 
          deal_id,
          deal_name,
          sponsor_name,
          deal_stage,
          priority,
          property_description,
          yield_on_cost,
          projected_gp_irr,
          projected_lp_irr,
          projected_total_irr,
          extraction_confidence,
          extra_fields
        FROM deals 
        WHERE deal_id = ANY($1)
        ORDER BY deal_id
      `

      const dealsResult = await this.query(dealsSql, [dealIds])
      const dealsMap = new Map(dealsResult.map((deal: any) => [deal.deal_id, deal]))

      // Merge API matching data with actual deal data
      const enrichedDeals = data.matches.map((match: any) => {
        const actualDeal = dealsMap.get(match.deal_id)
        if (!actualDeal) {
          // this.log('warn', `No actual deal data found for deal_id ${match.deal_id}`)
          return match
        }

        // Merge the matching data with essential deal data
        return {
          ...match,
          // Use actual deal data for essential information
          deal_name: actualDeal.deal_name || match.deal_name,
          sponsor_name: actualDeal.sponsor_name,
          deal_stage: actualDeal.deal_stage,
          priority: actualDeal.priority,
          property_description: actualDeal.property_description,
          yield_on_cost: actualDeal.yield_on_cost,
          projected_gp_irr: actualDeal.projected_gp_irr,
          projected_lp_irr: actualDeal.projected_lp_irr,
          projected_total_irr: actualDeal.projected_total_irr,
          extraction_confidence: actualDeal.extraction_confidence,
          extra_fields: actualDeal.extra_fields
        }
      })

      this.log('debug', `Found ${data.matches.length} total deals (CRM filtered), enriched with actual deal data for contact ${contactId}`)
      return enrichedDeals as MatchingDeal[]
    } catch (error) {
      this.log('error', `Error fetching matching deals for contact ${contactId}: ${error}`)
      return []
    }
  }

  /**
   * Generate personalized email using AI with V2 data structures
   */
  private async generatePersonalizedEmailV2(
    contact: UnifiedEntityData,
    contactEnrichmentData: ContactEnrichmentV2Data,
    companyOverviewData: CompanyOverviewData | null,
    investmentCriteriaData: InvestmentCriteriaV2Data[],
    matchingDeals: MatchingDeal[],
    articleData: ArticleData[] = [],
    campaign_id: string = ''
  ): Promise<GeneratedEmail | null> {
    try {
      // Use the new V2 message building with enhanced data structures
      const messages = this.buildEmailGenerationV2Messages(
        contact,
        contactEnrichmentData,
        companyOverviewData,
        investmentCriteriaData,
        matchingDeals,
        articleData
      )
      // Use the LLM provider with fallback support
      const response = await this.llmProvider.callLLM(messages);

      if (!response.content) {
        throw new Error(`No response content from ${response.provider}`);
      }

      this.log('info', `Generated email using V2 data and ${response.provider} (${response.model})`);

      return this.parseEmailResponse(response.content, campaign_id)
    } catch (error) {
      this.log('error', `Error calling LLM email generation V2 API: ${error}`)
      return null
    }
  }

  /**
   * Build messages for email generation with V2 data structures and enhanced information
   */
  private buildEmailGenerationV2Messages(
    contact: UnifiedEntityData,
    contactEnrichmentData: ContactEnrichmentV2Data,
    companyOverviewData: CompanyOverviewData | null,
    investmentCriteriaData: InvestmentCriteriaV2Data[],
    matchingDeals: MatchingDeal[],
    articleData: ArticleData[] = []
  ): LLMMessage[] {
    // Determine sender context (this would typically come from user/company settings)
    const senderInfo = {
      name: process.env.SENDER_NAME || 'Your Name',
      company: process.env.SENDER_COMPANY || 'Your Company',
      title: process.env.SENDER_TITLE || 'Business Development'
    }
    console.log('senderInfo', senderInfo)

    // Build contact summary with V2 data
    let contactSummary = `**Contact:** ${contact.first_name} ${contact.last_name}`
    if (contact.title) contactSummary += `\n**Title:** ${contact.title}`
    if (contact.company_name) contactSummary += `\n**Company:** ${contact.company_name}`
    if (contact.industry) contactSummary += `\n**Industry:** ${contact.industry}`

    // Build V2 lead enrichment data summary
    let leadEnrichmentData = ''
    if (contactEnrichmentData.executive_summary) {
      leadEnrichmentData += `**Executive Summary:** ${contactEnrichmentData.executive_summary}\n`
    }

    if (contactEnrichmentData.conversation_hooks && contactEnrichmentData.conversation_hooks.length > 0) {
      leadEnrichmentData += `**Conversation Hooks:** ${contactEnrichmentData.conversation_hooks.slice(0, 3).join(', ')}\n`
    }

    if (contactEnrichmentData.notable_activities && contactEnrichmentData.notable_activities.length > 0) {
      leadEnrichmentData += `**Notable Activities:** ${contactEnrichmentData.notable_activities.slice(0, 2).join(', ')}\n`
    }

    if (contactEnrichmentData.personal_tidbits && contactEnrichmentData.personal_tidbits.length > 0) {
      leadEnrichmentData += `**Personal Tidbits:** ${contactEnrichmentData.personal_tidbits.slice(0, 2).join(', ')}\n`
    }

    if (contactEnrichmentData.company_type) {
      leadEnrichmentData += `**Company Type:** ${contactEnrichmentData.company_type}\n`
    }

    if (contactEnrichmentData.capital_positions && contactEnrichmentData.capital_positions.length > 0) {
      leadEnrichmentData += `**Capital Positions:** ${contactEnrichmentData.capital_positions.join(', ')}\n`
    }

    // Build company overview data summary
    let companyOverviewDataSummary = ''
    if (companyOverviewData) {
      if (companyOverviewData.companytype) {
        companyOverviewDataSummary += `**Company Type:** ${companyOverviewData.companytype}\n`
      }
      if (companyOverviewData.businessmodel) {
        companyOverviewDataSummary += `**Business Model:** ${companyOverviewData.businessmodel}\n`
      }
      if (companyOverviewData.fundsize) {
        companyOverviewDataSummary += `**Fund Size:** ${companyOverviewData.fundsize}\n`
      }
      if (companyOverviewData.aum) {
        companyOverviewDataSummary += `**AUM:** ${companyOverviewData.aum}\n`
      }
      if (companyOverviewData.investmentfocus && companyOverviewData.investmentfocus.length > 0) {
        companyOverviewDataSummary += `**Investment Focus:** ${companyOverviewData.investmentfocus.slice(0, 3).join(', ')}\n`
      }
      if (companyOverviewData.geographicfocus && companyOverviewData.geographicfocus.length > 0) {
        companyOverviewDataSummary += `**Geographic Focus:** ${companyOverviewData.geographicfocus.slice(0, 3).join(', ')}\n`
      }
      if (companyOverviewData.recentdeals && companyOverviewData.recentdeals.length > 0) {
        companyOverviewDataSummary += `**Recent Deals:** ${companyOverviewData.recentdeals.slice(0, 2).map((deal: any) =>
          typeof deal === 'string' ? deal : JSON.stringify(deal)
        ).join(', ')}\n`
      }
    }

    // Build V2 investment criteria data summary
    let investmentCriteriaDataSummary = ''
    if (investmentCriteriaData.length > 0) {
      investmentCriteriaDataSummary += `**Investment Criteria V2:**\n`
      investmentCriteriaData.forEach((criteria, index) => {
        if (index < 3) { // Limit to first 3 criteria
          investmentCriteriaDataSummary += `\n**Criteria ${index + 1}:**\n`
          if (criteria.capital_position) {
            investmentCriteriaDataSummary += `- Capital Position: ${criteria.capital_position}\n`
          }
          if (criteria.property_types && criteria.property_types.length > 0) {
            investmentCriteriaDataSummary += `- Property Types: ${criteria.property_types.join(', ')}\n`
          }
          if (criteria.property_subcategories && criteria.property_subcategories.length > 0) {
            investmentCriteriaDataSummary += `- Property Subcategories: ${criteria.property_subcategories.join(', ')}\n`
          }
          if (criteria.strategies && criteria.strategies.length > 0) {
            investmentCriteriaDataSummary += `- Strategies: ${criteria.strategies.join(', ')}\n`
          }
          if (criteria.minimum_deal_size) {
            investmentCriteriaDataSummary += `- Min Deal Size: $${criteria.minimum_deal_size.toLocaleString()}\n`
          }
          if (criteria.maximum_deal_size) {
            investmentCriteriaDataSummary += `- Max Deal Size: $${criteria.maximum_deal_size.toLocaleString()}\n`
          }
          if (criteria.country && criteria.country.length > 0) {
            investmentCriteriaDataSummary += `- Countries: ${criteria.country.join(', ')}\n`
          }
          if (criteria.region && criteria.region.length > 0) {
            investmentCriteriaDataSummary += `- Regions: ${criteria.region.join(', ')}\n`
          }
          if (criteria.state && criteria.state.length > 0) {
            investmentCriteriaDataSummary += `- States: ${criteria.state.join(', ')}\n`
          }
          if (criteria.city && criteria.city.length > 0) {
            investmentCriteriaDataSummary += `- Cities: ${criteria.city.join(', ')}\n`
          }
        }
      })
    }

    // Build matching deals summary with V2 data from API
    let matchingDealsSummary = ''
    if (matchingDeals.length > 0) {
      matchingDealsSummary += `**Matching Deals V2:**\n`
      matchingDeals.slice(0, 10).forEach((deal, index) => {
        matchingDealsSummary += `\n**Deal ${index + 1}:**\n`
        
        // Deal name - available directly or from deal_data
        const dealName = deal.deal_name || deal.deal_data?.deal_name
        if (dealName) matchingDealsSummary += `- Name: ${dealName}\n`
        
        // Capital position - this is the matched capital position
        if (deal.capital_position) matchingDealsSummary += `- Capital Position: ${deal.capital_position}\n`
        
        // State from property data
        const state = deal.deal_data?.property_state
        if (state) matchingDealsSummary += `- State: ${state}\n`
        
        // Property type from property data
        const propertyType = deal.deal_data?.property_type
        if (propertyType) matchingDealsSummary += `- Property Type: ${propertyType}\n`
        
        // Deal size from ask_amount array (get the amount for the matched capital position)
        const askAmounts = deal.deal_data?.ask_amount
        const capitalPositions = deal.deal_data?.ask_capital_position
        if (askAmounts && Array.isArray(askAmounts) && askAmounts.length > 0) {
          // Find the amount for the matched capital position or use the first amount
          let dealAmount = askAmounts[0]
          if (capitalPositions && Array.isArray(capitalPositions) && deal.capital_position) {
            const positionIndex = capitalPositions.indexOf(deal.capital_position)
            if (positionIndex !== -1 && positionIndex < askAmounts.length) {
              dealAmount = askAmounts[positionIndex]
            }
          }
          if (dealAmount) {
            matchingDealsSummary += `- Deal Size: $${formatNumber(dealAmount)}M\n`
          }
        }
        
        // IRR - check multiple possible fields from deal_data (V2 API returns dealsv2 table data)
        const irr = Number(deal.deal_data?.total_internal_rate_of_return_irr) || 
                   Number(deal.deal_data?.common_equity_internal_rate_of_return_irr) ||
                   Number(deal.deal_data?.gp_internal_rate_of_return_irr) ||
                   Number(deal.deal_data?.lp_internal_rate_of_return_irr) ||
                   Number(deal.deal_data?.projected_total_irr) || 
                   Number(deal.deal_data?.projected_gp_irr) ||
                   Number(deal.deal_data?.projected_lp_irr) ||
                   Number(deal.projected_total_irr)
        if (irr && !isNaN(irr)) {
          // IRR values in database are in decimal format (0.1790 = 17.9%)
          // The matching API may return values in different formats, so we need to handle both
          let formattedIrr: string
          if (irr < 1) {
            // Value is in decimal format (0.1790), convert to percentage
            formattedIrr = (irr * 100).toFixed(1)
          } else if (irr < 100) {
            // Value is already in percentage format (17.9), use as is
            formattedIrr = irr.toFixed(1)
          } else {
            // Value is in large percentage format (1790), convert to normal percentage
            formattedIrr = (irr / 100).toFixed(1)
          }
          matchingDealsSummary += `- IRR: ${formattedIrr}%\n`
        }
        
        // Strategy
        if (deal.deal_data?.strategy) matchingDealsSummary += `- Strategy: ${deal.deal_data.strategy}\n`
        matchingDealsSummary += `\n`
      })
    }



    // Build article data summary from ArticleEnrichmentProcessor
    let articleDataSummary = ''
    if (articleData.length > 0) {
      articleDataSummary += `**Market Insights & Article Data:**\n`
      articleData.slice(0, 10).forEach((article, index) => {
        articleDataSummary += `\n**Article ${index + 1}:**\n`
        if (article.headline) articleDataSummary += `- Headline: ${article.headline}\n`
        if (article.summary) articleDataSummary += `- Summary: ${article.summary}\n`
      })
    }
    // console.log('articleDataSummary', articleDataSummary)
    // console.log('matchingDealsSummary', matchingDealsSummary)
    // console.log('investmentCriteriaDataSummary', investmentCriteriaDataSummary)
    // console.log('companyOverviewDataSummary', companyOverviewDataSummary)
    // console.log('leadEnrichmentData', leadEnrichmentData)
    // console.log('contactSummary', contactSummary)
    // console.log('senderInfo', senderInfo)

    // Return the messages array for the LLM
    return [
      {
        role: 'system',
        content: EMAIL_GENERATION_SYSTEM_PROMPT
      },
      {
        role: 'user',
        content: EMAIL_GENERATION_USER_TEMPLATE(
          contactSummary,
          senderInfo,
          leadEnrichmentData,
          companyOverviewDataSummary,
          investmentCriteriaDataSummary,
          matchingDealsSummary,
          articleDataSummary
        )
      }
    ]
  }

  /**
   * Parse email generation response
   */
  private parseEmailResponse(content: string, campaign_id: string): GeneratedEmail | null {
    try {
      // Extract JSON from response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }

      const result = this.llmProvider.parseJsonResponse(jsonMatch[0])

      if (!result) {
        throw new Error('Failed to parse email generation response')
      }

      return {
        campaign_id: campaign_id || '',
        subject1: result.subject1 || '',
        body1: result.body1 || '',
        subject2: result.subject2 || '',
        body2: result.body2 || '',
        subject3: result.subject3 || '',
        body3: result.body3 || ''
      }
    } catch (error) {
      this.log('error', `Error parsing email response: ${error}`)
      return null
    }
  }

  /**
   * Intelligently merge variables, replacing similar ones and adding new ones
   * @param existingVars Existing variables from metadata
   * @param newVars New variables from email generation
   * @returns Merged variables object
   */
  private mergeVariables(existingVars: Record<string, string>, newVars: Record<string, string>): Record<string, string> {
    const merged = { ...existingVars }

    // Define similarity threshold for content comparison
    const similarityThreshold = 0.8

    for (const [newKey, newValue] of Object.entries(newVars)) {
      let shouldReplace = false

      // Always replace if exact key match
      if (existingVars[newKey] !== undefined) {
        shouldReplace = true
        this.log('debug', `Replacing variable '${newKey}': '${existingVars[newKey]?.substring(0, 50)}...' -> '${newValue?.substring(0, 50)}...'`)
      } else {
        // Check for similar keys (e.g., body1 vs body_1, subject vs subject1)
        for (const existingKey of Object.keys(existingVars)) {
          if (this.areKeySimilar(existingKey, newKey)) {
            // If keys are similar, compare content similarity
            if (this.getContentSimilarity(existingVars[existingKey], newValue) < similarityThreshold) {
              // Content is different enough, replace the old one
              delete merged[existingKey]
              shouldReplace = true
              this.log('debug', `Replacing similar variable '${existingKey}' -> '${newKey}': content differs significantly`)
              break
            } else {
              this.log('debug', `Keeping existing variable '${existingKey}': content is similar to new '${newKey}'`)
            }
          }
        }
      }

      // Add or replace the variable
      if (shouldReplace || !existingVars[newKey]) {
        merged[newKey] = newValue
      }
    }

    return merged
  }

  /**
   * Check if two variable keys are similar (e.g., body1 vs body_1, subject vs subject1)
   */
  private areKeySimilar(key1: string, key2: string): boolean {
    // Normalize keys by removing underscores and numbers
    const normalize = (key: string) => key.toLowerCase().replace(/[_\d]/g, '')
    const base1 = normalize(key1)
    const base2 = normalize(key2)

    // Check if base names are the same
    if (base1 === base2) {
      return true
    }

    // Check for common variations
    const variations: Record<string, string[]> = {
      'subject': ['subj', 'title', 'headline'],
      'body': ['content', 'message', 'text'],
      'email': ['mail', 'emailaddress'],
      'firstname': ['first', 'fname'],
      'lastname': ['last', 'lname'],
      'companyname': ['company', 'org', 'organization']
    }

    for (const [base, alts] of Object.entries(variations)) {
      if ((base === base1 && alts.includes(base2)) || (base === base2 && alts.includes(base1))) {
        return true
      }
    }

    return false
  }

  /**
   * Calculate content similarity between two strings (simple Jaccard similarity)
   */
  private getContentSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0
    if (str1 === str2) return 1

    // Simple word-based similarity
    const words1 = new Set(str1.toLowerCase().split(/\s+/))
    const words2 = new Set(str2.toLowerCase().split(/\s+/))

    const intersection = new Set(Array.from(words1).filter(word => words2.has(word)))
    const union = new Set([...Array.from(words1), ...Array.from(words2)])

    return intersection.size / union.size
  }

  /**
   * Store generated email in messages table with proper structure
   * Prevents duplicate messages and updates metadata with variables
   */
  private async storeGeneratedEmail(contactId: number, email: GeneratedEmail, campaign_id: string): Promise<void> {
    try {
      // Get contact details for subject line
      const contactSql = `
        SELECT c.first_name, c.last_name, c.email, c.title, co.company_name
        FROM contacts c
        LEFT JOIN companies co ON c.company_id = co.company_id
        WHERE c.contact_id = $1
      `
      const contactResult = await this.query(contactSql, [contactId])

      if (contactResult.length === 0) {
        throw new Error(`Contact not found: ${contactId}`)
      }

      const contact = contactResult[0]
      const subject = `DRIP CAMPAIGN FOR ${contact.first_name} ${contact.last_name}`

      // Check if message already exists for this contact with email generation metadata
      const existingMessageSql = `
        SELECT message_id, metadata, thread_id
        FROM messages 
        WHERE to_email = $1 
        AND subject = $2 
        AND metadata->>'email_generation_version' IS NOT NULL
        ORDER BY created_at DESC
        LIMIT 1
      `

      const existingResult = await this.query(existingMessageSql, [contact.email, subject])

      // Prepare new variables - ensure all values are strings
      const newVariables: Record<string, string> = {
        email: String(contact.email || ''),
        job_title: String(contact.title || ''),
        last_name: String(contact.last_name || ''),
        first_name: String(contact.first_name || ''),
        company_name: String(contact.company_name || ''),
        body1: String(email.body1),
        body2: String(email.body2),
        body3: String(email.body3),
        subject1: String(email.subject1),
        subject2: String(email.subject2),
        subject3: String(email.subject3)
      }

      if (existingResult.length > 0) {
        // Update existing message metadata
        const existingMessage = existingResult[0]
        let existingMetadata: any = {}

        try {
          existingMetadata = typeof existingMessage.metadata === 'string'
            ? JSON.parse(existingMessage.metadata)
            : existingMessage.metadata || {}
        } catch (parseError) {
          this.log('warn', `Error parsing existing metadata for message ${existingMessage.message_id}: ${parseError}`)
          existingMetadata = {}
        }

        // Intelligently merge variables - replace similar ones, add new ones
        const existingVariables: Record<string, string> = existingMetadata.variables || {}
        const mergedVariables = this.mergeVariables(existingVariables, newVariables)

        // Update metadata
        const updatedMetadata = {
          ...existingMetadata,
          variables: mergedVariables,
          last_updated: new Date().toISOString(),
          contact_id: contactId,
          email_generation_version: '1.1',
          update_count: (existingMetadata.update_count || 0) + 1
        }

        // Update existing message
        const updateSql = `
          UPDATE messages 
          SET metadata = $1, updated_at = NOW()
          WHERE message_id = $2
        `

        await this.query(updateSql, [JSON.stringify(updatedMetadata), existingMessage.message_id])

        this.log('info', `Updated existing message ${existingMessage.message_id} for contact ${contactId} - merged ${Object.keys(newVariables).length} variables`)

      } else {
        // Create new message
        let threadId: string

        try {
          threadId = await this.createThread(contactId, contact.email as string, subject)
        } catch (threadError) {
          this.log('warn', `Error creating thread, using fallback: ${threadError}`)
          // Fallback: try to find existing thread or use a default
          threadId = `thread_${contactId}_${Date.now()}`
        }

        // Prepare metadata with all the generated email data
        const metadata = {
          variables: newVariables,
          generated_at: new Date().toISOString(),
          contact_id: contactId,
          email_generation_version: '1.1',
          update_count: 1
        }

        // Store in messages table with thread_id
        const sql = `
          INSERT INTO messages (
            thread_id, from_email, to_email, subject, body, metadata, direction,smartlead_campaign_id, role, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())
        `

        await this.query(sql, [
          threadId,
          process.env.SENDER_EMAIL || '<EMAIL>',
          contact.email,
          subject,
          '', // Empty body as requested
          JSON.stringify(metadata),
          'outbound',
          process.env.SMARTLEAD_CAMPAIGN_ID || '2289345',
          'assistant'
        ])

        this.log('info', `Created new generated email message for contact ${contactId} with ${Object.keys(newVariables).length} variables`)
      }

    } catch (error) {
      this.log('error', `Error storing generated email for contact ${contactId}: ${error}`)
      throw error
    }
  }

  /**
   * Create a thread for the email conversation
   */
  private async createThread(contactId: number, toEmail: string, subject: string): Promise<string> {
    try {
      // Get the workspace ID (using the first available workspace)
      const workspaceResult = await this.query('SELECT workspace_id FROM workspaces LIMIT 1')
      const workspaceId = workspaceResult[0]?.workspace_id

      if (!workspaceId) {
        throw new Error('No workspace found')
      }

      // Create thread
      const threadSql = `
        INSERT INTO threads (workspace_id, subject, to_email, contact_id, status, metadata, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        RETURNING thread_id
      `

      const threadMetadata = {
        email_generation: true,
        processor_type: 'EmailGenerationProcessor',
        created_by: 'system'
      }

      const threadResult = await this.query(threadSql, [
        workspaceId,
        subject,
        toEmail,
        contactId,
        'active',
        JSON.stringify(threadMetadata)
      ])

      const threadId = threadResult[0].thread_id as string

      this.log('debug', `Created thread ${threadId} for contact ${contactId}`)
      return threadId

    } catch (error) {
      this.log('error', `Error creating thread for contact ${contactId}: ${error}`)
      throw error
    }
  }

  /**
   * Get current email generation status for a contact
   */
  private async getCurrentEmailGenerationStatus(contactId: number): Promise<string | null> {
    try {
      const sql = `
        SELECT email_generation_status
        FROM contacts
        WHERE contact_id = $1
      `
      const result = await this.query(sql, [contactId])

      if (result.length === 0) {
        return null
      }

      return result[0].email_generation_status as string
    } catch (error) {
      this.log('error', `Error getting email generation status for contact ${contactId}: ${error}`)
      return null
    }
  }

  /**
   * Auto-sync generated email to Smartlead campaign
   */
  private async syncToSmartlead(contactId: number, generatedEmail: GeneratedEmail, contact: UnifiedEntityData, campaign_id: string): Promise<void> {
    try {
      // Validate campaign ID before attempting sync
      if (!campaign_id || campaign_id.trim() === '') {
        this.log('warn', `No campaign ID provided for contact ${contactId}, skipping Smartlead sync`)
        return
      }

      // Check if campaign exists before attempting sync
      let validCampaignId = campaign_id
      const campaignExists = await this.validateCampaignId(campaign_id)
      
      if (!campaignExists) {
        // Try to use the default campaign ID from environment variables
        const defaultCampaignId = process.env.SMARTLEAD_CAMPAIGN_ID
        if (defaultCampaignId && defaultCampaignId !== campaign_id) {
          this.log('info', `Campaign ${campaign_id} not found, trying default campaign ${defaultCampaignId}`)
          const defaultExists = await this.validateCampaignId(defaultCampaignId)
          if (defaultExists) {
            validCampaignId = defaultCampaignId
            this.log('info', `Using default campaign ${defaultCampaignId} for contact ${contactId}`)
          } else {
            this.log('warn', `Both campaign ${campaign_id} and default campaign ${defaultCampaignId} not found, skipping sync for contact ${contactId}`)
            return
          }
        } else {
          this.log('warn', `Campaign ${campaign_id} does not exist in Smartlead, skipping sync for contact ${contactId}`)
          return
        }
      }

      this.log('info', `Auto-syncing generated email to Smartlead campaign ${validCampaignId} for contact ${contactId}`)

      // Prepare the sync data using the first email variant
      const syncData = {
        campaignId: validCampaignId,
        subject: generatedEmail.subject1,
        body: generatedEmail.body1,
        is_html: true,
        custom_fields: {
          // Contact information
          first_name: contact.first_name || '',
          last_name: contact.last_name || '',
          email: contact.email || '',
          company_name: contact.company_name || '',
          job_title: contact.title || '',

          // All generated email variants
          subject1: generatedEmail.subject1,
          subject2: generatedEmail.subject2,
          subject3: generatedEmail.subject3,
          body1: generatedEmail.body1,
          body2: generatedEmail.body2,
          body3: generatedEmail.body3,

          generated_at: new Date().toISOString(),
        }
      }

      // Call the Smartlead sync API
      const apiUrl = `${process.env.API_BASE_URL || 'http://localhost:3030'}/api/smartlead/contacts/${contactId}/sync`

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(syncData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}` }))
        throw new Error(`Smartlead sync failed: ${errorData.error || response.status}`)
      }

      const result = await response.json()

      this.log('info', `Successfully synced to Smartlead - Lead ID: ${result.lead_id || 'N/A'}, Campaign: ${validCampaignId}`)

      // Save processing attempt with Smartlead sync info
      await this.saveProcessingAttempt(
        'contact',
        contactId,
        'smartlead_sync',
        { campaign_id: validCampaignId, lead_id: result.lead_id },
        'smartlead_api',
        0, // No token usage for sync
        true
      )

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error syncing to Smartlead for contact ${contactId}: ${errorMessage}`)

      // Save failed processing attempt
      await this.saveProcessingAttempt(
        'contact',
        contactId,
        'smartlead_sync',
        undefined,
        'smartlead_api',
        0,
        false,
        errorMessage
      )

      // Don't throw the error - email generation should still be considered successful
      // even if Smartlead sync fails
    }
  }

  /**
   * Validate if a campaign ID exists in Smartlead
   */
  private async validateCampaignId(campaignId: string): Promise<boolean> {
    try {
      const apiUrl = `${process.env.API_BASE_URL || 'http://localhost:3030'}/api/smartlead/campaigns/${campaignId}`
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        this.log('debug', `Campaign ${campaignId} exists in Smartlead`)
        return true
      } else if (response.status === 404) {
        this.log('warn', `Campaign ${campaignId} not found in Smartlead`)
        return false
      } else {
        this.log('warn', `Error checking campaign ${campaignId}: HTTP ${response.status}`)
        return false
      }
    } catch (error) {
      this.log('error', `Error validating campaign ID ${campaignId}: ${error}`)
      return false
    }
  }

  /**
   * Update contact email generation status using V2 field names
   */
  private async updateEmailGenerationStatus(contactId: number, status: string, error?: string): Promise<void> {
    const updateSql = `
      UPDATE contacts 
      SET email_generation_status = $2::varchar,
          email_generation_error = $3::text,
          email_generation_date = CASE WHEN $2 = 'completed' THEN NOW() ELSE email_generation_date END,
          updated_at = NOW() 
      WHERE contact_id = $1
    `

    await this.query(updateSql, [contactId, status, error])

    this.log('info', `✓ Contact ${contactId} email generation status updated successfully to ${status}`)
  }
}

