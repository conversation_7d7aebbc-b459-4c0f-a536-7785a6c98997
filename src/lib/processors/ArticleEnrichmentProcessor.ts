import { BaseProcessor } from './BaseProcessor'
import { EntityData, ProcessorOptions } from '../../types/processing'
import { createProcessor<PERSON><PERSON><PERSON><PERSON><PERSON>pter, LLMFactory, LLMMessage } from '../llm'
import { ARTICLE_ENRICHMENT_SYSTEM_PROMPT, ARTICLE_ENRICHMENT_USER_TEMPLATE_FUNCTION } from '../prompts/article-enrichment'
import {
  normalizeEmail,
  extractLinkedInHandle,
  normalizeContactName,
  normalizeCompanyName,
  extractDomain,
  normalizeDomain
} from '../utils/normalizationUtils'
import { DataNormalizationService } from '../services/dataNormalizationService'

interface ArticleData extends EntityData {
  article_id: number
  url: string
  headline: string
  article_body_text: string
  publication_name: string
  processing_state: any
  enriched: boolean
}

interface Property {
  propertyName: string[]
  address: string[]
  state: string[]
  city: string[]
  zipcode: string[]
  region: string[]
  country: string[]
  squareFootage: number
  unitCount: number
  constructionType: string[]
  projectTimeline: string
  jobCreation: number
  subsidyInfo: string
}

interface MarketMetric {
  marketCity: string[]
  marketState: string[]
  marketCountry: string[]
  timePeriod: string[]
  vacancyRate: number
  rentalRate: number
  absorptionRate: number
  transactionVolume: number
  constructionPipeline: number
  newDeliveriesSf: number
  rentalRateTrend: string[]
  capRateAvg: number
  capRateTrend: string[]
  demandTrend: string[]
  commentary: string
  remoteWorkImpact: string
  distressIndicator: boolean
}

interface Transaction {
  dealType: string[]
  dealSize: string
  capRate: string
  pricePerSf: number
  loanType: string[]
  equityType: string[]
  financingType: string[]
  capitalStackNotes: string[]
  capitalPosition: string[] // Capital position in the transaction (e.g., Senior Debt, Mezzanine, Preferred Equity, etc.)
  propertyTypes: string[]
}

interface Entity {
  entityType: string
  entityName: string
  entityRole: string
  entityDescription: string
  confidence: number
  webSearchVerified: boolean
  additionalContext: string
  entityData: any
}

interface ExtractedData {
  publicationDate: string
  author: string
  summary: string
  topic: string
  marketTrendTags: string[]
  isDistressed: boolean
  isRelevant: boolean
  sentiment: string
  sentimentSummary: string
  keyTakeaways: string
  llmTags: string[]
  quotesLlmTags: any[]
  sourceConfidence: number
  extractionNotes: string
  properties: Property[]
  marketMetrics: MarketMetric[]
  transactions: Transaction[]
  entities: Entity[]
}

export class ArticleEnrichmentProcessor extends BaseProcessor {
  private llmProvider: any
  private mappings: Record<string, string[]> = {}

  constructor(options: ProcessorOptions = {}) {
    super('ArticleEnrichment', options)

    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this))

    // Create LLM provider using Perplexity sonar model
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      {
        apiKey: process.env.PERPLEXITY_API_KEY,
      }
    )
  }

  /**
   * Get unprocessed article entries that have been fetched but not enriched
   */
  async getUnprocessedEntities(): Promise<ArticleData[]> {
    const conditions = [
      'fetch_status = \'completed\'',
      'article_body_text IS NOT NULL',
      '(is_bad_url IS NULL OR is_bad_url != true)'
    ]
    const params: unknown[] = []
    let paramIndex = 1

    // Handle multi-ID processing
    if (this.options.multiIds && this.options.multiIds.length > 0) {
      this.log('info', `Processing multiple article IDs: ${this.options.multiIds.join(', ')}`)

      // Create placeholder string for IN clause
      const placeholders = this.options.multiIds.map((_, index) => `$${paramIndex + index}`).join(', ')
      conditions.push(`article_id IN (${placeholders})`)

      // Add all IDs to params
      this.options.multiIds.forEach(id => params.push(id))
      paramIndex += this.options.multiIds.length

      // For multi-ID runs, we process all specified IDs regardless of status
      // Remove any status-based conditions for multi-ID runs to allow reprocessing
      const statusConditionIndex = conditions.findIndex(c => c.includes('extraction_status'))
      if (statusConditionIndex !== -1) {
        conditions.splice(statusConditionIndex, 1)
      }
    } else if (this.options.singleId) {
      conditions.push(`article_id = $${paramIndex}`)
      params.push(this.options.singleId)
      paramIndex++
    } else {
      conditions.push(`extraction_status = 'pending'`)
    }

    const sql = `
      SELECT
        article_id,
        article_url,
        publication_name,
        headline,
        article_body_text,
        extraction_status
      FROM article
      WHERE ${conditions.join(' AND ')}
      ORDER BY created_at DESC
      ${this.options.limit ? `LIMIT ${this.options.limit}` : ''}
    `

    const result = await this.query(sql, params)

    return result.map(row => ({
      id: row.article_id as number,
      article_id: row.article_id as number,
      url: row.article_url as string,
      publication_name: row.publication_name as string || '',
      headline: row.headline as string || '',
      article_body_text: row.article_body_text as string || '',
      processing_state: 'pending' as any,
      enriched: row.extraction_status === 'completed'
    }))
  }

  /**
   * Load mappings from central_mapping table
   */
  private async loadMappings(): Promise<void> {
    try {
      const sql = `
        SELECT type, level_1, value_1, level_2, value_2, level_3, value_3
        FROM central_mapping
        WHERE is_active = true
        ORDER BY type, level_1, value_1
      `
      const result = await this.query(sql)

      this.mappings = {}
      for (const row of result) {
        const type = row.type as string
        const level1 = row.level_1 as string
        const value1 = row.value_1 as string
        const level2 = row.level_2 as string
        const value2 = row.value_2 as string

        // Primary level (type -> value_1)
        if (!this.mappings[type]) {
          this.mappings[type] = []
        }

        if (value1 && !this.mappings[type].includes(value1)) {
          this.mappings[type].push(value1)
        }

        // Secondary level for subcategories (level_2 -> value_2)
        if (level2 && value2) {
          const subcategoryKey = `${type} - ${level2}`
          if (!this.mappings[subcategoryKey]) {
            this.mappings[subcategoryKey] = []
          }

          if (!this.mappings[subcategoryKey].includes(value2)) {
            this.mappings[subcategoryKey].push(value2)
          }
        }
      }

      this.log('info', `Loaded ${Object.keys(this.mappings).length} mapping types`)
    } catch (error) {
      this.log('error', `Failed to load mappings: ${error}`)
      this.mappings = {}
    }
  }

  /**
   * Safely serialize data to JSON for database storage
   */
  private safeJsonStringify(data: any): string {
    try {
      if (data === null || data === undefined) {
        return '[]'
      }

      // Handle arrays
      if (Array.isArray(data)) {
        // Ensure all array elements are serializable
        const cleanArray = data.map(item => {
          if (typeof item === 'object' && item !== null) {
            // For complex objects, extract only the essential properties
            if (item.name !== undefined) {
              return { name: item.name, type: item.type, role: item.role, confidenceScore: item.confidenceScore }
            }
            return item
          }
          return item
        })
        return JSON.stringify(cleanArray)
      }

      // Handle objects
      if (typeof data === 'object') {
        // For complex objects, extract only the essential properties
        if (data.name !== undefined) {
          return JSON.stringify({ name: data.name, type: data.type, role: data.role, confidenceScore: data.confidenceScore })
        }
        return JSON.stringify(data)
      }

      // Handle strings - wrap in array for consistency
      if (typeof data === 'string') {
        return JSON.stringify([data])
      }

      // Handle other primitives
      return JSON.stringify([String(data)])
    } catch (error) {
      this.log('warn', `Failed to serialize JSON data: ${error}, using empty array. Data type: ${typeof data}`)
      return '[]'
    }
  }

  /**
   * Parse and validate date strings from LLM responses
   * Always returns a string in YYYY-MM-DD format or null if invalid.
   */
  private parseDate(dateStr: string | null | undefined): string | null {
    if (!dateStr) return null
    try {
      // Normalize to YYYY-MM-DD
      let normalizedDate = dateStr.trim()
      // If it's just YYYY-MM, add -01 for the day
      if (/^\d{4}-\d{2}$/.test(normalizedDate)) {
        normalizedDate = `${normalizedDate}-01`
      }
      // If it's just YYYY, add -01-01
      if (/^\d{4}$/.test(normalizedDate)) {
        normalizedDate = `${normalizedDate}-01-01`
      }
      // Try to parse the date
      const date = new Date(normalizedDate)
      // Check if it's a valid date
      if (isNaN(date.getTime())) {
        this.log('warn', `Invalid date format: ${dateStr}, using null`)
        return null
      }
      // Return in YYYY-MM-DD format (UTC)
      return date.toISOString().split('T')[0]
    } catch (error) {
      this.log('warn', `Error parsing date "${dateStr}": ${error}, using null`)
      return null
    }
  }

  /**
   * Process a single article entity by enriching it with extracted data
   */
  async processEntity(entity: ArticleData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Processing article enrichment for ID ${entity.article_id}: ${entity.url}`)

      // Mark as running
      await this.setArticleEnrichmentStatus(entity.article_id, 'running')

      // Load mappings if not already loaded
      if (Object.keys(this.mappings).length === 0) {
        await this.loadMappings()
      }

      // Extract text from HTML if needed
      if (!entity.article_body_text) {
        throw new Error('No content available for processing')
      }

      // Generate the prompt using the already extracted title and text
      const prompt = ARTICLE_ENRICHMENT_USER_TEMPLATE_FUNCTION({
        title: entity.headline || 'No title extracted',
        text: entity.article_body_text || 'No content available',
        source: entity.publication_name,
        url: entity.url
      }, this.mappings)

      // console.log('prompt', prompt)

      // Call LLM for extraction
      const messages: LLMMessage[] = [
        { role: 'system', content: ARTICLE_ENRICHMENT_SYSTEM_PROMPT },
        { role: 'user', content: prompt }
      ]

      // Use Perplexity with sonar model for web-enhanced research
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.1,
        maxTokens: 8000,
        model: 'sonar'
      })

      // Parse the response
      const extractedData = await this.parseExtractionResponse(response.content)

      // Store the extracted data in the new article tables
      await this.storeArticleEnrichmentData(entity.article_id, extractedData, entity)

      // Update metadata in article table
      await this.updateArticleMetadata(entity.article_id, extractedData)

      this.log('info', `Successfully processed article enrichment for ID ${entity.article_id}`)

      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error processing article enrichment ${entity.article_id}: ${errorMessage}`)

      // Store error in database
      try {
        await this.storeEnrichmentError(entity.article_id, errorMessage)
      } catch (dbError) {
        this.log('error', `Failed to store error for article ${entity.article_id}: ${dbError}`)
      }

      return { success: false, error: errorMessage }
    }
  }

  /**
   * Parse the LLM response and validate JSON
   */
  private async parseExtractionResponse(response: string): Promise<ExtractedData> {
    try {
      // Clean the response - remove any markdown or extra text
      const cleanResponse = response
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .trim()

      // Parse JSON using the provider's parsing method
      const data = this.parseJsonResponse(cleanResponse)

      if (!data) {
        throw new Error('Failed to parse LLM response as JSON')
      }

      // Validate and structure the data according to our schema
      const validatedData: ExtractedData = {
        publicationDate: data.publicationDate || '',
        author: data.author || '',
        summary: data.summary || '',
        topic: data.topic || '',
        marketTrendTags: Array.isArray(data.marketTrendTags) ? data.marketTrendTags : [],
        isDistressed: data.isDistressed || false,
        isRelevant: data.isRelevant !== undefined ? data.isRelevant : true, // Default to relevant if not specified
        sentiment: data.sentiment || 'Neutral',
        sentimentSummary: data.sentimentSummary || '',
        keyTakeaways: data.keyTakeaways || '',
        llmTags: Array.isArray(data.llmTags) ? data.llmTags : [],
        quotesLlmTags: Array.isArray(data.quotesLlmTags) ? data.quotesLlmTags : [],
        sourceConfidence: data.sourceConfidence || 0,
        extractionNotes: data.extractionNotes || '',
        properties: Array.isArray(data.properties) ? data.properties : [],
        marketMetrics: Array.isArray(data.marketMetrics) ? data.marketMetrics : [],
        transactions: Array.isArray(data.transactions) ? data.transactions : [],
        entities: Array.isArray(data.entities) ? data.entities : []
      }

      return validatedData
    } catch (error) {
      this.log('error', `Failed to parse extraction response: ${error}`)
      this.log('debug', `Response content: ${response}`)
      throw new Error(`JSON parsing failed: ${error}`)
    }
  }


  /*
   * Parse json response with enhanced control character handling
   */
  private parseJsonResponse(response: string): ExtractedData {
    // If LLM provider parsing fails, try our enhanced cleaning approach
    try {
      // console.log('response', response)
      const cleanedResponse = this.enhancedControlCharacterCleaning(response)
      // console.log('cleanedResponse', cleanedResponse)
      const result = JSON.parse(cleanedResponse)
      this.log('info', 'Successfully parsed JSON using enhanced cleaning')
      return result
    } catch (error) {
      this.log('error', `Enhanced parsing also failed: ${error}`)
      throw new Error(`Failed to parse JSON response: ${error}`)
    }
  }

  /*
   * Enhanced control character cleaning for JSON responses
   */
  private enhancedControlCharacterCleaning(content: string): string {
    try {
      this.log('debug', 'Starting enhanced control character cleaning')
      
      let cleaned = content

      // Step 1: Extract JSON from markdown blocks or other wrappers
      const markdownMatch = cleaned.match(/```json\s*([\s\S]*?)\s*```/)
      if (markdownMatch) {
        cleaned = markdownMatch[1]
        this.log('debug', 'Extracted from markdown block')
      } else {
        // Try to extract JSON object
        const jsonMatch = cleaned.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          cleaned = jsonMatch[0]
          this.log('debug', 'Extracted JSON object using regex')
        }
      }

      // Step 2: Comprehensive control character removal
      cleaned = cleaned
        // Remove all control characters except newline, carriage return, and tab
        .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]/g, '')
        // Remove Unicode control characters and formatting characters
        .replace(/[\u200B-\u200D\uFEFF]/g, '')  // Zero-width spaces and BOM
        .replace(/[\u2000-\u200F]/g, ' ')       // Various Unicode spaces -> regular space
        .replace(/[\u2028-\u2029]/g, '\n')      // Line/paragraph separators -> newline
        // Normalize line endings
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')

      // Step 3: Fix common JSON string issues
      cleaned = this.fixJsonStringIssues(cleaned)

      // Step 4: Remove trailing commas
      cleaned = cleaned
        .replace(/,(\s*[}\]])/g, '$1')
        .trim()

      this.log('debug', `Enhanced cleaning completed. Length: ${cleaned.length}`)
      return cleaned

    } catch (error) {
      this.log('error', `Error in enhanced cleaning: ${error}`)
      return content // Return original if cleaning fails
    }
  }

  /*
   * Fix specific JSON string issues that cause parsing failures
   */
  private fixJsonStringIssues(jsonString: string): string {
    try {
      let fixed = jsonString

      // Fix unescaped backslashes in JSON strings
      // Look for backslashes that are not part of valid escape sequences
      fixed = fixed.replace(/\\(?!["\\/bfnrt])/g, '\\\\')

      // Fix unescaped quotes in JSON string values
      // This is tricky - we need to identify quotes within string values vs structural quotes
      let inString = false
      let escaped = false
      let result = ''
      
      for (let i = 0; i < fixed.length; i++) {
        const char = fixed[i]
        const prevChar = i > 0 ? fixed[i - 1] : ''
        
        if (char === '"' && !escaped) {
          if (!inString) {
            // Starting a string
            inString = true
            result += char
          } else {
            // Ending a string - check if this is followed by valid JSON structure
            const nextNonSpace = this.getNextNonSpaceChar(fixed, i)
            if (nextNonSpace && [',', '}', ']', ':'].includes(nextNonSpace)) {
              // This looks like a valid string termination
              inString = false
              result += char
            } else if (nextNonSpace && nextNonSpace === '"') {
              // This might be an unescaped quote within a string
              result += '\\"'
            } else {
              // Default to ending the string
              inString = false
              result += char
            }
          }
        } else if (char === '\\') {
          escaped = !escaped
          result += char
        } else {
          if (char !== '\\') {
            escaped = false
          }
          result += char
        }
      }

      return result

    } catch (error) {
      this.log('warn', `Error fixing JSON string issues: ${error}. Returning original.`)
      return jsonString
    }
  }

  /*
   * Helper to get next non-whitespace character
   */
  private getNextNonSpaceChar(str: string, startIndex: number): string | null {
    for (let i = startIndex + 1; i < str.length; i++) {
      const char = str[i]
      if (!/\s/.test(char)) {
        return char
      }
    }
    return null
  }

  /**
   * Store comprehensive article enrichment data in the related tables
   */
  private async storeArticleEnrichmentData(articleId: number, data: ExtractedData, entity: ArticleData): Promise<void> {
    try {
      // Clear existing enrichment data before storing new data
      await this.clearExistingEnrichmentData(articleId)

      // Update main article table with enrichment data
      await this.updateMainArticleData(articleId, data)

      // Store properties data
      if (data.properties && data.properties.length > 0) {
        for (const property of data.properties) {
          await this.storePropertyData(articleId, property)
        }
        this.log('info', `Stored ${data.properties.length} property records for article ${articleId}`)
      }

      // Store market metrics data
      if (data.marketMetrics && data.marketMetrics.length > 0) {
        for (const metric of data.marketMetrics) {
          await this.storeMarketMetricData(articleId, metric)
        }
        this.log('info', `Stored ${data.marketMetrics.length} market metric records for article ${articleId}`)
      }

      // Store transaction data
      if (data.transactions && data.transactions.length > 0) {
        for (const transaction of data.transactions) {
          await this.storeTransactionData(articleId, transaction)
        }
        this.log('info', `Stored ${data.transactions.length} transaction records for article ${articleId}`)
      }

      // Store entity data
      if (data.entities && data.entities.length > 0) {
        this.log('info', `Processing ${data.entities.length} entities for article ${articleId}`)
        for (const entityData of data.entities) {
          this.log('debug', `Processing entity: ${entityData.entityName} (${entityData.entityType})`)
          await this.storeEntityData(articleId, entityData)
        }
        this.log('info', `Stored ${data.entities.length} entity records for article ${articleId}`)
      }

      this.log('info', `Successfully stored all enrichment data for article ${articleId}`)
    } catch (error) {
      this.log('error', `Error storing article enrichment data for ${articleId}: ${error}`)
      throw error
    }
  }

  /**
   * Update main article table with enrichment data
   */
  private async updateMainArticleData(articleId: number, data: ExtractedData): Promise<void> {
    const sql = `
      UPDATE article
      SET
        publication_date = $1,
        author = $2,
        summary = $3,
        topic = $4,
        market_trend_tags = $5,
        is_distressed = $6,
        is_relevant = $7,
        sentiment = $8,
        sentiment_summary = $9,
        key_takeaways = $10,
        llm_tags = $11,
        quotes_llm_tags = $12,
        source_confidence = $13,
        extraction_notes = $14,
        extraction_status = 'completed',
        extraction_date = NOW(),
        extraction_error = NULL,
        updated_at = NOW()
      WHERE article_id = $15
    `

    const params = [
      data.publicationDate || null, // $1
      data.author || null, // $2
      data.summary || null, // $3
      data.topic || null, // $4
      this.safeJsonStringify(data.marketTrendTags), // $5
      data.isDistressed || false, // $6
      data.isRelevant !== undefined ? data.isRelevant : true, // $7
      data.sentiment || 'Neutral', // $8
      data.sentimentSummary || null, // $9
      data.keyTakeaways || null, // $10
      this.safeJsonStringify(data.llmTags), // $11
      this.safeJsonStringify(data.quotesLlmTags), // $12
      data.sourceConfidence || null, // $13
      data.extractionNotes || null, // $14
      articleId // $15
    ]

    await this.query(sql, params)
  }

  /**
   * Store property data in article_properties table
   */
  private async storePropertyData(articleId: number, property: Property): Promise<void> {
    const sql = `
      INSERT INTO article_properties (
        article_id, property_name, address, state, city, zipcode, region, country,
        square_footage, unit_count, construction_type, project_timeline, job_creation, subsidy_info
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
      )
    `

    const params = [
      articleId, // $1
      Array.isArray(property.propertyName) ? property.propertyName[0] || null : property.propertyName || null, // $2 - Take first property name from array
      Array.isArray(property.address) ? property.address[0] || null : property.address || null, // $3 - Take first address from array
      Array.isArray(property.state) ? property.state[0] || null : property.state || null, // $4 - Take first state from array
      Array.isArray(property.city) ? property.city[0] || null : property.city || null, // $5 - Take first city from array
      Array.isArray(property.zipcode) ? property.zipcode[0] || null : property.zipcode || null, // $6 - Take first zipcode from array
      Array.isArray(property.region) ? property.region[0] || null : property.region || null, // $7 - Take first region from array
      Array.isArray(property.country) ? property.country[0] || 'USA' : property.country || 'USA', // $8 - Take first country from array or default to USA
      property.squareFootage || null, // $9
      property.unitCount || null, // $10
      Array.isArray(property.constructionType) ? property.constructionType[0] || null : property.constructionType || null, // $11 - Take first construction type from array
      property.projectTimeline || null, // $12
      property.jobCreation || null, // $13
      property.subsidyInfo || null // $14
    ]

    await this.query(sql, params)
  }

  /**
   * Store market metric data in article_market_metrics table
   */
  private async storeMarketMetricData(articleId: number, metric: MarketMetric): Promise<void> {
    const sql = `
      INSERT INTO article_market_metrics (
        article_id, market_city, market_state, market_country, time_period,
        vacancy_rate, rental_rate, absorption_rate, transaction_volume,
        construction_pipeline, new_deliveries_sf, rental_rate_trend,
        cap_rate_avg, cap_rate_trend, demand_trend, commentary,
        remote_work_impact, distress_indicator
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
      )
    `

    const params = [
      articleId, // $1
      Array.isArray(metric.marketCity) ? metric.marketCity[0] || null : metric.marketCity || null, // $2 - Take first market city from array
      Array.isArray(metric.marketState) ? metric.marketState[0] || null : metric.marketState || null, // $3 - Take first market state from array
      Array.isArray(metric.marketCountry) ? metric.marketCountry[0] || 'USA' : metric.marketCountry || 'USA', // $4 - Take first market country from array or default to USA
      Array.isArray(metric.timePeriod) ? metric.timePeriod[0] || null : metric.timePeriod || null, // $5 - Take first time period from array
      metric.vacancyRate || null, // $6
      metric.rentalRate || null, // $7
      metric.absorptionRate || null, // $8
      metric.transactionVolume || null, // $9
      metric.constructionPipeline || null, // $10
      metric.newDeliveriesSf || null, // $11
      Array.isArray(metric.rentalRateTrend) ? metric.rentalRateTrend[0] || null : metric.rentalRateTrend || null, // $12 - Take first rental rate trend from array
      metric.capRateAvg || null, // $13
      Array.isArray(metric.capRateTrend) ? metric.capRateTrend[0] || null : metric.capRateTrend || null, // $14 - Take first cap rate trend from array
      Array.isArray(metric.demandTrend) ? metric.demandTrend[0] || null : metric.demandTrend || null, // $15 - Take first demand trend from array
      metric.commentary || null, // $16
      metric.remoteWorkImpact || null, // $17
      metric.distressIndicator || false // $18
    ]

    await this.query(sql, params)
  }

  /**
   * Store transaction data in article_transactions table
   */
  private async storeTransactionData(articleId: number, transaction: Transaction): Promise<void> {
    const sql = `
      INSERT INTO article_transactions (
        article_id, deal_type, deal_size, cap_rate, price_per_sf,
        loan_type, equity_type, financing_type, capital_stack_notes, capital_position, property_types
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
      )
    `

    const params = [
      articleId, // $1
      Array.isArray(transaction.dealType) ? transaction.dealType[0] || null : transaction.dealType || null, // $2 - Take first deal type from array
      transaction.dealSize || null, // $3
      transaction.capRate || null, // $4
      transaction.pricePerSf || null, // $5
      Array.isArray(transaction.loanType) ? transaction.loanType[0] || null : transaction.loanType || null, // $6 - Take first loan type from array
      Array.isArray(transaction.equityType) ? transaction.equityType[0] || null : transaction.equityType || null, // $7 - Take first equity type from array
      this.safeJsonStringify(transaction.financingType), // $8
      this.safeJsonStringify(transaction.capitalStackNotes), // $9
      Array.isArray(transaction.capitalPosition) ? transaction.capitalPosition[0] || null : transaction.capitalPosition || null, // $10 - Take first capital position from array
      this.safeJsonStringify(transaction.propertyTypes) // $11
    ]

    await this.query(sql, params)
  }


  /**
   * Search for existing contact using enhanced pattern-based matching
   */
  private async searchContact(entity: Entity): Promise<number | null> {
    try {
      const entityData = entity.entityData || {}
      const email = entityData.email || entityData.personal_email
      const linkedinUrl = entityData.linkedin_url
      const fullName = entity.entityName
      const companyName = entityData.company

      // Extract LinkedIn handle from URL if provided
      const linkedinHandle = linkedinUrl ? extractLinkedInHandle(linkedinUrl) : null

      // Use the enhanced pattern-based matching service
      const existingContact = await DataNormalizationService.findMatchingContact(
        email || '',
        fullName || '',
        linkedinHandle || '',
        companyName || ''
      )

      if (existingContact) {
        this.log('info', `Found existing contact ${existingContact.contact_id} for entity: ${entity.entityName}`)
        return existingContact.contact_id
      }

      return null
    } catch (error) {
      this.log('error', `Error searching for contact: ${error}`)
      return null
    }
  }

  /**
   * Search for existing company using enhanced pattern-based matching
   */
  private async searchCompany(entity: Entity): Promise<number | null> {
    try {
      const entityData = entity.entityData || {}
      const companyName = entity.entityName
      const website = entityData.website

      // Extract domain from website if provided
      const domain = website ? extractDomain(website) : null

      // Use the enhanced pattern-based matching service
      const existingCompany = await DataNormalizationService.findMatchingCompany(
        companyName || '',
        domain || ''
      )

      if (existingCompany) {
        this.log('info', `Found existing company ${existingCompany.company_id} for entity: ${entity.entityName}`)
        return existingCompany.company_id
      }

      return null
    } catch (error) {
      this.log('error', `Error searching for company: ${error}`)
      return null
    }
  }

  /**
   * Create new contact from entity data
   */
  private async createContact(entity: Entity, articleId: number): Promise<number | null> {
    try {
      const entityData = entity.entityData || {}
      
      // Extract name components
      const fullName = entity.entityName || ''
      const nameParts = fullName.split(' ')
      const firstName = nameParts[0] || ''
      const lastName = nameParts.slice(1).join(' ') || ''

      const sql = `
        INSERT INTO contacts (
          first_name, last_name, full_name, title, email, personal_email, 
          linkedin_url, company_name, contact_city, contact_state, contact_country,
          source, created_at, updated_at, notes
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW(), $13
        )
        RETURNING contact_id
      `

      const params = [
        firstName, // $1
        lastName, // $2
        fullName, // $3
        entityData.title || entity.entityRole || null, // $4
        entityData.email || null, // $5
        entityData.personal_email || null, // $6
        entityData.linkedin_url || null, // $7
        entityData.company || null, // $8
        entityData.location?.split(',')[0] || null, // $9 - city
        entityData.location?.split(',')[1]?.trim() || null, // $10 - state
        entityData.location?.split(',')[2]?.trim() || null, // $11 - country
        `Articles`, // $12 - source
        `Extracted from article ${articleId}: ${entity.entityDescription || ''}` // $13 - notes
      ]

      const result = await this.query(sql, params)
      const contactId = result[0]?.contact_id as number | undefined

      if (contactId) {
        this.log('info', `Created new contact ${contactId} for entity: ${entity.entityName}`)
        
        // Populate normalized data for the new contact
        try {
          await DataNormalizationService.normalizeContact(contactId)
          this.log('info', `Populated normalized data for contact ${contactId}`)
        } catch (normalizationError) {
          this.log('warn', `Failed to populate normalized data for contact ${contactId}: ${normalizationError}`)
        }
      }

      return contactId || null
    } catch (error) {
      this.log('error', `Error creating contact: ${error}`)
      return null
    }
  }

  /**
   * Create new company from entity data
   */
  private async createCompany(entity: Entity, articleId: number): Promise<number | null> {
    try {
      const entityData = entity.entityData || {}

      const sql = `
        INSERT INTO companies (
          company_name, company_website, industry, company_type,
          company_city, company_state, company_country,
          source, created_at, updated_at, summary
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW(), $9
        )
        RETURNING company_id
      `

      const params = [
        entity.entityName || 'Unknown Company', // $1
        entityData.website || null, // $2
        entityData.industry || null, // $3
        entityData.company_type || null, // $4
        entityData.headquarters?.split(',')[0] || null, // $5 - city
        entityData.headquarters?.split(',')[1]?.trim() || null, // $6 - state
        entityData.headquarters?.split(',')[2]?.trim() || 'USA', // $7 - country
        `Articles`, // $8 - source
        `Extracted from article ${articleId}: ${entity.entityDescription || ''}` // $9 - summary
      ]

      const result = await this.query(sql, params)
      const companyId = result[0]?.company_id as number | undefined

      if (companyId) {
        this.log('info', `Created new company ${companyId} for entity: ${entity.entityName}`)
        
        // Populate normalized data for the new company
        try {
          await DataNormalizationService.normalizeCompany(companyId)
          this.log('info', `Populated normalized data for company ${companyId}`)
        } catch (normalizationError) {
          this.log('warn', `Failed to populate normalized data for company ${companyId}: ${normalizationError}`)
        }
      }

      return companyId || null
    } catch (error) {
      this.log('error', `Error creating company: ${error}`)
      return null
    }
  }

  /**
   * Store entity data in articles_entities table with entity_id linking to contacts/companies
   */
  private async storeEntityData(articleId: number, entity: Entity): Promise<void> {
    let entityId: number | null = null

    try {
      // Handle contact entities
      if (entity.entityType === 'contact' && !entity.entityRole.includes('Author') && entity.entityName && entity.entityName.length > 0) {
        // First try to find existing contact
        const contactId = await this.searchContact(entity)
        
        if (contactId) {
          entityId = contactId
          this.log('info', `Found existing contact ${contactId} for entity: ${entity.entityName}`)
        } else if (entity.entityRole !== 'Author') {
          // If not found and not the author, create new contact
          const newContactId = await this.createContact(entity, articleId)
          if (newContactId) {
            entityId = newContactId
          }
        }
      }

      // Handle company entities
      if (entity.entityType === 'company' && entity.entityName && entity.entityName.length > 0) {
        // First try to find existing company
        const companyId = await this.searchCompany(entity)
        
        if (companyId) {
          entityId = companyId
          this.log('info', `Found existing company ${companyId} for entity: ${entity.entityName}`)
        } else {
          // If not found, create new company
          const newCompanyId = await this.createCompany(entity, articleId)
          if (newCompanyId) {
            entityId = newCompanyId
          }
        }
      }

      // Store the entity with the found/created entity_id
      const sql = `
        INSERT INTO articles_entities (
          article_id, entity_id, entity_type, entity_name, entity_role, 
          entity_description, confidence, web_search_verified, additional_context,
          entity_data, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW()
        )
      `
      
      const params = [
        articleId, // $1
        entityId, // $2 - This will be contact_id or company_id depending on entity type
        entity.entityType || 'company', // $3
        entity.entityName || 'Unknown Entity', // $4
        entity.entityRole || 'Unknown Role', // $5
        entity.entityDescription || null, // $6
        entity.confidence || null, // $7
        entity.webSearchVerified || false, // $8
        entity.additionalContext || null, // $9
        this.safeJsonStringify(entity.entityData) // $10
      ]

      await this.query(sql, params)

      if (entityId) {
        this.log('info', `Stored entity ${entity.entityName} with entity_id: ${entityId} (${entity.entityType})`)
      } else {
        this.log('warn', `Stored entity ${entity.entityName} without entity_id (${entity.entityType})`)
      }
    } catch (error) {
      this.log('error', `Error storing entity data: ${error}`)
      throw error
    }
  }

  /**
   * Update article table with summary metadata
   */
  private async updateArticleMetadata(articleId: number, data: ExtractedData): Promise<void> {
    const sql = `
      UPDATE article
      SET
        updated_at = NOW()
      WHERE article_id = $1
    `

    await this.query(sql, [articleId])
  }

  /**
   * Store enrichment error
   */
  private async storeEnrichmentError(articleId: number, error: string): Promise<void> {
    const sql = `
      UPDATE article
      SET extraction_error = $1,
          extraction_status = 'failed',
          updated_at = NOW()
      WHERE article_id = $2
    `

    await this.query(sql, [JSON.stringify({ error, timestamp: new Date().toISOString() }), articleId])
  }

  /**
   * Update article enrichment status
   */
  private async setArticleEnrichmentStatus(articleId: number, status: any, error?: string): Promise<void> {
    // Convert status to string to ensure PostgreSQL compatibility
    const statusStr = String(status)

    if (error) {
      const sql = `
        UPDATE article
        SET
          extraction_status = $1,
          extraction_error = $2,
          updated_at = NOW()
        WHERE article_id = $3
      `

      await this.query(sql, [statusStr, JSON.stringify({ error, timestamp: new Date().toISOString() }), articleId])
    } else {
      const sql = `
        UPDATE article
        SET
          extraction_status = $1,
          updated_at = NOW()
        WHERE article_id = $2
      `

      await this.query(sql, [statusStr, articleId])
    }
  }

  /**
   * Clear existing enrichment data for an article before reprocessing
   */
  private async clearExistingEnrichmentData(articleId: number): Promise<void> {
    try {
      // Check if there's existing data to clear
      const existingDataCount = await this.getExistingEnrichmentDataCount(articleId)
      
      if (existingDataCount.total > 0) {
        this.log('info', `Clearing existing enrichment data for article ${articleId} (${existingDataCount.properties} properties, ${existingDataCount.marketMetrics} market metrics, ${existingDataCount.transactions} transactions, ${existingDataCount.entities} entities)`)

        // Clear data from all related tables
        const clearQueries = [
          'DELETE FROM article_properties WHERE article_id = $1',
          'DELETE FROM article_market_metrics WHERE article_id = $1',
          'DELETE FROM article_transactions WHERE article_id = $1',
          'DELETE FROM articles_entities WHERE article_id = $1'
        ]

        for (const query of clearQueries) {
          await this.query(query, [articleId])
        }

        this.log('info', `Successfully cleared existing enrichment data for article ${articleId}`)
      } else {
        this.log('info', `No existing enrichment data found for article ${articleId}, proceeding with fresh extraction`)
      }
    } catch (error) {
      this.log('error', `Error clearing existing enrichment data for article ${articleId}: ${error}`)
      throw error
    }
  }

  /**
   * Get count of existing enrichment data for an article
   */
  private async getExistingEnrichmentDataCount(articleId: number): Promise<{
    properties: number;
    marketMetrics: number;
    transactions: number;
    entities: number;
    total: number;
  }> {
    try {
      const sql = `
        SELECT
          (SELECT COUNT(*) FROM article_properties WHERE article_id = $1) as properties,
          (SELECT COUNT(*) FROM article_market_metrics WHERE article_id = $1) as market_metrics,
          (SELECT COUNT(*) FROM article_transactions WHERE article_id = $1) as transactions,
          (SELECT COUNT(*) FROM articles_entities WHERE article_id = $1) as entities
      `

      const result = await this.query(sql, [articleId])
      const row = result[0]

      const counts = {
        properties: parseInt(row.properties as string) || 0,
        marketMetrics: parseInt(row.market_metrics as string) || 0,
        transactions: parseInt(row.transactions as string) || 0,
        entities: parseInt(row.entities as string) || 0
      }

      return {
        ...counts,
        total: counts.properties + counts.marketMetrics + counts.transactions + counts.entities
      }
    } catch (error) {
      this.log('warn', `Error getting existing enrichment data count for article ${articleId}: ${error}`)
      return {
        properties: 0,
        marketMetrics: 0,
        transactions: 0,
        entities: 0,
        total: 0
      }
    }
  }

  /**
   * Update entity status after processing
   */
  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    try {
      if (success) {
        await this.setArticleEnrichmentStatus(entityId, 'completed')
        this.log('info', `Article ${entityId} enrichment completed successfully`)
      } else {
        await this.setArticleEnrichmentStatus(entityId, 'failed', error)
        this.log('error', `Article ${entityId} enrichment failed: ${error}`)
      }
    } catch (updateError) {
      this.log('error', `Error updating status for article ${entityId}: ${updateError}`)
    }
  }
}