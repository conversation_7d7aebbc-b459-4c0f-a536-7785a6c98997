import { google, gmail_v1 } from "googleapis";
import { JWT } from "google-auth-library";
import fs from "fs";
import path from "path";
import { pool } from "../db";
import { GaxiosResponse } from "googleapis-common";
import { BaseProcessor } from "./BaseProcessor";

export class GmailEmailFetcherProcessor extends BaseProcessor {
  private serviceAccountPath: string;
  private scopes: string[] = [
    "https://www.googleapis.com/auth/gmail.readonly",
    "https://www.googleapis.com/auth/gmail.modify",
  ];
  private jwtClient: JWT | null = null;

  constructor(options: any = {}) {
    super(options);
    this.serviceAccountPath = options.serviceAccountPath || this.getDefaultServiceAccountPath();
  }

  private getDefaultServiceAccountPath(): string {
    return path.resolve(process.cwd(), "src/service-creds.json");
  }

  async authenticate() {
    // Load service account credentials
    const key = JSON.parse(fs.readFileSync(this.serviceAccountPath, "utf8"));
    // No longer use GMAIL_IMPERSONATE_EMAIL env variable
    // Just set up the JWT client for later impersonation
    this.jwtClient = new google.auth.JWT({
      email: key.client_email,
      key: key.private_key,
      scopes: this.scopes,
    });
  }

  async getGmailClientForUser(userEmail: string): Promise<gmail_v1.Gmail> {
    if (!this.jwtClient) throw new Error("Not authenticated");
    
    // Create a new JWT client for each user to avoid subject contamination
    const key = JSON.parse(fs.readFileSync(this.serviceAccountPath, "utf8"));
    const userJwtClient = new google.auth.JWT({
      email: key.client_email,
      key: key.private_key,
      scopes: this.scopes,
      subject: userEmail  // Set subject during creation
    });
    
    await userJwtClient.authorize();
    
    // Verify the client is properly configured for the correct user
    const testProfile = await google.gmail({ version: "v1", auth: userJwtClient }).users.getProfile({ userId: "me" });
    const testEmail = testProfile.data?.emailAddress;
    
    if (testEmail && testEmail.toLowerCase() !== userEmail.toLowerCase()) {
      console.error(`[ERROR] JWT client verification failed:`);
      console.error(`  - Expected: ${userEmail}`);
      console.error(`  - JWT client returned: ${testEmail}`);
      throw new Error(`JWT client verification failed: Expected ${userEmail}, but got ${testEmail}`);
    }
    
    return google.gmail({ version: "v1", auth: userJwtClient });
  }

  async fetchAccounts(): Promise<string[]> {
    // Fetch a limited number of Gmail accounts to avoid spam/overload
    const limit = 5; // You can adjust this limit as needed
    const result = await pool.query(
      "SELECT email FROM gmail_accounts ORDER BY updated_at ASC LIMIT $1",
      [limit]
    );
    return result.rows.map((row: any) => row.email);
  }

  async getLastMessageHistoryIdForAccount(accountEmail: string): Promise<string | null> {
    // Get the most recent message's history ID for this specific account
    // Only consider messages with valid sent_at timestamps and history IDs
    const result = await pool.query(`
      SELECT 
        gm.metadata->>'historyId' as history_id,
        gm.sent_at
      FROM gmail_messages gm
      JOIN gmail_threads gt ON gm.thread_id = gt.id
      JOIN gmail_accounts ga ON gt.account_id = ga.id
      WHERE ga.email = $1
        AND gm.sent_at IS NOT NULL
        AND gm.metadata->>'historyId' IS NOT NULL
        AND gm.metadata->>'historyId' != ''
      ORDER BY gm.sent_at DESC
      LIMIT 1
    `, [accountEmail]);
    
    if (result.rows.length > 0) {
      const historyId = result.rows[0].history_id;
      return historyId;
    }
    
    return null;
  }

  async validateAccountIsolation(accountEmail: string): Promise<void> {
    // Validate that threads and messages are properly isolated to this account
    const result = await pool.query(`
      SELECT 
        COUNT(DISTINCT ga.email) as account_count,
        COUNT(gt.id) as thread_count,
        COUNT(gm.id) as message_count
      FROM gmail_accounts ga
      LEFT JOIN gmail_threads gt ON gt.account_id = ga.id
      LEFT JOIN gmail_messages gm ON gt.id = gm.thread_id
      WHERE ga.email = $1
    `, [accountEmail]);
    
    const stats = result.rows[0];
    console.log(`[DEBUG] [${accountEmail}] Account isolation stats:`, stats);
    
    // Check if account exists and has proper isolation
    if (stats.account_count === 0) {
      throw new Error(`Account ${accountEmail} not found in database`);
    }
    
    if (stats.account_count > 1) {
      throw new Error(`Account isolation violation: Found ${stats.account_count} accounts for ${accountEmail}`);
    }
    
    console.log(`[DEBUG] [${accountEmail}] Account isolation validation passed`);
  }

  async fetchThreadsForAccount(accountEmail: string, lastHistoryId?: string) {
    const gmail = await this.getGmailClientForUser(accountEmail);
    let threads: gmail_v1.Schema$Thread[] = [];
    let latestHistoryId: string | undefined = undefined;
    
    // First, get the current history ID from Gmail to validate if incremental sync is possible
    const profile = await gmail.users.getProfile({ userId: "me" });
    const currentHistoryId = profile.data?.historyId;
    
    // Validate that the profile email matches the account we're processing
    const profileEmail = profile.data?.emailAddress;
    if (profileEmail && profileEmail.toLowerCase() !== accountEmail.toLowerCase()) {
      console.error(`[ERROR] Account mismatch detected:`);
      console.error(`  - Expected: ${accountEmail}`);
      console.error(`  - Gmail API returned: ${profileEmail}`);
      console.error(`  - This indicates JWT client subject contamination`);
      throw new Error(`Account mismatch: Expected ${accountEmail}, but Gmail API returned ${profileEmail}`);
    }
    
    // Special logging for Marc and Eric accounts - log raw API response
    if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] Gmail API Profile Response:`
      );
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] - Full Profile Object:`,
        JSON.stringify(profile, null, 2)
      );
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] - Profile Data:`,
        JSON.stringify(profile.data, null, 2)
      );
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] - Raw History ID from API: "${currentHistoryId}" (type: ${typeof currentHistoryId})`
      );
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] - Profile Status: ${profile.status}`
      );
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] - Profile Headers:`,
        JSON.stringify(profile.headers, null, 2)
      );
    }
    
    // Check if the stored history ID is recent enough for incremental sync
    if (lastHistoryId && currentHistoryId) {
      const historyIdDiff = parseInt(currentHistoryId) - parseInt(lastHistoryId);
      
      // Log the history ID values for debugging
      console.log(
        `[DEBUG] [fetchThreadsForAccount] History ID check - Current: ${currentHistoryId}, Stored: ${lastHistoryId}, Diff: ${historyIdDiff}`
      );
      
      // Special logging for Marc and Eric accounts
      if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] Detailed History ID Analysis:`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - API Returned History ID: "${currentHistoryId}" (type: ${typeof currentHistoryId})`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Database Stored History ID: "${lastHistoryId}" (type: ${typeof lastHistoryId})`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Parsed Current: ${parseInt(currentHistoryId)}`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Parsed Stored: ${parseInt(lastHistoryId)}`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Calculated Difference: ${historyIdDiff}`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Is Valid for Incremental Sync: ${historyIdDiff > 0 && historyIdDiff <= 10000}`
        );
      }
      
      if (historyIdDiff <= 0 || historyIdDiff > 10000) {
        // History ID is invalid, too old, or newer than current (negative diff)
        console.log(
          `[DEBUG] [fetchThreadsForAccount] History ID difference too large (${historyIdDiff}) or invalid, doing full sync`
        );
        
        // If the difference is negative, the stored history ID is newer than current
        // This shouldn't happen and indicates data corruption or account reset
        if (historyIdDiff < 0) {
          console.warn(
            `[WARN] [fetchThreadsForAccount] Stored history ID (${lastHistoryId}) is newer than current (${currentHistoryId}). This may indicate account reset or data corruption.`
          );
          
          // Reset the invalid history ID in the database
          try {
            await pool.query(
              `UPDATE gmail_accounts SET last_history_id = NULL, updated_at = NOW() WHERE email = $1`,
              [accountEmail]
            );
            console.log(
              `[INFO] [fetchThreadsForAccount] Reset invalid history ID for account ${accountEmail}`
            );
          } catch (dbError) {
            console.error(
              `[ERROR] [fetchThreadsForAccount] Failed to reset invalid history ID for ${accountEmail}:`,
              dbError
            );
          }
        }
        
        lastHistoryId = undefined; // Force full sync
      }
    }
    
    if (lastHistoryId) {
      // Incremental sync: fetch history since lastHistoryId
      let nextPageToken: string | undefined = undefined;
      try {
        do {
          const res: any = await gmail.users.history.list({
            userId: "me",
            startHistoryId: lastHistoryId,
            historyTypes: ["messageAdded"],
            pageToken: nextPageToken,
          });
          
          // Special logging for Marc and Eric accounts - log history API response
          if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
            console.log(
              `[SPECIAL_DEBUG] [${accountEmail}] Gmail History API Response:`
            );
            console.log(
              `[SPECIAL_DEBUG] [${accountEmail}] - History Records Count: ${res.data?.history?.length || 0}`
            );
            console.log(
              `[SPECIAL_DEBUG] [${accountEmail}] - Next Page Token: "${res.data?.nextPageToken || 'none'}"`
            );
            console.log(
              `[SPECIAL_DEBUG] [${accountEmail}] - Response History ID: "${res.data?.historyId || 'none'}"`
            );
          }
          if (res.data && res.data.history) {
            for (const historyRecord of res.data.history) {
              if (historyRecord.messagesAdded) {
                for (const msgAdded of historyRecord.messagesAdded) {
                  if (msgAdded.message?.threadId) {
                    // Only add unique threadIds
                    if (
                      !threads.find((t) => t.id === msgAdded.message!.threadId)
                    ) {
                      threads.push({
                        id: msgAdded.message!.threadId,
                      } as gmail_v1.Schema$Thread);
                    }
                  }
                }
              }
            }
          }
          nextPageToken = res.data?.nextPageToken || undefined;
          if (res.data?.historyId) {
            latestHistoryId = res.data.historyId;
          }
        } while (nextPageToken);
      } catch (err: any) {
        if (err.code === 404 || err.status === 404) {
          // History ID too old, do a full sync and reset last_history_id
          console.warn(
            `[GmailEmailFetcherProcessor] HistoryId ${lastHistoryId} not found for ${accountEmail}, doing full sync.`
          );
          let nextPageToken: string | undefined = undefined;
          do {
            console.log(
              `[DEBUG] [fetchThreadsForAccount] (full sync fallback) Fetching threads page, nextPageToken:`,
              nextPageToken
            );
            const res: any = await gmail.users.threads.list({
              userId: "me",
              maxResults: 50,
              pageToken: nextPageToken,
            });
            
            // Special logging for Marc and Eric accounts - log fallback threads API response
            if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
              console.log(
                `[SPECIAL_DEBUG] [${accountEmail}] Gmail Fallback Threads API Response:`
              );
              console.log(
                `[SPECIAL_DEBUG] [${accountEmail}] - Fallback Threads Count: ${res.data?.threads?.length || 0}`
              );
              console.log(
                `[SPECIAL_DEBUG] [${accountEmail}] - Fallback Next Page Token: "${res.data?.nextPageToken || 'none'}"`
              );
            }
            if (res.data && res.data.threads) {
              threads = threads.concat(res.data.threads);
            }
            nextPageToken = res.data?.nextPageToken || undefined;
          } while (nextPageToken);
          // After full sync, use the current historyId we already fetched
          latestHistoryId = currentHistoryId || undefined;
        } else {
          throw err;
        }
      }
    } else {
      // Full sync: fetch all threads
      let nextPageToken: string | undefined = undefined;
      do {
        const res: any = await gmail.users.threads.list({
          userId: "me",
          maxResults: 50,
          pageToken: nextPageToken,
        });
        
        // Special logging for Marc and Eric accounts - log threads API response
        if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] Gmail Threads API Response:`
          );
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] - Threads Count: ${res.data?.threads?.length || 0}`
          );
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] - Next Page Token: "${res.data?.nextPageToken || 'none'}"`
          );
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] - Response Status: ${res.status}`
          );
        }
        if (res.data && res.data.threads) {
          threads = threads.concat(res.data.threads);
        }
        nextPageToken = res.data?.nextPageToken || undefined;
      } while (nextPageToken);
      // After full sync, use the current historyId we already fetched
      latestHistoryId = currentHistoryId || undefined;
    }
    return { threads, latestHistoryId };
  }

  async fetchMessagesForThread(accountEmail: string, threadId: string) {
    // Use Gmail API to fetch all messages in a thread
    const gmail = await this.getGmailClientForUser(accountEmail);
    const res = await gmail.users.threads.get({
      userId: "me",
      id: threadId,
      format: "full",
    });
    
    // Special logging for Marc and Eric accounts - log messages API response
    if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] Gmail Messages API Response for Thread ${threadId}:`
      );
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] - Messages Count: ${res.data?.messages?.length || 0}`
      );
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] - Thread ID: "${res.data?.id || 'none'}"`
      );
      console.log(
        `[SPECIAL_DEBUG] [${accountEmail}] - Response Status: ${res.status}`
      );
    }
    
    return res.data.messages || [];
  }

  async processAndStoreThread(
    thread: any,
    messages: any[],
    accountEmail: string
  ) {
    // 1. Get account ID (should already exist from processEntity)
    const accountResult = await pool.query(
      `SELECT id FROM gmail_accounts WHERE email = $1`,
      [accountEmail]
    );
    
    if (accountResult.rows.length === 0) {
      throw new Error(`Account ${accountEmail} not found in database`);
    }
    
    const accountId = accountResult.rows[0].id;

    // 2. Upsert thread by provider_thread_id and account_id
    const providerThreadId = thread.id;
    const subject =
      messages[0]?.payload?.headers?.find((h: any) => h.name === "Subject")
        ?.value || null;
    const metadata = JSON.stringify(thread);
    const now = new Date();
    const threadResult = await pool.query(
      `INSERT INTO gmail_threads (provider_thread_id, account_id, subject, metadata, created_at, updated_at)
       VALUES ($1, $2, $3, $4::jsonb, $5, $6)
       ON CONFLICT (provider_thread_id, account_id) DO UPDATE SET subject = EXCLUDED.subject, metadata = EXCLUDED.metadata, updated_at = EXCLUDED.updated_at
       RETURNING id`,
      [providerThreadId, accountId, subject, metadata, now, now]
    );
    const internalThreadId = threadResult.rows[0].id;

    // 3. Insert only new messages (skip if already exists by gmail_message_id)
    for (const msg of messages) {
      const gmailMessageId = msg.id;
      const headers = msg.payload?.headers || [];
      // Extract only email addresses for sender and recipients
      const fromHeader = headers.find((h: any) => h.name === "From")?.value;
      const tosHeader = headers
        .filter((h: any) => h.name === "To")
        .map((h: any) => h.value);
      // Use regex to extract email addresses
      const extractEmail = (str: string) => {
        const match =
          str && str.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        return match ? match[0] : null;
      };
      const from = extractEmail(fromHeader);
      const tos = tosHeader.map(extractEmail).filter(Boolean);
      const subject = headers.find((h: any) => h.name === "Subject")?.value;
      const sentAt = headers.find((h: any) => h.name === "Date")?.value
        ? new Date(headers.find((h: any) => h.name === "Date").value)
        : null;
      let body = "";
      if (msg.payload?.body?.data) {
        body = Buffer.from(msg.payload.body.data, "base64").toString("utf8");
      } else if (msg.payload?.parts) {
        // Try to find the first text/plain or text/html part
        const part = msg.payload.parts.find(
          (p: any) => p.mimeType === "text/plain" || p.mimeType === "text/html"
        );
        if (part?.body?.data) {
          body = Buffer.from(part.body.data, "base64").toString("utf8");
        }
      }
      // Store all metadata (including headers and history ID)
      const messageMetadata = JSON.stringify({
        ...msg,
        headers,
        historyId: msg.historyId, // Store the history ID from the message
        accountEmail: accountEmail, // Ensure account association
      });
      await pool.query(
        `INSERT INTO gmail_messages (thread_id, gmail_message_id, sender, recipients, sent_at, subject, body, raw, metadata, created_at, account_id)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8::jsonb, $9::jsonb, $10, $11)
         ON CONFLICT (gmail_message_id) DO NOTHING`,
        [
          internalThreadId,
          gmailMessageId,
          from,
          tos,
          sentAt,
          subject,
          body,
          JSON.stringify(msg),
          messageMetadata,
          now,
          accountId,
        ]
      );
    }
    // Optionally: update last_history_id after processing (if available in thread/messages)
    // You may want to update this in a higher-level sync function after all threads/messages are processed.
  }

  // Implement BaseProcessor abstract methods
  async getUnprocessedEntities(): Promise<any[]> {
    // For Gmail processor, we get accounts to process
    const accounts = await this.fetchAccounts();
    return accounts.map(email => ({ id: email, data: email }));
  }

  async processEntity(entity: any): Promise<{ success: boolean; error?: string }> {
    const accountEmail = entity.data || entity.id;
    this.log("info", `Processing Gmail account: ${accountEmail}`);
    
    try {
      // Clear any previous JWT client state to ensure clean processing
      if (this.jwtClient) {
        this.jwtClient.subject = null;
      }
      // Ensure account exists in database
      const accountResult = await pool.query(
        `INSERT INTO gmail_accounts (email, created_at, updated_at)
         VALUES ($1, NOW(), NOW())
         ON CONFLICT (email) DO UPDATE SET updated_at = NOW()
         RETURNING id, last_history_id`,
        [accountEmail]
      );
      const accountId = accountResult.rows[0].id;
      const lastHistoryId = accountResult.rows[0].last_history_id;
      
      this.log("info", `Account ${accountEmail} (ID: ${accountId}) last_history_id: ${lastHistoryId}`);
      
      // Try to get a more recent history ID from the last message
      const lastMessageHistoryId = await this.getLastMessageHistoryIdForAccount(accountEmail);
      const effectiveHistoryId = lastMessageHistoryId || lastHistoryId;
      
      this.log("info", `Using effective history ID for ${accountEmail}: ${effectiveHistoryId}`);
      
      // Validate account isolation before processing
      await this.validateAccountIsolation(accountEmail);
      
      // Special logging for Marc and Eric accounts - log database query results
      if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] Database Query Results:`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Query: SELECT last_history_id FROM gmail_accounts WHERE email = $1`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Query Parameter: "${accountEmail}"`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Rows Returned: ${accountResult.rows.length}`
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Raw Row Data:`,
          JSON.stringify(accountResult.rows, null, 2)
        );
        console.log(
          `[SPECIAL_DEBUG] [${accountEmail}] - Extracted last_history_id: "${lastHistoryId}" (type: ${typeof lastHistoryId})`
        );
      }
      
      // 1. Fetch threads for this account (incremental if possible)
      const { threads, latestHistoryId } = await this.fetchThreadsForAccount(
        accountEmail,
        effectiveHistoryId
      );
      this.log("info", `Found ${threads.length} threads for account: ${accountEmail}`);
      
      for (const thread of threads) {
        try {
          // 2. Fetch messages for this thread
          const messages = await this.fetchMessagesForThread(
            accountEmail,
            thread.id!
          );
          this.log("debug", `Thread ${thread.id}: fetched ${messages.length} messages`);
          
          // 3. Process and store in DB
          await this.processAndStoreThread(thread, messages, accountEmail);
          this.log("debug", `Thread ${thread.id}: processed and stored`);
        } catch (err) {
          this.log("error", `Error processing thread ${thread.id} for account ${accountEmail}: ${err}`);
        }
      }
      
      // 4. Update last_history_id for this specific account only
      if (latestHistoryId) {
        // Special logging for Marc and Eric accounts - log history ID update
        if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] Updating History ID in Database:`
          );
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] - Old History ID: "${lastHistoryId}"`
          );
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] - New History ID: "${latestHistoryId}"`
          );
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] - Update Query: UPDATE gmail_accounts SET last_history_id = $1, updated_at = NOW() WHERE email = $2`
          );
        }
        
        await pool.query(
          `UPDATE gmail_accounts SET last_history_id = $1, updated_at = NOW() WHERE email = $2`,
          [latestHistoryId, accountEmail]
        );
        
        this.log("info", `Updated last_history_id for ${accountEmail} to ${latestHistoryId}`);
        
        if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
          console.log(
            `[SPECIAL_DEBUG] [${accountEmail}] - History ID update completed successfully`
          );
        }
      } else if (!effectiveHistoryId) {
        // If this was a full sync, fetch the current historyId and update
        const gmail = await this.getGmailClientForUser(accountEmail);
        const profile = await gmail.users.getProfile({ userId: "me" });
        if (profile.data && profile.data.historyId) {
          // Special logging for Marc and Eric accounts - log initial history ID set
          if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
            console.log(
              `[SPECIAL_DEBUG] [${accountEmail}] Setting Initial History ID:`
            );
            console.log(
              `[SPECIAL_DEBUG] [${accountEmail}] - Previous History ID: "${effectiveHistoryId}" (was null/undefined)`
            );
            console.log(
              `[SPECIAL_DEBUG] [${accountEmail}] - New History ID from API: "${profile.data.historyId}"`
            );
          }
          
          await pool.query(
            `UPDATE gmail_accounts SET last_history_id = $1, updated_at = NOW() WHERE email = $2`,
            [profile.data.historyId, accountEmail]
          );
          this.log("info", `Set initial last_history_id for ${accountEmail} to ${profile.data.historyId}`);
          
          if (accountEmail.toLowerCase().includes('marc') || accountEmail.toLowerCase().includes('eric')) {
            console.log(
              `[SPECIAL_DEBUG] [${accountEmail}] - Initial history ID set completed successfully`
            );
          }
        }
      }
      
      return { success: true };
    } catch (error) {
      this.log("error", `Error processing account ${accountEmail}: ${error}`);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    // For Gmail processor, entityId is actually the account email
    const accountEmail = entityId.toString();
    const now = new Date();
    
    if (success) {
      // Update the account's updated_at timestamp to indicate successful sync
      await pool.query(
        `UPDATE gmail_accounts SET updated_at = $1 WHERE email = $2`,
        [now, accountEmail]
      );
    } else {
      // Update the account's updated_at timestamp and log the error
      await pool.query(
        `UPDATE gmail_accounts SET updated_at = $1 WHERE email = $2`,
        [now, accountEmail]
      );
      // Log the error (we could store this in metadata if needed)
      console.error(`Gmail sync failed for ${accountEmail}: ${error || "Unknown error"}`);
    }
  }

  // Main process method that replaces run()
  async process(): Promise<any> {
    await this.authenticate();
    if (!this.jwtClient) {
      this.log("warn", "No JWT client available. Aborting process.");
      return { success: false, error: "No JWT client available" };
    }
    
    this.log("info", "Starting Gmail email processing");
    const result = await super.process();
    this.log("info", "Gmail email processing completed");
    return result;
  }
}
