import { pool } from '../db'
import Bottleneck from 'bottleneck'

export type LogLevel = 'debug' | 'info' | 'warn' | 'error'
import { EntityData, ProcessorOptions, ProcessorResult, UnifiedEntityData } from '../../types/processing'

// Bottleneck configuration interface for processor-wise control
export interface BottleneckConfig {
  // Rate limiting
  maxConcurrent?: number           // Max concurrent jobs
  minTime?: number                 // Minimum time between job starts (ms)
  highWater?: number              // Queue high water mark
  strategy?: Bottleneck.Strategy   // Queue overflow strategy
  
  // Retry configuration
  retryAttempts?: number          // Number of retry attempts
  retryDelayBase?: number         // Base delay for exponential backoff (ms)
  retryDelayMax?: number          // Maximum retry delay (ms)
  
  // Job priorities
  defaultPriority?: number        // Default job priority (lower = higher priority)
  
  // Timeouts
  timeout?: number                // Job timeout (ms)
  
  // Monitoring
  enableJobMetrics?: boolean      // Enable job execution metrics
}


export abstract class BaseProcessor {
  protected pool: typeof pool
  protected options: ProcessorOptions
  protected bottleneck: Bottleneck
  protected bottleneckConfig: BottleneckConfig

  protected name: string
  private logLevel: LogLevel
  private jobMetrics: Map<string, { startTime: number; endTime?: number; success?: boolean; error?: string }>

  constructor(name: string, options: ProcessorOptions = {}) {
    this.name = name
    this.pool = pool
    this.options = {
      ...options
    }
    
    // Initialize job metrics
    this.jobMetrics = new Map()
    
    // Set log level based on environment
    const envLogLevel = process.env.LOG_LEVEL as LogLevel
    const isProduction = process.env.NODE_ENV === 'production'
    
    // In production, default to 'info' unless explicitly set to debug
    // In development, default to 'debug'
    // Debug logs show timestamps, other levels don't
    const defaultLogLevel: LogLevel = isProduction ? 'info' : 'debug'
    this.logLevel = ['debug', 'info', 'warn', 'error'].includes(envLogLevel) ? envLogLevel : defaultLogLevel
    
    // this.log('info', `BaseProcessor constructor called with options: ${JSON.stringify(options)}`)
    
    // Initialize Bottleneck configuration with defaults
    this.bottleneckConfig = this.getDefaultBottleneckConfig()
    
    // Allow processor-specific configuration override
    if (options.bottleneckConfig) {
      this.bottleneckConfig = { ...this.bottleneckConfig, ...options.bottleneckConfig }
    }
    
    // Create Bottleneck instance with comprehensive configuration
    this.bottleneck = new Bottleneck({
      maxConcurrent: this.bottleneckConfig.maxConcurrent,
      minTime: this.bottleneckConfig.minTime,
      highWater: this.bottleneckConfig.highWater,
      strategy: this.bottleneckConfig.strategy,
      // Add timeout and retry configuration to prevent job drops
      timeout: this.bottleneckConfig.timeout,
      retryDelayBase: this.bottleneckConfig.retryDelayBase,
      retryDelayMax: this.bottleneckConfig.retryDelayMax
    })
    
    // Set up Bottleneck event listeners for monitoring
    this.setupBottleneckMonitoring()
    
    // this.log('info', `Initialized ${this.name} with Bottleneck config: ${JSON.stringify(this.bottleneckConfig)}`)
  }

  protected async query(sql: string, params: unknown[] = []): Promise<Record<string, unknown>[]> {
    const client = await this.pool.connect()
    try {
      const result = await client.query(sql, params)
      return result.rows
    } finally {
      client.release()
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    }
    return levels[level] >= levels[this.logLevel]
  }

  protected log(level: LogLevel, message: string): void {
    // Only log if the level meets or exceeds the configured log level
    if (!this.shouldLog(level)) {
      return
    }

    // Show timestamp only for debug logs
    const timestamp = level === 'debug' ? `[${new Date().toISOString()}] ` : ''
    console.log(`${timestamp}[${this.name}] [${level.toUpperCase()}] ${message}`)
  }

  /**
   * Get current log level for debugging purposes
   */
  protected getLogLevel(): LogLevel {
    return this.logLevel
  }

  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * @deprecated Use bottleneck.schedule() instead for better rate limiting and concurrency control
   */
  protected async rateLimitedCall<T>(
    fn: () => Promise<T>,
    delayMs: number
  ): Promise<T> {
    this.log('warn', 'rateLimitedCall is deprecated, consider using bottleneck.schedule() instead')
    await this.sleep(delayMs)
    return await fn()
  }

  /**
   * Get default Bottleneck configuration
   * Can be overridden by processors for specific needs
   */
  protected getDefaultBottleneckConfig(): BottleneckConfig {
    return {
      maxConcurrent: parseInt(process.env.PROCESSOR_MAX_CONCURRENT || '10'),
      minTime: parseInt(process.env.PROCESSOR_MIN_TIME || '100'),
      highWater: parseInt(process.env.PROCESSOR_HIGH_WATER || '1000'), // Increased from 50 to handle large queues
      strategy: Bottleneck.strategy.OVERFLOW,  // Changed from LEAK to OVERFLOW for better queue management
      retryAttempts: parseInt(process.env.PROCESSOR_RETRY_ATTEMPTS || '3'),
      retryDelayBase: parseInt(process.env.PROCESSOR_RETRY_DELAY_BASE || '1000'),
      retryDelayMax: parseInt(process.env.PROCESSOR_RETRY_DELAY_MAX || '30000'),
      defaultPriority: 5,
      timeout: parseInt(process.env.PROCESSOR_JOB_TIMEOUT || '180000'), // Reduced to 3 minutes to prevent timeouts
      enableJobMetrics: process.env.PROCESSOR_ENABLE_METRICS === 'true'
    }
  }

  /**
   * Set up Bottleneck monitoring and event listeners
   */
  private setupBottleneckMonitoring(): void {
    if (!this.bottleneckConfig.enableJobMetrics) {
      return
    }

    this.bottleneck.on('message', (message) => {
      this.log('debug', `Bottleneck: ${message}`)
    })

    this.bottleneck.on('failed', (error, jobInfo) => {
      this.log('error', `Job failed: ${error.message}. Job info: ${JSON.stringify(jobInfo)}`)
      
      if (jobInfo.options.id) {
        const metric = this.jobMetrics.get(jobInfo.options.id)
        if (metric) {
          metric.endTime = Date.now()
          metric.success = false
          metric.error = error.message
        }
      }
    })

    this.bottleneck.on('retry', (error: any, jobInfo: any) => {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('warn', `Retrying job due to error: ${errorMessage}. Job info: ${JSON.stringify(jobInfo)}`)
    })

    this.bottleneck.on('done', (jobInfo) => {
      if (jobInfo.options.id) {
        const metric = this.jobMetrics.get(jobInfo.options.id)
        if (metric && !metric.endTime) {
          metric.endTime = Date.now()
          metric.success = true
        }
      }
    })

    this.bottleneck.on('idle', () => {
      this.log('debug', `${this.name} Bottleneck queue is idle`)
    })

    this.bottleneck.on('empty', () => {
      this.log('debug', `${this.name} Bottleneck queue is empty`)
    })
  }

  /**
   * Execute a job with Bottleneck rate limiting and retry logic
   */
  protected async executeWithBottleneck<T>(
    jobFn: () => Promise<T>,
    jobId: string,
    priority?: number,
    retryOnFailure: boolean = true
  ): Promise<T> {
    const startTime = Date.now()
    
    if (this.bottleneckConfig.enableJobMetrics) {
      this.jobMetrics.set(jobId, { startTime })
    }

    const executeJob = async (): Promise<T> => {
      try {
        const result = await this.bottleneck.schedule(
          { 
            priority: priority ?? this.bottleneckConfig.defaultPriority, 
            id: jobId 
          },
          jobFn
        )
        
        this.log('debug', `Job ${jobId} completed successfully in ${Date.now() - startTime}ms`)
        return result
      } catch (error) {
        this.log('error', `Job ${jobId} failed: ${error}`)
        throw error
      }
    }

    if (!retryOnFailure || !this.bottleneckConfig.retryAttempts) {
      return await executeJob()
    }

    // Implement exponential backoff retry logic
    let lastError: Error = new Error('Unknown error')
    
    for (let attempt = 0; attempt <= (this.bottleneckConfig.retryAttempts || 0); attempt++) {
      try {
        return await executeJob()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        
        if (attempt === (this.bottleneckConfig.retryAttempts || 0)) {
          this.log('error', `Job ${jobId} failed after ${attempt + 1} attempts: ${lastError.message}`)
          break
        }

        const delay = Math.min(
          (this.bottleneckConfig.retryDelayBase || 1000) * Math.pow(2, attempt),
          this.bottleneckConfig.retryDelayMax || 30000
        )
        
        this.log('warn', `Job ${jobId} failed on attempt ${attempt + 1}, retrying in ${delay}ms: ${lastError.message}`)
        await this.sleep(delay)
      }
    }

    throw lastError
  }

  /**
   * Get job execution metrics
   */
  protected getJobMetrics(): { [jobId: string]: { duration?: number; success?: boolean; error?: string } } {
    const metrics: { [jobId: string]: { duration?: number; success?: boolean; error?: string } } = {}
    
    Array.from(this.jobMetrics.entries()).forEach(([jobId, metric]) => {
      metrics[jobId] = {
        success: metric.success,
        error: metric.error
      }
      
      if (metric.endTime) {
        metrics[jobId].duration = metric.endTime - metric.startTime
      }
    })
    
    return metrics
  }

  /**
   * Clear job metrics (useful for long-running processors)
   */
  protected clearJobMetrics(): void {
    this.jobMetrics.clear()
    this.log('debug', 'Cleared job metrics')
  }

  /**
   * Cleanup Bottleneck instance and resources
   * Should be called when processor is no longer needed
   */
  protected async cleanup(): Promise<void> {
    try {
      // Stop accepting new jobs and wait for existing jobs to complete
      await this.bottleneck.stop()
      this.log('info', `${this.name} processor cleanup completed`)
    } catch (error) {
      this.log('error', `Error during processor cleanup: ${error}`)
    }
  }

  /**
   * Get current Bottleneck status and metrics
   */
  protected getBottleneckStatus(): {
    running: number
    done: number
    queued: number
    empty: boolean
  } {
    // Note: Some Bottleneck methods may return promises in newer versions
    // For now, using basic status that should be synchronous
    try {
      return {
        running: (this.bottleneck as any)._running || 0,
        done: (this.bottleneck as any)._done || 0,
        queued: (this.bottleneck as any)._queued?.length || 0,
        empty: (this.bottleneck as any)._queued?.length === 0
      }
    } catch (error) {
      this.log('warn', `Could not get bottleneck status: ${error}`)
      return {
        running: 0,
        done: 0,
        queued: 0,
        empty: true
      }
    }
  }

  /**
   * Check if processor should force run based on search params filters
   * This indicates that the processor was triggered with specific filters
   * and should process entities regardless of their current status
   */
  protected shouldForceRun(): boolean {
    return !!(this.options.filters && Object.keys(this.options.filters).length > 0)
  }

  /**
   * Get the search params filters that triggered force run
   */
  protected getForceRunFilters(): Record<string, any> | null {
    return this.options.filters && Object.keys(this.options.filters).length > 0 ? this.options.filters : null
  }

  // Helper method to add date range filter condition
  protected addDateRangeFilter(conditions: string[], params: unknown[], paramIndex: number): number {
    if (this.options.filters?.dateRange && this.options.filters.dateRange !== 'all') {
      const dateRange = this.options.filters.dateRange

      switch (dateRange) {
        case 'today':
          conditions.push(`created_at >= CURRENT_DATE`)
          break
        case 'week':
          conditions.push(`created_at >= CURRENT_DATE - INTERVAL '7 days'`)
          break
        case 'month':
          conditions.push(`created_at >= CURRENT_DATE - INTERVAL '30 days'`)
          break
        case 'quarter':
          conditions.push(`created_at >= CURRENT_DATE - INTERVAL '90 days'`)
          break
      }
    }

    return paramIndex
  }

  // Abstract methods that must be implemented by concrete processors
  abstract getUnprocessedEntities(): Promise<EntityData[]>
  abstract processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }>
  abstract updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void>

  // Main processing loop with proper batching and Bottleneck integration
  async process(): Promise<ProcessorResult> {
    const startTime = Date.now()
    
    // Log force run status if applicable
    if (this.shouldForceRun()) {
      this.log('info', `Starting ${this.name} processing with FORCE RUN enabled due to search params filters: ${JSON.stringify(this.getForceRunFilters())}`)
    } else {
      this.log('info', `Starting ${this.name} processing with Bottleneck batching...`)
    }

    const entities = await this.getUnprocessedEntities()
    this.log('info', `Found ${entities.length} entities to process`)

    const results = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [] as string[]
    }

    if (entities.length === 0) {
      this.log('info', 'No entities to process')
      return results
    }

    // Clear previous metrics
    if (this.bottleneckConfig.enableJobMetrics) {
      this.clearJobMetrics()
    }

    // Get batch configuration - use smaller batches for better control
    const batchSize = this.options.batchSize || Math.max(10, Math.min(100, Math.ceil(entities.length / 10))) // Increased max from 50 to 100 for better performance
    const batchDelay = this.options.batchDelay || 100  // 100ms between batches (reduced from 500ms for faster processing)
    const totalBatches = Math.ceil(entities.length / batchSize)

    this.log('info', `Processing ${entities.length} entities in ${totalBatches} batches of ~${batchSize} (max concurrency: ${this.bottleneckConfig.maxConcurrent})`)

    // Process entities in proper batches
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const batchStart = batchIndex * batchSize
      const batchEnd = Math.min(batchStart + batchSize, entities.length)
      const batch = entities.slice(batchStart, batchEnd)
      const currentBatch = batchIndex + 1
      
      this.log('info', `Processing batch ${currentBatch}/${totalBatches} - ${batch.length} entities`)

      // Create processing jobs for current batch only
      const batchJobs = batch.map((entity, indexInBatch) => {
        const globalIndex = batchStart + indexInBatch
        const jobId = `${this.name}-batch${currentBatch}-entity-${entity.id}-${Date.now()}-${globalIndex}`
        
        return this.executeWithBottleneck(
          async () => {
            this.log('info', `Processing ${this.name} entity ${entity.id} [${globalIndex + 1}/${entities.length}] in batch ${currentBatch}`)
            
            const result = await this.processEntity(entity)
            await this.updateEntityStatus(entity.id, result.success, result.error)
            
            return { entity, result, globalIndex: globalIndex + 1, batchIndex: currentBatch }
          },
          jobId,
          this.bottleneckConfig.defaultPriority,
          true // Enable retry
        )
      })

      // Process current batch with Bottleneck managing concurrency within the batch
      const batchResults = await Promise.allSettled(batchJobs)

      // Process batch results
      for (let i = 0; i < batchResults.length; i++) {
        const jobResult = batchResults[i]
        const entity = batch[i]
        const globalIndex = batchStart + i
        
        results.processed++
        
        if (jobResult.status === 'fulfilled') {
          const { result, batchIndex } = jobResult.value
          
          if (result.success) {
            results.successful++
            this.log('info', `Successfully processed entity ${entity.id} [${globalIndex + 1}/${entities.length}] in batch ${batchIndex}`)
          } else {
            results.failed++
            if (result.error) {
              results.errors.push(result.error)
            }
            this.log('warn', `Failed to process entity ${entity.id} [${globalIndex + 1}/${entities.length}] in batch ${batchIndex}: ${result.error || 'Unknown error'}`)
          }
        } else {
          results.failed++
          const errorMessage = jobResult.reason instanceof Error ? jobResult.reason.message : String(jobResult.reason)
          results.errors.push(errorMessage)
          this.log('error', `Error processing entity ${entity.id} [${globalIndex + 1}/${entities.length}] in batch ${currentBatch}: ${errorMessage}`)

          // Try to update entity status on failure
          try {
            await this.updateEntityStatus(entity.id, false, errorMessage)
          } catch (updateError) {
            this.log('error', `Failed to update entity status: ${updateError}`)
          }
        }
      }

      // Log batch completion
      const batchSuccessful = batchResults.filter(r => r.status === 'fulfilled' && r.value.result.success).length
      const batchFailed = batchResults.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.result.success)).length
      this.log('info', `Batch ${currentBatch}/${totalBatches} completed: ${batchSuccessful} successful, ${batchFailed} failed`)

      // Log current Bottleneck status
      const bottleneckStatus = this.getBottleneckStatus()
      this.log('debug', `Bottleneck status after batch ${currentBatch}: ${JSON.stringify(bottleneckStatus)}`)

      // Wait between batches (except for the last batch)
      if (batchIndex < totalBatches - 1 && batchDelay > 0) {
        this.log('debug', `Waiting ${batchDelay}ms before next batch...`)
        await this.sleep(batchDelay)
      }
    }

    const duration = Date.now() - startTime
    
    // Log metrics if enabled
    if (this.bottleneckConfig.enableJobMetrics) {
      const metrics = this.getJobMetrics()
      const metricsSummary = {
        totalJobs: Object.keys(metrics).length,
        successfulJobs: Object.values(metrics).filter(m => m.success).length,
        failedJobs: Object.values(metrics).filter(m => m.success === false).length,
        averageDuration: Object.values(metrics)
          .filter(m => m.duration)
          .reduce((sum, m) => sum + (m.duration || 0), 0) / Object.values(metrics).filter(m => m.duration).length || 0
      }
      
      this.log('info', `Job metrics: ${JSON.stringify(metricsSummary)}`)
    }

    this.log('info', `${this.name} processing completed in ${duration}ms. Processed: ${results.processed}, Successful: ${results.successful}, Failed: ${results.failed}`)

    return results
  }

  /**
   * Get unified entities (contacts and companies) by calling the existing unified filters API
   * This reuses the proven filtering logic from the UI components
   * 
   * Example usage:
   * - UI sends: searchTerm="john", source=["website"], contactEnrichmentV2Status=["pending"]
   * - Trigger API receives these as filters and passes to processor
   * - BaseProcessor calls unified filters API with same params
   * - Result: Same data as UI would get
   */
  async getUnprocessedUnifiedEntities(
    baseFilters: Record<string, any> = {},
    specificFilters: Record<string, any> = {},
    entityType?: 'contact' | 'company' | 'both'
  ): Promise<UnifiedEntityData[]> {
    const results: UnifiedEntityData[] = []

    try {
      // Process contacts if requested
      if (entityType === 'contact' || entityType === 'both' || !entityType) {
        const contactResults = await this.fetchUnifiedEntitiesFromAPI('contact', baseFilters, specificFilters)
        results.push(...contactResults)
      }

      // Process companies if requested
      if (entityType === 'company' || entityType === 'both') {
        const companyResults = await this.fetchUnifiedEntitiesFromAPI('company', baseFilters, specificFilters)
        results.push(...companyResults)
      }

    } catch (error) {
      this.log('error', `Error in getUnprocessedUnifiedEntities: ${error}`)
      throw error
    }

    this.log('info', `Retrieved ${results.length} unified entities (${entityType || 'both'})`)
    return results
  }

  /**
   * Fetch entities from the unified filters API
   */
  private async fetchUnifiedEntitiesFromAPI(
    entityType: 'contact' | 'company',
    baseFilters: Record<string, any>,
    specificFilters: Record<string, any>
  ): Promise<UnifiedEntityData[]> {
    try {
      // Start with search params from processor options (if any from trigger API)
      const searchParams = new URLSearchParams()
      
      // Add processor-specific filters based on options
      this.addProcessorSpecificFilters(searchParams, baseFilters, specificFilters, entityType)
      
      
      console.log('searchParams', searchParams)
      // Construct API URL
      const apiPath = entityType === 'contact' 
        ? '/api/contacts/unified-filters-v2'
        : '/api/companies/unified-filters-v2'
      
      const apiUrl = `${process.env.API_BASE_URL || 'http://localhost:3030'}${apiPath}?${searchParams.toString()}`
      
      this.log('debug', `Fetching ${entityType} entities from API: ${apiUrl}`)

      // Make API call
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`API call failed with status ${response.status}: ${response.statusText}`)
      }

      const apiResult = await response.json()
      
      if (!apiResult.success || !apiResult.data) {
        throw new Error(`API returned error: ${apiResult.error || 'Unknown error'}`)
      }

      this.log('debug', `Received ${apiResult.data.length} ${entityType} entities from API`)

      // Convert API response to UnifiedEntityData format
      return apiResult.data.map((row: any) => this.convertApiRowToUnifiedEntity(row, entityType))

    } catch (error) {
      this.log('error', `Error fetching ${entityType} entities from API: ${error}`)
      throw error
    }
  }

  /**
   * Convert API response row to UnifiedEntityData format
   */
  private convertApiRowToUnifiedEntity(row: any, entityType: 'contact' | 'company'): UnifiedEntityData {
    if (entityType === 'contact') {
      return {
        id: row.contact_id,
        entity_type: 'contact' as const,

        // Contact-specific fields
        contact_id: row.contact_id,
        first_name: row.first_name,
        last_name: row.last_name,
        full_name: row.full_name,
        title: row.title,
        email: row.email,
        linkedin_url: row.linkedin_url,
        company_id: row.company_id,
        contact_country: row.contact_country,
        phone_number: row.phone_number,
        // Company-specific fields
        company_name: row.company_name,
        company_website: row.website || row.company_website,
        industry: row.industry,
        
        // Shared fields
        created_at: row.created_at,
        updated_at: row.updated_at
      }
    } else {
      return {
        id: row.company_id,
        entity_type: 'company' as const,

        // Contact-specific fields (undefined for companies)
        contact_id: undefined,
        first_name: undefined,
        last_name: undefined,
        full_name: undefined,
        title: undefined,
        email: undefined,
        linkedin_url: undefined,
        company_id: row.company_id,
        contact_country: undefined,
        phone_number: undefined,

        // Company-specific fields
        company_name: row.company_name,
        company_website: row.website || row.company_website,
        industry: row.industry,
        
        // Shared fields
        created_at: row.created_at,
        updated_at: row.updated_at
      }
    }
  }

  /**
   * Add processor-specific filters to search params with smart precedence order
   * This method can be overridden by specific processors to add their custom filters
   */
  protected addProcessorSpecificFilters(
    searchParams: URLSearchParams,
    baseFilters: Record<string, any> = {},
    specificFilters: Record<string, any> = {},
    entityType?: 'contact' | 'company'
  ): void {
    console.log('Options', this.options)
    
    // Check if this is a targeted request (should bypass specific filters)
    const isTargetedRequest = this.hasTargetingParameters(baseFilters, specificFilters)
    
    if (isTargetedRequest) {
      this.log('info', `Targeted request detected - bypassing specific filters, applying only base filters and search filters`)
      
      // For targeted requests, only apply search filters (from options) and base filters
      // Skip specific filters and default filters that might exclude the targeted entities
      const targetedFilters = this.mergeFiltersWithPrecedence(
        this.options.filters || {},
        {}, // Empty specific filters
        {}, // Empty default filters  
        baseFilters
      )
      
      this.applyFiltersToSearchParams(searchParams, targetedFilters, 'Targeted')
    } else {
      // Normal batch processing - apply all filters with precedence
      const defaultFilters = this.getDefaultProcessorFilters(entityType)
      
      // Smart filter merging with precedence order:
      // 1. Search filters (highest priority) - from trigger API
      // 2. Specific filters (medium priority) - processor-specific
      // 3. Default filters (low priority) - processor defaults when no UI filter
      // 4. Base filters (lowest priority) - common to all processors
      
      const mergedFilters = this.mergeFiltersWithPrecedence(
        this.options.filters || {},
        specificFilters,
        defaultFilters,
        baseFilters
      )
      
      // Apply merged filters to search params
      this.applyFiltersToSearchParams(searchParams, mergedFilters, 'Merged')
    }

    // Handle single ID and multi IDs
    if (this.options.singleId) {
      // For single ID processing, add the appropriate ID parameter
      const idParamName = entityType === 'company' ? 'companyIds' : 'contactIds'
      searchParams.set(idParamName, String(this.options.singleId))
      this.log('debug', `Processing single ID: ${this.options.singleId}`)
    }

    if (this.options.multiIds && this.options.multiIds.length > 0) {
      // For multi ID processing, add the appropriate IDs parameter
      const idParamName = entityType === 'company' ? 'companyIds' : 'contactIds'
      searchParams.set(idParamName, this.options.multiIds.join(','))
      this.log('debug', `Processing multiple IDs: ${this.options.multiIds.join(', ')}`)
    }

    // Add limit parameter - always add if specified
    // For specific IDs, use the number of IDs as limit to ensure we get all of them
    // For filter-based processing or force running, use the specified limit
    if (this.options.limit) {
      if (this.options.multiIds && this.options.multiIds.length > 0) {
        // For multi IDs, use the number of IDs as limit to ensure we get all of them
        searchParams.set('limit', this.options.multiIds.length.toString())
        this.log('debug', `Added limit parameter for multi IDs: ${this.options.multiIds.length}`)
      } else if (this.options.singleId) {
        // For single ID, use 1 as limit
        searchParams.set('limit', '1')
        this.log('debug', `Added limit parameter for single ID: 1`)
      } else {
        // For filter-based processing or force running, use the specified limit
        searchParams.set('limit', this.options.limit.toString())
        this.log('debug', `Added limit parameter for filter processing: ${this.options.limit}`)
      }
    }

    // Debug log snapshot of outgoing filters
    this.log('debug', `[UnifiedAPI] Outgoing filters: ${JSON.stringify(Object.fromEntries(searchParams))}`)
  }

  /**
   * Check if this request has targeting parameters that should bypass specific filters
   * Any non-limit search filters indicate a targeted call
   */
  protected hasTargetingParameters(baseFilters: Record<string, any>, specificFilters: Record<string, any>): boolean {
    // Check processor options for targeting
    if (this.options.singleId || (this.options.multiIds && this.options.multiIds.length > 0)) {
      return true
    }

    // Check for any non-limit search filters in options
    const searchFilters = this.options.filters || {}
    
    // Parameters that are NOT targeting (limit/pagination related)
    const nonTargetingParams = new Set([
      'limit', 'page', 'offset', 'sortBy', 'sortOrder', 
      'stats', 'statsOnly', 'pageSize', 'notEmptyEmail'
    ])
    
    // If there are any search filters that aren't limit/pagination related, it's targeted
    for (const [param, value] of Object.entries(searchFilters)) {
      // Skip non-targeting parameters
      if (nonTargetingParams.has(param)) {
        continue
      }
      
      // Check if the parameter has a meaningful value
      if (value !== undefined && value !== null && value !== '' && 
          !(Array.isArray(value) && value.length === 0)) {
        this.log('debug', `Targeting parameter detected: ${param} = ${JSON.stringify(value)}`)
        return true
      }
    }

    // Also check base and specific filters for non-limit parameters
    const allFilters = { ...baseFilters, ...specificFilters }
    for (const [param, value] of Object.entries(allFilters)) {
      // Skip non-targeting parameters
      if (nonTargetingParams.has(param)) {
        continue
      }
      
      // Check if the parameter has a meaningful value
      if (value !== undefined && value !== null && value !== '' && 
          !(Array.isArray(value) && value.length === 0)) {
        this.log('debug', `Targeting parameter detected in filters: ${param} = ${JSON.stringify(value)}`)
        return true
      }
    }

    return false
  }

  /**
   * Get default processor-specific filters when no UI filters are provided
   */
  protected getDefaultProcessorFilters(entityType?: 'contact' | 'company'): Record<string, any> {
    // Override this method in specific processors to provide default filters
    return {}
  }

  /**
   * Merge filters with precedence order: Search > Specific > Default > Base
   * If a filter is set to 'all', it falls back to the next level in precedence
   * Handles conflicts between positive and NOT filters for the same field
   */
  private mergeFiltersWithPrecedence(
    searchFilters: Record<string, any>,
    specificFilters: Record<string, any>,
    defaultFilters: Record<string, any>,
    baseFilters: Record<string, any>
  ): Record<string, any> {
    const merged: Record<string, any> = {}
    
    // Get all unique filter keys from all four sources
    const allKeys = new Set([
      ...Object.keys(searchFilters),
      ...Object.keys(specificFilters),
      ...Object.keys(defaultFilters),
      ...Object.keys(baseFilters)
    ])
    
    for (const key of allKeys) {
      // Skip entityType as it's handled separately
      if (key === 'entityType') continue
      
      // Apply precedence order: Search > Specific > Default > Base
      let value = searchFilters[key]
      
      // If search filter exists (not undefined/null), use it and skip all other levels
      if (value !== undefined && value !== null && value !== 'all') {
        merged[key] = value
        continue // Skip to next key, don't check other levels
      }
      
      // If search filter is 'all' or undefined, try specific filter
      if (value === 'all' || value === undefined || value === null) {
        value = specificFilters[key]
      }
      
      // If specific filter is 'all' or undefined, try default filter
      if (value === 'all' || value === undefined || value === null) {
        value = defaultFilters[key]
      }
      
      // If default filter is 'all' or undefined, try base filter
      if (value === 'all' || value === undefined || value === null) {
        value = baseFilters[key]
      }
      
      // Only add if we have a valid value (not 'all', undefined, or null)
      if (value !== 'all' && value !== undefined && value !== null) {
        merged[key] = value
      }
    }
    
    // Handle conflicts between different parameter names for the same field
    // For example: overviewV2Status vs companyOverviewV2Status
    const parameterNameMappings: Record<string, string[]> = {
      'overviewV2Status': ['companyOverviewV2Status'],
      'companyOverviewV2Status': ['overviewV2Status'],
      'websiteScrapingStatus': ['companyWebsiteScrapingStatus'],
      'companyWebsiteScrapingStatus': ['websiteScrapingStatus'],
      'contact_email_verification_status': ['emailVerificationStatus'],
      'emailVerificationStatus': ['contact_email_verification_status'],
    }
    
    const fieldsToRemove: string[] = []
    
    for (const key of Object.keys(merged)) {
      const conflictingNames = parameterNameMappings[key] || []
      for (const conflictingName of conflictingNames) {
        if (merged[conflictingName] !== undefined) {
          this.log('debug', `Parameter name conflict detected: both '${key}' and '${conflictingName}' present. Prioritizing processor-specific name.`)
          // Remove the UI parameter name, keep the processor-specific name
          if (key === 'overviewV2Status' || key === 'websiteScrapingStatus') {
            fieldsToRemove.push(key)
          } else {
            fieldsToRemove.push(conflictingName)
          }
        }
      }
    }
    
    // Handle conflicts between positive and NOT filters for the same field
    // If we have both a positive filter and a NOT filter for the same field,
    // prioritize the positive filter and remove the NOT filter
    for (const key of Object.keys(merged)) {
      if (key.startsWith('not')) {
        // Extract the base field name (remove 'not' prefix and capitalize first letter)
        const baseFieldName = key.slice(3).charAt(0).toLowerCase() + key.slice(4)
        
        // Check if we also have a positive filter for this field
        if (merged[baseFieldName] !== undefined) {
          this.log('debug', `Conflict detected: both '${baseFieldName}' and '${key}' filters present. Prioritizing positive filter.`)
          fieldsToRemove.push(key)
        }
        
        // Also check for field name variations (e.g., notOverviewV2Status vs companyOverviewV2Status)
        // Handle special cases where the field names don't match exactly
        const fieldMappings: Record<string, string[]> = {
          'notOverviewV2Status': ['companyOverviewV2Status', 'overviewV2Status'],
          'notWebsiteScrapingStatus': ['websiteScrapingStatus'],
          'notCompanyOverviewStatus': ['companyOverviewStatus'],
          'notInvestmentCriteriaStatus': ['investmentCriteriaStatus', 'companyInvestmentCriteriaStatus'],
          'notContactsEmailVerificationStatus': ['contactsEmailVerificationStatus'],
          'notContactsEnrichmentStatus': ['contactsEnrichmentStatus'],
          'notContactsEnrichmentV2Status': ['contactsEnrichmentV2Status'],
          'notContactsEmailGenerationStatus': ['contactsEmailGenerationStatus'],
          'notContactsEmailSendingStatus': ['contactsEmailSendingStatus'],
        }
        
        const possibleMatches = fieldMappings[key] || []
        for (const possibleMatch of possibleMatches) {
          if (merged[possibleMatch] !== undefined) {
            this.log('debug', `Conflict detected: both '${possibleMatch}' and '${key}' filters present. Prioritizing positive filter.`)
            fieldsToRemove.push(key)
            break
          }
        }
      }
    }
    
    // Remove the conflicting NOT filters
    fieldsToRemove.forEach(field => {
      delete merged[field]
    })
    
    this.log('debug', `Filter precedence merge:
      Search filters: ${JSON.stringify(searchFilters)}
      Specific filters: ${JSON.stringify(specificFilters)}
      Default filters: ${JSON.stringify(defaultFilters)}
      Base filters: ${JSON.stringify(baseFilters)}
      Merged result: ${JSON.stringify(merged)}
      Removed conflicting fields: ${fieldsToRemove.join(', ')}`)
    
    return merged
  }

  /**
   * Apply filters from key-value pairs to search parameters
   */
  private applyFiltersToSearchParams(
    searchParams: URLSearchParams,
    filters: Record<string, any>,
    filterType: string
  ): void {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== 'all') {
        if (Array.isArray(value)) {
          searchParams.set(key, value.join(','))
        } else {
          searchParams.set(key, String(value))
        }
      }
    })

    if (Object.keys(filters).length > 0) {
      this.log('debug', `${filterType} filters: ${JSON.stringify(filters)}`)
    }
  }

  /**
   * Get combined content from all scraped pages for a company
   */
  protected async getCompanyContent(companyId: number): Promise<string> {
    try {
      // Updated query to use company_web_pages with metadata
        const sql = `
          SELECT 
            url, 
            extracted_text as content, 
            crawl_depth, 
            parent_url, 
            relevance_rank,
            page_metadata
          FROM company_web_pages
        WHERE company_id = $1
        AND extracted_text IS NOT NULL
        ORDER BY 
          relevance_rank 
        LIMIT 20
      `

      const pages = await this.query(sql, [companyId])

      if (pages.length === 0) {
        this.log('warn', `No content found for company ${companyId}`)
        return ''
      }

      // Combine content with URL context and metadata
      const combinedContent = pages
        .map((page: Record<string, unknown>) => {
          const url = page.url as string
          const content = page.content as string
          const depth = page.crawl_depth as number || 0
          const parentUrl = page.parent_url as string || null
          const relevanceRank = page.relevance_rank as number || 0
          
          // Extract raw metadata (YAML string)
          const pageMetadata = page.page_metadata as string || null
          
          // Build metadata summary
          let metadataSection = ''
          if (pageMetadata) {
            metadataSection = `
          METADATA (Raw YAML):
      ${pageMetadata}`
          }
          
          return `\n=== URL: ${url} ===
          DEPTH: ${depth}
          RELEVANCE RANK: ${relevanceRank}
          ${parentUrl ? `PARENT URL: ${parentUrl}` : 'ROOT PAGE'}${metadataSection}
          CONTENT:
          ${content}`
        })
        .join('\n\n').slice(0, 100000)

      const pagesWithMetadata = pages.filter(page => 
        page.page_metadata && typeof page.page_metadata === 'string' && page.page_metadata.trim().length > 0
      ).length
      
      this.log('info', `Combined ${pages.length} pages of content for company ${companyId} (${pagesWithMetadata} with metadata)`)
      return combinedContent
    } catch (error) {
      this.log('error', `Error fetching company content for ${companyId}: ${error}`)
      return ''
    }
  }

  /**
   * Get condensed overview summary (from companies.overview_summary) and curated related URLs
   * from company_web_pages for a given company. This centralizes logic used by multiple processors.
   */
  protected async getCompanyOverviewSummaryAndUrls(
    companyId: number,
    urlLimit: number = 20
  ): Promise<{ overviewSummary: string; relatedUrls: string[] }> {
    try {
      // Fetch overview_summary JSON from companies
      const summarySql = `
        SELECT overview_summary
        FROM companies
        WHERE company_id = $1
      `
      const summaryRows = await this.query(summarySql, [companyId])
      let overviewSummary = ''
      if (summaryRows.length > 0 && (summaryRows[0] as any).overview_summary) {
        const os = (summaryRows[0] as any).overview_summary as any
        const parts: string[] = []
        if (os.financial_metrics_summary_text) parts.push(os.financial_metrics_summary_text)
        if (os.investment_criteria_summary_text) parts.push(os.investment_criteria_summary_text)
        overviewSummary = parts.join('\n\n')
      }

      // Fetch curated related URLs from company_web_pages
      const urlSql = `
        SELECT url
        FROM company_web_pages
        WHERE company_id = $1 AND extracted_text IS NOT NULL
        ORDER BY relevance_rank
        LIMIT ${urlLimit}
      `
      const urlRows = await this.query(urlSql, [companyId])
      const relatedUrls = urlRows
        .map((r: any) => r.url)
        .filter((u: any) => typeof u === 'string')

      return { overviewSummary, relatedUrls }
    } catch (e) {
      this.log('warn', `Failed to get overview summary/urls for company ${companyId}: ${e}`)
      return { overviewSummary: '', relatedUrls: [] }
    }
  }

  /**
   * Extracts an array of URL strings from companies.overview_sources JSON
   * to be used as high-priority seed sources for subsequent processors.
   */
  protected async getCompanyOverviewSourceUrls(companyId: number): Promise<string[]> {
    try {
      const sql = `
        SELECT overview_sources
        FROM companies
        WHERE company_id = $1
      `
      const rows = await this.query(sql, [companyId])
      if (rows.length === 0 || !(rows[0] as any).overview_sources) {
        return []
      }
      const sourcesObj = (rows[0] as any).overview_sources
      const urls: string[] = []

      const urlRegex = /https?:\/\/[\w.-]+(?:\/[\w\-._~:?#\[\]@!$&'()*+,;=%/]*)?/gi

      Object.values(sourcesObj).forEach((val: any) => {
        if (Array.isArray(val)) {
          val.forEach((entry: any) => {
            if (typeof entry === 'string') {
              const matches = entry.match(urlRegex)
              if (matches) urls.push(...matches)
            }
          })
        }
      })

      // Dedupe and return
      return Array.from(new Set(urls))
    } catch (e) {
      this.log('warn', `Failed to get overview source URLs for company ${companyId}: ${e}`)
      return []
    }
  }

  /**
   * Update contact email verification status
   */
  protected async updateContactEmailVerificationStatus(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_verification_status = $1::text, 
          email_verification_error = $2,
          email_verification_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_verification_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated email verification status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email verification status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact email generation status
   */
  protected async updateContactEmailGenerationStatus(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_generation_status = $1::text, 
          email_generation_error = $2,
          email_generated = $3,
          email_generation_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_generation_date END,
          updated_at = NOW() 
      WHERE contact_id = $4
    `
    try {
      // Update legacy email_generated field for backward compatibility
      const emailGenerated = status === 'completed' ? true : false
      await this.query(sql, [status, error || null, emailGenerated, contactId])
      this.log('debug', `Updated email generation status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email generation status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact email sending status
   */
  protected async updateContactEmailSendingStatus(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_sending_status = $1::text, 
          email_sending_error = $2,
          email_sending_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_sending_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated email sending status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email sending status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact enrichment V2 status
   */
  protected async updateContactEnrichmentV2Status(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET contact_enrichment_v2_status = $1::text, 
          contact_enrichment_v2_error = $2,
          contact_enrichment_v2_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE contact_enrichment_v2_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated contact enrichment V2 status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating contact enrichment V2 status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update company website scraping status
   */
  protected async updateCompanyWebscrapingStatus(
    companyId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET website_scraping_status = $1::text, 
          website_scraping_error = $2,
          website_scraping_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE website_scraping_date END,
          updated_at = NOW() 
      WHERE company_id = $3
    `
    try {
      await this.query(sql, [status, error || null, companyId])
      this.log('debug', `Updated website scraping status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating website scraping status for company ${companyId}: ${error}`)
    }
  }


  /**
   * Update company overview V2 extraction status
   */
  protected async updateOverviewV2Status(
    companyId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET 
        overview_v2_status = $1::text,
        overview_v2_error = $2,
        overview_v2_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE overview_v2_date END,
        overview_v2_error_count = CASE WHEN $1::text = 'failed' THEN COALESCE(overview_v2_error_count, 0) + 1 ELSE overview_v2_error_count END,
        updated_at = NOW() 
      WHERE company_id = $3
    `
    try {
      await this.query(sql, [status, error || null, companyId])
      this.log('debug', `Updated overview V2 status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating overview V2 status for company ${companyId}: ${error}`)
    }
  }

  /**
   * Get news entries that need HTML fetching
   */
  protected async getNewsForHTMLFetching(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    const conditions = [
      'fetched = false'
    ]

    const params: unknown[] = []
    let paramIndex = 1

    // Handle multi-ID processing
    if (this.options.multiIds && this.options.multiIds.length > 0) {
      this.log('info', `Processing multiple news IDs for HTML fetching: ${this.options.multiIds.join(', ')}`)

      // Create placeholder string for IN clause
      const placeholders = this.options.multiIds.map((_, index) => `$${paramIndex + index}`).join(', ')
      conditions.push(`id IN (${placeholders})`)

      // Add all IDs to params
      this.options.multiIds.forEach(id => params.push(id))
      paramIndex += this.options.multiIds.length

      // For multi-ID runs, allow reprocessing regardless of status
      const pendingConditionIndex = conditions.findIndex(c => c.includes('fetched'))
      if (pendingConditionIndex !== -1) {
        conditions.splice(pendingConditionIndex, 1)
      }
    }

    if (singleId) {
      // First check if the single news entry exists
      const checkSql = `
        SELECT id, url, fetched
        FROM news 
        WHERE id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `News entry ${singleId} not found`)
          return []
        }

        const news = checkResult[0]

        if (news.fetched) {
          this.log('info', `News entry ${singleId} already has HTML fetched - will reprocess for single run`)
          // For single runs, allow reprocessing even if already fetched
        }
      } catch (error) {
        this.log('error', `Error checking single news entry ${singleId}: ${error}`)
        return []
      }

      conditions.push(`id = $${paramIndex}`)
      params.push(singleId)
      paramIndex++
    }

    const sql = `
      SELECT id, url, fetched, created_at, updated_at
      FROM news 
      WHERE (bad_url = false OR bad_url IS NULL) AND ${conditions.join(' AND ')}
      ORDER BY id DESC
      ${limit && !this.options.multiIds ? `LIMIT ${limit}` : ''}
    `

    try {
      return await this.query(sql, params)
    } catch (error) {
      this.log('error', `Error fetching news for HTML fetching: ${error}`)
      return []
    }
  }

  /**
   * Update news HTML fetching status
   */
  protected async updateNewsHTMLFetchingStatus(
    newsId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    // Since news table uses the 'fetched' boolean field
    const sql = `
      UPDATE news 
      SET fetched = $1,
          fetch_status = $2,
          fetch_error = $3,
          updated_at = NOW() 
      WHERE id = $4
    `
    try {
      const fetched = status === 'completed'
      await this.query(sql, [fetched, status, error || null, newsId])
      this.log('debug', `Updated news HTML fetching status for news ${newsId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating news HTML fetching status for news ${newsId}: ${error}`)
    }
  }

  /**
   * Update article HTML fetching status
   */
  protected async updateArticleHTMLFetchingStatus(
    articleId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    try {
      if (error) {
        const sql = `
          UPDATE article
          SET
            fetch_status = $1,
            fetch_error = $2,
            updated_at = NOW()
          WHERE article_id = $3
        `
        const errorData = JSON.stringify({ error, timestamp: new Date().toISOString() })
        await this.query(sql, [status, errorData, articleId])
      } else {
        const sql = `
          UPDATE article
          SET
            fetch_status = $1,
            updated_at = NOW()
          WHERE article_id = $2
        `
        await this.query(sql, [status, articleId])
      }
      
      this.log('debug', `Updated article HTML fetching status for article ${articleId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating article HTML fetching status for article ${articleId}: ${error}`)
    }
  }

  /**
   * Update news enrichment status
   */
  protected async updateNewsEnrichmentStatus(
    newsId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    try {
      const extracted = status === 'completed'

      if (error) {
        // Update with error
        const sql = `
          UPDATE news 
          SET extracted = $1,
              enrichment_status = $2,
              enrichment_error = $3,
              updated_at = NOW()
          WHERE id = $4
        `
        await this.query(sql, [extracted, status, error, newsId])
      } else {
        // Update without error
        const sql = `
          UPDATE news 
          SET extracted = $1,
              enrichment_status = $2,
              enrichment_error = $3,
              updated_at = NOW()
          WHERE id = $4
        `
        await this.query(sql, [extracted, status, null, newsId])
      }

      this.log('debug', `Updated news enrichment status for news ${newsId}: ${status}${error ? ` (error: ${error})` : ''}`)
    } catch (dbError) {
      this.log('error', `Error updating news enrichment status for news ${newsId}: ${dbError}`)
    }
  }

  /**
   * Increment processing error count
   */
  protected async incrementProcessingErrorCount(entityType: 'contact' | 'company', entityId: number): Promise<void> {
    const table = entityType === 'contact' ? 'contacts' : 'companies'
    const idColumn = entityType === 'contact' ? 'contact_id' : 'company_id'

    const sql = `
      UPDATE ${table} 
      SET processing_error_count = COALESCE(processing_error_count, 0) + 1,
          updated_at = NOW() 
      WHERE ${idColumn} = $1
    `
    try {
      await this.query(sql, [entityId])
      this.log('debug', `Incremented processing error count for ${entityType} ${entityId}`)
    } catch (error) {
      this.log('error', `Error incrementing processing error count for ${entityType} ${entityId}: ${error}`)
    }
  }

  /**
   * Save processing attempt information to the processing_attempts column
   */
  protected async saveProcessingAttempt(
    entityType: 'contact' | 'company',
    entityId: number,
    stage: string,
    usage?: any,
    model?: string,
    tokens?: number,
    success?: boolean,
    error?: string
  ): Promise<void> {
    const table = entityType === 'contact' ? 'contacts' : 'companies'
    const idColumn = entityType === 'contact' ? 'contact_id' : 'company_id'

    try {
      // Create the attempt record
      const attemptRecord = {
        timestamp: new Date().toISOString(),
        stage: stage,
        success: success ?? true,
        model: model,
        tokens: tokens,
        usage: usage,
        ...(error && { error: error })
      }

      // Get current processing_attempts
      const getCurrentSql = `
        SELECT processing_attempts 
        FROM ${table} 
        WHERE ${idColumn} = $1
      `

      const currentResult = await this.query(getCurrentSql, [entityId])
      let currentAttempts: Record<string, any[]> = {}

      if (currentResult.length > 0 && currentResult[0].processing_attempts) {
        currentAttempts = currentResult[0].processing_attempts as Record<string, any[]>
      }

      // Add the new attempt (using stage as key, keeping array of attempts per stage)
      if (!currentAttempts[stage]) {
        currentAttempts[stage] = []
      }

      // Keep only the last 5 attempts per stage to avoid bloating
      if (currentAttempts[stage].length >= 5) {
        currentAttempts[stage] = currentAttempts[stage].slice(-4)
      }

      currentAttempts[stage].push(attemptRecord)

      // Update the processing_attempts column
      const updateSql = `
        UPDATE ${table} 
        SET processing_attempts = $1::json,
            updated_at = NOW() 
        WHERE ${idColumn} = $2
      `

      await this.query(updateSql, [JSON.stringify(currentAttempts), entityId])
      this.log('debug', `Saved processing attempt for ${entityType} ${entityId}, stage: ${stage}, success: ${success}`)

    } catch (error) {
      this.log('error', `Error saving processing attempt for ${entityType} ${entityId}: ${error}`)
    }
  }

  // Legacy status update methods (deprecated - use new status column methods above)

  /**
   * Use updateContactEmailVerificationStatus instead
   */
  protected async updateContactEmailStatus(contactId: number, status: string): Promise<void> {
    this.log('warn', 'updateContactEmailStatus is deprecated, use updateContactEmailVerificationStatus instead')
    const sql = `UPDATE contacts SET email_status = $1, updated_at = NOW() WHERE contact_id = $2`
    try {
      await this.query(sql, [status, contactId])
    } catch (error) {
      this.log('error', `Error updating email status for contact ${contactId}: ${error}`)
    }
  }
}
