import { BaseProcessor, BottleneckConfig } from './BaseProcessor'
import { EntityData, ProcessorOptions, UnifiedEntityData } from '../../types/processing'

export interface JobTierData extends EntityData {
  contact_id: number
  title?: string
  job_tier?: string
}

export class JobTierProcessor extends BaseProcessor {
  private jobTierMappings: Map<string, string> = new Map()
  private mappingsLoaded: boolean = false

  constructor(options: ProcessorOptions = {}) {
    super('JobTierProcessor', options)
  }

  /**
   * Override default bottleneck configuration for job tier processing
   */
  protected getDefaultBottleneckConfig(): BottleneckConfig {
    return {
      maxConcurrent: parseInt(process.env.JOB_TIER_MAX_CONCURRENT || '3'), // Reduced concurrency
      minTime: parseInt(process.env.JOB_TIER_MIN_TIME || '100'), // Slightly slower to prevent overload
      highWater: parseInt(process.env.JOB_TIER_HIGH_WATER || '500'), // Reduced queue size
      strategy: 'OVERFLOW' as any,
      retryAttempts: parseInt(process.env.JOB_TIER_RETRY_ATTEMPTS || '2'),
      retryDelayBase: parseInt(process.env.JOB_TIER_RETRY_DELAY_BASE || '1000'),
      retryDelayMax: parseInt(process.env.JOB_TIER_RETRY_DELAY_MAX || '5000'),
      defaultPriority: 5,
      timeout: parseInt(process.env.JOB_TIER_JOB_TIMEOUT || '15000'), // Reduced timeout
      enableJobMetrics: process.env.JOB_TIER_ENABLE_METRICS === 'true'
    }
  }

  /**
   * Load job tier mappings from central_mapping table
   */
  private async loadJobTierMappings(): Promise<void> {
    if (this.mappingsLoaded) return

    try {
      this.log('info', 'Loading job tier mappings from central_mapping table...')
      
      const sql = `
        SELECT value_2 as title, value_1 as job_tier
        FROM central_mapping 
        WHERE type = 'Job Tier Mapping' 
          AND is_active = true 
          AND level_1 = 'Job Tier' 
          AND level_2 = 'Title'
          AND value_2 IS NOT NULL
          AND value_1 IS NOT NULL
        ORDER BY value_2
      `
      
      const mappings = await this.query(sql, [])
      
      this.jobTierMappings.clear()
      
      for (const mapping of mappings) {
        const title = (mapping.title as string).toLowerCase().trim()
        const jobTier = mapping.job_tier as string
        this.jobTierMappings.set(title, jobTier)
      }

      this.log('info', `Loaded ${this.jobTierMappings.size} job tier mappings`)
      this.mappingsLoaded = true

    } catch (error) {
      this.log('error', `Error loading job tier mappings: ${error}`)
      throw error
    }
  }

  /**
   * Find matching job tier for a given title using advanced pattern matching first, then database mappings
   */
  private findJobTierForTitle(title: string): string | null {
    if (!title) return null
    
    const normalizedTitle = title.toLowerCase().trim()
    
    // FIRST: Advanced pattern matching based on matchTitle.js logic
    const jobTier = this.matchWithAdvancedPatterns(normalizedTitle)
    if (jobTier) {
      this.log('debug', `Advanced pattern match found: "${title}" -> "${jobTier}"`)
      return jobTier
    }

    // SECOND: Try exact match from database mappings
    if (this.jobTierMappings.has(normalizedTitle)) {
      this.log('debug', `Database exact match found: "${title}" -> "${this.jobTierMappings.get(normalizedTitle)}"`)
      return this.jobTierMappings.get(normalizedTitle) || null
    }

    // THIRD: Fallback to fuzzy matching from database mappings
    for (const [mappingTitle, jobTier] of this.jobTierMappings.entries()) {
      // Check if the contact title contains the mapping title
      if (normalizedTitle.includes(mappingTitle)) {
        this.log('debug', `Database fuzzy match found: "${title}" -> "${mappingTitle}" -> "${jobTier}"`)
        return jobTier
      }
      
      // Check if the mapping title contains the contact title (for shorter titles)
      if (mappingTitle.includes(normalizedTitle) && normalizedTitle.length >= 3) {
        this.log('debug', `Database reverse fuzzy match found: "${title}" -> "${mappingTitle}" -> "${jobTier}"`)
        return jobTier
      }
    }

    return null
  }

  /**
   * Advanced pattern matching based on matchTitle.js logic
   */
  private matchWithAdvancedPatterns(title: string): string | null {
    // Tier 1 - C-Suite / Founders (more specific patterns first)
    if (this.matchesPatterns(title, [
      '%chief credit officer%', '%cco%', '%chief credit officer (cco)%',
      '%chief development officer%', '%cdo%', '%chief development officer (cdo)%',
      '%chief executive officer%', '%ceo%', '%chief executive officer (ceo)%',
      '%chief financial officer%', '%cfo%', '%chief financial officer (cfo)%',
      '%chief investment officer%', '%cio%', '%chief investment officer (cio)%',
      '%chief lending officer%', '%clo%', '%chief lending officer (clo)%',
      '%chief operating officer%', '%coo%', '%chief operating officer (coo)%',
      '%chief real estate officer%', '%chief risk officer%',
      '%chief strategy officer%', '%cso%', '%chief strategy officer (cso)%',
      '%founder%', '%co-founder%', '%cofounder%',
      '%managing partner real estate investment%', '%managing partner real estate finance%',
      '%managing partner real estate%', '%managing partner%'
    ])) {
      return 'Tier 1 - C-Suite / Founders'
    }

    // President (but not Vice President) - more specific
    if (this.matchesPatterns(title, [
      '%president%'
    ]) && !this.matchesPatterns(title, [
      '%vice president%', '%vp%', '%assistant vice president%', '%first vice president%'
    ])) {
      return 'Tier 1 - C-Suite / Founders'
    }

    // Tier 2 - Senior Executives
    if (this.matchesPatterns(title, [
      '%head of acquisitions%', '%head of capital markets%', '%head of investments%',
      '%head of loan originations%', '%head of private markets%', '%head of real estate investments%',
      '%head of real estate%', '%managing director of acquisitions%', '%managing director acquisitions%',
      '%managing director of capital markets%', '%managing director capital markets%',
      '%managing director real estate investments%', '%managing director of real estate investments%',
      '%managing director%', '%partner of real estate finance%', '%partner real estate finance%',
      '%partner of real estate%', '%partner real estate%', '%partner real estate investment%',
      '%partner%', '%principal - real estate%', '%principal%'
    ])) {
      return 'Tier 2 - Senior Executives'
    }

    // Tier 3 - Mid-Level Executives
    if (this.matchesPatterns(title, [
      '%director of acquisitions%', '%director, acquisitions%', '%director of capital markets%',
      '%director, capital markets%', '%director of investments%', '%director, investments%',
      '%director of cre lending%', '%director cre lending%', '%director of structured finance%',
      '%director, structured finance%', '%director of real estate%', '%director of structured investments%',
      '%director of real estate strategy%', '%director, real estate strategy%',
      '%director of underwriting%', '%director, underwriting%',
      '%director of debt & equity placement%', '%director, debt & equity placement%',
      '%director -%', '%director,%', '%senior director%', '%executive director%',
      '%director%', // Catch standalone "Director"
      '%vice president of acquisitions%', '%vp of acquisitions%', '%vp, acquisitions%',
      '%vice president of capital markets%', '%vp of capital markets%', '%vp, capital markets%', '%vp capital markets%',
      '%vice president of cre lending%', '%vp of cre lending%', '%vp, cre lending%',
      '%vice president of investments%', '%vp of investments%', '%vp, investments%',
      '%vice president of real estate%', '%vp of real estate%', '%vp, real estate%',
      '%vice president of structured finance%', '%vp of structured finance%', '%vp, structured finance%',
      '%vice president of underwriting%', '%vp of underwriting%', '%vp, underwriting%',
      '%vice president of debt & equity placement%', '%vp of debt & equity placement%', '%vp, debt & equity placement%',
      '%assistant vice president%', '%first vice president%', '%executive vice president%'
    ])) {
      return 'Tier 3 - Mid-level Executives'
    }

    // Tier 4 - Investment / Portfolio Managers
    if (this.matchesPatterns(title, [
      '%cre origination manager%', '%investment manager real estate%', '%investment officer real estate%',
      '%portfolio manager - real estate%', '%portfolio manager, real estate%', '%real assets manager%',
      '%real estate portfolio strategist%'
    ])) {
      return 'Tier 4 - Investment / Portfolio Managers'
    }

    // Tier 5 - Analysts & Associates
    if (this.matchesPatterns(title, [
      '%acquisitions associate%', '%capital markets associate%', '%capital markets analyst%',
      '%real estate investment analyst%', '%real estate acquisitions analyst%',
      '%real estate private equity analyst%', '%investment associate real estate%',
      '%cre origination analyst%', '%cre originations associate%', '%cre originations%', '%originations%'
    ])) {
      return 'Tier 5 - Analysts & Associates'
    }

    return null
  }

  /**
   * Check if title matches any of the given patterns
   */
  private matchesPatterns(title: string, patterns: string[]): boolean {
    return patterns.some(pattern => {
      // Simple string matching for debugging - convert % to .* and ? to .
      let regexPattern = pattern
        .replace(/%/g, '.*')
        .replace(/\?/g, '.')
        .replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape remaining special chars
        .replace(/\\\.\\\*/g, '.*') // Restore .* for %
        .replace(/\\\./g, '.') // Restore . for ?
      
      try {
        const regex = new RegExp(`^${regexPattern}$`, 'i')
        return regex.test(title)
      } catch (error) {
        this.log('error', `Invalid regex pattern: ${regexPattern} from ${pattern}`)
        return false
      }
    })
  }

  /**
   * Get default processor-specific filters for job tier processing
   */
  protected getDefaultProcessorFilters(entityType?: 'contact' | 'company'): Record<string, any> {
    return {
      // Only process contacts (job tier is contact-specific)
      entityType: 'contact'
    }
  }

  /**
   * Get unprocessed entities - contacts that need job tier assignment
   */
  async getUnprocessedEntities(): Promise<EntityData[]> {
    try {
      // Ensure mappings are loaded
      await this.loadJobTierMappings()

      // Use the unified filters API to get contacts that need job tier assignment
      const results = await this.getUnprocessedUnifiedEntities(
        {jobTierStatus: ['pending'] }, // baseFilters - empty since we'll use specific filters
        {},
        'contact'
      )

      this.log('info', `Found ${results.length} contacts needing job tier assignment`)
      return results.map(contact => ({
        id: contact.contact_id!,
        contact_id: contact.contact_id!,
        title: contact.title,
        job_tier: undefined
      } as JobTierData))

    } catch (error) {
      this.log('error', `Error getting unprocessed job tier entities: ${error}`)
      throw error
    }
  }

  /**
   * Process individual contact for job tier assignment
   */
  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    const jobTierData = entity as JobTierData
    
    try {
      this.log('debug', `Processing job tier for contact ${jobTierData.contact_id} with title: "${jobTierData.title}"`)

      // Ensure mappings are loaded
      await this.loadJobTierMappings()

      if (!jobTierData.title) {
        const error = 'Contact has no title to process'
        this.log('warn', `Contact ${jobTierData.contact_id}: ${error}`)
        return { success: false, error }
      }

      // Find matching job tier
      const matchedJobTier = this.findJobTierForTitle(jobTierData.title)

      if (!matchedJobTier) {
        const error = `No job tier mapping found for title: "${jobTierData.title}"`
        this.log('debug', `Contact ${jobTierData.contact_id}: ${error}`)
        
        // Save processing attempt for unmatched titles
        await this.saveProcessingAttempt(
          'contact',
          jobTierData.contact_id,
          'job_tier_mapping',
          undefined,
          undefined,
          undefined,
          false,
          error
        )
        
        return { success: false, error }
      }

      // Update contact with job tier
      await this.updateContactJobTier(jobTierData.contact_id, matchedJobTier)

      this.log('info', `Contact ${jobTierData.contact_id}: Assigned job tier "${matchedJobTier}" for title "${jobTierData.title}"`)
      
      // Save successful processing attempt
      await this.saveProcessingAttempt(
        'contact',
        jobTierData.contact_id,
        'job_tier_mapping',
        undefined,
        undefined,
        undefined,
        true
      )

      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error processing job tier for contact ${jobTierData.contact_id}: ${errorMessage}`)
      
      // Save failed processing attempt
      await this.saveProcessingAttempt(
        'contact',
        jobTierData.contact_id,
        'job_tier_mapping',
        undefined,
        undefined,
        undefined,
        false,
        errorMessage
      )
      
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Update contact job tier status
   */
  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    // For job tier processing, the status is implied by the presence of the job_tier field
    // If successful, job_tier field will be populated
    // If failed, job_tier field will remain NULL
    
    if (!success && error) {
      this.log('debug', `Job tier assignment failed for contact ${entityId}: ${error}`)
      
      // Increment error count for failed processing
      await this.incrementProcessingErrorCount('contact', entityId)
    }
  }

  /**
   * Update contact with assigned job tier
   */
  private async updateContactJobTier(contactId: number, jobTier: string): Promise<void> {
    try {
      const sql = `
        UPDATE contacts 
        SET job_tier = $1::text,
            updated_at = NOW()
        WHERE contact_id = $2
      `
      
      await this.query(sql, [jobTier, contactId])
      this.log('debug', `Updated job tier for contact ${contactId}: ${jobTier}`)
      
    } catch (error) {
      this.log('error', `Error updating job tier for contact ${contactId}: ${error}`)
      throw error
    }
  }

  /**
   * Get job tier mapping statistics
   */
  async getJobTierMappingStats(): Promise<{
    totalMappings: number
    totalContacts: number
    contactsWithJobTier: number
    contactsNeedingJobTier: number
    contactsWithTitle: number
  }> {
    try {
      await this.loadJobTierMappings()
      
      const statsQuery = `
        SELECT 
          COUNT(*) as total_contacts,
          COUNT(job_tier) as contacts_with_job_tier,
          COUNT(*) - COUNT(job_tier) as contacts_needing_job_tier,
          COUNT(title) as contacts_with_title
        FROM contacts
      `
      
      const result = await this.query(statsQuery, [])
      const stats = result[0]
      
      return {
        totalMappings: this.jobTierMappings.size,
        totalContacts: Number(stats.total_contacts),
        contactsWithJobTier: Number(stats.contacts_with_job_tier),
        contactsNeedingJobTier: Number(stats.contacts_needing_job_tier),
        contactsWithTitle: Number(stats.contacts_with_title)
      }
      
    } catch (error) {
      this.log('error', `Error getting job tier mapping stats: ${error}`)
      throw error
    }
  }

  /**
   * Get sample of unmatched titles for debugging
   */
  async getUnmatchedTitlesSample(limit: number = 20): Promise<Array<{ title: string; count: number }>> {
    try {
      await this.loadJobTierMappings()
      
      const sql = `
        SELECT title, COUNT(*) as count
        FROM contacts 
        WHERE job_tier IS NULL 
          AND title IS NOT NULL 
          AND title != ''
          AND LENGTH(TRIM(title)) > 0
        GROUP BY title 
        ORDER BY count DESC, title
        LIMIT $1
      `
      
      const result = await this.query(sql, [limit])
      
      return result.map(row => ({
        title: row.title as string,
        count: Number(row.count)
      }))
      
    } catch (error) {
      this.log('error', `Error getting unmatched titles sample: ${error}`)
      throw error
    }
  }
}
