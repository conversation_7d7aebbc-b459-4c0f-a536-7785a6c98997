import { BaseProcessor, BottleneckConfig } from './BaseProcessor'
import { EntityData, ProcessorOptions, UnifiedEntityData } from '../../types/processing'

export interface SmartleadEntityData extends EntityData {
  contact_id: number
  email: string
  first_name?: string
  last_name?: string
  email_generation_status: string
  smartlead_status?: string
  smartlead_lead_id?: string
  campaign_id?: string
}

export class SmartleadProcessor extends BaseProcessor {
  constructor(options: ProcessorOptions = {}) {
    super('SmartleadProcessor', options)
  }

  /**
   * Get Smartlead-specific Bottleneck configuration with conservative rate limiting
   * Smartlead API typically has rate limits, so we'll be more conservative
   */
  protected getDefaultBottleneckConfig(): BottleneckConfig {
    return {
      maxConcurrent: parseInt(process.env.SMARTLEAD_MAX_CONCURRENT || '3'), // Lower concurrency
      minTime: parseInt(process.env.SMARTLEAD_MIN_TIME || '2000'), // 2 seconds between calls
      highWater: parseInt(process.env.SMARTLEAD_HIGH_WATER || '500'),
      strategy: 'OVERFLOW' as any,
      retryAttempts: parseInt(process.env.SMARTLEAD_RETRY_ATTEMPTS || '3'),
      retryDelayBase: parseInt(process.env.SMARTLEAD_RETRY_DELAY_BASE || '5000'), // 5s base delay
      retryDelayMax: parseInt(process.env.SMARTLEAD_RETRY_DELAY_MAX || '60000'), // 1 minute max
      defaultPriority: 5,
      timeout: parseInt(process.env.SMARTLEAD_JOB_TIMEOUT || '120000'), // 2 minutes timeout
      enableJobMetrics: process.env.SMARTLEAD_ENABLE_METRICS === 'true'
    }
  }

  /**
   * Get contacts that are ready for Smartlead sync
   * Criteria: email_generation_status = 'completed' AND smartlead_status is NULL or 'pending'
   */
  async getUnprocessedEntities(): Promise<SmartleadEntityData[]> {
    try {
      // Define base filters for Smartlead sync
      const baseFilters = {
        // Only process contacts with valid emails
        notEmptyEmail: true,
        // Only process contacts that have completed email generation
        emailGenerationStatus: ['completed']
      }

      // Define specific filters for Smartlead sync
      const specificFilters = {
        // Only process contacts that haven't been synced to Smartlead yet
        // This includes NULL smartlead_status and 'pending' status
        smartleadSyncStatus: ['null', 'pending']
      }

      // Use the unified entity approach from BaseProcessor
      const entities = await this.getUnprocessedUnifiedEntities(
        baseFilters,
        specificFilters,
        'contact'
      )

      // Transform to SmartleadEntityData
      return entities.map(entity => ({
        id: entity.contact_id!,
        contact_id: entity.contact_id!,
        email: entity.email!,
        first_name: entity.first_name,
        last_name: entity.last_name,
        // Use the processing state to determine email generation status
        email_generation_status: (entity as any).email_generation_status || 'pending',
        // Get smartlead status from the entity data
        smartlead_status: (entity as any).smartlead_status || null,
        // Get smartlead lead ID from the entity data
        smartlead_lead_id: (entity as any).smartlead_lead_id || undefined,
        campaign_id: this.options.campaignId
      }))
    } catch (error) {
      this.log('error', `Error getting unprocessed Smartlead entities: ${error}`)
      return []
    }
  }

  /**
   * Process individual contact for Smartlead sync
   */
  async processEntity(entity: SmartleadEntityData): Promise<{ success: boolean; error?: string }> {
    try {
      if (!entity.campaign_id) {
        return { success: false, error: 'Campaign ID is required for Smartlead sync' }
      }

      // Update status to running
      await this.updateSmartleadSyncStatus(entity.contact_id, 'running')

      // Call the individual contact sync API
      const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3030'}/api/smartlead/contacts/${entity.contact_id}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          campaignId: entity.campaign_id
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Smartlead sync API error: ${response.status} ${errorText}`)
      }

      const result = await response.json()
      
      if (result.success) {
        // Update status to completed
        await this.updateSmartleadSyncStatus(entity.contact_id, 'completed')
        
        // Update smartlead_lead_id if provided
        if (result.leadId) {
          await this.updateSmartleadLeadId(entity.contact_id, result.leadId)
        }

        this.log('info', `Successfully synced contact ${entity.contact_id} to Smartlead campaign ${entity.campaign_id}`)
        return { success: true }
      } else {
        throw new Error(result.error || 'Unknown error from Smartlead sync API')
      }

    } catch (error) {
      this.log('error', `Failed to sync contact ${entity.contact_id} to Smartlead: ${error}`)
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  }

  /**
   * Update entity status after processing
   */
  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    try {
      if (success) {
        await this.updateSmartleadSyncStatus(entityId, 'completed')
      } else {
        await this.updateSmartleadSyncStatus(entityId, 'failed', error)
      }

      // Save processing attempt for debugging and analytics
      await this.saveProcessingAttempt(
        'contact',
        entityId,
        'smartlead_sync',
        undefined, // usage
        undefined, // model
        undefined, // tokens
        success,
        error
      )

    } catch (updateError) {
      this.log('error', `Failed to update entity status for contact ${entityId}: ${updateError}`)
    }
  }

  /**
   * Update smartlead sync status
   */
  private async updateSmartleadSyncStatus(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET smartlead_status = $1,
          updated_at = NOW() 
      WHERE contact_id = $2
    `
    try {
      await this.query(sql, [status, contactId])
      this.log('debug', `Updated Smartlead sync status for contact ${contactId}: ${status}`)
    } catch (dbError) {
      this.log('error', `Error updating Smartlead sync status for contact ${contactId}: ${dbError}`)
    }
  }

  /**
   * Update smartlead lead ID when sync is successful
   */
  private async updateSmartleadLeadId(contactId: number, leadId: string): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET smartlead_lead_id = $1,
          updated_at = NOW() 
      WHERE contact_id = $2
    `
    try {
      await this.query(sql, [leadId, contactId])
      this.log('debug', `Updated Smartlead lead ID for contact ${contactId}: ${leadId}`)
    } catch (dbError) {
      this.log('error', `Error updating Smartlead lead ID for contact ${contactId}: ${dbError}`)
    }
  }

  /**
   * Get default processor-specific filters when no UI filters are provided
   */
  protected getDefaultProcessorFilters(entityType?: 'contact' | 'company'): Record<string, any> {
    return {
      // Only process contacts with completed email generation
      emailGenerationStatus: ['completed'],
      // Only process contacts that haven't been synced yet or have failed sync
      smartleadStatus: ['pending', 'failed', 'error']
    }
  }

  /**
   * Override the unified entities method to include smartlead-specific data
   * This method ensures proper filtering for Smartlead sync operations
   */
  async getUnprocessedUnifiedEntities(
    baseFilters: Record<string, any> = {},
    specificFilters: Record<string, any> = {},
    entityType?: 'contact' | 'company' | 'both'
  ): Promise<UnifiedEntityData[]> {
    try {
      // Merge base filters with Smartlead-specific requirements
      const smartleadBaseFilters = {
        ...baseFilters,
        // Ensure we only process contacts with valid emails
        notEmptyEmail: true,
        // Only process contacts that have completed email generation
        emailGenerationStatus: ['completed']
      }

      // Merge specific filters with Smartlead sync requirements
      const smartleadSpecificFilters = {
        ...specificFilters,
        // Default to processing contacts that haven't been synced yet
        // This can be overridden by specific filters if needed
        smartleadSyncStatus: specificFilters.smartleadSyncStatus || ['null', 'pending']
      }

      // Log campaign information if available
      if (this.options.campaignId) {
        this.log('info', `Processing Smartlead sync for campaign: ${this.options.campaignId}`)
      }

      this.log('debug', `Smartlead filters - Base: ${JSON.stringify(smartleadBaseFilters)}, Specific: ${JSON.stringify(smartleadSpecificFilters)}`)

      return await super.getUnprocessedUnifiedEntities(smartleadBaseFilters, smartleadSpecificFilters, 'contact')
    } catch (error) {
      this.log('error', `Error in getUnprocessedUnifiedEntities for Smartlead: ${error}`)
      throw error
    }
  }
}
