import { BaseProcessor } from './BaseProcessor'
import { EntityData, ProcessorOptions, UnifiedEntityData } from '../../types/processing'
import { LLMFactory, LLMMessage, createProcessorLog<PERSON>Adapter, LogLevel } from '../llm'
import { COMPANY_INVESTMENT_CRITERIA_SYSTEM_PROMPT, COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION } from '../prompts/company-investment-criteria'
import { processRegions as reconcileRegions, processValidMapping as cleanMapping } from '../validation/mappingReconciler'

interface InvestmentCriteriaData {
  investmentCriteria: Array<{
    // Deal Scope - Evidence-grounded structure
    capital_position: {
      value: string
      evidence: string
      confidence_score: number
      source_url: string
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    minimum_deal_size?: {
      value: number | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    maximum_deal_size?: {
      value: number | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    
    // Geography - Evidence-grounded structure
    country: {
      value: string[] | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    region: {
      value: string[] | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    state: {
      value: string[] | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    city: {
      value: string[] | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    
    // Asset Strategy - Evidence-grounded structure
    property_types: {
      value: string[] | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    property_subcategories: {
      value: string[] | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    strategies: {
      value: string[] | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    decision_making_process?: {
      value: string | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    
    // Additional Info - Evidence-grounded structure
    notes?: {
      value: string | null
      evidence: string | null
      confidence_score: number
      source_url: string | null
      sources: Array<{
        url: string
        page_section: string
        evidence_quote: string
        source_type: string
        date_found: string
      }>
    }
    
    // DEBT-SPECIFIC FIELDS - Evidence-grounded structure
    debt_criteria?: {
      closing_time?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      future_facilities?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      eligible_borrower?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      occupancy_requirements?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      lien_position?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      min_loan_dscr?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      max_loan_dscr?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      recourse_loan?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_min_debt_yield?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      prepayment?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      yield_maintenance?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      application_deposit?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      good_faith_deposit?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_origination_max_fee?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_origination_min_fee?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_exit_min_fee?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_exit_max_fee?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_interest_rate?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_interest_rate_based_off_sofr?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_interest_rate_based_off_wsj?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_interest_rate_based_off_prime?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_interest_rate_based_off_3yt?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_interest_rate_based_off_5yt?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_interest_rate_based_off_10yt?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_interest_rate_based_off_30yt?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      rate_lock?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      rate_type?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_to_value_max?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_to_value_min?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_to_cost_min?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_to_cost_max?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      debt_program_overview?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_type?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_type_normalized?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      structured_loan_tranche?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      loan_program?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      min_loan_term?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      max_loan_term?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      amortization?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
    }
    
    // EQUITY-SPECIFIC FIELDS - Evidence-grounded structure
    equity_criteria?: {
      target_return?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      minimum_internal_rate_of_return?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      minimum_yield_on_cost?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      minimum_equity_multiple?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      target_cash_on_cash_min?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      min_hold_period_years?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      max_hold_period_years?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      ownership_requirement?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      attachment_point?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      max_leverage_tolerance?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      typical_closing_timeline_days?: {
        value: number | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      proof_of_funds_requirement?: {
        value: boolean | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      equity_program_overview?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
      occupancy_requirements?: {
        value: string | null
        evidence: string | null
        confidence_score: number
        source_url: string | null
      }
    }
  }>
}

export class CompanyInvestmentCriteriaProcessor extends BaseProcessor {
  private llmProvider: any

  constructor(options: ProcessorOptions = {}) {
    // Gemini API specific rate limiting configuration for investment criteria
    const investmentCriteriaBottleneckConfig = {
      maxConcurrent: 15,                   // Higher concurrency for Gemini (faster API)
      minTime: 1000,                      // 1 second between requests (Gemini has generous rate limits)
      retryAttempts: 3,                   // Standard retries for LLM API
      retryDelayBase: 2000,               // 2 second base delay for retries
      retryDelayMax: 30000,               // Max 30 second retry delay
      timeout: 180000,                    // 3 minutes timeout for complex LLM processing
      highWater: 300,                     // Higher queue limit for faster API
      strategy: 'OVERFLOW' as any,        // Use OVERFLOW strategy
      defaultPriority: 6,                 // Lower priority for investment criteria
      enableJobMetrics: true              // Track LLM API performance
    }

    super('CompanyInvestmentCriteriaProcessor', {
      ...options,
      bottleneckConfig: options.bottleneckConfig || investmentCriteriaBottleneckConfig
    })
    
    // Create logger adapter for LLM provider
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this))
    
    // Use Gemini for cost-effective and fast investment criteria extraction
    // Benefits: 2x faster than Perplexity, 10x cheaper, excellent JSON structured output
    this.llmProvider = LLMFactory.createProvider(
      'gemini',
      loggerAdapter,
      {
        apiKey: process.env.GEMINI_API_KEY,
        defaultOptions: {
          model: 'gemini-1.5-flash-002' // Use the fast, cost-effective model
        }
      }
    )
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseFilters = {
      companyOverviewV2Status: 'completed'
    }

    const specificFilters = {
      companyInvestmentCriteriaStatus: 'pending'
    }

    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'company')
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Processing investment criteria for company ${entity.id}`)

      // Update status to running
      await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'running')

      // Get enrichment data from companies table (populated by CompanyOverviewProcessorV2)
      const enrichmentData = await this.getCompanyEnrichmentData(entity.id)

      // Build related URLs (curated) and overview summary via shared base helper
      const { relatedUrls, overviewSummary } = await this.getCompanyOverviewSummaryAndUrls(entity.id)
      // Use shared helper if available; fallback to empty array to avoid runtime errors
      const overviewSeedSources = typeof (this as any).getCompanyOverviewSourceUrls === 'function'
        ? await (this as any).getCompanyOverviewSourceUrls(entity.id)
        : []

      // Get central mappings for allowed values
      const mappings = await this.getCentralMappings()

      // Get page metadata from web crawling (NEW: Include metadata in investment criteria)
      const pageMetadata = await this.getPageMetadataForCompany(entity.id)
      this.log('debug', `Retrieved ${pageMetadata.length} pages with metadata for company ${entity.id}`)

      // Format page metadata for inclusion in prompt
      const metadataContent = this.formatPageMetadataForPrompt(pageMetadata)
      
      // Create the user prompt with enrichment data and metadata
      const userPrompt = COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION(
        {
          company_name: (entity as any).company_name || 'Unknown Company',
          company_website: (entity as any).company_website || '',
          industry: (entity as any).industry
        },
        mappings,
        enrichmentData, // still passed for context fields
        relatedUrls,
        overviewSummary,
        overviewSeedSources,
        metadataContent // NEW: Pass metadata directly to the template
      )

      this.log('debug', `Generated user prompt for company ${entity.id} with enrichment data`)

      // Call Gemini for investment criteria extraction
      const messages: LLMMessage[] = [
        { role: 'system', content: COMPANY_INVESTMENT_CRITERIA_SYSTEM_PROMPT },
        { role: 'user', content: userPrompt }
      ]
      
      const response = await this.llmProvider.callLLM(messages, {
        maxTokens: 8192, // Gemini Flash max output tokens
        temperature: 0.1, // Low temperature for consistent, accurate extraction
        topP: 0.9,
        topK: 40, // Gemini-specific parameter for token selection diversity
        candidateCount: 1 // Single response for consistency
      })

      if (!response || !response.content) {
        const error = 'Empty response from LLM'
        await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'failed', error)
        return { success: false, error }
      }

      // Parse the JSON response
      let extractedData: InvestmentCriteriaData
      try {
        extractedData = JSON.parse(response.content)
      } catch (parseError) {
        const error = `Failed to parse LLM response as JSON: ${parseError}`
        this.log('error', error)
        await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'failed', error)
        return { success: false, error }
      }

      // Validate extracted data
      if (!extractedData.investmentCriteria || !Array.isArray(extractedData.investmentCriteria)) {
        const error = 'Invalid investment criteria data structure'
        await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'failed', error)
        return { success: false, error }
      }

      // Reconcile mapped fields against allowed mappings BEFORE saving
      try {
        const reconciled = extractedData.investmentCriteria.map((crit: any) => {
          const copy = JSON.parse(JSON.stringify(crit))

          // Regions + States
          const regionsArr: string[] = Array.isArray(copy.region?.value) ? copy.region.value : []
          const statesArr: string[] = Array.isArray(copy.state?.value) ? copy.state.value : []
          const { regions, states } = reconcileRegions(regionsArr, statesArr, (mappings as any).US_REGIONS_MAP || {})
          if (copy.region) copy.region.value = regions
          if (copy.state) copy.state.value = states

          // Property Types
          const propTypes: string[] = Array.isArray(copy.property_types?.value) ? copy.property_types.value : []
          const allowedPropTypes: string[] = Array.isArray((mappings as any).PROPERTY_TYPES) ? (mappings as any).PROPERTY_TYPES : []
          const cleanedPropTypes = cleanMapping(propTypes, allowedPropTypes)
          if (copy.property_types) copy.property_types.value = cleanedPropTypes

          // Property Subcategories constrained by selected types
          const subcats: string[] = Array.isArray(copy.property_subcategories?.value) ? copy.property_subcategories.value : []
          const typeToSub: Record<string, string[]> = (mappings as any).PROPERTY_TYPE_SUBCATEGORY_MAP || {}
          const allowedSubSet = new Set<string>()
          for (const t of cleanedPropTypes) {
            const list = Array.isArray(typeToSub[t]) ? typeToSub[t] : []
            list.forEach((s: string) => { if (typeof s === 'string') allowedSubSet.add(s) })
          }
          const allowedSubcats = Array.from(allowedSubSet)
          const cleanedSubcats = cleanMapping(subcats, allowedSubcats)
          if (copy.property_subcategories) copy.property_subcategories.value = cleanedSubcats

          // Strategies
          const strategiesArr: string[] = Array.isArray(copy.strategies?.value) ? copy.strategies.value : []
          const allowedStrategies: string[] = Array.isArray((mappings as any).STRATEGIES) ? (mappings as any).STRATEGIES : []
          const cleanedStrategies = cleanMapping(strategiesArr, allowedStrategies)
          if (copy.strategies) copy.strategies.value = cleanedStrategies

          // Capital Position (single value)
          const capitalPositions: string[] = Array.isArray((mappings as any).CAPITAL_POSITIONS) ? (mappings as any).CAPITAL_POSITIONS : []
          if (copy.capital_position && typeof copy.capital_position.value === 'string') {
            const cleaned = cleanMapping([copy.capital_position.value], capitalPositions)
            copy.capital_position.value = cleaned.length > 0 ? cleaned[0] : null
          }

          // Debt-specific mapped string fields
          if (copy.debt_criteria) {
            const debt = copy.debt_criteria
            const recourseAllowed: string[] = Array.isArray((mappings as any).RECOURSE_LOAN_TYPES) ? (mappings as any).RECOURSE_LOAN_TYPES : []
            const loanTypesAllowed: string[] = Array.isArray((mappings as any).LOAN_TYPES) ? (mappings as any).LOAN_TYPES : []
            const tranchesAllowed: string[] = Array.isArray((mappings as any).STRUCTURED_LOAN_TRANCHES) ? (mappings as any).STRUCTURED_LOAN_TRANCHES : []
            const programsAllowed: string[] = Array.isArray((mappings as any).LOAN_PROGRAMS) ? (mappings as any).LOAN_PROGRAMS : []

            if (typeof debt.recourse_loan?.value === 'string') {
              const cleaned = cleanMapping([debt.recourse_loan.value], recourseAllowed)
              debt.recourse_loan.value = cleaned.length > 0 ? cleaned[0] : null
            }
            if (typeof debt.loan_type?.value === 'string') {
              const cleaned = cleanMapping([debt.loan_type.value], loanTypesAllowed)
              debt.loan_type.value = cleaned.length > 0 ? cleaned[0] : null
            }
            if (typeof debt.loan_type_normalized?.value === 'string') {
              const cleaned = cleanMapping([debt.loan_type_normalized.value], loanTypesAllowed)
              debt.loan_type_normalized.value = cleaned.length > 0 ? cleaned[0] : null
            }
            if (typeof debt.structured_loan_tranche?.value === 'string') {
              const cleaned = cleanMapping([debt.structured_loan_tranche.value], tranchesAllowed)
              debt.structured_loan_tranche.value = cleaned.length > 0 ? cleaned[0] : null
            }
            if (typeof debt.loan_program?.value === 'string') {
              const cleaned = cleanMapping([debt.loan_program.value], programsAllowed)
              debt.loan_program.value = cleaned.length > 0 ? cleaned[0] : null
            }
          }

          return copy
        })
        extractedData.investmentCriteria = reconciled
      } catch (reconErr) {
        this.log('warn', `Reconciliation warning: ${reconErr}`)
      }

      // Save investment criteria to database
      const saved = await this.saveInvestmentCriteria(entity.id, extractedData.investmentCriteria)
      if (!saved) {
        const error = 'Failed to save investment criteria to database'
        await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'failed', error)
        return { success: false, error }
      }

      // Save processing attempt information
      await this.saveProcessingAttempt(
        'company',
        entity.id,
        'company_investment_criteria',
        response.usage,
        response.model,
        response.usage?.total_tokens,
        true
      )

      // Update status to completed
      await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'completed')

      this.log('info', `Successfully processed investment criteria for company ${entity.id}`)
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error processing investment criteria for company ${entity.id}: ${errorMessage}`)

      await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'error', errorMessage)
      await this.incrementProcessingErrorCount('company', entity.id)

      // Save failed processing attempt
      await this.saveProcessingAttempt(
        'company',
        entity.id,
        'company_investment_criteria',
        undefined,
        undefined,
        undefined,
        false,
        errorMessage
      )

      return { success: false, error: errorMessage }
    }
  }

  // Removed local duplicate: using BaseProcessor.getCompanyOverviewSummaryAndUrls

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      await this.updateCompanyInvestmentCriteriaStatus(entityId, 'completed')
    } else {
      await this.updateCompanyInvestmentCriteriaStatus(entityId, 'failed', error)
    }
  }

  /**
   * Update company investment criteria extraction status
   */
  protected async updateCompanyInvestmentCriteriaStatus(
    companyId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET investment_criteria_status = $1::text, 
          investment_criteria_error = $2,
          investment_criteria_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE investment_criteria_date END,
          updated_at = NOW() 
      WHERE company_id = $3
    `
    try {
      await this.query(sql, [status, error || null, companyId])
      this.log('debug', `Updated investment criteria status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating investment criteria status for company ${companyId}: ${error}`)
    }
  }



  /**
   * Get page metadata for a company from the web crawling system
   */
  private async getPageMetadataForCompany(companyId: number): Promise<any[]> {
    const sql = `
      SELECT 
        url,
        extracted_text,
        relevance_rank,
        page_metadata
      FROM company_web_pages 
      WHERE company_id = $1 
        AND page_metadata IS NOT NULL 
        AND page_metadata != ''
      ORDER BY relevance_rank ASC 
      LIMIT 20
    `;
    
    try {
      const result = await this.query(sql, [companyId]);
      return Array.isArray(result) ? result : [];
    } catch (error) {
      this.log('warn', `Failed to retrieve page metadata for company ${companyId}: ${error}`);
      return [];
    }
  }

  /**
   * Format page metadata for inclusion in investment criteria prompt
   */
  private formatPageMetadataForPrompt(pageMetadata: any[]): string {
    if (!pageMetadata || pageMetadata.length === 0) {
      return '';
    }

    let metadataContent = `\n\n## PAGE METADATA FROM WEB CRAWLING ANALYSIS\n\n`;
    metadataContent += `**Analysis Summary:**\n`;
    metadataContent += `- Total pages with metadata: ${pageMetadata.length}\n`;
    metadataContent += `- All pages contain structured YAML metadata with investment criteria, financial data, and entity information\n\n`;

    // Show top 10 pages with their raw YAML metadata
    pageMetadata.slice(0, 10).forEach((page, index) => {
      metadataContent += `**Page ${index + 1}:** ${page.url} (Rank: ${page.relevance_rank})\n`;
      
      if (page.page_metadata) {
        metadataContent += `- **Raw Metadata (YAML):**\n\`\`\`yaml\n${page.page_metadata}\n\`\`\`\n`;
      } else {
        metadataContent += `- **No Metadata:** Page metadata not available\n`;
      }
      
      // Include a sample of the content
      if (page.extracted_text && page.extracted_text.length > 0) {
        metadataContent += `- **Content Sample:** ${page.extracted_text.substring(0, 300)}...\n`;
      }
      
      metadataContent += `\n---\n\n`;
    });

    metadataContent += `**Instructions for Using This Metadata:**\n`;
    metadataContent += `- Use the extracted data values (amounts, rates, locations, etc.) from the YAML as primary evidence\n`;
    metadataContent += `- Consider the entity roles to understand who provides vs. receives\n`;
    metadataContent += `- Use the classification categories and confidence levels to assess data quality\n`;
    metadataContent += `- Always cite metadata as source_type "page_metadata" and include the URL\n\n`;

    return metadataContent;
  }

  /**
   * Get central mappings for allowed values with nested property structure
   */
  private async getCentralMappings(): Promise<Record<string, any>> {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3030'
      const url = `${baseUrl}/api/mapping-tables/consolidated`
      const resp = await fetch(url, { method: 'GET', headers: { 'Content-Type': 'application/json' } })
      if (!resp.ok) throw new Error(`Consolidated mapping API failed: ${resp.status}`)
      const json = await resp.json()
      const data = json?.data || {}

      // Transform to the prompt's allowed-values format
      const allowed: Record<string, any> = {}

      // Flat arrays
      allowed['STRATEGIES'] = Array.isArray(data.strategies) ? data.strategies : []
      allowed['LOAN_PROGRAMS'] = Array.isArray(data.loan_program) ? data.loan_program : []
      allowed['LOAN_TYPES'] = Array.isArray(data.loan_type) ? data.loan_type : []
      allowed['STRUCTURED_LOAN_TRANCHES'] = Array.isArray(data.structured_loan_tranches) ? data.structured_loan_tranches : []
      allowed['RECOURSE_LOAN_TYPES'] = Array.isArray(data.recourse_loan) ? data.recourse_loan : []
      allowed['CAPITAL_POSITIONS'] = Array.isArray(data.capital_position) ? data.capital_position : []

      // Property types and nested subcategories
      const propertyTypeMap = data.property_type_map || {}
      const propertyTypes = Object.keys(propertyTypeMap)
      allowed['PROPERTY_TYPES'] = propertyTypes
      allowed['PROPERTY_TYPE_SUBCATEGORY_MAP'] = propertyTypeMap

      // Regions with nested states
      const regionsObj = data.us_regions || {}
      allowed['US_REGIONS'] = Object.keys(regionsObj)
      allowed['US_REGIONS_MAP'] = regionsObj

      // this.log('debug', `Loaded consolidated mappings: ${JSON.stringify(allowed, null, 2)}`)
      return allowed
    } catch (error) {
      this.log('error', `Error fetching mappings via API: ${error}`)
      return {}
    }
  }

  /**
   * Get company enrichment data from companies table (populated by CompanyOverviewProcessorV2)
   */
  private async getCompanyEnrichmentData(companyId: number): Promise<any> {
    try {
      const sql = `
        SELECT 
          company_name,
          company_type,
          industry,
          business_model,
          founded_year,
          investment_focus,
          investment_strategy_mission,
          investment_strategy_approach,
          company_website,
          company_phone,
          secondary_phone,
          main_email,
          secondary_email,
          company_linkedin,
          twitter,
          facebook,
          instagram,
          youtube,
          company_address,
          company_city,
          company_state,
          company_zip,
          company_country,
          additional_address,
          additional_city,
          additional_state,
          additional_zipcode,
          additional_country,
          office_locations,
          fund_size,
          aum,
          number_of_properties,
          number_of_offices,
          number_of_employees,
          annual_revenue,
          net_income,
          ebitda,
          profit_margin,
          market_capitalization,
          market_share_percentage,
          balance_sheet_strength,
          funding_sources,
          recent_capital_raises,
          typical_debt_to_equity_ratio,
          development_fee_structure,
          credit_rating,
          dry_powder,
          annual_deployment_target,
          investment_vehicle_type,
          active_fund_name_series,
          fund_size_active_fund,
          fundraising_status,
          lender_type,
          annual_loan_volume,
          lending_origin,
          portfolio_health,
          partnerships,
          key_equity_partners,
          key_debt_partners,
          board_of_directors,
          key_executives,
          founder_background,
          market_cycle_positioning,
          urban_vs_suburban_preference,
          sustainability_esg_focus,
          technology_proptech_adoption,
          adaptive_reuse_experience,
          regulatory_zoning_expertise,
          corporate_structure,
          parent_company,
          subsidiaries,
          stock_ticker_symbol,
          stock_exchange,
          products_services_description,
          target_customer_profile,
          major_competitors,
          unique_selling_proposition,
          industry_awards_recognitions,
          company_history,
          transactions_completed_last_12m,
          total_transaction_volume_ytd,
          deal_count_ytd,
          average_deal_size,
          portfolio_size_sqft,
          portfolio_asset_count,
          role_in_previous_deal,
          internal_relationship_manager,
          last_contact_date,
          pipeline_status,
          recent_news_sentiment,
          data_source,
          data_confidence_score,
          quarterly_earnings_link
        FROM companies
        WHERE company_id = $1
      `
      const result = await this.query(sql, [companyId])

      if (result.length === 0) {
        this.log('warn', `No enrichment data found for company ${companyId}`)
        return null
      }

      const enrichmentData = result[0]
      
      // Convert PostgreSQL arrays to JavaScript arrays
      const arrayFields = [
        'investment_focus', 'office_locations', 'funding_sources', 'partnerships',
        'key_equity_partners', 'key_debt_partners', 'board_of_directors', 'key_executives',
        'subsidiaries', 'major_competitors', 'industry_awards_recognitions'
      ]
      
      arrayFields.forEach(field => {
        if (enrichmentData[field] && typeof enrichmentData[field] === 'string') {
          try {
            // Parse PostgreSQL array format {item1,item2,item3}
            const arrayString = enrichmentData[field] as string
            if (arrayString.startsWith('{') && arrayString.endsWith('}')) {
              const items = arrayString.slice(1, -1).split(',').map(item => item.trim().replace(/"/g, ''))
              enrichmentData[field] = items.filter(item => item !== '')
            }
          } catch (error) {
            this.log('warn', `Error parsing array field ${field}: ${error}`)
            enrichmentData[field] = []
          }
        }
      })

      this.log('debug', `Retrieved enrichment data for company ${companyId} with ${Object.keys(enrichmentData).length} fields`)
      return enrichmentData
    } catch (error) {
      this.log('error', `Error fetching company enrichment data for ${companyId}: ${error}`)
      return null
    }
  }

  /**
   * Convert JavaScript array to PostgreSQL array literal format
   */
  private arrayToPostgresArray(arr: string[] | null | undefined): string | null {
    if (!arr || arr.length === 0) {
      return null
    }
    
    // Escape quotes and wrap values that contain spaces or special characters
    const escapedValues = arr.map(value => {
      if (typeof value !== 'string') {
        return String(value)
      }
      
      // If value contains spaces, commas, quotes, or special characters, wrap in quotes and escape
      if (value.includes(' ') || value.includes(',') || value.includes('"') || value.includes('\'') || value.includes('{') || value.includes('}')) {
        return `"${value.replace(/"/g, '\\"')}"` 
      }
      
      return value
    })
    
    return `{${escapedValues.join(',')}}`
  }

  /**
   * Validate and normalize financial metrics with guardrails
   */
  private validateFinancialMetrics(criteria: any): any {
    const validatedCriteria = { ...criteria }

    // Apply LTV/LTC guardrails (50-100)
    if (validatedCriteria.debt_criteria) {
      const debt = validatedCriteria.debt_criteria
      
      // LTV Max guardrail
      if (debt.loan_to_value_max?.value !== null && debt.loan_to_value_max?.value !== undefined) {
        const originalValue = debt.loan_to_value_max.value
        if (originalValue > 100) {
          debt.loan_to_value_max.value = 100
          debt.loan_to_value_max.evidence = debt.loan_to_value_max.evidence || ''
          debt.loan_to_value_max.evidence += ` [Original: ${originalValue}%, capped at 100% per guardrails]`
        } else if (originalValue < 50) {
          debt.loan_to_value_max.value = 50
          debt.loan_to_value_max.evidence = debt.loan_to_value_max.evidence || ''
          debt.loan_to_value_max.evidence += ` [Original: ${originalValue}%, capped at 50% per guardrails]`
        }
      }

      // LTV Min guardrail
      if (debt.loan_to_value_min?.value !== null && debt.loan_to_value_min?.value !== undefined) {
        const originalValue = debt.loan_to_value_min.value
        if (originalValue > 100) {
          debt.loan_to_value_min.value = 100
          debt.loan_to_value_min.evidence = debt.loan_to_value_min.evidence || ''
          debt.loan_to_value_min.evidence += ` [Original: ${originalValue}%, capped at 100% per guardrails]`
        } else if (originalValue < 50) {
          debt.loan_to_value_min.value = 50
          debt.loan_to_value_min.evidence = debt.loan_to_value_min.evidence || ''
          debt.loan_to_value_min.evidence += ` [Original: ${originalValue}%, capped at 50% per guardrails]`
        }
      }

      // LTC Max guardrail
      if (debt.loan_to_cost_max?.value !== null && debt.loan_to_cost_max?.value !== undefined) {
        const originalValue = debt.loan_to_cost_max.value
        if (originalValue > 100) {
          debt.loan_to_cost_max.value = 100
          debt.loan_to_cost_max.evidence = debt.loan_to_cost_max.evidence || ''
          debt.loan_to_cost_max.evidence += ` [Original: ${originalValue}%, capped at 100% per guardrails]`
        } else if (originalValue < 50) {
          debt.loan_to_cost_max.value = 50
          debt.loan_to_cost_max.evidence = debt.loan_to_cost_max.evidence || ''
          debt.loan_to_cost_max.evidence += ` [Original: ${originalValue}%, capped at 50% per guardrails]`
        }
      }

      // LTC Min guardrail
      if (debt.loan_to_cost_min?.value !== null && debt.loan_to_cost_min?.value !== undefined) {
        const originalValue = debt.loan_to_cost_min.value
        if (originalValue > 100) {
          debt.loan_to_cost_min.value = 100
          debt.loan_to_cost_min.evidence = debt.loan_to_cost_min.evidence || ''
          debt.loan_to_cost_min.evidence += ` [Original: ${originalValue}%, capped at 100% per guardrails]`
        } else if (originalValue < 50) {
          debt.loan_to_cost_min.value = 50
          debt.loan_to_cost_min.evidence = debt.loan_to_cost_min.evidence || ''
          debt.loan_to_cost_min.evidence += ` [Original: ${originalValue}%, capped at 50% per guardrails]`
        }
      }

      // DSCR Min guardrail (must be >= 1.0)
      if (debt.min_loan_dscr?.value !== null && debt.min_loan_dscr?.value !== undefined) {
        const originalValue = debt.min_loan_dscr.value
        if (originalValue < 1.0) {
          debt.min_loan_dscr.value = 1.0
          debt.min_loan_dscr.evidence = debt.min_loan_dscr.evidence || ''
          debt.min_loan_dscr.evidence += ` [Original: ${originalValue}, capped at 1.0 per guardrails]`
        }
      }
    }

    return validatedCriteria
  }

  /**
   * Save investment criteria to database with evidence-grounded structure
   */
  private async saveInvestmentCriteria(companyId: number, criteriaList: any[]): Promise<boolean> {
    const client = await this.pool.connect()
    try {
      await client.query('BEGIN')

      // Do not delete existing investment criteria for this company.
      // New criteria will be appended as additional rows linked to the same entity.

      // Insert new investment criteria
      for (const criteria of criteriaList) {
        // Validate and normalize financial metrics with guardrails
        const validatedCriteria = this.validateFinancialMetrics(criteria)
        
        // Extract values from evidence-grounded structure
        const capitalPosition = validatedCriteria.capital_position?.value || null
        const minimumDealSize = validatedCriteria.minimum_deal_size?.value || null
        const maximumDealSize = validatedCriteria.maximum_deal_size?.value || null
        const country = validatedCriteria.country?.value || null
        const region = validatedCriteria.region?.value || null
        const state = validatedCriteria.state?.value || null
        const city = validatedCriteria.city?.value || null
        const propertyTypes = validatedCriteria.property_types?.value || null
        const propertySubcategories = validatedCriteria.property_subcategories?.value || null
        const strategies = validatedCriteria.strategies?.value || null
        const decisionMakingProcess = validatedCriteria.decision_making_process?.value || null
        const notes = validatedCriteria.notes?.value || null

        // Consolidate all sources from all fields into a single sources array
        const allSources: Array<{
          field: string;
          url: string;
          page_section: string;
          evidence_quote: string;
          source_type: string;
          date_found: string;
          confidence_score: number;
        }> = [];

        // Helper function to add sources from a field
        const addFieldSources = (fieldName: string, fieldData: any) => {
          if (fieldData?.sources && Array.isArray(fieldData.sources)) {
            fieldData.sources.forEach((source: any) => {
              allSources.push({
                field: fieldName,
                url: source.url || '',
                page_section: source.page_section || '',
                evidence_quote: source.evidence_quote || '',
                source_type: source.source_type || '',
                date_found: source.date_found || new Date().toISOString().split('T')[0],
                confidence_score: fieldData.confidence_score || 0
              });
            });
          }
        };

        // Collect sources from all fields
        addFieldSources('capital_position', validatedCriteria.capital_position);
        addFieldSources('minimum_deal_size', validatedCriteria.minimum_deal_size);
        addFieldSources('maximum_deal_size', validatedCriteria.maximum_deal_size);
        addFieldSources('country', validatedCriteria.country);
        addFieldSources('region', validatedCriteria.region);
        addFieldSources('state', validatedCriteria.state);
        addFieldSources('city', validatedCriteria.city);
        addFieldSources('property_types', validatedCriteria.property_types);
        addFieldSources('property_subcategories', validatedCriteria.property_subcategories);
        addFieldSources('strategies', validatedCriteria.strategies);
        addFieldSources('decision_making_process', validatedCriteria.decision_making_process);
        addFieldSources('notes', validatedCriteria.notes);

        // Also collect sources from debt criteria if present
        if (validatedCriteria.debt_criteria) {
          Object.entries(validatedCriteria.debt_criteria).forEach(([fieldName, fieldData]) => {
            addFieldSources(`debt_${fieldName}`, fieldData);
          });
        }

        // Also collect sources from equity criteria if present
        if (validatedCriteria.equity_criteria) {
          Object.entries(validatedCriteria.equity_criteria).forEach(([fieldName, fieldData]) => {
            addFieldSources(`equity_${fieldName}`, fieldData);
          });
        }

        // Remove duplicates based on URL and evidence_quote
        const uniqueSources = allSources.filter((source, index, self) => 
          index === self.findIndex(s => s.url === source.url && s.evidence_quote === source.evidence_quote)
        );

        // Sort sources by source_type priority (following prompt hierarchy)
        const sourceTypePriority = {
          'page_metadata': 1,        // HIGHEST PRIORITY: Page metadata from semantic web analysis
          'web_scraped_text': 2,     // SECOND PRIORITY: Web scraped text from company websites
          'enrichment_data': 3,      // THIRD PRIORITY: Enrichment data (structured, verified data)
          'company_website': 4,      // FOURTH PRIORITY: Company website content
          'news_article': 5,         // FIFTH PRIORITY: News articles
          'press_release': 6,        // SIXTH PRIORITY: Press releases
          'web_search': 7            // Additional: Web search results
        };

        uniqueSources.sort((a, b) => {
          const aPriority = sourceTypePriority[a.source_type as keyof typeof sourceTypePriority] || 8;
          const bPriority = sourceTypePriority[b.source_type as keyof typeof sourceTypePriority] || 8;
          return aPriority - bPriority;
        });

        // Prepare consolidated sources data for storage
        const consolidatedSources = {
          all_sources: uniqueSources,
          total_sources: uniqueSources.length,
          source_types: [...new Set(uniqueSources.map(s => s.source_type))],
          fields_with_sources: [...new Set(uniqueSources.map(s => s.field))],
          extraction_date: new Date().toISOString()
        };

        // Insert into investment_criteria_central
        const centralSql = `
          INSERT INTO investment_criteria_central (
            entity_id, entity_type, investment_criteria_debt_id, investment_criteria_equity_id,
            capital_position, minimum_deal_size, maximum_deal_size,
            country, region, state, city, property_types, property_subcategories, 
            strategies, decision_making_process, notes, sources, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
          RETURNING investment_criteria_id
        `

        const centralResult = await client.query(centralSql, [
          companyId,
          'company',
          null, // investment_criteria_debt_id - NULL for company records
          null, // investment_criteria_equity_id - NULL for company records
          capitalPosition,
          minimumDealSize,
          maximumDealSize,
          this.arrayToPostgresArray(country),
          this.arrayToPostgresArray(region),
          this.arrayToPostgresArray(state),
          this.arrayToPostgresArray(city),
          this.arrayToPostgresArray(propertyTypes),
          this.arrayToPostgresArray(propertySubcategories),
          this.arrayToPostgresArray(strategies),
          decisionMakingProcess,
          notes,
          JSON.stringify(consolidatedSources), // Use consolidated sources instead of evidenceData
          new Date(),
          new Date()
        ])

        const criteriaId = centralResult.rows[0].investment_criteria_id

        // Determine if this is a debt or equity position based on capital_position
        const isDebtPosition = ['Senior Debt', 'Stretch Senior', 'Mezzanine'].includes(capitalPosition)
        const isEquityPosition = ['Preferred Equity', 'Common Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP'].includes(capitalPosition)
        const isOtherPosition = ['Third Party', 'Undetectable'].includes(capitalPosition)

        // Insert debt criteria ONLY for debt positions
        if (validatedCriteria.debt_criteria && isDebtPosition) {
          const debt = validatedCriteria.debt_criteria
          
          // Prepare debt evidence data with multi-level source structure
          const debtEvidenceData: Record<string, any> = {}
          for (const [key, value] of Object.entries(debt)) {
            if (value && typeof value === 'object' && 'evidence' in value) {
              const evidenceValue = value as any
              debtEvidenceData[key] = {
                evidence: evidenceValue.evidence,
                confidence_score: evidenceValue.confidence_score,
                source_url: evidenceValue.source_url,
                sources: evidenceValue.sources || []
              }
            }
          }

          const debtSql = `
            INSERT INTO investment_criteria_debt (
              investment_criteria_id, notes, closing_time, future_facilities, eligible_borrower,
              occupancy_requirements, lien_position, min_loan_dscr, max_loan_dscr, recourse_loan,
              loan_min_debt_yield, prepayment, yield_maintenance, application_deposit, good_faith_deposit,
              loan_origination_max_fee, loan_origination_min_fee, loan_exit_min_fee, loan_exit_max_fee,
              loan_interest_rate, loan_interest_rate_based_off_sofr, loan_interest_rate_based_off_wsj,
              loan_interest_rate_based_off_prime, loan_interest_rate_based_off_3yt, loan_interest_rate_based_off_5yt,
              loan_interest_rate_based_off_10yt, loan_interest_rate_based_off_30yt, rate_lock, rate_type,
              loan_to_value_max, loan_to_value_min, loan_to_cost_min, loan_to_cost_max,
              debt_program_overview, loan_type, loan_type_normalized, structured_loan_tranche,
              loan_program, min_loan_term, max_loan_term, amortization, created_at, updated_at
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19,
              $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43
            )
            RETURNING investment_criteria_debt_id
          `

          const debtResult = await client.query(debtSql, [
            criteriaId, 
            debt.notes?.value || null, 
            debt.closing_time?.value || null, 
            debt.future_facilities?.value || null,
            debt.eligible_borrower?.value || null, 
            debt.occupancy_requirements?.value || null, 
            debt.lien_position?.value || null,
            debt.min_loan_dscr?.value || null, 
            debt.max_loan_dscr?.value || null, 
            debt.recourse_loan?.value || null,
            debt.loan_min_debt_yield?.value || null, 
            debt.prepayment?.value || null, 
            debt.yield_maintenance?.value || null,
            debt.application_deposit?.value || null, 
            debt.good_faith_deposit?.value || null, 
            debt.loan_origination_max_fee?.value || null,
            debt.loan_origination_min_fee?.value || null, 
            debt.loan_exit_min_fee?.value || null, 
            debt.loan_exit_max_fee?.value || null,
            debt.loan_interest_rate?.value || null, 
            debt.loan_interest_rate_based_off_sofr?.value || null,
            debt.loan_interest_rate_based_off_wsj?.value || null, 
            debt.loan_interest_rate_based_off_prime?.value || null,
            debt.loan_interest_rate_based_off_3yt?.value || null, 
            debt.loan_interest_rate_based_off_5yt?.value || null,
            debt.loan_interest_rate_based_off_10yt?.value || null, 
            debt.loan_interest_rate_based_off_30yt?.value || null,
            debt.rate_lock?.value || null, 
            debt.rate_type?.value || null, 
            debt.loan_to_value_max?.value || null,
            debt.loan_to_value_min?.value || null, 
            debt.loan_to_cost_min?.value || null, 
            debt.loan_to_cost_max?.value || null,
            debt.debt_program_overview?.value || null, 
            debt.loan_type?.value || null, 
            debt.loan_type_normalized?.value || null,
            debt.structured_loan_tranche?.value || null, 
            debt.loan_program?.value || null, 
            debt.min_loan_term?.value || null,
            debt.max_loan_term?.value || null, 
            debt.amortization?.value || null, 
            new Date(), 
            new Date()
          ])
          const debtId = debtResult.rows[0].investment_criteria_debt_id
          
          // Update investment_criteria_central with the debt_id
          await client.query(
            'UPDATE investment_criteria_central SET investment_criteria_debt_id = $1 WHERE investment_criteria_id = $2',
            [debtId, criteriaId]
          )
        }

        // Insert equity criteria ONLY for equity positions
        if (validatedCriteria.equity_criteria && isEquityPosition) {
          const equity = validatedCriteria.equity_criteria
          
          // Prepare equity evidence data with multi-level source structure
          const equityEvidenceData: Record<string, any> = {}
          for (const [key, value] of Object.entries(equity)) {
            if (value && typeof value === 'object' && 'evidence' in value) {
              const evidenceValue = value as any
              equityEvidenceData[key] = {
                evidence: evidenceValue.evidence,
                confidence_score: evidenceValue.confidence_score,
                source_url: evidenceValue.source_url,
                sources: evidenceValue.sources || []
              }
            }
          }

          const equitySql = `
            INSERT INTO investment_criteria_equity (
              investment_criteria_id, target_return, minimum_internal_rate_of_return, minimum_yield_on_cost,
              minimum_equity_multiple, target_cash_on_cash_min, min_hold_period_years, max_hold_period_years,
              ownership_requirement, attachment_point, max_leverage_tolerance, typical_closing_timeline_days,
              proof_of_funds_requirement, notes, equity_program_overview, occupancy_requirements, created_at, updated_at
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
            )
            RETURNING investment_criteria_equity_id
          `

          const equityResult = await client.query(equitySql, [
            criteriaId, 
            equity.target_return?.value || null, 
            equity.minimum_internal_rate_of_return?.value || null,
            equity.minimum_yield_on_cost?.value || null, 
            equity.minimum_equity_multiple?.value || null,
            equity.target_cash_on_cash_min?.value || null, 
            equity.min_hold_period_years?.value || null,
            equity.max_hold_period_years?.value || null, 
            equity.ownership_requirement?.value || null,
            equity.attachment_point?.value || null, 
            equity.max_leverage_tolerance?.value || null,
            equity.typical_closing_timeline_days?.value || null, 
            equity.proof_of_funds_requirement?.value || null,
            equity.notes?.value || null, 
            equity.equity_program_overview?.value || null, 
            equity.occupancy_requirements?.value || null,
            new Date(), 
            new Date()
          ])

          // Update investment_criteria_central with the equity_id
          const equityId = equityResult.rows[0].investment_criteria_equity_id
          await client.query(
            'UPDATE investment_criteria_central SET investment_criteria_equity_id = $1 WHERE investment_criteria_id = $2',
            [equityId, criteriaId]
          )
        }

        // For other positions (Third Party, Undetectable), keep both debt_id and equity_id as NULL
        // This ensures they only exist in the central table
      }

      await client.query('COMMIT')
      this.log('info', `Saved ${criteriaList.length} investment criteria records for company ${companyId}`)
      return true

    } catch (error) {
      await client.query('ROLLBACK')
      this.log('error', `Error saving investment criteria for company ${companyId}: ${error}`)
      return false
    } finally {
      client.release()
    }
  }
}
