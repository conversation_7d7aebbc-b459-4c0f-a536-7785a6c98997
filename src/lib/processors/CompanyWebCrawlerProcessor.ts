import { BaseProcessor } from './BaseProcessor';
import { ProcessorOptions, UnifiedEntityData } from '../../types/processing';
import { crawlWebsiteComprehensive } from '../utils/fetchwebsite';
import { LLMFactory, createProcessorLoggerAdapter } from '../llm';

interface ProcessorOptionsExt extends ProcessorOptions {
  concurrency?: number;
}

/**
 * CompanyWebCrawlerProcessor
 * 
 * Crawls company websites to extract ALL content for further processing.
 * - Processes companies in batches
 * - Uses concurrent browser contexts
 * - Stores raw HTML and extracted text in company_web_pages
 * - Crawls ALL discoverable pages with intelligent ranking
 * - No artificial limits on pages or depth
 */
export class CompanyWebCrawlerProcessor extends BaseProcessor {
  // Configuration - removed artificial limits for comprehensive crawling
  private readonly MAX_DEPTH = 10; // Higher depth limit for thorough crawling
  private readonly MAX_PAGES = 1000; // Very high limit to capture all relevant content
  
  // Runtime state
  private activePromises: Promise<unknown>[] = [];
  private stats = {
    pagesCrawled: 0,
    errors: 0,
    startTime: Date.now()
  };
  
  // LLM provider for metadata classification
  private llmProvider: any;

  constructor(options: ProcessorOptionsExt = {}) {
    super('CompanyWebCrawler', options);
    
    // Initialize LLM provider for metadata classification
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    this.llmProvider = LLMFactory.createProvider(
      'gemini',
      loggerAdapter,
      {
        enableTracing: true, // Explicitly enable tracing
        defaultOptions: {
          model: 'gemini-2.5-flash',
          maxTokens: 3000
        }
      }
    );
    
    // Log tracing status
    this.log('info', `🔍 LLM Provider initialized: ${this.llmProvider.getName()}`);
    this.log('info', `🔍 Tracing enabled: ${this.llmProvider.isTracingEnabledStatus?.() || 'unknown'}`);
    this.log('info', `🔍 LangSmith API Key: ${process.env.LANGSMITH_API_KEY ? 'SET' : 'NOT SET'}`);
    this.log('info', `🔍 LangSmith Project: ${process.env.LANGSMITH_PROJECT || 'NOT SET'}`);
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseFilters = {
      notEmptyCompanyWebsite: true
    }
    const specificFilters = {
      websiteScrapingStatus: 'pending'
    }
    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'company')
  }

  async processEntity(entity: UnifiedEntityData): Promise<{ success: boolean; error?: string }> {
    // Only process companies for web crawling
    if (entity.entity_type !== 'company') {
      return { success: false, error: 'Web crawling only supports companies' }
    }
    
    try {
      this.log('info', `🚀 Starting COMPREHENSIVE web crawling for company: ${entity.company_name} (${entity.company_website})`)
      this.log('info', `📊 Configuration: maxPages=${this.MAX_PAGES}, maxDepth=${this.MAX_DEPTH}`)

      // Validate company website URL
      if (!entity.company_website || entity.company_website.trim() === '') {
        this.log('error', `Company ${entity.company_name} has no website URL`)
        return { success: false, error: 'No website URL provided' }
      }

      // Set status to running
      await this.updateCompanyWebscrapingStatus(entity.id, 'running')

      // Use the comprehensive crawling function from fetchwebsite
      const crawlStartTime = Date.now();
      const result = await crawlWebsiteComprehensive(
        entity.id,
        entity.company_website,
        {
          maxPages: this.MAX_PAGES,
          maxDepth: this.MAX_DEPTH,
          db: { query: this.query.bind(this) }, // Pass the query method as database connection
          logger: this.log.bind(this),
          llmProvider: this.llmProvider,
          companyName: entity.company_name || 'Unknown Company',
          enableMetadataClassification: true
        }
      )
      
      const crawlDurationMin = Math.round((Date.now() - crawlStartTime) / 1000 / 60);
      this.log('info', `⏱️ Crawl completed in ${crawlDurationMin} minutes`)

      if (result.success && result.pageCount > 0) {
        const rate = crawlDurationMin > 0 ? Math.round(result.pageCount / crawlDurationMin) : 0;
        this.log('info', `✅ Successfully processed ${result.pageCount} pages for ${entity.company_name} (${rate} pages/min)`)
        this.stats.pagesCrawled += result.pageCount;
        return { success: true }
      } else {
        this.log('warn', `⚠️ No pages crawled for company ${entity.company_name}: ${result.error || 'Unknown error'}`)
        this.stats.errors++;
        return { success: false, error: result.error || 'No pages were successfully crawled' }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error crawling website for company ${entity.company_name}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Update company status after processing
   */
  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    try {
      // Check if any pages were actually saved
      const checkSql = `
        SELECT COUNT(*) as page_count
        FROM company_web_pages
        WHERE company_id = $1
      `;
      
      const result = await this.query(checkSql, [entityId]);
      const pageCount = parseInt(String(result[0]?.page_count) || '0', 10);
      
      // Check if this is a manual processing (singleId mode)
      const isManualProcessing = this.options.singleId === entityId;
      
      if (success && pageCount > 0) {
        // Successfully crawled with pages saved - mark as completed
        await this.updateCompanyWebscrapingStatus(entityId, 'completed');
        this.log('info', `Company ${entityId} marked as processed with ${pageCount} pages saved${isManualProcessing ? ' (manual processing)' : ''}`);
      } else if (success && pageCount === 0) {
        // Success but no pages - mark as failed
        await this.updateCompanyWebscrapingStatus(entityId, 'failed', 'No pages were saved');
        this.log('warn', `Company ${entityId} marked as failed: no pages were saved${isManualProcessing ? ' (manual processing)' : ''}`);
      } else {
        // Failed processing - mark as failed with error
        await this.updateCompanyWebscrapingStatus(entityId, 'failed', error);
        await this.incrementProcessingErrorCount('company', entityId);
        const errorMsg = error ? `: ${error}` : '';
        this.log('error', `Company ${entityId} processing failed${isManualProcessing ? ' (manual processing)' : ''}${errorMsg}`);
      }
    } catch (updateError) {
      this.log('error', `Error updating status for company ${entityId}: ${updateError}`);
    }
  }


  /**
   * Clean up resources when done
   */
  async cleanup(): Promise<void> {
    // Wait for any pending promises
    if (this.activePromises.length > 0) {
      await Promise.all(this.activePromises);
    }
    
    // Log final statistics
    this.log('info', `Crawler statistics: ${JSON.stringify(this.stats)}`);
  }
}
