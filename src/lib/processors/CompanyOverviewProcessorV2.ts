import { BaseProcessor } from './BaseProcessor'
import { ProcessorOptions, UnifiedEntityData } from '../../types/processing'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'
import { BaseScraper } from '../scrapers/BaseScraper'
import { COMPANY_OVERVIEW_V2_SYSTEM_PROMPT, COMPANY_OVERVIEW_V2_USER_TEMPLATE_FUNCTION } from '../prompts/company-overview-v2'

// Evidence-grounded structure for each field
interface EvidenceGroundedField<T> {
  value: T
  evidence: string
  confidence_score: number
  source_url: string
  sources: Array<{
    url: string
    page_section: string
    evidence_quote: string
    source_type: string
    date_found: string
  }>
}

// Enhanced data model to match the new comprehensive schema with evidence-grounded structure
interface CompanyOverviewDataV2 {
  // Core Company Information - Evidence-grounded structure
  company_name?: EvidenceGroundedField<string>
  company_type?: EvidenceGroundedField<string>
  company_industry?: EvidenceGroundedField<string>
  business_model?: EvidenceGroundedField<string>
  founded_year?: EvidenceGroundedField<number>
  
  // Investment & Strategy - Evidence-grounded structure
  investment_focus?: EvidenceGroundedField<string[]>
  investment_strategy_mission?: EvidenceGroundedField<string>
  investment_strategy_approach?: EvidenceGroundedField<string>
  
  // Contact Information - Evidence-grounded structure
  website?: EvidenceGroundedField<string>
  main_phone?: EvidenceGroundedField<string>
  secondary_phone?: EvidenceGroundedField<string>
  main_email?: EvidenceGroundedField<string>
  secondary_email?: EvidenceGroundedField<string>
  linkedin?: EvidenceGroundedField<string>
  twitter?: EvidenceGroundedField<string>
  facebook?: EvidenceGroundedField<string>
  instagram?: EvidenceGroundedField<string>
  youtube?: EvidenceGroundedField<string>
  
  // Address Information - Evidence-grounded structure
  headquarters_address?: EvidenceGroundedField<string>
  headquarters_city?: EvidenceGroundedField<string>
  headquarters_state?: EvidenceGroundedField<string>
  headquarters_zipcode?: EvidenceGroundedField<string>
  headquarters_country?: EvidenceGroundedField<string>
  additional_address?: EvidenceGroundedField<string>
  additional_city?: EvidenceGroundedField<string>
  additional_state?: EvidenceGroundedField<string>
  additional_zipcode?: EvidenceGroundedField<string>
  additional_country?: EvidenceGroundedField<string>
  office_locations?: EvidenceGroundedField<string[]>
  
  // Company Metrics - Evidence-grounded structure
  fund_size?: EvidenceGroundedField<number>
  aum?: EvidenceGroundedField<number>
  number_of_properties?: EvidenceGroundedField<number>
  number_of_offices?: EvidenceGroundedField<number>
  number_of_employees?: EvidenceGroundedField<number>
  annual_revenue?: EvidenceGroundedField<number>
  net_income?: EvidenceGroundedField<number>
  ebitda?: EvidenceGroundedField<number>
  profit_margin?: EvidenceGroundedField<number>
  market_capitalization?: EvidenceGroundedField<number>
  market_share_percentage?: EvidenceGroundedField<number>
  
  // Financial Information - Evidence-grounded structure
  balance_sheet_strength?: EvidenceGroundedField<string>
  funding_sources?: EvidenceGroundedField<string[]>
  recent_capital_raises?: EvidenceGroundedField<string>
  typical_debt_to_equity_ratio?: EvidenceGroundedField<number>
  development_fee_structure?: EvidenceGroundedField<string>
  credit_rating?: EvidenceGroundedField<string>
  dry_powder?: EvidenceGroundedField<number>
  annual_deployment_target?: EvidenceGroundedField<number>
  
  // Investment & Fund Information - Evidence-grounded structure
  investment_vehicle_type?: EvidenceGroundedField<string>
  active_fund_name_series?: EvidenceGroundedField<string>
  fund_size_active_fund?: EvidenceGroundedField<number>
  fundraising_status?: EvidenceGroundedField<string>
  lender_type?: EvidenceGroundedField<string>
  annual_loan_volume?: EvidenceGroundedField<number>
  lending_origin_balance_sheet_securitization?: EvidenceGroundedField<string>
  portfolio_health?: EvidenceGroundedField<string>
  
  // Partnership & Leadership - Evidence-grounded structure
  partnerships?: EvidenceGroundedField<string[]>
  key_equity_partners?: EvidenceGroundedField<string[]>
  key_debt_partners?: EvidenceGroundedField<string[]>
  board_of_directors?: EvidenceGroundedField<string[]>
  key_executives?: EvidenceGroundedField<string[]>
  founder_background?: EvidenceGroundedField<string>
  
  // Market Positioning & Strategy - Evidence-grounded structure
  market_cycle_positioning?: EvidenceGroundedField<string>
  urban_vs_suburban_preference?: EvidenceGroundedField<string>
  sustainability_esg_focus?: EvidenceGroundedField<boolean>
  technology_proptech_adoption?: EvidenceGroundedField<boolean>
  adaptive_reuse_experience?: EvidenceGroundedField<boolean>
  regulatory_zoning_expertise?: EvidenceGroundedField<boolean>
  
  // Corporate Structure - Evidence-grounded structure
  corporate_structure?: EvidenceGroundedField<string>
  parent_company?: EvidenceGroundedField<string>
  subsidiaries?: EvidenceGroundedField<string[]>
  stock_ticker_symbol?: EvidenceGroundedField<string>
  stock_exchange?: EvidenceGroundedField<string>
  
  // Business Information - Evidence-grounded structure
  products_services_description?: EvidenceGroundedField<string>
  target_customer_profile?: EvidenceGroundedField<string>
  major_competitors?: EvidenceGroundedField<string[]>
  unique_selling_proposition?: EvidenceGroundedField<string>
  industry_awards_recognitions?: EvidenceGroundedField<string[]>
  company_history?: EvidenceGroundedField<string>
  
  // Transaction & Portfolio Data - Evidence-grounded structure
  transactions_completed_last_12m?: EvidenceGroundedField<number>
  total_transaction_volume_ytd?: EvidenceGroundedField<number>
  deal_count_ytd?: EvidenceGroundedField<number>
  average_deal_size?: EvidenceGroundedField<number>
  portfolio_size_sqft?: EvidenceGroundedField<number>
  portfolio_asset_count?: EvidenceGroundedField<number>
  role_in_previous_deal?: EvidenceGroundedField<string>
  
  // Relationship & Pipeline Data - Evidence-grounded structure
  internal_relationship_manager?: EvidenceGroundedField<string>
  last_contact_date?: EvidenceGroundedField<string>
  pipeline_status?: EvidenceGroundedField<string>
  recent_news_sentiment?: EvidenceGroundedField<string>
  
  // Data Quality & Processing - Evidence-grounded structure
  data_source?: EvidenceGroundedField<string>
  data_confidence_score?: EvidenceGroundedField<number>
  quarterly_earnings_link?: EvidenceGroundedField<string>
  
  // Exact-text summaries and snippets extracted from sources
  overview_summary?: {
    financial_metrics_summary_text?: string
    investment_criteria_summary_text?: string
    exact_snippets?: Array<{
      field?: string
      text: string
      source?: string
    }>
  }
}

export class CompanyOverviewProcessorV2 extends BaseProcessor {
  private llmProvider;
  private queryParams: any[] = []
  private queries: string[] = []
  private companiesProcessed: number = 0

  constructor(options: ProcessorOptions = {}) {
    // Perplexity API specific rate limiting configuration
    const perplexityBottleneckConfig = {
      maxConcurrent: 10,                   // Reduced from 5 to prevent queue overflow
      minTime: 1500,                      // 1.5 seconds between requests
      retryAttempts: 3,                   // Standard retries for LLM API
      retryDelayBase: 2000,               // 2 second base delay for retries
      retryDelayMax: 30000,               // Max 30 second retry delay
      timeout: 240000,                    // 4 minutes timeout for LLM processing
      highWater: 300,                     // Higher queue limit for batch processing
      strategy: 'OVERFLOW' as any,        // Use OVERFLOW strategy
      defaultPriority: 3,                 // Higher priority for company overview
      enableJobMetrics: true              // Track LLM API performance
    }

    super('CompanyOverviewV2', {
      ...options,
      bottleneckConfig: options.bottleneckConfig || perplexityBottleneckConfig
    })
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Use Perplexity for web-enhanced research capabilities
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      {
        apiKey: process.env.PERPLEXITY_API_KEY,
      }
    );
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseFilters = {

    }
    const specificFilters = {
      companyOverviewV2Status: 'pending'
    }
    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'company')
  }

  /**
   * Get default processor-specific filters when no UI filters are provided
   */
  protected getDefaultProcessorFilters(entityType?: 'contact' | 'company'): Record<string, any> {
    if (entityType === 'company') {
      return {
        companyOverviewV2Status: 'pending'
      }
    }
    return {}
  }

  async processEntity(entity: UnifiedEntityData): Promise<{ success: boolean; error?: string }> {
    // Only process companies for company overview
    if (entity.entity_type !== 'company') {
      return { success: false, error: 'Company overview only supports companies' }
    }
    
    try {
      this.log('info', `Extracting comprehensive company overview for: ${entity.company_name}`)

      // Set status to running
      await this.updateOverviewV2Status(entity.id, 'running')

      // Get company content
      const content = await this.getCompanyContent(entity.id)
      
      // if ((!content || content.trim().length === 0 ) && (!this.options.multiIds || this.options.multiIds.length === 0)) {
      //   this.log('warn', `No content found for company ${entity.id}`)
      //   await this.updateOverviewV2Status(entity.id, 'failed', 'No content available for extraction')
      //   return { success: false, error: 'No content available for extraction' }
      // }

      // Extract comprehensive company overview
      const { data: overviewData, llmResponse, llmUsage } = await this.extractCompanyOverviewV2(entity, content);
      
      if (!overviewData) {
        await this.updateOverviewV2Status(entity.id, 'failed', 'Failed to extract company overview')
        return { success: false, error: 'Failed to extract company overview' }
      }

      // Store the extracted data directly to companies table
      await this.storeCompanyOverviewV2(entity.id, overviewData, llmResponse || undefined, llmUsage || undefined)

      this.log('info', `Successfully extracted comprehensive company overview for ${entity.company_name}`)
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error extracting company overview for ${entity.company_name}: ${errorMessage}`)
      await this.updateOverviewV2Status(entity.id, 'failed', errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean): Promise<void> {
    if (success) {
      await this.updateOverviewV2Status(entityId, 'completed');
      this.log('info', `Company ${entityId} marked as processed with comprehensive data extracted`);
    } else {
      // For failed processing, mark as failed and increment error count
      await this.updateOverviewV2Status(entityId, 'failed',  "Failed to update comprehensive overview");
      await this.incrementProcessingErrorCount('company', entityId);
      this.log('info', `Company ${entityId} processing failed, marked as failed`);
    }
  }

  /**
   * Extract comprehensive company overview using LLM provider
   */
  private async extractCompanyOverviewV2(
    company: UnifiedEntityData, 
    content: string
  ): Promise<{ data: CompanyOverviewDataV2 | null; llmResponse?: string; llmUsage?: any }> {
    try {
      const messages = await this.buildExtractionMessagesV2(company, content);
      const model = 'sonar'

      // Use the LLM provider with fallback support and better model for web search
      const response = await this.llmProvider.callLLM(messages, {
        model: model,
        temperature: 0.3, // Lower temperature for more consistent extraction
        maxTokens: 25000 // Increased token limit for comprehensive extraction
      });

      if (!response.content) {
        throw new Error(`No response content from ${response.provider}`);
      }

      this.log('info', `Extracted comprehensive overview using ${response.provider} (${response.model})`);

      // Parse the structured response
      const parsedResponse = this.parseOverviewResponseV2(response.content);
      
      return {
        data: parsedResponse,
        llmResponse: response.content,
        llmUsage: response.usage
      };
    } catch (error) {
      this.log('error', `Error calling LLM extraction API: ${error}`);
      return { data: null };
    }
  }

  /**
   * Build messages for LLM comprehensive company overview extraction
   */
  private async buildExtractionMessagesV2(
    company: UnifiedEntityData, 
    content: string
  ): Promise<LLMMessage[]> {
    // Use the static getMappings method from BaseScraper
    const mappings = await BaseScraper.getMappings();

    // Fetch curated related URLs via base helper
    const { relatedUrls } = await this.getCompanyOverviewSummaryAndUrls(company.id)

    // Get page metadata from web crawling (NEW: Include metadata in company overview)
    const pageMetadata = await this.getPageMetadataForCompany(company.id)
    this.log('debug', `Retrieved ${pageMetadata.length} pages with metadata for company ${company.id}`)

    // Format page metadata for inclusion in prompt
    const metadataContent = this.formatPageMetadataForPrompt(pageMetadata)

    const userPrompt = COMPANY_OVERVIEW_V2_USER_TEMPLATE_FUNCTION({
      company_name: company.company_name || '',
      company_website: company.company_website || '',
      industry: company.industry || ''
    }, content, relatedUrls, mappings, metadataContent); // NEW: Pass metadata to template

    return [
      { role: 'system', content: COMPANY_OVERVIEW_V2_SYSTEM_PROMPT },
      { role: 'user', content: userPrompt }
    ]
  }

  // Removed local duplicate: using BaseProcessor.getCompanyOverviewSummaryAndUrls

  /**
   * Get page metadata for a company from the web crawling system
   */
  private async getPageMetadataForCompany(companyId: number): Promise<any[]> {
    const sql = `
      SELECT 
        url,
        extracted_text,
        relevance_rank,
        page_metadata
      FROM company_web_pages 
      WHERE company_id = $1 
        AND page_metadata IS NOT NULL 
        AND page_metadata != ''
      ORDER BY relevance_rank ASC 
      LIMIT 20
    `;
    
    try {
      const result = await this.query(sql, [companyId]);
      return Array.isArray(result) ? result : [];
    } catch (error) {
      this.log('warn', `Failed to retrieve page metadata for company ${companyId}: ${error}`);
      return [];
    }
  }

  /**
   * Format page metadata for inclusion in company overview prompt
   */
  private formatPageMetadataForPrompt(pageMetadata: any[]): string {
    if (!pageMetadata || pageMetadata.length === 0) {
      return '';
    }

    let metadataContent = `\n\n## PAGE METADATA FROM WEB CRAWLING ANALYSIS\n\n`;
    metadataContent += `**Analysis Summary:**\n`;
    metadataContent += `- Total pages with metadata: ${pageMetadata.length}\n`;
    metadataContent += `- All pages contain structured YAML metadata with investment criteria, financial data, and entity information\n\n`;

    // Show top 10 pages with their raw YAML metadata
    pageMetadata.slice(0, 10).forEach((page, index) => {
      metadataContent += `**Page ${index + 1}:** ${page.url} (Rank: ${page.relevance_rank})\n`;
      
      if (page.page_metadata) {
        metadataContent += `- **Raw Metadata (YAML):**\n\`\`\`yaml\n${page.page_metadata}\n\`\`\`\n`;
      } else {
        metadataContent += `- **No Metadata:** Page metadata not available\n`;
      }
      
      // Include a sample of the content
      if (page.extracted_text && page.extracted_text.length > 0) {
        metadataContent += `- **Content Sample:** ${page.extracted_text.substring(0, 300)}...\n`;
      }
      
      metadataContent += `\n---\n\n`;
    });

    metadataContent += `**Instructions for Using This Metadata:**\n`;
    metadataContent += `- Use the extracted data values (amounts, rates, locations, etc.) from the YAML as primary evidence\n`;
    metadataContent += `- Consider the entity roles to understand who provides vs. receives\n`;
    metadataContent += `- Use the classification categories and confidence levels to assess data quality\n`;
    metadataContent += `- Always cite metadata as source_type "page_metadata" and include the URL\n\n`;

    return metadataContent;
  }

  /**
   * Parse LLM response to extract comprehensive company overview data with evidence-grounded structure
   */
  private parseOverviewResponseV2(content: string): CompanyOverviewDataV2 | null {
    try {
      this.log('debug', `Parsing comprehensive LLM response content (length: ${content.length})`);
      
      // Clean the content and extract JSON
      let jsonContent = content.trim();
      
      // Handle Perplexity format: JSON comes AFTER </think> tag, not inside it
      const thinkMatch = jsonContent.match(/<think>[\s\S]*?<\/think>\s*([\s\S]*)/);
      if (thinkMatch) {
        // Extract content that comes AFTER the </think> tag
        const afterThinkContent = thinkMatch[1].trim();
        this.log('debug', `Extracted content after <think> tags (${afterThinkContent.length} chars)`);
        
        if (afterThinkContent) {
          jsonContent = afterThinkContent;
          this.log('debug', `Using content after </think> tag`);
        } else {
          throw new Error('No content found after </think> tag');
        }
      } else {
        // Remove any thinking tags if they appear without content
        jsonContent = jsonContent.replace(/<think>[\s\S]*?<\/think>/g, '');
        
        // Remove any leading/trailing text that isn't JSON
        jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '');
        
        // Try to extract from markdown code blocks
        const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
        if (markdownJsonMatch) {
          jsonContent = markdownJsonMatch[1].trim();
          this.log('debug', `Extracted JSON from markdown code block`);
        } else {
          // Look for the main JSON object
          const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
            throw new Error('No JSON found in response');
          }
          jsonContent = jsonMatch[0];
          this.log('debug', `Extracted JSON using direct object match`);
        }
      }

      // Clean up the JSON content
      jsonContent = jsonContent.trim()

      // Check if JSON appears to be truncated (doesn't end with closing brace)
      if (!jsonContent.endsWith('}')) {
        this.log('warn', `JSON appears to be truncated (doesn't end with }). Attempting to parse anyway.`)
      }

      const result = JSON.parse(jsonContent)

      this.log('info', `Successfully parsed comprehensive extraction response with ${Object.keys(result).length} fields`)
      
      // Handle nested structure from prompt: { "company": { ... } }
      let companyData = result;
      if (result.company && typeof result.company === 'object') {
        companyData = result.company;
        this.log('debug', `Extracted company data from nested structure`);
      }
      
      // Transform the evidence-grounded structure for validation and source tracking
      const transformedData = this.transformEvidenceGroundedData(companyData);
      
      // Ensure overview_summary is captured even if returned under alternate keys/paths
      try {
        const hasOverviewSummary = transformedData && typeof transformedData === 'object' && 'overview_summary' in transformedData
        if (!hasOverviewSummary) {
          const candidate = (result as any).overview_summary
            || ((result as any).overview && (result as any).overview.overview_summary)
            || ((result as any).summary && (result as any).summary.overview_summary)
            || (result as any).overview
            || null
          if (candidate && typeof candidate === 'object') {
            const financialText = candidate.financial_metrics_summary_text
              || candidate.financial_summary
              || candidate.financials_text
              || null
            const icText = candidate.investment_criteria_summary_text
              || candidate.ic_summary
              || candidate.investment_text
              || null
            const snippets = Array.isArray(candidate.exact_snippets)
              ? candidate.exact_snippets
              : (Array.isArray(candidate.snippets) ? candidate.snippets : [])
            if (financialText || icText || (Array.isArray(snippets) && snippets.length > 0)) {
              ;(transformedData as any).overview_summary = {
                financial_metrics_summary_text: financialText,
                investment_criteria_summary_text: icText,
                exact_snippets: snippets
              }
            }
          }
        }
      } catch (e) {
        this.log('warn', `Failed to reconcile overview_summary from response variants: ${e}`)
      }
      
      // Clean and return the result
      return this.cleanNullValues(transformedData) as CompanyOverviewDataV2
    } catch (error) {
      this.log('error', `Error parsing comprehensive overview response: ${error}`);
      
      // Log a portion of the content for debugging
      const contentPreview = content.length > 1500 ? content.substring(0, 1500) + '...' : content;
      this.log('debug', `Response content preview: ${contentPreview}`);
      
      return null;
    }
  }

  /**
   * Transform evidence-grounded data structure and validate confidence scores
   */
  private transformEvidenceGroundedData(data: any): CompanyOverviewDataV2 {
    const transformed: any = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (value && typeof value === 'object' && 'value' in value) {
        // This is an evidence-grounded field
        const evidenceField = value as any;
        
        // Only include fields with sufficient confidence scores (>= 0.7)
        if (evidenceField.confidence_score >= 0.7) {
          transformed[key] = {
            value: evidenceField.value,
            evidence: evidenceField.evidence || '',
            confidence_score: evidenceField.confidence_score,
            source_url: evidenceField.source_url || '',
            sources: Array.isArray(evidenceField.sources) ? evidenceField.sources : []
          };
          
          this.log('debug', `Transformed field ${key} with confidence ${evidenceField.confidence_score}`);
        } else {
          this.log('warn', `Skipping field ${key} due to low confidence score: ${evidenceField.confidence_score}`);
        }
      } else {
        // Non-evidence field (e.g., overview_summary)
        transformed[key] = value;
      }
    }
    
    this.log('info', `Transformed evidence-grounded data with ${Object.keys(transformed).length} validated fields`);
    return transformed;
  }

  /**
   * Store comprehensive extracted company overview directly in companies table with evidence-grounded source tracking
   */
  private async storeCompanyOverviewV2(companyId: number, data: CompanyOverviewDataV2, llmResponse?: string, llmUsage?: any): Promise<void> {
    this.log('debug', `Storing comprehensive overview data for company ${companyId}`);
    this.log('debug', `LLM Usage type: ${typeof llmUsage}, value: ${JSON.stringify(llmUsage)}`);
    
    // Extract values from evidence-grounded structure and consolidate sources
    const extractedValues = this.extractEvidenceGroundedValues(data);
    const consolidatedSources = this.consolidateOverviewSources(data);
    
    const sql = `
      UPDATE companies SET
        -- Core Company Information
        industry = COALESCE($1, industry),
        company_type = COALESCE($2, company_type),
        business_model = COALESCE($3, business_model),
        founded_year = COALESCE($4, founded_year),
        
        -- Investment & Strategy
        investment_focus = COALESCE($5, investment_focus),
        investment_strategy_mission = COALESCE($6, investment_strategy_mission),
        investment_strategy_approach = COALESCE($7, investment_strategy_approach),
        
        -- Contact Information (AI-updated fields)
        main_phone = COALESCE($8, main_phone),
        secondary_phone = COALESCE($9, secondary_phone),
        main_email = COALESCE($10, main_email),
        secondary_email = COALESCE($11, secondary_email),
        company_linkedin = COALESCE($12, company_linkedin),
        twitter = COALESCE($13, twitter),
        facebook = COALESCE($14, facebook),
        instagram = COALESCE($15, instagram),
        youtube = COALESCE($16, youtube),
        
        -- Address Information (AI-updated headquarters fields)
        headquarters_address = COALESCE($17, headquarters_address),
        headquarters_city = COALESCE($18, headquarters_city),
        headquarters_state = COALESCE($19, headquarters_state),
        headquarters_zipcode = COALESCE($20, headquarters_zipcode),
        headquarters_country = COALESCE($21, headquarters_country),
        additional_address = COALESCE($22, additional_address),
        additional_city = COALESCE($23, additional_city),
        additional_state = COALESCE($24, additional_state),
        additional_zipcode = COALESCE($25, additional_zipcode),
        additional_country = COALESCE($26, additional_country),
        office_locations = COALESCE($27, office_locations),
        
        -- Company Metrics
        fund_size = COALESCE($28, fund_size),
        aum = COALESCE($29, aum),
        number_of_properties = COALESCE($30, number_of_properties),
        number_of_offices = COALESCE($31, number_of_offices),
        number_of_employees = COALESCE($32, number_of_employees),
        annual_revenue = COALESCE($33, annual_revenue),
        net_income = COALESCE($34, net_income),
        ebitda = COALESCE($35, ebitda),
        profit_margin = COALESCE($36, profit_margin),
        market_capitalization = COALESCE($37, market_capitalization),
        market_share_percentage = COALESCE($38, market_share_percentage),
        
        -- Financial Information
        balance_sheet_strength = COALESCE($39, balance_sheet_strength),
        funding_sources = COALESCE($40, funding_sources),
        recent_capital_raises = COALESCE($41, recent_capital_raises),
        typical_debt_to_equity_ratio = COALESCE($42, typical_debt_to_equity_ratio),
        development_fee_structure = COALESCE($43, development_fee_structure),
        credit_rating = COALESCE($44, credit_rating),
        dry_powder = COALESCE($45, dry_powder),
        annual_deployment_target = COALESCE($46, annual_deployment_target),
        
        -- Investment & Fund Information
        investment_vehicle_type = COALESCE($47, investment_vehicle_type),
        active_fund_name_series = COALESCE($48, active_fund_name_series),
        fund_size_active_fund = COALESCE($49, fund_size_active_fund),
        fundraising_status = COALESCE($50, fundraising_status),
        lender_type = COALESCE($51, lender_type),
        annual_loan_volume = COALESCE($52, annual_loan_volume),
        lending_origin = COALESCE($53, lending_origin),
        portfolio_health = COALESCE($54, portfolio_health),
        
        -- Partnership & Leadership
        partnerships = COALESCE($55, partnerships),
        key_equity_partners = COALESCE($56, key_equity_partners),
        key_debt_partners = COALESCE($57, key_debt_partners),
        board_of_directors = COALESCE($58, board_of_directors),
        key_executives = COALESCE($59, key_executives),
        founder_background = COALESCE($60, founder_background),
        
        -- Market Positioning & Strategy
        market_cycle_positioning = COALESCE($61, market_cycle_positioning),
        urban_vs_suburban_preference = COALESCE($62, urban_vs_suburban_preference),
        sustainability_esg_focus = COALESCE($63, sustainability_esg_focus),
        technology_proptech_adoption = COALESCE($64, technology_proptech_adoption),
        adaptive_reuse_experience = COALESCE($65, adaptive_reuse_experience),
        regulatory_zoning_expertise = COALESCE($66, regulatory_zoning_expertise),
        
        -- Corporate Structure
        corporate_structure = COALESCE($67, corporate_structure),
        parent_company = COALESCE($68, parent_company),
        subsidiaries = COALESCE($69, subsidiaries),
        stock_ticker_symbol = COALESCE($70, stock_ticker_symbol),
        stock_exchange = COALESCE($71, stock_exchange),
        
        -- Business Information
        products_services_description = COALESCE($72, products_services_description),
        target_customer_profile = COALESCE($73, target_customer_profile),
        major_competitors = COALESCE($74, major_competitors),
        unique_selling_proposition = COALESCE($75, unique_selling_proposition),
        industry_awards_recognitions = COALESCE($76, industry_awards_recognitions),
        company_history = COALESCE($77, company_history),
        
        -- Transaction & Portfolio Data
        transactions_completed_last_12m = COALESCE($78, transactions_completed_last_12m),
        total_transaction_volume_ytd = COALESCE($79, total_transaction_volume_ytd),
        deal_count_ytd = COALESCE($80, deal_count_ytd),
        average_deal_size = COALESCE($81, average_deal_size),
        portfolio_size_sqft = COALESCE($82, portfolio_size_sqft),
        portfolio_asset_count = COALESCE($83, portfolio_asset_count),
        role_in_previous_deal = COALESCE($84, role_in_previous_deal),
        
        -- Relationship & Pipeline Data
        internal_relationship_manager = COALESCE($85, internal_relationship_manager),
        last_contact_date = COALESCE($86, last_contact_date),
        pipeline_status = COALESCE($87, pipeline_status),
        recent_news_sentiment = COALESCE($88, recent_news_sentiment),
        
        -- Data Quality & Processing
        data_source = COALESCE($89, data_source),
        data_confidence_score = COALESCE($90, data_confidence_score),
        quarterly_earnings_link = COALESCE($91, quarterly_earnings_link),
        overview_sources = COALESCE($92, overview_sources),
        
        -- Processing metadata
        updated_at = NOW(),
        last_updated_timestamp = NOW(),
        llm_used = $93,
        llm_response = $94,
        llm_token_usage = $95
        
      WHERE company_id = $96
    `
    
    // Prepare parameters array with extracted values
    const params = [
      extractedValues.company_industry || null,                          // $1 - industry field
      extractedValues.company_type || null,                              // $2
      extractedValues.business_model || null,                            // $3
      this.validateNumericValue(extractedValues.founded_year),           // $4
      extractedValues.investment_focus ? this.arrayToPostgresArray(extractedValues.investment_focus) : null,  // $5
      extractedValues.investment_strategy_mission || null,               // $6
      extractedValues.investment_strategy_approach || null,              // $7
      extractedValues.main_phone || null,                                // $8
      extractedValues.secondary_phone || null,                           // $9
      extractedValues.main_email || null,                                // $10
      extractedValues.secondary_email || null,                           // $11
      extractedValues.linkedin || null,                                  // $12
      extractedValues.twitter || null,                                   // $13
      extractedValues.facebook || null,                                  // $14
      extractedValues.instagram || null,                                 // $15
      extractedValues.youtube || null,                                   // $16
      extractedValues.headquarters_address || null,                      // $17
      extractedValues.headquarters_city || null,                         // $18
      extractedValues.headquarters_state || null,                        // $19
      extractedValues.headquarters_zipcode || null,                      // $20
      extractedValues.headquarters_country || null,                      // $21
      extractedValues.additional_address || null,                        // $22
      extractedValues.additional_city || null,                           // $23
      extractedValues.additional_state || null,                          // $24
      extractedValues.additional_zipcode || null,                        // $25
      extractedValues.additional_country || null,                        // $26
      extractedValues.office_locations ? this.arrayToPostgresArray(extractedValues.office_locations) : null,  // $27
      this.validateNumericValue(extractedValues.fund_size),              // $28
      this.validateNumericValue(extractedValues.aum),                    // $29
      this.validateNumericValue(extractedValues.number_of_properties),   // $30
      this.validateNumericValue(extractedValues.number_of_offices),     // $31
      this.validateNumericValue(extractedValues.number_of_employees),    // $32
      this.validateNumericValue(extractedValues.annual_revenue),         // $33
      this.validateNumericValue(extractedValues.net_income),            // $34
      this.validateNumericValue(extractedValues.ebitda),                // $35
      this.validateNumericValue(extractedValues.profit_margin),          // $36
      this.validateNumericValue(extractedValues.market_capitalization),  // $37
      this.validateNumericValue(extractedValues.market_share_percentage), // $38
      extractedValues.balance_sheet_strength || null,                    // $39
      extractedValues.funding_sources ? this.arrayToPostgresArray(extractedValues.funding_sources) : null,    // $40
      extractedValues.recent_capital_raises || null,                     // $41
      this.validateNumericValue(extractedValues.typical_debt_to_equity_ratio), // $42
      extractedValues.development_fee_structure || null,                 // $43
      extractedValues.credit_rating || null,                             // $44
      this.validateNumericValue(extractedValues.dry_powder),             // $45
      this.validateNumericValue(extractedValues.annual_deployment_target), // $46
      extractedValues.investment_vehicle_type || null,                   // $47
      extractedValues.active_fund_name_series || null,                   // $48
      this.validateNumericValue(extractedValues.fund_size_active_fund),  // $49
      extractedValues.fundraising_status || null,                        // $50
      extractedValues.lender_type || null,                               // $51
      this.validateNumericValue(extractedValues.annual_loan_volume),     // $52
      extractedValues.lending_origin_balance_sheet_securitization || null, // $53 - map to lending_origin column
      extractedValues.portfolio_health || null,                          // $54
      extractedValues.partnerships ? this.arrayToPostgresArray(extractedValues.partnerships) : null,        // $55
      extractedValues.key_equity_partners ? this.arrayToPostgresArray(extractedValues.key_equity_partners) : null,  // $56
      extractedValues.key_debt_partners ? this.arrayToPostgresArray(extractedValues.key_debt_partners) : null,  // $57
      extractedValues.board_of_directors ? this.arrayToPostgresArray(extractedValues.board_of_directors) : null,  // $58
      extractedValues.key_executives ? this.arrayToPostgresArray(extractedValues.key_executives) : null,      // $59
      extractedValues.founder_background || null,                        // $60
      extractedValues.market_cycle_positioning || null,                  // $61
      extractedValues.urban_vs_suburban_preference || null,              // $62
      extractedValues.sustainability_esg_focus || null,                  // $63
      extractedValues.technology_proptech_adoption || null,              // $64
      extractedValues.adaptive_reuse_experience || null,                 // $65
      extractedValues.regulatory_zoning_expertise || null,               // $66
      extractedValues.corporate_structure || null,                       // $67
      extractedValues.parent_company || null,                            // $68
      extractedValues.subsidiaries ? this.arrayToPostgresArray(extractedValues.subsidiaries) : null,        // $69
      extractedValues.stock_ticker_symbol || null,                       // $70
      extractedValues.stock_exchange || null,                            // $71
      extractedValues.products_services_description || null,             // $72
      extractedValues.target_customer_profile || null,                   // $73
      extractedValues.major_competitors ? this.arrayToPostgresArray(extractedValues.major_competitors) : null,  // $74
      extractedValues.unique_selling_proposition || null,                // $75
      extractedValues.industry_awards_recognitions ? this.arrayToPostgresArray(extractedValues.industry_awards_recognitions) : null,  // $76
      extractedValues.company_history || null,                           // $77
      this.validateNumericValue(extractedValues.transactions_completed_last_12m), // $78
      this.validateNumericValue(extractedValues.total_transaction_volume_ytd),    // $79
      this.validateNumericValue(extractedValues.deal_count_ytd),                 // $80
      this.validateNumericValue(extractedValues.average_deal_size),              // $81
      this.validateNumericValue(extractedValues.portfolio_size_sqft),             // $82
      this.validateNumericValue(extractedValues.portfolio_asset_count),           // $83
      extractedValues.role_in_previous_deal || null,                     // $84
      extractedValues.internal_relationship_manager || null,             // $85
      extractedValues.last_contact_date || null,                         // $86
      extractedValues.pipeline_status || null,                           // $87
      extractedValues.recent_news_sentiment || null,                     // $88
      extractedValues.data_source || null,                               // $89
      this.validateNumericValue(extractedValues.data_confidence_score), // $90
      extractedValues.quarterly_earnings_link || null,                   // $91
      JSON.stringify(consolidatedSources),                               // $92 - consolidated sources
      'perplexity-sonar',                                                // $93 - LLM used
      llmResponse || null,                                               // $94 - LLM response
      llmUsage && typeof llmUsage === 'object' ? JSON.stringify(llmUsage) : null,  // $95 - LLM usage
      companyId                                                          // $96
    ]
    
    await this.query(sql, params)
    
    this.log('debug', `Stored comprehensive company overview for company ${companyId}`)

    // Store overview_summary JSON separately to avoid altering the large parameterized query above
    if (data.overview_summary && Object.keys(data.overview_summary).length > 0) {
      const summarySql = `
        UPDATE companies
        SET overview_summary = COALESCE($1::jsonb, overview_summary),
            updated_at = NOW(),
            last_updated_timestamp = NOW()
        WHERE company_id = $2
      `
      const summaryParams = [JSON.stringify(data.overview_summary), companyId]
      await this.query(summarySql, summaryParams)
      this.log('debug', `Stored overview_summary for company ${companyId}`)
    }
  }

  /**
   * Extract values from evidence-grounded fields for database storage
   */
  private extractEvidenceGroundedValues(data: CompanyOverviewDataV2): Record<string, any> {
    const extractedValues: Record<string, any> = {};
    
    // Define which fields should be arrays
    const arrayFields = [
      'investment_focus', 'office_locations', 'funding_sources', 'partnerships',
      'key_equity_partners', 'key_debt_partners', 'board_of_directors', 'key_executives',
      'subsidiaries', 'major_competitors', 'industry_awards_recognitions'
    ];
    
    for (const [key, field] of Object.entries(data)) {
      if (field && typeof field === 'object' && 'value' in field) {
        // This is an evidence-grounded field, extract ONLY the value
        let value = (field as any).value;
        
        // Log if we're getting the wrong type of data
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          this.log('warn', `Field ${key} has object value instead of primitive: ${JSON.stringify(value).substring(0, 100)}...`);
          // Skip this field if it's an object
          continue;
        }
        
        // Ensure array fields are properly formatted as arrays
        if (arrayFields.includes(key)) {
          if (typeof value === 'string') {
            // If it's a string, try to split by common delimiters or wrap in array
            if (value.includes(',') || value.includes(';') || value.includes('\n')) {
              value = value.split(/[,;\n]/).map(item => item.trim()).filter(item => item.length > 0);
            } else {
              value = [value];
            }
          } else if (!Array.isArray(value)) {
            // If it's not an array and not a string, convert to array
            value = value ? [String(value)] : [];
          }
        }
        
        extractedValues[key] = value;
      } else if (field && typeof field === 'object' && !('value' in field)) {
        // This might be a non-evidence field, but check if it's an evidence-grounded object
        if ('evidence' in field || 'confidence_score' in field || 'sources' in field) {
          this.log('warn', `Field ${key} appears to be evidence-grounded but missing 'value' property: ${JSON.stringify(field).substring(0, 100)}...`);
          // Skip this field to avoid database errors
          continue;
        } else {
          // Non-evidence field (e.g., overview_summary)
          extractedValues[key] = field;
        }
      } else {
        // Non-evidence field (e.g., overview_summary)
        extractedValues[key] = field;
      }
    }
    
    this.log('debug', `Extracted ${Object.keys(extractedValues).length} values from evidence-grounded fields`);
    return extractedValues;
  }

  /**
   * Consolidate all sources from evidence-grounded fields into a unified structure
   */
  private consolidateOverviewSources(data: CompanyOverviewDataV2): any {
    const allSources: Array<{
      field: string;
      url: string;
      page_section: string;
      evidence_quote: string;
      source_type: string;
      date_found: string;
      confidence_score: number;
    }> = [];

    // Helper function to add sources from a field
    const addFieldSources = (fieldName: string, fieldData: any) => {
      if (fieldData?.sources && Array.isArray(fieldData.sources)) {
        fieldData.sources.forEach((source: any) => {
          allSources.push({
            field: fieldName,
            url: source.url || '',
            page_section: source.page_section || '',
            evidence_quote: source.evidence_quote || '',
            source_type: source.source_type || '',
            date_found: source.date_found || new Date().toISOString().split('T')[0],
            confidence_score: fieldData.confidence_score || 0
          });
        });
      }
    };

    // Collect sources from all evidence-grounded fields
    for (const [fieldName, field] of Object.entries(data)) {
      if (field && typeof field === 'object' && 'sources' in field) {
        addFieldSources(fieldName, field);
      }
    }

    // Remove duplicates based on URL and evidence_quote
    const uniqueSources = allSources.filter((source, index, self) => 
      index === self.findIndex(s => s.url === source.url && s.evidence_quote === source.evidence_quote)
    );

    // Sort sources by source_type priority (matching investment criteria processor)
    const sourceTypePriority = {
      'web_scraped_text': 1,
      'enrichment_data': 2,
      'company_website': 3,
      'news_article': 4,
      'press_release': 5
    };

    uniqueSources.sort((a, b) => {
      const aPriority = sourceTypePriority[a.source_type as keyof typeof sourceTypePriority] || 6;
      const bPriority = sourceTypePriority[b.source_type as keyof typeof sourceTypePriority] || 6;
      return aPriority - bPriority;
    });

    // Create consolidated sources structure
    const consolidatedSources = {
      all_sources: uniqueSources,
      total_sources: uniqueSources.length,
      source_types: [...new Set(uniqueSources.map(s => s.source_type))],
      fields_with_sources: [...new Set(uniqueSources.map(s => s.field))],
      extraction_date: new Date().toISOString(),
      average_confidence_score: uniqueSources.length > 0 
        ? uniqueSources.reduce((sum, s) => sum + s.confidence_score, 0) / uniqueSources.length 
        : 0
    };

    this.log('info', `Consolidated ${uniqueSources.length} unique sources from ${consolidatedSources.fields_with_sources.length} fields`);
    return consolidatedSources;
  }

  /**
   * Validate and convert numeric values, ensuring they're not objects
   */
  private validateNumericValue(value: any): number | null {
    if (value === null || value === undefined) {
      return null;
    }
    
    // If it's an object (evidence-grounded structure), log warning and return null
    if (typeof value === 'object' && value !== null) {
      this.log('warn', `Expected numeric value but got object: ${JSON.stringify(value).substring(0, 100)}...`);
      return null;
    }
    
    // Convert to number
    const numValue = Number(value);
    if (isNaN(numValue)) {
      this.log('warn', `Could not convert to number: ${value}`);
      return null;
    }
    
    return numValue;
  }

  /**
   * Convert JavaScript array to PostgreSQL array literal format
   */
  private arrayToPostgresArray(arr: string[] | string | null | undefined): string {
    // Handle null/undefined
    if (!arr) {
      return '{}' // Return empty PostgreSQL array instead of null
    }
    
    // If it's a string, convert to array
    if (typeof arr === 'string') {
      // If it's a single string, wrap it in an array
      arr = [arr]
    }
    
    // Ensure it's an array
    if (!Array.isArray(arr)) {
      this.log('warn', `Expected array but got ${typeof arr}: ${JSON.stringify(arr)}`)
      return '{}'
    }
    
    if (arr.length === 0) {
      return '{}' // Return empty PostgreSQL array instead of null
    }
    
    // Escape quotes and wrap values that contain spaces or special characters
    const escapedValues = arr.map(value => {
      if (typeof value !== 'string') {
        return String(value)
      }
      
      // If value contains spaces, commas, quotes, or special characters, wrap in quotes and escape
      if (value.includes(' ') || value.includes(',') || value.includes('"') || value.includes('\'') || value.includes('{') || value.includes('}')) {
        return `"${value.replace(/"/g, '\\"')}"` 
      }
      
      return value
    })
    
    return `{${escapedValues.join(',')}}`
  }

  /**
   * Clean out null, undefined, and empty values from an object
   */
  private cleanNullValues<T>(obj: T): T {
    const result = { ...obj } as any
    
    for (const key in result) {
      const value = result[key]
      
      // Remove null/undefined values
      if (value === null || value === undefined) {
        delete result[key]
        continue
      }
      
      // Handle arrays - remove empty arrays and clean arrays of objects
      if (Array.isArray(value)) {
        if (value.length === 0) {
          delete result[key]
        } else if (typeof value[0] === 'object') {
          result[key] = value.map(item => this.cleanNullValues(item))
            .filter(item => Object.keys(item).length > 0)
          
          if (result[key].length === 0) {
            delete result[key]
          }
        }
      }
      
      // Handle objects recursively
      else if (typeof value === 'object') {
        result[key] = this.cleanNullValues(value)
        if (Object.keys(result[key]).length === 0) {
          delete result[key]
        }
      }
      
      // Handle empty strings
      else if (typeof value === 'string' && value.trim() === '') {
        delete result[key]
      }
    }
    
    return result as T
  }
}
