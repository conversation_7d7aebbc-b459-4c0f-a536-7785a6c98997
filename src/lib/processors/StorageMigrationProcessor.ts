import { ProcessorResult, ProcessingJob } from '@/types/processing';
import { StorageProviderRegistry } from '@/lib/storage/StorageProviderRegistry';
import { IStorageProvider } from '@/lib/storage/IStorageProvider';
import { pool } from '@/lib/db';
import path from 'path';

export interface StorageMigrationJobData {
  sourceProvider: string;
  targetProvider: string;
  fileIds?: string[];
  batchSize?: number;
  dryRun?: boolean;
}

export class StorageMigrationProcessor {
  private name = 'storage_migration_bulk';

  constructor() {
    // Initialize processor
  }

  async process(job: any): Promise<ProcessorResult> {
    return this.processBulkMigration(job);
  }

  async processBulkMigration(job: any): Promise<ProcessorResult> {
    const jobData = job.data.options as StorageMigrationJobData;
    const { sourceProvider, targetProvider, fileIds, batchSize = 10, dryRun = false } = jobData;

    console.log(`Starting storage migration: ${sourceProvider} -> ${targetProvider}`);
    
    if (dryRun) {
      return await this.performDryRun(sourceProvider, targetProvider, fileIds);
    }

    return await this.performMigration(sourceProvider, targetProvider, fileIds, batchSize);
  }

  private async performDryRun(
    sourceProvider: string, 
    targetProvider: string, 
    fileIds?: string[]
  ): Promise<ProcessorResult> {
    try {
      let filesQuery = `
        SELECT file_id, original_name, storage_provider, storage_path, file_size_bytes
        FROM files 
        WHERE storage_provider = $1
      `;
      const queryParams = [sourceProvider];

      if (fileIds && fileIds.length > 0) {
        filesQuery += ` AND file_id = ANY($2)`;
        queryParams.push(fileIds as any);
      }

      const filesResult = await pool.query(filesQuery, queryParams);
      const files = filesResult.rows;

      const totalSize = files.reduce((sum, file) => sum + parseInt(file.file_size_bytes), 0);
      
      console.log(`Dry run: Would migrate ${files.length} files (${this.formatBytes(totalSize)}) from ${sourceProvider} to ${targetProvider}`);

      return {
        processed: files.length,
        successful: files.length,
        failed: 0,
        errors: []
      };
    } catch (error: any) {
      console.error(`Dry run failed: ${error.message}`);
      return {
        processed: 0,
        successful: 0,
        failed: 0,
        errors: [error.message]
      };
    }
  }

  private async performMigration(
    sourceProvider: string,
    targetProvider: string,
    fileIds: string[] | undefined,
    batchSize: number
  ): Promise<ProcessorResult> {
    const result: ProcessorResult = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: []
    };

    try {
      // Get files to migrate
      let filesQuery = `
        SELECT file_id, original_name, storage_provider, storage_path, storage_metadata,
               file_size_bytes, mime_type, content_hash
        FROM files 
        WHERE storage_provider = $1
      `;
      const queryParams = [sourceProvider];

      if (fileIds && fileIds.length > 0) {
        filesQuery += ` AND file_id = ANY($2)`;
        queryParams.push(fileIds as any);
      }

      const filesResult = await pool.query(filesQuery, queryParams);
      const files = filesResult.rows;

      if (files.length === 0) {
        console.log('No files found to migrate');
        return result;
      }

      // Get storage providers
      const registry = StorageProviderRegistry.getInstance();
      const sourceStorage = registry.getProvider(sourceProvider);
      const targetStorage = registry.getProvider(targetProvider);

      if (!sourceStorage || !targetStorage) {
        throw new Error(`Storage providers not found: ${sourceProvider} -> ${targetProvider}`);
      }

      console.log(`Starting migration of ${files.length} files in batches of ${batchSize}`);

      // Process files in batches
      for (let i = 0; i < files.length; i += batchSize) {
        const batch = files.slice(i, i + batchSize);
        
        await Promise.all(batch.map(async (file) => {
          try {
            await this.migrateFile(file, sourceStorage, targetStorage);
            result.successful++;
          } catch (error: any) {
            result.failed++;
            result.errors.push(`File ${file.original_name}: ${error.message}`);
            console.error(`Failed to migrate file ${file.original_name}: ${error.message}`);
          }
          result.processed++;
        }));

        console.log(`Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(files.length / batchSize)} (${result.processed}/${files.length} files)`);
      }

      console.log(`Migration completed: ${result.successful} successful, ${result.failed} failed`);

    } catch (error: any) {
      console.error(`Migration failed: ${error.message}`);
      result.errors.push(`Migration failed: ${error.message}`);
    }

    return result;
  }

  private async migrateFile(
    file: any,
    sourceStorage: IStorageProvider,
    targetStorage: IStorageProvider
  ): Promise<void> {
    try {
      // Download file from source
      const fileBuffer = await sourceStorage.downloadFile(file.storage_path);
      if (!fileBuffer) {
        throw new Error('File not found in source storage');
      }

      // Generate new path for target storage
      const newPath = this.generateTargetPath(file, targetStorage.name);
      
      // Upload to target storage
      const uploadResult = await targetStorage.uploadFile(fileBuffer, newPath, {
        originalName: file.original_name,
        mimeType: file.mime_type,
        contentHash: file.content_hash
      });

      if (!uploadResult.success) {
        throw new Error(`Upload failed: ${uploadResult.error}`);
      }

      // Verify the upload by checking if file exists in target storage
      const fileExists = await targetStorage.fileExists(newPath);
      if (!fileExists) {
        throw new Error('File upload verification failed - file not found in target storage');
      }

      // Get the actual storage URL from the target provider
      const actualStorageUrl = await targetStorage.getFileUrl(newPath);
      
      // Get storage provider metadata
      let storageProviderMetadata = {};
      if (targetStorage.name === 'azure' && 'getStorageInfo' in targetStorage) {
        storageProviderMetadata = (targetStorage as any).getStorageInfo();
      }

      // Only update database after successful upload and verification
      const newMetadata = {
        ...file.storage_metadata,
        migrated_from: file.storage_provider,
        migrated_at: new Date().toISOString(),
        migration_verified: true,
        actual_storage_url: actualStorageUrl,
        ...storageProviderMetadata
      };

      try {
        await pool.query(
          `UPDATE files 
           SET storage_provider = $1, storage_path = $2, storage_metadata = $3, updated_at = CURRENT_TIMESTAMP
           WHERE file_id = $4`,
          [targetStorage.name, newPath, JSON.stringify(newMetadata), file.file_id]
        );

        console.log(`Successfully migrated and verified file: ${file.original_name}`);
      } catch (dbError: any) {
        // If database update fails, clean up the uploaded file
        console.error(`Database update failed for ${file.original_name}, cleaning up uploaded file`);
        try {
          await targetStorage.deleteFile(newPath);
        } catch (cleanupError) {
          console.error(`Failed to cleanup uploaded file ${newPath}:`, cleanupError);
        }
        throw new Error(`Database update failed: ${dbError.message}`);
      }

    } catch (error: any) {
      console.error(`Failed to migrate file ${file.original_name}: ${error.message}`);
      throw error;
    }
  }

  private generateTargetPath(file: any, targetProvider: string): string {
    const hash = file.content_hash;
    const extension = path.extname(file.original_name);
    
    // Create hierarchical path based on content hash
    const pathSegments = [
      hash.substring(0, 2),
      hash.substring(2, 4),
      hash + extension
    ];
    
    return pathSegments.join('/');
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}