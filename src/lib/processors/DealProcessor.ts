import { BaseProcessor } from "@/lib/processors/BaseProcessor";
import { EntityData, ProcessorOptions } from "@/types/processing";
import {
  generateDealExtractionPrompt,
  generateMultipleFilesPrompt,
} from "../prompts/deal-extraction-simplified";
import {
  LLMFactory,
  createProcessorLoggerAdapter,
  LLMMessage,
  LLMResponse,
} from "../llm";
import {
  AbstractTextExtractor,
  PDFTextExtractor,
  CSVTextExtractor,
  XLSXTextExtractor,
  WordTextExtractor,
} from "../utils/textExtractor";
import { DataFormatter, defaultDataFormatter } from "../utils/dataFormatter";
import { getDealsTableColumns } from "../utils/dbSchema";
import * as XLSX from "xlsx";
import * as fs from "fs";
import * as path from "path";

// EntityData for deal file queue
interface DealFileEntity extends EntityData {
  id: number;
  status: string;
  files: {
    type: string;
    content: string;
    buffer?: Buffer;
    mime_type?: string;
  }[];
}

// Extended processor options for DealProcessor
interface DealProcessorOptions extends ProcessorOptions {
  useUniversalPrompt?: boolean;
  skipDatabaseInsert?: boolean;
  llmModel?:
    | "gemini-flash"
    | "gemini-pro"
    | "openai-4o"
    | "openai-4o-mini"
    | "openai-o1-preview"
    | "openai-o1-mini"; // Add model selection option
}

// Interface for the enhanced extraction response
interface UniversalExtractionResponse {
  extracted_data: Record<string, any>;
  custom_fields: Record<string, any>;
  metadata: {
    document_type: string;
    extraction_confidence: string;
    processing_notes: string;
    missing_critical_fields: string[];
    data_quality_issues: string[];
    custom_fields_count: number;
    custom_field_categories: string[];
  };
  extra_fields?: Record<string, any>;
}

// Add MappingData type
interface MappingData {
  [key: string]: string[];
}

export class DealProcessor extends BaseProcessor {
  private llmProvider;
  private useUniversalPrompt: boolean;
  private skipDatabaseInsert: boolean;
  private mappingCache: MappingData | null = null;
  private selectedModel: string = "gemini-flash"; // Store the selected model

  constructor(options: DealProcessorOptions = {}) {
    super("DealProcessor", options);
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));

    // Determine which model to use (default to gemini-flash for backward compatibility)
    const selectedModel = options.llmModel || "gemini-flash";
    this.selectedModel = selectedModel; // Store for later use

    // Debug logging to track model selection
    console.log("🔍 DealProcessor Constructor - Model Selection Debug:");
    console.log("  - Received options.llmModel:", options.llmModel);
    console.log("  - Selected model:", selectedModel);

    // Create the appropriate provider based on model selection
    if (selectedModel.startsWith("openai")) {
      // Map the model name to the actual OpenAI model
      let modelName: string;
      switch (selectedModel) {
        case "openai-4o":
          modelName = "gpt-4o";
          break;
        case "openai-4o-mini":
          modelName = "gpt-4o-mini";
          break;
        case "openai-o1-preview":
          modelName = "o1-preview";
          break;
        case "openai-o1-mini":
          modelName = "o1-mini";
          break;
        default:
          modelName = "gpt-4o";
      }

      console.log("  - Using OpenAI provider with model:", modelName);

      this.llmProvider = LLMFactory.createProvider(
        "openai",
        loggerAdapter,
        process.env.OPENAI_API_KEY
          ? {
              apiKey: process.env.OPENAI_API_KEY,
              defaultOptions: {
                temperature: 0.1,
                maxTokens: 8000,
                model: modelName,
              },
            }
          : {}
      );
    } else {
      // Map the model name to the actual Gemini model
      const modelName =
        selectedModel === "gemini-pro" ? "gemini-2.5-pro" : "gemini-2.5-flash";

      console.log("  - Using Gemini provider with model:", modelName);

      this.llmProvider = LLMFactory.createProvider(
        "gemini",
        loggerAdapter,
        process.env.GEMINI_API_KEY
          ? {
              apiKey: process.env.GEMINI_API_KEY,
              defaultOptions: {
                temperature: 0.1,
                maxTokens: 8000,
                model: modelName,
              },
            }
          : {}
      );
    }

    // Default to universal prompt, but can be overridden
    this.useUniversalPrompt = options.useUniversalPrompt !== false;
    // Default to inserting into database, but can be overridden
    this.skipDatabaseInsert = options.skipDatabaseInsert === true;
  }

  // Process method for queue-based job execution
  async processJob(jobData: any): Promise<{ success: boolean; error?: string; result?: any }> {
    try {
      this.log("info", `Starting DealProcessor job processing`);
      this.log("debug", `Job data received: ${JSON.stringify(jobData, null, 2)}`);
      
      // Extract files from job data - handle different data structures
      let files: any[] = [];
      if (jobData.files) {
        files = jobData.files;
      } else if (jobData.options && jobData.options.files) {
        files = jobData.options.files;
      } else if (Array.isArray(jobData)) {
        files = jobData;
      }
      
      if (files.length === 0) {
        throw new Error(`No files provided in job data. Available keys: ${Object.keys(jobData || {}).join(', ')}`);
      }

      // Convert base64 files back to Buffer objects
      const fileBuffers = files.map((file: any) => ({
        buffer: Buffer.from(file.buffer, "base64"),
        mimeType: file.mimeType,
        fileName: file.fileName
      }));

      // Process the files using the internal method
      const result = await this.processFilesInternal(fileBuffers);
      
      if (result.success) {
        this.log("info", `DealProcessor job completed successfully. Deal ID: ${result.dealId}`);
        return {
          success: true,
          result: {
            dealId: result.dealId,
            extractedData: result.extractedData,
            processingDuration: result.processingDuration
          }
        };
      } else {
        this.log("error", `DealProcessor job failed: ${result.error}`);
        return {
          success: false,
          error: result.error
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log("error", `DealProcessor job processing error: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage
      };
    }
  }


  /**
   * Get the actual model name used by the LLM provider
   */
  private getActualModelName(): string {
    if (this.selectedModel.startsWith("openai")) {
      switch (this.selectedModel) {
        case "openai-4o":
          return "gpt-4o";
        case "openai-4o-mini":
          return "gpt-4o-mini";
        case "openai-o1-preview":
          return "o1-preview";
        case "openai-o1-mini":
          return "o1-mini";
        default:
          return "gpt-4o";
      }
    } else {
      return this.selectedModel === "gemini-pro"
        ? "gemini-2.5-pro"
        : "gemini-2.5-flash";
    }
  }

  /**
   * Get the provider name based on selected model
   */
  private getProviderName(): string {
    return this.selectedModel.startsWith("openai") ? "openai" : "gemini";
  }

  /**
   * Get the appropriate media type for OpenAI vision API
   */
  private getMediaTypeForOpenAI(mimeType: string): string {
    // OpenAI supports these formats for vision/document processing
    const supportedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "application/pdf", // PDFs are supported directly in chat completions API
    ];

    if (supportedTypes.includes(mimeType)) {
      return mimeType;
    }

    // Default to application/octet-stream for unknown types
    return "application/octet-stream";
  }

  /**
   * Check if a file type is supported by OpenAI's vision/document API
   */
  private isOpenAIVisionSupported(mimeType: string): boolean {
    const supportedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "application/pdf", // PDFs are now supported directly in chat completions API
    ];
    return supportedTypes.includes(mimeType);
  }

  /**
   * Fetch standardized mappings from central_mapping table
   */
  private async getMappings(): Promise<MappingData> {
    if (this.mappingCache) {
      return this.mappingCache;
    }
    try {
      const sql = `
        SELECT type, level_1, value_1, level_2, value_2 
        FROM central_mapping 
        WHERE is_active = true 
        ORDER BY type, level_1, value_1
      `;
      const results = await this.query(sql, []);
      const mappings: MappingData = {};
      for (const row of results) {
        const type = row.type as string;
        const level1 = row.level_1 as string;
        const value1 = row.value_1 as string;
        const level2 = row.level_2 as string;
        const value2 = row.value_2 as string;
        if (!mappings[type]) {
          mappings[type] = [];
        }
        if (value1 && !mappings[type].includes(value1)) {
          mappings[type].push(value1);
        }
        if (level2 && value2) {
          const subcategoryKey = `${type} - ${level2}`;
          if (!mappings[subcategoryKey]) {
            mappings[subcategoryKey] = [];
          }
          if (!mappings[subcategoryKey].includes(value2)) {
            mappings[subcategoryKey].push(value2);
          }
        }
      }
      this.mappingCache = mappings;
      this.log(
        "info",
        `Loaded ${
          Object.keys(mappings).length
        } mapping types from central_mapping table`
      );
      return mappings;
    } catch (error) {
      this.log("error", `Error fetching mappings: ${error}`);
      return {};
    }
  }

    /**
   * Simple bullet-point parser - just convert to basic JSON format
 
  /**
   * Convert string to camelCase
 
  /**
   * Create error file for debugging JSON parsing failures
   */
  private async createErrorFile(
    fileName: string,
    errorMessage: string,
    llmResponse: string,
    additionalContext?: Record<string, any>
  ): Promise<string> {
    this.log("debug", `Creating error file for: ${fileName}, error: ${errorMessage.substring(0, 100)}`);
    try {
      // Create error directory if it doesn't exist
      const errorDir = path.join(process.cwd(), "error-files");
      if (!fs.existsSync(errorDir)) {
        fs.mkdirSync(errorDir, { recursive: true });
      }

      // Generate timestamp-based filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, "_");
      const errorFileName = `deal-processor-error_${timestamp}_${sanitizedFileName}.json`;
      const errorFilePath = path.join(errorDir, errorFileName);

      // Create error content
      const errorContent = {
        timestamp: new Date().toISOString(),
        originalFileName: fileName,
        errorMessage: errorMessage,
        processor: "DealProcessor",
        llmModel: this.selectedModel,
        llmProvider: this.getProviderName(),
        rawLLMResponse: llmResponse,
        responseLength: llmResponse.length,
        responsePreview: llmResponse.substring(0, 500) + (llmResponse.length > 500 ? "..." : ""),
        additionalContext: additionalContext || {}
      };

      // Write error file
      fs.writeFileSync(errorFilePath, JSON.stringify(errorContent, null, 2));

      this.log("error", `Error file created: ${errorFilePath}`);
      this.log("debug", `Error file size: ${fs.statSync(errorFilePath).size} bytes`);
      return errorFilePath;
    } catch (writeError) {
      this.log("error", `Failed to create error file: ${writeError}`);
      return "";
    }
  }

  /**
   * Save the complete prompt being sent to the LLM for debugging
   */
  private async savePromptLog(
    messages: any[],
    provider: string,
    fileNames: string[],
    additionalContext?: Record<string, any>
  ): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const sanitizedFileNames = fileNames.map(name => name.replace(/[^a-zA-Z0-9.-]/g, "_")).join("_");
      const promptFileName = `prompt-${provider}-${sanitizedFileNames}-${timestamp}.json`;
      const promptDir = path.join(process.cwd(), "prompt-logs");
      
      // Ensure prompt logs directory exists
      if (!fs.existsSync(promptDir)) {
        fs.mkdirSync(promptDir, { recursive: true });
      }
      
      const promptFilePath = path.join(promptDir, promptFileName);
      
      // Clean up messages for logging (remove binary data but keep structure)
      const cleanedMessages = messages.map(msg => {
        if (provider === "gemini") {
          return {
            role: msg.role,
            parts: msg.parts?.map((part: any) => {
              if (part.fileBuffer) {
                return {
                  type: "file",
                  fileName: part.fileName,
                  mimeType: part.mimeType,
                  fileSize: part.fileBuffer.length,
                  fileBuffer: "[BINARY DATA REMOVED]"
                };
              }
              return part;
            })
          };
        } else {
          // OpenAI format
          return {
            role: msg.role,
            content: Array.isArray(msg.content) 
              ? msg.content.map((content: any) => {
                  if (content.type === "image_url" || content.type === "file") {
                    return {
                      type: content.type,
                      [content.type === "image_url" ? "image_url" : "file"]: {
                        ...content[content.type === "image_url" ? "image_url" : "file"],
                        [content.type === "image_url" ? "url" : "file_data"]: "[BASE64 DATA REMOVED]"
                      }
                    };
                  }
                  return content;
                })
              : msg.content
          };
        }
      });
      
      const promptData = {
        timestamp: new Date().toISOString(),
        provider: provider,
        model: this.selectedModel,
        fileNames: fileNames,
        fileCount: fileNames.length,
        messages: cleanedMessages,
        additionalContext: additionalContext || {},
        totalMessageCount: messages.length
      };
      
      fs.writeFileSync(promptFilePath, JSON.stringify(promptData, null, 2));
      this.log("info", `Prompt details saved to: ${promptFilePath}`);
      
      return promptFilePath;
    } catch (err) {
      this.log("error", `Failed to create prompt log file: ${err}`);
      return `Failed to create prompt log file: ${err}`;
    }
  }

  /**
   * Fetch investment_criteria table columns dynamically
   */
  private async getInvestmentCriteriaColumns(): Promise<string[]> {
    try {
      const schemaRes = await this.query(
        `SELECT column_name FROM information_schema.columns 
         WHERE table_name = 'investment_criteria' AND table_schema = 'public' 
         ORDER BY ordinal_position`
      );
      const columns = schemaRes.map((row: any) => row.column_name);
      this.log("debug", `Loaded ${columns.length} investment_criteria columns: ${columns.join(", ")}`);
      
      // Check if we have the old simple table structure
      if (columns.length === 3 && columns.includes("id") && columns.includes("criteria") && columns.includes("value")) {
        this.log("warn", "⚠️  Detected OLD investment_criteria table structure with only [id, criteria, value] columns");
        this.log("warn", "⚠️  Need to run migration: sql-files/create_investment_criteria_table.sql");
        this.log("warn", "⚠️  Investment criteria insertion will be skipped until table is updated");
        return [];
      }
      
      // Check if we have the new comprehensive table structure
      if (columns.includes("entity_type") && columns.includes("capital_position") && columns.includes("minimum_deal_size")) {
        this.log("info", "✅ Detected NEW investment_criteria table structure with comprehensive columns");
        return columns;
      }
      
      this.log("warn", `⚠️  Unknown investment_criteria table structure. Columns: ${columns.join(", ")}`);
      return columns;
    } catch (error) {
      this.log("error", `Error fetching investment_criteria columns: ${error}`);
      return [];
    }
  }

  /**
   * Process and insert investment criteria from extracted data
   * Expects new schema format with database column names from LLM
   */
  private async processInvestmentCriteria(
    dealId: number,
    extractedData: Record<string, any>
  ): Promise<void> {
    try {
      const investmentCriteriaArray = extractedData.investmentCriteria || [];
      
      if (!Array.isArray(investmentCriteriaArray) || investmentCriteriaArray.length === 0) {
        this.log("info", `No investment criteria found for deal ${dealId}`);
        return;
      }

      this.log("debug", `Found ${investmentCriteriaArray.length} investment criteria to process`);
      this.log("debug", `Sample criteria fields: ${Object.keys(investmentCriteriaArray[0] || {}).join(", ")}`);

      // Get dynamic columns
      const columns = await this.getInvestmentCriteriaColumns();
      
      if (columns.length === 0) {
        this.log("error", "No investment_criteria columns found, skipping criteria processing");
        return;
      }

      this.log("debug", `Available database columns: ${columns.join(", ")}`);

      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < investmentCriteriaArray.length; i++) {
        const criteria = investmentCriteriaArray[i];
        this.log("debug", `Processing criteria ${i + 1}/${investmentCriteriaArray.length}`);
        this.log("debug", `Criteria keys: ${Object.keys(criteria).join(", ")}`);
        this.log("debug", `Capital position: ${JSON.stringify(criteria.capital_position || criteria.capitalPosition || 'unknown')}`);
        
        try {
          await this.insertInvestmentCriteria(dealId, criteria, columns);
          successCount++;
        } catch (error) {
          this.log("error", `Failed to insert criteria ${i + 1}: ${error}`);
          errorCount++;
        }
      }

      this.log("info", `Investment criteria processing complete - Success: ${successCount}, Errors: ${errorCount}`);
    } catch (error) {
      this.log("error", `Error processing investment criteria for deal ${dealId}: ${error}`);
    }
  }

  /**
   * Insert a single investment criteria record
   * First tries new schema (database column names), then falls back to old schema mapping
   */
  private async insertInvestmentCriteria(
    dealId: number,
    criteria: Record<string, any>,
    columns: string[]
  ): Promise<void> {
    try {
      // First attempt: Try using LLM fields as-is (new schema - database column names)
      const normalizedCriteria: Record<string, any> = { ...criteria };
      
      // Set required entity fields
      normalizedCriteria.entity_type = "Deal";
      normalizedCriteria.entity_id = dealId.toString();
      
      // Set default values for system fields if not present
      if (!normalizedCriteria.created_at) normalizedCriteria.created_at = new Date().toISOString();
      if (!normalizedCriteria.updated_at) normalizedCriteria.updated_at = new Date().toISOString();
      if (normalizedCriteria.is_active === null || normalizedCriteria.is_active === undefined) {
        normalizedCriteria.is_active = true;
      }

      // Debug: Log the field comparison
      const llmFields = Object.keys(normalizedCriteria);
      this.log("debug", `LLM Output Fields (${llmFields.length}): ${llmFields.join(", ")}`);
      this.log("debug", `Database Columns (${columns.length}): ${columns.join(", ")}`);

      // Check how many fields match database columns
      const matchingColumns = columns.filter(col => 
        normalizedCriteria.hasOwnProperty(col) && 
        normalizedCriteria[col] !== undefined
      );
      
      this.log("debug", `Matching columns (${matchingColumns.length}): ${matchingColumns.join(", ")}`);
      
      // Show non-matching LLM fields
      const nonMatchingLLMFields = llmFields.filter(field => !columns.includes(field));
      if (nonMatchingLLMFields.length > 0) {
        this.log("debug", `Non-matching LLM fields (${nonMatchingLLMFields.length}): ${nonMatchingLLMFields.join(", ")}`);
      }

      // If we have good matches, proceed with new schema
      if (matchingColumns.length >= 3) { // Require at least 3 matching fields including entity fields
        this.log("debug", `Using new schema - found ${matchingColumns.length} matching database columns`);
        await this.executeInvestmentCriteriaInsert(dealId, normalizedCriteria, matchingColumns);
        return;
      }

      // Fallback: Try old schema mapping
      this.log("warn", `New schema had only ${matchingColumns.length} matches, trying old schema fallback`);
      const oldSchemaMapping = await this.tryOldSchemaMapping(criteria, columns);
      
      if (oldSchemaMapping.validColumns.length >= 3) {
        this.log("debug", `Using old schema fallback - found ${oldSchemaMapping.validColumns.length} matching columns`);
        await this.executeInvestmentCriteriaInsert(dealId, oldSchemaMapping.normalizedCriteria, oldSchemaMapping.validColumns);
        return;
      }

      // If both fail, log and skip
      this.log("warn", `Both new and old schema failed, skipping investment criteria insertion`);
      
    } catch (error) {
      this.log("error", `❌ Error inserting investment criteria: ${error}`);
      this.log("debug", `Original criteria data: ${JSON.stringify(criteria, null, 2)}`);
      throw error;
    }
  }

  /**
   * Try old schema mapping as fallback
   */
  private async tryOldSchemaMapping(
    criteria: Record<string, any>,
    columns: string[]
  ): Promise<{ normalizedCriteria: Record<string, any>; validColumns: string[] }> {
    // Old schema mapping (only used as fallback)
    const oldToNewFieldMapping: Record<string, string> = {
      'entityType': 'entity_type',
      'entityId': 'entity_id',
      'criteriaId': 'criteria_id',
      'targetReturn': 'target_return',
      'propertyTypes': 'property_types',
      'propertySubCategories': 'property_sub_categories',
      'minimumDealSize': 'minimum_deal_size',
      'maximumDealSize': 'maximum_deal_size',
      'minHoldPeriod': 'min_hold_period',
      'maxHoldPeriod': 'max_hold_period',
      'financialProducts': 'financial_products',
      'historicalIrr': 'historical_irr',
      'historicalEm': 'historical_em',
      'loanProgram': 'loan_program',
      'loanType': 'loan_type',
      'capitalSource': 'capital_source',
      'structuredLoanTranche': 'structured_loan_tranche',
      'minLoanTerm': 'min_loan_term',
      'maxLoanTerm': 'max_loan_term',
      'loanInterestRate': 'interest_rate',
      'loanInterestRateSofr': 'interest_rate_sofr',
      'loanInterestRateWsj': 'interest_rate_wsj',
      'loanInterestRatePrime': 'interest_rate_prime',
      'loanToValueMin': 'loan_to_value_min',
      'loanToValueMax': 'loan_to_value_max',
      'loanToCostMin': 'loan_to_cost_min',
      'loanToCostMax': 'loan_to_cost_max',
      'loanOriginationFeeMin': 'loan_origination_fee_min',
      'loanOriginationFeeMax': 'loan_origination_fee_max',
      'loanExitFeeMin': 'loan_exit_fee_min',
      'loanExitFeeMax': 'loan_exit_fee_max',
      'minLoanDscr': 'min_loan_dscr',
      'maxLoanDscr': 'max_loan_dscr',
      'recourseLoan': 'recourse_loan',
      'extraFields': 'extra_fields',
      'createdAt': 'created_at',
      'updatedAt': 'updated_at',
      'createdBy': 'created_by',
      'updatedBy': 'updated_by',
      'isActive': 'is_active',
      'capitalPosition': 'capital_position',
      'loanTypeNormalized': 'loan_type_normalized',
      'closingTimeWeeks': 'closing_time_weeks',
      'assetClasses': 'asset_classes',
      'loanLtv': 'loan_ltv',
      'loanLtc': 'loan_ltc',
      'loanOriginationFee': 'loan_origination_fee',
      'loanExitFee': 'loan_exit_fee'
    };

    const normalizedCriteria: Record<string, any> = {};
    
    // Apply old schema mapping
    for (const [llmField, value] of Object.entries(criteria)) {
      const dbField = oldToNewFieldMapping[llmField] || llmField;
      normalizedCriteria[dbField] = value;
    }

    // Set required entity fields
    normalizedCriteria.entity_type = "Deal";
    normalizedCriteria.entity_id = "placeholder"; // Will be set during insert
    
    // Set default values for system fields if not present
    if (!normalizedCriteria.created_at) normalizedCriteria.created_at = new Date().toISOString();
    if (!normalizedCriteria.updated_at) normalizedCriteria.updated_at = new Date().toISOString();
    if (normalizedCriteria.is_active === null || normalizedCriteria.is_active === undefined) {
      normalizedCriteria.is_active = true;
    }

    // Check matches
    const validColumns = columns.filter(col => 
      normalizedCriteria.hasOwnProperty(col) && 
      normalizedCriteria[col] !== undefined
    );

    return { normalizedCriteria, validColumns };
  }

  /**
   * Execute the actual database insert for investment criteria
   */
  private async executeInvestmentCriteriaInsert(
    dealId: number,
    normalizedCriteria: Record<string, any>,
    validColumns: string[]
  ): Promise<void> {
    // Set the correct deal ID
    normalizedCriteria.entity_id = dealId.toString();
    
    // Get column types for proper formatting
    const columnTypes = await DataFormatter.getColumnTypes('investment_criteria', this.pool);
    
    // Format the record using the data formatter
    const formattedCriteria = defaultDataFormatter.formatRecordForInsertion(normalizedCriteria, columnTypes);
    
    // Filter to only include valid columns that have formatted values
    const finalColumns = validColumns.filter(col => formattedCriteria[col] !== undefined);
    const finalValues = finalColumns.map(col => formattedCriteria[col]);
    
    if (finalColumns.length === 0) {
      this.log("warn", "No valid columns found after formatting, skipping insertion");
      return;
    }
    
    const columnNames = finalColumns.map(col => `"${col}"`).join(", ");
    const placeholders = finalColumns.map((_, i) => `$${i + 1}`).join(", ");

    const sql = `INSERT INTO investment_criteria (${columnNames}) VALUES (${placeholders})`;
    
    this.log("debug", `Preparing to insert investment criteria:`);
    this.log("debug", `  - Columns (${finalColumns.length}): ${finalColumns.join(", ")}`);
    this.log("debug", `  - Values: ${finalValues.map((v, i) => `${finalColumns[i]}=${typeof v === 'string' && v.length > 50 ? v.substring(0, 50) + '...' : v}`).join(", ")}`);
    
    await this.query(sql, finalValues);
    
    this.log("info", `✅ Successfully inserted investment criteria with ${finalColumns.length} fields (capitalPosition: ${JSON.stringify(formattedCriteria.capital_position)})`);
  }

  // Factory method to get the appropriate extractor
  static getExtractor(mimeType: string | undefined): AbstractTextExtractor | null {
    if (!mimeType) return null;
    if (mimeType === "application/pdf") return new PDFTextExtractor();
    if (mimeType === "text/csv") return new CSVTextExtractor();
    if (
      mimeType ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      mimeType === "application/vnd.ms-excel"
    )
      return new XLSXTextExtractor();
    // Add Word document support
    if (
      mimeType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
      mimeType === "application/msword"
    )
      return new WordTextExtractor();
    return null;
  }

  // 1. Get unprocessed deal files from the queue
  async getUnprocessedEntities(): Promise<DealFileEntity[]> {
    // Look for deals that haven't been processed yet (no extraction_timestamp or failed processing)
    const sql = `
      SELECT 
        id,
        document_filename,
        document_source,
        document_size_bytes,
        status,
        review_status
      FROM deals 
      WHERE (extraction_timestamp IS NULL OR status = 'failed' OR review_status = 'pending')
      AND document_filename IS NOT NULL
      ORDER BY id ASC 
      LIMIT $1
    `;

    const rows = await this.query(sql, [this.options.limit || 10]);

    return rows.map((row) => ({
      id: Number(row.id),
      status: String(row.status || "pending"),
      files: [
        {
          type: "document",
          content: "", // Content will be loaded separately if needed
          mime_type: String(row.document_source || "text/plain"),
        },
      ],
    }));
  }

  // 2. Process a single deal file entity
  async processEntity(
    entity: DealFileEntity
  ): Promise<{ success: boolean; error?: string }> {
    const startTime = Date.now();

    try {
      this.log("info", `Processing deal entity ${entity.id}`);

      // Get the existing deal record to see what we're working with
      const dealRecord = await this.query("SELECT * FROM deals WHERE id = $1", [
        entity.id,
      ]);

      if (dealRecord.length === 0) {
        this.log("error", `Deal record ${entity.id} not found`);
        return { success: false, error: "Deal record not found" };
      }

      const deal = dealRecord[0];

      // If the deal already has extracted data and is marked as completed, skip processing
      if (
        deal.extraction_timestamp &&
        deal.status === "active" &&
        deal.review_status === "completed"
      ) {
        this.log("info", `Deal ${entity.id} already processed, skipping`);
        return { success: true };
      }

      // Extract text from file if buffer and mime type are provided
      const structuredDocs: Record<string, string> = {};
      let documentContent = "";

      for (const file of entity.files) {
        let text = file.content;
        if (file.buffer && file.mime_type) {
          const extractor = DealProcessor.getExtractor(file.mime_type);
          if (extractor) {
            try {
              text = await extractor.extractText(file.buffer);
            } catch (err) {
              this.log(
                "warn",
                `File extraction failed for ${file.type}: ${err}. Falling back to plain text.`
              );
            }
          }
        }
        structuredDocs[file.type] = text;
        documentContent += text;
      }

      // If we don't have document content, we can't process
      if (!documentContent.trim()) {
        this.log("warn", `No document content available for deal ${entity.id}`);
        return { success: false, error: "No document content available" };
      }

      // For now, just mark as completed since we're using processFileDirectly for actual processing
      await this.updateEntityStatus(entity.id, true, undefined);
      return { success: true };
    } catch (err) {
      this.log("error", `Error processing entity ${entity.id}: ${err}`);
      await this.updateEntityStatus(
        entity.id,
        false,
        err instanceof Error ? err.message : String(err)
      );
      return {
        success: false,
        error: err instanceof Error ? err.message : String(err),
      };
    }
  }

  // Implement updateEntityStatus
  async updateEntityStatus(
    entityId: number,
    success: boolean,
    error?: string
  ): Promise<void> {
    const status = success ? "active" : "failed";
    const reviewStatus = success ? "completed" : "failed";
    const sql = `
      UPDATE deals 
      SET status = $1, 
          review_status = $2, 
          extraction_timestamp = $3,
          processing_notes = $4
      WHERE id = $5
    `;
    await this.query(sql, [
      status,
      reviewStatus,
      success ? new Date() : null,
      error || null,
      entityId,
    ]);
  }

  // Unified processing method - handles both single and multiple files
  async processFiles(
    files: Array<{
      buffer: Buffer;
      mimeType: string;
      fileName: string;
    }>
  ): Promise<{
    success: boolean;
    dealId?: number;
    error?: string;
    extractedData?: any;
    llmResponse?: any;
    processingDuration?: number;
  }> {
    // Call the actual implementation that handles both single and multiple files
    return this.processFilesInternal(files);
  }

  // Legacy method for backward compatibility - wraps single file in array
  async processFileDirectly(
    fileBuffer: Buffer,
    mimeType: string,
    fileName: string
  ): Promise<{
    success: boolean;
    dealId?: number;
    error?: string;
    extractedData?: any;
    llmResponse?: any;
    processingDuration?: number;
  }> {
    // Convert single file to array and use unified method
    return this.processFiles([{ buffer: fileBuffer, mimeType, fileName }]);
  }

  /**
   * Update an existing deal with new extracted data from files
   */
  async updateExistingDeal(
    dealId: number,
    files: Array<{
      buffer: Buffer;
      mimeType: string;
      fileName: string;
    }>
  ): Promise<{
    success: boolean;
    dealId?: number;
    error?: string;
    extractedData?: any;
    llmResponse?: any;
    processingDuration?: number;
  }> {
    const startTime = Date.now();
    
    try {
      // Verify the deal exists
      const dealCheck = await this.query(
        "SELECT deal_id FROM deals WHERE deal_id = $1",
        [dealId]
      );
      
      if (dealCheck.length === 0) {
        throw new Error(`Deal with ID ${dealId} not found`);
      }

      // Process files using the same logic as processFilesInternal
      const result = await this.processFilesInternal(files);
      
      if (!result.success) {
        return result;
      }

      const { extractedData, llmResponse } = result;
      
      // Update the existing deal with new extracted data
      const dealsTableColumns = await getDealsTableColumns();
      const coreFields: Record<string, any> = {};
      const extraFields: Record<string, any> = {};

      // Separate core fields from extra fields
      for (const [key, value] of Object.entries(extractedData.extracted_data || {})) {
        if (dealsTableColumns.has(key)) {
          coreFields[key] = value;
        } else {
          extraFields[key] = value;
        }
      }

      // Add metadata fields
      const metadata = extractedData.metadata || {};
      const isMultipleFiles = files.length > 1;
      const totalFileSize = files.reduce((total, f) => total + f.buffer.length, 0);

      const metadataFields = {
        extraction_timestamp: new Date(),
        llm_model_used: metadata.llm_model_used || this.getActualModelName(),
        llm_provider: metadata.llm_provider || this.getProviderName(),
        processor_version: metadata.processor_version || "1.0",
        processing_duration_ms: result.processingDuration || 0,
        document_type: Array.isArray(metadata.document_type)
          ? metadata.document_type
          : metadata.document_type
          ? [metadata.document_type]
          : ["deal_document"],
        extraction_method: isMultipleFiles
          ? ["simplified_multiple"]
          : ["simplified"],
        document_source: files.map((f) => f.mimeType),
        document_filename: files.map((f) => f.fileName),
        document_size_bytes: totalFileSize,
        extraction_confidence: metadata.extraction_confidence || "medium",
        processing_notes: metadata.processing_notes ||
          (isMultipleFiles
            ? `Reprocessed ${files.length} files together using ${this.getActualModelName()}`
            : `Reprocessed single file using ${this.getActualModelName()}`),
        review_status: "pending",
        status: "active",
        data_quality_issues: metadata.data_quality_issues || null,
        missing_critical_fields: metadata.missing_critical_fields || null,
      };

      // Combine core fields with metadata fields
      const updateFields: Record<string, any> = { ...coreFields, ...metadataFields };
      
      // Build UPDATE query dynamically
      const validColumns = Object.keys(updateFields).filter(key => 
        dealsTableColumns.has(key)
      );
      
      if (validColumns.length === 0) {
        throw new Error("No valid columns found for update");
      }

      const setClause = validColumns.map((col, index) => `${col} = $${index + 2}`).join(", ");
      const values = [dealId, ...validColumns.map(col => updateFields[col])];
      
      const updateSql = `
        UPDATE deals 
        SET ${setClause}, updated_at = NOW()
        WHERE deal_id = $1
        RETURNING deal_id
      `;

      const updateResult = await this.query(updateSql, values);
      
      if (updateResult.length === 0) {
        throw new Error("Failed to update deal");
      }

      // Update investment criteria if present
      if (extractedData.investmentCriteria && extractedData.investmentCriteria.length > 0) {
        // Clear existing investment criteria for this deal
        await this.query(
          "DELETE FROM investment_criteria WHERE entity_type = 'Deal' AND entity_id = $1",
          [dealId.toString()]
        );

        // Insert new investment criteria
        const criteriaData = { investmentCriteria: extractedData.investmentCriteria };
        await this.processInvestmentCriteria(dealId, criteriaData);
      }

      this.log(
        "info",
        `Successfully updated deal ${dealId} with data from ${files.length} files`
      );

      return {
        success: true,
        dealId,
        llmResponse,
        processingDuration: Date.now() - startTime,
        extractedData: {
          coreFields,
          extraFields,
          metadata: metadataFields,
          fileCount: files.length,
          investmentCriteria: extractedData.investmentCriteria || [],
        },
      };

    } catch (err) {
      this.log("error", `Error updating deal ${dealId}: ${err}`);
      
      return {
        success: false,
        error: err instanceof Error ? err.message : String(err),
      };
    }
  }

  private async processFilesInternal(
    files: Array<{
      buffer: Buffer;
      mimeType: string;
      fileName: string;
    }>
  ): Promise<{
    success: boolean;
    dealId?: number;
    error?: string;
    extractedData?: any;
    llmResponse?: any;
    processingDuration?: number;
  }> {
    const startTime = Date.now();

    try {
      this.log("info", `Processing ${files.length} files together`);

      // Excel to CSV conversion and Word to text conversion for each file
      const uploadFiles = await Promise.all(files.map(async (file) => {
        const isExcel =
          file.mimeType === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
          file.mimeType === "application/vnd.ms-excel" ||
          file.fileName.toLowerCase().endsWith(".xlsx") ||
          file.fileName.toLowerCase().endsWith(".xls");
        const isWord =
          file.mimeType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
          file.mimeType === "application/msword" ||
          file.fileName.toLowerCase().endsWith(".docx") ||
          file.fileName.toLowerCase().endsWith(".doc");
        
        if (isExcel) {
          try {
            const workbook = XLSX.read(file.buffer, { type: "buffer" });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const csvContent = XLSX.utils.sheet_to_csv(worksheet);
            return {
              buffer: Buffer.from(csvContent, "utf-8"),
              mimeType: "text/csv",
              fileName: file.fileName.replace(/\.(xlsx|xls)$/i, ".csv"),
            };
          } catch (err) {
            this.log(
              "warn",
              `Failed to convert Excel to CSV for ${file.fileName}: ${err}. Uploading original file.`
            );
            return file;
          }
        } else if (isWord) {
          try {
            const extractor = DealProcessor.getExtractor(file.mimeType);
            if (extractor) {
              const textContent = await extractor.extractText(file.buffer);
              return {
                buffer: Buffer.from(textContent, "utf-8"),
                mimeType: "text/plain",
                fileName: file.fileName.replace(/\.(docx|doc)$/i, ".txt"),
              };
            } else {
              this.log(
                "warn",
                `No extractor available for Word document ${file.fileName}. Uploading original file.`
              );
              return file;
            }
          } catch (err) {
            this.log(
              "warn",
              `Failed to convert Word document to text for ${file.fileName}: ${err}. Uploading original file.`
            );
            return file;
          }
        }
        return file;
      }));

      // Extract text from all files (for fallback, logging, or OpenAI text fallback)
      const fileContents: Array<{
        fileName: string;
        content: string;
        type: string;
      }> = [];
      let combinedContent = "";

      for (const file of uploadFiles) {
        let documentContent = "";
        const extractor = DealProcessor.getExtractor(file.mimeType);
        if (extractor) {
          try {
            documentContent = await extractor.extractText(file.buffer);
          } catch (err) {
            this.log(
              "warn",
              `File extraction failed for ${file.fileName}: ${err}. Falling back to plain text.`
            );
            documentContent = file.buffer.toString("utf-8");
          }
        } else {
          documentContent = file.buffer.toString("utf-8");
        }

        if (documentContent.trim()) {
          fileContents.push({
            fileName: file.fileName,
            content: documentContent,
            type: file.mimeType,
          });
          combinedContent += `\n\n--- FILE: ${file.fileName} ---\n${documentContent}`;
        }
      }

      if (!combinedContent.trim()) {
        this.log("warn", `No document content available from any files`);
        return {
          success: false,
          error: "No document content available from any files",
        };
      }

      // Fetch allowed values from central_mapping
      const mappings = await this.getMappings();
      // Use the simplified extraction map for the prompt
      const systemPrompt = await generateMultipleFilesPrompt(uploadFiles, mappings);

      // Prepare files for Gemini multimodal input
      const geminiMessages: any[] = [
        {
          role: "system",
          parts: [systemPrompt],
        },
        {
          role: "user",
          parts: uploadFiles.map((file) => ({
            fileBuffer: file.buffer,
            mimeType: file.mimeType,
            fileName: file.fileName,
          })),
        },
      ];

      // Call LLM with file support (different approach for OpenAI vs Gemini)
      let llmResponse: LLMResponse;

      if (this.selectedModel.startsWith("openai")) {
        // For OpenAI, use multimodal support with base64 encoded files when possible
        const mappings = await this.getMappings();
        const systemPrompt = await generateMultipleFilesPrompt(files, mappings);

        // Check if we have any files that can be processed with vision/document API
        const visionSupportedFiles = files.filter((f) =>
          this.isOpenAIVisionSupported(f.mimeType)
        );

        let userContent;
        if (visionSupportedFiles.length > 0) {
          // Use multimodal approach with base64 encoded files
          const contentParts: any[] = [
            {
              type: "text",
              text: `Please analyze the following ${
                files.length
              } files together to extract comprehensive deal information:\n\n${files
                .map((f, i) => `File ${i + 1}: ${f.fileName} (${f.mimeType})`)
                .join(
                  "\n"
                )}\n\nFor non-supported files, here's the extracted text content:\n\n${combinedContent}`,
            },
          ];

          // Add vision-supported files as base64
          for (const file of visionSupportedFiles) {
            const base64Data = file.buffer.toString("base64");
            // PDFs use the "file" type, images use "image_url" type
            if (file.mimeType === "application/pdf") {
              contentParts.push({
                type: "file",
                file: {
                  file_data: `data:${file.mimeType};base64,${base64Data}`,
                },
              });
            } else {
              contentParts.push({
                type: "image_url",
                image_url: {
                  url: `data:${file.mimeType};base64,${base64Data}`,
                  detail: "high",
                },
              });
            }
          }

          userContent = contentParts;
        } else {
          // No vision-supported files, use text-only approach
          userContent = `Please analyze the following ${
            files.length
          } files together to extract comprehensive deal information:\n\n${files
            .map((f, i) => `File ${i + 1}: ${f.fileName} (${f.mimeType})`)
            .join("\n")}\n\nDocument contents:\n\n${combinedContent}`;
        }

        const openaiMessages = [
          {
            role: "system" as const,
            content: systemPrompt,
          },
          {
            role: "user" as const,
            content: userContent,
          },
        ] as LLMMessage[];

        console.log(
          visionSupportedFiles.length > 0
            ? `  - Calling OpenAI with ${visionSupportedFiles.length} base64 encoded files and text content`
            : "  - Calling OpenAI with text-only content (no vision-supported files)"
        );
        
        // Save prompt log for debugging
        await this.savePromptLog(
          openaiMessages,
          "openai",
          files.map(f => f.fileName),
          {
            visionSupportedFiles: visionSupportedFiles.length,
            totalFiles: files.length,
            combinedContentLength: combinedContent.length
          }
        );
        
        llmResponse = await this.llmProvider.callLLM(openaiMessages);
      } else {
        // For Gemini, use callLLMWithFiles for proper multimodal support
        console.log("  - Calling Gemini with native file support");
        
        // Save prompt log for debugging
        await this.savePromptLog(
          geminiMessages,
          "gemini",
          uploadFiles.map(f => f.fileName),
          {
            totalFiles: uploadFiles.length,
            combinedContentLength: combinedContent.length,
            fileTypes: uploadFiles.map(f => f.mimeType)
          }
        );
        
        llmResponse = await (this.llmProvider as any).callLLMWithFiles(
          geminiMessages,
          undefined,
          this.selectedModel === "gemini-pro" // use pro if selected, otherwise flash
        );
      }

      let extracted: UniversalExtractionResponse | Record<string, any> = {};
      try {
        // Clean up the response content to handle markdown-wrapped JSON
        let jsonContent = llmResponse.content.trim();

        this.log(
          "debug",
          `Raw LLM response length: ${jsonContent.length} characters`
        );

        // Handle markdown-formatted JSON responses
        if (jsonContent.includes("```json")) {
          const jsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch) {
            jsonContent = jsonMatch[1].trim();
          }
        } else if (jsonContent.includes("```")) {
          const codeMatch = jsonContent.match(/```\s*([\s\S]*?)\s*```/);
          if (codeMatch) {
            jsonContent = codeMatch[1].trim();
          }
        }

        // Clean up any remaining markdown or extra text
        const firstBrace = jsonContent.indexOf("{");
        const lastBrace = jsonContent.lastIndexOf("}");

        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
          jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
        }

        jsonContent = jsonContent
          .replace(/^\s*```\s*/, "")
          .replace(/\s*```\s*$/, "")
          .replace(/^[^{]*/, "")
          .replace(/[^}]*$/, "")
          .trim();

        this.log(
          "debug",
          `Cleaned JSON content length: ${jsonContent.length} characters`
        );
        this.log(
          "debug",
          `Attempting to parse JSON: ${jsonContent.substring(0, 200)}...`
        );

        // Try to parse the JSON
        extracted = JSON.parse(jsonContent);
      } catch (err) {
        this.log(
          "error",
          `Failed to parse LLM JSON output for multiple files: ${err}. Raw response length: ${llmResponse.content.length}`
        );
        this.log(
          "error",
          `Raw response preview: ${llmResponse.content.substring(0, 1000)}...`
        );

        // Create error file for debugging
        const fileNames = files.map(f => f.fileName).join(", ");
        const errorFilePath = await this.createErrorFile(
          `multiple_files_${fileNames.substring(0, 50)}`,
          `JSON parsing failed for multiple files: ${err}`,
          llmResponse.content,
          {
            fileCount: files.length,
            fileNames: files.map(f => f.fileName),
            mimeTypes: files.map(f => f.mimeType),
            processingMethod: "multiple_files",
            selectedModel: this.selectedModel,
            responseLength: llmResponse.content.length
          }
        );

        // Check if response is obviously not JSON (very short or no braces)
        if (llmResponse.content.length < 100 || !llmResponse.content.includes("{")) {
          this.log("error", `LLM returned non-JSON response for multiple files: "${llmResponse.content}"`);
          return {
            success: false,
            error: `LLM returned non-JSON response instead of required JSON format. Response: "${llmResponse.content.substring(0, 200)}..." Error file: ${errorFilePath}`,
          };
        }

        // Try to salvage partial JSON by attempting to repair common issues
        try {
          let repairAttempt = llmResponse.content.trim();

          // Extract just the JSON part if it exists
          const firstBrace = repairAttempt.indexOf("{");
          if (firstBrace !== -1) {
            repairAttempt = repairAttempt.substring(firstBrace);

            // Try to find the last complete object by counting braces
            let braceCount = 0;
            let lastValidIndex = -1;

            for (let i = 0; i < repairAttempt.length; i++) {
              if (repairAttempt[i] === "{") {
                braceCount++;
              } else if (repairAttempt[i] === "}") {
                braceCount--;
                if (braceCount === 0) {
                  lastValidIndex = i;
                  break;
                }
              }
            }

            if (lastValidIndex > 0) {
              repairAttempt = repairAttempt.substring(0, lastValidIndex + 1);
              this.log(
                "debug",
                `Attempting to parse repaired JSON: ${repairAttempt.substring(
                  0,
                  200
                )}...`
              );
              extracted = JSON.parse(repairAttempt);
              this.log(
                "info",
                "Successfully recovered from truncated JSON response"
              );
            } else {
              throw new Error("Could not repair truncated JSON");
            }
          } else {
            throw new Error("No JSON object found in response");
          }
        } catch (repairErr) {
          this.log(
            "error",
            `Failed to repair JSON: ${repairErr}. Error file saved: ${errorFilePath}`
          );
          return {
            success: false,
            error: `Failed to parse LLM output: ${err}. Response may be truncated or malformed. Error file: ${errorFilePath}`,
          };
        }
      }

      // Handle both universal and legacy response formats
      let extractedData: Record<string, any>;
      let customFields: Record<string, any> = {};
      let metadata: any = {};

      if (this.useUniversalPrompt && "extracted_data" in extracted) {
        // Universal format
        const universalResponse = extracted as UniversalExtractionResponse;
        extractedData = universalResponse.extracted_data;
        customFields = universalResponse.custom_fields || {};
        metadata = universalResponse.metadata || {};
      } else {
        // Legacy format
        extractedData = extracted as Record<string, any>;
      }

      // Separate core fields from extra fields
      const coreFields: Record<string, any> = {};
      const extraFields: Record<string, any> = {};

      // Save investment criteria before processing other fields
      const investmentCriteria = extractedData.investmentCriteria || [];
      this.log("debug", `Found ${investmentCriteria.length} investment criteria to save`);
      if (investmentCriteria.length > 0) {
        this.log("debug", `Sample criteria: ${JSON.stringify(investmentCriteria[0], null, 2)}`);
      }

      // Map extracted data to core fields or extra fields
      for (const [key, value] of Object.entries(extractedData)) {
        if (key === 'investmentCriteria') {
          // Don't include investment criteria in core fields - we'll process them separately
          continue;
        }
        coreFields[key] = value;
      }

      // Ensure document fields are always arrays (empty array if missing)
      const docArrayFields = [
        "document_type",
        "document_source",
        "document_filename",
        "extraction_method",
      ];
      for (const field of docArrayFields) {
        if (!(field in coreFields) || coreFields[field] == null) {
          coreFields[field] = [];
        } else if (!Array.isArray(coreFields[field])) {
          coreFields[field] = [coreFields[field]];
        }
      }

      // Add all other extracted data to extra_fields
      if (customFields) {
        Object.assign(extraFields, customFields);
      }

      // Build the insert query for the deals table
      const coreFieldNames = Object.keys(coreFields);

      console.log("Core field names from extraction:", coreFieldNames);

      // Get the actual columns that exist in the deals table
      const existingColumnsResult = await this.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'deals' 
        AND table_schema = 'public'
      `);

      const existingColumns = existingColumnsResult.map(
        (row) => row.column_name
      );
      console.log("Existing columns in deals table:", existingColumns);

      // Filter core fields to only include those that exist in the deals table
      const validCoreFields = coreFieldNames.filter((field) =>
        existingColumns.includes(field)
      );
      const invalidCoreFields = coreFieldNames.filter(
        (field) => !existingColumns.includes(field)
      );

      console.log("Valid core fields:", validCoreFields);
      console.log("Invalid core fields:", invalidCoreFields);

      if (invalidCoreFields.length > 0) {
        this.log(
          "warn",
          `Skipping fields that don't exist in deals table: ${invalidCoreFields.join(
            ", "
          )}`
        );
        // Move invalid fields to extra_fields
        for (const field of invalidCoreFields) {
          extraFields[field] = coreFields[field];
        }
      }

      const columns = [
        ...validCoreFields,
        "extra_fields",
        "document_type",
        "extraction_confidence",
        "processing_notes",
        "extraction_timestamp",
        "llm_model_used",
        "llm_provider",
        "extraction_method",
        "document_source",
        "document_filename",
        "document_size_bytes",
        "processing_duration_ms",
        "review_status",
        "status",
      ]
        .filter((column) => existingColumns.includes(column))
        .filter((column, index, array) => array.indexOf(column) === index); // Remove duplicates

      // Additional safety check using Set to ensure no duplicates
      const uniqueColumns = [...new Set(columns)];
      if (uniqueColumns.length !== columns.length) {
        console.warn("Found duplicate columns, using deduplicated version");
        console.log("Original columns:", columns);
        console.log("Deduplicated columns:", uniqueColumns);
      }

      console.log("Final columns for INSERT:", uniqueColumns);

      const values = uniqueColumns.map((_, i) => `$${i + 1}`).join(", ");
      const sql = `INSERT INTO deals (${uniqueColumns
        .map((f) => `"${f}"`)
        .join(", ")}) VALUES (${values}) RETURNING deal_id`;

      console.log("SQL Query:", sql);
      console.log("Number of columns:", uniqueColumns.length);

      // Build parameters array to match columns exactly
      const params = uniqueColumns.map((column) => {
        if (validCoreFields.includes(column)) {
          return coreFields[column];
        } else if (column === "extra_fields") {
          return JSON.stringify(extraFields);
        } else if (column === "document_type") {
          return metadata.document_type || "multiple_files";
        } else if (column === "extraction_confidence") {
          return metadata.extraction_confidence || null;
        } else if (column === "processing_notes") {
          return (
            metadata.processing_notes ||
            `Processed ${files.length} files together`
          );
        } else if (column === "extraction_timestamp") {
          return new Date();
        } else if (column === "llm_model_used") {
          return this.getActualModelName();
        } else if (column === "llm_provider") {
          return this.getProviderName();
        } else if (column === "extraction_method") {
          return this.useUniversalPrompt
            ? "simplified_multiple"
            : "legacy_multiple";
        } else if (column === "document_source") {
          return files.map((f) => f.mimeType).join(", ");
        } else if (column === "document_filename") {
          return files.map((f) => f.fileName).join(", ");
        } else if (column === "document_size_bytes") {
          return files.reduce((total, f) => total + f.buffer.length, 0);
        } else if (column === "processing_duration_ms") {
          return Date.now() - startTime;
        } else if (column === "review_status") {
          return "completed";
        } else if (column === "status") {
          return "active";
        } else {
          return null;
        }
      });

      console.log("Number of parameters:", params.length);
      console.log("Parameters:", params);

      let dealId: number | undefined = undefined;

              if (!this.skipDatabaseInsert) {
          const result = await this.query(sql, params);
          dealId = result[0]?.deal_id as number | undefined;

          // Process investment criteria after deal is inserted
          if (dealId && investmentCriteria.length > 0) {
            this.log("info", `Processing ${investmentCriteria.length} investment criteria for deal ${dealId}`);
            // Create a temporary object with the saved investment criteria data
            const criteriaData = { investmentCriteria };
            await this.processInvestmentCriteria(dealId, criteriaData);
          } else if (dealId) {
            this.log("info", `No investment criteria found for deal ${dealId}`);
          }



          this.log(
            "info",
            `Successfully processed ${files.length} files together and stored as deal ID: ${dealId}`
          );
        } else {
          this.log(
            "info",
            `Successfully processed ${files.length} files together (database insert skipped)`
          );
          
          // Still log investment criteria info even when skipping database insert
          if (extractedData.investmentCriteria) {
            this.log("info", `Found ${extractedData.investmentCriteria.length} investment criteria (not inserted due to skip flag)`);
          }
        }

      // Ensure metadata includes the model information
      metadata.llm_model_used = this.getActualModelName();
      metadata.llm_provider = this.getProviderName();
      metadata.processor_version = "1.0";

      return {
        success: true,
        dealId: dealId,
        llmResponse: llmResponse,
        processingDuration: Date.now() - startTime,
        extractedData: {
          coreFields,
          extraFields,
          metadata,
          fileCount: files.length,
          investmentCriteria: extractedData.investmentCriteria || [],
        },
      };
    } catch (err) {
      this.log("error", `Error processing multiple files: ${err}`);
      
      // Create error file for general multiple files processing errors
      const fileNames = files.map(f => f.fileName).join(", ");
      await this.createErrorFile(
        `multiple_files_error_${fileNames.substring(0, 50)}`,
        `General processing error for multiple files: ${err}`,
        "No LLM response available - error occurred before LLM call",
        {
          fileCount: files.length,
          fileNames: files.map(f => f.fileName),
          mimeTypes: files.map(f => f.mimeType),
          processingMethod: "multiple_files",
          selectedModel: this.selectedModel,
          errorType: "general_processing_error"
        }
      );
      
      return {
        success: false,
        error: err instanceof Error ? err.message : String(err),
      };
    }
  }
}
