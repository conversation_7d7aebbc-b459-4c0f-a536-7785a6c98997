/**
 * Helper functions and constants for Investment Criteria filtering
 * Organized by debt vs equity sections with role-based access control
 */

// Capital position classification
export const DEBT_CAPITAL_POSITIONS = [
  'senior debt',
  'mezzanine debt', 
  'junior debt',
  'subordinated debt',
  'acquisition loan',
  'bridge loan',
  'construction loan',
  'inventory loan',
  'construction-to-permanent loan',
  'pre-development loan',
  'refinancing',
  'takeout loan',
  'bridge-to-permanent',
  'permanent financing',
  'recapitalization',
  'note-on-note financing',
  'non-performing loan',
  'NPL'
] as const;

export const EQUITY_CAPITAL_POSITIONS = [
  'General Partner (GP)',
  'GP equity',
  'Limited Partner (LP)',
  'LP equity', 
  'preferred equity',
  'joint venture (JV)',
  'sponsor equity',
  'common equity',
  'minority stake',
  'controlling interest'
] as const;

// === FIELD ORGANIZATION BY SECTIONS ===

// Basic Investment Criteria - Always visible to both admin and non-admin users
export const BASIC_INVESTMENT_FIELDS = [
  'capitalPosition',
  'dealSize',
  'dealSizeMin',
  'dealSizeMax',
  'countries',
  'regions', 
  'states',
  'cities',
  'propertyTypes',
  'strategies',
  'decisionMakingProcess',
  'investmentCriteriaNotes'
] as const;

// Debt Investment Criteria - Visible when debt capital position is selected
export const DEBT_INVESTMENT_FIELDS = {
  // Non-admin allowed debt fields
  nonAdminFields: [
    'minLoanDscr',
    'minLoanDscrMin',
    'minLoanDscrMax',
    'maxLoanDscr',
    'maxLoanDscrMin', 
    'maxLoanDscrMax',
    'recourseLoan',
    'loanMinDebtYield',
    'loanToValueMin',
    'loanToValueMax',
    'loanToValueMinMin',
    'loanToValueMinMax',
    'loanToValueMaxMin',
    'loanToValueMaxMax',
    'loanToCostMin',
    'loanToCostMax',
    'loanToCostMinMin',
    'loanToCostMinMax',
    'loanToCostMaxMin',
    'loanToCostMaxMax',
    'minLoanTerm',
    'maxLoanTerm',
    'minLoanTermMin',
    'minLoanTermMax',
    'maxLoanTermMin',
    'maxLoanTermMax'
  ],
  // Admin-only debt fields
  adminOnlyFields: [
    'loanTypes',
    'structuredLoanTranche',
    'loanProgram',
    'eligibleBorrower',
    'lienPosition',
    'rateLock',
    'rateType',
    'amortization',
    'loanTypeNormalized',
    'futureFacilities',
    'occupancyRequirements',
    'prepayment',
    'yieldMaintenance',
    'closingTimeMin',
    'closingTimeMax',
    'loanOriginationMaxFeeMin',
    'loanOriginationMaxFeeMax',
    'loanOriginationMinFeeMin',
    'loanOriginationMinFeeMax',
    'loanExitMinFeeMin',
    'loanExitMinFeeMax',
    'loanExitMaxFeeMin',
    'loanExitMaxFeeMax',
    'loanInterestRateSofrMin',
    'loanInterestRateSofrMax',
    'loanInterestRateWsjMin',
    'loanInterestRateWsjMax',
    'loanInterestRatePrimeMin',
    'loanInterestRatePrimeMax',
    'loanInterestRate3ytMin',
    'loanInterestRate3ytMax',
    'loanInterestRate5ytMin',
    'loanInterestRate5ytMax',
    'loanInterestRate10ytMin',
    'loanInterestRate10ytMax',
    'loanInterestRate30ytMin',
    'loanInterestRate30ytMax'
  ]
} as const;

// Equity Investment Criteria - Visible when equity capital position is selected
export const EQUITY_INVESTMENT_FIELDS = {
  // Non-admin allowed equity fields
  nonAdminFields: [
    'targetReturn',
    'targetReturnMin',
    'targetReturnMax',
    'minimumIrr',
    'minimumIrrMin',
    'minimumIrrMax',
    'minimumYieldOnCost',
    'minimumYieldOnCostMin',
    'minimumYieldOnCostMax',
    'minimumEquityMultiple',
    'targetCashOnCash',
    'targetCashOnCashMin',
    'targetCashOnCashMax',
    'minHoldPeriodYears',
    'minHoldPeriodYearsMin',
    'minHoldPeriodYearsMax',
    'maxHoldPeriodYears',
    'maxHoldPeriodYearsMin',
    'maxHoldPeriodYearsMax'
  ],
  // Admin-only equity fields
  adminOnlyFields: [
    'ownershipRequirement',
    'maxLeverageTolerance',
    'maxLeverageToleranceMin',
    'maxLeverageToleranceMax'
  ]
} as const;

// === SIMPLIFIED HELPER FUNCTIONS ===

/**
 * Check if a capital position value indicates debt financing
 */
export function isDebtCapitalPosition(capitalPositions: string[]): boolean {
  if (!capitalPositions || capitalPositions.length === 0) return false;
  
  return capitalPositions.some(position => 
    DEBT_CAPITAL_POSITIONS.some(debtTerm => 
      position.toLowerCase().includes(debtTerm.toLowerCase())
    )
  );
}

/**
 * Check if a capital position value indicates equity financing
 */
export function isEquityCapitalPosition(capitalPositions: string[]): boolean {
  if (!capitalPositions || capitalPositions.length === 0) return false;
  
  return capitalPositions.some(position => 
    EQUITY_CAPITAL_POSITIONS.some(equityTerm => 
      position.toLowerCase().includes(equityTerm.toLowerCase())
    )
  );
}

/**
 * Check if user has admin role based on session data
 */
export function isAdminUser(session: any): boolean {
  return session?.user?.role === 'admin' || session?.user?.is_admin === true;
}

/**
 * Get field visibility settings based on capital positions and user role
 */
export function getInvestmentFieldVisibility(
  capitalPositions: string[], 
  session: any
): {
  showDebtSection: boolean;
  showEquitySection: boolean;
  isAdmin: boolean;
  hasCapitalPosition: boolean;
} {
  const isAdmin = isAdminUser(session);
  const hasDebt = isDebtCapitalPosition(capitalPositions);
  const hasEquity = isEquityCapitalPosition(capitalPositions);
  const hasCapitalPosition = hasDebt || hasEquity;
  
  return {
    showDebtSection: hasDebt,
    showEquitySection: hasEquity,
    isAdmin,
    hasCapitalPosition
  };
}

/**
 * Check if a field should be visible based on section, role, and capital position
 */
export function isFieldVisible(
  fieldName: string,
  capitalPositions: string[],
  session: any
): boolean {
  const { isAdmin, showDebtSection, showEquitySection } = getInvestmentFieldVisibility(capitalPositions, session);
  
  // Basic fields are always visible
  if (BASIC_INVESTMENT_FIELDS.includes(fieldName as any)) {
    return true;
  }
  
  // Debt fields - visible when debt capital position is selected
  if (DEBT_INVESTMENT_FIELDS.nonAdminFields.includes(fieldName as any) || 
      DEBT_INVESTMENT_FIELDS.adminOnlyFields.includes(fieldName as any)) {
    if (!showDebtSection) return false;
    
    // Admin can see all debt fields, non-admin only allowed ones
    return isAdmin || DEBT_INVESTMENT_FIELDS.nonAdminFields.includes(fieldName as any);
  }
  
  // Equity fields - visible when equity capital position is selected
  if (EQUITY_INVESTMENT_FIELDS.nonAdminFields.includes(fieldName as any) || 
      EQUITY_INVESTMENT_FIELDS.adminOnlyFields.includes(fieldName as any)) {
    if (!showEquitySection) return false;
    
    // Admin can see all equity fields, non-admin only allowed ones
    return isAdmin || EQUITY_INVESTMENT_FIELDS.nonAdminFields.includes(fieldName as any);
  }
  
  // Unknown field - only visible to admin
  return isAdmin;
}

/**
 * Get all fields that should be visible for debt section
 */
export function getVisibleDebtFields(isAdmin: boolean): string[] {
  if (isAdmin) {
    return [...DEBT_INVESTMENT_FIELDS.nonAdminFields, ...DEBT_INVESTMENT_FIELDS.adminOnlyFields];
  }
  return [...DEBT_INVESTMENT_FIELDS.nonAdminFields];
}

/**
 * Get all fields that should be visible for equity section
 */
export function getVisibleEquityFields(isAdmin: boolean): string[] {
  if (isAdmin) {
    return [...EQUITY_INVESTMENT_FIELDS.nonAdminFields, ...EQUITY_INVESTMENT_FIELDS.adminOnlyFields];
  }
  return [...EQUITY_INVESTMENT_FIELDS.nonAdminFields];
}

// Type exports
export type DebtCapitalPosition = typeof DEBT_CAPITAL_POSITIONS[number];
export type EquityCapitalPosition = typeof EQUITY_CAPITAL_POSITIONS[number];
export type BasicInvestmentField = typeof BASIC_INVESTMENT_FIELDS[number];
export type DebtInvestmentField = typeof DEBT_INVESTMENT_FIELDS.nonAdminFields[number] | typeof DEBT_INVESTMENT_FIELDS.adminOnlyFields[number];
export type EquityInvestmentField = typeof EQUITY_INVESTMENT_FIELDS.nonAdminFields[number] | typeof EQUITY_INVESTMENT_FIELDS.adminOnlyFields[number];
