import { IStorageProvider, StorageResult, UrlOptions, PresignedUrlOptions } from './IStorageProvider';
import fs from 'fs';
import path from 'path';

export class LocalFileStorage implements IStorageProvider {
  name = 'local';
  private baseDir: string;
  private isServerless: boolean;

  constructor(baseDir: string = 'file_storage') {
    this.baseDir = path.resolve(process.cwd(), baseDir);
    this.isServerless = process.env.VERCEL === '1';
    
    // Always try to create directories - let the actual operations fail if disk access is restricted
    this.ensureDirectoryExists();
  }

  private ensureDirectoryExists(): void {
    try {
      if (!fs.existsSync(this.baseDir)) {
        fs.mkdirSync(this.baseDir, { recursive: true });
      }
    } catch (error) {
      console.warn('Could not create local storage directory:', error.message);
    }
  }

  private getStoragePath(filePath: string): string {
    // If the path is already absolute, use it as-is
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
    // Otherwise, resolve it relative to the base directory
    return path.join(this.baseDir, filePath);
  }

  async uploadFile(buffer: Buffer, filePath: string, metadata?: any): Promise<StorageResult> {
    // In serverless environments, local storage is not available
    if (this.isServerless) {
      return {
        success: false,
        path: filePath,
        metadata: {},
        error: 'Local storage not available in serverless environment'
      };
    }

    try {
      const fullPath = this.getStoragePath(filePath);
      const dir = path.dirname(fullPath);
      
      // Ensure directory exists
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Write file
      fs.writeFileSync(fullPath, buffer);

      return {
        success: true,
        path: filePath,
        metadata: {
          file_system_path: fullPath,
          directory: dir,
          size: buffer.length,
          uploaded_at: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        path: filePath,
        metadata: {},
        error: error.message
      };
    }
  }

  async downloadFile(filePath: string): Promise<Buffer | null> {
    try {
      const fullPath = this.getStoragePath(filePath);
      
      if (fs.existsSync(fullPath)) {
        return fs.readFileSync(fullPath);
      }
      
      return null;
    } catch (error) {
      console.error('Local file download error:', error);
      return null;
    }
  }

  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const fullPath = this.getStoragePath(filePath);
      
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Local file delete error:', error);
      return false;
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      const fullPath = this.getStoragePath(filePath);
      return fs.existsSync(fullPath);
    } catch (error) {
      return false;
    }
  }

  async getFileUrl(filePath: string, options?: UrlOptions): Promise<string> {
    // For local storage, return the file system path
    return this.getStoragePath(filePath);
  }

  async getPresignedUrl?(filePath: string, options?: PresignedUrlOptions): Promise<string> {
    // Local storage doesn't need presigned URLs
    return this.getFileUrl(filePath, options);
  }

  /**
   * Check if this storage provider is actually functional
   */
  async isAvailable(): Promise<boolean> {
    try {
      // Test if we can create a test file
      const testPath = 'availability_test_' + Date.now();
      const testContent = Buffer.from('test');
      
      const uploadResult = await this.uploadFile(testContent, testPath, {});
      if (!uploadResult.success) {
        return false;
      }
      
      // Test if we can read it back
      const downloadResult = await this.downloadFile(testPath);
      const isReadable = downloadResult !== null && downloadResult.equals(testContent);
      
      // Clean up
      await this.deleteFile(testPath);
      
      return isReadable;
    } catch (error) {
      return false;
    }
  }
}
