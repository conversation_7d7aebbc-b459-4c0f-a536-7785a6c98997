import { IStorageProvider } from './IStorageProvider';
import { LocalFileStorage } from './LocalFileStorage';
import { AzureBlobStorage } from './AzureBlobStorage';

export class StorageProviderRegistry {
  private static instance: StorageProviderRegistry;
  private providers = new Map<string, IStorageProvider>();

  private constructor() {
    // Initialize providers asynchronously
    this.initializeDefaultProviders().catch(error => {
      console.error('Error initializing storage providers:', error);
    });
  }

  public static getInstance(): StorageProviderRegistry {
    if (!StorageProviderRegistry.instance) {
      StorageProviderRegistry.instance = new StorageProviderRegistry();
    }
    return StorageProviderRegistry.instance;
  }

  private async initializeDefaultProviders() {
    // Check if local file storage is available by testing disk access
    const localStorage = new LocalFileStorage();
    const isLocalStorageAvailable = await this.checkLocalStorageAvailability(localStorage);
    
    if (isLocalStorageAvailable) {
      this.register(localStorage);
      console.log('Local file storage registered and available');
    } else {
      console.log('Local file storage not available - disk access restricted');
    }
    
    // Register Azure Blob Storage if configured
    const azureConnectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
    const azureContainer = process.env.AZURE_STORAGE_CONTAINER || 'files';
    
    if (azureConnectionString) {
      this.register(new AzureBlobStorage(azureConnectionString, azureContainer));
    } else {
      console.warn('Azure storage not configured. Set AZURE_STORAGE_CONNECTION_STRING environment variable.');
    }
  }

  public register(provider: IStorageProvider): void {
    this.providers.set(provider.name, provider);
    console.log(`Registered storage provider: ${provider.name}`);
  }

  public getProvider(name: string): IStorageProvider | null {
    return this.providers.get(name) || null;
  }

  public getAllProviders(): IStorageProvider[] {
    return Array.from(this.providers.values());
  }

  public getDefaultProvider(): IStorageProvider | null {
    // Return Azure if available, otherwise local
    return this.getProvider('azure') || this.getProvider('local');
  }

  public getProviderNames(): string[] {
    return Array.from(this.providers.keys());
  }

  public isProviderAvailable(name: string): boolean {
    return this.providers.has(name);
  }

  /**
   * Get detailed status of all storage providers
   */
  public async getStorageStatus(): Promise<{ name: string; available: boolean; error?: string }[]> {
    const statuses: { name: string; available: boolean; error?: string }[] = [];
    
    for (const [name, provider] of this.providers) {
      try {
        if (name === 'local' && 'isAvailable' in provider) {
          const available = await (provider as any).isAvailable();
          statuses.push({ name, available });
        } else {
          // For other providers, assume available if registered
          statuses.push({ name, available: true });
        }
      } catch (error) {
        statuses.push({ name, available: false, error: (error as Error).message });
      }
    }
    
    return statuses;
  }

  /**
   * Check if local file storage is actually available by testing disk access
   */
  private async checkLocalStorageAvailability(localStorage: LocalFileStorage): Promise<boolean> {
    try {
      return await localStorage.isAvailable();
    } catch (error) {
      console.log('Local storage availability check failed:', error.message);
      return false;
    }
  }
}
