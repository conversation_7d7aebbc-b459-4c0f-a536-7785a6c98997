export interface IStorageProvider {
  name: string;
  
  // Core operations
  uploadFile(buffer: Buffer, path: string, metadata?: any): Promise<StorageResult>;
  downloadFile(path: string): Promise<Buffer | null>;
  deleteFile(path: string): Promise<boolean>;
  fileExists(path: string): Promise<boolean>;
  
  // URL generation
  getFileUrl(path: string, options?: UrlOptions): Promise<string>;
  getPresignedUrl?(path: string, options?: PresignedUrlOptions): Promise<string>;
  
  // Provider-specific operations
  getProviderMetadata?(path: string): Promise<any>;
  migrateFrom?(otherProvider: IStorageProvider, path: string): Promise<StorageResult>;
}

export interface StorageResult {
  success: boolean;
  path: string;
  metadata: any;
  error?: string;
}

export interface UrlOptions {
  expiresIn?: number; // seconds
  isPublic?: boolean;
  download?: boolean;
}

export interface PresignedUrlOptions extends UrlOptions {
  method?: 'GET' | 'PUT' | 'DELETE';
}
