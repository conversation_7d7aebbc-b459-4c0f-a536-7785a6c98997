import { IStorageProvider, StorageResult, UrlOptions, PresignedUrlOptions } from './IStorageProvider';
import { BlobServiceClient, StorageSharedKeyCredential, generateBlobSASQueryParameters, BlobSASPermissions } from '@azure/storage-blob';

export class AzureBlobStorage implements IStorageProvider {
  name = 'azure';
  private client: BlobServiceClient;
  private containerName: string;
  private accountName: string;

  constructor(connectionString: string, containerName: string = 'files') {
    this.client = BlobServiceClient.fromConnectionString(connectionString);
    this.containerName = containerName;
    
    // Extract account name from connection string
    // Try multiple patterns to handle different connection string formats
    let match = connectionString.match(/AccountName=([^;]+)/);
    if (!match) {
      match = connectionString.match(/DefaultEndpointsProtocol=https;AccountName=([^;]+)/);
    }
    if (!match) {
      // Try to extract from the URL format
      match = connectionString.match(/https:\/\/([^.]+)\.blob\.core\.windows\.net/);
    }
    
    this.accountName = match ? match[1] : 'unknown';
    
    // Try to get account name from the client URL as fallback
    if (this.accountName === 'unknown') {
      try {
        const url = this.client.url;
        const urlMatch = url.match(/https:\/\/([^.]+)\.blob\.core\.windows\.net/);
        if (urlMatch) {
          this.accountName = urlMatch[1];
        }
      } catch (error) {
        console.warn('Could not extract account name from client URL:', error);
      }
    }
    
    // Log for debugging
    console.log(`Azure Blob Storage initialized with account name: ${this.accountName}`);
    console.log(`Connection string format: ${connectionString.substring(0, 50)}...`);
  }

  async uploadFile(buffer: Buffer, filePath: string, metadata?: any): Promise<StorageResult> {
    try {
      const containerClient = this.client.getContainerClient(this.containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(filePath);
      
      // Ensure container exists
      await containerClient.createIfNotExists();
      
      // Convert metadata values to strings (Azure requirement)
      const stringMetadata: Record<string, string> = {
        originalName: metadata?.originalName || '',
        uploadedAt: new Date().toISOString()
      };
      
      // Convert all metadata values to strings
      if (metadata) {
        Object.entries(metadata).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            stringMetadata[key] = typeof value === 'string' ? value : JSON.stringify(value);
          }
        });
      }

      await blockBlobClient.upload(buffer, buffer.length, {
        blobHTTPHeaders: {
          blobContentType: metadata?.mimeType || 'application/octet-stream'
        },
        metadata: stringMetadata
      });

      return {
        success: true,
        path: filePath,
        metadata: {
          container: this.containerName,
          blob_name: filePath,
          url: blockBlobClient.url,
          account_name: this.accountName,
          etag: (await blockBlobClient.getProperties()).etag,
          size: buffer.length
        }
      };
    } catch (error) {
      return {
        success: false,
        path: filePath,
        metadata: {},
        error: error.message
      };
    }
  }

  async downloadFile(filePath: string): Promise<Buffer | null> {
    try {
      const containerClient = this.client.getContainerClient(this.containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(filePath);
      
      const downloadResponse = await blockBlobClient.download();
      const chunks: Buffer[] = [];
      
      for await (const chunk of downloadResponse.readableStreamBody!) {
        chunks.push(Buffer.from(chunk));
      }
      
      return Buffer.concat(chunks);
    } catch (error) {
      console.error('Azure download error:', error);
      return null;
    }
  }

  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const containerClient = this.client.getContainerClient(this.containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(filePath);
      await blockBlobClient.delete();
      return true;
    } catch (error) {
      console.error('Azure delete error:', error);
      return false;
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      const containerClient = this.client.getContainerClient(this.containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(filePath);
      return await blockBlobClient.exists();
    } catch (error) {
      return false;
    }
  }

  async getFileUrl(filePath: string, options?: UrlOptions): Promise<string> {
    const containerClient = this.client.getContainerClient(this.containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(filePath);
    
    // Debug logging
    console.log(`Generating URL for file: ${filePath}`);
    console.log(`Container: ${this.containerName}`);
    console.log(`Account name: ${this.accountName}`);
    console.log(`Generated URL: ${blockBlobClient.url}`);
    
    return blockBlobClient.url;
  }

  async getPresignedUrl(filePath: string, options?: PresignedUrlOptions): Promise<string> {
    try {
      const containerClient = this.client.getContainerClient(this.containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(filePath);
      
      const expiresOn = new Date();
      expiresOn.setMinutes(expiresOn.getMinutes() + (options?.expiresIn || 60));
      
      const permissions = new BlobSASPermissions();
      if (options?.method === 'GET' || !options?.method) {
        permissions.read = true;
      }
      if (options?.method === 'PUT') {
        permissions.write = true;
      }
      if (options?.method === 'DELETE') {
        permissions.delete = true;
      }
      
      const sasToken = generateBlobSASQueryParameters({
        containerName: this.containerName,
        blobName: filePath,
        permissions: permissions,
        expiresOn: expiresOn
      }, this.client.credential as StorageSharedKeyCredential);
      
      return `${blockBlobClient.url}?${sasToken}`;
    } catch (error) {
      console.error('Azure presigned URL generation error:', error);
      return this.getFileUrl(filePath, options);
    }
  }

  async getProviderMetadata(filePath: string): Promise<any> {
    try {
      const containerClient = this.client.getContainerClient(this.containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(filePath);
      const properties = await blockBlobClient.getProperties();
      
      return {
        size: properties.contentLength,
        lastModified: properties.lastModified,
        etag: properties.etag,
        contentType: properties.contentType,
        metadata: properties.metadata
      };
    } catch (error) {
      console.error('Azure metadata fetch error:', error);
      return null;
    }
  }

  /**
   * Get storage information for display purposes
   */
  getStorageInfo(): { accountName: string; containerName: string; url: string } {
    // Get the actual URL from Azure client instead of constructing it manually
    const containerClient = this.client.getContainerClient(this.containerName);
    const actualUrl = containerClient.url;
    
    return {
      accountName: this.accountName,
      containerName: this.containerName,
      url: actualUrl
    };
  }
}
