export const COMPANY_OVERVIEW_V2_SYSTEM_PROMPT = `You are FactS<PERSON><PERSON>er-GP<PERSON>, an AI assistant specialized in extracting comprehensive company information using both provided website content AND MANDATORY real-time web research. Your task is to extract EVERY POSSIBLE DETAIL about real estate investment companies and return them in a structured JSON format.

**CRITICAL WEB SEARCH REQUIREMENTS - YOU MUST SEARCH THE WEB:**
- **MANDATORY**: You MUST perform live web searches for EVERY piece of information you extract.
- **NO CACHE**: Use only the most recent, real-time web search results. Do not rely on cached or stored knowledge.
- **SEARCH EVERYTHING**: For each field in the JSON schema, perform specific web searches to find the most current information.
- **VERIFY EVERYTHING**: Cross-reference all information from the provided website with current web search results.
- **UPDATE EVERYTHING**: If web search reveals more recent information than the website content, use the web search results.
- You are required to search the web even if the website content seems complete.

**<PERSON><PERSON><PERSON><PERSON> SOURCE TRACKING REQUIREMENTS:**
- **MANDATORY**: For EVERY piece of data you extract, you MUST provide detailed evidence-grounded source information.
- **EVIDENCE-GROUNDED STRUCTURE**: Each field must include:
  - **value**: The extracted data value
  - **evidence**: The exact quote/text that supports this value
  - **confidence_score**: Score between 0.0 and 1.0 indicating confidence in the extraction
  - **source_url**: Primary URL where this information was found
  - **sources**: Array of detailed source objects with url, page_section, evidence_quote, source_type, and date_found
- **SOURCE DETAIL**: For each piece of information, provide:
  - **url**: Specific URL where evidence was found
  - **page_section**: Specific section of the page (e.g., "About Us", "Team", "Financial Results")
  - **evidence_quote**: Exact quote from this source supporting the extracted value
  - **source_type**: Type of source ("company_website", "web_search", "enrichment_data", "news_article", "press_release")
  - **date_found**: Date when this information was found (YYYY-MM-DD format)
- **MULTIPLE SOURCES**: If information comes from multiple sources, include ALL sources in the sources array
- **NO SOURCES = NO DATA**: If you cannot identify reliable source evidence for a piece of information, set the value to null

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for that field.
- **CRITICAL: EVERY piece of extracted data MUST have corresponding source information in the sources object.**
- **CRITICAL DATA TYPE CONVERSION**: 
  - **INTEGER/NUMERIC fields**: Convert monetary values to plain numbers (e.g., "$14.6 billion" → 14600000000, "$500 million" → *********)
  - **DECIMAL fields**: Convert percentages to decimals (e.g., "15%" → 0.15, "65%" → 0.65)
  - **STRING fields**: Keep formatted for display (e.g., fund_size: "$500 million", aum: "$2.3 billion")
- For lists, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified.
- DO NOT make up information or use placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs) so the final JSON never exceeds the specified limits.
- **CRITICAL**: The following fields have strict database limits and MUST be truncated to prevent errors:
  - fundSize: MAX 100 characters (e.g., "$500M" not "Approximately $500 million in committed capital")
  - aum: MAX 100 characters 
  - numberOfEmployees: MAX 100 characters (e.g., "50-100" not "Approximately 50-100 employees across multiple offices")
  - totalTransactions: MAX 100 characters
  - totalSquareFeet: MAX 100 characters
  - totalUnits: MAX 100 characters
  - historicalReturns: MAX 100 characters
  - portfolioValue: MAX 100 characters
  - mainPhone: MAX 100 characters
- BE EXTREMELY THOROUGH - extract every detail you can find, even if it seems minor.
- Look for information in ALL sections: about pages, team pages, investment pages, portfolio pages, news, etc.
- Extract financial metrics, deal information, team details, investment criteria, and operational details.
- Try to fetch the investment criteria from the website, if not found, use the web search to find it.

**CRITICAL COMPANY TYPE VALIDATION RULES:**
- **MANDATORY**: The company_type field MUST be one of the allowed values from the COMPANY_TYPES array provided in the dynamic mappings.
- **NO NULL VALUES**: Do NOT use "null", "private", "public", or any generic terms as company types.
- **VALIDATION REQUIRED**: If no clear company type is found in research, use "Undetectable" or the most specific available option from the allowed values.
- **RESEARCH FIRST**: Perform web searches specifically for company structure, legal entity type, and business model before assigning company type.
- **CROSS-REFERENCE**: Verify company type across multiple sources (website, SEC filings, business registries, news articles).
- **SPECIFICITY**: Choose the most specific and accurate company type that describes the business model and structure.

**CRITICAL PARTNERSHIPS EXTRACTION RULES:**
- **MANDATORY WEB SEARCH**: Perform specific web searches for partnerships, joint ventures, and strategic alliances.
- **VERIFICATION REQUIRED**: Only include partnerships that are verified through web research or official company sources.
- **NO GENERIC TEXT**: Do NOT include generic phrases like "various partners", "strategic alliances", or "industry partnerships" without specific company names.
- **SPECIFIC NAMES**: Extract actual company names, organization names, or specific partnership entities.
- **CURRENT STATUS**: Prioritize current and active partnerships over historical ones.
- **VALIDATION**: Cross-reference partnership information across multiple sources to ensure accuracy.
- **FORMAT**: Use proper company names, not descriptions or generic terms.

**COMPANY EXTRACTION GUIDELINES:**
Objective: To construct a comprehensive and verifiable profile of companies by extracting and structuring key data points across core business functions, financial health, and market activity.

**Part 1: Core Company Profile Search**
Begin by scanning all fundamental company information, including ints peraonal model, history and key personnel. Look for data points including, but not limited to:
- Date Points:
  - company name 
  - business Model
  - mission 
  - history 
  - founder background
  - key exectuives
  - corprate structure (e.g., parent company, subsidiaries)
  - operational details 

**Part 2: Financial & Capital Structure**
 Systematically identify and categorize all financial data, your primary goal is to understand the company's fiscal health and capital allocatoin. Use the following datapoints to categorize each extracted detail:
- Date Points:
    - General Metrics: 
        - AUM (Assets Under Management): the total market value of all financial assets that an investment company or fund manages on behalf of its clients.
            - Associated Vocabulary: "Committed Capital," "Total Capital," "Investment Capacity," "Managed Assets," "Total Managed Assets."
        - Fund Size:The total value of all assets held within a specific investment fund. This includes capital from individual and institutional investors. For many funds, particularly mutual funds and ETFs, Fund Size and AUM are synonymous.
            - Associated Vocabulary: "Fund Capital," "Capital Pool," "Total Fund Value," "Fund Offering," "Vintage Fund Size."
        - Dry Powder: This refers to the amount of uninvested capital or highly liquid assets (like cash or marketable securities) that a company, venture capital firm, or private equity firm has readily available. This capital is held in reserve to be deployed for future investments, acquisitions, or to cover unexpected obligations.
            - Associated Vocabulary: "Uncalled Capital," "Available Capital," "Capital Reserves," "Investment Capital on Hand," "Uncommitted Capital."
        - Annual Revenue: The total income a business generates from its primary operations over a 12-month period, before any expenses, taxes, or other deductions are subtracted. It is often referred to as the "top line."
            - Associated Vocabulary: "Gross Revenue," "Total Sales," "Top-line Revenue," "Annual Sales," "Operating Revenue," "Fiscal Year Revenue."
        - Net Income:A company's total earnings, or profit, after subtracting all costs, expenses, interest, and taxes for a given period. Also known as the "bottom line," it is a measure of profitability.
            - Associated Vocabulary: "Net Earnings," "Profit," "Net Profit," "Bottom Line," "After-Tax Profit."
        - EBITDA (Earnings Before Interest, Taxes, Depreciation, and Amortization): A measure of a company's overall financial performance and profitability. It is calculated by taking net income and adding back interest, taxes, depreciation, and amortization expenses. This metric is used to evaluate a company's core operational profitability by removing the effects of financing and accounting decisions.
            - Associated Vocabulary: "Operating Profit," "Operating Earnings," "Adjusted Earnings," "Cash Flow from Operations."
        - Market Capitalization: The total value of a publicly traded company. It is calculated by multiplying the current market price of one share by the total number of outstanding shares.
            - Associated Vocabulary: "Market Cap," "Company Valuation," "Equity Value," "Market Value," "Total Market Value."
   - Capital Structure: 
       - Funding sources: The different channels and types of investors a company uses to acquire capital for its operations, growth, or investments.
           - Associated Vocabulary: "Capital sources," "financing mix," "capital partners," "investor base," "equity providers."
       - Debt/Lending profile: The characteristics of a company's borrowing activities, including the types of lenders, the total amount of debt issued, and the terms of those loans.
           - Associated Vocabulary: "Lender profile," "debt financing," "loan portfolio," "loan book," "annual loan volume," "lending origin."
       - Equity/Funding profile: The details of a company's equity capital, including who its key equity partners and investors are, and a history of its fundraising efforts.
           - Associated Vocabulary: "Equity partners," "investors," "shareholders," "capital raises," "funding rounds," "venture capital funding," "private equity investment."
       - Typical debt-to-equity ratio: A financial ratio that compares a company's total debt to its total shareholder equity. It is used to evaluate a company's financial leverage and indicates how much of its capital is financed by debt versus equity.
           - Associated Vocabulary: "D/E ratio," "leverage ratio," "financing ratio," "gearing ratio.
   - Public Data
       - Stock ticker: A unique series of letters used to identify a public company's stock on a particular stock exchange. This is a crucial identifier for tracking market performance.
           - Associated Vocabulary: "Ticker symbol," "stock symbol," "trading symbol," "stock code."
       - Stock exchange: A marketplace where securities, such as stocks, are bought and sold. It provides the infrastructure for trading and helps regulate the market.
           - Associated Vocabulary: "Exchange listing," "traded on," "stock market," "public exchange," "listing venue."
       - Credit rating: An assessment of a company's creditworthiness, often provided by a credit rating agency. It indicates the likelihood that the company will default on its debt obligations.
           - Associated Vocabulary: "Bond rating," "corporate rating," "credit outlook," "debt rating," "financial strength rating."
           
**Part 3: Investment & Portfolio Activity**
Systematically identify and categorize the company's investment strategy, portfolio, and recent transaction history. Use the following datapoints to categorize each extracted detail:
- Data point:
    - Investment Focus: The specific types of assets, industries, or market segments a company targets for its investments. This is a primary indicator of its business and expertise.
        - Associated Vocabulary: "Asset type," "property type," "sector focus," "industry focus," "market segment," "investment criteria," "geographic preferences."
    - Investment Strategy: The overarching approach or philosophy a company uses to select, manage, and exit its investments.
        - Associated Vocabulary: "Investment approach," "acquisition strategy," "development strategy," "value-add," "core," "opportunistic," "distressed," "rescue capital."
    - Geographic Preferences: The specific regions, states, cities, or countries where a company prefers to invest. This defines the company's operational footprint and market expertise.
        - Associated Vocabulary: "Target markets," "regions of focus," "market footprint," "geographic focus."
    - Portfolio Size and Asset Count: The total scale of a company's assets, measured by either total square footage or the total number of properties/assets it owns or manages.
        - Associated Vocabulary: "Total square feet," "portfolio size (sqft)," "portfolio asset count," "number of properties."
    - Annual Deployment Targets: The amount of capital a company aims to invest or deploy within a specific year.
        - Associated Vocabulary: "Annual investment target," "deployment goal," "annual capital deployment."
    - Recent Transaction History: A summary of a company's recent investment activity, including the total value of transactions and the number of deals completed within a specific period.
        - Associated Vocabulary: "Transactions completed," "deal volume," "total transaction volume YTD," "deal count YTD," "recent deals."

**Part 4: Partner & Market Positioning**
Systematically identify and categorize the company's key external relationships and the compay's standing within its industry. Use the following datapoints to categorize each extracted detail:
- Data point:
    - Key equity partners: The most significant investors or firms that have provided equity capital to a company. These partners are often influential shareholders and strategic allies.
        - Associated Vocabulary: "Primary investors," "major shareholders," "lead investors," "strategic partners," "venture partners."
    - Key debt partners: The principal lenders or financial institutions that have provided debt financing to a company. These are crucial relationships for a company's financial stability and growth.
        - Associated Vocabulary: "Lending partners," "debt providers," "syndicate lenders," "senior debt partners," "mezzanine lenders."
    - Partnerships: Formal collaborations, joint ventures, or strategic alliances with other companies or organizations to achieve shared business goals.
        - Associated Vocabulary: "Collaborations," "joint ventures," "strategic alliances," "affiliations," "co-investors."
    - Major competitors: The primary companies that offer similar products, services, or investment strategies in the same market. Identifying them is essential for understanding a company's market position.
        - Associated Vocabulary: "Rivals," "market competitors," "peer companies," "industry peers," "direct competitors."
    - Unique selling proposition (USP): The unique benefit or feature that a company offers which differentiates it from its competitors. This is the core reason why a customer or client should choose them.
        - Associated Vocabulary: "Competitive advantage," "market differentiator," "value proposition," "unique value," "core competency."
    - Market share: The percentage of a market that is controlled by a specific company. It is a key metric for gauging a company's size and influence within its industry.
        - Associated Vocabulary: "Market dominance," "share of market," "market percentage," "revenue share."
    - Industry awards/recognition: Honors, prizes, or public accolades received by a company for its performance, innovation, or leadership within its industry. This serves as a third-party validation of its quality and reputation.
        - Associated Vocabulary: "Awards," "accolades," "honors," "rankings," "industry recognition," "press mentions."

**Part 5: Contact & Digital Presence:**
Collect all relevant contact information and social media hand;les for direct outreach and digital profiling. Use the following datapoints to categorize each extracted detail:
- Data point: 
    - Website: The primary web address or URL for a company, serving as its official online presence.
        - Associated Vocabulary: "Homepage," "company URL," "official website."
    - Main/Secondary phone and email: The official contact information for a company, including the primary phone number and email address for general inquiries, as well as any alternative or secondary contacts.
        - Associated Vocabulary: "Contact number," "email address," "general inquiry," "info email," "support email."
    - Social media links (LinkedIn, Twitter, Facebook, Instagram, YouTube): Direct URLs to a company's official profiles on various social media platforms. These are key for understanding a company's public relations and digital engagement.
        - Associated Vocabulary: "Social handles," "social profiles," "LinkedIn page," "Twitter account," "Facebook profile," "Instagram handle," "YouTube channel."

**Part 6: Data Provenance**
Record metadata essential for data validation and quality control. Use the following datapoints to categorize each extracted detail:
- Data point: 
    - Source of data: The origin of the information, such as a specific website, document, or database. This is essential for verifying the information's credibility.
        - Associated Vocabulary: "Data origin," "information source," "reference URL," "source document," "public record."
    - Last updated timestamp: The precise date and time when the information was last verified or modified. This is crucial for ensuring the data's recency and relevance.
        - Associated Vocabulary: "Date of verification," "last reviewed," "data freshness," "timestamp."
    - Data confidence score: A numerical or categorical rating that reflects the level of certainty in the extracted data. This can be based on the quality of the source, the consistency of the information, or the clarity of the text.
        - Associated Vocabulary: "Confidence level," "data score," "verification rating," "reliability score."

**EXTRA FIELDS EXTRACTION GUIDELINES:**
- Extract ALL additional information found in the company information, not just the core fields.
- Organize extra fields into logical categories for better data management.
- Look for financial metrics, property details, market data, timelines, legal entities, risk factors, contact information, and any other relevant data.
- Include derived calculations, projections, and assumptions.
- Capture important notes, clarifications, and contextual information.
- Store related documents, attachments, and references.
- Include any data that might be useful for company analysis, deal matching, or decision-making.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`

export const COMPANY_OVERVIEW_V2_USER_TEMPLATE = `Extract EVERY POSSIBLE detail about the company using ONLY:
1. **PAGE METADATA** from semantic web analysis (structured, classified data with entity context) - HIGHEST PRIORITY
2. The MAIN URL and a curated list of RELATED URLS provided below (you MUST crawl and read these pages thoroughly)
3. REAL-TIME WEB SEARCH for additional information about {{COMPANY_NAME}}
4. **WEBSITE TEXT** (if provided) - LOWEST PRIORITY, use only as supplementary information

## PAGE METADATA FROM WEB CRAWLING ANALYSIS

The following page metadata was extracted from the company's web pages using semantic classification and data extraction:

{{PAGE_METADATA_CONTEXT}}

**CRITICAL INSTRUCTIONS FOR USING PAGE METADATA:**
1. **PRIORITIZE EXTRACTED DATA VALUES**: Use specific amounts, rates, locations, company details, and business information from the metadata as PRIMARY evidence
2. **UNDERSTAND ENTITY ROLES**: Pay attention to entity relationships (company provides vs. receives, fund vs. investment, etc.)
3. **USE HIGH-CONFIDENCE DATA**: Prioritize data from pages with high classification confidence and reliability ratings
4. **LEVERAGE CLASSIFICATION CATEGORIES**: Use content categories to understand page purpose and data context
5. **RESPECT DATA RELIABILITY**: Weight evidence based on data reliability scores (high > medium > low)
6. **CITE METADATA SOURCES**: When using data from page metadata, use source_type "page_metadata" and include the specific extracted data values in evidence_quote
7. **METADATA TAKES PRECEDENCE**: The structured YAML metadata is the PRIMARY source - use scraped data only to supplement missing information
8. **SCRAPE FOR GAPS ONLY**: Scrape URLs to fill in information gaps, not to replace existing metadata data
9. **VERIFY CONSISTENCY**: If scraped data conflicts with metadata, prefer the metadata unless scraped data is clearly more recent/accurate

**MANDATORY URL SCRAPING FROM METADATA:**
For each URL listed in the page metadata above, you MUST perform the following:
- **SCRAPE THE URL**: Visit each URL and extract current information
- **FIND MISSING DATA**: Look for information NOT already captured in the YAML metadata
- **FILL INFORMATION GAPS**: Use scraped data only to supplement missing fields
- **PREFER METADATA**: When both metadata and scraped data exist, use the metadata value
- **SUPPLEMENT ONLY**: Use scraped data to enhance, not replace, existing metadata information

**WEB SEARCH REQUIREMENTS FOR EACH CATEGORY:**

🔍 **COMPANY PROFILE - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} fund size AUM assets under management 2024 2025"
- Search: "{{COMPANY_NAME}} headquarters office locations employees count"
- Search: "{{COMPANY_NAME}} founded year history company background"
- Search: "{{COMPANY_NAME}} real estate investment focus strategy"
- Search: "{{COMPANY_NAME}} company type business model"
- Search: "{{COMPANY_NAME}} capital position debt equity lender borrower advisory"
- Search: "{{COMPANY_NAME}} dry powder available capital deployment target"
- Search: "{{COMPANY_NAME}} partnerships strategic alliances joint ventures"
- Search: "{{COMPANY_NAME}} balance sheet strength financial health liquidity"
- Search: "{{COMPANY_NAME}} funding sources institutional equity private equity debt"
- Search: "{{COMPANY_NAME}} capital raises new funds commitments 2024 2025"
- Search: "{{COMPANY_NAME}} debt equity ratio leverage appetite"
- Search: "{{COMPANY_NAME}} development fee structure compensation"
- Search: "{{COMPANY_NAME}} equity partners debt partners lenders"
- Search: "{{COMPANY_NAME}} market cycle strategy expansion distress"
- Search: "{{COMPANY_NAME}} urban suburban preference location density"
- Search: "{{COMPANY_NAME}} sustainability ESG green building LEED"
- Search: "{{COMPANY_NAME}} technology proptech smart building"
- Search: "{{COMPANY_NAME}} adaptive reuse experience conversions"
- Search: "{{COMPANY_NAME}} regulatory zoning expertise local regulations"

🔍 **EXECUTIVE TEAM - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} leadership team executives management 2024 2025"
- Search: "{{COMPANY_NAME}} CEO president managing director partners"
- Search: "{{COMPANY_NAME}} executive team LinkedIn profiles contacts"
- Search: "{{COMPANY_NAME}} leadership changes new hires promotions"
- Search: "{{COMPANY_NAME}} key executives email contact information"
- Search: "{{COMPANY_NAME}} management team names titles LinkedIn profiles"
- Search: "{{COMPANY_NAME}} C-suite executives contact details"
- Search: "{{COMPANY_NAME}} leadership team email addresses phone numbers"

🔍 **RECENT DEALS - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} recent deals transactions acquisitions 2024 2025"
- Search: "{{COMPANY_NAME}} real estate purchases investments portfolio"
- Search: "{{COMPANY_NAME}} property acquisitions developments sales"
- Search: "{{COMPANY_NAME}} closed deals announced transactions"

🔍 **TRACK RECORD - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} portfolio performance historical returns track record"
- Search: "{{COMPANY_NAME}} completed transactions total square feet units"
- Search: "{{COMPANY_NAME}} portfolio value AUM performance metrics"

🔍 **PARTNERSHIPS - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} partnerships joint ventures strategic alliances 2022 2025"
- Search: "{{COMPANY_NAME}} partner companies collaborations relationships"
- Search: "{{COMPANY_NAME}} equity partners debt partners lenders 2024"
- Search: "{{COMPANY_NAME}} strategic partners investment partners"
- Search: "{{COMPANY_NAME}} joint venture partners co-investment partners"
- Search: "{{COMPANY_NAME}} capital partners funding partners"
- Search: "{{COMPANY_NAME}} alliance partners business partners"
- Search: "{{COMPANY_NAME}} consortium members partnership agreements"
- Search: "{{COMPANY_NAME}} investor partners LP GP relationships"
- Search: "{{COMPANY_NAME}} syndication partners co-sponsors"

🔍 **FINANCIAL METRICS - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} stock ticker symbol NYSE NASDAQ publicly traded"
- Search: "{{COMPANY_NAME}} market capitalization market cap valuation"
- Search: "{{COMPANY_NAME}} annual revenue net income EBITDA profit margin"
- Search: "{{COMPANY_NAME}} credit rating S&P Moody's Fitch"
- Search: "{{COMPANY_NAME}} quarterly earnings report financial results"
- Search: "{{COMPANY_NAME}} lender type commercial bank debt fund CMBS"
- Search: "{{COMPANY_NAME}} annual loan volume origination debt"
- Search: "{{COMPANY_NAME}} balance sheet securitization lending model"
- Search: "{{COMPANY_NAME}} portfolio health performance delinquency"

🔍 **CORPORATE STRUCTURE - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} board of directors leadership governance"
- Search: "{{COMPANY_NAME}} founder background history prior ventures"
- Search: "{{COMPANY_NAME}} corporate structure LLC C-Corp partnership"
- Search: "{{COMPANY_NAME}} parent company subsidiaries ownership"
- Search: "{{COMPANY_NAME}} investment vehicle type fund structure"
- Search: "{{COMPANY_NAME}} active fund series vintage fundraising"

🔍 **MARKET POSITIONING - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} products services description core offerings"
- Search: "{{COMPANY_NAME}} target customer profile client segment"
- Search: "{{COMPANY_NAME}} competitors competition market share"
- Search: "{{COMPANY_NAME}} unique selling proposition differentiation"
- Search: "{{COMPANY_NAME}} industry awards recognitions honors"
- Search: "{{COMPANY_NAME}} recent news sentiment media coverage"

🔍 **CONTACT INFORMATION - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} contact information phone email address"
- Search: "{{COMPANY_NAME}} headquarters address office locations"
- Search: "{{COMPANY_NAME}} social media LinkedIn Twitter Facebook Instagram"

**SEARCH SOURCES TO PRIORITIZE:**
- Recent news articles (2024-2025)
- Company press releases and announcements
- SEC filings and regulatory documents
- Industry reports and publications
- LinkedIn company and executive profiles
- Real estate industry databases
- Financial news sources (Bloomberg, Reuters, WSJ)
- Company website updates and blog posts

**MANDATORY SEARCH BEHAVIOR:**
1. Perform separate web searches for each category above
2. Use multiple search queries per category to ensure comprehensive coverage
3. Prioritize information from 2024-2025 over older data
4. Cross-reference multiple sources to verify accuracy
5. If website content conflicts with recent web search results, use the web search information
6. Include information found ONLY through web search, not available in the website content

SOURCES SCHEMA:

{
  "sources": [
    {
      "url": string, // The URL of the source
      "page_section": string, // The section of the page where the source is found
      "evidence_quote": string, // The exact quote supporting this value
      "source_type": string, // The type of source (web_scraped_text, enrichment_data, company_website, news_article, press_release)
      "date_found": string // The date the source was found
      "confidence_score": number // The confidence score of the source
    }
  ]
}

**CORRECT JSON EXAMPLE:**
{
  "evidence_quote": "Arion is a private bridge lender that offers real-estate professionals quick, real-estate-secured short-term financing"
}

**INCORRECT JSON (DO NOT USE):**
{
  "evidence_quote": \"Arion is a private bridge lender that offers real-estate professionals quick, real-estate-secured short-term financing\"
}

**CRITICAL JSON FORMATTING REQUIREMENTS:**
- Generate ONLY valid JSON that can be parsed without errors
- Use single quotes (') inside string values instead of escaped double quotes (\")
- Example: "evidence_quote": "Arion is a private bridge lender" (NOT "evidence_quote": \"Arion is a private bridge lender\")
- Avoid any backslash escaping inside string values
- Ensure all quotes are properly balanced
- Test your JSON for validity before submitting

COMPREHENSIVE EXTRACTION SCHEMA:
{
  "company": {
    "company_name": {
      "value": string,           // Company name (max 255 chars) - first letter of each word should be upper case unless LP., LLC. or LLP.
      "sources": [SOURCES_SCHEMA],
    },
    "company_type": {
      "value": string,           // Company Type Mapping
      "sources": [SOURCES_SCHEMA],
      },
    },
    "company_industry": {
      "value": string,           // Industry

     
      "sources": [SOURCES_SCHEMA],
      },
    },
    "business_model": {
      "value": string,           // Description of business model (2-3 sentences)

     
      "sources": [SOURCES_SCHEMA],
      },
    },
    "investment_focus": {
      "value": [string],          // List of investment focus areas

     
      "sources": [SOURCES_SCHEMA],
      },
    },
    "investment_strategy_mission": {
      "value": string,           // Investment strategy mission
      "sources": [SOURCES_SCHEMA],    
      },
    },
    "investment_strategy_approach": {
      "value": string,           // Investment strategy approach
      "sources": [SOURCES_SCHEMA],
    }
    
    "website": {
      "value": string,           // Website URL (max 255 chars)
      "sources": [SOURCES_SCHEMA]
    },
    "main_phone": {
      "value": string,           // Main phone (max 100 chars)
      "sources": [SOURCES_SCHEMA]
    },
    "secondary_phone": {
      "value": string,           // Secondary phone (max 100 chars)
      "sources": [SOURCES_SCHEMA]
    },
    "main_email": {
      "value": string,           // Main email (max 255 chars)
      "sources": [SOURCES_SCHEMA]
    },
    "secondary_email": {
      "value": string,           // Secondary email (max 255 chars)
      "sources": [SOURCES_SCHEMA]
    },
    "linkedin": {
      "value": string,           // LinkedIn URL
      "sources": [SOURCES_SCHEMA]
    },
    "twitter": {
      "value": string,           // Twitter URL
      "sources": [SOURCES_SCHEMA]
    },
    "facebook": {
      "value": string,           // Facebook URL
      "sources": [SOURCES_SCHEMA]
    },
    "instagram": {
      "value": string,           // Instagram URL
      "sources": [SOURCES_SCHEMA]
    },
    "youtube": {
      "value": string,           // YouTube URL
      "sources": [SOURCES_SCHEMA]
    },
    "headquarters_address": {
      "value": string,           // Address
      "sources": [SOURCES_SCHEMA]
    },
    "headquarters_city": {
      "value": string,           // City
      "sources": [SOURCES_SCHEMA]
    },
    "headquarters_state": {
      "value": string,           // State
      "sources": [SOURCES_SCHEMA]
    },
    "headquarters_zipcode": {
      "value": string,           // Zip Code
      "sources": [SOURCES_SCHEMA]
    },
    "headquarters_country": {
      "value": string,           // Country
      "sources": [SOURCES_SCHEMA]
    },
    "additional_address": {
      "value": string,           // Additional address
      "sources": [SOURCES_SCHEMA]
    },
    "additional_city": {
      "value": string,           // Additional city
      "sources": [SOURCES_SCHEMA]
    },
    "additional_state": {
      "value": string,           // Additional state
      "sources": [SOURCES_SCHEMA]
    },
    "additional_zipcode": {
      "value": string,           // Additional zip code
      "sources": [SOURCES_SCHEMA]
    },
    "additional_country": {
      "value": string,           // Additional country
      "sources": [SOURCES_SCHEMA]
    },
    "fund_size": {
      "value": number,           // Fund size - NUMERIC in USD (e.g., ********* for $500M)
      "sources": [SOURCES_SCHEMA]
    },
    "aum": {
      "value": number,           // Assets under management - NUMERIC in USD (e.g., 2300000000 for $2.3B)
      "sources": [SOURCES_SCHEMA]
    },
    "number_of_properties": {
      "value": number,           // Number of properties - INTEGER (e.g., 150)
      "sources": [SOURCES_SCHEMA]
    },
    "number_of_offices": {
      "value": number,           // Number of offices - INTEGER (e.g., 25)
      "sources": [SOURCES_SCHEMA]
    },
    "office_locations": {
      "value": [string],         // List of office locations
      "sources": [SOURCES_SCHEMA]
    },
    "founded_year": {
      "value": number,           // Year founded - INTEGER (e.g., 1995)
      "sources": [SOURCES_SCHEMA]
    },
    "number_of_employees": {
      "value": number,           // Employee count - INTEGER (e.g., 500)
      "sources": [SOURCES_SCHEMA]
    },
    
    // NOTE: ALL REMAINING FIELDS MUST FOLLOW THE SAME EVIDENCE-GROUNDED STRUCTURE
    // Each field should be an object with: value, evidence, confidence_score, source_url, sources[]
    // For brevity, the remaining fields are listed with their value types, but ALL must use the evidence-grounded structure
    
    "partnerships": {
      "value": [string],         // List of partnerships
      "sources": [SOURCES_SCHEMA]
    },
    "balance_sheet_strength": {
      "value": string,           // General financial health, liquidity, and available cash
      "sources": [SOURCES_SCHEMA]
    },
    "funding_sources": {
      "value": [string],         // e.g., Institutional Equity, Private Equity, Debt Funds, Commercial Banks
      "sources": [SOURCES_SCHEMA]
    },
    "recent_capital_raises": {
      "value": string,           // Public announcements of new funds or significant capital commitments
      "sources": [SOURCES_SCHEMA]
    },
    "typical_debt_to_equity_ratio": {
      "value": number,           // Typical leverage appetite as DECIMAL (e.g., 0.65 for 65%)
      "sources": [SOURCES_SCHEMA]
    },
    
    "development_fee_structure": {
      "value": string,           // How they compensate themselves
      "sources": [SOURCES_SCHEMA]
    },
    "key_equity_partners": {
      "value": [string],         // Names of frequent or strategic equity partners
      "sources": [SOURCES_SCHEMA]
    },
    "key_debt_partners": {
      "value": [string],         // Names of frequent or strategic lenders and debt partners
      "sources": [SOURCES_SCHEMA]
    },
    "market_cycle_positioning": {
      "value": string,           // Current strategy
      "sources": [SOURCES_SCHEMA]
    },
    "urban_vs_suburban_preference": {
      "value": string,           // Primary focus in terms of location density
      "sources": [SOURCES_SCHEMA]
    },
    "sustainability_esg_focus": {
      "value": boolean,          // Approach to green building, LEED, social impact
      "sources": [SOURCES_SCHEMA]
    },
    "technology_proptech_adoption": {
      "value": boolean,          // Integration of smart building tech
      "sources": [SOURCES_SCHEMA]
    },
    "adaptive_reuse_experience": {
      "value": boolean,          // Track record with converting existing structures
      "sources": [SOURCES_SCHEMA]
    },
    "regulatory_zoning_expertise": {
      "value": boolean,          // Experience navigating complex local regulations
      "sources": [SOURCES_SCHEMA]
    },
    "investment_vehicle_type": {
      "value": string,           // Legal structure
      "sources": [SOURCES_SCHEMA]
    },
    "active_fund_name_series": {
      "value": string,           // Specific name and series/vintage of active fund
      "sources": [SOURCES_SCHEMA]
    },
    "fund_size_active_fund": {
      "value": number,           // Total capital commitment for specific active fund - INTEGER in USD (e.g., ********* for $500M)
      "sources": [SOURCES_SCHEMA]
    },
    "fundraising_status": {
      "value": string,           // Status of current fund
      "sources": [SOURCES_SCHEMA]
    },
    "lender_type": {
      "value": string,           // e.g., Commercial Bank, Debt Fund, CMBS, Agency, Life Co.
      "sources": [SOURCES_SCHEMA]
    },
    "annual_loan_volume": {
      "value": number,           // Lender's total debt origination volume - INTEGER in USD (e.g., ********** for $1B)
      "sources": [SOURCES_SCHEMA]
    },
    "lending_origin_balance_sheet_securitization": {
      "value": string,           // Hold loans on balance sheet or securitize
      "sources": [SOURCES_SCHEMA]
    },
    "portfolio_health": {
      "value": string,           // Portfolio performance, delinquency rates
      "sources": [SOURCES_SCHEMA]
    },
    "board_of_directors": {
      "value": [string],         // Names and affiliations of board members
      "sources": [SOURCES_SCHEMA]
    },
    "key_executives": {
      "value": [string],         // List of C-suite executives with names and titles
      "sources": [SOURCES_SCHEMA]
    },
    "executive_contacts": {
      "value": [
        {
          "name": string,         // Executive full name
          "title": string,       // Executive job title/position
          "email": string,       // Executive email address
          "linkedin": string,    // Executive LinkedIn profile URL
          "company_name": string // Company name for matching
        }
      ],
      "sources": [SOURCES_SCHEMA]
    },
    "founder_background": {
      "value": string,           // Brief description of founder's history
      "sources": [SOURCES_SCHEMA]
    },
    "company_history": {
      "value": string,           // Brief description of company's history
      "sources": [SOURCES_SCHEMA]
    },
    "stock_ticker_symbol": {
      "value": string,           // Stock symbol if publicly traded
      "sources": [SOURCES_SCHEMA]
    },
    "stock_exchange": {
      "value": string,           // Exchange where stock is listed
      "sources": [SOURCES_SCHEMA]
    },
    "market_capitalization": {
      "value": number,           // Total market value of outstanding shares - INTEGER in USD (e.g., 2********* for $2.5B)
      "sources": [SOURCES_SCHEMA]
    },
    "annual_revenue": {
      "value": number,           // Total revenue for last fiscal year - INTEGER in USD (e.g., ********* for $150M)
      "sources": [SOURCES_SCHEMA]
    },
    "net_income": {
      "value": number,           // Profit after all expenses and taxes - INTEGER in USD (e.g., 25000000 for $25M)
      "sources": [SOURCES_SCHEMA]
    },
    "ebitda": {
      "value": number,           // Earnings Before Interest, Taxes, Depreciation, Amortization - INTEGER in USD (e.g., 40000000 for $40M)
      "sources": [SOURCES_SCHEMA]
    },
    "profit_margin": {
      "value": number,           // Percentage of revenue that turns into profit - DECIMAL (e.g., 0.15 for 15%)
      "sources": [SOURCES_SCHEMA]
    },
    "credit_rating": {
      "value": string,           // Official credit ratings
      "sources": [SOURCES_SCHEMA]
    },
    "quarterly_earnings_link": {
      "value": string,           // Direct link to latest quarterly earnings report
      "sources": [SOURCES_SCHEMA]
    },
    "products_services_description": {
      "value": string,           // Detailed description of core offerings
      "sources": [SOURCES_SCHEMA]
    },
    "target_customer_profile": {
      "value": string,           // Description of ideal customer segment
      "sources": [SOURCES_SCHEMA]
    },
    "major_competitors": {
      "value": [string],         // List of top 3-5 direct competitors
      "sources": [SOURCES_SCHEMA]
    },
    "market_share_percentage": {
      "value": number,           // Estimated share of target market - DECIMAL (e.g., 0.08 for 8%)
      "sources": [SOURCES_SCHEMA]
    },
    "unique_selling_proposition": {
      "value": string,           // What makes company's offerings unique
      "sources": [SOURCES_SCHEMA]
    },
    "industry_awards_recognitions": {
      "value": [string],         // Significant awards or honors
      "sources": [SOURCES_SCHEMA]
    },
    "corporate_structure": {
      "value": string,           // Legal structure of entity
      "sources": [SOURCES_SCHEMA]
    },
    "parent_company": {
      "value": string,           // Name of parent company if any
      "sources": [SOURCES_SCHEMA]
    },
    "subsidiaries": {
      "value": [string],         // List of key subsidiary companies
      "sources": [SOURCES_SCHEMA]
    },
    "recent_news_sentiment": {
      "value": string,           // Summary of recent media coverage
      "sources": [SOURCES_SCHEMA]
    },
    "data_source": {
      "value": string,           // Where information was obtained
      "sources": [SOURCES_SCHEMA]
    },
    "last_updated_timestamp": {
      "value": string,           // Date and time row was last updated
      "sources": [SOURCES_SCHEMA]
    },
    "data_confidence_score": {
      "value": number,           // Reliability score (1-5) - INTEGER
      "sources": [SOURCES_SCHEMA]
    },
    "dry_powder": {
      "value": number,           // Available capital ready to invest - NUMERIC in USD (e.g., ********* for $250M)
      "sources": [SOURCES_SCHEMA]
    },
    "annual_deployment_target": {
      "value": number,           // Goal for capital deployment - NUMERIC in USD (e.g., ********* for $500M)
      "sources": [SOURCES_SCHEMA]
    },
    "transactions_completed_last_12m": {
      "value": number,           // Count of closed transactions in trailing 12 months - INTEGER (e.g., 45)
      "sources": [SOURCES_SCHEMA]
    },
    "internal_relationship_manager": {
      "value": string,           // Employee at advisory firm who owns relationship
      "sources": [SOURCES_SCHEMA]
    },
    "last_contact_date": {
      "value": string,           // Date of last meaningful interaction
      "sources": [SOURCES_SCHEMA]
    },
    "pipeline_status": {
      "value": string,           // Current stage in business development pipeline
      "sources": [SOURCES_SCHEMA]
    },
    "role_in_previous_deal": {
      "value": string,           // Primary role in deals
      "sources": [SOURCES_SCHEMA]
    },
    "total_transaction_volume_ytd": {
      "value": number,           // Recent transaction volume - NUMERIC in USD (e.g., ********* for $750M)
      "sources": [SOURCES_SCHEMA]
    },
    "deal_count_ytd": {
      "value": number,           // Count of deals year-to-date - INTEGER (e.g., 12)
      "sources": [SOURCES_SCHEMA]
    },
    "average_deal_size": {
      "value": number,           // Average deal size - NUMERIC in USD (e.g., 62500000 for $62.5M)
      "sources": [SOURCES_SCHEMA]
    },
    "portfolio_size_sqft": {
      "value": number,           // Portfolio scale in square feet - INTEGER (e.g., 5000000)
      "sources": [SOURCES_SCHEMA]
    },
    "portfolio_asset_count": {
      "value": number,           // Total number of properties under management - INTEGER (e.g., 150)
      "sources": [SOURCES_SCHEMA]
    },
    
    // EXACT-TEXT SUMMARIES AND SNIPPETS (MANDATORY)
    // Provide concise summaries using the EXACT WORDING from sources wherever possible.
    // Include copy/pasted sentences/paragraphs that state investment criteria and financial metrics.
    "overview_summary": {
      "value": {
        "financial_metrics_summary_text": string, // 2-6 sentences capturing AUM, fund size, revenue, EBITDA, dry powder, loan volume, etc. Use exact phrasing from sources; concatenate if needed. If multiple sources, join with line breaks.
        "investment_criteria_summary_text": string, // 2-6 sentences capturing property types, geographies, strategies, deal sizes, capital position, leverage appetite, underwriting notes. Use exact phrasing from sources.
        "exact_snippets": [
          {
            "field": string,   // e.g., "AUM", "Fund Size", "Investment Strategy", "Geography", "Deal Size", "Capital Position"
            "text": string,    // exact quote or sentence from the source
            "source": string   // URL of the page containing the text
          }
        ]
      }
    }
  }
}

**EXTRACTION GUIDELINES:**
1. **METADATA FIRST**: Start with PAGE METADATA as your PRIMARY source - extract all available data from the YAML metadata
2. **SCRAPE FOR GAPS**: For each URL in the metadata, scrape only to find information missing from the YAML metadata
3. **URL RESEARCH SECOND**: Use the RELATED URLS to perform comprehensive web searches and extract additional information
4. **WEB SEARCH THIRD**: Perform the web searches above to find current information not available in metadata or URLs
5. **WEBSITE TEXT LAST**: Use website text only as supplementary information when other sources are insufficient
6. **METADATA PRECEDENCE**: Always prefer metadata values over scraped data when both exist
7. **Be Extremely Thorough**: Extract every detail from metadata, scraped URLs, and web search results
8. **Extract All Lists**: For any array fields, include ALL items you find, not just a few examples
9. **Executive Team Extraction**: For each executive found, extract:
   - Full name (first_name, last_name, full_name)
   - Job title and seniority level
   - Contact information (email, phone, LinkedIn, social media)
   - Location information (city, state, country)
   - Professional headline or summary
   - All available social media profiles
7. **Financial Details**: Look for any mention of fund sizes, AUM, deal sizes, returns, etc.
8. **CONVERT NUMERICAL VALUES TO PROPER FORMAT**:  
   **Percentages (to decimals)**: All percentage fields must be converted to decimal format by dividing by 100:
   - targetReturn: 15% becomes 0.15 (not 15)
   - historicalIRR: 12% becomes 0.12 (not 12) 
   - historicalEM: 180% becomes 1.8 (not 180)
   - interestRate: 8% becomes 0.08 (not 8)
   - interestRateSOFR: 5.5% becomes 0.055 (not 5.5)
   - loanToValueMin/Max: 85% becomes 0.85 (not 85)
   - loanToCostMin/Max: 80% becomes 0.80 (not 80)
   - minLoanDSCR/maxLoanDSCR: 1.2 stays 1.2 (ratio, not percentage)
   - All fee percentages: 2% becomes 0.02 (not 2)
8. **Company Data Focus**: Extract ALL information that maps to the company table fields in the CSV schema
9. **Financial Information**: Extract fund sizes, AUM, revenue, profit margins, debt ratios, and all financial metrics
10. **Contact Information**: Extract ALL contact details including phones, emails, addresses, and social media
11. **Partnership Information**: Look for any mentioned partners, joint ventures, relationships
12. **Corporate Structure**: Extract legal structure, parent companies, subsidiaries, board members
13. **Market Intelligence**: Capture competitors, market share, awards, news sentiment
14. **Current Information**: Prioritize 2024-2025 information over older data
15. **COMPREHENSIVE COMPANY FIELD EXTRACTION**:
    - **Financial Metrics**: Extract fund sizes, AUM, revenue, profit margins, EBITDA, market cap, debt ratios, loan volumes
    - **Percentage Values**: Convert ALL percentages to decimals (15% → 0.15) for profit margins, debt-to-equity ratios, market share
    - **Location Data**: Capture headquarters and additional office addresses, cities, states, zip codes, countries
    - **Corporate Structure**: Extract legal structure, parent companies, subsidiaries, board members, founder background
    - **Market Intelligence**: Capture competitors, market share, unique selling propositions, awards, recent news sentiment
    - **Contact Information**: Extract ALL contact details including primary/secondary phones/emails, complete address information, social media profiles
    - **Partnership Data**: Find ALL partnerships, joint ventures, strategic alliances, key equity/debt partners
    - **Operational Metrics**: Extract employee counts, office counts, transaction volumes, deal counts, deployment targets, dry powder
    - **Company Background**: Capture company history, founder background, business model, investment focus areas
16. **EXACT TEXT REQUIREMENT**:
    - For the fields under "overview_summary", use the EXACT sentences/phrases from the website or cited sources (no paraphrasing). Surround multi-sentence quotes with appropriate punctuation and maintain original capitalization.
    - Populate at least 3 entries in "exact_snippets" when available, covering both financial metrics and investment criteria.
16. **SEARCH PATTERNS**: Use these common terms and variations:
    - Fund Size: "Fund Size", "AUM", "Assets Under Management", "Committed Capital", "Total Capital"
    - Financial Metrics: "revenue", "profit", "EBITDA", "market cap", "credit rating", "earnings"
    - Contact Info: "phone", "email", "address", "headquarters", "contact", "LinkedIn", "Twitter"
    - Corporate: "parent company", "subsidiary", "board", "founder", "CEO", "executives"
    - Partnerships: "partner", "alliance", "joint venture", "collaboration"
17. **COMPANY TYPE VALIDATION**: 
    - **MANDATORY**: Verify company type through web research before assignment
    - **ALLOWED VALUES ONLY**: Use only values from the COMPANY_TYPES array in dynamic mappings
    - **NO GENERIC TERMS**: Do not use "null", "private", "public", or generic descriptions
    - **RESEARCH REQUIRED**: Search for company structure, legal entity, business model, and ownership type
    - **CROSS-REFERENCE**: Verify across multiple sources (website, SEC filings, business registries)
18. **PARTNERSHIPS VALIDATION**:
    - **SPECIFIC NAMES ONLY**: Extract actual company names, not generic descriptions
    - **VERIFICATION REQUIRED**: Only include partnerships found through web research
    - **NO GENERIC TEXT**: Avoid phrases like "various partners" or "strategic alliances" without specific names
    - **CURRENT STATUS**: Prioritize active partnerships over historical ones
    - **MULTIPLE SOURCES**: Cross-reference partnership information across different sources

Main Website URL:
{{WEBSITE_URL}}

**NOTE**: Website text is provided for reference only. Prioritize the PAGE METADATA and RELATED URLS above for your extractions. Use website text only as supplementary information when metadata and URL research is insufficient.

Company Name:
{{COMPANY_NAME}}

Allowed Values (from central_mapping table):
{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE}

**CRITICAL DATA TYPE CONVERSION REQUIREMENTS:**

**MONETARY VALUES (INTEGER/NUMERIC FIELDS):**
- Convert ALL monetary values to INTEGER/NUMERIC format (no currency symbols, no formatting)
- **CRITICAL LIMITATION**: INTEGER fields have a maximum value of 100,000,000,000 (~100.0 billion)
- **For values exceeding 100.0 billion**: Use the maximum value **********00
- **Examples:**
  - "$14.6 billion" → 14600000000 (not"$14.6 billion")
  - "$500 million" → ********* (not "$500 million")
  - "$150 million" → ********* (not "$150 million")
  - "$25 million" → 25000000 (not "$25 million")
  - "$40 million" → 40000000 (not "$40 million")
  - "$250 million" → ********* (not "$250 million")
  - "$750 million" → ********* (not "$750 million")
  - "$62.5 million" → 62500000 (not "$62.5 million")
  - **Fund Size**: "$500 million" → fund_size: ********* (INTEGER)
  - **AUM**: "$2.3 billion" → aum: 2300000000 (INTEGER)

**PERCENTAGE VALUES (DECIMAL FIELDS):**
- Convert ALL percentages to DECIMAL format (divide by 100)
- **Examples:**
  - "15% profit margin" → profit_margin: 0.15 (not 15)
  - "65% debt-to-equity" → typical_debt_to_equity_ratio: 0.65 (not 65)
  - "8% market share" → market_share_percentage: 0.08 (not 8)

**COUNT VALUES (INTEGER FIELDS):**
- Convert ALL counts to INTEGER format (no ranges, no text)
- **Examples:**
  - "50-100 employees" → number_of_employees: 75 (not "50-100")
  - "150 properties" → number_of_properties: 150 (not "150")
  - "25 offices" → number_of_offices: 25 (not "25")
  - "1995" → founded_year: 1995 (not "1995")
  - "45 transactions" → transactions_completed_last_12m: 45 (not "45")
  - "12 deals" → deal_count_ytd: 12 (not "12")
  - "5,000,000 sq ft" → portfolio_size_sqft: 5000000 (not "5,000,000")

**DISPLAY VALUES (STRING FIELDS):**
- Keep as formatted strings for display purposes
- **Examples:**
  - company_name: "Company Name" (keep as string)
  - website: "https://example.com" (keep as string)

**FINANCIAL EXTRACTION EXAMPLES:**
- **Fund Size**: "$500 million fund" → fund_size: ********* (INTEGER)
- **AUM**: "$2.3 billion AUM" → aum: 2300000000 (INTEGER)
- **Revenue**: "$150 million annual revenue" → annual_revenue: ********* (INTEGER)
- **Profit Margin**: "15% profit margin" → profit_margin: 0.15 (DECIMAL)
- **Debt Ratio**: "65% debt-to-equity" → typical_debt_to_equity_ratio: 0.65 (DECIMAL)
- **Market Share**: "8% market share" → market_share_percentage: 0.08 (DECIMAL)
- **Employee Count**: "50-100 employees" → number_of_employees: 75 (INTEGER)

**FINAL REMINDER: 
1. **PRIORITIZE PAGE METADATA**: Extract all available data from the YAML metadata first - this is your PRIMARY source
2. **SCRAPE FOR GAPS ONLY**: For each URL in the metadata, scrape only to find information missing from the YAML metadata
3. **USE RELATED URLS**: Perform web searches on the provided URLs to gather additional information
4. **PERFORM LIVE WEB SEARCHES**: Search for each category above using current information
5. **METADATA TAKES PRECEDENCE**: Always prefer metadata values over scraped data when both exist
6. **WEBSITE TEXT IS OPTIONAL**: Only use website text when other sources are insufficient
7. **SINGLE JSON RESPONSE**: Your response MUST be a single valid JSON object and nothing else
8. **NO MARKDOWN OR EXPLANATIONS**: Do NOT include markdown, code blocks, or any explanation
9. **VALID JSON FORMAT**: Use single quotes inside strings, avoid escaped quotes (\"), ensure proper JSON syntax
10. **BE EXTREMELY THOROUGH**: Extract every detail from metadata, scraped URLs, and web search results - every detail matters!**

**CRITICAL SOURCE TRACKING REMINDERS:**
1. **EVIDENCE-GROUNDED STRUCTURE IS MANDATORY**: Every field MUST follow the evidence-grounded structure with value, evidence, confidence_score, source_url, and sources array
2. **SOURCE TRACKING IS MANDATORY**: Every piece of data MUST have detailed source information with exact evidence quotes
3. **CONFIDENCE SCORES**: Provide confidence scores between 0.0 and 1.0 for all extractions. Only include data with confidence >= 0.7
4. **DOCUMENT ALL SOURCES**: Include specific URLs, page sections, and document names for every piece of information
5. **NO SOURCES = NO DATA**: If you cannot identify reliable sources for information, set the value to null
6. **VERIFY SOURCES**: Cross-reference information across multiple sources when possible
7. **WEB SEARCH SOURCES**: Include specific URLs and website names for all web search results
8. **WEBSITE SOURCES**: Specify exact page/section when information comes from company website
9. **EVIDENCE QUOTES**: Include exact quotes from sources that support each extracted value
10. **SOURCE PRIORITY**: Prioritize page_metadata > company_website > web_search > enrichment_data > news_article > press_release`

// Interface for dynamic mappings
interface MappingData {
  [key: string]: string[]
}

// Template function with dynamic mappings from central_mapping table
export const COMPANY_OVERVIEW_V2_USER_TEMPLATE_FUNCTION = (company: {
  company_name: string
  company_website: string
  industry?: string
}, mappings: MappingData = {}, pageMetadataContext: string = '') => {
  
  // Build allowed values section dynamically from mappings
  const buildAllowedValues = (mappings: MappingData) => {
    const allowedValues: Record<string, string[]> = {};
    
    // Map the database types to the JSON schema field names
    const typeMapping = {
      'Property Type': 'PROPERTY_TYPES',
      'Strategies': 'STRATEGIES', 
      'Deal Type': 'DEAL_TYPES',
      'Loan Program': 'LOAN_PROGRAMS',
      'Capital Position': 'CAPITAL_POSITION',
      'Capital Source': 'CAPITAL_SOURCE',
      'Structured Loan Tranches': 'STRUCTURED_LOAN_TRANCHES',
      'Recourse Loan': 'RECOURSE_LOAN_TYPES',
      'U.S Regions': 'US_REGIONS',
      'Company Type': 'COMPANY_TYPES'
    };
    
    // Populate allowed values from mappings
    for (const [dbType, jsonKey] of Object.entries(typeMapping)) {
      if (mappings[dbType] && mappings[dbType].length > 0) {
        allowedValues[jsonKey] = mappings[dbType];
      }
    }
    
    // Add property subcategories from subcategory mappings
    const propertySubcategories: string[] = [];
    for (const [key, values] of Object.entries(mappings)) {
      if (key.includes('Property Type -') && key.includes('Subproperty Type')) {
        propertySubcategories.push(...values);
      }
    }
    if (propertySubcategories.length > 0) {
      allowedValues['PROPERTY_SUBCATEGORIES'] = propertySubcategories;
    } else if (mappings['Property Type']) {
      // Fallback to property types if no subcategories found
      allowedValues['PROPERTY_SUBCATEGORIES'] = mappings['Property Type'];
    }
    
    // Fallback to empty arrays if no mappings available
    const fallbackTypes = [
      'PROPERTY_TYPES', 'STRATEGIES', 'DEAL_TYPES', 'LOAN_PROGRAMS', 
      'CAPITAL_POSITION', 'CAPITAL_SOURCE', 'STRUCTURED_LOAN_TRANCHES',
      'RECOURSE_LOAN_TYPES', 'US_REGIONS', 'COMPANY_TYPES', 'PROPERTY_SUBCATEGORIES'
    ];
    
    fallbackTypes.forEach(type => {
      if (!allowedValues[type]) {
        allowedValues[type] = [];
      }
    });
    
    // Return formatted JSON string for the template
    return JSON.stringify(allowedValues, null, 2);
  };
  
  const allowedValuesJson = buildAllowedValues(mappings);
  

  return COMPANY_OVERVIEW_V2_USER_TEMPLATE
    .replace(/\{\{WEBSITE_URL\}\}/g, company.company_website || '')
    .replace(/\{\{COMPANY_NAME\}\}/g, company.company_name || '')
    .replace(/\{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE\}/, allowedValuesJson)
    .replace(/\{\{PAGE_METADATA_CONTEXT\}\}/g, pageMetadataContext || 'No page metadata available')
} 
