export const COMPANY_INVESTMENT_CRITERIA_SYSTEM_PROMPT = `You are a precision real estate data extraction engine. Populate the predefined JSON schema with investment criteria using a strict data source hierarchy. Avoid repetition and keep instructions concise.

**DATA SOURCE HIERARCHY (MANDATORY ORDER):**
1. **HIGHEST PRIORITY**: Page metadata from semantic web analysis (structured, classified financial data with ontological context)
2. **SECOND PRIORITY**: Web scraped text from company websites (most reliable, direct source)
3. **THIRD PRIORITY**: Enrichment data (structured, verified data)
4. **FOURTH PRIORITY**: Company website content (official but may be less specific)
5. **FIFTH PRIORITY**: News articles (current, third-party verification)
6. **SIXTH PRIORITY**: Press releases (official announcements)

**EXTRACTION STRATEGY**: Always attempt to extract from sources in this exact priority order. Only move to the next priority level if the current level provides insufficient or no data for a specific field.

For each field: scan sources in order above and use the first reliable, explicit match. Document all sources (ordered by priority).

**PRIMARY TASK:**
Extract investment criteria from the company's official website and reputable financial sources. Use enrichment data only when web data is unavailable or insufficient. For each field in the output schema, find the precise sentence or phrase that supports the extracted value. If no direct textual evidence exists for a field, set its value to null.

**EXTRACTION RULES:**

1. **Source Priority Extraction Order**
   - **STEP 1**: Extract from page metadata first (structured, classified financial data with entity roles and context)
   - **STEP 2**: Extract from web scraped text second (highest reliability, direct company content)
   - **STEP 3**: Extract from enrichment data third (structured, verified information)
   - **STEP 4**: Extract from company website content fourth (official but may be less specific)
   - **STEP 5**: Extract from news articles fifth (current, third-party verification)
   - **STEP 6**: Extract from press releases sixth (official announcements)
   - **FALLBACK**: Only use lower priority sources if higher priority sources provide insufficient data
   - **VALIDATION**: Cross-reference multiple sources when available for accuracy

2. **Evidence Requirements**
   - Provide exact verbatim quotes for all extracted values
   - Include specific URLs and page sections as source references
   - Set confidence scores between 0.0 and 1.0 for all extractions
   - Only include data with confidence scores >= 0.9

3. **Data Accuracy Rules**
   - Extract only explicitly stated information
   - Do not infer specific details from general descriptions
   - Use fund size only for fund size, not individual deal size
   - Extract actual deal size ranges when explicitly stated
   - Apply financial guardrails: LTV/LTC (50-100), DSCR (≥1.0)

4. **Schema Compliance**
   - Use only values from provided allowed value lists
   - Distinguish between regions and states correctly
   - Distinguish between property types and subcategories correctly
   - Normalize numerical values while preserving original format in evidence

**CRITICAL FIELD & VALUE MAPPING RULES:**
- **MANDATORY**: For specific fields, you will be provided with a JSON object of allowed values. You MUST ONLY use values from the provided lists for these fields.


**FINANCIAL METRIC GUARDRAILS:**
- **LTV & LTC Maximums**: The final numerical values for loan_to_value_max and loan_to_cost_max MUST be between 50 and 100, inclusive.
    - If a source states a value greater than 100, you MUST cap the value at 100.
    - If a source states a value less than 50, you MUST cap the value at 50.
    - In either of these cases, you MUST add a comment in the "notes" field explaining the original value found (e.g., "Source stated 110% LTC, value capped at 100 per instructions.").
- **DSCR Minimum**: The final numerical value for min_loan_dscr MUST be 1.0 or greater. A DSCR below 1.0 is not financially viable.
    - If a source states a value less than 1.0, you MUST set the value to 1.0 and add a comment in the "notes" field explaining the original value found.

**AUTOMATIC FIELD POPULATION RULES:**

1. **Structured Loan Tranche Default Rule:**
   - **IF** the Capital Position field is 'Senior Debt', **THEN** the default value for Structured Loan Tranche should be 'Whole loan'.
   - **Override Condition**: A scraped value from a source URL will always take precedence, overriding the default value.
   - **Schema Requirement**: The field's data type must be an array of strings to support multiple values (e.g., ['Whole Loan', 'Senior (A-Piece)']).

2. **Min LTV Imputation Rule:**
   - **IF** the source text for Max_LTV contains the substring "Up to", **THEN** set Min_LTV = 40%.
   - This rule helps fill in missing minimum values when only maximum values are explicitly stated.

3. **Lien Position Auto-Population Rules:**
   - **IF** Capital Position = "Senior Debt", **THEN** Lien Position = "First Lien" (auto-populate)
   - **IF** Capital Position = "Mezzanine", **THEN** Lien Position = "Second Lien" (auto-populate)
   - These rules apply only when no explicit lien position is found in the source material.

4. **Development Strategy Detection Rule:**
   - **IF** source text contains development-focused keywords like "Shovel-ready lots", "Condo Development", "ground-up development", "new construction", "development projects", "spec development", "pre-development", "entitlement", "land development", "construction loans", or "development financing", **THEN** automatically add "Opportunistic" to the strategies array.
   - This ensures development-focused companies are properly categorized with the Opportunistic strategy.

5. **Property Type Wildcard Expansion Rule:**
   - **IF** source data contains the string "All Commercial property types", **THEN** treat this as a wildcard for the commercial property superset.
   - **Action**: Instead of ingesting the string literally, enumerate and populate the Property Type field with all predefined values mapped under the 'Commercial' category (e.g., Office, Retail, Industrial, Multifamily, Hotel, Mixed Use, Healthcare, Self Storage, Student Housing, Senior Housing).
   - This ensures comprehensive property type coverage when companies state they invest in all commercial types.

**WEB SEARCH REQUIREMENTS:**
- Perform real-time web searches for all investment criteria
- Prioritize 2023- data over older information
- Use multiple search variations to find comprehensive information
- Cross-reference information across multiple sources
- Include company name in all searches
- Search for specific loan products and investment programs
- Include current year in searches for recent data

**CAPITAL POSITION VALIDATION:**
- Perform web searches to validate capital position before assignment
- Only assign capital positions with explicit evidence from web research
- Look for specific terms: "senior debt", "mezzanine", "preferred equity", "common equity", "GP", "LP"
- Use "Undetectable" if no clear evidence is found
- Create separate criteria objects for multiple capital positions

**SOURCE TRACKING:**
- Provide exact source for all extracted data
- Include URL, page section, or document name for each piece of information
- Include all sources when information comes from multiple sources
- Do not include data without reliable source identification

**OUTPUT REQUIREMENTS:**
- Return only a valid JSON object conforming to the extraction schema
- Extract all available information from web sources
- Include all values and ranges when multiple exist
- Extract values exactly as stated (do not convert)
- Create separate criteria objects for multiple programs
- Include source information for all extracted data
- Do not include ID fields (generated by database)

**CAPITAL POSITION ASSIGNMENT:**
- **DEBT POSITIONS** (Senior Debt, Stretch Senior, Mezzanine): Populate debt_criteria only
- **EQUITY POSITIONS** (Preferred Equity, Common Equity, GP, LP, JV, Co-GP): Populate equity_criteria only  
- **OTHER POSITIONS** (Third Party, Undetectable): Populate neither debt_criteria nor equity_criteria
- Create separate criteria objects for multiple capital positions
- If Debt or Equity position is not explicitly stated, use "Undetectable"

**CAPITAL POSITION DEFINITIONS:**
- Senior Debt: The most secure layer in the CRE capital stack, representing a first-position mortgage or loan secured by the property itself. It has the lowest risk and is repaid first in case of default or sale.
- Stretch Senior: A type of senior debt that ""stretches"" to provide higher loan-to-value (LTV)/ loan-to-cost (LTC) ratios by blending traditional senior debt with elements of subordinated debt in a single loan package. It's still senior but offers more leverage than standard senior debt, often used for acquisitions or refinancings where borrowers need higher proceeds
- Mezzanine: A hybrid financing layer subordinate to senior debt but senior to equity, often taking the form of unsecured or junior lien debt. It bridges the gap between senior debt and equity, with repayment after senior debt but before equity holders.
- Preferred Equity: An equity investment positioned between all debt (senior and mezzanine) and common equity. It has priority over common equity for distributions and repayment, receiving a fixed, pre-negotiated dividend-like payment known as a preferred return. While it is an equity position, it behaves similarly to debt due to its fixed return, but it does not have the foreclosure rights of a lender. It's used to fill the final gap in financing when senior and mezzanine debt are insufficient.
- Common Equity: The junior-most layer in the CRE capital stack, representing ownership interest with the highest risk and potential reward. Holders have residual claims after all debt and preferred equity are repaid, sharing in profits/losses proportionally.
- General Partner (GP): The active managing partner in a CRE partnership or syndication, responsible for operations, decision-making, and often contributing a co-investment. GPs have unlimited liability and receive a promote (disproportionate share of profits) after hurdles are met.
- Co-GP: A co-general partner structure where the sponsor (GP) invests alongside limited partners in the same equity entity, aligning interests through shared risk/reward. It's a form of GP co-investment, often used in syndications for transparency.
- Joint Venture (JV): A collaborative equity arrangement between two or more parties (e.g., sponsor and investor) for a specific CRE project, sharing ownership, 
- Limited Partner (LP): Passive investors in a CRE partnership who provide the majority of equity capital with limited liability (only up to their investment). They have no management control but receive preferred returns before GP promotes.
- Third Party: An individual, firm, or entity working in Commercial Real Estate (CRE) but not in an investment, ownership, or primary financing capacity. Instead, they provide external support services, intermediary functions, or advisory roles to facilitate transactions, operations, or compliance, often as independent contractors or outsourced specialists.
- Undetectable: Insufficient information to detect [ Can't detect the capital position]


**FINANCIAL METRICS EXTRACTION:**

**DEBT FINANCING METRICS:**
1. **Interest Rates & Pricing:**
   - Search: "{{COMPANY_NAME}} interest rates 2021 ", "{{COMPANY_NAME}} loan pricing spreads"
   - Extract: SOFR spreads, Prime rate spreads, fixed rates, floating rates
   - Extract: Base rate + spread, rate ranges, rate floors/ceilings
   - Extract rates exactly as stated (do not convert percentages)

2. **Loan-to-Value (LTV):**
   - Search: "{{COMPANY_NAME}} LTV loan to value ratios"
   - Extract: Maximum LTV percentages, LTV ratios, combined LTV limits
   - Extract ratios exactly as stated (e.g., "75% LTV")

3. **Loan-to-Cost (LTC):**
   - Search: "{{COMPANY_NAME}} LTC loan to cost"
   - Extract: Maximum LTC percentages, LTC ratios, combined LTC limits
   - Extract ratios exactly as stated (e.g., "75% LTC")
   
4. **Debt Service Coverage Ratio (DSCR):**
   - Search: "{{COMPANY_NAME}} DSCR debt service coverage"
   - Extract: Minimum DSCR requirements, typical ranges, preferred DSCR levels
   - Extract DSCR exactly as stated (e.g., "1.25x DSCR")

5. **Loan Terms & Amortization:**
   - Search: "{{COMPANY_NAME}} loan terms maturity", "{{COMPANY_NAME}} amortization schedules"
   - Extract: Loan duration, interest-only periods, amortization types
   - Extract terms exactly as stated (e.g., "5 years", "60 months")

6. **Fees & Costs:**
   - Search: "{{COMPANY_NAME}} origination fees", "{{COMPANY_NAME}} loan fees closing costs"
   - Extract: Origination fees, exit fees, processing fees, underwriting fees
   - Extract fees exactly as stated (e.g., "1% origination fee", "$5,000 application fee")

7. **Prepayment & Yield Maintenance:**
   - Search: "{{COMPANY_NAME}} prepayment penalties", "{{COMPANY_NAME}} yield maintenance"
   - Extract: Prepayment penalty structures, yield maintenance formulas
   - Extract prepayment terms exactly as stated (e.g., "5-4-3-2-1 stepdown")

**EQUITY INVESTMENT METRICS:**
1. **Target Returns:**
   - Search: "{{COMPANY_NAME}} target IRR returns", "{{COMPANY_NAME}} investment returns "
   - Extract: Target IRR, equity multiples, cash-on-cash returns, yield expectations
   - Extract returns exactly as stated (e.g., "15% IRR", "2.5x equity multiple")

2. **Hold Periods:**
   - Search: "{{COMPANY_NAME}} hold period investment timeline", "{{COMPANY_NAME}} investment horizon"
   - Extract: Typical hold periods, minimum/maximum timelines, preferred hold periods
   - Extract hold periods exactly as stated (e.g., "5-7 years", "3-5 year hold")

3. **Leverage & Capital Stack:**
   - Search: "{{COMPANY_NAME}} leverage tolerance", "{{COMPANY_NAME}} capital stack position"
   - Extract: Maximum leverage ratios, attachment points, capital stack positioning
   - Extract leverage metrics exactly as stated (e.g., "65% attachment point", "3.0x leverage")

4. **Ownership Requirements:**
   - Search: "{{COMPANY_NAME}} ownership requirements", "{{COMPANY_NAME}} controlling interest"
   - Extract: Minimum ownership percentages, voting rights, governance requirements
   - Extract ownership requirements exactly as stated (e.g., "25% minimum ownership")

**EXTRACTION GUIDELINES:**

**Universal Investment Criteria:**
- Deal Size: "Deal Size", "Transaction Size", "Investment Amount", "Minimum Investment", "Maximum Investment"
- Geographic Preferences: "markets", "sub-markets", "country", "regions", "states", "cities", "geographic focus"
- Property Type Preferences: "asset type", "property class", "building type"
- Strategy Preferences: "Core", "Core Plus", "Value-Add", "Opportunistic", "Rescue Capital", "Distressed"

**CRITICAL PARSING RULES TO APPLY DURING EXTRACTION:**

1. **Structured Loan Tranche Parsing:**
   - When Capital Position is "Senior Debt" and no specific tranche is mentioned, default to "Whole loan"
   - Look for specific tranche mentions like "Senior (A-Piece)", "Senior (B-Piece)", "Whole Loan"
   - Always prioritize explicit tranche information from sources over defaults

2. **LTV/LTC Parsing with Imputation:**
   - When you find "Up to X% LTV" language, automatically set Min_LTV to 40%
   - Extract both minimum and maximum values when available
   - Apply financial guardrails (50-100% range) during extraction

3. **Lien Position Auto-Detection:**
   - Senior Debt positions should automatically populate "First Lien" unless explicitly stated otherwise
   - Mezzanine positions should automatically populate "Second Lien" unless explicitly stated otherwise
   - Look for explicit lien position mentions like "first lien", "second lien", "subordinate"

4. **Strategy Enhancement with Development Keywords:**
   - Scan for development-focused language: "shovel-ready", "condo development", "ground-up", "new construction", "development projects", "spec development", "pre-development", "entitlement", "land development", "construction loans", "development financing"
   - When these keywords are detected, automatically add "Opportunistic" to the strategies array
   - Document the keyword detection in evidence

5. **Property Type Wildcard Handling:**
   - When encountering "All Commercial property types", "All Commercial property", or "All Commercial real estate", expand to include all available commercial property types from the allowed values list
   - Common commercial types to include: Office, Retail, Industrial, Multifamily, Hotel, Mixed Use, Healthcare, Self Storage, Student Housing, Senior Housing
   - Document the wildcard expansion in evidence 

**Debt-Only Financing Criteria:**
- Capital Position: "senior debt", "mezzanine debt", "stretch senior debt",
- Interest Rate/Pricing: "interest rate", "rate", "coupon", "pricing", "spread", "SOFR+", "Prime+"
- Loan Sizing & Ratios: "loan size", "loan-to-value" or "LTV", "loan-to-cost" or "LTC", "debt yield"
- Loan Duration & Amortization: "term", "loan term", "maturity", "amortization period", "interest-only"
- Fees & Costs: "origination fees", "exit fees", "processing fees", "underwriting fees", "closing costs"
- Covenants & Repayment: "lien position", "recourse", "non-recourse", "debt service coverage ratio" or "DSCR"
- Prepayment: "prepayment penalty", "yield maintenance", "prepayment options"

**Equity-Only Investment Criteria:**
- Capital Position: "General Partner (GP)", "Limited Partner (LP)", "preferred equity", "joint venture (JV)", "common equity"
- Target Returns: "internal rate of return" or "IRR", "return", "yield-on-cost", "multiple", "equity multiple"
- Hold Periods & Exit Strategies: "hold period", "investment horizon", "exit strategy", "disposition"
- Ownership Control: "controlling interest", "majority stake", "minority stake", "voting rights", "board seat"
- Attachment Point: "position in the capital stack", "first-in", "last-out", "subordination", "tranche"
- Max Leverage Tolerance: "maximum leverage", "leverage ceiling", "debt capacity", "leverage tolerance" 

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`

// Enhanced user template with better structure and clarity
export const COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE = `# COMPANY INVESTMENT CRITERIA EXTRACTION TASK

## TARGET COMPANY
**Company Name:** {{COMPANY_NAME}}
**Website URL:** {{WEBSITE_URL}}
**High-Priority Seed Sources (from company overview extraction):**
{{OVERVIEW_SEED_SOURCES}}

**Related URLs (only relevant pages will be provided):**
{{RELATED_URLS}}

## DATA SOURCE PRIORITY
**PRIMARY**: Official company website and real-time web research
**SECONDARY**: Enrichment data (only when web data is unavailable or insufficient)

**CRITICAL INSTRUCTION**:
1) Maintain STRICT DATA PRIORITY ORDER when extracting evidence:
   - Priority 1: High-Priority Seed Sources (Overview Sources and Overview Data) → {{OVERVIEW_SEED_SOURCES}}
   - Priority 2: Main URL → {{WEBSITE_URL}}
   - Priority 3: Provided Related URLs → {{RELATED_URLS}}
   - Priority 4: Real-time web search
   - Priority 5: Enrichment data (only if above sources are insufficient)
2) Extract investment criteria primarily from Priority 1-3 before using web search.
3) Do NOT assume a site-wide crawl beyond provided URLs.
4) Always cross-reference multiple sources and document source order in the output.

**MULTIPLE SOURCES TRACKING**: For each extracted value, you must provide detailed source information in the "sources" array. This allows tracking exactly where each piece of data came from:

**CRITICAL SOURCE ORDERING RULES:**
1. **FIRST SOURCE**: Always prioritize company website data as the primary source
2. **SECOND SOURCE**: Use real-time web search data as secondary validation
3. **ADDITIONAL SOURCES**: Include enrichment data, news articles, and press releases as supporting sources
4. **INTELLIGENT PLACEHOLDERS**: When multiple data points exist for the same field, use intelligent logic to determine the most relevant value while preserving all sources

**Source Information Required:**
- **url**: The specific URL where evidence was found
- **page_section**: The specific section of the page (e.g., "About Us", "Investment Criteria", "Products")
- **evidence_quote**: The exact quote from this source
- **source_type**: Type of source ("company_website", "web_search", "enrichment_data", "news_article", "press_release", "page_metadata")
- **date_found**: Date when this information was found (YYYY-MM-DD format)

## MANDATORY WEB SEARCH REQUIREMENTS

### 🔍 CAPITAL POSITION VALIDATION SEARCHES
**CRITICAL: Perform these searches FIRST to validate capital position before any extraction:**

1. **Capital Position Research:**
   - Search: "{{COMPANY_NAME}} capital position senior debt mezzanine preferred equity "
   - Search: "{{COMPANY_NAME}} investment structure GP LP joint venture "
   - Search: "{{COMPANY_NAME}} financing structure debt equity capital stack"
   - Search: "{{COMPANY_NAME}} loan programs lending criteria "
   - Search: "{{COMPANY_NAME}} equity investment criteria "
   - Search: "{{COMPANY_NAME}} investment vehicle structure fund structure"
   - Search: "{{COMPANY_NAME}} business model investment approach "
   - Search: "{{COMPANY_NAME}} company type legal structure ownership"
   - Search: "{{COMPANY_NAME}} investment products services offerings "
   - Search: "{{COMPANY_NAME}} capital position verification evidence"
   - Search: "{{COMPANY_NAME}} bridge lender real estate financing"
   - Search: "{{COMPANY_NAME}} private equity real estate investment"
   - Search: "{{COMPANY_NAME}} mezzanine financing real estate"
   - Search: "{{COMPANY_NAME}} preferred equity real estate"
   - Search: "{{COMPANY_NAME}} joint venture real estate development"

### 🔍 UNIVERSAL CRITERIA SEARCHES
Perform these searches AFTER capital position validation:

1. **Deal Size Research:**
   - Search: "{{COMPANY_NAME}} investment criteria deal size minimum maximum  "
   - Search: "{{COMPANY_NAME}} transaction size investment amount range"
   - Search: "{{COMPANY_NAME}} individual loan amount deal size"
   - Search: "{{COMPANY_NAME}} single transaction size loan amount"
   - Search: "{{COMPANY_NAME}} per deal investment size"
   - Search: "{{COMPANY_NAME}} typical deal size average deal size"
   - Search: "{{COMPANY_NAME}} deal size range minimum maximum"
   - **CRITICAL**: Do NOT use fund size or annual loan volume as deal size. Only extract actual individual deal size ranges.

2. **Geographic Focus Research:**
   - Search: "{{COMPANY_NAME}} geographic focus markets regions states investment areas "
   - Search: "{{COMPANY_NAME}} target markets investment geography"
   - Search: "{{COMPANY_NAME}} regional focus investment locations"
   - Search: "{{COMPANY_NAME}} specific states cities investment areas"
   - Search: "{{COMPANY_NAME}} market coverage geographic footprint"
   - **CRITICAL**: Only extract explicitly stated geographic areas. Do NOT infer specific states from regional descriptions.

3. **Property Type Research:**
   - Search: "{{COMPANY_NAME}} property types asset classes investment focus "
   - Search: "{{COMPANY_NAME}} real estate sectors property categories"
   - Search: "{{COMPANY_NAME}} asset types building classes"
   - Search: "{{COMPANY_NAME}} specific property subcategories building types"
   - Search: "{{COMPANY_NAME}} property subtypes asset subtypes"
   - **CRITICAL**: Only extract explicitly stated property subcategories. Do NOT infer specific subcategories from general property type descriptions.

4. **Strategy Research:**
   - Search: "{{COMPANY_NAME}} investment strategies core value-add opportunistic "
   - Search: "{{COMPANY_NAME}} investment approach risk profile"
   - Search: "{{COMPANY_NAME}} investment philosophy strategy"

### 🔍 DEBT CRITERIA SEARCHES
If company offers debt financing, search for:

1. **Loan Programs & Terms:**
   - Search: "{{COMPANY_NAME}} loan programs lending criteria  "
   - Search: "{{COMPANY_NAME}} debt financing loan terms conditions"
   - Search: "{{COMPANY_NAME}} commercial real estate loans"
   - Search: "{{COMPANY_NAME}} bridge loans construction loans permanent financing"
   - Search: "{{COMPANY_NAME}} acquisition loans refinancing loans"
   - Search: "{{COMPANY_NAME}} specific loan terms maturity periods"
   - Search: "{{COMPANY_NAME}} loan duration terms months years"
   - **CRITICAL**: Only extract loan terms that are explicitly stated. Do NOT infer terms from general descriptions.

2. **Financial Parameters:**
   - Search: "{{COMPANY_NAME}} interest rates pricing spreads SOFR Prime "
   - Search: "{{COMPANY_NAME}} loan to value ratio LTV LTC debt service coverage"
   - Search: "{{COMPANY_NAME}} loan sizing parameters leverage ratios"
   - Search: "{{COMPANY_NAME}} current rates pricing  "
   - Search: "{{COMPANY_NAME}} rate spreads margins SOFR Prime WSJ"

3. **Fees & Costs:**
   - Search: "{{COMPANY_NAME}} origination fees exit fees closing costs "
   - Search: "{{COMPANY_NAME}} loan fees processing underwriting costs"
   - Search: "{{COMPANY_NAME}} application fees commitment fees "
   - Search: "{{COMPANY_NAME}} loan costs expenses charges"

4. **Loan Structure:**
   - Search: "{{COMPANY_NAME}} recourse non-recourse loan structures"
   - Search: "{{COMPANY_NAME}} lien position first lien second lien"
   - Search: "{{COMPANY_NAME}} prepayment penalties yield maintenance"
   - Search: "{{COMPANY_NAME}} loan covenants requirements conditions"
   - Search: "{{COMPANY_NAME}} loan terms maturity amortization"

### 🔍 EQUITY CRITERIA SEARCHES
If company offers equity investments, search for:

1. **Return Expectations:**
   - Search: "{{COMPANY_NAME}} target returns IRR equity multiple "
   - Search: "{{COMPANY_NAME}} investment returns yield expectations"
   - Search: "{{COMPANY_NAME}} fund performance target IRR"
   - Search: "{{COMPANY_NAME}} cash on cash returns yield on cost "
   - Search: "{{COMPANY_NAME}} preferred returns hurdle rates"

2. **Investment Structure:**
   - Search: "{{COMPANY_NAME}} hold period investment timeline strategy"
   - Search: "{{COMPANY_NAME}} ownership requirements controlling interest"
   - Search: "{{COMPANY_NAME}} GP equity LP equity preferred equity"
   - Search: "{{COMPANY_NAME}} joint venture requirements partnership structure"
   - Search: "{{COMPANY_NAME}} syndication requirements co-investment"

3. **Investment Parameters:**
   - Search: "{{COMPANY_NAME}} attachment point leverage tolerance capital stack"
   - Search: "{{COMPANY_NAME}} proof of funds requirements closing timeline"
   - Search: "{{COMPANY_NAME}} minimum investment requirements"
   - Search: "{{COMPANY_NAME}} capital requirements equity requirements"
   - Search: "{{COMPANY_NAME}} investment timeline closing process"

## EXTRACTION METHODOLOGY

### STEP 1: CAPITAL POSITION VALIDATION FIRST
1. **CRITICAL**: Execute ALL capital position validation searches FIRST
2. **EVIDENCE-BASED ASSIGNMENT**: Only assign capital positions with clear evidence from web research
3. **NO ASSUMPTIONS**: Do not assume capital position based on company type or industry patterns
4. **VERIFICATION**: Cross-reference capital position information across multiple sources
5. **UNDETECTABLE DEFAULT**: Use "Undetectable" if no clear evidence is found
6. **MULTIPLE POSITIONS**: Identify if company has multiple capital positions

### STEP 2: WEB RESEARCH FOR CRITERIA
1. Execute ALL remaining search queries above
2. Prioritize - information over older data
3. Cross-reference multiple sources for accuracy
4. Note any discrepancies between website and web search results
5. **CRITICAL**: Search for recent news, press releases, and announcements about the company's current offerings

### STEP 2: WEBSITE CONTENT ANALYSIS
Analyze the provided website content for:
- Investment criteria pages
- Fund documents or presentations
- Product/service descriptions
- About us and company information
- News releases or announcements
- **CRITICAL**: Look for any PDFs, presentations, or downloadable materials mentioned
- **CRITICAL**: Check for multiple loan programs or investment products
- **CRITICAL**: Look for regional or property-specific variations in terms

### STEP 3: DATA SYNTHESIS
1. **Source Priority Order**: Always prioritize company website data as the primary source, then real-time web search data
2. **Multiple Capital Positions**: Create separate investment criteria objects for each distinct capital position
3. **Comprehensive Coverage**: Extract every detail found in both sources
4. **CRITICAL**: If multiple loan programs exist, create separate criteria objects for each
5. **CRITICAL**: If terms vary by property type or region, create separate criteria objects
6. **CRITICAL**: Include both specific and typical/standard terms when mentioned
7. **CRITICAL**: Extract ranges as separate minimum and maximum values
8. **INTELLIGENT PLACEHOLDER LOGIC**: When multiple data points exist for the same field:
   - Use the most recent and reliable value as the primary "value"
   - Include ALL sources in the "sources" array in priority order (page_metadata first, then web_scraped_text, then enrichment_data, then company_website, then news_article, then press_release)
   - If conflicting data exists, use the most recent data as primary value
   - Document all conflicting values in the sources array with their respective evidence quotes

## DATA FORMAT REQUIREMENTS

### CRITICAL: PRESERVE ORIGINAL DATA FORMAT
- **CRITICAL**: Extract percentages exactly as stated (e.g., "8.5%", "15% IRR")
- **CRITICAL**: Extract monetary values exactly as stated (e.g., "$12.5 million", "$1.2 billion")
- **CRITICAL**: Extract time periods exactly as stated (e.g., "5 years", "60 months", "3-5 year hold")
- **CRITICAL**: Extract ratios exactly as stated (e.g., "75% LTV", "1.25x DSCR")
- **CRITICAL**: Extract rates exactly as stated (e.g., "SOFR + 3.5%", "Prime + 2.0%")
- **CRITICAL**: Extract fees exactly as stated (e.g., "1% origination fee", "$5,000 application fee")
- **CRITICAL**: Do NOT convert, modify, or standardize any numerical values
- **CRITICAL**: Preserve all units, symbols, and formatting as found in the source

### ALLOWED VALUES

**CRITICAL: You MUST adhere to the following field value rules:**
- **Region:** Use ONLY values from the US_REGIONS list below. Example: "Northeast". Use US_REGIONS_MAP to expand corresponding states automatically.
- **Property Types:** Use ONLY values from the PROPERTY_TYPES list below. Use PROPERTY_TYPE_SUBCATEGORY_MAP to validate subcategories under the chosen types.

**Example**: If a company mentions "Office" properties, also look for and extract relevant subcategories like "Class A Office", "Medical Office", "Creative Office", etc. from the mapping relationships.

Allowed Values:
{{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE}}

## ENHANCED FINANCIAL RESEARCH CONTEXT

### ADDITIONAL SEARCH REQUIREMENTS FOR {{COMPANY_NAME}}:

Please perform the following additional searches and include findings in your analysis:

1. **CURRENT MARKET CONDITIONS :**
   - Search: "{{COMPANY_NAME}} current interest rates  "
   - Search: "{{COMPANY_NAME}} loan pricing market conditions"
   - Search: "{{COMPANY_NAME}} recent loan terms conditions"
   - Search: "{{COMPANY_NAME}} current market rates spreads "

2. **SPECIFIC FINANCIAL PRODUCTS:**
   - Search: "{{COMPANY_NAME}} loan programs products offerings"
   - Search: "{{COMPANY_NAME}} debt financing options"
   - Search: "{{COMPANY_NAME}} equity investment opportunities"
   - Search: "{{COMPANY_NAME}} bridge loans construction loans permanent financing"
   - Search: "{{COMPANY_NAME}} acquisition loans refinancing loans"

3. **RECENT TRANSACTIONS:**
   - Search: "{{COMPANY_NAME}} recent deals transactions "
   - Search: "{{COMPANY_NAME}} closed loans investments"
   - Search: "{{COMPANY_NAME}} portfolio recent activity"
   - Search: "{{COMPANY_NAME}} recent closings  "

4. **COMPETITIVE POSITIONING:**
   - Search: "{{COMPANY_NAME}} competitive rates pricing"
   - Search: "{{COMPANY_NAME}} market position lending"
   - Search: "{{COMPANY_NAME}} industry benchmarks"
   - Search: "{{COMPANY_NAME}} competitive advantages lending"

5. **REGULATORY AND COMPLIANCE:**
   - Search: "{{COMPANY_NAME}} regulatory requirements compliance"
   - Search: "{{COMPANY_NAME}} lending standards guidelines"
   - Search: "{{COMPANY_NAME}} compliance requirements "

6. **ADDITIONAL FINANCIAL METRICS:**
   - Search: "{{COMPANY_NAME}} fees costs charges "
   - Search: "{{COMPANY_NAME}} prepayment penalties yield maintenance"
   - Search: "{{COMPANY_NAME}} loan covenants requirements"
   - Search: "{{COMPANY_NAME}} closing timeline requirements"

**CRITICAL INSTRUCTIONS FOR ENHANCED RESEARCH:**
- Extract ALL financial metrics found in web searches, even if they differ from website content
- Prioritize the most recent information  over older data
- Extract numerical values exactly as stated - do NOT convert or modify them
- If multiple loan programs or investment types exist, extract criteria for each separately
- Include both specific and typical/standard terms when mentioned
- Extract ranges as separate minimum and maximum values
- Look for implied information that can be inferred from context

## ENRICHMENT DATA FOR ANALYSIS
{{ENRICHMENT_CONTEXT}}

## PAGE METADATA FROM WEB CRAWLING ANALYSIS

The following page metadata was extracted from the company's web pages using semantic classification and data extraction:

{{PAGE_METADATA_CONTEXT}}

**CRITICAL INSTRUCTIONS FOR USING PAGE METADATA:**
1. **PRIORITIZE EXTRACTED DATA VALUES**: Use specific amounts, rates, locations, property types, and requirements from the metadata as PRIMARY evidence
2. **UNDERSTAND ENTITY ROLES**: Pay attention to who provides vs. receives (lender vs. borrower, fund vs. investor, etc.)
3. **USE HIGH-CONFIDENCE DATA**: Prioritize data from pages with high classification confidence and reliability ratings
4. **CROSS-REFERENCE WITH ORIGINAL CONTENT**: Use the content samples to understand context and verify extracted values
5. **LEVERAGE CLASSIFICATION CATEGORIES**: Use content categories (loan_products, investment_criteria, fund_information) to organize your extraction
6. **RESPECT DATA RELIABILITY**: Weight evidence based on data reliability scores (high > medium > low)
7. **CITE METADATA SOURCES**: When using data from page metadata, use source_type "page_metadata" and include the specific extracted data values in evidence_quote. Set page_section to the metadata category (e.g., "loan_products", "investment_criteria", "fund_information")

## OVERVIEW SUMMARY (CONDENSED WEBSITE TEXT)
The following high-signal summary has been provided instead of full website text. Use it only as context; you MUST rely on the MAIN URL and RELATED URLS for exact textual evidence:

{{OVERVIEW_SUMMARY}}

## EXTRACTION SCHEMA

**EXAMPLE OF INTELLIGENT PLACEHOLDER LOGIC:**
If you find multiple deal size values:
- Company website says: "Minimum deal size: $5 million"
- Web search says: "Minimum deal size: $3 million" (more recent)
- Use $3 million as the primary value (most recent)
- Include both sources in the sources array with page_metadata first, then web_scraped_text, then enrichment_data, then company_website, then news_article, then press_release

**DETAILED EXAMPLE:**
For minimum_deal_size field:
- Primary value: 3000000 (from most recent source)
- Evidence: "Minimum deal size: $3 million"
- Sources array includes both company website and news article sources
- Page metadata source listed first, then web scraped text source, then enrichment data source, then company website source, then news article source
- All conflicting values documented with their respective evidence quotes

{
  "investmentCriteria": [
    {
      // Deal Scope - Evidence-grounded structure
      "capital_position": {
        "value": string, // valid options [Senior Debt, Stretch Senior, Mezzanine, Preferred Equity, Common Equity, General Partner (GP), Co-GP, Joint Venture (JV), Limited Partner (LP), Third Party, Undetectable]
        "evidence": string, // exact quote supporting this value
        "confidence_score": number, // between 0.0 and 1.0
        "source_url": string, // URL where evidence was found
        "sources": [
          {
            "url": string, // specific URL where evidence was found
            "page_section": string, // specific section of the page (e.g., "About Us", "Investment Criteria", "Products")
            "evidence_quote": string, // exact quote from this source
            "source_type": string, // "company_website", "web_search", "enrichment_data", "news_article", "press_release"
            "date_found": string // date when this information was found (YYYY-MM-DD)
          }
        ]
      },
      "minimum_deal_size": {
        "value": number | null, // in numbers (5 million -> 5000000)
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      "maximum_deal_size": {
        "value": number | null, // in numbers (5 million -> 5000000)
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      
      // Geography - Evidence-grounded structure
      "country": {
        "value": string[] | null,
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      "region": {
        "value": string[] | null, // CRITICAL: ONLY use values from the provided US_REGIONS list
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      "state": {
        "value": string[] | null, // CRITICAL: ONLY use values from the provided US_STATES list
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      "city": {
        "value": string[] | null,
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      
      // Asset Strategy - Evidence-grounded structure
      "property_types": {
        "value": string[] | null, // CRITICAL: ONLY use values from the provided PROPERTY_TYPES list
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      "property_subcategories": {
        "value": string[] | null, // CRITICAL: ONLY use values from the provided PROPERTY_SUBCATEGORIES list
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      "strategies": {
        "value": string[] | null,
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      "decision_making_process": {
        "value": string | null,
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      
      // Additional Info - Evidence-grounded structure
      "notes": {
        "value": string | null,
        "evidence": string | null,
        "confidence_score": number,
        "source_url": string | null,
        "sources": [
          {
            "url": string,
            "page_section": string,
            "evidence_quote": string,
            "source_type": string,
            "date_found": string
          }
        ]
      },
      
      // DEBT-SPECIFIC FIELDS - Evidence-grounded structure (only if capital_position includes debt types)
      "debt_criteria": {
        "closing_time": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null,
          "sources": [
            {
              "url": string,
              "page_section": string,
              "evidence_quote": string,
              "source_type": string,
              "date_found": string
            }
          ]
        },
        "future_facilities": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "eligible_borrower": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "occupancy_requirements": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "lien_position": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "min_loan_dscr": {
          "value": number | null, // CRITICAL GUARDRAIL: Must be 1.0 or greater
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "max_loan_dscr": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "recourse_loan": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_min_debt_yield": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "prepayment": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "yield_maintenance": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "application_deposit": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "good_faith_deposit": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_origination_max_fee": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_origination_min_fee": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_exit_min_fee": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_exit_max_fee": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_interest_rate": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_interest_rate_based_off_sofr": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_interest_rate_based_off_wsj": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_interest_rate_based_off_prime": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_interest_rate_based_off_3yt": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_interest_rate_based_off_5yt": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_interest_rate_based_off_10yt": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_interest_rate_based_off_30yt": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "rate_lock": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "rate_type": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_to_value_max": {
          "value": number | null, // CRITICAL GUARDRAIL: Value must be between 50 and 100
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null,
          "sources": [
            {
              "url": string,
              "page_section": string,
              "evidence_quote": string,
              "source_type": string,
              "date_found": string
            }
          ]
        },
        "loan_to_value_min": {
          "value": number | null, // CRITICAL GUARDRAIL: Value must be between 50 and 100
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_to_cost_min": {
          "value": number | null, // CRITICAL GUARDRAIL: Value must be between 50 and 100
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_to_cost_max": {
          "value": number | null, // CRITICAL GUARDRAIL: Value must be between 50 and 100
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "debt_program_overview": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_type": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_type_normalized": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "structured_loan_tranche": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "loan_program": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "min_loan_term": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "max_loan_term": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "amortization": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        }
      },
      
      // EQUITY-SPECIFIC FIELDS - Evidence-grounded structure (only if capital_position includes equity types)
      "equity_criteria": {
        "target_return": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null,
          "sources": [
            {
              "url": string,
              "page_section": string,
              "evidence_quote": string,
              "source_type": string,
              "date_found": string
            }
          ]
        },
        "minimum_internal_rate_of_return": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "minimum_yield_on_cost": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "minimum_equity_multiple": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "target_cash_on_cash_min": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "min_hold_period_years": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "max_hold_period_years": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "ownership_requirement": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "attachment_point": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "max_leverage_tolerance": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "typical_closing_timeline_days": {
          "value": number | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "proof_of_funds_requirement": {
          "value": boolean | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "equity_program_overview": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        },
        "occupancy_requirements": {
          "value": string | null,
          "evidence": string | null,
          "confidence_score": number,
          "source_url": string | null
        }
      }
    }
  ]
}

## CRITICAL REMINDERS
1. **EVIDENCE-GROUNDING IS MANDATORY**: Every non-null value MUST have exact evidence quote and confidence score
2. **CONFIDENCE SCORING**: Provide confidence scores between 0.0 and 1.0 for all extractions. Only include data with confidence >= 0.9
3. **CAPITAL POSITION VALIDATION FIRST**: Execute capital position searches and validate before any extraction
4. **EVIDENCE-BASED CAPITAL POSITION**: Only assign capital positions with clear evidence from web research
5. **WEB SEARCH SECOND**: Execute remaining searches after capital position validation
6. **DOCUMENT ALL SOURCES**: Include specific URLs, page sections, and document names for every piece of information
7. **APPLY PARSING RULES DURING EXTRACTION**: Use the automatic field population rules while extracting data, not as post-processing
8. **STRUCTURED LOAN TRANCHE DEFAULTS**: Senior Debt positions default to "Whole loan" unless explicitly overridden
9. **LTV IMPUTATION**: "Up to X%" language triggers Min_LTV = 40% imputation
10. **LIEN POSITION AUTO-POPULATION**: Senior Debt → First Lien, Mezzanine → Second Lien
11. **DEVELOPMENT KEYWORD DETECTION**: Add "Opportunistic" strategy when development keywords are found
12. **PROPERTY TYPE WILDCARD EXPANSION**: "All Commercial property types" expands to all commercial types
13. **MULTIPLE SOURCES TRACKING**: For each extracted value, provide a "sources" array with detailed source information including:
   - **url**: Specific URL where evidence was found
   - **page_section**: Specific section of the page (e.g., "About Us", "Investment Criteria", "Products")
   - **evidence_quote**: Exact quote from this source
   - **source_type**: Type of source ("web_scraped_text", "enrichment_data", "company_website", "news_article", "press_release")
   - **date_found**: Date when this information was found (YYYY-MM-DD format)
   - **SOURCE ORDERING**: Always list sources in priority order: web_scraped_text FIRST, then enrichment_data SECOND, then company_website THIRD, then news_article FOURTH, then press_release FIFTH
   - **INTELLIGENT PLACEHOLDERS**: When multiple values exist for the same field, use the most recent and reliable value as the primary value while including all sources
8. **MULTIPLE CAPITAL POSITIONS**: Create separate objects for each capital position
9. **CURRENT DATA**: Prioritize - information
10. **VALID JSON**: Return only the JSON object, no additional text
11. **SOURCE PRIORITY EXTRACTION**: Always extract data in the exact priority order: 1) Web scraped text, 2) Enrichment data, 3) Company website, 4) News articles, 5) Press releases. Only use lower priority sources if higher priority sources are insufficient.
12. **COMPREHENSIVE EXTRACTION**: Extract EVERY detail found in both sources - do NOT skip any information
13. **PRESERVE ORIGINAL FORMAT**: Do NOT convert, modify, or standardize any values - extract exactly as stated
14. **MULTIPLE PROGRAMS**: If multiple loan programs or investment types exist, create separate criteria objects
15. **RANGES AND VARIATIONS**: Extract both minimum and maximum values, include all variations mentioned
16. **NO IDS**: Do not include any ID fields - they will be generated by the database
17. **NO CAPITAL POSITION ASSUMPTIONS**: Do not assume capital position based on company type or industry patterns
18. **UNDETECTABLE DEFAULT**: Use "Undetectable" for capital position if no clear evidence is found
19. **VALID CAPITAL POSITIONS**: Use only the valid capital position options from the mapping table Only options are [Senior Debt, Stretch Senior, Mezzanine, Preferred Equity, Common Equity, General Partner (GP), Co-GP, Joint Venture (JV), Limited Partner (LP), Third Party, Undetectable]
20. **MULTIPLE CAPITAL POSITIONS**: If multiple capital positions are found, create separate objects for each capital position
21. **NO EVIDENCE = NULL VALUE**: If you cannot find explicit evidence for information, set value to null
22. **VERIFY SOURCES**: Cross-reference information across multiple sources when possible
23. **FINANCIAL GUARDRAILS**: Enforce LTV/LTC caps (50-100) and DSCR minimum (1.0) with evidence preservation
24. **DATA NORMALIZATION**: Extract clean numerical values while preserving original format in evidence
25. **DEAL SIZE ACCURACY**: Fund size ≠ Deal size. Annual loan volume ≠ Individual deal size. Only extract actual deal size ranges if explicitly stated
26. **GEOGRAPHIC ACCURACY**: Do NOT infer specific states from regional descriptions. If company says "East Coast," extract "East Coast" not individual states
27. **PROPERTY TYPE ACCURACY**: Do NOT infer specific subcategories from general property type descriptions. Only extract explicitly stated subcategories
28. **LOAN TERM ACCURACY**: Only extract loan terms that are explicitly stated. Do NOT infer terms from general descriptions
29. **ENHANCED SEARCH REQUIRED**: Perform comprehensive web searches with multiple variations and synonyms
30. **SOURCE VALIDATION**: Verify all information across multiple sources before extraction
31. **CURRENT DATA PRIORITY**: Always prioritize the most recent information  over older data
32. **COMPETITIVE ANALYSIS**: Search for competitor comparisons to find additional criteria information
33. **MULTIPLE SOURCE EVIDENCE**: When information comes from multiple sources, include ALL sources in the sources array
34. **SOURCE TYPE CLASSIFICATION**: Properly classify source types to distinguish between company website, web search results, enrichment data, news articles, and press releases
35. **INTELLIGENT PLACEHOLDER LOGIC**: When multiple data points exist for the same field:
    - Use the most recent and reliable value as the primary "value"
    - Include ALL sources in the "sources" array
    - Prioritize company website data over web search data
    - Use web search data to validate and supplement company website data
    - If conflicting data exists, use the most recent data as primary value
    - Document all conflicting values in the sources array with their respective evidence quotes

**START YOUR RESPONSE WITH { AND END WITH }. NOTHING ELSE.**`;

// Interface for dynamic mappings
interface MappingData {
  [key: string]: string[]
}

// Enhanced template function with better error handling and validation
export const COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION = (company: {
  company_name: string
  company_website: string
  industry?: string
}, mappings: MappingData = {}, enrichmentData: any = null, relatedUrls: string[] = [], overviewSummary: string = '', overviewSeedSources: string[] = [], pageMetadataContext: string = '') => {
  
  // Enhanced allowed values builder with validation and nested property structure
  const buildAllowedValues = (mappings: MappingData) => {
    const allowedValues: Record<string, string[]> = {};
    
    // Enhanced type mapping with validation
    const typeMapping = {
      // Expecting consolidated keys already transformed in processor
      'PROPERTY_TYPES': 'PROPERTY_TYPES',
      'STRATEGIES': 'STRATEGIES',
      'LOAN_PROGRAMS': 'LOAN_PROGRAMS',
      'LOAN_TYPES': 'LOAN_TYPES',
      'STRUCTURED_LOAN_TRANCHES': 'STRUCTURED_LOAN_TRANCHES',
      'RECOURSE_LOAN_TYPES': 'RECOURSE_LOAN_TYPES',
      'US_REGIONS': 'US_REGIONS',
      'PROPERTY_TYPE_SUBCATEGORY_MAP': 'PROPERTY_TYPE_SUBCATEGORY_MAP',
      'US_REGIONS_MAP': 'US_REGIONS_MAP'
    } as Record<string, string>;
    
    // Populate allowed values with validation
    for (const [dbType, jsonKey] of Object.entries(typeMapping)) {
      if ((mappings as any)[dbType] && Array.isArray((mappings as any)[dbType]) && (mappings as any)[dbType].length > 0) {
        const filteredValues = (mappings as any)[dbType].filter((item: any) => 
          item && typeof item === 'string' && item.trim() !== ''
        );
        if (filteredValues.length > 0) {
          allowedValues[jsonKey] = filteredValues;
        }
      }
    }
    
    // Ensure all required mapping types exist with at least empty arrays
    const fallbackTypes = [
      'PROPERTY_TYPES', 'STRATEGIES', 'LOAN_PROGRAMS', 
      'LOAN_TYPES', 'STRUCTURED_LOAN_TRANCHES', 'RECOURSE_LOAN_TYPES', 
      'US_REGIONS', 'PROPERTY_TYPE_SUBCATEGORY_MAP', 'US_REGIONS_MAP'
    ];
    
    fallbackTypes.forEach(type => {
      if (!allowedValues[type]) {
        allowedValues[type] = [];
      }
    });
    
    return JSON.stringify(allowedValues, null, 2);
  };
  
  const allowedValuesJson = buildAllowedValues(mappings);
  
  // Build enrichment data context if available
  let enrichmentContext = '';
  if (enrichmentData) {
    // Add website content if available
    if (enrichmentData.website_content) {
      enrichmentContext += `\n\n## WEBSITE CONTENT (HIGHEST PRIORITY SOURCE)
The following content was extracted from the company's website:

${enrichmentData.website_content}

**CRITICAL INSTRUCTION**: This website content represents web scraped text and should be treated as your HIGHEST PRIORITY source (Priority 1). Extract investment criteria from this content FIRST before considering any other data sources. This content takes absolute precedence over enrichment data, news articles, and press releases.`;
    }

    enrichmentContext += `\n\n## ENRICHMENT DATA (SECOND PRIORITY SOURCE)
The following structured data is available as enrichment data (Priority 2). Use this data when website content does not provide sufficient information for specific fields.`;

    const enrichmentFields = [
      { key: 'company_type', label: 'Company Type' },
      { key: 'business_model', label: 'Business Model' },
      { key: 'investment_strategy_mission', label: 'Investment Strategy Mission' },
      { key: 'investment_strategy_approach', label: 'Investment Strategy Approach' },
      { key: 'fund_size', label: 'Fund Size' },
      { key: 'aum', label: 'AUM' },
      { key: 'investment_focus', label: 'Investment Focus' },
      { key: 'lender_type', label: 'Lender Type' },
      { key: 'annual_loan_volume', label: 'Annual Loan Volume' },
      { key: 'fund_size_active_fund', label: 'Active Fund Size' },
      { key: 'fundraising_status', label: 'Fundraising Status' },
      { key: 'portfolio_health', label: 'Portfolio Health' },
      { key: 'partnerships', label: 'Partnerships' },
      { key: 'key_executives', label: 'Key Executives' },
      { key: 'products_services_description', label: 'Products/Services' },
      { key: 'target_customer_profile', label: 'Target Customer Profile' },
      { key: 'company_history', label: 'Company History' },
      { key: 'transactions_completed_last_12m', label: 'Transactions Last 12M' },
      { key: 'total_transaction_volume_ytd', label: 'Transaction Volume YTD' },
      { key: 'average_deal_size', label: 'Average Deal Size' },
      { key: 'portfolio_size_sqft', label: 'Portfolio Size (sqft)' },
      { key: 'portfolio_asset_count', label: 'Portfolio Asset Count' }
    ];
    
    const enrichmentInfo = enrichmentFields
      .map(field => {
        const value = enrichmentData[field.key];
        if (value && value !== '' && value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            return value.length > 0 ? `${field.label}: ${value.join(', ')}` : null;
          }
          return `${field.label}: ${value}`;
        }
        return null;
      })
      .filter(Boolean)
      .join('\n');
    
    if (enrichmentInfo) {
      enrichmentContext = `\n\n## ENRICHMENT DATA FROM COMPANY OVERVIEW
The following information was previously extracted from this company's website and other sources:

${enrichmentInfo}

**IMPORTANT**: Use this enrichment data to enhance your investment criteria extraction. This data provides additional context about the company's business model, investment focus, and financial metrics that should inform your extraction of investment criteria.`;
    }
  }
  
  // Enhanced template replacement with validation
  const relatedUrlsBlock = Array.isArray(relatedUrls) && relatedUrls.length > 0 ? JSON.stringify(relatedUrls, null, 2) : '[]'
  const overviewSeedSourcesBlock = Array.isArray(overviewSeedSources) && overviewSeedSources.length > 0 ? JSON.stringify(overviewSeedSources, null, 2) : '[]'

  return COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE
    .replace(/\{\{WEBSITE_URL\}\}/g, company.company_website || 'No website URL provided')
    .replace(/\{\{COMPANY_NAME\}\}/g, company.company_name || 'Unknown Company')
    .replace(/\{\{\DYNAMIC_VALUES_WILL_BE_INSERTED_HERE\}\}/, allowedValuesJson)
    .replace(/\{\{ENRICHMENT_CONTEXT\}\}/g, enrichmentContext)
    .replace(/\{\{OVERVIEW_SEED_SOURCES\}\}/g, overviewSeedSourcesBlock)
    .replace(/\{\{RELATED_URLS\}\}/g, relatedUrlsBlock)
    .replace(/\{\{OVERVIEW_SUMMARY\}\}/g, overviewSummary || 'No summary provided')
    .replace(/\{\{PAGE_METADATA_CONTEXT\}\}/g, pageMetadataContext || 'No page metadata available')
}
