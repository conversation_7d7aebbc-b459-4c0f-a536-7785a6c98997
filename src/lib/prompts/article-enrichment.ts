export const ARTICLE_ENRICHMENT_SYSTEM_PROMPT = `You are ArticleAnalyzer-GP<PERSON>, an AI assistant specialized in extracting comprehensive structured information from real estate news articles. Your task is to extract EVERY POSSIBLE DETAIL about properties, market metrics, transactions, and entities from news articles and return them in a structured JSON format.

**CRITICAL INSTRUCTIONS:**
- Extract ALL entities mentioned in the article content
- Extract ALL market metrics, trends, and conditions mentioned in the article
- Focus on extracting accurate names, roles, and descriptions from the article text
- Focus on extracting accurate market data, statistics, transactions, property data, financinal data, and trends from the article text
- Do not perform web searches - only extract information present in the article
- Be thorough in identifying companies, contacts, deals, transactions and properties mentioned
- Be thorough in identifying market conditions, property data, financial data, trends, and statistics mentioned

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- If information for a field isn't available, use null for that field.
- For monetary values, convert to millions format (e.g., $5M becomes 5, $2.3B becomes 2300).
- For percentages, convert to decimal format (e.g., 15% becomes 0.15).
- For lists, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified.
- DO NOT make up information or use placeholders.
- DO NOT use single letters or placeholder values like "S", "F", "U", "2" - extract actual market data or leave fields null.
- If no specific market data is found, leave market metrics fields as null rather than using placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs).
- BE EXTREMELY THOROUGH - extract every detail you can find, even if it seems minor.
- **MULTIPLE PROPERTIES/DEALS/MARKETS**: If an article mentions multiple properties, deals, or markets, create separate objects for each distinct item.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`

export const ARTICLE_ENRICHMENT_USER_TEMPLATE = `Extract EVERY POSSIBLE detail from this real estate news article using:
1. The provided news article content below
2. Focus on extracting accurate information present in the article text

**ENTITY EXTRACTION GUIDELINES:**
- Extract ALL companies, contacts, deals, properties, property data and financial data, mentioned in the article
- Identify the role of each entity in the context of the article
- Be thorough in capturing names, titles, and relationships
- Do not perform web searches - only extract information from the article content

**RELEVANCE ASSESSMENT:**
- Set "isRelevant" to true if the article contains real estate market information, property details, transactions, or industry insights
- Set "isRelevant" to false if the article is completely unrelated to real estate (e.g., general news, sports, entertainment)
- Most real estate news articles should be marked as relevant (true)

COMPREHENSIVE EXTRACTION SCHEMA:
{
  "publicationDate": string,        // Date of the news article in YYYY-MM-DD format
  "author": string,                 // Author of the news article
  "summary": string,                // AI-generated summary of the article
  "topic": string,                  // Primary topic (Market Report, Company News, Individual Deal)
  "marketTrendTags": [string],      // Keywords identifying market trends
  "isDistressed": boolean,          // Indicator for distressed debt or workout situation
  "isRelevant": boolean,            // Whether this article is relevant to real estate analysis (default: true)
  "sentiment": string,              // Market sentiment (Positive, Neutral, Negative)
  "sentimentSummary": string,       // Summary of market sentiment
  "keyTakeaways": string,           // Key takeaways from the article
  "llmTags": [string],             // LLM-extracted meta labels
  "quotesLlmTags": [               // Attributed quotes extracted from article
    {
      "source": string,            // Quote source
      "role": string,              // Source role
      "statement": string,         // Quote statement
      "context": string            // Quote context
    }
  ],
  "sourceConfidence": number,      // Confidence score for extraction accuracy (0.00 to 1.00)
  "extractionNotes": string,       // Logging, notes, feedback related to parsing

  // Property Information
  "properties": [
    {
      "propertyName": [string],      // Official or unofficial name of building/development
      "address": [string],           // Specific street address if disclosed
      "state": [string],             // U.S. state where property is located
      "city": [string],              // City where property is located
      "zipcode": [string],           // ZIP code of the property
      "region": [string],            // Granular neighborhood/region location
      "country": [string],           // Country (default USA)
      "squareFootage": number,       // Size of building/project/lease in square feet
      "unitCount": number,           // Number of units for multifamily assets
      "constructionType": [string],  // Type of construction (New Development, Adaptive Reuse, etc.)
      "projectTimeline": string,     // Projected completion date or development phases
      "jobCreation": number,         // Number of jobs mentioned as being created
      "subsidyInfo": string          // Details on tax abatements, TIFs, PILOTs, etc.
    }
  ],

  // Market Metrics
  "marketMetrics": [
    {
      "marketCity": [string],        // Market discussed (NYC, Chicago, etc.)
      "marketState": [string],       // State for the market
      "marketCountry": [string],     // Country for the market
      "timePeriod": [string],        // Time period covered by report
      "vacancyRate": number,         // Vacancy rate as decimal
      "rentalRate": number,          // Asking or effective rental rate
      "absorptionRate": number,      // Net change in occupied space
      "transactionVolume": number,   // Total transaction volume
      "constructionPipeline": number, // Volume of active development
      "newDeliveriesSf": number,     // New space delivered in current period
      "rentalRateTrend": [string],   // Direction of rental rates (Rising, Flat, Declining)
      "capRateAvg": number,          // Average cap rate as decimal
      "capRateTrend": [string],      // Direction of cap rates (Compressing, Expanding, Stable)
      "demandTrend": [string],       // Trend for market demand (Strong, Weak, Recovering)
      "commentary": string,          // Summary of qualitative insights
      "remoteWorkImpact": string,    // Commentary on remote work impact
      "distressIndicator": boolean   // Indicator of rising distress trend
    }
  ],

  // Transaction Information
  "transactions": [
    {
      "dealType": [string],          // Type of deal (Acquisition, Refinancing, etc.)
      "dealSize": string,            // Deal size as millions of dollars with no $ sign and only numbers
      "capRate": string,             // Cap rate as mentioned in article
      "pricePerSf": number,          // Price per square foot for transaction
      "loanType": [string],          // Type of loan instrument (Bridge, Construction, etc.)
      "equityType": [string],        // Type of equity (Common Equity, Preferred Equity, etc.)
      "financingType": [string],     // Type of financing used in deal
      "capitalStackNotes": [string], // Description of financing stack and components
      "capitalPosition": [string]    // Capital position in the transaction (e.g., Check the Capital Position section for allowed values)
      "propertyTypes": [string]      // Property type in the transaction (e.g., Check the Property Types section for allowed values)
    }
  ],

  // Entity Information - NEW SECTION
  "entities": [
    {
      "entityType": string,          // Type: "company", "contact", "deal", "property"
      "entityName": string,          // Full name of the entity
      "entityRole": string,          // Role in the article (Buyer, Seller, Lender, Developer, etc.)
      "entityDescription": string,   // Brief description or context
      "confidence": number,          // Confidence in entity identification (0.0 to 1.0)
      "webSearchVerified": boolean,  // Whether web search confirmed this entity
      "additionalContext": string,    // Any additional context from web search
      "entityData": object          // Extensive data about the entity
    }
  ]
}

**EXTRACTION GUIDELINES:**
1. **Be Extremely Thorough**: Read through ALL content carefully and extract every detail
2. **CONVERT NUMERICAL VALUES TO PROPER FORMAT**:
   **Deal Sizes**: Keep as text strings as they appear in the article
   **Percentages (to decimals)**: Convert all percentages to decimal format:
   - 15% becomes 0.15 (not 15)
   - 6.5% cap rate becomes 0.065 (not 6.5)
   - 85% LTV becomes 0.85 (not 85)
3. **Multiple Items**: If article mentions multiple distinct properties, deals, or entities, create separate objects for each
4. **Location Extraction**: Extract ALL location mentions, from specific addresses to broad market areas
5. **Company and People**: Extract ALL companies and people mentioned, even if they play minor roles
6. **Market Intelligence**: Extract any market data, trends, conditions,property data, financing data, or competitive intelligence mentioned
7. **Grouped Information**: Group related information logically
8. **Deal Sheet Processing**: For transaction articles and deal sheets, extract market-level insights from the collection of deals

**ADDRESS EXTRACTION GUIDELINES:**
- **CRITICAL**: Extract the address for each property mentioned in the article
- **CRITICAL**: Extract the city full name for each property mentioned in the article
- **CRITICAL**: Extract the state full name for each property mentioned in the article
- **CRITICAL**: Extract the zipcode for each property mentioned in the article
- **CRITICAL**: Extract the country full name for each property mentioned in the article
- **CRITICAL**: Extract the region for each property mentioned in the article
- **CRITICAL**: Extract the country full name for each property mentioned in the article

**MARKET METRICS EXTRACTION GUIDELINES:**
- **Market Location**: Extract the specific market/city/region being discussed (e.g., "South Florida", "Miami", "Boca Raton", "West Palm Beach")
- **Market Data**: Look for any market statistics, trends, or conditions mentioned:
  - Vacancy rates (convert percentages to decimals: 5.2% → 0.052)
  - Rental rates (in dollars per square foot)
  - Absorption rates (net change in occupied space)
  - Transaction volumes (total deal volume in the market)
  - Construction pipeline (active development volume)
  - New deliveries (space delivered in current period)
  - Cap rates (convert percentages to decimals: 6.5% → 0.065)
- **Market Trends**: Identify trends mentioned (Rising, Flat, Declining, Strong, Weak, Recovering, Compressing, Expanding, Stable)
- **Market Commentary**: Extract any qualitative insights about market conditions
- **Time Periods**: Note any specific time periods mentioned (Q1 2024, 2023, etc.)
- **Distress Indicators**: Look for mentions of distress, defaults, or market challenges

**EXAMPLES OF MARKET DATA TO EXTRACT:**
- "South Florida market shows strong demand" → marketCity: ["South Florida"], demandTrend: ["Strong"]
- "Vacancy rates at 4.2%" → vacancyRate: 0.042
- "Rental rates averaging $2.50 per square foot" → rentalRate: 2.50
- "Transaction volume reached $500M in Q1" → transactionVolume: 500000000, timePeriod: ["Q1"]
- "Cap rates compressing to 5.8%" → capRateAvg: 0.058, capRateTrend: ["Compressing"]
- "Construction pipeline of $2.3B" → constructionPipeline: **********
- "Market commentary: Active multifamily acquisitions indicate robust market" → commentary: "Active multifamily acquisitions indicate robust market"

**DEAL SHEET MARKET DATA EXTRACTION:**
- From transaction articles, extract the market being discussed (e.g., "South Florida Deal Sheet" → marketCity: ["South Florida"])
- Sum up transaction volumes from all deals mentioned to get total market activity
- Extract rental rates mentioned in property descriptions
- Note construction activity and development pipeline
- Extract market commentary about overall market conditions
- Identify market trends from the types of deals (acquisitions, financing, development)
- Look at article titles and headers for market context (e.g., "South Florida Deal Sheet" indicates South Florida market)
- Extract market insights from the overall pattern of deals and transactions

**CLASSIFICATION CAPITAL POSITION RULES:**
- **Senior Debt**: The most secure layer in the CRE capital stack, representing a first-position mortgage or loan secured by the property itself. It has the lowest risk and is repaid first in case of default or sale.
- **Stretch Senior**: A type of senior debt that "stretches" to provide higher loan-to-value (LTV)/loan-to-cost (LTC) ratios by blending traditional senior debt with elements of subordinated debt in a single loan package. It's still senior but offers more leverage than standard senior debt, often used for acquisitions or refinancings where borrowers need higher proceeds.
- **Mezzanine**: A hybrid financing layer subordinate to senior debt but senior to equity, often taking the form of unsecured or junior lien debt. It bridges the gap between senior debt and equity, with repayment after senior debt but before equity holders.
- **Preferred Equity**: An equity investment positioned between all debt (senior and mezzanine) and common equity. It has priority over common equity for distributions and repayment, receiving a fixed, pre-negotiated dividend-like payment known as a preferred return. While it is an equity position, it behaves similarly to debt due to its fixed return, but it does not have the foreclosure rights of a lender. It's used to fill the final gap in financing when senior and mezzanine debt are insufficient.
- **Common Equity**: The junior-most layer in the CRE capital stack, representing ownership interest with the highest risk and potential reward. Holders have residual claims after all debt and preferred equity are repaid, sharing in profits/losses proportionally.
- **General Partner (GP)**: The active managing partner in a CRE partnership or syndication, responsible for operations, decision-making, and often contributing a co-investment. GPs have unlimited liability and receive a promote (disproportionate share of profits) after hurdles are met.
- **Co-GP**: A co-general partner structure where the sponsor (GP) invests alongside limited partners in the same equity entity, aligning interests through shared risk/reward. It's a form of GP co-investment, often used in syndications for transparency.
- **Joint Venture (JV)**: A collaborative equity arrangement between two or more parties (e.g., sponsor and investor) for a specific CRE project, sharing ownership, risk, and returns. JVs are typically structured as separate legal entities with defined roles and responsibilities for each partner.
- **Limited Partner (LP)**: Passive investors in a CRE partnership who provide the majority of equity capital with limited liability (only up to their investment). They have no management control but receive preferred returns before GP promotes.
- **Third Party**: An individual, firm, or entity working in Commercial Real Estate (CRE) but not in an investment, ownership, or primary financing capacity. Instead, they provide external support services, intermediary functions, or advisory roles to facilitate transactions, operations, or compliance, often as independent contractors or outsourced specialists.
- **Undetectable**: Insufficient information to detect the capital position from the article content.

**CAPITAL POSITION EXTRACTION GUIDELINES:**
- **CRITICAL**: Extract the capital position for each transaction mentioned in the article
- **CRITICAL**: Extract the property type for each transaction mentioned in the article
- **CRITICAL**: Do comprehensive web search for the property type and capital position for the transaction


**ENTITY EXTRACTION GUIDELINES:**
- **Companies**: Extract all company names mentioned (buyers, sellers, lenders, developers, brokers, etc.)
- **Contacts**: Extract all people mentioned (executives, brokers, lawyers, government officials, etc.)
- **Deals**: Extract specific deal names or transaction identifiers if mentioned
- **Properties**: Extract property names, addresses, and development projects
- **Roles**: Be specific about each entity's role (e.g., "Buyer", "Seller", "Lender", "Developer", "Broker", "CEO", "Managing Director")
- **Focus on Article Content**: Extract information directly from the article text

**EXTENSIVE ENTITY DATA EXTRACTION GUIDELINES:**
- DO EXTENSIVELY WEB SEARCH FOR THE ENTITY DATA
- **Contacts**: Extract email, LinkedIn URL, company, title, phone, location
- **Companies**: Extract website, industry, company type, headquarters, founding year, size, description, services, specializations, markets, leadership
- **Entity Data Structure**: The entityData field should contain a comprehensive object with all available information about the entity, including:
  - For Companies: website, industry, company_type, headquarters, founding_year, size, description, services, specializations, markets, leadership, financial_data, recent_news
  - For Contacts: email, linkedin_url, company, title, phone, location, education, experience, social_media_profiles
  - For Properties: property_details, ownership_history, development_status, zoning_info, environmental_data
  - For Deals: deal_structure, participants, timeline, financial_terms, regulatory_approvals

**Article Information:**
Title: {{ARTICLE_TITLE}}
Content: {{ARTICLE_TEXT}}
Source: {{ARTICLE_SOURCE}}
URL: {{ARTICLE_URL}}

Allowed Values (from central_mapping table):
{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE}

**ENTITY DATA EXAMPLES:**
- **Company Entity**: 
  {
    "entityType": "company",
    "entityName": "Blackstone Group",
    "entityRole": "Buyer",
    "entityDescription": "Global investment firm acquiring the property",
    "confidence": 0.95,
    "webSearchVerified": true,
    "additionalContext": "Verified through company website and news sources",
    "entityData": {
      "website": "https://www.blackstone.com",
      "industry": "Private Equity",
      "company_type": "Investment Firm",
      "headquarters": "New York, NY",
      "founding_year": 1985,
      "size": "Global",
      "description": "Leading global investment business",
      "services": ["Private Equity", "Real Estate", "Hedge Funds"],
      "specializations": ["Commercial Real Estate", "Infrastructure"],
      "markets": ["North America", "Europe", "Asia"],
      "leadership": ["Stephen A. Schwarzman", "Jonathan Gray"]
    }
  }
  - **Contact Entity**: 
  {
    "entityType": "contact",
    "entityName": "John Doe",
    "entityRole": "Buyer",
    "entityDescription": "Global investment firm acquiring the property",
    "confidence": 0.95,
    "webSearchVerified": true,
    "additionalContext": "Verified through company website and news sources",
    "entityData": {
      "email": "<EMAIL>",
      "company": "Blackstone Group",
      "title": "Senior Investment Manager",
      "phone": "******-555-1234",
      "location": "New York, NY",
      "education": ["Harvard University", "Columbia University"],
      "experience": ["Blackstone Group", "Morgan Stanley"],
    }
  }

**FINAL REMINDER: Extract ALL entities, properties, transactions, and market metrics mentioned in the article content. Your response MUST be a single valid JSON object and nothing else. DO NOT include markdown, code blocks, or any explanation. BE EXTREMELY THOROUGH in your extraction - every detail matters! For multiple properties/deals/entities/markets, create separate objects! Pay special attention to market data, trends, and conditions mentioned in the article! Assess article relevance and set isRelevant appropriately!**`

// Interface for dynamic mappings
interface MappingData {
  [key: string]: string[]
}

// Template function with dynamic mappings from central_mapping table
export const ARTICLE_ENRICHMENT_USER_TEMPLATE_FUNCTION = (article: {
  source?: string
  title?: string
  text?: string
  url?: string
}, mappings: MappingData = {}) => {

  // Build allowed values section dynamically from mappings
  const buildAllowedValues = (mappings: MappingData) => {
    const allowedValues: Record<string, string[]> = {};

    // Map the database types to the JSON schema field names
    const typeMapping = {
      'Property Type': 'PROPERTY_TYPES',
      'Strategies': 'STRATEGIES',
      'Deal Type': 'DEAL_TYPES',
      'Capital Position': 'CAPITAL_POSITION',
      'U.S Regions': 'US_REGIONS',
      'Company Type': 'COMPANY_TYPES'
    };

    // Populate allowed values from mappings
    for (const [dbType, jsonKey] of Object.entries(typeMapping)) {
      if (mappings[dbType] && mappings[dbType].length > 0) {
        allowedValues[jsonKey] = mappings[dbType];
      }
    }

    // Add property subcategories from subcategory mappings
    const propertySubcategories: string[] = [];
    for (const [key, values] of Object.entries(mappings)) {
      if (key.includes('Property Type -') && key.includes('Subproperty Type')) {
        propertySubcategories.push(...values);
      }
    }
    if (propertySubcategories.length > 0) {
      allowedValues['PROPERTY_SUBCATEGORIES'] = propertySubcategories;
    } else if (mappings['Property Type']) {
      // Fallback to property types if no subcategories found
      allowedValues['PROPERTY_SUBCATEGORIES'] = mappings['Property Type'];
    }

    // Fallback to empty arrays if no mappings available
    const fallbackTypes = [
      'PROPERTY_TYPES', 'STRATEGIES', 'DEAL_TYPES',
      'CAPITAL_POSITION', 'US_REGIONS', 'COMPANY_TYPES', 'PROPERTY_SUBCATEGORIES'
    ];

    fallbackTypes.forEach(type => {
      if (!allowedValues[type]) {
        allowedValues[type] = [];
      }
    });

    // Return formatted JSON string for the template
    return JSON.stringify(allowedValues, null, 2);
  };

  const allowedValuesJson = buildAllowedValues(mappings);

  return ARTICLE_ENRICHMENT_USER_TEMPLATE
    .replace(/\{\{ARTICLE_TITLE\}\}/g, article.title || 'Unknown')
    .replace(/\{\{ARTICLE_TEXT\}\}/g, article.text || 'Unknown')
    .replace(/\{\{ARTICLE_SOURCE\}\}/g, article.source || 'Unknown')
    .replace(/\{\{ARTICLE_URL\}\}/g, article.url || 'Unknown')
    .replace(/\{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE\}/, allowedValuesJson)
}
