import { 
  PageMetadata, 
  PageContentCategory, 
  EntityFocus, 
  BusinessFunction,
  DataReliability,
  FinancialDataPresence,
  InvestmentCriteriaPresence,
  ExtractedDataValues,
  ClassificationContext
} from '../../types/page-metadata';

/**
 * Page Metadata Classifier
 * 
 * Uses LLM to intelligently classify scraped pages into semantic categories
 * to prevent data loss and misattribution when combining content.
 */

export class PageMetadataClassifier {
  private llmProvider: any;
  private version: string = '1.0.0';

  constructor(llmProvider: any) {
    this.llmProvider = llmProvider;
  }

  /**
   * Classify a single page and generate comprehensive metadata
   */
  async classifyPage(context: ClassificationContext): Promise<string> {
    console.log('\n🧠 MULTIMODAL METADATA CLASSIFIER DEBUG:');
    console.log('📄 URL:', context.url);
    console.log('🏢 Company:', context.company_name);
    console.log('📝 Extracted Text Length:', context.content.length);
    console.log('📸 Screenshot Available:', !!context.screenshot);
    if (context.screenshot) {
      console.log('📸 Screenshot Size:', context.screenshot.length, 'bytes');
    }
    
    // Use multimodal approach if screenshot is available
    if (context.screenshot && this.llmProvider.callLLMWithFiles) {
      console.log('🌐 Using GEMINI DIRECT SCRAPING: URL + Screenshot + Reference Text');
      return this.classifyPageWithScreenshot(context);
    } else {
      console.log('📝 Using TEXT-ONLY classification (no screenshot available)');
      return this.classifyPageTextOnly(context);
    }
  }
  
  /**
   * Classify page using screenshot + URL + extracted text
   */
  private async classifyPageWithScreenshot(context: ClassificationContext): Promise<PageMetadata> {
    const prompt = this.buildScreenshotClassificationPrompt(context);
    
    // Create multimodal message with screenshot
    const messages = [
      {
        role: 'system',
        content: this.getScreenshotSystemPrompt()
      },
      {
        role: 'user',
        parts: [
          prompt,
          {
            fileBuffer: context.screenshot!,
            mimeType: 'image/png'
          }
        ]
      }
    ];

    try {
      console.log('🚀 Calling Gemini to scrape URL + analyze screenshot...');
      const response = await this.llmProvider.callLLMWithFiles(messages, {
        temperature: 0.1,
        maxTokens: 4000,
        model: 'gemini-2.5-flash'
      });

      console.log('✅ Multimodal LLM Response Received:');
      console.log('📏 Response Length:', response.content.length);
      console.log('🔝 Response Preview (first 300 chars):');
      console.log('---START RESPONSE---');
      console.log(response.content.substring(0, 300));
      console.log('---END RESPONSE---');

      // Just return the raw YAML response - no parsing needed
      return response.content;
    } catch (error) {
      console.error('❌ Error in multimodal classification:', error);
      console.log('🔄 Falling back to text-only classification...');
      return this.classifyPageTextOnly(context);
    }
  }
  
  /**
   * Classify page using multimodal approach (screenshot + HTML + text)
   */
  private async classifyPageMultimodal(context: ClassificationContext): Promise<PageMetadata> {
    console.log('🎨 Using MULTIMODAL classification with screenshot + HTML + text');
    
    const prompt = this.buildScreenshotClassificationPrompt(context);
    
    // Create multimodal message with screenshot
    const messages = [
      {
        role: 'system',
        content: this.getMultimodalSystemPrompt()
      },
      {
        role: 'user',
        parts: [
          prompt,
          {
            fileBuffer: context.screenshot!,
            mimeType: 'image/png'
          }
        ]
      }
    ];

    try {
      console.log('🚀 Calling Gemini with multimodal input...');
      const response = await this.llmProvider.callLLMWithFiles(messages, {
        temperature: 0.1,
        maxTokens: 4000,
        model: 'gemini-2.5-flash'
      });

      console.log('✅ Multimodal LLM Response Received:');
      console.log('📏 Response Length:', response.content.length);
      console.log('🔝 Response Preview (first 300 chars):');
      console.log('---START RESPONSE---');
      console.log(response.content.substring(0, 300));
      console.log('---END RESPONSE---');

      const classification = this.parseClassificationResponse(response.content);
      
      return {
        content_category: classification.content_category || 'other',
        entity_focus: classification.entity_focus || 'unclear',
        business_function: classification.business_function || 'unclear',
        data_reliability: classification.data_reliability || 'low',
        content_freshness: classification.content_freshness || 'unclear',
        content_specificity: classification.content_specificity || 'vague',
        financial_data: classification.financial_data || {
          loan_amounts: false,
          interest_rates: false,
          investment_amounts: false,
          returns_performance: false,
          fund_sizes: false,
          property_values: false,
          fees_costs: false,
          financial_ratios: false
        },
        investment_criteria: classification.investment_criteria || {
          deal_size_range: false,
          property_types: false,
          geographic_focus: false,
          investment_strategy: false,
          hold_period: false,
          return_targets: false,
          leverage_preferences: false,
          sector_focus: false
        },
        extracted_data: classification.extracted_data || {},
        key_entities_mentioned: classification.key_entities_mentioned || [],
        primary_topics: classification.primary_topics || [],
        classification_confidence: classification.classification_confidence || 0.1,
        classification_notes: classification.classification_notes || 'Multimodal classification with screenshot analysis',
        potential_conflicts: classification.potential_conflicts || [],
        classified_at: new Date(),
        classifier_version: this.version
      };
    } catch (error) {
      console.error('❌ Error in multimodal classification:', error);
      console.log('🔄 Falling back to text-only classification...');
      return this.classifyPageTextOnly(context);
    }
  }
  
  /**
   * Fallback text-only classification
   */
  private async classifyPageTextOnly(context: ClassificationContext): Promise<PageMetadata> {
    console.log('📝 Using TEXT-ONLY classification');
    
    const prompt = this.buildClassificationPrompt(context);
    
    // Extract the actual content that will be sent to LLM
    const contentMatch = prompt.match(/CONTENT TO ANALYZE[\s\S]*?(?=\n\nProvide a YAML response)/);
    const actualContentLength = contentMatch ? contentMatch[0].length : 0;
    
    console.log('🎯 Calling LLM with Gemini 2.5 Flash...');
    console.log('📝 Full Prompt Length:', prompt.length);
    console.log('📄 Actual Content Sent to LLM:', actualContentLength, 'chars');
    console.log('⚠️  Content Truncated:', context.content.length > 12000 ? 'YES' : 'NO');
    
    const messages = [
      { role: 'system', content: this.getSystemPrompt() },
      { role: 'user', content: prompt }
    ];

    try {
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.1, // Low temperature for consistent classification
        maxTokens: 3000,
        model: 'gemini-2.5-flash' // Use Gemini 2.5 Flash for fast classification
      });

      console.log('✅ LLM Response Received:');
      console.log('📏 Response Length:', response.content.length);
      console.log('🔝 Response Preview (first 300 chars):');
      console.log('---START RESPONSE---');
      console.log(response.content.substring(0, 300));
      console.log('---END RESPONSE---');

      // Just return the raw YAML response - no parsing needed
      return response.content;
    } catch (error) {
      console.error('Error classifying page:', error);
      return this.createFallbackMetadata();
    }
  }

  /**
   * System prompt for URL scraping + screenshot analysis
   */
  private getScreenshotSystemPrompt(): string {
    return `You are an expert multimodal financial data extraction and classification system specializing in real estate and investment company web pages.

You have the ability to scrape web pages directly and will analyze them using THREE sources:
1. 🌐 DIRECT WEB SCRAPING: Live page content from the provided URL
2. 📸 FULL PAGE SCREENSHOT: Visual layout, design, and content organization  
3. 📝 REFERENCE TEXT: Previously extracted text for cross-reference

WEB SCRAPING + VISUAL ANALYSIS PRIORITIES:

1. **DIRECT PAGE ACCESS**: 
   - Scrape the provided URL to get the most current content
   - Access all page elements including dynamic content
   - Get complete financial data, tables, and structured information

2. **VISUAL LAYOUT ANALYSIS**: 
   - Use screenshot to understand page structure and hierarchy
   - Identify visual emphasis (headers, bold text, colors)
   - Assess professional presentation and data organization

3. **COMPREHENSIVE DATA EXTRACTION**:
   - Extract specific financial values with full context
   - Identify tables with loan amounts, rates, terms
   - Capture investment criteria and requirements
   - Note geographic locations and property types

4. **ONTOLOGICAL CONTEXT**:
   - Determine entity roles (who provides vs. receives)
   - Include temporal context (when applicable)
   - Preserve relationships (lender-borrower, sponsor-investor)

CRITICAL INSTRUCTIONS:
- SCRAPE THE URL FIRST to get complete, current data
- Use the screenshot to understand visual emphasis and layout
- Cross-reference with provided text to ensure accuracy
- Extract actual values, not just descriptions
- Include full ontological context for all data points

OUTPUT FORMAT: Return YAML (not JSON) for token efficiency.`;
  }

  /**
   * Enhanced system prompt for multimodal classification
   */
  private getMultimodalSystemPrompt(): string {
    return `You are an expert multimodal financial data extraction and classification system specializing in real estate and investment company content.

You will analyze web pages using THREE data sources:
1. 📸 SCREENSHOT: Visual layout, design, charts, tables, and UI elements
2. 🏗️ HTML STRUCTURE: Semantic markup, metadata, and code structure  
3. 📝 EXTRACTED TEXT: Clean readable content

MULTIMODAL ANALYSIS PRIORITIES:
1. **SCREENSHOT ANALYSIS**: Look for visual elements that text might miss:
   - Charts, graphs, and financial visualizations
   - Table layouts and data organization
   - Navigation structure and page hierarchy
   - Visual emphasis (bold, colors, sizing)
   - Form fields and interactive elements
   - Logos, branding, and professional presentation

2. **HTML STRUCTURE ANALYSIS**: Extract semantic meaning:
   - Section organization and content hierarchy
   - Table structures with financial data
   - List formats (criteria, requirements, features)
   - Metadata and structured data
   - Class names and IDs that indicate content type

3. **TEXT CONTENT ANALYSIS**: Process readable content:
   - Financial amounts, rates, and terms
   - Geographic locations and property types
   - Investment criteria and requirements
   - Company descriptions and capabilities

CRITICAL MULTIMODAL EXTRACTION RULES:
- Use visual cues from screenshot to identify important sections
- Cross-reference visual layout with HTML structure
- Extract specific values with visual and textual context
- Identify entity roles based on visual presentation and text content
- Note visual emphasis and hierarchy for data prioritization

OUTPUT FORMAT: Return YAML (not JSON) for token efficiency. Use proper YAML syntax with dashes for lists.`;
  }

  /**
   * System prompt that defines the classification task
   */
  private getSystemPrompt(): string {
    return `You are an expert financial data extraction and classification system specializing in real estate and investment company content.

Your PRIMARY task is to extract ACTUAL DATA VALUES from web pages with their full ontological context (who/what/where/for whom) while classifying the content.

CRITICAL OUTPUT FORMAT: Return your analysis in YAML format (NOT JSON) to save tokens and improve readability.

CRITICAL DATA EXTRACTION RULES:
1. EXTRACT SPECIFIC VALUES: Get actual amounts, percentages, ranges, locations, property types
2. CAPTURE ONTOLOGICAL CONTEXT: For each data point, identify WHO it applies to and WHAT ROLE they play
3. DISTINGUISH DIRECTION: Is this what "we" (the company) provide or what "they" (clients/borrowers) must have?
4. PRESERVE RELATIONSHIPS: Identify lender-borrower, sponsor-investor, manager-LP relationships
5. INCLUDE TEMPORAL CONTEXT: When applicable, note timeframes, terms, hold periods

ONTOLOGICAL ROLES TO IDENTIFY:
- LENDER vs BORROWER (who provides money vs who receives it)
- SPONSOR vs INVESTOR/LP (who manages deals vs who provides capital)
- FUND MANAGER vs LIMITED PARTNER (who runs fund vs who invests in fund)
- PROPERTY OWNER vs PROPERTY MANAGER (who owns vs who operates)

DATA EXTRACTION PRIORITIES:
1. Financial amounts with WHO provides/receives them
2. Geographic focus with WHO operates where
3. Property types with WHO handles/owns them  
4. Terms/periods with WHO they apply to
5. Requirements/criteria with WHO must meet them
6. Returns/performance with WHO benefits

EXAMPLES OF GOOD EXTRACTIONS:
- "Company lends $1M-$5M to real estate investors" 
- "Borrowers must provide 20% down payment"
- "Fund targets 15% IRR for limited partners"
- "We focus on East Coast multifamily properties"
- "Investors must be accredited with $1M minimum"

OUTPUT FORMAT: Return YAML (not JSON) for token efficiency. Use proper YAML syntax with dashes for lists.`;
  }

  /**
   * Smart content truncation that preserves important sections
   */
  private smartTruncateContent(content: string): string {
    const MAX_CONTENT_LENGTH = 12000; // Increased from 4000 to 12000 chars
    
    if (content.length <= MAX_CONTENT_LENGTH) {
      console.log(`📏 Content fits within limit: ${content.length} chars`);
      return content;
    }
    
    console.log(`⚠️  Content truncation needed: ${content.length} chars -> ${MAX_CONTENT_LENGTH} chars`);
    
    // If it's HTML content, try to preserve important sections
    if (content.includes('<html>') || content.includes('<!DOCTYPE')) {
      return this.smartTruncateHtml(content, MAX_CONTENT_LENGTH);
    } else {
      // For text content, just truncate with indication
      return content.substring(0, MAX_CONTENT_LENGTH) + '\n\n[CONTENT TRUNCATED - ORIGINAL LENGTH: ' + content.length + ' chars]';
    }
  }
  
  /**
   * Smart HTML truncation that preserves key sections
   */
  private smartTruncateHtml(htmlContent: string, maxLength: number): string {
    // Priority sections to preserve (in order of importance)
    const importantSections = [
      // Investment/lending specific content
      /<section[^>]*class[^>]*(?:invest|lend|loan|criteria|program)[^>]*>[\s\S]*?<\/section>/gi,
      /<div[^>]*class[^>]*(?:invest|lend|loan|criteria|program)[^>]*>[\s\S]*?<\/div>/gi,
      
      // Main content areas
      /<main[\s\S]*?<\/main>/gi,
      /<article[\s\S]*?<\/article>/gi,
      /<div[^>]*class[^>]*(?:content|main)[^>]*>[\s\S]*?<\/div>/gi,
      
      // Tables (often contain financial data)
      /<table[\s\S]*?<\/table>/gi,
      
      // Lists (often contain criteria)
      /<ul[\s\S]*?<\/ul>/gi,
      /<ol[\s\S]*?<\/ol>/gi,
      
      // Headers and important text
      /<h[1-6][^>]*>[\s\S]*?<\/h[1-6]>/gi,
      /<p[^>]*>[\s\S]*?<\/p>/gi
    ];
    
    let extractedContent = '';
    let remainingLength = maxLength;
    
    // Extract important sections first
    for (const sectionRegex of importantSections) {
      const matches = htmlContent.match(sectionRegex);
      if (matches && remainingLength > 500) { // Reserve space for other sections
        for (const match of matches) {
          if (match.length < remainingLength * 0.3) { // Don't let one section take too much space
            extractedContent += match + '\n';
            remainingLength -= match.length;
          }
        }
      }
    }
    
    // If we still have space, add beginning of the document
    if (remainingLength > 1000 && extractedContent.length < maxLength * 0.7) {
      const beginningLength = Math.min(remainingLength - 200, 2000);
      const beginning = htmlContent.substring(0, beginningLength);
      extractedContent = beginning + '\n\n' + extractedContent;
    }
    
    // Final truncation if needed
    if (extractedContent.length > maxLength) {
      extractedContent = extractedContent.substring(0, maxLength - 100);
    }
    
    extractedContent += '\n\n[SMART HTML TRUNCATION APPLIED - ORIGINAL LENGTH: ' + htmlContent.length + ' chars]';
    
    console.log(`🧠 Smart HTML truncation: ${htmlContent.length} -> ${extractedContent.length} chars`);
    return extractedContent;
  }

  /**
   * Build classification prompt for URL-based scraping + screenshot analysis
   */
  private buildScreenshotClassificationPrompt(context: ClassificationContext): string {
    return `Please scrape and analyze this web page to classify and extract financial data:

COMPANY: ${context.company_name}
URL TO SCRAPE: ${context.url}
PAGE TITLE: ${context.title}
RELEVANCE RANK: ${context.page_rank} (1 = most important)

REFERENCE EXTRACTED TEXT (for context):
${context.content.substring(0, 4000)} ${context.content.length > 4000 ? '...[truncated]' : ''}

ANALYSIS INSTRUCTIONS:
1. **SCRAPE THE URL**: Visit ${context.url} and analyze the live web page content
2. **VISUAL ANALYSIS**: Use the provided screenshot to understand layout and emphasis  
3. **EXTRACT DATA**: Focus on financial data, investment criteria, and company capabilities
4. **CONTEXT**: Include entity roles (who provides vs. receives) and ontological context
5. **CROSS-REFERENCE**: Use the reference text to verify and supplement your findings

Provide a YAML response with the classification and extracted data:

\`\`\`yaml
content_category: investment_criteria  # Primary page category
entity_focus: company_entity  # Main entity focus
business_function: lending_financing  # Primary business function
data_reliability: high  # Based on visual presentation quality
content_freshness: current
content_specificity: highly_specific

financial_data:
  loan_amounts: true
  interest_rates: true
  geographic_locations: true
  # ... other financial flags

investment_criteria:
  deal_size_range: true
  property_types: true
  geographic_focus: true
  # ... other criteria flags

extracted_data:
  loan_amounts:
    - amount: "$1M-$5M"
      context: "Bridge loan amounts provided by company"
      entity_role: "lender_provides_to_borrower"
  interest_rates:
    - rate: "Prime + 2.5%"
      context: "Bridge loan interest rate"
      entity_role: "lender_charges_borrower"
  # ... other extracted data

key_entities_mentioned: ["ABC Capital", "borrowers", "investors"]
primary_topics: ["bridge loans", "investment criteria", "lending programs"]
classification_confidence: 0.9
classification_notes: "Clear investment criteria page with detailed loan programs"
\`\`\``;
  }

  /**
   * Build the classification prompt for a specific page
   */
  private buildClassificationPrompt(context: ClassificationContext): string {
    return `Analyze this web page content and classify it according to the metadata schema:

COMPANY: ${context.company_name}
URL: ${context.url}
PAGE TITLE: ${context.title}
RELEVANCE RANK: ${context.page_rank} (1 = most important)
RANKING FACTORS: ${JSON.stringify(context.ranking_factors, null, 2)}

CONTENT TO ANALYZE (may include HTML structure - focus on meaningful content):
${this.smartTruncateContent(context.content)}

Provide a YAML response with the following structure (more token-efficient than JSON):

\`\`\`yaml
content_category: company_overview  # one of: company_overview, investment_criteria, fund_information, services_capabilities, team_leadership, portfolio_properties, financial_information, loan_products, deal_case_studies, market_insights, contact_application, legal_compliance, news_press, other

entity_focus: company_entity  # one of: company_entity, fund_entity, individual_person, property_asset, deal_transaction, market_general, multiple_entities, unclear

business_function: lending_financing  # one of: investment_management, lending_financing, property_management, development_construction, capital_raising, business_operations, marketing_sales, compliance_legal, multiple_functions, unclear

data_reliability: high  # one of: high, medium, low, mixed
content_freshness: current  # one of: current, dated, unclear
content_specificity: highly_specific  # one of: highly_specific, general, vague

financial_data:
  loan_amounts: true
  interest_rates: false
  investment_amounts: true
  returns_performance: false
  fund_sizes: true
  property_values: false
  fees_costs: false
  financial_ratios: true

investment_criteria:
  deal_size_range: true
  property_types: true
  geographic_focus: true
  investment_strategy: false
  hold_period: true
  return_targets: false
  leverage_preferences: true
  sector_focus: true

extracted_data:
  loan_amounts:
    - amount: "$1M-$5M"
      context: "Company provides to borrowers"
      entity_role: "lender_provides_to_borrower"
  interest_rates:
    - rate: "8-12%"
      context: "Charged to borrowers"
      entity_role: "lender_charges_borrower"
  geographic_locations:
    - location: "East Coast"
      context: "Primary lending area"
      entity_role: "lender_focus"
  property_types:
    - type: "Multifamily"
      context: "Properties we finance"
      entity_role: "financing_target"
  requirements_criteria:
    - requirement: "20% down payment"
      context: "Borrower requirement"
      entity_role: "borrower_must_have"

key_entities_mentioned:
  - "Company Name"
  - "Target Borrowers"

primary_topics:
  - "Real estate lending"
  - "Bridge financing"

classification_confidence: 0.95

classification_notes: "Detailed explanation of classification and extracted data"

potential_conflicts:
  - "Any data conflicts or ambiguities found"
\`\`\`

IMPORTANT CLASSIFICATION GUIDELINES:

1. **Entity Attribution**: Be very careful to distinguish between:
   - Company-level information vs Fund-level information
   - Individual team member criteria vs company criteria
   - Historical vs current information

2. **Data Type Distinction**: Clearly separate:
   - Loan products (what they lend) vs Investment products (what they invest in)
   - Company overview vs specific fund mandates
   - Team member backgrounds vs company capabilities

3. **Conflict Detection**: Flag potential issues like:
   - Mixed loan and investment amounts on same page
   - Individual vs company attribution ambiguity
   - Outdated vs current information mixing

4. **Reliability Assessment**: Consider:
   - Official company statements = high reliability
   - Marketing copy = medium reliability  
   - Vague or unclear statements = low reliability

Analyze the content thoroughly and provide detailed reasoning in your classification_notes.`;
  }

  /**
   * Parse the LLM response into structured metadata (now expects YAML)
   */
  private parseClassificationResponse(response: string): Partial<PageMetadata> {
    try {
      // Clean the response to extract YAML (between ```yaml and ```)
      const yamlMatch = response.match(/```yaml\s*([\s\S]*?)\s*```/) || response.match(/```\s*([\s\S]*?)\s*```/);
      let yamlContent = '';
      
      if (yamlMatch) {
        yamlContent = yamlMatch[1];
      } else {
        // If no code blocks, try to find YAML-like content
        const lines = response.split('\n');
        const yamlStart = lines.findIndex(line => line.includes('content_category:') || line.includes('entity_focus:'));
        if (yamlStart >= 0) {
          yamlContent = lines.slice(yamlStart).join('\n');
        } else {
          throw new Error('No YAML content found in response');
        }
      }

      // Parse YAML using a simple parser (since we control the format)
      const parsed = this.parseSimpleYaml(yamlContent);
      
      // Validate required fields
      this.validateClassification(parsed);
      
      return parsed;
    } catch (error) {
      console.error('Error parsing YAML classification response:', error);
      console.error('Raw response:', response);
      return this.createFallbackMetadata();
    }
  }

  /**
   * Simple YAML parser for our controlled metadata format
   */
  private parseSimpleYaml(yamlContent: string): any {
    const result: any = {};
    const lines = yamlContent.split('\n');
    let currentKey = '';
    let currentObject: any = null;
    let currentArray: any[] = [];
    let inArray = false;
    let inObject = false;

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;

      // Handle top-level key-value pairs
      if (trimmed.includes(':') && !trimmed.startsWith('-') && !trimmed.startsWith(' ')) {
        const [key, ...valueParts] = trimmed.split(':');
        const value = valueParts.join(':').trim();
        
        if (value && !value.startsWith('{') && !value.startsWith('[')) {
          // Simple value
          result[key.trim()] = this.parseValue(value);
          inArray = false;
          inObject = false;
        } else {
          // Object or array follows
          currentKey = key.trim();
          currentObject = {};
          currentArray = [];
          inObject = !value;
          inArray = false;
        }
      }
      // Handle object properties (indented)
      else if (trimmed.includes(':') && trimmed.startsWith(' ') && !trimmed.startsWith('- ')) {
        const [key, ...valueParts] = trimmed.split(':');
        const value = valueParts.join(':').trim();
        
        if (currentKey && inObject) {
          if (!result[currentKey]) result[currentKey] = {};
          result[currentKey][key.trim()] = this.parseValue(value);
        }
      }
      // Handle array items
      else if (trimmed.startsWith('- ')) {
        const item = trimmed.substring(2).trim();
        
        if (item.includes(':')) {
          // Object in array
          const obj: any = {};
          const [key, ...valueParts] = item.split(':');
          obj[key.trim()] = this.parseValue(valueParts.join(':').trim());
          currentArray.push(obj);
          inArray = true;
        } else {
          // Simple array item
          currentArray.push(this.parseValue(item));
          inArray = true;
        }
        
        if (currentKey) {
          result[currentKey] = currentArray;
        }
      }
      // Handle nested object properties in arrays
      else if (trimmed.includes(':') && trimmed.startsWith('  ') && inArray && currentArray.length > 0) {
        const [key, ...valueParts] = trimmed.split(':');
        const value = valueParts.join(':').trim();
        const lastItem = currentArray[currentArray.length - 1];
        if (typeof lastItem === 'object') {
          lastItem[key.trim()] = this.parseValue(value);
        }
      }
    }

    return result;
  }

  /**
   * Parse individual values from YAML
   */
  private parseValue(value: string): any {
    const trimmed = value.trim();
    
    // Remove comments
    const withoutComment = trimmed.split('#')[0].trim();
    
    if (withoutComment === 'true') return true;
    if (withoutComment === 'false') return false;
    if (withoutComment === 'null') return null;
    
    // Try to parse as number
    const num = parseFloat(withoutComment);
    if (!isNaN(num) && isFinite(num)) return num;
    
    // Remove quotes if present
    if ((withoutComment.startsWith('"') && withoutComment.endsWith('"')) ||
        (withoutComment.startsWith("'") && withoutComment.endsWith("'"))) {
      return withoutComment.slice(1, -1);
    }
    
    return withoutComment;
  }

  /**
   * Validate the classification response
   */
  private validateClassification(classification: any): void {
    const requiredFields = [
      'content_category',
      'entity_focus', 
      'business_function',
      'data_reliability'
    ];

    for (const field of requiredFields) {
      if (!classification[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Set defaults for missing optional fields
    classification.financial_data = classification.financial_data || this.createEmptyFinancialData();
    classification.investment_criteria = classification.investment_criteria || this.createEmptyInvestmentCriteria();
    classification.key_entities_mentioned = classification.key_entities_mentioned || [];
    classification.primary_topics = classification.primary_topics || [];
    classification.classification_confidence = classification.classification_confidence || 0.5;
    classification.classification_notes = classification.classification_notes || 'Auto-generated classification';
    classification.potential_conflicts = classification.potential_conflicts || [];
    classification.content_freshness = classification.content_freshness || 'unclear';
    classification.content_specificity = classification.content_specificity || 'general';
  }

  /**
   * Create fallback metadata when classification fails - return raw YAML
   */
  private createFallbackMetadata(): string {
    return `content_category: other
entity_focus: unclear
business_function: unclear
data_reliability: low
content_freshness: unclear
content_specificity: vague
financial_data:
  loan_amounts: false
  interest_rates: false
  investment_amounts: false
  returns_performance: false
  fund_sizes: false
  property_values: false
  fees_costs: false
  financial_ratios: false
investment_criteria:
  deal_size_range: false
  property_types: false
  geographic_focus: false
  investment_strategy: false
  hold_period: false
  return_targets: false
  leverage_preferences: false
  sector_focus: false
extracted_data: {}
key_entities_mentioned: []
primary_topics: []
classification_confidence: 0.1
classification_notes: "Fallback classification due to processing error"
potential_conflicts:
  - "Classification failed - manual review needed"
classified_at: "${new Date().toISOString()}"
classifier_version: "${this.version}"`;
  }

  private createEmptyFinancialData(): FinancialDataPresence {
    return {
      loan_amounts: false,
      interest_rates: false,
      investment_amounts: false,
      returns_performance: false,
      fund_sizes: false,
      property_values: false,
      fees_costs: false,
      financial_ratios: false
    };
  }

  private createEmptyInvestmentCriteria(): InvestmentCriteriaPresence {
    return {
      deal_size_range: false,
      property_types: false,
      geographic_focus: false,
      investment_strategy: false,
      hold_period: false,
      return_targets: false,
      leverage_preferences: false,
      sector_focus: false
    };
  }

  /**
   * Batch classify multiple pages
   */
  async classifyPages(contexts: ClassificationContext[]): Promise<PageMetadata[]> {
    const results: PageMetadata[] = [];
    
    for (const context of contexts) {
      try {
        const metadata = await this.classifyPage(context);
        results.push(metadata);
        
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`Error classifying page ${context.url}:`, error);
        results.push(this.createFallbackMetadata());
      }
    }
    
    return results;
  }
}