/**
 * Browser Pool Manager - Centralized browser instance management
 * Prevents memory leaks by reusing browser instances across all services
 */

import { chromium, B<PERSON>er, BrowserContext } from 'playwright';
// Simple logger function
const logger = (level: string, message: string) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
};

export class BrowserPoolManager {
  private static instance: BrowserPoolManager;
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private isInitialized = false;
  private activePages = new Set<any>();
  private maxPages = 10; // Maximum concurrent pages per browser

  private constructor() {}

  static getInstance(): BrowserPoolManager {
    if (!BrowserPoolManager.instance) {
      BrowserPoolManager.instance = new BrowserPoolManager();
    }
    return BrowserPoolManager.instance;
  }

  /**
   * Initialize the shared browser instance
   */
  async initialize(): Promise<void> {
    if (this.isInitialized && this.browser) {
      return;
    }

    try {
      logger('info', '🚀 Initializing shared browser pool...');
      
      this.browser = await chromium.launch({
        headless: true,
        args: [
          '--disable-dev-shm-usage',
          '--no-sandbox',
          '--disable-gpu',
          '--disable-extensions',
          '--disable-popup-blocking',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=TranslateUI',
          '--disable-ipc-flooding-protection',
          '--no-first-run',
          '--no-default-browser-check',
          '--disable-default-apps',
          '--disable-sync',
          '--disable-background-networking',
          '--disable-component-update',
          '--disable-client-side-phishing-detection',
          '--enable-cookies',
          '--disable-cookie-encryption'
        ]
      });

      this.context = await this.browser.newContext({
        userAgent: "Mozilla/5.0 (compatible; AnaxBot/1.0; +https://anax.ai/bot)",
        viewport: { width: 1280, height: 800 }
      });

      this.isInitialized = true;
      logger('info', '✅ Shared browser pool initialized successfully');
      
    } catch (error) {
      logger('error', `❌ Failed to initialize browser pool: ${error}`);
      throw error;
    }
  }

  /**
   * Get a new page from the shared browser context
   */
  async getPage(): Promise<any> {
    if (!this.isInitialized || !this.browser || !this.context) {
      await this.initialize();
    }

    if (this.activePages.size >= this.maxPages) {
      logger('warn', `⚠️ Maximum pages reached (${this.maxPages}), waiting for cleanup...`);
      // Wait for some pages to be released
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const page = await this.context!.newPage();
    page.setDefaultTimeout(60000); // 60 seconds for pages with animations
    
    this.activePages.add(page);
    logger('debug', `📄 Created page (${this.activePages.size}/${this.maxPages} active)`);
    
    return page;
  }

  /**
   * Release a page back to the pool
   */
  async releasePage(page: any): Promise<void> {
    try {
      if (page && !page.isClosed()) {
        await page.close();
        this.activePages.delete(page);
        logger('debug', `🧹 Released page (${this.activePages.size}/${this.maxPages} active)`);
      }
    } catch (error) {
      logger('warn', `⚠️ Error releasing page: ${error}`);
    }
  }

  /**
   * Get browser and context for direct use
   */
  async getBrowserAndContext(): Promise<{ browser: Browser; context: BrowserContext }> {
    if (!this.isInitialized || !this.browser || !this.context) {
      await this.initialize();
    }
    return { browser: this.browser!, context: this.context! };
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    try {
      logger('info', '🧹 Cleaning up browser pool...');
      
      // Close all active pages
      for (const page of this.activePages) {
        try {
          if (!page.isClosed()) {
            await page.close();
          }
        } catch (error) {
          logger('warn', `⚠️ Error closing page: ${error}`);
        }
      }
      this.activePages.clear();

      // Close context
      if (this.context) {
        await this.context.close();
        this.context = null;
      }

      // Close browser
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }

      this.isInitialized = false;
      logger('info', '✅ Browser pool cleaned up successfully');
      
    } catch (error) {
      logger('error', `❌ Error during browser pool cleanup: ${error}`);
    }
  }

  /**
   * Get pool status
   */
  getStatus(): { isInitialized: boolean; activePages: number; maxPages: number } {
    return {
      isInitialized: this.isInitialized,
      activePages: this.activePages.size,
      maxPages: this.maxPages
    };
  }

  /**
   * Force restart the browser (useful for memory management)
   */
  async restart(): Promise<void> {
    logger('info', '🔄 Restarting browser pool...');
    await this.cleanup();
    await this.initialize();
  }
}

// Export singleton instance
export const browserPool = BrowserPoolManager.getInstance();
