import { createHash } from "crypto";
import fs from "fs";
import path from "path";
import { pool } from "@/lib/db";
import {
  File,
  FileRelationship,
  FileUploadRequest,
  FileRelationshipRequest,
  FileSearchFilters,
  FileSearchResult,
  TableFileQuery,
  FileRelationshipQuery,
  TableColumnValidation,
  FileProcessingOptions,
  DuplicateFile,
} from "@/types/file";
import { StorageProviderRegistry } from "../storage/StorageProviderRegistry";
import { IStorageProvider } from "../storage/IStorageProvider";

export class EnhancedFileManager {
  private static storageRegistry = StorageProviderRegistry.getInstance();

  /**
   * Upload a new file with content hash deduplication and storage provider support
   */
  static async uploadFile(
    fileBuffer: Buffer,
    request: FileUploadRequest,
    options: FileProcessingOptions = {},
    preferredProvider?: string
  ): Promise<{ file: File; isDuplicate: boolean; filePath: string }> {
    try {
      // Generate content hash
      const contentHash = this.generateContentHash(fileBuffer);

      // Check for existing content
      const existingFile = await this.getFileByContentHash(contentHash);
      if (existingFile && !options.allow_duplicates) {
        // Return existing file info - don't create new file, just reuse existing one
        console.log(`File deduplication: Reusing existing file ${existingFile.file_id} for content hash ${contentHash}`);
        return {
          file: existingFile,
          isDuplicate: true,
          filePath: existingFile.storage_path || "",
        };
      }

      // Determine storage provider
      const provider = this.getStorageProvider(preferredProvider);
      if (!provider) {
        throw new Error('No storage provider available');
      }

      // Generate storage path
      const storagePath = this.generateStoragePath(contentHash, request.original_name);

      // Upload to storage provider
      const uploadResult = await provider.uploadFile(fileBuffer, storagePath, {
        originalName: request.original_name,
        mimeType: request.mime_type,
        metadata: request.metadata
      });

      if (!uploadResult.success) {
        throw new Error(`Upload failed: ${uploadResult.error}`);
      }

      // Generate file name if not provided
      const fileName =
        request.file_name || `${contentHash.substring(0, 8)}_${Date.now()}`;

      // Extract file extension
      const fileExtension = this.getFileExtension(request.original_name);

      // Determine MIME type
      const mimeType =
        request.mime_type || this.getMimeTypeFromExtension(fileExtension);

      // Prepare metadata with storage info
      const metadata = {
        ...request.metadata,
        content_hash: contentHash,
        storage_provider: provider.name,
        ...uploadResult.metadata
      };

      // Insert new file
      const result = await pool.query(
        `INSERT INTO files (
          file_name, original_name, title, description, content_hash, 
          mime_type, file_size_bytes, file_extension, uploaded_by, 
          upload_source, is_public, access_level, tags, metadata, custom_fields,
          storage_provider, storage_path, storage_metadata
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
        ) RETURNING *`,
        [
          fileName,
          request.original_name,
          request.title || null,
          request.description || null,
          contentHash,
          mimeType,
          fileBuffer.length,
          fileExtension || null,
          request.uploaded_by || null,
          request.upload_source || "api",
          request.is_public || false,
          request.access_level || "private",
          request.tags || null,
          metadata,
          request.custom_fields || null,
          provider.name,
          storagePath,
          uploadResult.metadata
        ]
      );

      const file = result.rows[0] as File;
      return { file, isDuplicate: false, filePath: storagePath };
    } catch (error) {
      console.error("Error uploading file:", error);
      throw new Error("Failed to upload file");
    }
  }

  /**
   * Get file from storage (backward compatible with getFileFromDisk)
   */
  static async getFileFromStorage(fileId: string): Promise<Buffer | null> {
    const file = await this.getFileById(fileId);
    if (!file) return null;

    const provider = this.storageRegistry.getProvider(file.storage_provider || 'local');
    if (!provider) {
      console.error(`Storage provider '${file.storage_provider}' not found for file ${fileId}. Available providers: ${this.storageRegistry.getProviderNames().join(', ')}`);
      return null;
    }

    const fileBuffer = await provider.downloadFile(file.storage_path || '');
    if (!fileBuffer) {
      console.error(`Failed to download file ${fileId} from storage provider '${file.storage_provider}' at path '${file.storage_path}'`);
    }
    
    return fileBuffer;
  }

  /**
   * Get file buffer and metadata by file ID - unified method for any storage provider
   */
  static async getFileBufferById(fileId: string): Promise<{
    buffer: Buffer | null;
    fileName: string;
    mimeType: string;
    originalName: string;
  } | null> {
    try {
      const file = await this.getFileById(fileId);
      if (!file) {
        console.error(`File with ID ${fileId} not found`);
        return null;
      }

      const buffer = await this.getFileFromStorage(fileId);
      if (!buffer) {
        console.error(`Failed to download file ${fileId} from storage`);
        return null;
      }

      return {
        buffer,
        fileName: file.file_name || 'unknown',
        mimeType: file.mime_type || 'application/octet-stream',
        originalName: file.original_name || 'unknown'
      };
    } catch (error) {
      console.error(`Error getting file buffer for ID ${fileId}:`, error);
      return null;
    }
  }

  /**
   * Get multiple file buffers by file IDs - batch method for processors
   */
  static async getFileBuffersByIds(fileIds: string[]): Promise<Array<{
    buffer: Buffer | null;
    fileName: string;
    mimeType: string;
    originalName: string;
    fileId: string;
  }>> {
    const results = await Promise.all(
      fileIds.map(async (fileId) => {
        const fileData = await this.getFileBufferById(fileId);
        if (!fileData) {
          return {
            fileId,
            buffer: null,
            fileName: 'unknown',
            mimeType: 'application/octet-stream',
            originalName: 'unknown'
          };
        }
        return {
          fileId,
          buffer: fileData.buffer,
          fileName: fileData.fileName,
          mimeType: fileData.mimeType,
          originalName: fileData.originalName
        };
      })
    );

    return results;
  }

  /**
   * Backward compatible method - routes to getFileFromStorage
   */
  static async getFileFromDisk(filePath: string): Promise<Buffer | null> {
    // For backward compatibility, try to find file by storage_path
    const file = await this.getFileByStoragePath(filePath);
    if (!file) return null;

    return await this.getFileFromStorage(file.file_id);
  }

  /**
   * Get file by storage path
   */
  static async getFileByStoragePath(storagePath: string): Promise<File | null> {
    try {
      const result = await pool.query(
        'SELECT * FROM files WHERE storage_path = $1',
        [storagePath]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting file by storage path:', error);
      return null;
    }
  }

  /**
   * Get file by ID
   */
  static async getFileById(fileId: string): Promise<File | null> {
    try {
      const result = await pool.query(
        'SELECT * FROM files WHERE file_id = $1',
        [fileId]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting file by ID:', error);
      return null;
    }
  }

  /**
   * Get file by content hash
   */
  static async getFileByContentHash(contentHash: string): Promise<File | null> {
    try {
      const result = await pool.query(
        'SELECT * FROM files WHERE content_hash = $1',
        [contentHash]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting file by content hash:', error);
      return null;
    }
  }

  /**
   * Get storage provider
   */
  private static getStorageProvider(preferredProvider?: string): IStorageProvider | null {
    if (preferredProvider) {
      const provider = this.storageRegistry.getProvider(preferredProvider);
      if (provider) return provider;
    }

    // Default to Azure, fallback to local
    return this.storageRegistry.getDefaultProvider();
  }

  /**
   * Generate storage path for a file
   */
  private static generateStoragePath(contentHash: string, originalName: string): string {
    // Create subdirectories based on hash for better organization
    const hashPrefix = contentHash.substring(0, 2);
    const hashSubdir = contentHash.substring(2, 4);
    const extension = path.extname(originalName);
    const fileName = `${contentHash}${extension}`;

    return `files/${hashPrefix}/${hashSubdir}/${fileName}`;
  }

  /**
   * Generate content hash
   */
  private static generateContentHash(buffer: Buffer): string {
    return createHash('sha256').update(buffer).digest('hex');
  }

  /**
   * Get file extension
   */
  private static getFileExtension(filename: string): string {
    return path.extname(filename).toLowerCase();
  }

  /**
   * Get MIME type from extension
   */
  private static getMimeTypeFromExtension(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.txt': 'text/plain',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.mp4': 'video/mp4',
      '.mp3': 'audio/mpeg',
      '.zip': 'application/zip',
      '.json': 'application/json',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * Create file relationship
   */
  static async createFileRelationship(
    fileId: string,
    relationship: FileRelationshipRequest,
    options: FileProcessingOptions = {}
  ): Promise<FileRelationship> {
    try {
      // Validate table and column if requested
      if (options.validate_table_column) {
        const isValid = await this.validateTableColumn(
          relationship.target_table_name,
          relationship.target_column_name
        );
        if (!isValid) {
          throw new Error(`Invalid table/column: ${relationship.target_table_name}.${relationship.target_column_name}`);
        }
      }

      // Insert relationship
      const result = await pool.query(
        `INSERT INTO file_relationships (
          file_id, target_table_name, target_column_name, target_row_id,
          relationship_type, relationship_title, relationship_notes,
          display_order, is_primary
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *`,
        [
          fileId,
          relationship.target_table_name,
          relationship.target_column_name,
          relationship.target_row_id,
          relationship.relationship_type || null,
          relationship.relationship_title || null,
          relationship.relationship_notes || null,
          relationship.display_order || 0,
          relationship.is_primary || false
        ]
      );

      return result.rows[0] as FileRelationship;
    } catch (error) {
      console.error("Error creating file relationship:", error);
      throw new Error("Failed to create file relationship");
    }
  }

  /**
   * Validate table and column
   */
  private static async validateTableColumn(tableName: string, columnName: string): Promise<boolean> {
    try {
      const result = await pool.query(
        `SELECT column_name FROM information_schema.columns 
         WHERE table_name = $1 AND column_name = $2`,
        [tableName, columnName]
      );
      return result.rows.length > 0;
    } catch (error) {
      console.error("Error validating table column:", error);
      return false;
    }
  }

  /**
   * Search files with filters
   */
  static async searchFiles(
    filters: FileSearchFilters = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<FileSearchResult> {
    try {
      const whereConditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      // Build WHERE conditions
      if (filters.file_name) {
        whereConditions.push(`file_name ILIKE $${paramIndex}`);
        params.push(`%${filters.file_name}%`);
        paramIndex++;
      }
      if (filters.original_name) {
        whereConditions.push(`original_name ILIKE $${paramIndex}`);
        params.push(`%${filters.original_name}%`);
        paramIndex++;
      }
      if (filters.mime_type) {
        whereConditions.push(`mime_type = $${paramIndex}`);
        params.push(filters.mime_type);
        paramIndex++;
      }
      if (filters.uploaded_by) {
        whereConditions.push(`uploaded_by = $${paramIndex}`);
        params.push(filters.uploaded_by);
        paramIndex++;
      }
      if (filters.access_level) {
        whereConditions.push(`access_level = $${paramIndex}`);
        params.push(filters.access_level);
        paramIndex++;
      }
      if (filters.storage_provider) {
        whereConditions.push(`storage_provider = $${paramIndex}`);
        params.push(filters.storage_provider);
        paramIndex++;
      }

      const whereClause =
        whereConditions.length > 0
          ? `WHERE ${whereConditions.join(" AND ")}`
          : "";

      // Get total count
      const countResult = await pool.query(
        `SELECT COUNT(*) as total FROM files ${whereClause}`,
        params
      );
      const totalCount = parseInt(countResult.rows[0]?.total || "0");

      // Get paginated results
      const offset = (page - 1) * pageSize;
      const result = await pool.query(
        `SELECT f.*, 
               COUNT(fr.relationship_id) as relationship_count,
               ARRAY_AGG(DISTINCT fr.target_table_name) as linked_table_names
        FROM files f
        LEFT JOIN file_relationships fr ON f.file_id = fr.file_id
        ${whereClause}
        GROUP BY f.file_id
        ORDER BY f.uploaded_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
        [...params, pageSize, offset]
      );

      const files = result.rows as File[];
      const totalPages = Math.ceil(totalCount / pageSize);

      return {
        files,
        total_count: totalCount,
        page,
        page_size: pageSize,
        total_pages: totalPages,
      };
    } catch (error) {
      console.error("Error searching files:", error);
      return {
        files: [],
        total_count: 0,
        page,
        page_size: pageSize,
        total_pages: 0,
      };
    }
  }

  /**
   * Get files with storage information
   */
  static async getFilesWithStorage(
    filters: FileSearchFilters = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<any> {
    try {
      const result = await pool.query(`
        SELECT 
          f.*,
          CASE 
            WHEN f.storage_provider = 'local' THEN 'Local File System'
            WHEN f.storage_provider = 'azure' THEN 'Azure Blob Storage'
            WHEN f.storage_provider = 's3' THEN 'AWS S3'
            WHEN f.storage_provider = 'gdrive' THEN 'Google Drive'
            ELSE f.storage_provider
          END as storage_provider_name,
          CASE 
            WHEN f.storage_provider = 'local' THEN f.storage_path
            WHEN f.storage_provider = 'azure' THEN CONCAT('https://', f.storage_metadata->>'account_name', '.blob.core.windows.net/', f.storage_metadata->>'container', '/', f.storage_path)
            WHEN f.storage_provider = 's3' THEN CONCAT('https://', f.storage_metadata->>'bucket', '.s3.amazonaws.com/', f.storage_path)
            WHEN f.storage_provider = 'gdrive' THEN f.storage_metadata->>'web_view_link'
            ELSE f.storage_path
          END as storage_url,
          COALESCE(rel_counts.relationship_count, 0) as relationship_count
        FROM files f
        LEFT JOIN (
          SELECT file_id, COUNT(*) as relationship_count
          FROM file_relationships
          GROUP BY file_id
        ) rel_counts ON f.file_id = rel_counts.file_id
        ORDER BY f.uploaded_at DESC
        LIMIT $1 OFFSET $2
      `, [pageSize, (page - 1) * pageSize]);

      return result.rows;
    } catch (error) {
      console.error("Error getting files with storage:", error);
      return [];
    }
  }
}
