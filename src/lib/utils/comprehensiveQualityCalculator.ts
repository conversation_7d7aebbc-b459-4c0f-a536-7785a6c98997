import { DealV2, PropertyV2, DealNsfFieldV2 } from '@/components/dashboard/deals/shared/types-v2';

export interface ComprehensiveQualityMetrics {
  overview: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  debt: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    criteriaCount: number;
    individualCriteria?: Array<{
      criteria_id: number;
      capital_position: string[];
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
    }>;
  };
  equity: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    criteriaCount: number;
    individualCriteria?: Array<{
      criteria_id: number;
      capital_position: string[];
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
    }>;
  };
  nsf: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    sourcesCount: number;
    usesCount: number;
  };
  property: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  financial: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  units: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  campaign: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
}

// Helper function to check if a value is considered "populated"
function isValuePopulated(value: any): boolean {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim() !== '';
  if (typeof value === 'number') return !isNaN(value);
  if (Array.isArray(value)) return value.length > 0;
  if (typeof value === 'boolean') return true;
  if (value instanceof Date) return !isNaN(value.getTime());
  return true;
}

// Helper function to calculate quality for a set of fields
function calculateSectionQuality(fields: Record<string, any>): {
  qualityScore: number;
  completedFields: number;
  totalFields: number;
  missingFields: string[];
  fieldDetails: Record<string, { hasValue: boolean; value: any }>;
} {
  const fieldEntries = Object.entries(fields);
  const totalFields = fieldEntries.length;
  
  if (totalFields === 0) {
    return {
      qualityScore: 100,
      completedFields: 0,
      totalFields: 0,
      missingFields: [],
      fieldDetails: {}
    };
  }

  const fieldDetails: Record<string, { hasValue: boolean; value: any }> = {};
  const missingFields: string[] = [];
  let completedFields = 0;

  fieldEntries.forEach(([fieldName, value]) => {
    const hasValue = isValuePopulated(value);
    fieldDetails[fieldName] = { hasValue, value };
    
    if (hasValue) {
      completedFields++;
    } else {
      missingFields.push(fieldName);
    }
  });

  const qualityScore = Math.round((completedFields / totalFields) * 100);

  return {
    qualityScore,
    completedFields,
    totalFields,
    missingFields,
    fieldDetails
  };
}

export function calculateComprehensiveQuality(deal: DealV2): ComprehensiveQualityMetrics {
  // Overview fields (basic deal information)
  const overviewFields = {
    dealName: deal.dealName,
    summary: deal.summary,
    dealType: deal.dealType,
    askCapitalPosition: deal.askCapitalPosition,
    askAmount: deal.askAmount,
    capitalRaiseTimeline: deal.capitalRaiseTimeline,
    dateReceived: deal.dateReceived,
    dealStage: deal.dealStage,
    dateClosed: deal.dateClosed,
    dateUnderContract: deal.dateUnderContract,
    strategy: deal.strategy,
    holdPeriod: deal.holdPeriod,
    dealStatus: deal.dealStatus,
    priority: deal.priority,
    reviewStatus: deal.reviewStatus,
    reviewedBy: deal.reviewedBy,
    isDistressed: deal.isDistressed,
    isInternalOnly: deal.isInternalOnly,
    extractionConfidence: deal.extractionConfidence,
    processorVersion: deal.processorVersion,
    llmModelUsed: deal.llmModelUsed,
    llmProvider: deal.llmProvider,
    documentFilename: deal.documentFilename,
    documentSizeBytes: deal.documentSizeBytes,
    documentSource: deal.documentSource,
    documentType: deal.documentType,
    extractionMethod: deal.extractionMethod,
    extractionTimestamp: deal.extractionTimestamp,
    createdAt: deal.createdAt,
    updatedAt: deal.updatedAt
  };

  // Calculate debt criteria quality based on actual investment criteria records
  let debtQuality = {
    qualityScore: 100,
    completedFields: 0,
    totalFields: 0,
    missingFields: [],
    fieldDetails: {},
    criteriaCount: 0
  };

  if (deal.investmentCriteriaDebt && deal.investmentCriteriaDebt.length > 0) {
    debtQuality.criteriaCount = deal.investmentCriteriaDebt.length;
    
    // Define relevant debt fields for quality calculation
    const debtFieldsToCheck = [
      'loanInterestRate', 'loanToValueMin', 'loanToValueMax', 'loanToCostMin', 'loanToCostMax',
      'minLoanTerm', 'maxLoanTerm', 'loanType', 'amortization', 'minLoanDscr', 'maxLoanDscr',
      'recourseLoan', 'lienPosition', 'rateType', 'loanProgram', 'structuredLoanTranche'
    ];
    
    let totalCompletedFields = 0;
    let totalPossibleFields = 0;
    const allMissingFields = new Set<string>();
    const allFieldDetails: Record<string, { hasValue: boolean; value: any }> = {};
    
    // Check each debt criteria record
    deal.investmentCriteriaDebt.forEach((criteria, index) => {
      debtFieldsToCheck.forEach(fieldName => {
        const value = (criteria as any)[fieldName];
        const hasValue = isValuePopulated(value);
        const fieldKey = `debt_${index}_${fieldName}`;
        
        allFieldDetails[fieldKey] = { hasValue, value };
        totalPossibleFields++;
        
        if (hasValue) {
          totalCompletedFields++;
        } else {
          allMissingFields.add(fieldKey);
        }
      });
    });
    
    debtQuality = {
      qualityScore: totalPossibleFields > 0 ? Math.round((totalCompletedFields / totalPossibleFields) * 100) : 100,
      completedFields: totalCompletedFields,
      totalFields: totalPossibleFields,
      missingFields: Array.from(allMissingFields),
      fieldDetails: allFieldDetails,
      criteriaCount: deal.investmentCriteriaDebt.length
    };
  }

  // Calculate equity criteria quality based on actual investment criteria records
  let equityQuality = {
    qualityScore: 100,
    completedFields: 0,
    totalFields: 0,
    missingFields: [],
    fieldDetails: {},
    criteriaCount: 0
  };

  // For equity, we need to consider both investment criteria AND NSF fields
  // Many deals have equity data in NSF fields rather than formal investment criteria
  const hasEquityCriteria = deal.investmentCriteriaEquity && deal.investmentCriteriaEquity.length > 0;
  const hasEquityNsfFields = deal.nsfFields && deal.nsfFields.some(nsf => 
    nsf.sourceType && ['General Partner (GP)', 'Limited Partner (LP)', 'Common Equity', 'Preferred Equity', 'Co-GP', 'Joint Venture (JV)'].includes(nsf.sourceType)
  );

  if (hasEquityCriteria || hasEquityNsfFields) {
    let totalCompletedFields = 0;
    let totalPossibleFields = 0;
    const allMissingFields = new Set<string>();
    const allFieldDetails: Record<string, { hasValue: boolean; value: any }> = {};
    
    // Count equity criteria records (if any)
    let criteriaRecordsCount = 0;
    if (hasEquityCriteria && deal.investmentCriteriaEquity) {
      criteriaRecordsCount = deal.investmentCriteriaEquity.length;
      
      // Define relevant equity fields for quality calculation
      const equityFieldsToCheck = [
        'targetReturn', 'minimumInternalRateOfReturn', 'minimumYieldOnCost', 'minimumEquityMultiple',
        'targetCashOnCashMin', 'minHoldPeriodYears', 'maxHoldPeriodYears', 'ownershipRequirement',
        'attachmentPoint', 'maxLeverageTolerance', 'yieldOnCost', 'targetReturnIrrOnEquity',
        'equityMultiple', 'positionSpecificIrr', 'positionSpecificEquityMultiple'
      ];
      
      // Check each equity criteria record
      deal.investmentCriteriaEquity.forEach((criteria, index) => {
        equityFieldsToCheck.forEach(fieldName => {
          const value = (criteria as any)[fieldName];
          const hasValue = isValuePopulated(value);
          const fieldKey = `equity_criteria_${index}_${fieldName}`;
          
          allFieldDetails[fieldKey] = { hasValue, value };
          totalPossibleFields++;
          
          if (hasValue) {
            totalCompletedFields++;
          } else {
            allMissingFields.add(fieldKey);
          }
        });
      });
    }
    
    // Count equity NSF fields as completed equity data
    if (hasEquityNsfFields && deal.nsfFields) {
      const equityNsfFields = deal.nsfFields.filter(nsf => 
        nsf.sourceType && ['General Partner (GP)', 'Limited Partner (LP)', 'Common Equity', 'Preferred Equity', 'Co-GP', 'Joint Venture (JV)'].includes(nsf.sourceType)
      );
      
      criteriaRecordsCount = Math.max(criteriaRecordsCount, equityNsfFields.length);
      
      // For each equity NSF field, count key equity metrics as completed
      equityNsfFields.forEach((nsf, index) => {
        const nsfEquityFields = [
          'amount', 'percentageOfTotal', 'amountPerGsf', 'amountPerNsf', 'sourceType'
        ];
        
        nsfEquityFields.forEach(fieldName => {
          const value = (nsf as any)[fieldName];
          const hasValue = isValuePopulated(value);
          const fieldKey = `equity_nsf_${index}_${fieldName}`;
          
          allFieldDetails[fieldKey] = { hasValue, value };
          totalPossibleFields++;
          
          if (hasValue) {
            totalCompletedFields++;
          } else {
            allMissingFields.add(fieldKey);
          }
        });
      });
    }
    
    equityQuality = {
      qualityScore: totalPossibleFields > 0 ? Math.round((totalCompletedFields / totalPossibleFields) * 100) : 100,
      completedFields: totalCompletedFields,
      totalFields: totalPossibleFields,
      missingFields: Array.from(allMissingFields),
      fieldDetails: allFieldDetails,
      criteriaCount: criteriaRecordsCount
    };
  }

  // NSF fields - should reflect Sources & Uses data, not building measurements
  console.log('🔍 [Data Quality Debug] NSF Fields check:');
  console.log('  - deal.nsfFields exists:', !!deal.nsfFields);
  console.log('  - deal.nsfFields length:', deal.nsfFields?.length || 0);
  if (deal.nsfFields && deal.nsfFields.length > 0) {
    console.log('  - First NSF field sample:', JSON.stringify(deal.nsfFields[0], null, 2));
  }

  // NSF fields quality should be based on Sources & Uses completeness, not building measurements
  let nsfQuality = {
    qualityScore: 0,
    completedFields: 0,
    totalFields: 0,
    missingFields: [] as string[],
    fieldDetails: {},
    criteriaCount: 0
  };

  if (deal.nsfFields && deal.nsfFields.length > 0) {
    let totalCompletedFields = 0;
    let totalPossibleFields = 0;
    const allMissingFields = new Set<string>();
    const allFieldDetails: Record<string, { hasValue: boolean; value: any }> = {};

    // Key NSF fields that should be populated for each source/use (matching the UI breakdown)
    const coreNsfFields = ['amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal'];

    deal.nsfFields.forEach((nsf, index) => {
      const nsfType = nsf.sourceType ? 'source' : 'use';
      console.log(`  🔍 [Data Quality Debug] Checking NSF ${index} (${nsfType}):`, nsf.sourceType || nsf.useType);
      
      // Check the 5 core NSF fields that appear in the UI breakdown
      coreNsfFields.forEach(fieldName => {
        const value = (nsf as any)[fieldName];
        const hasValue = isValuePopulated(value);
        const fieldKey = `nsf_${index}_${fieldName}`;
        
        if (hasValue) {
          console.log(`    ✅ ${fieldName}: ${JSON.stringify(value)} (populated)`);
        } else {
          console.log(`    ❌ ${fieldName}: ${JSON.stringify(value)} (empty)`);
        }
        
        allFieldDetails[fieldKey] = { hasValue, value };
        totalPossibleFields++;
        
        if (hasValue) {
          totalCompletedFields++;
        } else {
          allMissingFields.add(fieldKey);
        }
      });
      
      // Log the type for debugging but don't count it as a quality field
      // (it's used as a label/header in the UI, not as a measurable field)
      const typeField = nsf.sourceType ? 'sourceType' : 'useType';
      const typeValue = (nsf as any)[typeField];
      console.log(`    ℹ️  ${typeField}: ${JSON.stringify(typeValue)} (label/header, not counted in quality)`)
    });

    nsfQuality = {
      qualityScore: totalPossibleFields > 0 ? Math.round((totalCompletedFields / totalPossibleFields) * 100) : 100,
      completedFields: totalCompletedFields,
      totalFields: totalPossibleFields,
      missingFields: Array.from(allMissingFields),
      fieldDetails: allFieldDetails,
      criteriaCount: deal.nsfFields.length
    };
    
    console.log('🔍 [Data Quality Debug] Final NSF quality metrics:');
    console.log(`  - Completed fields: ${totalCompletedFields}`);
    console.log(`  - Total fields: ${totalPossibleFields}`);
    console.log(`  - Quality score: ${nsfQuality.qualityScore}%`);
    console.log(`  - NSF records count: ${nsfQuality.criteriaCount}`);
  }

  // Property fields - check both deal-level and property object fields
  console.log('🔍 [Data Quality Debug] Property fields check:');
  console.log('  - deal.property exists:', !!deal.property);
  if (deal.property) {
    console.log('  - Property object keys:', Object.keys(deal.property));
    console.log('  - Property sample data:', JSON.stringify({
      propertyType: deal.property.propertyType,
      address: deal.property.address,
      buildingSqft: deal.property.buildingSqft,
      yearBuilt: deal.property.yearBuilt
    }, null, 2));
  }

  // Property fields - remove duplicates and focus on most relevant fields
  const propertyFields = {
    // Core property identification (5 fields)
    propertyId: deal.propertyId,
    propertyType: deal.property?.propertyType,
    propertySubpropertyType: deal.property?.subpropertyType,
    propertyStatus: deal.property?.propertyStatus,
    propertyDescription: deal.property?.propertyDescription,
    
    // Location (8 fields)
    propertyAddress: deal.property?.address,
    propertyCity: deal.property?.city,
    propertyState: deal.property?.state,
    propertyZipcode: deal.property?.zipcode,
    propertyCountry: deal.property?.country,
    propertyRegion: deal.property?.region,
    propertyMarket: deal.property?.market,
    propertySubmarket: deal.property?.submarket,
    // ❌ Removed propertyNeighborhood - less critical
    
    // Physical characteristics (8 fields)
    propertyBuildingSqft: deal.property?.buildingSqft,
    propertyLandAcres: deal.property?.landAcres,
    propertyYearBuilt: deal.property?.yearBuilt,
    propertyYearRenovated: deal.property?.yearRenovated,
    propertyGsfGrossSquareFoot: deal.property?.gsfGrossSquareFoot,
    propertyZfaZoningFloorArea: deal.property?.zfaZoningFloorArea,
    propertyTotalNsfNetSquareFoot: deal.property?.totalNsfNetSquareFoot,
    propertyFloorAreaRatio: deal.property?.floorAreaRatio,
    // ❌ Removed propertyZoningSquareFootage - duplicate of zfaZoningFloorArea
    // ❌ Removed propertyLotArea - less commonly used
    
    // Units and value (4 fields)
    propertyNumberOfUnits: deal.property?.numberOfUnits,
    numApartmentUnits: deal.numApartmentUnits, // Deal-level apartment units
    propertyAppraisalValue: deal.property?.appraisalValue,
    propertyEnvironmentalRiskScore: deal.property?.environmentalRiskScore,
    
    // Deal-level property stats (4 fields)
    totalNumAffordableHousingUnits: deal.totalNumAffordableHousingUnits,
    totalNumMarketRateUnits: deal.totalNumMarketRateUnits,
    hotelKeys: deal.hotelKeys,
    parkingSpots: deal.parkingSpots
    // ❌ Removed closingTime, parkingSf - less critical
    // ❌ Removed propertyLatitude, propertyLongitude - coordinates less critical for quality
    // ❌ Removed propertyAppraisalValueDate, propertyHistoricalOccupancyTrend, propertyFar - less commonly used
  };
  console.log(`  - Total property fields being checked: ${Object.keys(propertyFields).length}`);

  // Financial fields
  const financialFields = {
    purchasePrice: deal.purchasePrice,
    totalProjectCost: deal.totalProjectCost,
    costPerTotalNsfNetSquareFoot: deal.costPerTotalNsfNetSquareFoot,
    costPerZoningFloorArea: deal.costPerZoningFloorArea
  };

  // Unit-related fields - ONLY include fields that actually exist in DealsV2 entity
  console.log('🔍 [Data Quality Debug] Units fields check:');
  const unitFields = {
    // Unit counts (8 fields) - ✅ All exist in DealsV2
    numAffordableHousing1bedroomUnits: deal.numAffordableHousing1bedroomUnits,
    numAffordableHousing2bedroomUnits: deal.numAffordableHousing2bedroomUnits,
    numAffordableHousing3bedroomUnits: deal.numAffordableHousing3bedroomUnits,
    numAffordableHousingStudiosUnits: deal.numAffordableHousingStudiosUnits,
    numMarketRate1bedroomUnits: deal.numMarketRate1bedroomUnits,
    numMarketRate2bedroomUnits: deal.numMarketRate2bedroomUnits,
    numMarketRate3bedroomUnits: deal.numMarketRate3bedroomUnits,
    numMarketRateStudiosUnits: deal.numMarketRateStudiosUnits,
    
    // Rent prices (7 fields) - ✅ All exist in DealsV2 (NO marketRateRentStudioUnit!)
    affordableHousingRent1bedroomUnit: deal.affordableHousingRent1bedroomUnit,
    affordableHousingRent2bedroomUnit: deal.affordableHousingRent2bedroomUnit,
    affordableHousingRent3bedroomUnit: deal.affordableHousingRent3bedroomUnit,
    affordableHousingRentStudioUnit: deal.affordableHousingRentStudioUnit,
    marketRateRent1bedroomUnit: deal.marketRateRent1bedroomUnit,
    marketRateRent2bedroomUnit: deal.marketRateRent2bedroomUnit,
    marketRateRent3bedroomUnit: deal.marketRateRent3bedroomUnit,
    // ❌ marketRateRentStudioUnit does NOT exist in DealsV2 entity
    
    // Sale prices (8 fields) - ✅ All exist in DealsV2
    affordableHousingSale1bedroomUnit: deal.affordableHousingSale1bedroomUnit,
    affordableHousingSale2bedroomUnit: deal.affordableHousingSale2bedroomUnit,
    affordableHousingSale3bedroomUnit: deal.affordableHousingSale3bedroomUnit,
    affordableHousingSaleStudioUnit: deal.affordableHousingSaleStudioUnit,
    marketRateSale1bedroomUnit: deal.marketRateSale1bedroomUnit,
    marketRateSale2bedroomUnit: deal.marketRateSale2bedroomUnit,
    marketRateSale3bedroomUnit: deal.marketRateSale3bedroomUnit,
    marketRateSaleStudioUnit: deal.marketRateSaleStudioUnit,
    
    // Community facility (4 fields) - ✅ All exist in DealsV2  
    communityFacilityRent: deal.communityFacilityRent,
    communityFacilityRentAdditional: deal.communityFacilityRentAdditional,
    communityFacilitySalePrice: deal.communityFacilitySalePrice,
    communityFacilitySalePriceAdditional: deal.communityFacilitySalePriceAdditional
  };
  console.log(`  - Total unit fields being checked: ${Object.keys(unitFields).length}`);

  // Campaign fields
  const campaignFields = {
    dealCampaignDate: deal.dealCampaignDate,
    dealCampaignEmailsAdditionalInfoRequestedReceived: deal.dealCampaignEmailsAdditionalInfoRequestedReceived,
    dealCampaignEmailsBounceBackReceived: deal.dealCampaignEmailsBounceBackReceived,
    dealCampaignEmailsOutOfOfficeResponseReceived: deal.dealCampaignEmailsOutOfOfficeResponseReceived,
    dealCampaignEmailsResponseReceived: deal.dealCampaignEmailsResponseReceived,
    dealCampaignEmailsSentOut: deal.dealCampaignEmailsSentOut,
    dealCampaignEmailsSoftQuotesReceived: deal.dealCampaignEmailsSoftQuotesReceived,
    dealCampaignEmailsTermSheetsReceived: deal.dealCampaignEmailsTermSheetsReceived,
    dealCampaignStatus: deal.dealCampaignStatus,
    dealCampaignLeadScore: deal.dealCampaignLeadScore,
    dealCampaignPredictedScenario: deal.dealCampaignPredictedScenario
  };

  // Calculate quality for each section
  const overview = calculateSectionQuality(overviewFields);
  const debt = debtQuality; // Use the calculated debt criteria quality
  const equity = equityQuality; // Use the calculated equity criteria quality
  const nsf = nsfQuality; // Use the calculated NSF sources/uses quality
  const property = calculateSectionQuality(propertyFields);
  const financial = calculateSectionQuality(financialFields);
  const units = calculateSectionQuality(unitFields);
  const campaign = calculateSectionQuality(campaignFields);

  // Add NSF-specific counts
  const nsfFieldsData = deal.nsfFields || deal.nsf_fields || [];
  const sourcesCount = nsfFieldsData.filter(nsf => nsf.sourceType).length;
  const usesCount = nsfFieldsData.filter(nsf => nsf.useType).length;

  // Add investment criteria counts
  const debtCriteriaCount = deal.investmentCriteriaDebt?.length || 0;
  const equityCriteriaCount = deal.investmentCriteriaEquity?.length || 0;

  return {
    overview,
    debt: {
      ...debt,
      criteriaCount: debtCriteriaCount
    },
    equity: {
      ...equity,
      criteriaCount: equityCriteriaCount
    },
    nsf: {
      ...nsf,
      sourcesCount,
      usesCount
    },
    property,
    financial,
    units,
    campaign
  };
}
