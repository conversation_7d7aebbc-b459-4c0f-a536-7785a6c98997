import * as cheerio from "cheerio";
import fetch from "node-fetch";
import * as xmlParser from "xml-js";
import * as playwright from "playwright";
import { relative } from "path";
import { v4 as uuidv4 } from 'uuid';
import { gunzipSync } from 'zlib';
import Bottleneck from 'bottleneck';
import { PageMetadataClassifier } from './page-metadata-classifier';
import { PageMetadata, ClassificationContext } from '../../types/page-metadata';
import { browserPool } from './browser-pool-manager';

/**
 * Normalize URL for deduplication purposes
 * Removes www prefix, hash fragments, and ensures consistent format
 */
function normalizeUrlForDeduplication(url: string): string {
  try {
    const urlObj = new URL(url);
    
    // Normalize hostname (remove www prefix for comparison)
    const normalizedHost = urlObj.hostname.replace(/^www\./i, "").toLowerCase();
    
    // Build normalized URL with consistent format
    const normalized = new URL(url);
    normalized.hostname = normalizedHost;
    normalized.hash = ''; // Remove hash fragments
    normalized.search = normalized.search; // Keep query params
    
    // Ensure pathname starts with / if it's not empty
    if (normalized.pathname === '') {
      normalized.pathname = '/';
    }
    
    return normalized.toString();
  } catch {
    return url; // Return original if parsing fails
  }
}

const MAX_RETRIES = 3;
const RETRY_DELAY = 5000;

// Global limiters
const httpLimiter = new Bottleneck({
  maxConcurrent: 5,
  minTime: 0
});

const pageLimiter = new Bottleneck({
  maxConcurrent: 5,
  minTime: 0
});
const PAGE_CONCURRENCY = 5;

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Click "VIEW MORE", "SEE ALL", and similar buttons to load additional content
 */
async function clickLoadMoreButtons(page: any): Promise<void> {
  try {
    console.log('Looking for load more buttons...');
    
    // Common selectors for "load more" buttons
    const loadMoreSelectors = [
      // Text-based selectors
      'button:has-text("VIEW MORE")',
      'button:has-text("SEE ALL")',
      'button:has-text("LOAD MORE")',
      'button:has-text("SHOW MORE")',
      'button:has-text("VIEW ALL")',
      'a:has-text("VIEW MORE")',
      'a:has-text("SEE ALL")',
      'a:has-text("LOAD MORE")',
      'a:has-text("SHOW MORE")',
      'a:has-text("VIEW ALL")',
      
      // Class-based selectors
      '[class*="load-more"]',
      '[class*="view-more"]',
      '[class*="see-all"]',
      '[class*="show-more"]',
      '[class*="expand"]',
      
      // Data attribute selectors
      '[data-load-more]',
      '[data-view-more]',
      '[data-see-all]',
      '[data-show-more]',
      
      // ID-based selectors
      '#load-more',
      '#view-more',
      '#see-all',
      '#show-more',
      
      // Generic selectors
      '.more-content',
      '.expand-content',
      '.toggle-content'
    ];
    
    let buttonsClicked = 0;
    const maxClicks = 10; // Prevent infinite clicking
    
    while (buttonsClicked < maxClicks) {
      let foundButton = false;
      
      // Try each selector
      for (const selector of loadMoreSelectors) {
        try {
          // Check if button exists and is visible
          const button = await page.$(selector);
          if (button) {
            const isVisible = await button.isVisible();
            if (isVisible) {
              console.log(`Clicking button with selector: ${selector}`);
              
              // Click the button
              await button.click();
              buttonsClicked++;
              foundButton = true;
              
              // Wait for content to load
              await page.waitForTimeout(3030);
              
              // Wait for any loading indicators to disappear
              try {
                await page.waitForFunction(
                  () => !document.querySelector('.loading, .spinner, [class*="loading"], [class*="spinner"]'),
                  { timeout: 5000 }
                );
              } catch (error) {
                // Continue if timeout
              }
              
              break; // Found and clicked a button, try again
            }
          }
        } catch (error) {
          // Continue to next selector if this one fails
          continue;
        }
      }
      
      // If no button was found, break the loop
      if (!foundButton) {
        break;
      }
    }
    
    console.log(`Clicked ${buttonsClicked} load more buttons`);
    
    // Wait a bit more for any final content to load
    await page.waitForTimeout(2000);
    
  } catch (error) {
    console.error('Error clicking load more buttons:', error);
    // Continue execution even if clicking fails
  }
}

/**
 * Check if page has dynamic content that would benefit from screenshot analysis
 */
async function hasDynamicContent(page: any): Promise<boolean> {
  try {
    return await page.evaluate(() => {
      const hasCounters = document.querySelectorAll('[class*="counter"], [class*="number"], [class*="stat"]').length > 0;
      const hasCharts = document.querySelectorAll('canvas, svg, [class*="chart"]').length > 0;
      const hasDataTables = document.querySelectorAll('table[class*="data"]').length > 0;
      const hasAnimations = document.querySelectorAll('[class*="animate"], [data-animate]').length > 0;
      return hasCounters || hasCharts || hasDataTables || hasAnimations;
    });
  } catch (error) {
    return false;
  }
}

/**
 * Check if page has financial content that would benefit from visual analysis
 */
async function hasFinancialContent(page: any): Promise<boolean> {
  try {
    return await page.evaluate(() => {
      const text = document.body.innerText.toLowerCase();
      const financialKeywords = [
        'investment', 'loan', 'fund', 'capital', 'equity', 'debt',
        'portfolio', 'returns', 'yield', 'interest', 'rate', 'amount',
        'million', 'billion', 'dollar', 'financing', 'lending'
      ];
      return financialKeywords.some(keyword => text.includes(keyword));
    });
  } catch (error) {
    return false;
  }
}

/**
 * Wait for animated counters to complete using requestAnimationFrame and DOM stability detection
 * Maximum wait time: 45 seconds total (out of 60 second page budget)
 */
async function waitForDynamicContentComplete(page: any): Promise<void> {
  const maxTotalWaitTime = 30300; // 30 seconds maximum
  const startTime = Date.now();
  
  try {
    console.log('Waiting for dynamic content to complete (smooth scroll + network + timeout strategy)...');
    
    // Strategy: Smooth scroll + network idle + 4 second timeout
    const contentDetectionPromise = (async () => {
      // Step 1: Smooth scroll through the entire page
      console.log('  Step 1: Smooth scrolling through page...');
      
      const { pageHeight: initialPageHeight, viewportHeight } = await page.evaluate(() => ({
        pageHeight: document.body.scrollHeight,
        viewportHeight: window.innerHeight
      }));
      
      const scrollStep = Math.floor(viewportHeight * 0.25); // 25% of viewport per step
      let currentPosition = 0;
      let scrollSteps = 0;
      const maxScrollSteps = 20;
      let pageHeight = initialPageHeight;
      
      // Scroll down smoothly
      while (currentPosition < pageHeight && scrollSteps < maxScrollSteps) {
        currentPosition += scrollStep;
        scrollSteps++;
        
        await page.evaluate((position) => {
          window.scrollTo({
            top: position,
            behavior: 'smooth'
          });
        }, currentPosition);
        
        // Wait for scroll to complete and any triggered animations
        await page.waitForTimeout(800);
        
        // Check if page height changed (dynamic content loading)
        const newPageHeight = await page.evaluate(() => document.body.scrollHeight);
        if (newPageHeight > pageHeight) {
          pageHeight = newPageHeight;
        }
      }
      
      // Scroll back to top smoothly
      await page.evaluate(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      });
      await page.waitForTimeout(1000);
      
      console.log(`  ✅ Smooth scrolling complete (${scrollSteps} steps)`);
      
      // Step 2: Wait for network requests to complete
      console.log('  Step 2: Waiting for network idle...');
      try {
        await page.waitForLoadState('networkidle', { timeout: 10000 });
        console.log('  ✅ Network idle achieved');
      } catch (error) {
        console.log('  ⚠️ Network idle timeout (continuing anyway)');
      }
      
      // Step 3: Additional 4-second timeout for animations/dynamic content
      console.log('  Step 3: Additional 4-second timeout for animations...');
      await page.waitForTimeout(4000);
      console.log('  ✅ Animation timeout complete');
      
      // Step 4: Check for dynamic content indicators
      console.log('  Step 4: Checking for dynamic content completion...');
      const contentCheck = await page.evaluate(() => {
        const text = document.body.innerText || '';
        const hasLargeNumbers = /\d{3,}/.test(text);
        const hasFormattedNumbers = /\d{1,3}(,\d{3})+/.test(text);
        const hasCurrency = /[\$€£¥]\s*\d+/.test(text);
        const hasCounterElements = document.querySelectorAll('[class*="counter"], [class*="number"], [class*="stat"]').length > 0;
        
        return {
          hasLargeNumbers,
          hasFormattedNumbers,
          hasCurrency,
          hasCounterElements,
          textLength: text.length
        };
      });
      
      console.log(`  📊 Content check: Numbers=${contentCheck.hasLargeNumbers}, Formatted=${contentCheck.hasFormattedNumbers}, Currency=${contentCheck.hasCurrency}, Counters=${contentCheck.hasCounterElements}, TextLength=${contentCheck.textLength}`);
      
      return contentCheck;
    })();
    
    // Execute the strategy with overall timeout protection
    const result = await Promise.race([
      contentDetectionPromise,
      new Promise(resolve => setTimeout(() => resolve(null), maxTotalWaitTime))
    ]);
    
    const totalTime = Date.now() - startTime;
    console.log(`  ✅ Dynamic content detection complete (${totalTime}ms elapsed)`);
    
    if (result === null) {
      console.log('  ⚠️ Strategy timed out, but continuing with current content');
    }
    
  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error(`Error waiting for dynamic content (${totalTime}ms elapsed):`, error);
  }
}

/**
 * Scroll gradually through the page to properly trigger intersection observers and lazy loading
 * Uses only smooth scrolling - no instant jumps 
 */
async function scrollToBottom(page: any): Promise<void> {
  try {
    console.log('Starting gradual scroll to trigger intersection observers...');
    
    // Get initial page height and viewport height
    const { pageHeight: initialPageHeight, viewportHeight } = await page.evaluate(() => ({
      pageHeight: document.body.scrollHeight,
      viewportHeight: window.innerHeight
    }));
    
    console.log(`Page height: ${initialPageHeight}px, Viewport height: ${viewportHeight}px`);
    
    // Scroll gradually in smaller chunks for better intersection observer triggering
    const scrollStep = Math.floor(viewportHeight * 0.3); // 30% of viewport height per step (smaller steps)
    let currentPosition = 0;
    let scrollAttempts = 0;
    const maxScrollAttempts = 30; // More attempts with smaller steps
    let pageHeight = initialPageHeight;
    
    while (currentPosition < pageHeight && scrollAttempts < maxScrollAttempts) {
      // Scroll down by one step using smooth scrolling
      currentPosition += scrollStep;
      
      await page.evaluate((position) => {
        window.scrollTo({
          top: position,
          behavior: 'smooth'
        });
      }, currentPosition);
      
      // Wait for smooth scroll to complete and observers to trigger
      await page.waitForTimeout(1000); // Reduced wait time since steps are smaller
      
      // Check if page height changed (new content loaded)
      const newPageHeight = await page.evaluate('document.body.scrollHeight');
      if (newPageHeight > pageHeight) {
        console.log(`Page height increased from ${pageHeight} to ${newPageHeight} during scroll`);
        pageHeight = newPageHeight; // Update target
      }
      
      scrollAttempts++;
      console.log(`Scroll step ${scrollAttempts}: scrolled to ${currentPosition}px of ${pageHeight}px`);
    }
    
    // Ensure we reach the absolute bottom with smooth scrolling
    console.log('Final smooth scroll to bottom...');
    await page.evaluate(() => {
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
      });
    });
    
    await page.waitForTimeout(2000);
    
    // Gradually scroll back to top to trigger any reverse observers
    console.log('Gradually scrolling back to top...');
    
    const finalHeight = await page.evaluate('document.body.scrollHeight');
    const upwardScrollStep = Math.floor(viewportHeight * 0.5); // Larger steps going back up
    let upwardPosition = finalHeight;
    let upwardAttempts = 0;
    
    while (upwardPosition > upwardScrollStep && upwardAttempts < 15) {
      upwardPosition = Math.max(0, upwardPosition - upwardScrollStep);
      
      await page.evaluate((position) => {
        window.scrollTo({
          top: position,
          behavior: 'smooth'
        });
      }, upwardPosition);
      
      await page.waitForTimeout(600); // Faster on the way back up
      upwardAttempts++;
    }
    
    // Final smooth scroll to top
    await page.evaluate(() => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
    
    await page.waitForTimeout(1000);
    console.log('Gradual scrolling complete - intersection observers should have triggered');
    
  } catch (error) {
    console.error('Error during gradual scrolling:', error);
    // Continue execution even if scrolling fails
  }
}

// Real estate specific keywords for finding relevant pages
const excludeKeywords = [
  "/sitemap",
  "gallery",
  "contact",
  "/posts",
  "/videos",
  "/media",
  "/blog",
  "/news",
  "/events",
  "/careers",
  "/jobs",
  "/privacy",
  "/terms",
  "/legal",
  "/cookie",
  "/disclaimer",
  "/team",
  "/application",
  "/applications",
  "/apply",
  "team",
  "staff",
  "our-team",
  "our-staff",
  "team-members",
  "staff-members",
  "team-member",
];

const realEstateKeywords = [
  // Company information
  "about",
  "story",
  "mission",
  "vision",
  "goal",
  "our-company",
  "company",
  "firm",
  "overview",
  "leadership",
  "team",
  "management",
  "principals",
  "partners",
  "executives",
  "founders",
  "who-we-are",
  "our-team",

  // Services and capabilities
  "capabilities",
  "services",
  "expertise",
  "experience",
  "history",
  "background",
  "specialties",
  "focus-areas",
  "practice-areas",
  "what-we-do",
  "our-services",

  // Investment/finance specific
  "investment",
  "philosophy",
  "approach",
  "strategy",
  "focus",
  "criteria",
  "portfolio",
  "investments",
  "assets",
  "properties",
  "fund",
  "funds",
  "capital",

  // Real estate specific
  "real-estate",
  "realestate",
  "property",
  "properties",
  "development",
  "construction",
  "acquisition",
  "disposition",
  "leasing",
  "management",
  "commercial",
  "residential",
  "industrial",
  "retail",
  "office",
  "multifamily",
  "hospitality",
  "logistics",
  "warehouse",
  "asset-management",
  "property-management",
  "real-estate-investment",
  "commercial-real-estate"
];

// Interface for the crawled page data - exactly as requested

const sitemapTemplates = [
  "sitemap.xml",
  "sitemap_index.xml", 
  "sitemap/sitemap.xml",
  "sitemap/index.xml",
  "sitemap1.xml",
  "wp-sitemap.xml",
  "page-sitemap.xml",
  "post-sitemap.xml",
  "category-sitemap.xml",
  "sitemap.xml.gz",
  "sitemap_index.xml.gz",
  "sitemapindex.xml",
  "sitemap-index.xml",
  "sitemap-misc.xml",
  "sitemap_misc.xml"
];

interface CrawlOptions {
  maxPages?: number;
  maxDepth?: number;
}

export interface ProcessUrlResult {
  url:string;
  text:string;
  screenshot?: Buffer; // Full page screenshot for multimodal analysis
  depth:number;
  parentUrl:string;
}

export interface UrlTrackingItem {
  url: string;
  parentUrl: string | null;
  depth: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  discoveredAt: number;
  source: 'sitemap' | 'page_links' | 'initial';
}

export interface CrawlResult {
  success: boolean;
  pageCount: number;
  error?: string;
}

export interface CrawledPageData {
  id: string;
  company_id: number;
  url: string;
  parent_url: string | null;
  crawl_depth: number;
  crawl_run_id: string;
  extracted_text: string;
  relevance_rank?: number;
  rank_factors?: any;
  page_metadata?: string; // Raw YAML string
  // Individual category fields removed - all metadata stored in page_metadata as raw YAML
}

export interface BusinessKeywords {
  propertyTypes: string[];
  strategies: string[];
  capitalPositions: string[];
  loanPrograms: string[];
  loanTypes: string[];
  recourseLoan: string[];
  structuredLoanTranches: string[];
  usRegions: string[];
  usStates: string[];
}

export interface RankingFactors {
  url_structure_score: number;
  content_quality_score: number;
  page_type_score: number;
  investment_criteria_score: number;
  financial_numbers_score: number;
  business_keywords_score: number;
  geographic_relevance_score: number;
  url_quality_bonus: number;
  total_score: number;
}

export interface DatabaseConnection {
  query(sql: string, params?: any[]): Promise<any[]>;
}   

// Global cache for business keywords
let cachedBusinessKeywords: BusinessKeywords | null = null;
let keywordsCacheTimestamp: number = 0;
const KEYWORDS_CACHE_DURATION = 60 * 60 * 1000; // 1 hour

/**
 * Fetch business keywords from mapping tables API
 */
async function fetchBusinessKeywords(): Promise<BusinessKeywords> {
  // Check cache first
  const now = Date.now();
  if (cachedBusinessKeywords && (now - keywordsCacheTimestamp) < KEYWORDS_CACHE_DURATION) {
    return cachedBusinessKeywords;
  }

  try {
    console.log('Fetching business keywords from mapping tables...');
    // For server-side usage, we'll need to construct the full URL or use direct DB access
    // This is a fallback that will need to be adjusted based on your deployment environment
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3030';
    const response = await fetch(`${baseUrl}/api/mapping-tables/consolidated`);
    if (!response.ok) {
      throw new Error(`Failed to fetch mapping data: ${response.status}`);
    }
    
    const result = await response.json() as { success: boolean; error?: string; data?: any };
    if (!result.success) {
      throw new Error(`API error: ${result.error}`);
    }

    const data = result.data || {};
    
    // Extract all keywords into flat arrays
    const businessKeywords: BusinessKeywords = {
      propertyTypes: [
        ...Object.keys(data.property_type_map || {}),
        ...(Object.values(data.property_type_map || {}).flat() as string[])
      ].filter((item): item is string => typeof item === 'string' && Boolean(item)),
      strategies: (data.strategies || []) as string[],
      capitalPositions: (data.capital_position || []) as string[],
      loanPrograms: (data.loan_program || []) as string[],
      loanTypes: (data.loan_type || []) as string[],
      recourseLoan: (data.recourse_loan || []) as string[],
      structuredLoanTranches: (data.structured_loan_tranches || []) as string[],
      usRegions: Object.keys(data.us_regions || {}),
      usStates: (data.us_states || []) as string[]
    };

    // Cache the results
    cachedBusinessKeywords = businessKeywords;
    keywordsCacheTimestamp = now;
    
    console.log('Business keywords fetched and cached:', {
      propertyTypes: businessKeywords.propertyTypes.length,
      strategies: businessKeywords.strategies.length,
      capitalPositions: businessKeywords.capitalPositions.length,
      totalKeywords: Object.values(businessKeywords).reduce((sum, arr) => sum + arr.length, 0)
    });

    return businessKeywords;
  } catch (error) {
    console.error('Error fetching business keywords:', error);
    // Return empty keywords as fallback
    return {
      propertyTypes: [],
      strategies: [],
      capitalPositions: [],
      loanPrograms: [],
      loanTypes: [],
      recourseLoan: [],
      structuredLoanTranches: [],
      usRegions: [],
      usStates: []
    };
  }
}

/**
 * Calculate relevance score for a web page based on URL and content
 */
export function calculatePageRelevance(url: string, content: string, businessKeywords: BusinessKeywords): RankingFactors {
  const urlLower = url.toLowerCase();
  const contentLower = content.toLowerCase();
  
  // EXCLUSION LOGIC: Penalize or exclude irrelevant pages
  const irrelevantPatterns = [
    // Other irrelevant patterns
    /\/press/,
    /\/blog\/\d+/,
    /\/events\/\d+/,
    /\/careers\/\d+/,
    /\/jobs\/\d+/,
    /\/gallery/,
    /\/photos/,
    /\/videos/,
    /\/media/,
    /\/downloads/,
    /\/files/,
    /\/pdf/,
    /\/privacy/,
    /\/terms/,
    /\/legal/,
    /\/cookie/,
    /\/disclaimer/,
    /\/sitemap/,
    /\/rss/,
    /\/feed/,
    /\/api/,
    /\/admin/,
    /\/login/,
    /\/register/,
    /\/signup/,
    /\/signin/,
    /\/documents/,
    /\/news\/\d+/,
    /\/team\/[a-z-]+/,
    /\/our-team\/[a-z-]+/,
    /\/staff\/[a-z-]+/,
    /\/adam-parks/,
    /\/christopher-goetz/,
    /\/marc-brooks/,
  ];
  
  // Check for irrelevant patterns
  const isIrrelevant = irrelevantPatterns.some(pattern => pattern.test(urlLower));
  if (isIrrelevant) {
    // Return very low score for irrelevant pages
    return {
      url_structure_score: 0,
      content_quality_score: 0,
      page_type_score: 0,
      investment_criteria_score: 0,
      financial_numbers_score: 0,
      business_keywords_score: 0,
      geographic_relevance_score: 0,
      url_quality_bonus: -10, // Penalty for irrelevant content
      total_score: -10
    };
  }
  
  // 1. URL Structure Score (0-25 points)
  let urlStructureScore = 0;
  try {
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/').filter(s => s.length > 0);
    
    if (pathSegments.length === 0) {
      urlStructureScore = 25; // Root page
    } else if (pathSegments.length === 1) {
      urlStructureScore = 20; // Primary sections
    } else if (pathSegments.length === 2) {
      urlStructureScore = 15; // Secondary pages
    } else {
      urlStructureScore = 10; // Deep nested
    }
    
    // Penalty for parameter-heavy URLs
    if (urlObj.search.length > 50) {
      urlStructureScore -= 5;
    }
  } catch {
    urlStructureScore = 5; // Invalid URL
  }

  // 2. Content Quality Score (0-20 points) - ENHANCED FOR BUSINESS RELEVANCE
  let contentQualityScore = 0;
  const contentLength = content.length;
  
  // Basic length scoring (max 15 points)
  if (contentLength >= 500 && contentLength <= 3030) {
    contentQualityScore = 15;
  } else if (contentLength >= 100 && contentLength < 500) {
    contentQualityScore = 12;
  } else if (contentLength > 5000) {
    contentQualityScore = 8;
  } else if (contentLength >= 50) {
    contentQualityScore = 5;
  }
  
  // BUSINESS CONTENT QUALITY CHECK (max 5 points bonus)
  const businessContentIndicators = [
    'investment', 'fund', 'capital', 'real estate', 'property', 'portfolio',
    'strategy', 'approach', 'criteria', 'philosophy', 'mission', 'vision',
    'services', 'capabilities', 'expertise', 'solutions', 'offerings',
    'company', 'firm', 'partnership', 'leadership', 'management',
    'financial', 'equity', 'debt', 'lending', 'financing', 'development'
  ];
  
  const businessContentCount = businessContentIndicators.filter(keyword => 
    contentLower.includes(keyword)
  ).length;
  
  // Penalize pages with insufficient business content
  if (businessContentCount < 2) {
    contentQualityScore = Math.max(contentQualityScore - 8, 0); // Heavy penalty
  } else if (businessContentCount >= 5) {
    contentQualityScore += 3; // Bonus for rich business content
  }
  
  // Penalize pages that are mostly personal/team content
  const personalContentIndicators = [
    'biography', 'bio', 'personal', 'individual', 'staff', 'employee',
    'team member', 'profile', 'background', 'education', 'experience'
  ];
  
  const personalContentCount = personalContentIndicators.filter(keyword => 
    contentLower.includes(keyword)
  ).length;
  
  if (personalContentCount > businessContentCount) {
    contentQualityScore = Math.max(contentQualityScore - 10, 0); // Heavy penalty for personal content
  }
  
  // Ensure content quality score doesn't exceed 20
  contentQualityScore = Math.min(contentQualityScore, 20);

  // 3. Page Type Classification Score (0-15 points) - PRIORITIZE BUSINESS PAGES
  let pageTypeScore = 0;
  const pageTypeKeywords = {
    homepage: ['home', 'index', ''],
    about: ['about', 'company', 'story', 'mission', 'vision', 'who-we-are'],
    services: ['services', 'capabilities', 'expertise', 'what-we-do'],
    investment: ['investment', 'philosophy', 'strategy', 'approach', 'criteria', 'portfolio', 'funds'],
    contact: ['contact', 'apply', 'get-started'],
    // Team pages get LOWER scores (not higher)
    team: ['team', 'leadership', 'management', 'principals', 'partners', 'executives']
  };

  // HIGH PRIORITY: Business-critical pages (max 12 points)
  if (pageTypeKeywords.homepage.some(k => urlLower.endsWith(`/${k}`) || urlLower.endsWith(k))) {
    pageTypeScore = 12; // Homepage gets highest score
  } else if (pageTypeKeywords.about.some(k => urlLower.includes(k))) {
    pageTypeScore = 10; // Company info
  } else if (pageTypeKeywords.investment.some(k => urlLower.includes(k))) {
    pageTypeScore = 11; // Investment content
  } else if (pageTypeKeywords.services.some(k => urlLower.includes(k))) {
    pageTypeScore = 9; // Services
  } else if (pageTypeKeywords.contact.some(k => urlLower.includes(k))) {
    pageTypeScore = 6; // Contact
  } else if (pageTypeKeywords.team.some(k => urlLower.includes(k))) {
    pageTypeScore = 3; // Team pages get low score
  }
  
  // ADDITIONAL BUSINESS RELEVANCE BOOST (max 3 points)
  const businessRelevanceKeywords = [
    'investment', 'fund', 'capital', 'real-estate', 'property', 'portfolio',
    'strategy', 'approach', 'criteria', 'philosophy', 'mission', 'vision',
    'services', 'capabilities', 'expertise', 'solutions', 'offerings',
    'about', 'company', 'firm', 'partnership', 'leadership'
  ];
  
  const hasBusinessRelevance = businessRelevanceKeywords.some(keyword => 
    urlLower.includes(keyword) || contentLower.includes(keyword)
  );
  
  if (hasBusinessRelevance) {
    pageTypeScore += 2; // Bonus for business relevance
  }
  
  // Ensure page type score doesn't exceed 15
  pageTypeScore = Math.min(pageTypeScore, 15);

  // 4. Investment Criteria Field Names Score (0-80 points) - HIGHEST PRECEDENCE
  let investmentCriteriaScore = 0;
  
  // Investment Criteria human-readable terms that appear on real company websites
  const investmentCriteriaFields = [
    // Investment Focus & Strategy
    'investment focus', 'investment strategy', 'investment approach', 'investment philosophy', 'investment criteria',
    'investment parameters', 'investment guidelines', 'investment preferences', 'investment objectives',
    'target investments', 'investment thesis', 'investment mandate', 'investment profile',
    
    // Deal Size & Capital
    'deal size', 'minimum deal size', 'maximum deal size', 'deal size range', 'transaction size',
    'minimum investment', 'maximum investment', 'investment size', 'deal size requirements',
    'capital requirements', 'minimum capital', 'maximum capital', 'capital range',
    'deal size criteria', 'transaction criteria', 'investment size criteria',
    
    // Geographic Focus
    'geographic focus', 'target markets', 'target regions', 'geographic preferences',
    'market focus', 'regional focus', 'geographic criteria', 'location preferences',
    'target locations', 'geographic scope', 'market coverage', 'regional coverage',
    
    // Property Types
    'property types', 'asset types', 'property focus', 'asset focus', 'property preferences',
    'target properties', 'property criteria', 'asset criteria', 'property requirements',
    'multifamily', 'office', 'retail', 'industrial', 'hospitality', 'mixed-use',
    'commercial real estate', 'residential', 'apartment', 'office building', 'shopping center',
    'warehouse', 'hotel', 'residential properties', 'commercial properties',
    
    // Investment Strategies
    'value-add', 'core', 'core-plus', 'opportunistic', 'development', 'acquisition',
    'stabilized', 'distressed', 'turnaround', 'repositioning', 'redevelopment',
    'ground-up development', 'repositioning strategy', 'value creation',
    
    // Capital Positions
    'senior debt', 'mezzanine', 'preferred equity', 'common equity', 'joint venture',
    'limited partner', 'general partner', 'co-gp', 'stretch senior', 'bridge loan',
    'construction loan', 'permanent financing', 'refinancing', 'acquisition financing',
    
    // Loan Terms & Rates
    'loan terms', 'loan structure', 'financing terms', 'loan parameters', 'loan criteria',
    'interest rate', 'rate structure', 'rate environment', 'pricing', 'loan pricing',
    'loan to value', 'ltv', 'loan to cost', 'ltc', 'debt service coverage', 'dscr',
    'loan amount', 'loan size', 'financing amount', 'loan capacity', 'credit facility',
    'credit line', 'revolving credit', 'term loan', 'bridge financing', 'permanent loan',
    
    // Fees & Costs
    'origination fee', 'loan fee', 'financing fee', 'arrangement fee', 'commitment fee',
    'exit fee', 'prepayment penalty', 'yield maintenance', 'defeasance', 'prepayment',
    'loan costs', 'financing costs', 'transaction costs', 'closing costs',
    
    // Return Requirements
    'target return', 'required return', 'minimum return', 'return expectations', 'return targets',
    'irr', 'internal rate of return', 'yield', 'yield requirements', 'yield targets',
    'cash flow', 'cash on cash', 'equity multiple', 'return multiple', 'profit multiple',
    'hold period', 'investment horizon', 'holding period', 'exit strategy', 'disposition',
    
    // Risk & Requirements
    'risk tolerance', 'risk profile', 'risk parameters', 'risk criteria', 'risk management',
    'leverage', 'leverage ratio', 'debt capacity', 'credit quality', 'credit requirements',
    'occupancy', 'occupancy requirements', 'tenant requirements', 'lease requirements',
    'due diligence', 'underwriting', 'approval process', 'decision process',
    
    // Timeline & Process
    'closing timeline', 'closing process', 'timeline', 'processing time', 'approval time',
    'underwriting time', 'due diligence period', 'closing period', 'funding timeline',
    'application process', 'approval process', 'decision timeline', 'response time',
    
    // Documentation & Requirements
    'documentation', 'required documents', 'application requirements', 'qualification criteria',
    'borrower requirements', 'sponsor requirements', 'guarantee requirements', 'recourse',
    'non-recourse', 'personal guarantee', 'corporate guarantee', 'financial statements',
    'audited financials', 'tax returns', 'bank statements', 'proof of funds',
    
    // Market & Economic Terms
    'market conditions', 'economic environment', 'interest rate environment', 'rate environment',
    'market outlook', 'economic outlook', 'market trends', 'economic trends', 'ltv', 'ltc', 'dscr',
    'sofr', 'libor', 'prime rate', 'treasury rate', 'benchmark rate', 'index rate',
    'rate lock', 'rate protection', 'hedging', 'interest rate hedge'
  ];

  // Check for Investment Criteria field names and business terms
  for (const field of investmentCriteriaFields) {
    if (!field) continue;
    const fieldLower = field.toLowerCase();
    const fieldRegex = new RegExp(`\\b${fieldLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
    
    // VERY HIGH WEIGHT for URL matches (Investment Criteria fields are most important)
    if (fieldRegex.test(urlLower)) {
      investmentCriteriaScore += 12; // Highest weight for field names
    }
    
    // VERY HIGH WEIGHT for content matches
    const contentMatches = (contentLower.match(fieldRegex) || []).length;
    if (contentMatches > 0) {
      investmentCriteriaScore += Math.min(contentMatches * 6, 18); // Highest multiplier
    }
  }

  // 5. Financial Numbers and Dollar Signs Score (0-40 points) - SECOND HIGHEST
  let financialNumbersScore = 0;
  
  // Dollar amounts ($1M, $5M, $100M, etc.)
  const dollarAmounts = contentLower.match(/\$[\d,]+(?:\.\d+)?[kmb]?/g) || [];
  financialNumbersScore += Math.min(dollarAmounts.length * 8, 20); // High weight for dollar amounts
  
  // Interest rates (5.5%, 12%, etc.)
  const interestRates = contentLower.match(/\d+(?:\.\d+)?%/g) || [];
  financialNumbersScore += Math.min(interestRates.length * 6, 15); // High weight for percentages
  
  // Loan terms and ratios (LTV, LTC, DSCR, etc.)
  const loanTerms = contentLower.match(/\b(?:ltv|ltc|dscr|irr|yield|multiple|ratio)\b/gi) || [];
  financialNumbersScore += Math.min(loanTerms.length * 4, 12); // Medium weight for loan terms
  
  // Numbers in general (deal sizes, loan amounts, etc.)
  const numbers = contentLower.match(/\b\d+(?:,\d{3})*(?:\.\d+)?\b/g) || [];
  financialNumbersScore += Math.min(numbers.length * 2, 8); // Lower weight for general numbers

  financialNumbersScore = Math.min(financialNumbersScore, 40);

  // 6. Business Keywords Score (0-30 points) - THIRD PRIORITY
  let businessKeywordsScore = 0;
  
  // Pre-compute lowercase arrays for efficient case-insensitive matching
  const strategiesLower = businessKeywords.strategies.map(k => k.toLowerCase());
  const propertyTypesLower = businessKeywords.propertyTypes.map(k => k.toLowerCase());
  const capitalPositionsLower = businessKeywords.capitalPositions.map(k => k.toLowerCase());
  
  // Count keyword matches in both URL and content
  const allKeywords = [
    ...businessKeywords.propertyTypes,
    ...businessKeywords.strategies,
    ...businessKeywords.capitalPositions,
    ...businessKeywords.loanPrograms,
    ...businessKeywords.loanTypes,
    ...businessKeywords.recourseLoan,
    ...businessKeywords.structuredLoanTranches
  ];

  // REDUCED WEIGHT for consolidated keywords (now third priority)
  for (const keyword of allKeywords) {
    if (!keyword) continue;
    const keywordLower = keyword.toLowerCase();
    const keywordRegex = new RegExp(`\\b${keywordLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
    
    // REDUCED WEIGHT for URL matches (now third priority)
    if (keywordRegex.test(urlLower)) {
      if (strategiesLower.includes(keywordLower)) businessKeywordsScore += 3; // Reduced from 8
      else if (propertyTypesLower.includes(keywordLower)) businessKeywordsScore += 2; // Reduced from 6
      else if (capitalPositionsLower.includes(keywordLower)) businessKeywordsScore += 2; // Reduced from 6
      else businessKeywordsScore += 1; // Reduced from 4
    }
    
    // REDUCED WEIGHT for content matches (now third priority)
    const contentMatches = (contentLower.match(keywordRegex) || []).length;
    if (contentMatches > 0) {
      if (strategiesLower.includes(keywordLower)) businessKeywordsScore += Math.min(contentMatches * 1.5, 4); // Reduced
      else if (propertyTypesLower.includes(keywordLower)) businessKeywordsScore += Math.min(contentMatches * 1, 3); // Reduced
      else businessKeywordsScore += Math.min(contentMatches * 0.5, 2); // Reduced
    }
  }

  businessKeywordsScore = Math.min(businessKeywordsScore, 30); // Reduced cap to 30

  // 5. Geographic Relevance Score (0-15 points)
  let geographicRelevanceScore = 0;
  const allGeoKeywords = [...businessKeywords.usRegions, ...businessKeywords.usStates];
  
  for (const geoKeyword of allGeoKeywords) {
    if (!geoKeyword) continue;
    const geoRegex = new RegExp(`\\b${geoKeyword.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
    
    if (geoRegex.test(urlLower)) {
      geographicRelevanceScore += businessKeywords.usRegions.includes(geoKeyword) ? 2 : 1;
    }
    if (geoRegex.test(contentLower)) {
      geographicRelevanceScore += businessKeywords.usRegions.includes(geoKeyword) ? 1 : 0.5;
    }
  }
  
  geographicRelevanceScore = Math.min(geographicRelevanceScore, 15); // Cap at 15

  // 6. URL Quality Bonus (-5 to +10 points)
  let urlQualityBonus = 0;
  
  // Clean, semantic URLs
  if (urlLower.match(/^https?:\/\/[^\/]+\/[a-z-]+\/?$/)) {
    urlQualityBonus += 3;
  }
  
  // Contains relevant business keywords
  const hasBusinessKeywords = allKeywords.some(k => k && urlLower.includes(k.toLowerCase()));
  if (hasBusinessKeywords) {
    urlQualityBonus += 2;
  }
  
  // Penalties for messy URLs
  if (urlLower.includes('?') && urlLower.split('?')[1].split('&').length > 3) {
    urlQualityBonus -= 3; // Too many parameters
  }
  if (urlLower.length > 100) {
    urlQualityBonus -= 2; // Very long URLs
  }
  if (urlLower.match(/\/\d+/)) {
    urlQualityBonus -= 1; // Contains numeric IDs
  }

  const totalScore = urlStructureScore + contentQualityScore + pageTypeScore + 
                    investmentCriteriaScore + financialNumbersScore + businessKeywordsScore + 
                    geographicRelevanceScore + urlQualityBonus;

  // Calculate theoretical maximum: 25 + 20 + 15 + 80 + 40 + 30 + 15 + 10 = 235
  // But we'll cap it at 200 for practical purposes
  const maxPossibleScore = 200;
  const cappedTotalScore = Math.min(Math.max(totalScore, 0), maxPossibleScore);

  return {
    url_structure_score: urlStructureScore,
    content_quality_score: contentQualityScore,
    page_type_score: pageTypeScore,
    investment_criteria_score: investmentCriteriaScore,
    financial_numbers_score: financialNumbersScore,
    business_keywords_score: businessKeywordsScore,
    geographic_relevance_score: geographicRelevanceScore,
    url_quality_bonus: urlQualityBonus,
    total_score: cappedTotalScore
  };
}

/**
 * Rank pages for a company and assign relevance ranks
 */
async function rankPagesForCompany(pages: ProcessUrlResult[], businessKeywords: BusinessKeywords): Promise<CrawledPageData[]> {
  console.log(`Ranking ${pages.length} pages...`);
  
  // Calculate scores for all pages
  const scoredPages = pages.map(page => {
    const rankingFactors = calculatePageRelevance(page.url, page.text, businessKeywords);
    return {
      ...page,
      rankingFactors,
      totalScore: rankingFactors.total_score
    };
  });

  // FILTER OUT IRRELEVANT PAGES (negative scores)
  const relevantPages = scoredPages.filter(page => page.totalScore > 0);
  const irrelevantPages = scoredPages.filter(page => page.totalScore <= 0);
  
  console.log(`Filtered out ${irrelevantPages.length} irrelevant pages (team members, press, etc.)`);
  console.log(`Ranking ${relevantPages.length} relevant pages...`);

  // Sort by total score (highest first) and assign ranks
  relevantPages.sort((a, b) => b.totalScore - a.totalScore);
  
  const rankedPages = relevantPages.map((page, index) => ({
    id: uuidv4(),
    company_id: 0, // Will be set by caller
    url: page.url,
    parent_url: page.parentUrl || null,
    crawl_depth: page.depth,
    crawl_run_id: '', // Will be set by caller
    extracted_text: page.text,
    relevance_rank: index + 1,
    rank_factors: page.rankingFactors
  }));

  console.log('Pages ranked successfully. Top 3 RELEVANT pages:');
  rankedPages.slice(0, 3).forEach((page, i) => {
    console.log(`  ${i + 1}. ${page.url} (score: ${page.rank_factors?.total_score})`);
  });
  
  if (irrelevantPages.length > 0) {
    console.log('Filtered out irrelevant pages:');
    irrelevantPages.slice(0, 3).forEach((page, i) => {
      console.log(`  - ${page.url} (score: ${page.totalScore})`);
    });
  }

  return rankedPages;
}

/**
 * Main function to crawl website and find nested URLs related to real estate
 * Returns an array of ProcessUrlResult objects with parentUrl, url, text, and depth
 */
export async function getWebsiteUrls(
  startUrl: string,
  options: CrawlOptions = {}
): Promise<ProcessUrlResult[]> {
  const { maxPages = 1000, maxDepth = 10 } = options;
  const normalizedUrl = cleanUrl(startUrl);

  if (!normalizedUrl) {
    console.error(`Invalid URL: ${startUrl}`);
    return [];
  }

  // Use shared browser pool to prevent memory leaks
  await browserPool.initialize();
  const { browser, context } = await browserPool.getBrowserAndContext();

  const results: ProcessUrlResult[] = [];
  const resultUrls = new Set<string>();
  const visited = new Set<string>();
  const queue: Array<{ url: string; parentUrl: string | null; depth: number }> = [
    { url: normalizedUrl, parentUrl: null, depth: 0 }
  ];

  try {
    const domain = new URL(normalizedUrl).hostname;

    // Safety mechanisms
    const startTime = Date.now();
    const maxCrawlTimeMs = 60 * 60 * 1000; // 1 hour maximum
    let pagesProcessed = 0;

    while (queue.length > 0 && results.length < maxPages) {
      // Safety check: maximum crawl time
      if (Date.now() - startTime > maxCrawlTimeMs) {
        console.log(`Stopping legacy crawl after ${Math.round((Date.now() - startTime) / 1000 / 60)} minutes for safety`);
        break;
      }
      const { url, parentUrl, depth } = queue.shift()!;

      if (visited.has(url) || depth > maxDepth) {
        continue;
      }

      visited.add(url);

      try {
        const page = await context.newPage();
        page.setDefaultTimeout(60000); // 60 seconds for pages with animations

        await page.goto(url, { waitUntil: 'domcontentloaded' });
        await page.waitForTimeout(2000); // Wait for dynamic content

        // Wait for common dynamic content indicators
        try {
          // Wait for any loading indicators to disappear
          await page.waitForFunction(
            () => !document.querySelector('.loading, .spinner, [class*="loading"], [class*="spinner"]'),
            { timeout: 10000 }
          ).catch(() => {}); // Continue if not found
          
          // Wait for images to load (common lazy loading trigger)
          await page.waitForLoadState('networkidle', { timeout: 15000 }).catch(() => {});
          
          // Wait for common "load more" or pagination elements to appear
          await page.waitForSelector('[data-load-more], [class*="load-more"], [class*="pagination"], [class*="infinite"], .more-content, #load-more', { timeout: 5000 }).catch(() => {});
        } catch (error) {
          console.log('Dynamic content wait completed or timed out');
        }

        // Click "VIEW MORE", "SEE ALL", and similar buttons to load additional content
        await clickLoadMoreButtons(page);

        // Wait for dynamic content to complete (includes smooth scrolling, network idle, and 4s timeout)
        await waitForDynamicContentComplete(page);

        const content = await page.content();
        const text = await extractCleanText(content);

        // Smart screenshot strategy based on ranking and content type
        let screenshot: Buffer | undefined;
        
        // Only capture screenshots for high-ranking pages or pages with dynamic content
        const shouldCaptureScreenshot = (
          results.length < 5 || // Top 5 pages always get screenshots
          (results.length < 10 && await hasDynamicContent(page)) || // Top 10 if dynamic content
          (results.length < 15 && await hasFinancialContent(page)) // Top 15 if financial content
        );
        
        if (shouldCaptureScreenshot) {
          try {
            screenshot = await page.screenshot({
              fullPage: true,
              type: 'png'
            });
            console.log(`📸 Screenshot captured for ${url} (${screenshot?.length || 0} bytes) - Rank: ${results.length + 1}`);
          } catch (screenshotError) {
            console.warn(`⚠️ Failed to capture screenshot for ${url}:`, screenshotError);
          }
        } else {
          console.log(`📄 Skipping screenshot for ${url} - Low rank (${results.length + 1})`);
        }

        // Only add pages with meaningful content
        if (text && text.length > 100) {
          if (!resultUrls.has(url)) {
            results.push({
              parentUrl: parentUrl || '',
              url,
              text,
              screenshot: screenshot || undefined, // Include screenshot for multimodal analysis
              depth
            });
            resultUrls.add(url);
            pagesProcessed++;
            
            // Progress logging for unlimited crawling
            if (pagesProcessed % 10 === 0) {
              const elapsed = Math.round((Date.now() - startTime) / 1000);
              const rate = (pagesProcessed / elapsed * 60).toFixed(1);
              console.log(`📊 Legacy crawler progress: ${pagesProcessed} pages (${rate} pages/min)`);
            }
          }
        }

        // Extract links for next level if not at max depth
        if (depth < maxDepth) {
          const links = extractSameDomainLinks(content, url, domain);
          for (const link of links) {
            if (!visited.has(link) && results.length < maxPages) {
              queue.push({ url: link, parentUrl: url, depth: depth + 1 });
            }
          }
        }

        await page.close();
        console.log(`🧹 Closed page for ${url}`);
      } catch (error) {
        console.error(`Error crawling ${url}:`, error);
      }
    }

    // Don't close shared browser/context - they're managed by the pool
    console.log('🔄 Returning browser to shared pool');
  } catch (error) {
    console.error(`Error in getWebsiteUrls:`, error);
  }

  return results;
}


/**
 * Extract clean text from HTML content
 */
async function extractCleanText(html: string): Promise<string> {
  try {
    const $ = cheerio.load(html);

    // Remove script and style elements
    $('script, style, nav, footer, header, aside, .nav, .footer, .header, .sidebar').remove();

    // Decode Cloudflare obfuscated emails before extracting text
    const decodedHTML = await decodeCFEmails(html);
    const $decoded = cheerio.load(decodedHTML);

    // Get text content from decoded HTML
    let text = $decoded('body').text() || '';

    // Clean up whitespace
    text = text.replace(/\s+/g, ' ').trim();

    return text;
  } catch (error) {
    console.error('Error extracting text:', error);
    return '';
  }
}

/**
 * Decode Cloudflare obfuscated emails in HTML content
 */
async function decodeCFEmails(html: string): Promise<string> {
  try {
    // Import CFEmailDecoder dynamically to avoid circular dependencies
    const { CFEmailDecoder } = await import('./cf-email-decoder');
    return CFEmailDecoder.decodeEmailsInHTML(html);
  } catch (error) {
    console.warn('Failed to decode CF emails:', error);
    return html; // Return original HTML if decoding fails
  }
}

/**
 * Extract links from HTML within the same domain using exclusion-only filtering
 * (no topic-based allow-list). Also reads common data- attributes and JS patterns.
 */
function extractSameDomainLinks(html: string, baseUrl: string, domain: string): string[] {
  try {
    const $ = cheerio.load(html);
    const links: string[] = [];

    // Extract links from href attributes
    $('a[href]').each((_, element) => {
      let href = $(element).attr('href');
      if (!href) return;

      // Skip anchors, mailto, tel, etc.
      if (href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:')) {
        return;
      }

      // Resolve relative URLs
      if (!href.startsWith('http')) {
        try {
          href = new URL(href, baseUrl).toString();
        } catch {
          return; // Skip invalid URLs
        }
      }

      try {
        const linkUrl = new URL(href);

        // Only include links from the same domain
        if (linkUrl.hostname !== domain) {
          return;
        }

        // Exclusion-only: allow same-domain pages that look like real pages
        if (!isValidPageUrl(href)) return;
        let normalizedUrl = `${linkUrl.protocol}//${linkUrl.hostname}${linkUrl.pathname}`;
        if (linkUrl.search) {
          normalizedUrl += linkUrl.search;
        }
        links.push(normalizedUrl);
      } catch {
        // Skip invalid URLs
      }
    });

    // Also extract links from data attributes and JavaScript (common in SPAs)
    $('[data-href], [data-url], [data-link]').each((_, element) => {
      const dataHref = $(element).attr('data-href') || $(element).attr('data-url') || $(element).attr('data-link');
      if (!dataHref) return;

      // Process similar to href links
      let href = dataHref;
      if (!href.startsWith('http')) {
        try {
          href = new URL(href, baseUrl).toString();
        } catch {
          return;
        }
      }

      try {
        const linkUrl = new URL(href);
        if (linkUrl.hostname === domain && isValidPageUrl(href)) {
          let normalizedUrl = `${linkUrl.protocol}//${linkUrl.hostname}${linkUrl.pathname}`;
          if (linkUrl.search) {
            normalizedUrl += linkUrl.search;
          }
          links.push(normalizedUrl);
        }
      } catch {
        // Skip invalid URLs
      }
    });

    // Extract URLs from JavaScript code (basic regex for common patterns)
    const jsUrlRegex = /(?:href|url|link)\s*[:=]\s*["']([^"']+)["']/gi;
    let match;
    while ((match = jsUrlRegex.exec(html)) !== null) {
      let href = match[1];
      if (!href.startsWith('http')) {
        try {
          href = new URL(href, baseUrl).toString();
        } catch {
          continue;
        }
      }

      try {
        const linkUrl = new URL(href);
        if (linkUrl.hostname === domain && isValidPageUrl(href)) {
          let normalizedUrl = `${linkUrl.protocol}//${linkUrl.hostname}${linkUrl.pathname}`;
          if (linkUrl.search) {
            normalizedUrl += linkUrl.search;
          }
          links.push(normalizedUrl);
        }
      } catch {
        // Skip invalid URLs
      }
    }

    return [...new Set(links)]; // Remove duplicates
  } catch (error) {
    console.error('Error extracting links:', error);
    return [];
  }
}

/**
 * Check if a URL is related to real estate based on keywords
 */
function isRealEstateRelated(url: string): boolean {
  const urlLower = url.toLowerCase();

  // Exclude certain types of pages
  for (const keyword of excludeKeywords) {
    if (urlLower.includes(keyword.toLowerCase())) {
      return false;
    }
  }

  // Check for real estate related keywords
  for (const keyword of realEstateKeywords) {
    if (urlLower.includes(keyword.toLowerCase())) {
      return true;
    }
  }

  return false;
}
async function sendRequest(url: string, retries: number = 0): Promise<any> {
  try {
    if (retries > MAX_RETRIES) {
      throw new Error(`Max Retries Exceeded for ${url}`);
    }
    const response = await httpLimiter.schedule(() => fetch(url, { 
      signal: AbortSignal.timeout(60000) 
    } as any));
    // catch the redirected url
    // if (response.code === 301 || response.code === 302) {
    //   console.log("Redirected to:", response);
    //   console.log("Redirected to:", response.headers.location);
    //   return sendRequest(response.headers.location, retries + 1);
    // }
    return response;
  } catch (error: any) {
    console.error("Error Stack:", error.stack);
    console.error("Error in sendRequest:", error);
    if (
      url.startsWith("https") &&
      error.name === "FetchError" &&
      error.code === "DEPTH_ZERO_SELF_SIGNED_CERT"
    ) {
      return sendRequest(url.replace("https", "http"), retries + 1);
    }
    throw error;
  }
}

function cleanUrl(myUrl: string): string {
  //   console.log("myUrl", myUrl);
  if (!myUrl.startsWith("http")) {
    myUrl = "https://" + myUrl;
  }
  const url = new URL(myUrl);
  let netloc = url.hostname || url.pathname;
  let path = url.pathname;
  if (!netloc.startsWith("www.")) {
    netloc = "www." + netloc;
  }
  return `https://${netloc}${path}`;
}


function validRelatedUrl(url: string): boolean {
  const urlLower = url.toLowerCase();
  
  // Exclude certain types of pages
  const hasExcludeKeyword = excludeKeywords.some((keyword: string) => urlLower.includes(keyword.toLowerCase()));
  if (hasExcludeKeyword) {
    return false;
  }
  
  // More permissive approach - include pages that are likely relevant for financial/real estate companies
  const financialKeywords = [
    "loan", "lending", "finance", "financing", "capital", "fund", "invest", "investment", 
    "apply", "application", "borrow", "borrower", "credit", "debt", "equity", "mortgage",
    "bridge", "hard-money", "private-money", "real-estate", "property", "commercial", 
    "residential", "development", "construction", "acquisition", "refinance"
  ];
  
  // Check for financial/lending keywords
  const hasFinancialKeyword = financialKeywords.some((keyword: string) => urlLower.includes(keyword));
  if (hasFinancialKeyword) {
    return true;
  }
  
  // Check for original real estate keywords
  const hasRealEstateKeyword = realEstateKeywords.some((keyword: string) => urlLower.includes(keyword));
  if (hasRealEstateKeyword) {
    return true;
  }
  
  // For main company pages (about, services, etc.), always include them
  const companyPageKeywords = [
    "about", "story", "leadership", "contact", "services", "what-we-do",
    "how-it-works", "process", "why-choose", "benefits", "rates", "terms", "faq",
    "apply", "get-started", "calculator", "form", "success"
  ];
  
  const hasCompanyKeyword = companyPageKeywords.some((keyword: string) => urlLower.includes(keyword));
  if (hasCompanyKeyword) {
    return true;
  }
  
  // Also allow root pages and simple paths (no deep nested paths with IDs)
  try {
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/').filter(s => s.length > 0);
    
    // Allow root page
    if (pathSegments.length === 0) {
      return true;
    }
    
    // Allow simple paths (not too deep or with numeric IDs)
    if (pathSegments.length <= 3 && !pathSegments.some(segment => /^\d+$/.test(segment))) {
      return true;
    }
  } catch {
    // If URL parsing fails, be conservative
  }
  
  return false;
}

function validSitemapUrl(url: string): boolean {
  return url.includes("sitemap");
}

function isValidPageUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const path = urlObj.pathname.toLowerCase();
    
    // Skip URLs with file extensions (except html/htm/php)
    const fileExtensions = [
      '.xml', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.zip', '.rar', '.tar', '.gz', '.jpg', '.jpeg', '.png', '.gif', '.svg',
      '.mp4', '.avi', '.mov', '.css', '.js', '.json', '.txt', '.csv',
      '.rss', '.atom', '.ico', '.woff', '.woff2', '.ttf', '.eot'
    ];
    
    const hasInvalidExtension = fileExtensions.some(ext => path.endsWith(ext));
    if (hasInvalidExtension) {
      return false;
    }
    
    // Allow paths ending with .html, .htm, .php, or no extension
    const validExtensions = ['.html', '.htm', '.php'];
    const hasExtension = path.includes('.') && !path.endsWith('/');
    if (hasExtension && !validExtensions.some(ext => path.endsWith(ext))) {
      return false;
    }
    
    return true;
  } catch {
    return false;
  }
}

class GlobalUrlTracker {
  private urls = new Map<string, UrlTrackingItem>();
  private domain: string;
  private canonicalHost: string;
  private baseDomainNormalized: string;
  
  constructor(domain: string) {
    this.domain = domain;
    this.canonicalHost = domain;
    this.baseDomainNormalized = this.normalizeHostname(domain); // example.com
  }
  
  private normalizeHostname(hostname: string): string {
    return hostname.replace(/^www\./i, "").toLowerCase();
  }

  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      
      // Normalize hostname (remove www prefix for comparison)
      const normalizedHost = this.normalizeHostname(urlObj.hostname);
      
      // Build normalized URL with consistent format
      const normalized = new URL(url);
      normalized.hostname = normalizedHost;
      normalized.hash = ''; // Remove hash fragments
      normalized.search = normalized.search; // Keep query params
      
      // Ensure pathname starts with / if it's not empty
      if (normalized.pathname === '') {
        normalized.pathname = '/';
      }
      
      return normalized.toString();
    } catch {
      return url; // Return original if parsing fails
    }
  }

  normalizeUrlForStorage(url: string): string {
    return this.normalizeUrl(url);
  }

  addUrl(url: string, parentUrl: string | null, depth: number, source: UrlTrackingItem['source']): boolean {
    try {
      const urlObj = new URL(url);
      
      // Only track URLs from the same domain (treat www/non-www as same)
      const urlHostNormalized = this.normalizeHostname(urlObj.hostname);
      if (urlHostNormalized !== this.baseDomainNormalized) {
        return false;
      }
      
      // Skip invalid page URLs
      if (!isValidPageUrl(url)) {
        return false;
      }
      
      // Normalize URL to avoid duplicates
      const normalizedUrl = this.normalizeUrl(url);
      
      // Skip if already tracked
      if (this.urls.has(normalizedUrl)) {
        console.log(`Skipping duplicate URL: ${url} -> ${normalizedUrl}`);
        return false;
      }
      
      console.log(`Adding new URL: ${url} -> ${normalizedUrl}`);
      this.urls.set(normalizedUrl, {
        url: normalizedUrl,
        parentUrl,
        depth,
        status: 'pending',
        discoveredAt: Date.now(),
        source
      });
      
      return true;
    } catch (error) {
      console.error(`Error adding URL ${url}:`, error);
      return false;
    }
  }
  
  getNextPendingUrl(): UrlTrackingItem | null {
    for (const item of this.urls.values()) {
      if (item.status === 'pending') {
        return item;
      }
    }
    return null;
  }
  
  updateStatus(url: string, status: UrlTrackingItem['status']): void {
    const item = this.urls.get(url);
    if (item) {
      item.status = status;
    }
  }
  
  getCompletedUrls(): UrlTrackingItem[] {
    return Array.from(this.urls.values()).filter(item => item.status === 'completed');
  }
  
  getPendingCount(): number {
    return Array.from(this.urls.values()).filter(item => item.status === 'pending').length;
  }
  
  getStats() {
    const items = Array.from(this.urls.values());
    return {
      total: items.length,
      pending: items.filter(i => i.status === 'pending').length,
      processing: items.filter(i => i.status === 'processing').length,
      completed: items.filter(i => i.status === 'completed').length,
      failed: items.filter(i => i.status === 'failed').length,
      sources: {
        sitemap: items.filter(i => i.source === 'sitemap').length,
        page_links: items.filter(i => i.source === 'page_links').length,
        initial: items.filter(i => i.source === 'initial').length
      }
    };
  }
}

function getHostnameVariants(hostname: string): string[] {
  const noWww = hostname.replace(/^www\./i, "");
  const withWww = hostname.startsWith("www.") ? hostname : `www.${noWww}`;
  return [noWww, withWww];
}

function buildBaseUrlVariants(inputUrl: string): string[] {
  const u = inputUrl.startsWith("http") ? new URL(inputUrl) : new URL(`https://${inputUrl}`);
  const hostVariants = getHostnameVariants(u.hostname);
  const protocols = ["https:", "http:"];
  const variants: string[] = [];
  for (const proto of protocols) {
    for (const host of hostVariants) {
      variants.push(`${proto}//${host}/`);
    }
  }
  return Array.from(new Set(variants));
}

async function fetchRobotsTxtSitemaps(robotsUrl: string): Promise<string[]> {
  const list: string[] = [];
  try {
    const res = await sendRequest(robotsUrl);
    if (res.status !== 200) return list;
    const text = await res.text();
    const lines = text.split(/\r?\n/);
    for (const line of lines) {
      const m = line.match(/^\s*Sitemap:\s*(.+)\s*$/i);
      if (m && m[1]) {
        const loc = m[1].trim();
        list.push(loc);
      }
    }
  } catch {
    // ignore
  }
  return list;
}

async function bfsDiscoverSitemapUrls(baseUrl: string, urlTracker: GlobalUrlTracker): Promise<void> {
  const baseVariants = buildBaseUrlVariants(baseUrl);
  const sitemapQueue: string[] = [];
  const processedSitemaps = new Set<string>();
  
  // Add initial sitemap candidates
  for (const base of baseVariants) {
    for (const template of sitemapTemplates) {
      const sitemapUrl = base.endsWith("/") ? base + template : base + "/" + template;
      sitemapQueue.push(sitemapUrl);
    }
  }
  
  // Add sitemaps from robots.txt
  for (const base of baseVariants) {
    const robotsUrl = base.endsWith("/") ? base + "robots.txt" : base + "/robots.txt";
    const robotsSitemaps = await fetchRobotsTxtSitemaps(robotsUrl);
    for (const sm of robotsSitemaps) {
      if (!processedSitemaps.has(sm)) {
        sitemapQueue.push(sm);
      }
    }
  }
  
  console.log(`Starting BFS sitemap discovery with ${sitemapQueue.length} initial sitemaps`);
  
  // BFS processing of sitemaps
  while (sitemapQueue.length > 0) {
    const currentSitemapUrl = sitemapQueue.shift()!;
    
    if (processedSitemaps.has(currentSitemapUrl)) {
      continue;
    }
    
    processedSitemaps.add(currentSitemapUrl);
    console.log(`Processing sitemap: ${currentSitemapUrl}`);
    
    try {
      const response = await sendRequest(currentSitemapUrl);
      if (response.status !== 200) {
        continue;
      }
      
      const isGzip = currentSitemapUrl.endsWith('.gz') || String(response.headers.get('content-encoding') || '').includes('gzip');
      let xmlContent: string;
      
      if (isGzip) {
        const raw: Buffer = await response.arrayBuffer().then((b: any) => Buffer.from(b));
        try {
          xmlContent = gunzipSync(raw).toString('utf-8');
        } catch {
          // If gzip decompression fails, fall back to text
          const response2 = await sendRequest(currentSitemapUrl);
          xmlContent = await response2.text();
        }
      } else {
        xmlContent = await response.text();
      }
      
      const sitemapJson = xmlParser.xml2js(xmlContent, { compact: true }) as any;
      
      // Handle sitemap index - add child sitemaps to queue
      if (sitemapJson.sitemapindex && sitemapJson.sitemapindex.sitemap) {
        const items = Array.isArray(sitemapJson.sitemapindex.sitemap) ? sitemapJson.sitemapindex.sitemap : [sitemapJson.sitemapindex.sitemap];
        let addedChildSitemaps = 0;
        for (const sm of items) {
          const loc = (sm.loc?._text || sm.loc)?.toString().trim();
          if (!loc) continue;
          if (!processedSitemaps.has(loc)) {
            sitemapQueue.push(loc);
            addedChildSitemaps++;
          }
        }
        console.log(`Found ${addedChildSitemaps} child sitemaps in sitemap index`);
      }
      
      // Handle URL set - add pages to tracker (do not filter by topic; rely on domain + extension checks)
      if (sitemapJson.urlset && sitemapJson.urlset.url) {
        const items = Array.isArray(sitemapJson.urlset.url) ? sitemapJson.urlset.url : [sitemapJson.urlset.url];
        let addedPages = 0;
        let skippedInvalid = 0;
        for (const urlItem of items) {
          const loc = (urlItem.loc?._text || urlItem.loc)?.toString().trim();
          if (!loc) continue;
          
          console.log(`  Found URL in sitemap: ${loc}`);
          
          if (urlTracker.addUrl(loc, baseUrl, 0, 'sitemap')) {
            console.log(`    ✅ Added to tracker: ${loc}`);
            addedPages++;
          } else {
            console.log(`    ❌ Skipped by tracker: ${loc}`);
            skippedInvalid++;
          }
        }
        console.log(`From sitemap ${currentSitemapUrl}: ${addedPages} added, ${skippedInvalid} skipped/invalid`);
      }
      
    } catch (error) {
      console.error(`Error processing sitemap ${currentSitemapUrl}:`, error);
    }
  }
  
  console.log(`BFS sitemap discovery completed. Processed ${processedSitemaps.size} sitemaps.`);
}

async function fetchSitemapUrlsV2(base_url: string, url: string): Promise<string[]> {
    console.log("Fetching Sitemap Urls:", url);
    const spanElements: string[] = [];
    try {
      const response = await sendRequest(url);
      if (response.status === 200) {
        const htmlContent = await response.text();
        const $ = cheerio.load(htmlContent);
  
        $("span").each((_, span) => {
          const spanText = $(span).text().trim();
          console.log("spanText", spanText);
          if (spanText.includes(base_url)) {
            if (!spanElements.includes(spanText)) spanElements.push(spanText);
          }
        });
      }
      // else {
      //   console.error(`Failed to Fetch Sitemap Urls ${url}: ${response.status}`);
      // }
      return spanElements;
    } catch (error: any) {
      console.error("Error Stack:", error.stack);
      console.error(
        `An error occurred while fetching sitemap from ${url}: ${error}`
      );
      return spanElements;
    }
  }


async function getRelatedUrls(baseUrl: string): Promise<string[]> {
  const updatedUrls: string[] = [];
  const seen = new Set<string>();
  try {
    const sitemapUrls = sitemapTemplates.map((template) =>
      (baseUrl.endsWith("/")) ? baseUrl + template : baseUrl + "/" + template
    );
    for (const sitemapUrl of sitemapUrls) {
      // NOTE: fetchSitemapUrls was replaced by bfsDiscoverSitemapUrls
      // This legacy function is kept for compatibility; we'll skip direct calls here
      const urls: string[] = [];
      const urlsV2 = await fetchSitemapUrlsV2(baseUrl, sitemapUrl);
      for (const u of urls) {
        if (!seen.has(u)) { seen.add(u); updatedUrls.push(u); }
      }
      for (const u of urlsV2) {
        if (!seen.has(u)) { seen.add(u); updatedUrls.push(u); }
      }
    }
    const response = await sendRequest(baseUrl);
    if (response.status === 200) {
      const html = await response.text();
      const $ = cheerio.load(html);
      const modifiedUrl = baseUrl.split(".").slice(-2).join(".");
      const relatedUrls: string[] = [];
      
      $("a[href]").each((_, link) => {
        const href = $(link).attr("href");
        if (href && (
          href.startsWith("/") ||
          href.includes(baseUrl) ||
          href.includes(modifiedUrl)
        )) {
          const cleanedUrl = cleanUrl(href.startsWith("/") ? baseUrl + href : href);
          if (validRelatedUrl(cleanedUrl)) {
            if (!seen.has(cleanedUrl)) { seen.add(cleanedUrl); relatedUrls.push(cleanedUrl); }
          }
        }
      });
      
      for (const u of relatedUrls) {
        if (!seen.has(u)) { seen.add(u); updatedUrls.push(u); }
      }
    } else if (response.status === 202) {
      console.log(
        `Received 202 Accepted. Retrying in ${RETRY_DELAY / 1000} seconds...`
      );
      await sleep(RETRY_DELAY);
      return getRelatedUrls(baseUrl);
    } else {
      console.error(
        `Failed Request to Get Related urls ${baseUrl}: ${response.status}`
      );
    }
    return updatedUrls;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    // console.error(`Failed to Get Related urls ${baseUrl}: ${error}`);
    return updatedUrls;
  }
}

/**
 * Classify pages with metadata using LLM
 */
async function classifyPagesWithMetadata(
  pages: CrawledPageData[],
  originalPages: ProcessUrlResult[], // Original pages with HTML content
  companyName: string,
  llmProvider: any,
  logger: (level: string, message: string) => void
): Promise<CrawledPageData[]> {
  const classifier = new PageMetadataClassifier(llmProvider);
  const classifiedPages: CrawledPageData[] = [];
  
  // Focus on top-ranked pages first (limit to top 20 for cost efficiency)
  const topPages = pages.slice(0, 20);
  logger('info', `Classifying top ${topPages.length} pages with metadata...`);
  
  for (let i = 0; i < topPages.length; i++) {
    const page = topPages[i];
    
    try {
      logger('info', `Classifying page ${i + 1}/${topPages.length}: ${page.url}`);
      
      // Find the original page with screenshot
      const originalPage = originalPages.find(orig => orig.url === page.url);
      const screenshot = originalPage?.screenshot;
      
      // Extract title from content (simple approach)
      const titleMatch = page.extracted_text.match(/^(.{0,200})/);
      const title = titleMatch ? titleMatch[1].trim() : 'No title';
      
      const context: ClassificationContext = {
        url: page.url,
        title: title,
        content: page.extracted_text, // Use extracted text as reference
        screenshot: screenshot, // Include screenshot for multimodal analysis
        company_name: companyName,
        page_rank: page.relevance_rank || 999,
        ranking_factors: page.rank_factors
      };
      
      const rawYamlMetadata = await classifier.classifyPage(context);
      
      // Create enhanced page data with raw YAML metadata only
      const enhancedPage: CrawledPageData = {
        ...page,
        page_metadata: rawYamlMetadata // Store raw YAML only
      };
      
      classifiedPages.push(enhancedPage);
      
      logger('info', `Classified page with metadata (${rawYamlMetadata.length} chars)`);
      
      // IMMEDIATE MEMORY CLEANUP: Free screenshot memory after classification
      if (originalPage && originalPage.screenshot) {
        originalPage.screenshot = undefined; // Free memory immediately
        logger('debug', `🧹 Freed screenshot memory for ${page.url}`);
      }
      
      // Add delay to avoid rate limiting
      if (i < topPages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error) {
      logger('warn', `Failed to classify page ${page.url}: ${error}`);
      // Keep the original page without metadata
      classifiedPages.push(page);
    }
  }
  
  // Add remaining pages without classification
  const remainingPages = pages.slice(20);
  classifiedPages.push(...remainingPages);
  
  logger('info', `Metadata classification completed. Classified ${topPages.length} pages, ${remainingPages.length} pages kept as-is.`);
  
  return classifiedPages;
}

/**
 * Comprehensive crawling function that handles everything
 * This is the main function that should be called by CompanyWebCrawlerProcessor
 */
export async function crawlWebsiteComprehensive(
  companyId: number,
  websiteUrl: string,
  options: { 
    maxPages?: number; 
    maxDepth?: number; 
    db?: DatabaseConnection; 
    logger?: (level: string, message: string) => void;
    llmProvider?: any;
    companyName?: string;
    enableMetadataClassification?: boolean;
  } = {}
): Promise<CrawlResult> {
  const { 
    maxPages = 1000, 
    maxDepth = 10, 
    db, 
    logger = console.log,
    llmProvider,
    companyName = 'Unknown Company',
    enableMetadataClassification = true
  } = options;
  
  try {
    logger('info', `Starting comprehensive crawling for company ${companyId}: ${websiteUrl}`);
    
    // Clear existing pages for this company if database is provided
    if (db) {
      await clearExistingPages(db, companyId, logger);
    }
    
    // Get all URLs to crawl
    const urlsToCrawl = await processUrl(websiteUrl, { maxPages, maxDepth });
    logger('info', `Discovered ${urlsToCrawl.length} URLs to crawl`);
    
    if (urlsToCrawl.length === 0) {
      return { success: false, pageCount: 0, error: 'No URLs found to crawl' };
    }
    
    // Rank pages using business keywords
    logger('info', 'Fetching business keywords for ranking...');
    const businessKeywords = await fetchBusinessKeywords();
    
    logger('info', 'Ranking pages based on relevance...');
    const rankedPages = await rankPagesForCompany(urlsToCrawl, businessKeywords);
    
    // Classify pages with metadata if enabled and LLM provider is available
    let classifiedPages = rankedPages;
    if (enableMetadataClassification && llmProvider && rankedPages.length > 0) {
      logger('info', 'Classifying pages with metadata...');
      classifiedPages = await classifyPagesWithMetadata(
        rankedPages, 
        urlsToCrawl, // Pass original pages with HTML content
        companyName, 
        llmProvider, 
        logger
      );
    }
    
    // Store ranked and classified pages in database
    const runId = uuidv4();
    let storedCount = 0;
    const storedUrls = new Set<string>();
    
    for (const pageData of classifiedPages) {
      try {
        if (db) {
          // Normalize URL for storage deduplication
          const normalizedUrl = normalizeUrlForDeduplication(pageData.url);
          
          // Skip if already stored (additional safety check)
          if (storedUrls.has(normalizedUrl)) {
            logger('debug', `Skipping duplicate URL at storage level: ${pageData.url} -> ${normalizedUrl}`);
            continue;
          }
          
          // Add to stored URLs set
          storedUrls.add(normalizedUrl);
          
          // Set the company_id and run_id
          pageData.company_id = companyId;
          pageData.crawl_run_id = runId;
          pageData.parent_url = pageData.url === websiteUrl ? null : (pageData.parent_url || websiteUrl);
          
          await storePage(db, pageData);
          storedCount++;
        }
      } catch (error) {
        logger('warn', `Failed to store page ${pageData.url}: ${error}`);
      }
    }
    
    logger('info', `Successfully crawled and stored ${storedCount} pages for company ${companyId}`);
    return { success: true, pageCount: storedCount };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger('error', `Error in comprehensive crawling for company ${companyId}: ${errorMessage}`);
    return { success: false, pageCount: 0, error: errorMessage };
  }
}

/**
 * Enhanced processUrl function with BFS approach and global URL tracking
 */
export async function processUrl(
  url: string,
  options?: { maxPages?: number; maxDepth?: number }
): Promise<ProcessUrlResult[]> {
  const website = cleanUrl(url);
  const maxPages = options?.maxPages ?? 1000;
  const maxDepth = options?.maxDepth ?? 10;
  
  console.log(`Starting comprehensive BFS crawling for: ${website} (maxPages: ${maxPages}, maxDepth: ${maxDepth})`);
  
  const domain = new URL(website).hostname;
  const urlTracker = new GlobalUrlTracker(domain);
  const finalResults: ProcessUrlResult[] = [];
  
  // Safety mechanisms for unlimited crawling
  const startTime = Date.now();
  const maxCrawlTimeMs = 60 * 60 * 1000; // 1 hour maximum crawl time
  let lastProgressLog = 0;
  const progressLogInterval = 10; // Log progress every 10 pages
  
  // Add initial URL
  urlTracker.addUrl(website, null, 0, 'initial');
  
  // Step 1: BFS sitemap discovery
  console.log('Step 1: Comprehensive sitemap discovery...');
  await bfsDiscoverSitemapUrls(website, urlTracker);
  
  const sitemapStats = urlTracker.getStats();
  console.log(`Step 1 completed. Discovered ${sitemapStats.total} URLs from sitemaps:`, sitemapStats);
  
  // Step 2: BFS page crawling with link extraction (concurrent batches)
  console.log('Step 2: Comprehensive page crawling...');
  let processedCount = 0;
  
  while (processedCount < maxPages) {
    // Safety check: maximum crawl time
    if (Date.now() - startTime > maxCrawlTimeMs) {
      console.log(`Stopping crawl after ${Math.round((Date.now() - startTime) / 1000 / 60)} minutes for safety`);
      break;
    }
    
    // Build a batch of pending URLs up to PAGE_CONCURRENCY
    const batch: UrlTrackingItem[] = [];
    for (let i = 0; i < PAGE_CONCURRENCY; i++) {
      const nextItem = urlTracker.getNextPendingUrl();
      if (!nextItem) break;
      if (nextItem.depth > maxDepth) { 
        urlTracker.updateStatus(nextItem.url, 'failed'); 
        continue; 
      }
      urlTracker.updateStatus(nextItem.url, 'processing');
      batch.push(nextItem);
    }
    
    if (batch.length === 0) {
      console.log('No more pending URLs to process - crawl complete');
      break;
    }
    
    await Promise.all(batch.map(async (item, idx) => {
      const ordinal = processedCount + idx + 1; // stable per-batch label
      console.log(`Processing [${ordinal}/${maxPages}] depth ${item.depth}: ${item.url}`);
      try {
        const pageData = await scrapeSingleUrlWithLinkExtraction(item.url, item.parentUrl || website, domain, urlTracker, item.depth, maxDepth);
        if (pageData) {
          finalResults.push(pageData);
          urlTracker.updateStatus(item.url, 'completed');
          processedCount++;
        } else {
          urlTracker.updateStatus(item.url, 'failed');
        }
      } catch (error) {
        console.error(`Error processing ${item.url}:`, error);
        urlTracker.updateStatus(item.url, 'failed');
      }
    }));
    
    // Enhanced progress logging for unlimited crawling
    if (processedCount - lastProgressLog >= progressLogInterval || processedCount === maxPages) {
      const elapsed = Math.round((Date.now() - startTime) / 1000);
      const stats = urlTracker.getStats();
      const rate = processedCount > 0 ? (processedCount / elapsed * 60).toFixed(1) : '0';
      
      console.log(`📊 Progress: ${processedCount}/${maxPages} pages (${rate} pages/min)`);
      console.log(`   Tracker stats:`, stats);
      console.log(`   Elapsed: ${Math.floor(elapsed / 60)}:${(elapsed % 60).toString().padStart(2, '0')}`);
      
      lastProgressLog = processedCount;
    }
  }
  
  console.log('Final crawler stats:', urlTracker.getStats());
  console.log(`Crawling completed: ${finalResults.length} pages scraped successfully`);
  return finalResults;
}

/**
 * BFS traversal from a URL to discover related URLs within the same domain
 */
async function bfsTraverseFromUrl(
  startUrl: string,
  maxPages: number,
  maxDepth: number,
  globalScrapedUrls: Set<string>
): Promise<ProcessUrlResult[]> {
  const results: ProcessUrlResult[] = [];
  const visited = new Set<string>();
  const queue: Array<{ url: string; parentUrl: string | null; depth: number }> = [
    { url: startUrl, parentUrl: null, depth: 0 }
  ];
  
  const domain = new URL(startUrl).hostname;
  
  while (queue.length > 0 && results.length < maxPages) {
    const { url, parentUrl, depth } = queue.shift()!;
    
    // Skip if already visited, too deep, or already scraped globally
    if (visited.has(url) || depth > maxDepth || globalScrapedUrls.has(url)) {
      continue;
    }
    
    visited.add(url);
    globalScrapedUrls.add(url); // Mark as scraped globally
    
    try {
      // Get the page content and extract links
      const pageData = await scrapeSingleUrl(url, parentUrl || startUrl);
      if (pageData) {
        results.push({
          url: pageData.url,
          text: pageData.text,
          depth: pageData.depth,
          parentUrl: pageData.parentUrl
        });
        
        // Extract links for next level if not at max depth
        if (depth < maxDepth) {
          const links = await extractLinksFromUrl(url, domain);
          for (const link of links) {
            if (!visited.has(link) && !globalScrapedUrls.has(link) && results.length < maxPages) {
              queue.push({ url: link, parentUrl: url, depth: depth + 1 });
            }
          }
        }
      }
    } catch (error) {
      console.error(`Error in BFS traversal for ${url}:`, error);
    }
  }
  
  return results;
}

/**
 * Scrape a single URL, extract links, and add them to the tracker
 */
async function scrapeSingleUrlWithLinkExtraction(
  url: string, 
  parentUrl: string, 
  domain: string, 
  urlTracker: GlobalUrlTracker, 
  currentDepth: number, 
  maxDepth: number,
  sharedBrowser?: any,
  sharedContext?: any
): Promise<ProcessUrlResult | null> {
  let browser: any = null;
  let context: any = null;
  let page: any = null;
  
  try {
    console.log(`Scraping URL with link extraction: ${url}`);

    // Use browser pool to prevent memory leaks
    await browserPool.initialize();
    const { browser: sharedBrowser, context: sharedContext } = await browserPool.getBrowserAndContext();
    
    browser = sharedBrowser;
    context = sharedContext;
    page = await context.newPage();
    page.setDefaultTimeout(60000); // 60 seconds for pages with animations
    await page.goto(url, { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(2000);
    
    // Click load more buttons
    await clickLoadMoreButtons(page);
    
    // Wait for dynamic content to complete (includes smooth scrolling, network idle, and 4s timeout)
    await waitForDynamicContentComplete(page);
    
    const content = await page.content();
    const text = await extractCleanText(content);
    
    // Capture full page screenshot for multimodal analysis
    let screenshot: Buffer | undefined;
    try {
      screenshot = await page.screenshot({
        fullPage: true,
        type: 'png'
      });
      console.log(`📸 Full page screenshot captured for ${url} (${screenshot?.length || 0} bytes)`);
    } catch (screenshotError) {
      console.warn(`⚠️ Failed to capture screenshot for ${url}:`, screenshotError);
    }
    
    // Extract links from the page and add to tracker
    if (currentDepth < maxDepth) {
      const links = extractSameDomainLinks(content, url, domain);
      let addedLinksCount = 0;
      for (const link of links) {
        if (urlTracker.addUrl(link, url, currentDepth + 1, 'page_links')) {
          addedLinksCount++;
        }
      }
      if (addedLinksCount > 0) {
        console.log(`  → Found ${addedLinksCount} new links on page`);
      }
    }
    
    if (text && text.length > 100) {
      return {
        url,
        text,
        screenshot: screenshot || undefined, // Include screenshot for multimodal analysis
        depth: currentDepth,
        parentUrl: parentUrl || ''
      };
    }
    
    return null;
  } catch (error) {
    console.error(`Error scraping ${url}:`, error);
    return null;
  } finally {
    // CRITICAL: Always clean up page resources to prevent memory leaks
    try {
      if (page) {
        await browserPool.releasePage(page);
        console.log(`🧹 Released page to browser pool for ${url}`);
      }
    } catch (cleanupError) {
      console.warn(`⚠️ Error during cleanup for ${url}:`, cleanupError);
    }
  }
}

/**
 * Scrape a single URL and return its data (legacy function for backward compatibility)
 */
async function scrapeSingleUrl(url: string, parentUrl: string): Promise<ProcessUrlResult | null> {
  try {
    console.log(`Scraping single URL: ${url}`);
    
    const browser = await playwright.chromium.launch({
      headless: true,
      args: ['--disable-dev-shm-usage', '--no-sandbox', '--disable-gpu']
    });
    
    const context = await browser.newContext({
      userAgent: "Mozilla/5.0 (compatible; AnaxBot/1.0; +https://anax.ai/bot)",
      viewport: { width: 1280, height: 800 }
    });
    
    const page = await context.newPage();
    page.setDefaultTimeout(30300);
    
    await page.goto(url, { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(2000);
    
    // Click load more buttons
    await clickLoadMoreButtons(page);
    
    // Wait for dynamic content to complete (includes smooth scrolling, network idle, and 4s timeout)
    await waitForDynamicContentComplete(page);
    
    const content = await page.content();
    const text = await extractCleanText(content);
    
    // Capture full page screenshot for multimodal analysis
    let screenshot: Buffer | undefined;
    try {
      screenshot = await page.screenshot({
        fullPage: true,
        type: 'png'
      });
      console.log(`📸 Full page screenshot captured for ${url} (${screenshot?.length || 0} bytes)`);
    } catch (screenshotError) {
      console.warn(`⚠️ Failed to capture screenshot for ${url}:`, screenshotError);
    }
    
    await context.close();
    await browser.close();
    
    if (text && text.length > 100) {
      return {
        url,
        text,
        screenshot: screenshot || undefined, // Include screenshot for multimodal analysis
        depth: 0, // Will be set by caller
        parentUrl: parentUrl || ''
      };
    }
    
    return null;
  } catch (error) {
    console.error(`Error scraping ${url}:`, error);
    return null;
  }
}

/**
 * Extract links from a URL (without scraping full content)
 */
async function extractLinksFromUrl(url: string, domain: string): Promise<string[]> {
  let browser: any = null;
  let context: any = null;
  let page: any = null;
  
  try {
    const browserInstance = await pageLimiter.schedule(async () => {
      browser = await playwright.chromium.launch({
        headless: true,
        args: ['--disable-dev-shm-usage', '--no-sandbox', '--disable-gpu']
      });
      context = await browser.newContext({
        userAgent: "Mozilla/5.0 (compatible; AnaxBot/1.0; +https://anax.ai/bot)",
        viewport: { width: 1280, height: 800 }
      });
      page = await context.newPage();
      page.setDefaultTimeout(60000); // 60 seconds for pages with animations
      await page.goto(url, { waitUntil: 'domcontentloaded' });
      await page.waitForTimeout(1000);
      return { context, page, browser };
    });
    
    const content = await page.content();
    const links = extractSameDomainLinks(content, url, domain);
    
    return links;
  } catch (error) {
    console.error(`Error extracting links from ${url}:`, error);
    return [];
  } finally {
    // CRITICAL: Always clean up browser resources to prevent memory leaks
    try {
      if (page) {
        await page.close();
        console.log(`🧹 Closed page for link extraction ${url}`);
      }
      if (context) {
        await context.close();
        console.log(`🧹 Closed context for link extraction ${url}`);
      }
      if (browser) {
        await browser.close();
        console.log(`🧹 Closed browser for link extraction ${url}`);
      }
    } catch (cleanupError) {
      console.warn(`⚠️ Error during cleanup for link extraction ${url}:`, cleanupError);
    }
  }
}

/**
 * Store a crawled page in the database
 */
async function storePage(db: DatabaseConnection, page: CrawledPageData): Promise<void> {
  const sql = `
    INSERT INTO company_web_pages
      (id, company_id, url, parent_url, crawl_depth, crawl_run_id,
       extracted_text, relevance_rank, rank_factors, 
       page_metadata, content_category, entity_focus, business_function,
       data_reliability, classification_confidence, metadata_classified_at, classifier_version,
       last_scraped_at, created_at, extracted)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW(), NOW(), FALSE)
    ON CONFLICT DO NOTHING
  `;
  
  await db.query(sql, [
    page.id,
    page.company_id,
    page.url,
    page.parent_url,
    page.crawl_depth,
    page.crawl_run_id,
    page.extracted_text,
    page.relevance_rank || 0,
    JSON.stringify(page.rank_factors || {}),
    page.page_metadata || null, // Raw YAML string
    null, // content_category (not used)
    null, // entity_focus (not used)
    null, // business_function (not used)
    null, // data_reliability (not used)
    null, // classification_confidence (not used)
    new Date(), // Set classified_at to now
    '1.0.0' // Set classifier_version
  ]);
}

/**
 * Clear existing web pages for a company
 */
async function clearExistingPages(db: DatabaseConnection, companyId: number, logger: (level: string, message: string) => void): Promise<void> {
  // First count existing pages
  const countSql = `
    SELECT COUNT(*) as page_count
    FROM company_web_pages
    WHERE company_id = $1
  `;
  
  try {
    // Get count before deletion
    const countResult = await db.query(countSql, [companyId]);
    const pageCount = parseInt(String(countResult[0]?.page_count) || '0', 10);
    
    if (pageCount === 0) {
      logger('info', `No existing web pages found for company ${companyId}`);
      return;
    }
    
    // Perform deletion
    const deleteSql = `
      DELETE FROM company_web_pages
      WHERE company_id = $1
    `;
    
    await db.query(deleteSql, [companyId]);
    
    logger('info', `Cleared ${pageCount} existing web pages for company ${companyId}`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger('error', `Error clearing existing web pages for company ${companyId}: ${errorMessage}`);
  }
}