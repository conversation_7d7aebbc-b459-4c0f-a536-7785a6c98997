/**
 * Cloudflare Email Obfuscation Decoder
 * 
 * Decodes emails that are obfuscated using Cloudflare's email protection system.
 * The pattern is: <span class="__cf_email__" data-cfemail="encoded_string">[email protected]</span>
 * 
 * Algorithm:
 * 1. Extract the data-cfemail attribute value
 * 2. Convert from hex to bytes
 * 3. XOR each byte with the first byte (key)
 * 4. Convert back to ASCII string
 */

export class CFEmailDecoder {
  /**
   * Decode a single Cloudflare obfuscated email using the correct algorithm
   */
  static decodeEmail(encodedEmail: string): string {
    try {
      let email = '';
      const r = parseInt(encodedEmail.substr(0, 2), 16);
      let i = 2;
      while (i < encodedEmail.length) {
        const c = parseInt(encodedEmail.substr(i, 2), 16) ^ r;
        email += String.fromCharCode(c);
        i += 2;
      }
      return email;
    } catch (error) {
      console.warn('Failed to decode CF email:', error);
      return '';
    }
  }


  /**
   * Find and decode all Cloudflare obfuscated emails in HTML content using Cheerio
   */
  static async decodeEmailsInHTML(html: string): Promise<string> {
    if (!html || typeof html !== 'string') {
      return html;
    }

    try {
      const cheerio = await import('cheerio');
      const $ = cheerio.load(html);
      
      // Find all spans with __cf_email__ class
      $('span.__cf_email__').each((index: number, element: any) => {
        const $element = $(element);
        const encodedEmail = $element.attr('data-cfemail');
        
        if (encodedEmail) {
          const decodedEmail = this.decodeEmail(encodedEmail);
          if (decodedEmail && this.isValidEmail(decodedEmail)) {
            console.log(`🔓 Decoded CF email: ${decodedEmail}`);
            // Replace the entire span with just the decoded email
            $element.replaceWith(decodedEmail);
          }
        }
      });
      
      return $.html();
    } catch (error) {
      console.warn('Failed to decode CF emails with Cheerio:', error);
      return html; // Return original HTML if decoding fails
    }
  }

  /**
   * Find and decode all Cloudflare obfuscated emails in text content
   */
  static decodeEmailsInText(text: string): string {
    if (!text || typeof text !== 'string') {
      return text;
    }

    // Look for patterns like: [email protected] with nearby encoded data
    // This is a more flexible approach for text content
    const cfEmailRegex = /\[email[^]]*protected\]/gi;
    
    return text.replace(cfEmailRegex, (match) => {
      // Try to find the encoded email in the surrounding context
      const context = text.substring(Math.max(0, text.indexOf(match) - 200), text.indexOf(match) + 200);
      const encodedMatch = context.match(/data-cfemail="([a-f0-9]+)"/i);
      
      if (encodedMatch) {
        const decodedEmail = this.decodeEmail(encodedMatch[1]);
        if (decodedEmail && this.isValidEmail(decodedEmail)) {
          console.log(`🔓 Decoded CF email from text: ${decodedEmail}`);
          return decodedEmail;
        }
      }
      
      return match; // Return original if no encoded data found
    });
  }

  /**
   * Validate if a string is a valid email address
   */
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Extract all emails (both obfuscated and regular) from HTML content
   */
  static async extractAllEmails(html: string): Promise<string[]> {
    const emails: string[] = [];
    
    // First decode CF obfuscated emails
    const decodedHTML = await this.decodeEmailsInHTML(html);
    
    // Then extract all email addresses (including decoded ones)
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const matches = decodedHTML.match(emailRegex);
    
    if (matches) {
      emails.push(...matches);
    }
    
    // Remove duplicates and return
    return [...new Set(emails)];
  }

  /**
   * Test the decoder with the provided example
   */
  static async testDecoder(): Promise<void> {
    console.log('🧪 Testing Cloudflare Email Decoder...');
    
    // Test with the provided example
    const testHTML = `<span class="elementor-icon-list-text">
      <span class="__cf_email__" data-cfemail="bfc6d0d1d6ffdecdd6d0d1d9cad1db91dcd0d2">[email &#160;protected]</span>
    </span>`;
    
    console.log('📧 Original HTML:', testHTML);
    
    const decoded = await this.decodeEmailsInHTML(testHTML);
    console.log('🔓 Decoded HTML:', decoded);
    
    const emails = await this.extractAllEmails(testHTML);
    console.log('📬 Extracted emails:', emails);
  }
}

// Export for use in other modules
export default CFEmailDecoder;
