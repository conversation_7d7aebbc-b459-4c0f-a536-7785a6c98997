/**
 * Utility functions for search normalization and forgiving search logic
 */

// Common business suffixes to remove from company names
const BUSINESS_SUFFIXES = [
  'LLC', 'Inc', 'Corp', 'Group', 'LP', 'LLP', 'Ltd', 'Limited',
  'Company', 'Co', 'Partnership', 'Associates', 'Holdings',
  'Enterprises', 'Ventures', 'Capital', 'Management', 'Partners'
];

/**
 * Normalize a search term for forgiving search
 * - Trim whitespace
 * - Convert to lowercase
 * - Remove punctuation except @ and . for emails
 * - Remove common business suffixes for company names
 */
export function normalizeSearchTerm(term: string, isCompanyName: boolean = false): string {
  if (!term) return '';
  
  let normalized = term.trim().toLowerCase();
  
  // Remove punctuation except @ and . for email fields
  normalized = normalized.replace(/[^\w@.\s-]/g, '');
  
  // Remove extra whitespace
  normalized = normalized.replace(/\s+/g, ' ').trim();
  
  // Remove business suffixes for company names
  if (isCompanyName) {
    const suffixRegex = new RegExp(`\\b(${BUSINESS_SUFFIXES.join('|')})\\b`, 'gi');
    normalized = normalized.replace(suffixRegex, '').trim();
  }
  
  return normalized;
}

/**
 * Create a forgiving search pattern for ILIKE queries
 * - Handles partial matching
 * - Case insensitive
 * - Trims whitespace
 */
export function createSearchPattern(term: string): string {
  const normalized = normalizeSearchTerm(term);
  return `%${normalized}%`;
}

/**
 * Create multiple search patterns for different field types
 */
export function createMultiFieldSearchPatterns(term: string) {
  const basePattern = createSearchPattern(term);
  const companyPattern = createSearchPattern(term, true); // Remove business suffixes
  
  return {
    base: basePattern,
    company: companyPattern,
    email: term.trim().toLowerCase() // Keep @ and . for emails
  };
}
