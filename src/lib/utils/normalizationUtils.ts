/**
 * Data normalization utilities for consistent data processing
 * Extracted from DataNormalizationService for reuse across processors
 */

/**
 * Normalize company name by removing common suffixes and converting to lowercase
 */
export function normalizeCompanyName(name: string | null): string {
  if (!name) return ''
  
  return name
    .toLowerCase()
    .trim()
    .replace(/\s+(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?)$/gi, '')
    .replace(/\s+/g, ' ')
    .trim()
}

/**
 * Extract domain from website URL
 */
export function extractDomain(website: string | null): string | null {
  if (!website) return null
  
  try {
    const cleaned = website
      .toLowerCase()
      .replace(/^https?:\/\/(www\.)?/, '')
      .replace(/\/.*$/, '')
      .trim()
    
    return cleaned || null
  } catch {
    return null
  }
}

/**
 * Normalize phone number by keeping only digits
 */
export function normalizePhone(phone: string | null): string | null {
  if (!phone) return null
  
  const digits = phone.replace(/[^0-9]/g, '')
  return digits.length >= 10 ? digits : null
}

/**
 * Extract LinkedIn handle from LinkedIn URL
 */
export function extractLinkedInHandle(url: string | null): string | null {
  if (!url) return null
  
  try {
    const match = url.match(/linkedin\.com\/in\/([^\/\?]+)/i)
    return match ? match[1].toLowerCase() : null
  } catch {
    return null
  }
}

/**
 * Tokenize name into searchable parts
 */
export function tokenizeName(name: string | null): string[] {
  if (!name) return []
  
  return name
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(token => token.length > 1)
}

/**
 * Normalize email to lowercase
 */
export function normalizeEmail(email: string | null): string | null {
  if (!email) return null
  return email.toLowerCase().trim()
}

/**
 * Normalize contact name for consistent searching
 */
export function normalizeContactName(name: string): string {
  return name.toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, ' ') // Normalize whitespace
}

/**
 * Normalize domain for consistent searching
 */
export function normalizeDomain(domain: string): string {
  return domain.toLowerCase()
    .replace(/^www\./, '') // Remove www prefix
    .trim()
}
