import { createHash } from "crypto";
import fs from "fs";
import path from "path";
import { pool } from "@/lib/db";
import {
  File,
  FileRelationship,
  FileUploadRequest,
  FileRelationshipRequest,
  FileSearchFilters,
  FileSearchResult,
  TableFileQuery,
  FileRelationshipQuery,
  TableColumnValidation,
  FileProcessingOptions,
  DuplicateFile,
} from "@/types/file";
import { StorageProviderRegistry } from "@/lib/storage/StorageProviderRegistry";
import { EnhancedFileManager } from "./EnhancedFileManager";

export class FileManager {
  // Base directory for file storage
  private static readonly BASE_STORAGE_DIR = path.join(
    process.cwd(),
    "file_storage"
  );

  /**
   * Ensure storage directory exists
   */
  private static ensureStorageDirectory(): void {
    if (!fs.existsSync(this.BASE_STORAGE_DIR)) {
      fs.mkdirSync(this.BASE_STORAGE_DIR, { recursive: true });
    }
  }

  /**
   * Get storage path for a file based on content hash
   */
  private static getStoragePath(
    contentHash: string,
    originalName: string
  ): string {
    this.ensureStorageDirectory();

    // Create subdirectories based on hash for better organization
    const hashPrefix = contentHash.substring(0, 2);
    const hashSubdir = contentHash.substring(2, 4);
    const storageDir = path.join(this.BASE_STORAGE_DIR, hashPrefix, hashSubdir);

    if (!fs.existsSync(storageDir)) {
      fs.mkdirSync(storageDir, { recursive: true });
    }

    // Use hash as filename to avoid conflicts, but preserve extension
    const extension = path.extname(originalName);
    const fileName = `${contentHash}${extension}`;

    return path.join(storageDir, fileName);
  }

  /**
   * Save file to disk
   */
  private static async saveFileToDisk(
    fileBuffer: Buffer,
    contentHash: string,
    originalName: string
  ): Promise<string> {
    const filePath = this.getStoragePath(contentHash, originalName);

    // Only write if file doesn't exist (content hash deduplication)
    if (!fs.existsSync(filePath)) {
      fs.writeFileSync(filePath, fileBuffer);
    }

    return filePath;
  }

  /**
   * Get file from storage (backward compatible with disk access)
   * Now supports multiple storage providers
   */
  static async getFileFromDisk(filePath: string): Promise<Buffer | null> {
    // Delegate to enhanced file manager for backward compatibility
    return await EnhancedFileManager.getFileFromDisk(filePath);
  }

  /**
   * Delete file from disk
   */
  private static async deleteFileFromDisk(filePath: string): Promise<boolean> {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error deleting file from disk:", error);
      return false;
    }
  }

  /**
   * Generate SHA-256 hash for file content
   */
  static generateContentHash(content: Buffer): string {
    return createHash("sha256").update(content).digest("hex");
  }

  /**
   * Extract file extension from filename
   */
  static getFileExtension(filename: string): string | undefined {
    const match = filename.match(/\.([^.]+)$/);
    return match ? match[1].toLowerCase() : undefined;
  }

  /**
   * Validate if a table and column exist in the database
   */
  static async validateTableColumn(
    tableName: string,
    columnName: string
  ): Promise<boolean> {
    try {
      const result = await pool.query(
        `SELECT EXISTS(
          SELECT 1 
          FROM information_schema.columns 
          WHERE table_name = $1 
          AND column_name = $2
          AND table_schema = 'public'
        ) as is_valid`,
        [tableName, columnName]
      );
      return result.rows[0]?.is_valid || false;
    } catch (error) {
      console.error("Error validating table column:", error);
      return false;
    }
  }

  /**
   * Check if file content already exists by hash
   */
  static async fileContentExists(contentHash: string): Promise<boolean> {
    try {
      const result = await pool.query(
        `SELECT EXISTS(SELECT 1 FROM files WHERE content_hash = $1) as exists`,
        [contentHash]
      );
      return result.rows[0]?.exists || false;
    } catch (error) {
      console.error("Error checking file content existence:", error);
      return false;
    }
  }

  /**
   * Get existing file by content hash
   */
  static async getFileByContentHash(contentHash: string): Promise<File | null> {
    try {
      const result = await pool.query(
        `SELECT * FROM files WHERE content_hash = $1`,
        [contentHash]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error("Error getting file by content hash:", error);
      return null;
    }
  }

  /**
   * Get files for a specific table/column/row
   */
  static async getTableFiles(query: TableFileQuery): Promise<File[]> {
    try {
      const result = await pool.query(
        `SELECT 
          f.file_id,
          f.file_name,
          f.original_name,
          f.title,
          f.mime_type,
          f.file_size_bytes,
          fr.relationship_type,
          fr.relationship_title,
          fr.is_primary,
          fr.display_order,
          f.uploaded_at
        FROM files f
        INNER JOIN file_relationships fr ON f.file_id = fr.file_id
        WHERE fr.target_table_name = $1 
        AND fr.target_column_name = $2
        AND fr.target_row_id = $3
        ORDER BY fr.is_primary DESC, fr.display_order ASC, f.uploaded_at DESC`,
        [query.table_name, query.column_name, query.row_id]
      );
      return result.rows as File[];
    } catch (error) {
      console.error("Error getting table files:", error);
      return [];
    }
  }

  /**
   * Get all relationships for a file
   */
  static async getFileRelationships(
    query: FileRelationshipQuery
  ): Promise<FileRelationship[]> {
    try {
      const result = await pool.query(
        `SELECT 
          fr.relationship_id,
          fr.target_table_name,
          fr.target_column_name,
          fr.target_row_id,
          fr.relationship_type,
          fr.relationship_title,
          fr.is_primary,
          fr.display_order,
          fr.created_at
        FROM file_relationships fr
        WHERE fr.file_id = $1
        ORDER BY fr.is_primary DESC, fr.display_order ASC, fr.created_at DESC`,
        [query.file_id]
      );
      return result.rows as FileRelationship[];
    } catch (error) {
      console.error("Error getting file relationships:", error);
      return [];
    }
  }

  /**
   * Get files with relationship counts and linked table names
   */
  static async getFilesWithRelationships(): Promise<File[]> {
    try {
      const result = await pool.query(
        `SELECT 
          f.*,
          COUNT(fr.relationship_id) as relationship_count,
          ARRAY_AGG(DISTINCT fr.target_table_name) as linked_table_names
        FROM files f
        LEFT JOIN file_relationships fr ON f.file_id = fr.file_id
        GROUP BY f.file_id`
      );
      return result.rows as File[];
    } catch (error) {
      console.error("Error getting files with relationships:", error);
      return [];
    }
  }

  /**
   * Get duplicate files by content hash
   */
  static async getDuplicateFiles(): Promise<DuplicateFile[]> {
    try {
      const result = await pool.query(
        `SELECT 
          content_hash,
          COUNT(*) as duplicate_count,
          ARRAY_AGG(file_id::text) as file_ids,
          ARRAY_AGG(file_name) as file_names,
          MIN(created_at) as first_uploaded,
          MAX(created_at) as last_uploaded
        FROM files
        GROUP BY content_hash
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC`
      );
      return result.rows as DuplicateFile[];
    } catch (error) {
      console.error("Error getting duplicate files:", error);
      return [];
    }
  }

  /**
   * Upload a new file with content hash deduplication and storage provider support
   * Now uses Azure by default with backward compatibility
   */
  static async uploadFile(
    fileBuffer: Buffer,
    request: FileUploadRequest,
    options: FileProcessingOptions = {}
  ): Promise<{ file: File; isDuplicate: boolean; filePath: string }> {
    // Delegate to enhanced file manager for new uploads
    return await EnhancedFileManager.uploadFile(fileBuffer, request, options);
  }

  /**
   * Create a relationship between a file and any table/column/row
   */
  static async createFileRelationship(
    fileId: string,
    relationship: FileRelationshipRequest,
    options: FileProcessingOptions = {}
  ): Promise<FileRelationship> {
    try {
      // Validate table and column if requested
      if (options.validate_table_column) {
        const isValid = await this.validateTableColumn(
          relationship.target_table_name,
          relationship.target_column_name
        );
        if (!isValid) {
          throw new Error(
            `Invalid table/column: ${relationship.target_table_name}.${relationship.target_column_name}`
          );
        }
      }

      // Check if relationship already exists
      const existingRelationship = await pool.query(
        `SELECT * FROM file_relationships 
         WHERE file_id = $1 
         AND target_table_name = $2 
         AND target_column_name = $3 
         AND target_row_id = $4`,
        [
          fileId,
          relationship.target_table_name,
          relationship.target_column_name,
          relationship.target_row_id
        ]
      );

      if (existingRelationship.rows.length > 0) {
        console.log(`Relationship already exists for file ${fileId} -> ${relationship.target_table_name}.${relationship.target_column_name}.${relationship.target_row_id}`);
        return existingRelationship.rows[0] as FileRelationship;
      }

      // Insert new relationship
      const result = await pool.query(
        `INSERT INTO file_relationships (
          file_id, target_table_name, target_column_name, target_row_id,
          relationship_type, relationship_title, relationship_notes,
          display_order, is_primary
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9
        ) RETURNING *`,
        [
          fileId,
          relationship.target_table_name,
          relationship.target_column_name,
          relationship.target_row_id,
          relationship.relationship_type || "attachment",
          relationship.relationship_title || null,
          relationship.relationship_notes || null,
          relationship.display_order || 0,
          relationship.is_primary || false,
        ]
      );

      return result.rows[0] as FileRelationship;
    } catch (error) {
      console.error("Error creating file relationship:", error);
      throw new Error("Failed to create file relationship");
    }
  }

  /**
   * Upload file and create relationship in one operation
   */
  static async uploadFileWithRelationship(
    fileBuffer: Buffer,
    uploadRequest: FileUploadRequest,
    relationshipRequest: FileRelationshipRequest,
    options: FileProcessingOptions = {}
  ): Promise<{
    file: File;
    relationship: FileRelationship;
    isDuplicate: boolean;
    filePath: string;
  }> {
    try {
      // Upload file
      const { file, isDuplicate, filePath } = await this.uploadFile(
        fileBuffer,
        uploadRequest,
        options
      );

      // Create relationship
      const relationship = await this.createFileRelationship(
        file.file_id,
        relationshipRequest,
        options
      );

      return { file, relationship, isDuplicate, filePath };
    } catch (error) {
      console.error("Error uploading file with relationship:", error);
      throw new Error("Failed to upload file with relationship");
    }
  }

  /**
   * Get file by ID with relationships
   */
  static async getFileById(fileId: string): Promise<File | null> {
    try {
      const result = await pool.query(
        `SELECT * FROM files WHERE file_id = $1`,
        [fileId]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error("Error getting file by ID:", error);
      return null;
    }
  }

  /**
   * Search files with filters
   */
  static async searchFiles(
    filters: FileSearchFilters = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<FileSearchResult> {
    try {
      const whereConditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      if (filters.file_name) {
        whereConditions.push(`file_name ILIKE $${paramIndex}`);
        params.push(`%${filters.file_name}%`);
        paramIndex++;
      }
      if (filters.original_name) {
        whereConditions.push(`original_name ILIKE $${paramIndex}`);
        params.push(`%${filters.original_name}%`);
        paramIndex++;
      }
      if (filters.mime_type) {
        whereConditions.push(`mime_type = $${paramIndex}`);
        params.push(filters.mime_type);
        paramIndex++;
      }
      if (filters.uploaded_by) {
        whereConditions.push(`uploaded_by = $${paramIndex}`);
        params.push(filters.uploaded_by);
        paramIndex++;
      }
      if (filters.access_level) {
        whereConditions.push(`access_level = $${paramIndex}`);
        params.push(filters.access_level);
        paramIndex++;
      }
      if (filters.uploaded_after) {
        whereConditions.push(`uploaded_at >= $${paramIndex}`);
        params.push(filters.uploaded_after);
        paramIndex++;
      }
      if (filters.uploaded_before) {
        whereConditions.push(`uploaded_at <= $${paramIndex}`);
        params.push(filters.uploaded_before);
        paramIndex++;
      }
      if (filters.file_size_min) {
        whereConditions.push(`file_size_bytes >= $${paramIndex}`);
        params.push(filters.file_size_min);
        paramIndex++;
      }
      if (filters.file_size_max) {
        whereConditions.push(`file_size_bytes <= $${paramIndex}`);
        params.push(filters.file_size_max);
        paramIndex++;
      }
      if (filters.has_relationships) {
        whereConditions.push(
          `EXISTS (SELECT 1 FROM file_relationships WHERE file_id = files.file_id)`
        );
      }
      if (filters.target_table_name) {
        whereConditions.push(`EXISTS (
          SELECT 1 FROM file_relationships 
          WHERE file_id = files.file_id 
          AND target_table_name = $${paramIndex}
        )`);
        params.push(filters.target_table_name);
        paramIndex++;
      }

      const whereClause =
        whereConditions.length > 0
          ? `WHERE ${whereConditions.join(" AND ")}`
          : "";

      // Get total count
      const countResult = await pool.query(
        `SELECT COUNT(*) as total FROM files ${whereClause}`,
        params
      );
      const totalCount = parseInt(countResult.rows[0]?.total || "0");

      // Get paginated results
      const offset = (page - 1) * pageSize;
      const result = await pool.query(
        `SELECT f.*, 
               COUNT(fr.relationship_id) as relationship_count,
               ARRAY_AGG(DISTINCT fr.target_table_name) as linked_table_names
        FROM files f
        LEFT JOIN file_relationships fr ON f.file_id = fr.file_id
        ${whereClause}
        GROUP BY f.file_id
        ORDER BY f.uploaded_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
        [...params, pageSize, offset]
      );

      const files = result.rows as File[];
      const totalPages = Math.ceil(totalCount / pageSize);

      return {
        files,
        total_count: totalCount,
        page,
        page_size: pageSize,
        total_pages: totalPages,
      };
    } catch (error) {
      console.error("Error searching files:", error);
      return {
        files: [],
        total_count: 0,
        page,
        page_size: pageSize,
        total_pages: 0,
      };
    }
  }

  /**
   * Delete a file and all its relationships
   */
  static async deleteFile(fileId: string): Promise<boolean> {
    try {
      // Get file info before deletion
      const file = await this.getFileById(fileId);
      if (!file) {
        return false;
      }

      // Delete from database first
      const result = await pool.query(`DELETE FROM files WHERE file_id = $1`, [
        fileId,
      ]);

      if ((result.rowCount ?? 0) > 0) {
        // Delete from disk if no other files reference this content
        const otherFiles = await pool.query(
          `SELECT COUNT(*) as count FROM files WHERE content_hash = $1`,
          [file.content_hash]
        );

        if (parseInt(otherFiles.rows[0]?.count || "0") === 0) {
          // No other files use this content, safe to delete from disk
          const filePath = file.metadata?.file_path;
          if (filePath) {
            await this.deleteFileFromDisk(filePath);
          }
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error("Error deleting file:", error);
      return false;
    }
  }

  /**
   * Delete a specific file relationship with smart file deletion
   */
  static async deleteFileRelationship(
    relationshipId: string
  ): Promise<{
    success: boolean;
    message: string;
    fileDeleted?: boolean;
    fileOrphaned?: boolean;
    remainingRelationships?: number;
  }> {
    try {
      // First, get the relationship to find the file_id
      const relationshipResult = await pool.query(
        'SELECT * FROM file_relationships WHERE relationship_id = $1',
        [relationshipId]
      );

      if (relationshipResult.rows.length === 0) {
        return {
          success: false,
          message: "Relationship not found"
        };
      }

      const relationship = relationshipResult.rows[0];
      const fileId = relationship.file_id;

      // Delete the relationship
      await pool.query(
        'DELETE FROM file_relationships WHERE relationship_id = $1',
        [relationshipId]
      );

      // Check if there are any remaining relationships for this file
      const remainingRelationshipsResult = await pool.query(
        'SELECT COUNT(*) as count FROM file_relationships WHERE file_id = $1',
        [fileId]
      );

      const remainingCount = parseInt(remainingRelationshipsResult.rows[0].count);

      let responseMessage = "File relationship removed successfully";
      let fileDeleted = false;
      let fileOrphaned = false;

      // If no relationships remain, delete the file
      if (remainingCount === 0) {
        try {
          // Get file details
          const file = await this.getFileById(fileId);
          if (file) {
            // Check if this is a duplicate file (same content hash exists elsewhere)
            const duplicateCheckResult = await pool.query(
              'SELECT COUNT(*) as count FROM files WHERE content_hash = $1 AND file_id != $2',
              [file.content_hash, fileId]
            );

            const duplicateCount = parseInt(duplicateCheckResult.rows[0].count);

            if (duplicateCount === 0) {
              // No duplicates, delete from storage and database
              try {
                const storageRegistry = StorageProviderRegistry.getInstance();
                const provider = storageRegistry.getProvider((file as any).storage_provider);
                
                if (provider) {
                  await provider.deleteFile((file as any).storage_path);
                }
              } catch (storageError) {
                console.warn("Could not delete from storage provider:", storageError);
                // Continue with database deletion even if storage deletion fails
              }

              // Delete from database
              await pool.query(
                'DELETE FROM files WHERE file_id = $1',
                [fileId]
              );

              fileDeleted = true;
              responseMessage = `File "${file.original_name}" has been permanently deleted as it has no remaining relationships and no duplicate content exists.`;
            } else {
              // Has duplicates, just delete the file record but keep storage
              await pool.query(
                'DELETE FROM files WHERE file_id = $1',
                [fileId]
              );

              fileOrphaned = true;
              responseMessage = `File "${file.original_name}" has been removed from the system as duplicate content exists elsewhere.`;
            }
          }
        } catch (fileDeleteError) {
          console.error("Error deleting orphaned file:", fileDeleteError);
          responseMessage = "File relationship removed, but there was an error cleaning up the orphaned file.";
        }
      }

      return {
        success: true,
        message: responseMessage,
        fileDeleted,
        fileOrphaned,
        remainingRelationships: remainingCount
      };
    } catch (error) {
      console.error("Error deleting file relationship:", error);
      return {
        success: false,
        message: "Failed to delete file relationship"
      };
    }
  }

  /**
   * Delete a file relationship by file ID and target details with smart file deletion
   */
  static async deleteFileRelationshipByTarget(
    fileId: string,
    targetTableName: string,
    targetColumnName: string,
    targetRowId: string
  ): Promise<{
    success: boolean;
    message: string;
    fileDeleted?: boolean;
    fileOrphaned?: boolean;
    remainingRelationships?: number;
  }> {
    try {
      // Find the relationship
      const relationshipResult = await pool.query(
        `SELECT relationship_id FROM file_relationships 
         WHERE file_id = $1 
         AND target_table_name = $2 
         AND target_column_name = $3 
         AND target_row_id = $4`,
        [fileId, targetTableName, targetColumnName, targetRowId]
      );

      if (relationshipResult.rows.length === 0) {
        return {
          success: false,
          message: "File relationship not found"
        };
      }

      const relationshipId = relationshipResult.rows[0].relationship_id;
      
      // Use the centralized deleteFileRelationship method
      return await this.deleteFileRelationship(relationshipId);
    } catch (error) {
      console.error("Error deleting file relationship by target:", error);
      return {
        success: false,
        message: "Failed to delete file relationship"
      };
    }
  }

  /**
   * Update file metadata
   */
  static async updateFile(
    fileId: string,
    updates: Partial<File>
  ): Promise<File | null> {
    try {
      const updateFields: string[] = [];
      const values: (string | number | boolean | string[] | Record<string, any>)[] = [];
      let paramIndex = 1;

      // Build update fields dynamically
      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined && key !== "file_id") {
          updateFields.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      });

      if (updateFields.length === 0) {
        return await this.getFileById(fileId);
      }

      values.push(fileId);
      const result = await pool.query(
        `UPDATE files 
         SET ${updateFields.join(", ")}, updated_at = CURRENT_TIMESTAMP
         WHERE file_id = $${paramIndex}
         RETURNING *`,
        values
      );

      return result.rows[0] || null;
    } catch (error) {
      console.error("Error updating file:", error);
      return null;
    }
  }

  /**
   * Update file relationship
   */
  static async updateFileRelationship(
    relationshipId: string,
    updates: Partial<FileRelationship>
  ): Promise<FileRelationship | null> {
    try {
      const updateFields: string[] = [];
      const values: (string | number | boolean | string[] | Record<string, any>)[] = [];
      let paramIndex = 1;

      // Build update fields dynamically
      Object.entries(updates).forEach(([key, value]) => {
        if (
          value !== undefined &&
          key !== "relationship_id" &&
          key !== "file_id"
        ) {
          updateFields.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      });

      if (updateFields.length === 0) {
        return null;
      }

      values.push(relationshipId);
      const result = await pool.query(
        `UPDATE file_relationships 
         SET ${updateFields.join(", ")}, updated_at = CURRENT_TIMESTAMP
         WHERE relationship_id = $${paramIndex}
         RETURNING *`,
        values
      );

      return result.rows[0] || null;
    } catch (error) {
      console.error("Error updating file relationship:", error);
      return null;
    }
  }

  /**
   * Get file statistics
   */
  static async getFileStatistics(): Promise<{
    total_files: number;
    total_size_bytes: number;
    files_by_type: Record<string, number>;
    duplicate_count: number;
  }> {
    try {
      // Get basic stats
      const basicStats = await pool.query(
        `SELECT 
          COUNT(*) as total_files,
          COALESCE(SUM(file_size_bytes), 0) as total_size_bytes
        FROM files`
      );

      // Get files by type
      const typeStats = await pool.query(
        `SELECT mime_type, COUNT(*) as count 
        FROM files 
        GROUP BY mime_type`
      );

      // Get duplicate count
      const duplicateStats = await pool.query(
        `SELECT COUNT(*) as duplicate_count
        FROM (
          SELECT content_hash
          FROM files
          GROUP BY content_hash
          HAVING COUNT(*) > 1
        ) duplicates`
      );

      const filesByType: Record<string, number> = {};
      typeStats.rows.forEach((row) => {
        filesByType[row.mime_type] = parseInt(row.count);
      });

      return {
        total_files: parseInt(basicStats.rows[0]?.total_files || "0"),
        total_size_bytes: parseInt(basicStats.rows[0]?.total_size_bytes || "0"),
        files_by_type: filesByType,
        duplicate_count: parseInt(
          duplicateStats.rows[0]?.duplicate_count || "0"
        ),
      };
    } catch (error) {
      console.error("Error getting file statistics:", error);
      return {
        total_files: 0,
        total_size_bytes: 0,
        files_by_type: {},
        duplicate_count: 0,
      };
    }
  }

  /**
   * Clean up orphaned files (files with no relationships)
   */
  static async cleanupOrphanedFiles(): Promise<number> {
    try {
      const result = await pool.query(
        `DELETE FROM files 
        WHERE file_id NOT IN (SELECT DISTINCT file_id FROM file_relationships)`
      );
      return result.rowCount ?? 0;
    } catch (error) {
      console.error("Error cleaning up orphaned files:", error);
      return 0;
    }
  }

  /**
   * Helper method to determine MIME type from file extension
   */
  private static getMimeTypeFromExtension(extension?: string): string {
    if (!extension) return "application/octet-stream";

    const mimeTypes: Record<string, string> = {
      pdf: "application/pdf",
      doc: "application/msword",
      docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      xls: "application/vnd.ms-excel",
      xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ppt: "application/vnd.ms-powerpoint",
      pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      txt: "text/plain",
      csv: "text/csv",
      json: "application/json",
      xml: "application/xml",
      html: "text/html",
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      png: "image/png",
      gif: "image/gif",
      svg: "image/svg+xml",
      mp4: "video/mp4",
      avi: "video/x-msvideo",
      mp3: "audio/mpeg",
      wav: "audio/wav",
      zip: "application/zip",
      rar: "application/vnd.rar",
      "7z": "application/x-7z-compressed",
    };

    return mimeTypes[extension.toLowerCase()] || "application/octet-stream";
  }
}
