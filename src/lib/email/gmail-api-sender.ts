import { google, gmail_v1 } from "googleapis";
import { JWT } from "google-auth-library";
import fs from "fs";
import path from "path";

// Gmail API configuration
const SERVICE_ACCOUNT_PATH = path.resolve(process.cwd(), "src/service-creds.json");
const GMAIL_SENDER_EMAIL = process.env.GMAIL_SENDER_EMAIL || "<EMAIL>";
const GOOGLE_WORKSPACE_DOMAIN = process.env.GOOGLE_WORKSPACE_DOMAIN || "anaxrep.com";

interface HomepageContact {
  first_name?: string;
  last_name?: string;
  email: string;
  company?: string;
  job_title?: string;
  message?: string;
  utm?: Record<string, string>;
}

interface DealRequestInfo {
  deal_requested?: string;
  deal_requested_id?: string;
  deal_type?: string;
  location?: string;
}

class GmailAPISender {
  private jwtClient: JWT | null = null;
  private scopes: string[] = [
    "https://www.googleapis.com/auth/gmail.send",
    "https://www.googleapis.com/auth/gmail.compose",
  ];

  constructor(private impersonateEmail?: string) {
    this.authenticate();
  }

  private async authenticate() {
    try {
      // Load service account credentials
      const key = JSON.parse(fs.readFileSync(SERVICE_ACCOUNT_PATH, "utf8"));
      
      this.jwtClient = new google.auth.JWT({
        email: key.client_email,
        key: key.private_key,
        scopes: this.scopes,
      });

      console.log("Gmail API authenticated successfully");
    } catch (error) {
      console.error("Failed to authenticate Gmail API:", error);
      throw error;
    }
  }

  private async getGmailClient(): Promise<gmail_v1.Gmail> {
    if (!this.jwtClient) {
      throw new Error("Gmail API not authenticated");
    }

    // Use impersonation email if provided, otherwise use default sender
    const emailToImpersonate = this.impersonateEmail || GMAIL_SENDER_EMAIL;
    
    // Set the subject to the email for impersonation (Domain-Wide Delegation)
    this.jwtClient.subject = emailToImpersonate || undefined;
    await this.jwtClient.authorize();
    
    return google.gmail({ version: "v1", auth: this.jwtClient });
  }

  private createEmailMessage(to: string, subject: string, htmlBody: string): string {
    const boundary = "----=_Part_" + Math.random().toString(36).substr(2, 9);
    const fromEmail = this.impersonateEmail || GMAIL_SENDER_EMAIL;
    
    const message = [
      `To: ${to}`,
      `From: ${fromEmail}`,
      `Subject: ${subject}`,
      `MIME-Version: 1.0`,
      `Content-Type: multipart/alternative; boundary="${boundary}"`,
      ``,
      `--${boundary}`,
      `Content-Type: text/plain; charset=UTF-8`,
      `Content-Transfer-Encoding: 7bit`,
      ``,
      this.htmlToText(htmlBody),
      ``,
      `--${boundary}`,
      `Content-Type: text/html; charset=UTF-8`,
      `Content-Transfer-Encoding: 7bit`,
      ``,
      htmlBody,
      ``,
      `--${boundary}--`
    ].join("\n");

    return Buffer.from(message).toString("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
  }

  private htmlToText(html: string): string {
    // Simple HTML to text conversion
    return html
      .replace(/<br\s*\/?>/gi, "\n")
      .replace(/<p[^>]*>/gi, "\n")
      .replace(/<\/p>/gi, "\n")
      .replace(/<[^>]*>/g, "")
      .replace(/\n\s*\n/g, "\n")
      .trim();
  }

  async sendEmail(to: string, subject: string, htmlBody: string): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      console.log(`Sending Gmail API email to ${to}`);

      const gmail = await this.getGmailClient();
      const rawMessage = this.createEmailMessage(to, subject, htmlBody);

      const response = await gmail.users.messages.send({
        userId: "me",
        requestBody: {
          raw: rawMessage,
        },
      });

      const messageId = response.data.id || undefined;
      console.log(`Gmail API email sent successfully. Message ID: ${messageId}`);

      return {
        success: true,
        messageId: messageId,
      };
    } catch (error) {
      console.error("Failed to send Gmail API email:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}

// Load and process the homepage welcome email template
function loadHomepageWelcomeTemplate(contact: HomepageContact, dealInfo?: DealRequestInfo): string {
  const templatePath = path.resolve(process.cwd(), "emailHtmlTemplates/homepage_welcome_template.html");
  
  try {
    let template = fs.readFileSync(templatePath, "utf8");
    
    // Basic contact replacements
    const replacements: Record<string, string> = {
      '{{first_name}}': contact.first_name || 'there',
      '{{last_name}}': contact.last_name || '',
      '{{email}}': contact.email || '',
      '{{company}}': contact.company || 'your company',
      '{{job_title}}': contact.job_title || '',
    };

    // Deal-specific replacements
    if (dealInfo) {
      replacements['{{deal_type}}'] = dealInfo.deal_type || 'real estate investment';
      replacements['{{location}}'] = dealInfo.location || '';
    }

    // Apply all replacements
    Object.entries(replacements).forEach(([placeholder, value]) => {
      template = template.replace(new RegExp(placeholder, 'g'), value);
    });

    // Handle conditional sections
    if (dealInfo?.deal_requested === 'true') {
      // Show deal request section
      template = template.replace(/\{\{#if deal_requested\}\}([\s\S]*?)\{\{\/if\}\}/g, '$1');
    } else {
      // Remove deal request section
      template = template.replace(/\{\{#if deal_requested\}\}[\s\S]*?\{\{\/if\}\}/g, '');
    }

    // Handle location conditional
    if (dealInfo?.location) {
      // Show location in the text
      template = template.replace(/\{\{#if location\}\}([\s\S]*?)\{\{\/if\}\}/g, '$1');
    } else {
      // Remove location part
      template = template.replace(/\{\{#if location\}\}[\s\S]*?\{\{\/if\}\}/g, '');
    }

    // Clean up any remaining template syntax (but keep already processed variables)
    template = template.replace(/\{\{[^}]+\}\}/g, '');
    
    return template;
  } catch (error) {
    console.error("Error loading email template:", error);
    // Fallback template
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <h2>Welcome to ANAX, ${contact.first_name || "there"}!</h2>
          <p>Thank you for your interest in our investment opportunities.</p>
          <p>We'll be in touch soon to discuss your investment objectives.</p>
          <p>Best regards,<br>The ANAX Team</p>
        </body>
      </html>
    `;
  }
}

/**
 * Send homepage welcome email via Gmail API with optional impersonation
 */
export async function sendHomepageWelcomeEmailViaGmail(
  contact: HomepageContact, 
  impersonateEmail?: string
): Promise<{ success: boolean; error?: string; messageId?: string }> {
  try {
    const senderEmail = impersonateEmail || GMAIL_SENDER_EMAIL;
    console.log(`Sending homepage welcome email via Gmail API to ${contact.email} from ${senderEmail}`);

    // Extract deal request information from UTM data
    const dealInfo: DealRequestInfo = {};
    if (contact.utm) {
      dealInfo.deal_requested = contact.utm.deal_requested;
      dealInfo.deal_requested_id = contact.utm.deal_requested_id;
      dealInfo.deal_type = contact.utm.deal_type;
      dealInfo.location = contact.utm.location;
    }

    // Load and process the email template
    const htmlBody = loadHomepageWelcomeTemplate(contact, dealInfo);
    const subject = `Your ANAX Inquiry`;

    // Send via Gmail API with optional impersonation
    const gmailSender = new GmailAPISender(impersonateEmail);
    const result = await gmailSender.sendEmail(contact.email, subject, htmlBody);

    if (result.success) {
      console.log(`Homepage welcome email sent successfully via Gmail API to ${contact.email} from ${senderEmail} (Message ID: ${result.messageId})`);
    } else {
      console.error(`Failed to send homepage welcome email via Gmail API to ${contact.email} from ${senderEmail}: ${result.error}`);
    }

    return result;
  } catch (error) {
    console.error("Error sending homepage welcome email via Gmail API:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Send email from any user in your Google Workspace domain
 * Requires Domain-Wide Delegation to be set up in Google Admin Console
 */
export async function sendEmailFromWorkspaceUser(
  fromEmail: string,
  toEmail: string,
  subject: string,
  htmlBody: string
): Promise<{ success: boolean; error?: string; messageId?: string }> {
  try {
    console.log(`Sending email from ${fromEmail} to ${toEmail} via Gmail API with domain-wide delegation`);

    // Validate that the fromEmail is in your workspace domain
    if (!fromEmail.endsWith(`@${GOOGLE_WORKSPACE_DOMAIN}`)) {
      return {
        success: false,
        error: `From email must be in the ${GOOGLE_WORKSPACE_DOMAIN} domain for domain-wide delegation`,
      };
    }

    // Send via Gmail API with impersonation
    const gmailSender = new GmailAPISender(fromEmail);
    const result = await gmailSender.sendEmail(toEmail, subject, htmlBody);

    if (result.success) {
      console.log(`Email sent successfully from ${fromEmail} to ${toEmail} (Message ID: ${result.messageId})`);
    } else {
      console.error(`Failed to send email from ${fromEmail} to ${toEmail}: ${result.error}`);
    }

    return result;
  } catch (error) {
    console.error("Error sending email with domain-wide delegation:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export { GmailAPISender };
