import fs from 'fs';
import path from 'path';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '';
const SMARTLEAD_BASE_URL = 'https://server.smartlead.ai/api/v1/';
const HOMEPAGE_CAMPAIGN_ID = process.env.HOMEPAGE_CAMPAIGN_ID || process.env.SMARTLEAD_CAMPAIGN_ID || '1897921';

interface HomepageContact {
  first_name?: string;
  last_name?: string;
  email: string;
  company?: string;
  job_title?: string;
  message?: string;
  utm?: Record<string, string>;
}

interface DealRequestInfo {
  deal_requested?: string;
  deal_requested_id?: string;
  deal_type?: string;
  location?: string;
}

/**
 * Process email template with contact data
 */
function processEmailTemplate(template: string, contact: HomepageContact, dealInfo?: DealRequestInfo): string {
  let processedTemplate = template;
  
  // Basic contact replacements
  const replacements: Record<string, string> = {
    '{{first_name}}': contact.first_name || 'there',
    '{{last_name}}': contact.last_name || '',
    '{{email}}': contact.email || '',
    '{{company}}': contact.company || 'your company',
    '{{job_title}}': contact.job_title || '',
  };

  // Deal-specific replacements
  if (dealInfo) {
    replacements['{{deal_type}}'] = dealInfo.deal_type || 'real estate investment';
    replacements['{{location}}'] = dealInfo.location || '';
  }

  // Apply all replacements
  Object.entries(replacements).forEach(([placeholder, value]) => {
    processedTemplate = processedTemplate.replace(new RegExp(placeholder, 'g'), value);
  });

  // Handle conditional sections
  if (dealInfo?.deal_requested === 'true') {
    // Show deal request section
    processedTemplate = processedTemplate.replace(/\{\{#if deal_requested\}\}([\s\S]*?)\{\{\/if\}\}/g, '$1');
  } else {
    // Remove deal request section
    processedTemplate = processedTemplate.replace(/\{\{#if deal_requested\}\}[\s\S]*?\{\{\/if\}\}/g, '');
  }

  // Handle location conditional
  if (dealInfo?.location) {
    // Show location in the text
    processedTemplate = processedTemplate.replace(/\{\{#if location\}\}([\s\S]*?)\{\{\/if\}\}/g, '$1');
  } else {
    // Remove location part
    processedTemplate = processedTemplate.replace(/\{\{#if location\}\}[\s\S]*?\{\{\/if\}\}/g, '');
  }

  // Clean up any remaining template syntax (but keep already processed variables)
  processedTemplate = processedTemplate.replace(/\{\{[^}]+\}\}/g, '');

  return processedTemplate;
}

/**
 * Load and process the homepage welcome email template
 */
function loadHomepageWelcomeTemplate(contact: HomepageContact, dealInfo?: DealRequestInfo): string {
  try {
    const templatePath = path.join(process.cwd(), 'emailHtmlTemplates', 'homepage_welcome_template.html');
    const template = fs.readFileSync(templatePath, 'utf8');
    return processEmailTemplate(template, contact, dealInfo);
  } catch (error) {
    console.error('Error loading homepage welcome template:', error);
    // Fallback to a simple HTML template
    return `
      <html>
        <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2>Welcome to ANAX</h2>
          <p>Hi ${contact.first_name || 'there'},</p>
          <p>Thank you for your interest in ANAX. We'll be in touch soon to discuss your investment objectives.</p>
          <p>Best regards,<br>Eric Brody<br>ANAX Capital Advisory</p>
        </body>
      </html>
    `;
  }
}

/**
 * Send welcome email via Smartlead
 */
export async function sendHomepageWelcomeEmail(contact: HomepageContact): Promise<{ success: boolean; error?: string; leadId?: string }> {
  try {
    console.log(`Sending homepage welcome email to ${contact.email}`);

    // Extract deal request information from UTM data
    const dealInfo: DealRequestInfo = {};
    if (contact.utm) {
      dealInfo.deal_requested = contact.utm.deal_requested;
      dealInfo.deal_requested_id = contact.utm.deal_requested_id;
      dealInfo.deal_type = contact.utm.deal_type;
      dealInfo.location = contact.utm.location;
    }

    // Load and process the email template
    const htmlBody = loadHomepageWelcomeTemplate(contact, dealInfo);
    const subject = `Your ANAX Inquiry`;

    // Prepare the lead data for Smartlead
    const leadData = {
      email: contact.email,
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      company_name: contact.company || '',
      website: '',
      job_title: contact.job_title || '',
      phone_number: '',
      city: '',
      state: '',
      country: '',
      linkedin_url: '',
      tags: ['homepage-welcome', 'anax-sync'],
      custom_fields: {
        // Store the welcome email content
        welcome_email_subject: subject,
        welcome_email_body: htmlBody,
        homepage_message: contact.message || '',
        deal_requested: dealInfo.deal_requested || 'false',
        deal_requested_id: dealInfo.deal_requested_id || '',
        source: 'homepage_form',
        sent_at: new Date().toISOString(),
      }
    };

    // Prepare the payload for Smartlead
    const payload = {
      lead_list: [leadData]
    };

    // Check if API key is configured
    if (!SMARTLEAD_API_KEY) {
      console.warn('SMARTLEAD_API_KEY not configured, skipping email send');
      return { success: false, error: 'Smartlead API key not configured' };
    }

    // Send to Smartlead
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${HOMEPAGE_CAMPAIGN_ID}/leads?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Smartlead API error: ${response.status} - ${errorText}`);
      return { success: false, error: `Smartlead API error: ${response.status}` };
    }

    const result = await response.json();
    console.log(`Successfully sent welcome email to ${contact.email} via Smartlead`);

    // Extract lead ID from response
    const leadId = result.data?.[0]?.id || result.lead_id || 'unknown';

    return { success: true, leadId };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error sending homepage welcome email:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Send immediate welcome email (bypasses campaign sequence)
 */
export async function sendImmediateWelcomeEmail(contact: HomepageContact): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`Sending immediate welcome email to ${contact.email}`);

    // Extract deal request information
    const dealInfo: DealRequestInfo = {};
    if (contact.utm) {
      dealInfo.deal_requested = contact.utm.deal_requested;
      dealInfo.deal_requested_id = contact.utm.deal_requested_id;
      dealInfo.deal_type = contact.utm.deal_type;
      dealInfo.location = contact.utm.location;
    }

    // Load and process the email template
    const htmlBody = loadHomepageWelcomeTemplate(contact, dealInfo);
    const subject = `Your ANAX Inquiry`;

    // Check if API key is configured
    if (!SMARTLEAD_API_KEY) {
      console.warn('SMARTLEAD_API_KEY not configured, skipping immediate email send');
      return { success: false, error: 'Smartlead API key not configured' };
    }

    // For immediate sending, we'll use the manual email endpoint
    // First, we need to add the lead to the campaign, then send the email
    const leadData = {
      email: contact.email,
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      company_name: contact.company || '',
      job_title: contact.job_title || '',
      tags: ['homepage-welcome', 'immediate-send'],
      custom_fields: {
        homepage_message: contact.message || '',
        deal_requested: dealInfo.deal_requested || 'false',
        deal_requested_id: dealInfo.deal_requested_id || '',
        source: 'homepage_form',
      }
    };

    // Add lead to campaign first
    const addLeadUrl = `${SMARTLEAD_BASE_URL}campaigns/${HOMEPAGE_CAMPAIGN_ID}/leads?api_key=${SMARTLEAD_API_KEY}`;
    const addLeadResponse = await fetch(addLeadUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ lead_list: [leadData] }),
    });

    if (!addLeadResponse.ok) {
      const errorText = await addLeadResponse.text();
      console.error(`Error adding lead to campaign: ${addLeadResponse.status} - ${errorText}`);
      return { success: false, error: `Failed to add lead to campaign: ${addLeadResponse.status}` };
    }

    const addLeadResult = await addLeadResponse.json();
    const leadId = addLeadResult.data?.[0]?.id;

    if (!leadId) {
      return { success: false, error: 'Failed to get lead ID from Smartlead' };
    }

    // Now send the immediate email
    const sendEmailUrl = `${SMARTLEAD_BASE_URL}campaigns/${HOMEPAGE_CAMPAIGN_ID}/leads/${leadId}/manual-email?api_key=${SMARTLEAD_API_KEY}`;
    
    const emailPayload = {
      subject: subject,
      html_body: htmlBody,
      is_html: true
    };

    const sendEmailResponse = await fetch(sendEmailUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailPayload),
    });

    if (!sendEmailResponse.ok) {
      const errorText = await sendEmailResponse.text();
      console.error(`Error sending immediate email: ${sendEmailResponse.status} - ${errorText}`);
      return { success: false, error: `Failed to send email: ${sendEmailResponse.status}` };
    }

    console.log(`Successfully sent immediate welcome email to ${contact.email}`);
    return { success: true };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error sending immediate welcome email:', errorMessage);
    return { success: false, error: errorMessage };
  }
}
