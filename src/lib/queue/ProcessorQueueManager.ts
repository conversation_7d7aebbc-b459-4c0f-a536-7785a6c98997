import { Queue, Worker, QueueEvents } from 'bullmq'
import { BaseProcessor } from '../processors/BaseProcessor'
import { ProcessorResult, ProcessingStage } from '../../types/processing'
import { QUEUE_DEFINITIONS, QueueType, QueueConfig, BASE_QUEUE_CONFIG } from './config/queue-config'
// Removed processor-jobs import as it doesn't exist
import { PROCESSOR_REGISTRY, getProcessorConfig, getAllQueueTypes } from './config/processor-registry'
import Redis from 'ioredis'

// Load environment variables
import dotenv from 'dotenv'
dotenv.config()

// Configuration is now imported from separate config files

// Job data interface
export interface ProcessorJobData {
  processorType: ProcessingStage
  options: {
    limit?: number
    singleId?: number
    multiIds?: number[]
    filters?: any
    batchSize?: number
    entityType?: 'contact' | 'company' | 'both'
    campaignId?: string
    timeout?: number // Override default timeout
  }
  priority?: number
  delay?: number
  queueName?: string // Allow custom queue name
}

export class ProcessorQueueManager {
  private static instance: ProcessorQueueManager
  private queues: Map<string, Queue> = new Map()
  private workers: Map<string, Worker> = new Map()
  private events: Map<string, QueueEvents> = new Map()
  private processors: Map<string, (options: any) => Promise<any>> = new Map()

  private constructor() {
    this.initializeQueues()
    this.initializeProcessors()
  }

  public static getInstance(): ProcessorQueueManager {
    if (!ProcessorQueueManager.instance) {
      ProcessorQueueManager.instance = new ProcessorQueueManager()
    }
    return ProcessorQueueManager.instance
  }

  private initializeQueues() {
    // Create standard queues
    Object.entries(QUEUE_DEFINITIONS).forEach(([type, config]) => {
      const queue = new Queue(config.name, {
        connection: this.getRedisConnection(),
        defaultJobOptions: {
          removeOnComplete: config.removeOnComplete,
          removeOnFail: config.removeOnFail,
          attempts: config.attempts,
          backoff: config.backoff,
          // Set timeout based on max runtime
          delay: 0
        }
      })
      
      this.queues.set(type, queue)
      console.log(`[QueueManager] Initialized ${type} queue: ${config.name} (max runtime: ${config.maxRuntime}min)`)
    })

    // Initialize custom queues from processor registry
    const customQueues = PROCESSOR_REGISTRY.filter(config => 
      config.queueType === 'CUSTOM' && config.enabled && config.customQueueConfig
    )

    customQueues.forEach(config => {
      const queueName = config.customQueueName!
      const queueConfig = config.customQueueConfig!
      
      const queue = new Queue(queueName, {
        connection: this.getRedisConnection(),
        defaultJobOptions: {
          removeOnComplete: queueConfig.removeOnComplete,
          removeOnFail: queueConfig.removeOnFail,
          attempts: queueConfig.attempts,
          backoff: queueConfig.backoff
        }
      })
      
      this.queues.set(queueName, queue)
      console.log(`[QueueManager] Initialized custom queue: ${queueName} (max runtime: ${queueConfig.maxRuntime}min)`)
    })
  }

  private initializeProcessors() {
    // Initialize processors from registry
    PROCESSOR_REGISTRY.forEach(config => {
      if (config.enabled) {
        const processorFactory = this.createProcessorFactory(config)
        this.processors.set(config.processorType, processorFactory)
      }
    })
  }

  private createProcessorFactory(config: any) {
    return async (options: any): Promise<any> => {
      const module = await import(`../processors/${config.processorClass}`)
      const ProcessorClass = module[config.processorClass]
      
      if (!ProcessorClass) {
        throw new Error(`Processor class ${config.processorClass} not found`)
      }

      // Instantiate based on constructor args configuration
      if (config.constructorArgs === 'none') {
        return new ProcessorClass()
      } else {
        return new ProcessorClass(options || {})
      }
    }
  }

  private getRedisConnection() {
    // If REDIS_URL is provided, use it directly
    if (process.env.REDIS_URL) {
      let url = process.env.REDIS_URL;
      // If URL starts with redis:// but SSL is enabled, convert to rediss://
      if (url.startsWith('redis://') && 
          (process.env.REDIS_SSL === "true" || process.env.REDIS_SSL === "1" || 
           process.env.REDIS_TLS === "true" || process.env.REDIS_TLS === "1")) {
        url = url.replace('redis://', 'rediss://');
      }
      
      // Return URL with Azure Redis optimizations
      return {
        url,
        connectTimeout: 30000,
        lazyConnect: true,
        enableOfflineQueue: true,
        tls: {
          rejectUnauthorized: false // Azure Redis uses self-signed certificates
        }
      };
    }
    
    // Otherwise use individual Redis options with Azure optimizations
    const options = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6380'), // Default to SSL port
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      tls: process.env.REDIS_SSL === "true" || process.env.REDIS_SSL === "1" || 
           process.env.REDIS_TLS === "true" || process.env.REDIS_TLS === "1" ? {
             rejectUnauthorized: false // Azure Redis uses self-signed certificates
           } : undefined,
      // Azure Redis specific options
      connectTimeout: 30000,
      lazyConnect: true,
      enableOfflineQueue: true
    };
    
    return options;
  }

  // Get queue by type or create custom queue
  getQueue(type: QueueType | string): Queue {
    if (this.queues.has(type)) {
      return this.queues.get(type)!
    }
    
    // Create custom queue if it doesn't exist
    return this.createCustomQueue(type)
  }

  // Create custom queue with configurable timeout
  createCustomQueue(queueName: string, maxRuntime: number = 5): Queue {
    if (this.queues.has(queueName)) {
      return this.queues.get(queueName)!
    }

    const config: QueueConfig = {
      name: queueName,
      maxRuntime,
      ...BASE_QUEUE_CONFIG,
      concurrency: maxRuntime <= 1 ? 10 : maxRuntime <= 5 ? 5 : 2
    }

    const queue = new Queue(config.name, {
      connection: this.getRedisConnection(),
      defaultJobOptions: {
        removeOnComplete: config.removeOnComplete,
        removeOnFail: config.removeOnFail,
        attempts: config.attempts,
        backoff: config.backoff
      }
    })

    this.queues.set(queueName, queue)
    console.log(`[QueueManager] Created custom queue: ${queueName} (max runtime: ${maxRuntime}min)`)
    
    return queue
  }

  // Add job to appropriate queue based on processor type or custom config
  async addProcessorJob(
    processorType: ProcessingStage,
    options: ProcessorJobData['options'] = {},
    customConfig?: {
      queueName?: string
      maxRuntime?: number
      priority?: number
      delay?: number
    }
  ): Promise<string> {
    let queue: Queue
    let queueType: string

    if (customConfig?.queueName) {
      // Use custom queue
      queue = this.getQueue(customConfig.queueName)
      queueType = customConfig.queueName
    } else {
      // Auto-select queue based on processor type
      const autoQueueType = this.getQueueTypeForProcessor(processorType)
      queue = this.getQueue(autoQueueType)
      queueType = autoQueueType
    }

    // Calculate timeout based on queue max runtime or custom timeout
    const timeout = options.timeout || this.getTimeoutForQueue(queueType) || 300000 // 5 min default

    const job = await queue.add(processorType, {
      processorType,
      options: {
        ...options,
        timeout
      }
    }, {
      priority: customConfig?.priority || this.getPriorityForQueue(queueType),
      delay: customConfig?.delay || 0
    })

    console.log(`[QueueManager] Added ${processorType} job to ${queueType} queue (timeout: ${timeout}ms)`)
    return job.id!
  }

  // Schedule job for specific time (delayed execution)
  async scheduleProcessorJob(
    processorType: ProcessingStage,
    options: ProcessorJobData['options'] = {},
    scheduleTime: Date | number,
    customConfig?: {
      queueName?: string
      maxRuntime?: number
      priority?: number
    }
  ): Promise<string> {
    let queue: Queue
    let queueType: string

    if (customConfig?.queueName) {
      queue = this.getQueue(customConfig.queueName)
      queueType = customConfig.queueName
    } else {
      const autoQueueType = this.getQueueTypeForProcessor(processorType)
      queue = this.getQueue(autoQueueType)
      queueType = autoQueueType
    }

    const timeout = options.timeout || this.getTimeoutForQueue(queueType) || 300000
    const delay = typeof scheduleTime === 'number' ? scheduleTime : scheduleTime.getTime() - Date.now()

    const job = await queue.add(processorType, {
      processorType,
      options: {
        ...options,
        timeout
      }
    }, {
      priority: customConfig?.priority || this.getPriorityForQueue(queueType),
      delay: Math.max(0, delay)
    })

    console.log(`[QueueManager] Scheduled ${processorType} job for ${scheduleTime}: ${job.id}`)
    return job.id!
  }

  // Schedule repeatable job (cron pattern) using upsertJobScheduler
  async scheduleRepeatableJob(
    processorType: ProcessingStage,
    options: ProcessorJobData['options'] = {},
    cronPattern: string,
    customConfig?: {
      queueName?: string
      maxRuntime?: number
      priority?: number
      jobId?: string
    }
  ): Promise<string> {
    let queue: Queue
    let queueType: string

    if (customConfig?.queueName) {
      queue = this.getQueue(customConfig.queueName)
      queueType = customConfig.queueName
    } else {
      const autoQueueType = this.getQueueTypeForProcessor(processorType)
      queue = this.getQueue(autoQueueType)
      queueType = autoQueueType
    }

    const timeout = options.timeout || this.getTimeoutForQueue(queueType) || 300000
    // Use consistent jobId for cron jobs to prevent duplicates
    const jobId = customConfig?.jobId || `cron-${processorType}`

    // Use upsertJobScheduler with cron strategy
    const job = await queue.upsertJobScheduler(
      jobId,
      {
        pattern: cronPattern,
        tz: 'UTC'
      },
      {
        name: processorType,
        data: {
          processorType,
          options: {
            ...options,
            timeout
          }
        },
        opts: {
          priority: customConfig?.priority || this.getPriorityForQueue(queueType),
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: {
            type: 'exponential' as const,
            delay: 2000
          }
        }
      }
    )

    console.log(`[QueueManager] Scheduled repeatable ${processorType} job with pattern ${cronPattern}: ${job.id}`)
    return job.id!
  }

  // Schedule repeatable job with interval using upsertJobScheduler
  async scheduleIntervalJob(
    processorType: ProcessingStage,
    options: ProcessorJobData['options'] = {},
    intervalMs: number,
    customConfig?: {
      queueName?: string
      maxRuntime?: number
      priority?: number
      jobId?: string
    }
  ): Promise<string> {
    let queue: Queue
    let queueType: string

    if (customConfig?.queueName) {
      queue = this.getQueue(customConfig.queueName)
      queueType = customConfig.queueName
    } else {
      const autoQueueType = this.getQueueTypeForProcessor(processorType)
      queue = this.getQueue(autoQueueType)
      queueType = autoQueueType
    }

    const timeout = options.timeout || this.getTimeoutForQueue(queueType) || 300000
    const jobId = customConfig?.jobId || `interval-${processorType}-${Date.now()}`

    // Use upsertJobScheduler with every strategy
    const job = await queue.upsertJobScheduler(
      jobId,
      {
        every: intervalMs
      },
      {
        name: processorType,
        data: {
          processorType,
          options: {
            ...options,
            timeout
          }
        },
        opts: {
          priority: customConfig?.priority || this.getPriorityForQueue(queueType),
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: {
            type: 'exponential' as const,
            delay: 2000
          }
        }
      }
    )

    console.log(`[QueueManager] Scheduled interval ${processorType} job every ${intervalMs}ms: ${job.id}`)
    return job.id!
  }

  // Remove repeatable job (job scheduler)
  async removeRepeatableJob(processorType: ProcessingStage, jobId: string) {
    try {
      const queueType = this.getQueueTypeForProcessor(processorType)
      const queue = this.getQueue(queueType)
      
      // Remove job scheduler using the modern API
      await queue.removeJobScheduler(jobId)
      console.log(`[QueueManager] Removed job scheduler ${jobId} for ${processorType}`)
      
    } catch (error) {
      console.error(`[QueueManager] Failed to remove job scheduler ${jobId}:`, error)
      throw error
    }
  }

  // Schedule job with custom repeat strategy
  async scheduleCustomRepeatJob(
    processorType: ProcessingStage,
    options: ProcessorJobData['options'] = {},
    customPattern: string,
    customConfig?: {
      queueName?: string
      maxRuntime?: number
      priority?: number
      jobId?: string
    }
  ): Promise<string> {
    let queue: Queue
    let queueType: string

    if (customConfig?.queueName) {
      queue = this.getQueue(customConfig.queueName)
      queueType = customConfig.queueName
    } else {
      const autoQueueType = this.getQueueTypeForProcessor(processorType)
      queue = this.getQueue(autoQueueType)
      queueType = autoQueueType
    }

    const timeout = options.timeout || this.getTimeoutForQueue(queueType) || 300000
    const jobId = customConfig?.jobId || `custom-${processorType}-${Date.now()}`

    // Use upsertJobScheduler with custom pattern
    const job = await queue.upsertJobScheduler(
      jobId,
      {
        pattern: customPattern
      },
      {
        name: processorType,
        data: {
          processorType,
          options: {
            ...options,
            timeout
          }
        },
        opts: {
          priority: customConfig?.priority || this.getPriorityForQueue(queueType),
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: {
            type: 'exponential' as const,
            delay: 2000
          }
        }
      }
    )

    console.log(`[QueueManager] Scheduled custom repeat ${processorType} job with pattern ${customPattern}: ${job.id}`)
    return job.id!
  }

  // Initialize all cron jobs from config
  async initializeCronJobs() {
    try {
      const { getEnabledCronJobs, CRON_JOBS } = await import('../scheduler/config/cron-config')
      const enabledJobs = getEnabledCronJobs()
      
      // Separate background sync jobs from standard jobs
      const backgroundSyncJobs = CRON_JOBS.filter(job => job.category === 'BACKGROUND_SYNC')
      const standardJobs = CRON_JOBS.filter(job => job.category === 'STANDARD')
      
      console.log(`[QueueManager] Found ${enabledJobs.length} enabled cron jobs in config`)
      console.log(`[QueueManager] Background Sync jobs: ${backgroundSyncJobs.length}`)
      console.log(`[QueueManager] Standard jobs: ${standardJobs.length}`)
      
      // Always schedule background sync jobs (critical system processes)
      for (const job of backgroundSyncJobs) {
        if (job.enabled) {
          await this.scheduleRepeatableJob(
            job.processorType as ProcessingStage,
            job.options,
            job.cronExpression,
            {
              jobId: job.id,
              priority: this.getPriorityForQueue(job.queueType)
            }
          )
          console.log(`[QueueManager] Scheduled background sync job: ${job.id}`)
        }
      }
      
      console.log(`[QueueManager] Background sync jobs are always running`)
      console.log(`[QueueManager] Standard jobs are available in UI for manual scheduling`)
      
    } catch (error) {
      console.error('[QueueManager] Failed to initialize cron jobs:', error)
      throw error
    }
  }

  // Manually enable a cron job from the UI
  async enableCronJob(jobId: string): Promise<string> {
    try {
      const { getCronJobById } = await import('../scheduler/config/cron-config')
      const jobConfig = getCronJobById(jobId)
      
      if (!jobConfig) {
        throw new Error(`Cron job with ID ${jobId} not found`)
      }
      
      console.log(`[QueueManager] Enabling cron job: ${jobId}`)
      
      const jobId_result = await this.scheduleRepeatableJob(
        jobConfig.processorType as ProcessingStage,
        jobConfig.options,
        jobConfig.cronExpression,
        {
          jobId: jobConfig.id,
          priority: this.getPriorityForQueue(jobConfig.queueType)
        }
      )
      
      console.log(`[QueueManager] Successfully enabled cron job: ${jobId}`)
      return jobId_result
      
    } catch (error) {
      console.error(`[QueueManager] Failed to enable cron job ${jobId}:`, error)
      throw error
    }
  }

  // Manually disable a cron job from the UI
  async disableCronJob(jobId: string): Promise<void> {
    try {
      console.log(`[QueueManager] Disabling cron job: ${jobId}`)
      
      // Get all queues to check for the job
      const allQueues = this.getAllQueues()
      
      for (const queue of allQueues) {
        try {
          // Remove the job scheduler
          await queue.removeJobScheduler(jobId)
          console.log(`[QueueManager] Removed job scheduler ${jobId} from queue ${queue.name}`)
        } catch (error) {
          // Job might not exist in this queue, continue
        }
      }
      
      console.log(`[QueueManager] Successfully disabled cron job: ${jobId}`)
      
    } catch (error) {
      console.error(`[QueueManager] Failed to disable cron job ${jobId}:`, error)
      throw error
    }
  }

  // Get all available queues
  private getAllQueues(): Queue[] {
    const queues: Queue[] = []
    
    // Add all queues from the Map
    this.queues.forEach(queue => {
      if (queue) queues.push(queue)
    })
    
    return queues
  }

  // Auto-determine queue type based on processor registry
  private getQueueTypeForProcessor(processorType: ProcessingStage): QueueType | string {
    const config = getProcessorConfig(processorType)
    if (!config) {
      console.warn(`[QueueManager] No configuration found for processor ${processorType}, defaulting to MEDIUM queue`)
      return 'MEDIUM'
    }
    
    if (config.queueType === 'CUSTOM') {
      return config.customQueueName!
    }
    
    return config.queueType
  }

  private getTimeoutForQueue(queueType: string): number {
    if (queueType === 'SHORT') return 60000 // 1 minute
    if (queueType === 'MEDIUM') return 300000 // 5 minutes
    if (queueType === 'LONG') return 3600000 // 60 minutes (1 hour)
    return 300000 // 5 minutes default
  }

  private getPriorityForQueue(queueType: string): number {
    if (queueType === 'SHORT') return 1 // Highest priority
    if (queueType === 'MEDIUM') return 5 // Medium priority  
    if (queueType === 'LONG') return 10 // Lowest priority
    return 5 // Medium priority default
  }

  // Start worker for specific queue type
  async startWorker(queueType: QueueType | string) {
    if (this.workers.has(queueType)) {
      console.log(`[QueueManager] Worker for ${queueType} already running`)
      return
    }

    const queue = this.getQueue(queueType)
    const config = QUEUE_DEFINITIONS[queueType as QueueType] || { concurrency: 5 }

    const worker = new Worker(
      queue.name,
      async (job) => this.processJob(job),
      {
        connection: this.getRedisConnection(),
        concurrency: config.concurrency
      }
    )

    // Set up event listeners
    this.setupWorkerEvents(worker, queueType)
    
    this.workers.set(queueType, worker)
    console.log(`[QueueManager] Started ${queueType} worker with concurrency: ${config.concurrency}`)
  }

  // Process individual job
  private async processJob(job: any): Promise<ProcessorResult> {
    const { processorType, options } = job.data
    
    console.log(`[${job.queueName}] Processing ${processorType} job ${job.id}`)
    
    try {
      // Check if this is a repeatable job (created with upsertJobScheduler) - make ALL repeatable jobs singular: true
      // This ensures only one instance of any repeatable job runs at a time
      // Repeatable jobs are identified by:
      // 1. job.opts.repeat - for jobs with repeat options
      // 2. job.opts.every - for "every" strategy jobs (intervals)
      // 3. job.opts.pattern - for "cron" strategy jobs (cron expressions)
      // 4. job.repeat - for legacy repeatable jobs
      const isRepeatableJob = (job.opts && job.opts.repeat) || 
                             (job.opts && (job.opts.every || job.opts.pattern)) ||
                             job.repeat
      
      if (isRepeatableJob) {
        const lockKey = `repeatable_lock:${job.name}`
        const lockValue = `${job.id}:${Date.now()}`
        
        // Try to acquire lock with 10 minute expiry (enough for any repeatable job)
        const lockAcquired = await this.acquireJobLock(lockKey, lockValue, 600000)
        if (!lockAcquired) {
          console.log(`[${job.queueName}] Repeatable job ${job.name} already running, skipping ${job.id}`)
          return { 
            processed: 0, 
            successful: 0, 
            failed: 0, 
            errors: [`Repeatable job ${job.name} already running, skipped`]
          }
        }
        
        try {
          return await this.executeJob(job, processorType, options)
        } finally {
          // Always release the lock
          await this.releaseJobLock(lockKey, lockValue)
        }
      } else {
        // For non-repeatable jobs, process normally
        return await this.executeJob(job, processorType, options)
      }
      
    } catch (error) {
      console.error(`[${job.queueName}] Job ${job.id} failed:`, error)
      throw error
    }
  }

  // Execute the actual job processing
  private async executeJob(job: any, processorType: string, options: any): Promise<ProcessorResult> {
    // Update job progress
    await job.updateProgress(10)
    
    // Get processor factory and create instance
    const processorFactory = this.processors.get(processorType)
    if (!processorFactory) {
      throw new Error(`Processor ${processorType} not found`)
    }

    // Create processor instance with options
    const processor = await (processorFactory as any)(options || {})

    // Execute the processor using registry configuration
    const config = getProcessorConfig(processorType as ProcessingStage)
    if (!config) {
      throw new Error(`No configuration found for processor ${processorType}`)
    }

    let result: ProcessorResult
    const executionMethod = config.executionMethod || 'process'
    
    if (typeof (processor as any)[executionMethod] === 'function') {
      // For processors that use processJob, pass the job data
      if (executionMethod === 'processJob') {
        result = await (processor as any)[executionMethod](job.data)
      } else if (executionMethod === 'process') {
        // For processors that use process, pass the job object
        result = await (processor as any)[executionMethod](job)
      } else {
        result = await (processor as any)[executionMethod]()
      }
    } else {
      throw new Error(`Processor ${processorType} does not have method ${executionMethod}`)
    }
    
    await job.updateProgress(100)
    
    console.log(`[${job.queueName}] Job ${job.id} completed: ${result.processed} processed, ${result.successful} successful, ${result.failed} failed`)
    
    return result
  }

  // Acquire job lock using Redis
  private async acquireJobLock(lockKey: string, lockValue: string, ttlMs: number): Promise<boolean> {
    try {
      const redis = new Redis(this.getRedisConnection())
      const result = await redis.set(lockKey, lockValue, 'PX', ttlMs, 'NX')
      await redis.disconnect()
      return result === 'OK'
    } catch (error) {
      console.error(`[QueueManager] Failed to acquire lock ${lockKey}:`, error)
      return false
    }
  }

  // Release job lock using Redis
  private async releaseJobLock(lockKey: string, lockValue: string): Promise<void> {
    try {
      const redis = new Redis(this.getRedisConnection())
      // Use Lua script to ensure we only delete our own lock
      const script = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `
      await redis.eval(script, 1, lockKey, lockValue)
      await redis.disconnect()
    } catch (error) {
      console.error(`[QueueManager] Failed to release lock ${lockKey}:`, error)
    }
  }

  private setupWorkerEvents(worker: Worker, queueType: string) {
    worker.on('completed', (job) => {
      console.log(`[${queueType}] Job ${job.id} completed successfully`)
    })

    worker.on('failed', (job, err) => {
      console.error(`[${queueType}] Job ${job?.id} failed:`, err.message)
    })

    worker.on('error', (err) => {
      console.error(`[${queueType}] Worker error:`, err)
    })

    worker.on('stalled', (jobId) => {
      console.warn(`[${queueType}] Job ${jobId} stalled`)
    })
  }

  // Get queue statistics
  async getQueueStats() {
    const stats: Record<string, any> = {}
    let totalJobs = 0
    let completedJobs = 0
    let failedJobs = 0
    let activeJobs = 0
    let waitingJobs = 0
    const queues: any[] = []
    
    for (const [queueType, queue] of this.queues) {
      try {
        const counts = await queue.getJobCounts()
        const isPaused = await queue.isPaused()
        
        stats[queueType] = counts
        
        // Aggregate totals
        totalJobs += counts.waiting + counts.active + counts.completed + counts.failed + counts.delayed
        completedJobs += counts.completed
        failedJobs += counts.failed
        activeJobs += counts.active
        waitingJobs += counts.waiting
        
        // Add to queues array for detailed view
        queues.push({
          name: queueType,
          waiting: counts.waiting,
          active: counts.active,
          completed: counts.completed,
          failed: counts.failed,
          delayed: counts.delayed,
          paused: isPaused
        })
      } catch (error) {
        stats[queueType] = { error: error.message }
      }
    }
    
    return {
      totalJobs,
      completedJobs,
      failedJobs,
      activeJobs,
      waitingJobs,
      queues,
      details: stats
    }
  }

  // Get a specific job by ID
  async getJob(jobId: string) {
    // Search through all queues to find the job
    for (const [queueType, queue] of this.queues) {
      try {
        const job = await queue.getJob(jobId)
        if (job) {
          return {
            id: job.id,
            name: job.name,
            data: job.data,
            state: await job.getState(),
            progress: job.progress,
            returnvalue: job.returnvalue,
            failedReason: job.failedReason,
            processedOn: job.processedOn,
            finishedOn: job.finishedOn,
            timestamp: job.timestamp,
            delay: job.delay,
            attemptsMade: job.attemptsMade,
            queueName: queueType
          }
        }
      } catch (error) {
        // Job not found in this queue, continue searching
        continue
      }
    }
    
    return null
  }

  // Get jobs from a specific queue
  async getJobs(queueName?: string, options: { limit?: number; state?: string } = {}) {
    const { limit = 50, state } = options
    
    if (queueName) {
      const queue = this.getQueue(queueName)
      const jobs = await queue.getJobs([state as any || 'waiting', 'active', 'completed', 'failed', 'delayed'], 0, limit - 1)
      
      return jobs.map(job => ({
        id: job.id,
        name: job.name,
        data: job.data,
        progress: job.progress,
        state: job.state,
        createdAt: job.timestamp,
        processedAt: job.processedOn,
        failedReason: job.failedReason
      }))
    } else {
      // Get jobs from all queues
      const allJobs: any[] = []
      
      for (const [queueType, queue] of this.queues) {
        try {
          const jobs = await queue.getJobs([state as any || 'waiting', 'active', 'completed', 'failed', 'delayed'], 0, Math.floor(limit / this.queues.size))
          
          allJobs.push(...jobs.map(job => ({
            id: job.id,
            name: job.name,
            data: job.data,
            progress: job.progress,
            state: job.state,
            createdAt: job.timestamp,
            processedAt: job.processedOn,
            failedReason: job.failedReason,
            queue: queueType
          })))
        } catch (error) {
          console.error(`Error getting jobs from ${queueType}:`, error)
        }
      }
      
      // Sort by creation time and limit
      return allJobs
        .sort((a, b) => b.createdAt - a.createdAt)
        .slice(0, limit)
    }
  }

  // Pause repeatable jobs (cron jobs) for a specific processor type
  async pauseRepeatableJob(processorType: ProcessingStage) {
    const config = getProcessorConfig(processorType)
    if (!config) {
      throw new Error(`Processor ${processorType} not found in registry`)
    }

    const queueName = config.queueType === 'CUSTOM' ? config.customQueueName! : config.queueType
    const queue = this.getQueue(queueName)
    
    // Get all repeatable jobs and remove the one for this processor
    const repeatableJobs = await queue.getRepeatableJobs()
    const jobToRemove = repeatableJobs.find(job => 
      job.name === processorType
    )
    
    if (jobToRemove) {
      await queue.removeRepeatableByKey(jobToRemove.key)
      console.log(`[QueueManager] Paused repeatable job for ${processorType}`)
      return true
    }
    
    console.log(`[QueueManager] No repeatable job found for ${processorType}`)
    return false
  }

  // Resume repeatable jobs (cron jobs) for a specific processor type
  async resumeRepeatableJob(processorType: ProcessingStage, cronPattern: string = '*/30 * * * *') {
    const config = getProcessorConfig(processorType)
    if (!config) {
      throw new Error(`Processor ${processorType} not found in registry`)
    }

    const queueName = config.queueType === 'CUSTOM' ? config.customQueueName! : config.queueType
    const queue = this.getQueue(queueName)
    
    // Schedule the repeatable job
    const jobId = `repeat-${processorType}`
    await queue.upsertJobScheduler(
      jobId,
      {
        pattern: cronPattern,
        tz: 'UTC'
      },
      {
        name: processorType,
        data: {
          processorType,
          options: config.defaultOptions
        },
        opts: {
          priority: this.getPriorityForQueue(queueName),
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: {
            type: 'exponential' as const,
            delay: 2000
          }
        }
      }
    )
    
    console.log(`[QueueManager] Resumed repeatable job for ${processorType} with pattern ${cronPattern}`)
    return true
  }

  // Pause all repeatable jobs
  async pauseAllRepeatableJobs() {
    const results: { processorType: string; success: boolean }[] = []
    
    for (const config of PROCESSOR_REGISTRY) {
      if (config.enabled) {
        try {
          const success = await this.pauseRepeatableJob(config.processorType)
          results.push({ processorType: config.processorType, success })
        } catch (error) {
          console.error(`Failed to pause ${config.processorType}:`, error)
          results.push({ processorType: config.processorType, success: false })
        }
      }
    }
    
    return results
  }

  // Resume all repeatable jobs
  async resumeAllRepeatableJobs() {
    const results: { processorType: string; success: boolean }[] = []
    
    for (const config of PROCESSOR_REGISTRY) {
      if (config.enabled) {
        try {
          const success = await this.resumeRepeatableJob(config.processorType)
          results.push({ processorType: config.processorType, success })
        } catch (error) {
          console.error(`Failed to resume ${config.processorType}:`, error)
          results.push({ processorType: config.processorType, success: false })
        }
      }
    }
    
    return results
  }

  // Get status of repeatable jobs
  async getRepeatableJobsStatus() {
    const status: { processorType: string; isActive: boolean; schedule?: string }[] = []
    
    for (const config of PROCESSOR_REGISTRY) {
      if (config.enabled) {
        const queueName = config.queueType === 'CUSTOM' ? config.customQueueName! : config.queueType
        const queue = this.getQueue(queueName)
        
        try {
          const repeatableJobs = await queue.getRepeatableJobs()
          const jobExists = repeatableJobs.some(job => 
            job.name === config.processorType
          )
          
          const foundJob = repeatableJobs.find(job => 
            job.name === config.processorType
          )
          
          status.push({
            processorType: config.processorType,
            isActive: jobExists,
            schedule: foundJob?.pattern || undefined
          })
        } catch (error) {
          console.error(`Failed to check status for ${config.processorType}:`, error)
          status.push({
            processorType: config.processorType,
            isActive: false
          })
        }
      }
    }
    
    return status
  }

  // Legacy methods for backward compatibility (pause entire queue)
  async pauseQueue(queueName: string) {
    const queue = this.getQueue(queueName)
    await queue.pause()
    console.log(`[QueueManager] Paused entire queue: ${queueName}`)
  }

  async resumeQueue(queueName: string) {
    const queue = this.getQueue(queueName)
    await queue.resume()
    console.log(`[QueueManager] Resumed entire queue: ${queueName}`)
  }

  // Clear a queue
  async clearQueue(queueName: string) {
    const queue = this.getQueue(queueName)
    await queue.obliterate({ force: true })
    console.log(`[QueueManager] Cleared queue: ${queueName}`)
  }

  // Stop specific worker
  async stopWorker(queueType: QueueType | string) {
    const worker = this.workers.get(queueType)
    if (worker) {
      await worker.close()
      this.workers.delete(queueType)
      console.log(`[QueueManager] Stopped ${queueType} worker`)
    }
  }

  // Stop all workers
  async stopAllWorkers() {
    for (const [queueType, worker] of this.workers) {
      await worker.close()
      console.log(`[QueueManager] Stopped ${queueType} worker`)
    }
    this.workers.clear()
  }

  // Cleanup all resources
  async cleanup() {
    await this.stopAllWorkers()
    
    for (const [queueType, queue] of this.queues) {
      await queue.close()
      console.log(`[QueueManager] Closed ${queueType} queue`)
    }
    
    this.queues.clear()
  }
}

// Export singleton instance
export const processorQueueManager = ProcessorQueueManager.getInstance()
