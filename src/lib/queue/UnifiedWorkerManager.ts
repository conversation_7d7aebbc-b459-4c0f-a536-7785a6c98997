import { processorQueueManager } from './ProcessorQueueManager'
import { getAllQueueTypes, getProcessorsForQueue, getCustomQueues } from './config/processor-registry'
import { QueueType } from './config/queue-config'

export class UnifiedWorkerManager {
  private static instance: UnifiedWorkerManager
  private workers: Map<string, any> = new Map()
  private isRunning: boolean = false

  private constructor() {}

  static getInstance(): UnifiedWorkerManager {
    if (!UnifiedWorkerManager.instance) {
      UnifiedWorkerManager.instance = new UnifiedWorkerManager()
    }
    return UnifiedWorkerManager.instance
  }

  async startAllWorkers(): Promise<void> {
    if (this.isRunning) {
      console.log('[UnifiedWorkerManager] Workers already running')
      return
    }

    console.log('[UnifiedWorkerManager] Starting all workers...')
    
    try {
      // Initialize cron jobs first
      console.log('[UnifiedWorkerManager] Initializing cron jobs...')
      await processorQueueManager.initializeCronJobs()
      
      // Get all queue types (standard + custom)
      const allQueueTypes = getAllQueueTypes()
      
      // Start workers for each queue type
      const workerPromises = allQueueTypes.map(queueType => 
        this.startWorkerForQueue(queueType as string)
      )

      await Promise.all(workerPromises)
      
      this.isRunning = true
      console.log(`[UnifiedWorkerManager] Successfully started workers for ${allQueueTypes.length} queues`)
      
      // Log queue details
      this.logQueueDetails()
      
    } catch (error) {
      console.error('[UnifiedWorkerManager] Failed to start workers:', error)
      throw error
    }
  }

  async startWorkerForQueue(queueType: string): Promise<void> {
    try {
      console.log(`[UnifiedWorkerManager] Starting worker for queue: ${queueType}`)
      
      // Start the worker using the processor queue manager
      await processorQueueManager.startWorker(queueType as QueueType)
      
      console.log(`[UnifiedWorkerManager] Worker started for queue: ${queueType}`)
      
    } catch (error) {
      console.error(`[UnifiedWorkerManager] Failed to start worker for queue ${queueType}:`, error)
      throw error
    }
  }

  async stopAllWorkers(): Promise<void> {
    if (!this.isRunning) {
      console.log('[UnifiedWorkerManager] No workers running')
      return
    }

    console.log('[UnifiedWorkerManager] Stopping all workers...')
    
    try {
      // Stop all workers
      const stopPromises = Array.from(this.workers.keys()).map(queueType =>
        this.stopWorkerForQueue(queueType)
      )

      await Promise.all(stopPromises)
      
      this.workers.clear()
      this.isRunning = false
      
      console.log('[UnifiedWorkerManager] All workers stopped')
      
    } catch (error) {
      console.error('[UnifiedWorkerManager] Error stopping workers:', error)
      throw error
    }
  }

  async stopWorkerForQueue(queueType: string): Promise<void> {
    try {
      console.log(`[UnifiedWorkerManager] Stopping worker for queue: ${queueType}`)
      
      // Stop the worker using the processor queue manager
      await processorQueueManager.stopWorker(queueType as QueueType)
      
      this.workers.delete(queueType)
      console.log(`[UnifiedWorkerManager] Worker stopped for queue: ${queueType}`)
      
    } catch (error) {
      console.error(`[UnifiedWorkerManager] Failed to stop worker for queue ${queueType}:`, error)
      throw error
    }
  }


  private logQueueDetails(): void {
    console.log('\n[UnifiedWorkerManager] Queue Configuration Summary:')
    console.log('=' .repeat(60))
    
    // Standard queues
    const standardQueues: QueueType[] = ['SHORT', 'MEDIUM', 'LONG']
    standardQueues.forEach(queueType => {
      const processors = getProcessorsForQueue(queueType)
      console.log(`\n${queueType} QUEUE (${processors.length} processors):`)
      processors.forEach(processor => {
        console.log(`  - ${processor.processorType}: ${processor.description}`)
      })
    })
    
    // Custom queues
    const customQueues = getCustomQueues()
    if (customQueues.length > 0) {
      console.log(`\nCUSTOM QUEUES (${customQueues.length} processors):`)
      customQueues.forEach(processor => {
        console.log(`  - ${processor.processorType} (${processor.customQueueName}): ${processor.description}`)
        console.log(`    Config: ${processor.customQueueConfig?.maxRuntime}min runtime, ${processor.customQueueConfig?.concurrency} concurrency`)
      })
    }
    
    console.log('\n' + '=' .repeat(60))
  }

  getWorkerStatus(): { queueType: string; isRunning: boolean }[] {
    return Array.from(this.workers.keys()).map(queueType => ({
      queueType,
      isRunning: this.workers.has(queueType)
    }))
  }

  isWorkersRunning(): boolean {
    return this.isRunning
  }
}

// Export singleton instance
export const unifiedWorkerManager = UnifiedWorkerManager.getInstance()
