import { ProcessingStage } from '../../../types/processing'
import { QueueType } from './queue-config'

// Processor configuration interface
export interface ProcessorConfig {
  processorType: ProcessingStage
  queueType: QueueType | 'CUSTOM'
  customQueueName?: string // Required if queueType is 'CUSTOM'
  customQueueConfig?: {
    maxRuntime: number // in minutes
    concurrency: number
    attempts: number
    backoff: {
      type: 'exponential' | 'fixed'
      delay: number
    }
    removeOnComplete: number
    removeOnFail: number
  }
  defaultOptions: Record<string, any>
  description: string
  enabled: boolean
  // Processor-specific configuration
  processorClass: string // Path to processor class
  constructorArgs?: 'none' | 'options' // How to instantiate the processor
  executionMethod?: string // Method to call for execution (default: 'process')
  category?: 'BACKGROUND_SYNC' | 'STANDARD' // Category for UI grouping
}

// Registry of all processors and their configurations
export const PROCESSOR_REGISTRY: ProcessorConfig[] = [
  // BACKGROUND SYNC PROCESSORS (Always running, critical system processes - LONG queue for resource-intensive operations)
  {
    processorType: 'email_worker',
    queueType: 'LONG',
    defaultOptions: {
      limit: 50,
      batchSize: 10
    },
    description: 'Gmail email synchronization (always running)',
    enabled: true, // ALWAYS ENABLED
    processorClass: 'GmailEmailFetcherProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'BACKGROUND_SYNC'
  },
  {
    processorType: 'fireflies_worker',
    queueType: 'LONG',
    defaultOptions: {
      limit: 30,
      batchSize: 5
    },
    description: 'Fireflies transcript synchronization (always running)',
    enabled: true, // ALWAYS ENABLED
    processorClass: 'FirefliesTranscriptProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'BACKGROUND_SYNC'
  },

  // SHORT QUEUE PROCESSORS (0-1 min runtime)
  {
    processorType: 'email_validation',
    queueType: 'SHORT',
    defaultOptions: {
      limit: 100,
      batchSize: 20
    },
    description: 'Validates email addresses for contacts',
    enabled: true, // ENABLED BY DEFAULT
    processorClass: 'EmailValidatorProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'job_tiering',
    queueType: 'SHORT',
    defaultOptions: {
      limit: 50,
      batchSize: 10
    },
    description: 'Categorizes jobs by tier/priority',
    enabled: true,
    processorClass: 'JobTierProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },

  // MEDIUM QUEUE PROCESSORS (1-5 min runtime)
  {
    processorType: 'deal_worker',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 10,
      batchSize: 1
    },
    description: 'Processes uploaded deals (triggered on upload)',
    enabled: true, // ENABLED BY DEFAULT
    processorClass: 'DealProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'deal_processor',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 5,
      batchSize: 1
    },
    description: 'Standard deal processing for uploaded files',
    enabled: true, // ENABLED BY DEFAULT
    processorClass: 'DealProcessor',
    constructorArgs: 'options',
    executionMethod: 'processJob'
  },
  {
    processorType: 'deal_processor_v2',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 5,
      batchSize: 1
    },
    description: 'Advanced deal processing with enhanced features',
    enabled: true, // DISABLED FOR TESTING
    processorClass: 'DealProcessorV2',
    constructorArgs: 'options',
    executionMethod: 'processJob'
  },
  {
    processorType: 'deal_requirement',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 15,
      batchSize: 3
    },
    description: 'Processes deal requirements and criteria',
    enabled: true,
    processorClass: 'DealRequirementProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'contact_enrichment_v2',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 50,
      batchSize: 10
    },
    description: 'Enriches contact data with additional information',
    enabled: true,
    processorClass: 'ContactEnrichmentProcessorV2',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'company_overview_v2',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 25,
      batchSize: 5
    },
    description: 'Generates company overview summaries',
    enabled: true,
    processorClass: 'CompanyOverviewProcessorV2',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'company_investment_criteria',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 20,
      batchSize: 5
    },
    description: 'Analyzes company investment criteria',
    enabled: true,
    processorClass: 'CompanyInvestmentCriteriaProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'contact_investment_criteria',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 20,
      batchSize: 5
    },
    description: 'Analyzes contact investment criteria',
    enabled: true,
    processorClass: 'ContactInvestmentCriteriaProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'email_generation',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 30,
      batchSize: 5
    },
    description: 'Generates personalized emails for contacts',
    enabled: true,
    processorClass: 'EmailGenerationProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'investment_criteria',
    queueType: 'MEDIUM',
    defaultOptions: {
      limit: 20,
      batchSize: 5
    },
    description: 'Processes general investment criteria analysis',
    enabled: true,
    processorClass: 'InvestmentCriteriaProcessor',
    constructorArgs: 'none',
    executionMethod: 'processInvestmentCriteria'
  },

  // LONG QUEUE PROCESSORS (5+ min runtime)
  {
    processorType: 'website_scraping',
    queueType: 'LONG',
    defaultOptions: {
      limit: 10,
      batchSize: 2
    },
    description: 'Scrapes company websites for additional data',
    enabled: true,
    processorClass: 'CompanyWebCrawlerProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'company_web_crawl',
    queueType: 'LONG',
    defaultOptions: {
      limit: 5,
      batchSize: 1
    },
    description: 'Performs deep web crawling for company data',
    enabled: true,
    processorClass: 'CompanyWebCrawlerProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'smartlead_sync',
    queueType: 'LONG',
    defaultOptions: {
      limit: 5,
      batchSize: 1
    },
    description: 'Syncs data with SmartLead platform',
    enabled: true,
    processorClass: 'SmartleadProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'article_html_fetch',
    queueType: 'LONG',
    defaultOptions: {
      limit: 15,
      batchSize: 3
    },
    description: 'Fetches HTML content for articles',
    enabled: true,
    processorClass: 'ArticleHTMLFetcherProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'article_enrichment',
    queueType: 'LONG',
    defaultOptions: {
      limit: 10,
      batchSize: 2
    },
    description: 'Enriches article content with additional data',
    enabled: true,
    processorClass: 'ArticleEnrichmentProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'article_link_fetch',
    queueType: 'LONG',
    defaultOptions: {
      limit: 20,
      batchSize: 4
    },
    description: 'Fetches and processes article links',
    enabled: true,
    processorClass: 'ArticleLinkFetcherProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },

  // STORAGE MIGRATION PROCESSORS
  {
    processorType: 'storage_migration_single',
    queueType: 'LONG',
    defaultOptions: {
      limit: 50,
      batchSize: 1
    },
    description: 'Migrates a single file between storage providers',
    enabled: true, // ENABLED BY DEFAULT
    processorClass: 'StorageMigrationProcessor',
    constructorArgs: 'options',
    executionMethod: 'processSingleMigration'
  },
  {
    processorType: 'storage_migration_bulk',
    queueType: 'LONG',
    defaultOptions: {
      limit: 20,
      batchSize: 10
    },
    description: 'Migrates multiple files between storage providers in batches',
    enabled: true, // ENABLED BY DEFAULT
    processorClass: 'StorageMigrationProcessor',
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },

  // CUSTOM QUEUE EXAMPLES
  {
    processorType: 'high_priority_processing' as any,
    queueType: 'CUSTOM',
    customQueueName: 'high-priority-queue',
    customQueueConfig: {
      maxRuntime: 2, // 2 minutes
      concurrency: 15,
      attempts: 5,
      backoff: { type: 'exponential', delay: 1000 },
      removeOnComplete: 100,
      removeOnFail: 50
    },
    defaultOptions: {
      limit: 200,
      batchSize: 25
    },
    description: 'High priority processing with custom queue configuration',
    enabled: true, // Disabled by default
    processorClass: 'EmailValidatorProcessor', // Example processor
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  },
  {
    processorType: 'bulk_processing' as any,
    queueType: 'CUSTOM',
    customQueueName: 'bulk-processing-queue',
    customQueueConfig: {
      maxRuntime: 60, // 60 minutes
      concurrency: 1,
      attempts: 1,
      backoff: { type: 'fixed', delay: 5000 },
      removeOnComplete: 10,
      removeOnFail: 5
    },
    defaultOptions: {
      limit: 1000,
      batchSize: 100
    },
    description: 'Bulk processing with custom queue configuration',
    enabled: true, // Disabled by default
    processorClass: 'CompanyWebCrawlerProcessor', // Example processor
    constructorArgs: 'options',
    executionMethod: 'process',
    category: 'STANDARD'
  }
]

// Helper functions
export function getProcessorConfig(processorType: ProcessingStage): ProcessorConfig | undefined {
  return PROCESSOR_REGISTRY.find(config => config.processorType === processorType)
}

export function getProcessorsForQueue(queueType: QueueType | 'CUSTOM'): ProcessorConfig[] {
  return PROCESSOR_REGISTRY.filter(config => config.queueType === queueType && config.enabled)
}

export function getEnabledProcessors(): ProcessorConfig[] {
  return PROCESSOR_REGISTRY.filter(config => config.enabled)
}

export function getCustomQueues(): ProcessorConfig[] {
  return PROCESSOR_REGISTRY.filter(config => config.queueType === 'CUSTOM' && config.enabled)
}

export function getAllQueueTypes(): (QueueType | string)[] {
  const standardQueues: QueueType[] = ['SHORT', 'MEDIUM', 'LONG']
  const customQueues = getCustomQueues().map(config => config.customQueueName!)
  return [...standardQueues, ...customQueues]
}
