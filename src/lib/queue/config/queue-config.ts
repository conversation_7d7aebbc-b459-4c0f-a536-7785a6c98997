export interface QueueConfig {
  name: string
  maxRuntime: number // in minutes
  concurrency: number
  attempts: number
  backoff: {
    type: 'exponential' | 'fixed'
    delay: number
  }
  removeOnComplete: number
  removeOnFail: number
}

// Base configuration for all queues
export const BASE_QUEUE_CONFIG: Omit<QueueConfig, 'name' | 'maxRuntime'> = {
  concurrency: 5,
  attempts: 3,
  backoff: { type: 'exponential', delay: 2000 },
  removeOnComplete: 50,
  removeOnFail: 20
}

// Queue definitions based on runtime
export const QUEUE_DEFINITIONS = {
  SHORT: {
    name: 'short-processing',
    maxRuntime: 1, // 1 minute max
    ...BASE_QUEUE_CONFIG
  },
  MEDIUM: {
    name: 'medium-processing', 
    maxRuntime: 5, // 5 minutes max
    ...BASE_QUEUE_CONFIG
  },
  LONG: {
    name: 'long-processing',
    maxRuntime: 60, // 60 minutes (1 hour) max
    ...BASE_QUEUE_CONFIG
  }
} as const

export type QueueType = keyof typeof QUEUE_DEFINITIONS

