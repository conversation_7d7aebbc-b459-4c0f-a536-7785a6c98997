import { pool } from "@/lib/db";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// Types
export interface User {
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  username?: string;
  role: string;
  is_admin: boolean;
  is_active: boolean;
  permissions: string[];
  last_login?: string;
  impersonated_by?: number;
  original_user_id?: number;
}

export interface AdminAuthResult {
  success: boolean;
  user?: User;
  error?: string;
  isImpersonating?: boolean;
  originalAdmin?: User;
}

// Permission constants
export const PERMISSIONS = {
  // User management
  USER_READ: 'user:read',
  USER_WRITE: 'user:write', 
  USER_DELETE: 'user:delete',
  
  // Admin specific
  ADMIN_IMPERSONATE: 'admin:impersonate',
  ADMIN_RESET_PASSWORD: 'admin:reset_password',
  ADMIN_MANAGE_ROLES: 'admin:manage_roles',
  ADMIN_VIEW_LOGS: 'admin:view_logs',
  ADMIN_SYSTEM_CONFIG: 'admin:system_config',
  
  // Data management
  DATA_EXPORT: 'data:export',
  DATA_IMPORT: 'data:import',
  DATA_DELETE: 'data:delete',
  
  // Campaign management
  CAMPAIGN_MANAGE: 'campaign:manage',
  CAMPAIGN_SEND: 'campaign:send'
} as const;

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];

// Default role permissions
export const ROLE_PERMISSIONS = {
  admin: Object.values(PERMISSIONS),
  manager: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.DATA_IMPORT,
    PERMISSIONS.CAMPAIGN_MANAGE,
    PERMISSIONS.CAMPAIGN_SEND
  ],
  user: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.DATA_EXPORT
  ],
  guest: []
} as const;

/**
 * Get user by email from database
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const result = await pool.query(`
      SELECT 
        user_id, first_name, last_name, email, username, role,
        is_admin, is_active, permissions, last_login,
        impersonated_by, original_user_id
      FROM users 
      WHERE email = $1 AND deleted_at IS NULL
    `, [email]);

    if (result.rows.length === 0) {
      return null;
    }

    const user = result.rows[0];
    return {
      user_id: user.user_id,
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      username: user.username,
      role: user.role,
      is_admin: user.is_admin,
      is_active: user.is_active,
      permissions: user.permissions || ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [],
      last_login: user.last_login,
      impersonated_by: user.impersonated_by,
      original_user_id: user.original_user_id
    };
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}

/**
 * Get user by ID from database
 */
export async function getUserById(userId: number): Promise<User | null> {
  try {
    const result = await pool.query(`
      SELECT 
        user_id, first_name, last_name, email, username, role,
        is_admin, is_active, permissions, last_login,
        impersonated_by, original_user_id
      FROM users 
      WHERE user_id = $1 AND deleted_at IS NULL
    `, [userId]);

    if (result.rows.length === 0) {
      return null;
    }

    const user = result.rows[0];
    return {
      user_id: user.user_id,
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      username: user.username,
      role: user.role,
      is_admin: user.is_admin,
      is_active: user.is_active,
      permissions: user.permissions || ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [],
      last_login: user.last_login,
      impersonated_by: user.impersonated_by,
      original_user_id: user.original_user_id
    };
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}

/**
 * Check if user has required permissions
 */
export function hasPermission(user: User, requiredPermission: Permission): boolean {
  if (!user.is_active) {
    return false;
  }

  // Admins have all permissions by default
  if (user.is_admin) {
    return true;
  }

  // Check specific permissions
  return user.permissions.includes(requiredPermission);
}

/**
 * Check if user has any of the required permissions
 */
export function hasAnyPermission(user: User, requiredPermissions: Permission[]): boolean {
  if (!user.is_active) {
    return false;
  }

  // Admins have all permissions by default
  if (user.is_admin) {
    return true;
  }

  // Check if user has any of the required permissions
  return requiredPermissions.some(permission => user.permissions.includes(permission));
}

/**
 * Check if user has admin role
 */
export function isAdmin(user: User): boolean {
  return user.is_admin && user.is_active;
}

/**
 * Authenticate admin user from session
 */
export async function authenticateAdmin(request?: NextRequest): Promise<AdminAuthResult> {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return { success: false, error: 'No active session' };
    }

    const user = await getUserByEmail(session.user.email);
    if (!user) {
      return { success: false, error: 'User not found' };
    }

    if (!user.is_active) {
      return { success: false, error: 'Account is inactive' };
    }

    if (!isAdmin(user)) {
      return { success: false, error: 'Admin access required' };
    }

    // Check for impersonation
    let isImpersonating = false;
    let originalAdmin: User | undefined;
    
    if (user.impersonated_by) {
      isImpersonating = true;
      originalAdmin = await getUserById(user.impersonated_by);
    }

    return { 
      success: true, 
      user,
      isImpersonating,
      originalAdmin
    };
  } catch (error) {
    console.error('Error authenticating admin:', error);
    return { success: false, error: 'Authentication error' };
  }
}

/**
 * Middleware function to check admin permissions
 */
export async function requireAdminPermission(
  requiredPermission?: Permission,
  request?: NextRequest
): Promise<AdminAuthResult> {
  const authResult = await authenticateAdmin(request);
  
  if (!authResult.success || !authResult.user) {
    return authResult;
  }

  // If specific permission is required, check it
  if (requiredPermission && !hasPermission(authResult.user, requiredPermission)) {
    return { 
      success: false, 
      error: `Permission '${requiredPermission}' required` 
    };
  }

  return authResult;
}

/**
 * Log user activity
 */
export async function logUserActivity(
  userId: number,
  action: string,
  description: string,
  performedBy?: number,
  impersonationActive: boolean = false,
  request?: NextRequest,
  additionalData?: {
    resourceType?: string;
    resourceId?: string;
    oldValues?: any;
    newValues?: any;
  }
) {
  try {
    const clientIP = request?.headers.get('x-forwarded-for') || 
                    request?.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request?.headers.get('user-agent') || 'unknown';
    const sessionId = request?.headers.get('x-session-id') || null;

    await pool.query(`
      INSERT INTO user_activity_log (
        user_id, action, description, ip_address, user_agent, session_id,
        performed_by, impersonation_active, resource_type, resource_id,
        old_values, new_values
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
    `, [
      userId, action, description, clientIP, userAgent, sessionId,
      performedBy, impersonationActive, 
      additionalData?.resourceType, additionalData?.resourceId,
      additionalData?.oldValues ? JSON.stringify(additionalData.oldValues) : null,
      additionalData?.newValues ? JSON.stringify(additionalData.newValues) : null
    ]);
  } catch (error) {
    console.error('Error logging user activity:', error);
  }
}

/**
 * Update user last login
 */
export async function updateUserLastLogin(userId: number): Promise<void> {
  try {
    await pool.query(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP, login_count = login_count + 1 WHERE user_id = $1',
      [userId]
    );
  } catch (error) {
    console.error('Error updating user last login:', error);
  }
}

/**
 * Create a middleware wrapper for API routes that require admin permissions
 */
export function withAdminAuth(
  requiredPermission?: Permission,
  handler?: (authResult: AdminAuthResult, request: NextRequest, ...args: any[]) => Promise<Response>
) {
  return async (request: NextRequest, ...args: any[]) => {
    const authResult = await requireAdminPermission(requiredPermission, request);
    
    if (!authResult.success) {
      return new Response(
        JSON.stringify({ error: authResult.error }), 
        { 
          status: authResult.error?.includes('session') ? 401 : 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (handler) {
      return handler(authResult, request, ...args);
    }

    return new Response(
      JSON.stringify({ success: true, user: authResult.user }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  };
}
