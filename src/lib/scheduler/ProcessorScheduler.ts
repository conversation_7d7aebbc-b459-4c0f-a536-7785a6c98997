import { BaseProcessor } from '../processors/BaseProcessor'
import { ProcessorResult, ProcessingFilters } from '../../types/processing'
import { EmailValidatorProcessor } from '../processors/EmailValidatorProcessor'
import { CompanyOverviewProcessorV2 } from '../processors/CompanyOverviewProcessorV2'
import { CompanyInvestmentCriteriaProcessor } from '../processors/CompanyInvestmentCriteriaProcessor'
import { ContactEnrichmentProcessorV2 } from '../processors/ContactEnrichmentProcessorV2'
import { ContactInvestmentCriteriaProcessor } from '../processors/ContactInvestmentCriteriaProcessor'
import { EmailGenerationProcessor } from '../processors/EmailGenerationProcessor'
import { SmartleadProcessor } from '../processors/SmartleadProcessor'
import { JobTierProcessor } from '../processors/JobTierProcessor'
import { CompanyWebCrawlerProcessor } from '../processors/CompanyWebCrawlerProcessor'
import { ArticleHTMLFetcherProcessor } from '../processors/ArticleHTMLFetcherProcessor'
import { ArticleEnrichmentProcessor } from '../processors/ArticleEnrichmentProcessor'
import { ArticleLinkFetcherProcessor } from '../processors/ArticleLinkFetcherProcessor'
import { ProcessingStage } from '../../types/processing'
import { getProcessorLimit, validateProcessorLimit, getProcessorBatchSize } from '../../config/processor-limits'

interface ScheduledJob {
  id: string
  stage: ProcessingStage
  processor: string
  schedule: string // cron expression
  enabled: boolean
  lastRun?: Date
  nextRun?: Date
  isRunning: boolean
}

interface JobExecution {
  jobId: string
  stage: ProcessingStage
  startTime: Date
  endTime?: Date
  result?: ProcessorResult
  error?: string
}

export class ProcessorScheduler {
  private jobs: Map<string, ScheduledJob> = new Map()
  private processors: Map<string, BaseProcessor> = new Map()
  private runningJobs: Map<string, JobExecution> = new Map()
  private scheduleIntervals: Map<string, NodeJS.Timeout> = new Map()

  constructor() {
    this.initializeProcessors()
    this.initializeDefaultJobs()
  }

  /**
   * Initialize available processors with default configurations
   * Each processor has its own optimized Bottleneck configuration
   */
  private initializeProcessors(): void {
    // Email validation with BulkEmailChecker rate limits (already configured in constructor)
    this.processors.set('email_validator', new EmailValidatorProcessor())
    
    // Contact enrichment V2 with Perplexity rate limits (already configured in constructor)
    this.processors.set('contact_enrichment_v2', new ContactEnrichmentProcessorV2())
    
    // Contact investment criteria with Perplexity rate limits (already configured in constructor)
    this.processors.set('contact_investment_criteria', new ContactInvestmentCriteriaProcessor())
    
    // Company overview V2 with Perplexity rate limits (already configured in constructor)
    this.processors.set('company_overview_v2', new CompanyOverviewProcessorV2())
    
    // Company investment criteria with Perplexity rate limits (already configured in constructor)
    this.processors.set('company_investment_criteria', new CompanyInvestmentCriteriaProcessor())
    
    // Web crawler with moderate concurrency
    this.processors.set('company_web_crawler', new CompanyWebCrawlerProcessor())
    
    // Email generation (standard rate limits)
    this.processors.set('email_generation', new EmailGenerationProcessor())

    // Smartlead sync with conservative rate limits (configured in constructor)
    this.processors.set('smartlead_sync', new SmartleadProcessor())

    // Job tiering (fast processing for simple lookups)
    this.processors.set('job_tiering', new JobTierProcessor())

    // Article HTML fetcher (moderate rate limits for web scraping)
    this.processors.set('article_html_fetcher', new ArticleHTMLFetcherProcessor())

    // Article enrichment (standard rate limits)
    this.processors.set('article_enrichment', new ArticleEnrichmentProcessor())
    
    // Article link fetcher processor (calls the scraping API for comprehensive scraping)
    this.processors.set('article_link_fetcher', new ArticleLinkFetcherProcessor())
  }

  /**
   * Initialize default scheduled jobs
   */
  private initializeDefaultJobs(): void {
    // Email validation - every 30 minutes
    this.addJob({
      id: 'email_validation_auto',
      stage: 'email_validation',
      processor: 'email_validator',
      schedule: '*/30 * * * *', // Every 30 minutes
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Company web crawler - every hour
    this.addJob({
      id: 'company_web_crawler_auto',
      stage: 'website_scraping',
      processor: 'company_web_crawler',
      schedule: '0 * * * *', // Every hour
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Company overview v2 extraction - every 2 hours
    this.addJob({
      id: 'company_overview_v2_auto',
      stage: 'company_overview_v2',
      processor: 'company_overview_v2',
      schedule: '0 */2 * * *', // Every 2 hours
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Company investment criteria extraction - every 4 hours
    this.addJob({
      id: 'company_investment_criteria_auto',
      stage: 'company_investment_criteria',
      processor: 'company_investment_criteria',
      schedule: '0 */4 * * *', // Every 4 hours
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Contact enrichment V2 (enhanced enrichment with additional fields) - every 2 hours
    this.addJob({
      id: 'contact_enrichment_v2_auto',
      stage: 'contact_enrichment_v2',
      processor: 'contact_enrichment_v2',
      schedule: '0 */2 * * *', // Every 2 hours
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Contact investment criteria extraction - every 6 hours
    this.addJob({
      id: 'contact_investment_criteria_auto',
      stage: 'contact_investment_criteria',
      processor: 'contact_investment_criteria',
      schedule: '0 */6 * * *', // Every 6 hours
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Email generation - every 2 hours
    this.addJob({
      id: 'email_generation_auto',
      stage: 'email_generation',
      processor: 'email_generation',
      schedule: '0 */2 * * *', // Every 2 hours
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Smartlead sync - every hour (after email generation)
    this.addJob({
      id: 'smartlead_sync_auto',
      stage: 'smartlead_sync',
      processor: 'smartlead_sync',
      schedule: '30 * * * *', // Every hour at 30 minutes past (offset from other jobs)
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Article HTML fetcher - every 30 minutes
    this.addJob({
      id: 'article_html_fetcher_auto',
      stage: 'article_html_fetch',
      processor: 'article_html_fetcher',
      schedule: '*/30 * * * *', // Every 30 minutes
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Article enrichment - every hour
    this.addJob({
      id: 'article_enrichment_auto',
      stage: 'article_enrichment',
      processor: 'article_enrichment',
      schedule: '0 * * * *', // Every hour
      enabled: false, // Disabled by default
      isRunning: false
    })

    // Article link fetcher - comprehensive scraping of all sources - every 4 hours
    this.addJob({
      id: 'article_link_fetcher_auto',
      stage: 'article_link_fetch',
      processor: 'article_link_fetcher',
      schedule: '0 */4 * * *', // Every 4 hours
      enabled: false, // Enabled by default for comprehensive scraping
      isRunning: false
    })

    // Job tiering - every 2 hours
    this.addJob({
      id: 'job_tiering_auto',
      stage: 'job_tiering',
      processor: 'job_tiering',
      schedule: '0 */2 * * *', // Every 2 hours
      enabled: true, // Disabled by default
      isRunning: false
    })
  }

  /**
   * Add a scheduled job
   */
  addJob(job: ScheduledJob): void {
    this.jobs.set(job.id, job)
    if (job.enabled) {
      this.scheduleJob(job)
    }
  }

  /**
   * Remove a scheduled job
   */
  removeJob(jobId: string): void {
    const interval = this.scheduleIntervals.get(jobId)
    if (interval) {
      clearInterval(interval)
      this.scheduleIntervals.delete(jobId)
    }
    this.jobs.delete(jobId)
  }

  /**
   * Enable/disable a job
   */
  toggleJob(jobId: string, enabled: boolean): void {
    const job = this.jobs.get(jobId)
    if (!job) return

    job.enabled = enabled
    
    if (enabled) {
      this.scheduleJob(job)
    } else {
      const interval = this.scheduleIntervals.get(jobId)
      if (interval) {
        clearInterval(interval)
        this.scheduleIntervals.delete(jobId)
      }
    }
  }

  /**
   * Schedule a job using cron-like timing
   */
  private scheduleJob(job: ScheduledJob): void {
    // Simple interval-based scheduling (in production, use a proper cron library)
    const intervalMs = this.parseCronToInterval(job.schedule)
    
    const interval = setInterval(async () => {
      if (!job.isRunning && job.enabled) {
        await this.executeJob(job.id)
      }
    }, intervalMs)
    
    this.scheduleIntervals.set(job.id, interval)
  }

  /**
   * Simple cron parser (basic implementation)
   * In production, use a proper cron library like node-cron
   */
  private parseCronToInterval(schedule: string): number {
    // Simple mappings for common patterns
    const patterns: Record<string, number> = {
      '*/30 * * * *': 30 * 60 * 1000,     // Every 30 minutes
      '0 * * * *': 60 * 60 * 1000,        // Every hour
      '0 */2 * * *': 2 * 60 * 60 * 1000,  // Every 2 hours
      '0 */4 * * *': 4 * 60 * 60 * 1000,  // Every 4 hours
      '0 0 * * *': 24 * 60 * 60 * 1000,   // Daily
    }
    
    return patterns[schedule] || 60 * 60 * 1000 // Default to 1 hour
  }

  /**
   * Execute a scheduled job
   */
  async executeJob(jobId: string): Promise<ProcessorResult | null> {
    const job = this.jobs.get(jobId)
    if (!job || job.isRunning) return null

    const processor = this.processors.get(job.processor)
    if (!processor) {
      console.error(`Processor ${job.processor} not found for job ${jobId}`)
      return null
    }

    // Get dynamic limits for this processor
    const processorLimits = getProcessorLimit(job.processor as any)
    const defaultLimit = processorLimits.defaultLimit
    const batchSize = getProcessorBatchSize(job.processor as any)

    // Set a timeout to prevent jobs from running indefinitely
    const jobTimeout = 30 * 60 * 1000 // 30 minutes timeout
    let timeoutHandle: NodeJS.Timeout | undefined

    job.isRunning = true
    job.lastRun = new Date()

    const execution: JobExecution = {
      jobId,
      stage: job.stage,
      startTime: new Date()
    }

    this.runningJobs.set(jobId, execution)

    try {
      console.log(`[Scheduler] Starting job ${jobId} (${job.stage}) with limit: ${defaultLimit}, batch size: ${batchSize}`)
      
      // Set timeout to force cleanup if job hangs
      timeoutHandle = setTimeout(() => {
        console.error(`[Scheduler] Job ${jobId} timed out after ${jobTimeout}ms`)
        job.isRunning = false
        this.runningJobs.delete(jobId)
      }, jobTimeout)
      
      // Create processor options with dynamic limits
      const processorOptions = {
        limit: defaultLimit,
        batchSize: batchSize
      }

      // Create a new processor instance with the dynamic options
      const ProcessorClass = processor.constructor as new (options?: any) => BaseProcessor
      const processorInstance = new ProcessorClass(processorOptions)
      
      const result = await processorInstance.process()
      
      // Clear timeout if job completes normally
      clearTimeout(timeoutHandle)
      
      execution.endTime = new Date()
      execution.result = result
      
      console.log(`[Scheduler] Job ${jobId} completed: ${result.processed} processed, ${result.successful} successful, ${result.failed} failed`)
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      // Clear timeout on error
      if (timeoutHandle) clearTimeout(timeoutHandle)
      
      execution.endTime = new Date()
      execution.error = errorMessage
      
      console.error(`[Scheduler] Job ${jobId} failed: ${errorMessage}`)
      
      return null
    } finally {
      // Ensure cleanup always happens
      job.isRunning = false
      this.runningJobs.delete(jobId)
      if (timeoutHandle) clearTimeout(timeoutHandle)
    }
  }

  /**
   * Execute a job manually (on-demand)
   * Creates a new processor instance with custom options and proper Bottleneck configuration
   */
  async executeManualJob(
    stage: ProcessingStage,
    options: { limit?: number; singleId?: number; multiIds?: number[]; filters?: ProcessingFilters; batchSize?: number; entityType?: 'contact' | 'company' | 'both'; campaignId?: string; bottleneckConfig?: any } = {}
  ): Promise<ProcessorResult | null> {
    const processorMap: Record<ProcessingStage, string> = {
      'email_validation': 'email_validator',
      'company_overview_v2': 'company_overview_v2',
      'company_investment_criteria': 'company_investment_criteria',
      'contact_investment_criteria': 'contact_investment_criteria',
      'website_scraping': 'company_web_crawler',
      'contact_enrichment_v2': 'contact_enrichment_v2',
      'email_generation': 'email_generation',
      'smartlead_sync': 'smartlead_sync',
      'job_tiering': 'job_tiering',
      'article_html_fetch': 'article_html_fetcher',
      'article_link_fetch': 'article_link_fetcher',
      'article_enrichment': 'article_enrichment'
    }

    const processorName = processorMap[stage]
    const processor = this.processors.get(processorName)
    
    if (!processor) {
      console.error(`Processor for stage ${stage} not found`)
      return null
    }

    // Generate unique job ID
    const jobId = `manual_${stage}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Check if manual job for this stage is already running
    const existingManualJob = Array.from(this.runningJobs.values()).find(
      job => job.stage === stage && job.jobId.startsWith('manual_')
    )
    
    if (existingManualJob) {
      console.warn(`[Scheduler] Manual job for stage ${stage} already running (${existingManualJob.jobId})`)
      return null
    }

    // Get dynamic limits for this processor if no limit is provided
    let finalLimit = options.limit
    let finalBatchSize = options.batchSize
    
    if (!finalLimit) {
      const processorLimits = getProcessorLimit(processorName as any)
      finalLimit = processorLimits.defaultLimit
    } else {
      // Validate the provided limit
      finalLimit = validateProcessorLimit(processorName as any, finalLimit)
    }
    
    if (!finalBatchSize) {
      finalBatchSize = getProcessorBatchSize(processorName as any)
    }

    // Create processor options with the provided options, including Bottleneck configuration
    const processorOptions = {
      limit: finalLimit,
      singleId: options.singleId,
      multiIds: options.multiIds,
      filters: options.filters,
      batchSize: finalBatchSize,
      entityType: options.entityType,
      campaignId: options.campaignId,
      bottleneckConfig: options.bottleneckConfig // Pass custom Bottleneck config if provided
    }

    // Create a new processor instance with the correct options
    // Note: The processor will use its default Bottleneck config unless a custom one is provided
    const ProcessorClass = processor.constructor as new (options?: any) => BaseProcessor
    const processorInstance = new ProcessorClass(processorOptions)
    
    const execution: JobExecution = {
      jobId,
      stage,
      startTime: new Date()
    }

    this.runningJobs.set(jobId, execution)

    try {
      console.log(`[Scheduler] Starting manual job ${jobId} (${stage}) with limit: ${finalLimit}, batch size: ${finalBatchSize}`)
      if (options.singleId) {
        console.log(`[Scheduler] Processing single ID: ${options.singleId}`)
      }
      if (options.multiIds && options.multiIds.length > 0) {
        console.log(`[Scheduler] Processing ${options.multiIds.length} IDs: ${options.multiIds.join(', ')}`)
      }
      if (options.filters) {
        console.log(`[Scheduler] Applying filters:`, options.filters)
      }
      if (options.entityType) {
        console.log(`[Scheduler] Processing entity type: ${options.entityType}`)
      }
      if (options.campaignId) {
        console.log(`[Scheduler] Processing campaign ID: ${options.campaignId}`)
      }
      
      const result = await processorInstance.process()
      
      execution.endTime = new Date()
      execution.result = result
      
      console.log(`[Scheduler] Manual job ${jobId} completed: ${result.processed} processed, ${result.successful} successful, ${result.failed} failed`)
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      execution.endTime = new Date()
      execution.error = errorMessage
      
      console.error(`[Scheduler] Manual job ${jobId} failed: ${errorMessage}`)
      
      return null
    } finally {
      // Ensure cleanup always happens
      this.runningJobs.delete(jobId)
    }
  }

  /**
   * Get all jobs
   */
  getJobs(): ScheduledJob[] {
    return Array.from(this.jobs.values())
  }

  /**
   * Get running jobs
   */
  getRunningJobs(): JobExecution[] {
    return Array.from(this.runningJobs.values())
  }

  /**
   * Get job status
   */
  getJobStatus(jobId: string): ScheduledJob | null {
    return this.jobs.get(jobId) || null
  }

  /**
   * Detect and clean up stuck jobs
   */
  cleanupStuckJobs(): void {
    const maxRunTime = 45 * 60 * 1000 // 45 minutes
    const now = new Date()
    
    console.log('[Scheduler] Checking for stuck jobs...')
    
    let cleanedCount = 0
    
    // Check scheduled jobs
    for (const job of this.jobs.values()) {
      if (job.isRunning && job.lastRun) {
        const runTime = now.getTime() - job.lastRun.getTime()
        if (runTime > maxRunTime) {
          console.warn(`[Scheduler] Cleaning up stuck scheduled job ${job.id} (running for ${Math.round(runTime / 60000)} minutes)`)
          job.isRunning = false
          cleanedCount++
        }
      }
    }
    
    // Check manual jobs in runningJobs map
    for (const [jobId, execution] of this.runningJobs.entries()) {
      const runTime = now.getTime() - execution.startTime.getTime()
      if (runTime > maxRunTime) {
        console.warn(`[Scheduler] Cleaning up stuck manual job ${jobId} (running for ${Math.round(runTime / 60000)} minutes)`)
        this.runningJobs.delete(jobId)
        cleanedCount++
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`[Scheduler] Cleaned up ${cleanedCount} stuck jobs`)
    } else {
      console.log('[Scheduler] No stuck jobs found')
    }
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    // Clear all intervals
    for (const interval of this.scheduleIntervals.values()) {
      clearInterval(interval)
    }
    this.scheduleIntervals.clear()
  }

  /**
   * Start the scheduler
   */
  start(): void {
    console.log('[Scheduler] Starting processor scheduler')
    
    // Clean up any stuck jobs from previous sessions
    this.cleanupStuckJobs()
    
    // Schedule all enabled jobs
    for (const job of this.jobs.values()) {
      if (job.enabled) {
        this.scheduleJob(job)
      }
    }
    
    // Set up periodic cleanup of stuck jobs every 15 minutes
    setInterval(() => {
      this.cleanupStuckJobs()
    }, 15 * 60 * 1000)
  }
}

// Global scheduler instance - initialized and auto-started
export const processorScheduler = new ProcessorScheduler()
processorScheduler.start()