export interface CronJobConfig {
  id: string
  cronExpression: string
  processorType: string
  queueType: 'SHORT' | 'MEDIUM' | 'LONG'
  options?: Record<string, any>
  enabled: boolean
  description: string
  category?: 'BACKGROUND_SYNC' | 'STANDARD' // Category for UI grouping
}

// All cron job configurations
export const CRON_JOBS: CronJobConfig[] = [
  // BACKGROUND SYNC CRON JOBS (Always running, critical system processes)
  {
    id: 'gmail_sync_cron',
    cronExpression: '* * * * *', // Every 1 minute
    processorType: 'email_worker',
    queueType: 'LONG', // Changed to match processor registry
    options: {
      limit: 50,
      batchSize: 10
    },
    enabled: false, // ALWAYS ENABLED - Critical system process
    description: 'Gmail email synchronization (always running)',
    category: 'BACKGROUND_SYNC'
  },
  {
    id: 'fireflies_sync_cron',
    cronExpression: '*/20 * * * *', // Every 20 minutes
    processorType: 'fireflies_worker',
    queueType: 'LONG', // Changed to match processor registry
    options: {
      limit: 30,
      batchSize: 5
    },
    enabled: false, // ALWAYS ENABLED - Critical system process
    description: 'Fireflies transcript synchronization (always running)',
    category: 'BACKGROUND_SYNC'
  },

  // SHORT QUEUE CRON JOBS (0-1 min runtime)
  {
    id: 'email_validation_cron',
    cronExpression: '*/30 * * * *', // Every 30 minutes
    processorType: 'email_validation',
    queueType: 'SHORT',
    options: {
      limit: 100,
      batchSize: 20
    },
    enabled: true,
    description: 'Validate email addresses every 30 minutes',
    category: 'STANDARD'
  },
  {
    id: 'job_tiering_cron',
    cronExpression: '0 */2 * * *', // Every 2 hours
    processorType: 'job_tiering',
    queueType: 'SHORT',
    options: {
      limit: 50,
      batchSize: 10
    },
    enabled: true,
    description: 'Categorize jobs by tier every 2 hours',
    category: 'STANDARD'
  },

  // MEDIUM QUEUE CRON JOBS (1-5 min runtime)
  {
    id: 'contact_enrichment_v2_cron',
    cronExpression: '0 */2 * * *', // Every 2 hours
    processorType: 'contact_enrichment_v2',
    queueType: 'MEDIUM',
    options: {
      limit: 50,
      batchSize: 10
    },
    enabled: true,
    description: 'Enrich contact data every 2 hours',
    category: 'STANDARD'
  },
  {
    id: 'email_generation_cron',
    cronExpression: '0 */2 * * *', // Every 2 hours
    processorType: 'email_generation',
    queueType: 'MEDIUM',
    options: {
      limit: 30,
      batchSize: 5
    },
    enabled: true,
    description: 'Generate personalized emails every 2 hours',
    category: 'STANDARD'
  },
  {
    id: 'company_overview_v2_cron',
    cronExpression: '0 */2 * * *', // Every 2 hours
    processorType: 'company_overview_v2',
    queueType: 'MEDIUM',
    options: {
      limit: 25,
      batchSize: 5
    },
    enabled: true,
    description: 'Generate company overviews every 2 hours',
    category: 'STANDARD'
  },
  {
    id: 'company_investment_criteria_cron',
    cronExpression: '0 */4 * * *', // Every 4 hours
    processorType: 'company_investment_criteria',
    queueType: 'MEDIUM',
    options: {
      limit: 20,
      batchSize: 5
    },
    enabled: true,
    description: 'Analyze company investment criteria every 4 hours',
    category: 'STANDARD'
  },
  {
    id: 'contact_investment_criteria_cron',
    cronExpression: '0 */6 * * *', // Every 6 hours
    processorType: 'contact_investment_criteria',
    queueType: 'MEDIUM',
    options: {
      limit: 20,
      batchSize: 5
    },
    enabled: true,
    description: 'Analyze contact investment criteria every 6 hours',
    category: 'STANDARD'
  },

  // LONG QUEUE CRON JOBS (5+ min runtime)
  {
    id: 'website_scraping_cron',
    cronExpression: '0 */8 * * *', // Every 8 hours
    processorType: 'website_scraping',
    queueType: 'LONG',
    options: {
      limit: 10,
      batchSize: 2
    },
    enabled: true,
    description: 'Scrape company websites every 8 hours',
    category: 'STANDARD'
  },
  {
    id: 'smartlead_sync_cron',
    cronExpression: '0 */12 * * *', // Every 12 hours
    processorType: 'smartlead_sync',
    queueType: 'LONG',
    options: {
      limit: 5,
      batchSize: 1
    },
    enabled: true,
    description: 'Sync with SmartLead every 12 hours',
    category: 'STANDARD'
  },
  {
    id: 'company_web_crawl_cron',
    cronExpression: '0 0 */2 * *', // Every 2 days at midnight
    processorType: 'company_web_crawl',
    queueType: 'LONG',
    options: {
      limit: 5,
      batchSize: 1
    },
    enabled: true,
    description: 'Deep web crawl for company data every 2 days',
    category: 'STANDARD'
  }
]

// Helper function to get cron jobs by queue type
export function getCronJobsForQueue(queueType: 'SHORT' | 'MEDIUM' | 'LONG'): CronJobConfig[] {
  return CRON_JOBS.filter(job => job.queueType === queueType && job.enabled)
}

// Helper function to get all enabled cron jobs
export function getEnabledCronJobs(): CronJobConfig[] {
  return CRON_JOBS.filter(job => job.enabled)
}

// Helper function to get cron job by ID
export function getCronJobById(id: string): CronJobConfig | undefined {
  return CRON_JOBS.find(job => job.id === id)
}
