import { pool } from '@/lib/db';

export interface MatchingCriteria {
  property_types: string[];
  property_sub_categories: string[];
  strategies: string[];
  countries: string[];
  states: string[];
  cities: string[];
  loan_types: string[];
  capital_positions: string[];
  min_deal_size: number | null;
  max_deal_size: number | null;
}

export interface ArticleMatch {
  article_id: number;
  headline: string;
  summary: string;
  publication_date: string;
  publication_name: string;
  article_url: string;
  topic: string;
  sentiment: string;
  is_distressed: boolean;
  market_trend_tags: any;
  llm_tags: any;
  
  // Property data (from joins)
  property_names: any;
  addresses: any;
  property_states: any;
  property_cities: any;
  square_footage: number;
  unit_count: number;
  construction_types: any;
  
  // Transaction data (from joins)
  deal_types: any;
  deal_sizes: any;
  cap_rates: any;
  price_per_sf: number;
  loan_types: any;
  equity_types: any;
  financing_types: any;
  
  // Market data (from joins)
  market_cities: any;
  market_states: any;
  vacancy_rate: number;
  rental_rate: number;
  cap_rate_avg: number;
  transaction_volume: number;
  
  // Entity data (from joins)
  entity_names: any;
  entity_roles: any;
  
  // Scoring data
  score: number;
  reasons: string[];
  breakdown: any[];
  scoring_method: string;
  max_possible_score: number;
  actual_score: number;
  deal_size_rank: boolean;
  
  // Hierarchical search fields
  isPremium?: boolean;
  searchTier?: 1 | 2 | 3 | 4;
}

export interface CriteriaSource {
  source: 'contact' | 'company' | 'location';
  description: string;
  criteria: MatchingCriteria;
  hasInvestmentCriteria: boolean;
}

/**
 * Parse deal size string into numeric value in dollars
 * Handles various formats like "5.2M", "6.6 million", "$10B", etc.
 * Excludes non-monetary units like square feet, acres, etc.
 */
function parseDealSize(dealSizeStr: string): number | null {
  if (!dealSizeStr || typeof dealSizeStr !== 'string') return null;
  
  const cleanStr = dealSizeStr.toLowerCase().trim();
  
  // Skip non-monetary units that shouldn't be treated as deal sizes
  if (cleanStr.includes('square feet') || 
      cleanStr.includes('sq ft') || 
      cleanStr.includes('sqft') ||
      cleanStr.includes('acres') ||
      cleanStr.includes('hectares') ||
      cleanStr.includes('units') ||
      cleanStr.includes('beds') ||
      cleanStr.includes('bedrooms') ||
      cleanStr.includes('bathrooms') ||
      cleanStr.includes('rooms')) {
    return null;
  }
  
  // Extract numeric part
  const numericMatch = cleanStr.match(/[\d.,]+/);
  if (!numericMatch) return null;
  
  const numericValue = parseFloat(numericMatch[0].replace(/,/g, ''));
  if (isNaN(numericValue)) return null;
  
  // Check for unit multipliers
  if (cleanStr.includes('billion') || cleanStr.includes('b')) {
    return numericValue * 1000000000;
  } else if (cleanStr.includes('million') || cleanStr.includes('m')) {
    return numericValue * 1000000;
  } else if (cleanStr.includes('thousand') || cleanStr.includes('k')) {
    return numericValue * 1000;
  } else {
    // If no unit, assume it's already in dollars
    return numericValue;
  }
}

/**
 * Check if deal size matches criteria
 */
function isDealSizeMatch(dealSizeStr: string, minDealSize: number | null, maxDealSize: number | null): boolean {
  const dealSizeNum = parseDealSize(dealSizeStr);
  if (!dealSizeNum) return false;
  
  if (minDealSize && dealSizeNum < minDealSize) return false;
  if (maxDealSize && dealSizeNum > maxDealSize) return false;
  
  return true;
}

/**
 * Check if address matches criteria
 */
function isAddressMatch(address: string, states: string[], cities: string[]): boolean {
  if (!address) return false;
  
  const addressLower = address.toLowerCase();
  
  // Check if any state matches
  if (states.length > 0) {
    const stateMatch = states.some(state => 
      addressLower.includes(state.toLowerCase())
    );
    if (stateMatch) return true;
  }
  
  // Check if any city matches
  if (cities.length > 0) {
    const cityMatch = cities.some(city => 
      addressLower.includes(city.toLowerCase())
    );
    if (cityMatch) return true;
  }
  
  return false;
}

/**
 * Check property type match - exact matching only
 */
function isPropertyTypeMatch(propertyName: string, propertyTypes: string[]): boolean {
  if (!propertyName || propertyTypes.length === 0) return false;
  
  const propertyNameLower = propertyName.toLowerCase();
  
  return propertyTypes.some(type => {
    const typeLower = type.toLowerCase();
    // Exact matching only - no partial matching
    return propertyNameLower === typeLower;
  });
}

/**
 * Check company name match in entities
 */
function isCompanyNameMatch(entityNames: any[], companyName: string): { matched: boolean, matchedNames: string[] } {
  if (!companyName || !entityNames || entityNames.length === 0) {
    return { matched: false, matchedNames: [] };
  }
  
  const companyNameLower = companyName.toLowerCase();
  const matchedNames: string[] = [];
  
  for (const entityName of entityNames) {
    if (entityName && typeof entityName === 'string') {
      const entityNameLower = entityName.toLowerCase();
      // Check for exact match or partial match
      if (entityNameLower.includes(companyNameLower) || companyNameLower.includes(entityNameLower)) {
        matchedNames.push(entityName);
      }
    }
  }
  
  return { matched: matchedNames.length > 0, matchedNames };
}

/**
 * Simplified direct matching function without complex scoring
 */
export async function findArticleMatches(
  criteria: MatchingCriteria, 
  hasInvestmentCriteria: boolean,
  isLocationFallback: boolean = false,
  companyName?: string
): Promise<ArticleMatch[]> {

  const whereConditions: string[] = [];
  const queryParams: any[] = [];
  let paramCount = 0;

  // Primary focus: Property Type Matching (only if we have investment criteria)
  if (hasInvestmentCriteria && criteria.property_types.length > 0) {
    whereConditions.push(`
      EXISTS (
        SELECT 1 FROM article_properties ap 
        WHERE ap.article_id = a.article_id 
        AND ap.property_name IS NOT NULL
      )
    `);
    // We'll filter by property type in post-processing since property_name vs property_types are different concepts
  }

  // Primary focus: Deal Size Matching (only if we have investment criteria)
  if (hasInvestmentCriteria && (criteria.min_deal_size || criteria.max_deal_size)) {
    whereConditions.push(`
      EXISTS (
        SELECT 1 FROM article_transactions at 
        WHERE at.article_id = a.article_id 
        AND at.deal_size IS NOT NULL
        AND at.deal_size != ''
      )
    `);
    // We'll filter by deal size in post-processing since the formats vary
  }

  // Primary focus: Deal Type (Strategies) Matching 
  if (hasInvestmentCriteria && criteria.strategies.length > 0) {
    whereConditions.push(`
      EXISTS (
        SELECT 1 FROM article_transactions at 
        WHERE at.article_id = a.article_id 
        AND at.deal_type IS NOT NULL
      )
    `);
    // We'll filter by deal type in post-processing for better matching
  }

  // Location matching (states/cities - for address focus)
  if (criteria.states.length > 0) {
    paramCount++;
    whereConditions.push(`
      (
        EXISTS (
          SELECT 1 FROM article_properties ap 
          WHERE ap.article_id = a.article_id 
          AND ap.state = ANY($${paramCount}::text[])
        ) OR
        EXISTS (
          SELECT 1 FROM article_market_metrics amm 
          WHERE amm.article_id = a.article_id 
          AND amm.market_state = ANY($${paramCount}::text[])
        )
      )
    `);
    queryParams.push(criteria.states);
  }

  if (criteria.cities.length > 0) {
    paramCount++;
    whereConditions.push(`
      (
        EXISTS (
          SELECT 1 FROM article_properties ap 
          WHERE ap.article_id = a.article_id 
          AND ap.city = ANY($${paramCount}::text[])
        ) OR
        EXISTS (
          SELECT 1 FROM article_market_metrics amm 
          WHERE amm.article_id = a.article_id 
          AND amm.market_city = ANY($${paramCount}::text[])
        )
      )
    `);
    queryParams.push(criteria.cities);
  }

  // If no conditions, return empty results
  if (whereConditions.length === 0) {
    return [];
  }

  // Query articles with all relevant data
  const articlesQuery = `
    SELECT DISTINCT
      a.article_id,
      a.headline,
      a.summary,
      a.publication_date,
      a.publication_name,
      a.article_url,
      a.topic,
      a.sentiment,
      a.is_distressed,
      a.market_trend_tags,
      a.llm_tags,
      
      -- Property data
      CASE WHEN COUNT(ap.article_property_id) > 0 THEN
        jsonb_agg(DISTINCT jsonb_build_object(
          'property_name', ap.property_name,
          'address', ap.address,
          'city', ap.city,
          'state', ap.state,
          'square_footage', ap.square_footage,
          'unit_count', ap.unit_count
        )) FILTER (WHERE ap.article_property_id IS NOT NULL)
      ELSE NULL END as properties,
      
      -- Transaction data  
      CASE WHEN COUNT(at.article_transaction_id) > 0 THEN
        jsonb_agg(DISTINCT jsonb_build_object(
          'deal_type', at.deal_type,
          'deal_size', at.deal_size,
          'loan_type', at.loan_type,
          'cap_rate', at.cap_rate,
          'price_per_sf', at.price_per_sf
        )) FILTER (WHERE at.article_transaction_id IS NOT NULL)
      ELSE NULL END as transactions,
      
      -- Entity data
      CASE WHEN COUNT(ae.article_entity_id) > 0 THEN
        jsonb_agg(DISTINCT jsonb_build_object(
          'entity_name', ae.entity_name,
          'entity_role', ae.entity_role
        )) FILTER (WHERE ae.article_entity_id IS NOT NULL)
      ELSE NULL END as entities,
      
      -- Market data
      CASE WHEN COUNT(amm.article_id) > 0 THEN
        jsonb_agg(DISTINCT jsonb_build_object(
          'market_city', amm.market_city,
          'market_state', amm.market_state,
          'vacancy_rate', amm.vacancy_rate,
          'rental_rate', amm.rental_rate
        )) FILTER (WHERE amm.article_id IS NOT NULL)
      ELSE NULL END as market_data
      
    FROM article a
    LEFT JOIN article_properties ap ON a.article_id = ap.article_id
    LEFT JOIN article_transactions at ON a.article_id = at.article_id
    LEFT JOIN article_market_metrics amm ON a.article_id = amm.article_id
    LEFT JOIN articles_entities ae ON a.article_id = ae.article_id
    WHERE ${whereConditions.length > 0 ? '(' + whereConditions.join(' OR ') + ')' : '1=1'}
    AND a.extraction_status = 'completed'
    AND a.is_relevant = true
    AND a.publication_date >= (CURRENT_DATE - INTERVAL '90 days')::text
    -- Only include articles with valid transaction deal sizes (exclude square footage, units, etc.)
    AND EXISTS (
      SELECT 1 FROM article_transactions at2 
      WHERE at2.article_id = a.article_id 
      AND at2.deal_size IS NOT NULL 
      AND at2.deal_size != ''
      AND at2.deal_size !~ 'square feet|sq ft|sqft|acres|hectares|units|beds|bedrooms|bathrooms|rooms'
      AND at2.deal_size ~ 'M|B|million|billion|thousand|\\$|\\d+[kmb]'
    )
    GROUP BY a.article_id, a.headline, a.summary, a.publication_date, a.publication_name, a.article_url, 
             a.topic, a.sentiment, a.is_distressed, a.market_trend_tags, a.llm_tags
    ORDER BY a.publication_date DESC
    LIMIT 1000
  `;



  const articleResult = await pool.query(articlesQuery, queryParams);
  
  // Direct matching with clear reasons
  const matches: ArticleMatch[] = [];
  
  for (const articleItem of articleResult.rows) {
    const matchResult = evaluateDirectMatch(articleItem, criteria, hasInvestmentCriteria, isLocationFallback);
    
    // Skip articles with score 0 - they don't provide meaningful matches
    if (matchResult.isMatch && matchResult.score > 0) {
      matches.push({
        ...articleItem,
        
        // Legacy fields for compatibility (converted from new structure)
        property_names: articleItem.properties ? articleItem.properties.map((p: any) => p.property_name).filter(Boolean) : null,
        addresses: articleItem.properties ? articleItem.properties.map((p: any) => p.address).filter(Boolean) : null,
        property_states: articleItem.properties ? articleItem.properties.map((p: any) => p.state).filter(Boolean) : null,
        property_cities: articleItem.properties ? articleItem.properties.map((p: any) => p.city).filter(Boolean) : null,
        square_footage: articleItem.properties ? Math.max(...articleItem.properties.map((p: any) => p.square_footage || 0)) : 0,
        unit_count: articleItem.properties ? Math.max(...articleItem.properties.map((p: any) => p.unit_count || 0)) : 0,
        construction_types: null,
        
        deal_types: articleItem.transactions ? articleItem.transactions.map((t: any) => t.deal_type).filter(Boolean) : null,
        deal_sizes: articleItem.transactions ? articleItem.transactions.map((t: any) => t.deal_size).filter(Boolean) : null,
        cap_rates: articleItem.transactions ? articleItem.transactions.map((t: any) => t.cap_rate).filter(Boolean) : null,
        price_per_sf: articleItem.transactions ? Math.max(...articleItem.transactions.map((t: any) => t.price_per_sf || 0)) : 0,
        loan_types: articleItem.transactions ? articleItem.transactions.map((t: any) => t.loan_type).filter(Boolean) : null,
        equity_types: null,
        financing_types: null,
        
        market_cities: articleItem.market_data ? articleItem.market_data.map((m: any) => m.market_city).filter(Boolean) : null,
        market_states: articleItem.market_data ? articleItem.market_data.map((m: any) => m.market_state).filter(Boolean) : null,
        vacancy_rate: articleItem.market_data ? Math.max(...articleItem.market_data.map((m: any) => m.vacancy_rate || 0)) : 0,
        rental_rate: articleItem.market_data ? Math.max(...articleItem.market_data.map((m: any) => m.rental_rate || 0)) : 0,
        cap_rate_avg: 0,
        transaction_volume: 0,
        
        entity_names: articleItem.entities ? articleItem.entities.map((e: any) => e.entity_name).filter(Boolean) : null,
        entity_roles: articleItem.entities ? articleItem.entities.map((e: any) => e.entity_role).filter(Boolean) : null,
        
        score: matchResult.score,
        reasons: matchResult.reasons,
        breakdown: matchResult.breakdown,
        scoring_method: 'direct_match',
        max_possible_score: 100,
        actual_score: matchResult.score,
        deal_size_rank: true
      });
    }
  }

  // Sort by tier-based quality system
  return sortArticlesByTierAndQuality(matches);
}


/**
 * Create SQL condition for deal size range filtering
 */
function createDealSizeRangeCondition(criteria: MatchingCriteria, paramOffset: number): { condition: string, params: number[] } {
  const params: number[] = [];
  let condition = '';
  
  if (criteria.min_deal_size || criteria.max_deal_size) {
    // For PostgreSQL, we need to create a custom function or use a complex query
    // For now, we'll rely on post-processing filtering since deal sizes are stored as strings
    condition = `
      AND at.deal_size IS NOT NULL AND at.deal_size != ''
      AND at.deal_size !~ 'square feet|sq ft|sqft|acres|hectares|units|beds|bedrooms|bathrooms|rooms'
      AND at.deal_size ~ 'M|B|million|billion|thousand|\\$|\\d+[kmb]'
    `;
  }
  
  return { condition, params };
}

/**
 * Create SQL condition for location filtering with proper logic
 * Fixed to prevent broad country matches from overriding specific state/city requirements
 * Checks both article_properties AND article_market_metrics tables
 */
function createLocationCondition(criteria: MatchingCriteria, paramOffset: number): { condition: string, params: any[] } {
  const params: any[] = [];
  let condition = '';
  
  if (criteria.states.length > 0 || criteria.cities.length > 0 || criteria.countries.length > 0) {
    condition = ` AND (`;
    
    // Build conditions for both properties and market metrics tables
    const locationConditionSets: string[] = [];
    
    // Properties table conditions
    const propertyConditions: string[] = [];
    if (criteria.states.length > 0) {
      propertyConditions.push(`ap3.state = ANY($${paramOffset + params.length + 1}::text[])`);
      params.push(criteria.states);
    }
    if (criteria.cities.length > 0) {
      propertyConditions.push(`ap3.city = ANY($${paramOffset + params.length + 1}::text[])`);
      params.push(criteria.cities);
    }
    
    // Only add country filter if no specific states/cities are specified
    // This prevents the "United States" filter from overriding specific location requirements
    if (criteria.countries.length > 0 && criteria.states.length === 0 && criteria.cities.length === 0) {
      // Create flexible country matching: "United States" → "USA", "United States", "U"
      const flexibleCountries: string[] = [];
      for (const country of criteria.countries) {
        flexibleCountries.push(country);
        if (country.toLowerCase() === 'united states') {
          flexibleCountries.push('USA', 'U');
        }
      }
      propertyConditions.push(`ap3.country = ANY($${paramOffset + params.length + 1}::text[])`);
      params.push(flexibleCountries);
    }
    
    if (propertyConditions.length > 0) {
      locationConditionSets.push(`EXISTS (SELECT 1 FROM article_properties ap3 WHERE ap3.article_id = a.article_id AND (${propertyConditions.join(' OR ')}))`);
    }
    
    // Market metrics table conditions
    const marketConditions: string[] = [];
    if (criteria.states.length > 0) {
      // Add separate parameter for market metrics (simpler and more reliable)
      marketConditions.push(`amm3.market_state = ANY($${paramOffset + params.length + 1}::text[])`);
      params.push(criteria.states);
    }
    if (criteria.cities.length > 0) {
      // Add separate parameter for market metrics
      marketConditions.push(`amm3.market_city = ANY($${paramOffset + params.length + 1}::text[])`);
      params.push(criteria.cities);
    }
    
    if (marketConditions.length > 0) {
      locationConditionSets.push(`EXISTS (SELECT 1 FROM article_market_metrics amm3 WHERE amm3.article_id = a.article_id AND (${marketConditions.join(' OR ')}))`);
    }
    
    // Join all location condition sets with OR - an article matches if it matches in ANY of the location sources
    condition += locationConditionSets.join(' OR ');
    condition += `)`;
  }
  
  return { condition, params };
}

/**
 * 5-Tier hierarchical article matching system
 * Returns 10 unique articles from each tier based on criteria completeness
 * 
 * Tier 1: Capital Position + Property Type + Deal Size + Location (Premium)
 * Tier 2: Capital Position + Property Type + Location
 * Tier 3: Capital Position + Deal Size + Location  
 * Tier 4: Property Type + Deal Size + Location
 * Tier 5: Location Only (Fallback)
 */
export async function findArticleMatchesWithHierarchicalTiers(
  criteria: MatchingCriteria, 
  hasInvestmentCriteria: boolean,
  isLocationFallback: boolean = false,
  companyName?: string
): Promise<ArticleMatch[]> {
  const allMatches: ArticleMatch[] = [];
  const usedArticleIds = new Set<number>();

  // Helper function to execute tier query and get full article data
  async function executeTierQuery(
    tierNum: 1 | 2 | 3 | 4 | 5,
    conditions: string,
    params: any[],
    tierDescription: string,
    isPremium: boolean = false,
    requiresDealSizeFilter: boolean = false
  ): Promise<ArticleMatch[]> {
    // Get article IDs first
    const idsQuery = `
      SELECT DISTINCT a.article_id, a.publication_date
      FROM article a
      JOIN article_transactions at ON a.article_id = at.article_id
      LEFT JOIN article_properties ap ON a.article_id = ap.article_id
      WHERE a.extraction_status = 'completed'
        AND a.is_relevant = true
        AND a.publication_date >= (CURRENT_DATE - INTERVAL '90 days')::text
        ${usedArticleIds.size > 0 ? `AND a.article_id NOT IN (${Array.from(usedArticleIds).join(',')})` : ''}
        AND (${conditions})
      ORDER BY a.publication_date DESC
      LIMIT 50
    `;

    const idsResult = await pool.query(idsQuery, params);
    const articleIds = idsResult.rows.map((r: any) => r.article_id);

    if (articleIds.length === 0) return [];

    // Add to used IDs to ensure unique articles across tiers
    articleIds.forEach((id: number) => usedArticleIds.add(id));

    // Get full article data for these IDs
  const articlesQuery = `
    SELECT DISTINCT
        a.article_id, a.headline, a.summary, a.publication_date, a.publication_name,
        a.article_url, a.topic, a.sentiment, a.is_distressed, a.market_trend_tags, a.llm_tags,
      
      -- Property data
      CASE WHEN COUNT(ap.article_property_id) > 0 THEN
        jsonb_agg(DISTINCT jsonb_build_object(
          'property_name', ap.property_name,
          'address', ap.address,
          'city', ap.city,
          'state', ap.state,
          'square_footage', ap.square_footage,
          'unit_count', ap.unit_count
        )) FILTER (WHERE ap.article_property_id IS NOT NULL)
      ELSE NULL END as properties,
      
        -- Transaction data
      CASE WHEN COUNT(at.article_transaction_id) > 0 THEN
        jsonb_agg(DISTINCT jsonb_build_object(
          'deal_type', at.deal_type,
          'deal_size', at.deal_size,
          'loan_type', at.loan_type,
          'cap_rate', at.cap_rate,
          'price_per_sf', at.price_per_sf,
          'capital_position', at.capital_position,
          'property_type', at.property_types
        )) FILTER (WHERE at.article_transaction_id IS NOT NULL)
      ELSE NULL END as transactions,
      
      -- Entity data
      CASE WHEN COUNT(ae.article_entity_id) > 0 THEN
        jsonb_agg(DISTINCT jsonb_build_object(
          'entity_name', ae.entity_name,
          'entity_role', ae.entity_role
        )) FILTER (WHERE ae.article_entity_id IS NOT NULL)
      ELSE NULL END as entities,
      
      -- Market data
      CASE WHEN COUNT(amm.article_id) > 0 THEN
        jsonb_agg(DISTINCT jsonb_build_object(
          'market_city', amm.market_city,
          'market_state', amm.market_state,
          'vacancy_rate', amm.vacancy_rate,
          'rental_rate', amm.rental_rate
        )) FILTER (WHERE amm.article_id IS NOT NULL)
      ELSE NULL END as market_data
      
    FROM article a
    LEFT JOIN article_properties ap ON a.article_id = ap.article_id
    LEFT JOIN article_transactions at ON a.article_id = at.article_id
    LEFT JOIN article_market_metrics amm ON a.article_id = amm.article_id
    LEFT JOIN articles_entities ae ON a.article_id = ae.article_id
      WHERE a.article_id = ANY($1::int[])
      GROUP BY a.article_id, a.headline, a.summary, a.publication_date, a.publication_name,
               a.article_url, a.topic, a.sentiment, a.is_distressed, a.market_trend_tags, a.llm_tags
    ORDER BY a.publication_date DESC
  `;

    const articlesResult = await pool.query(articlesQuery, [articleIds]);
    const tierMatches: ArticleMatch[] = [];
  
    for (const articleItem of articlesResult.rows) {
    const matchResult = evaluateDirectMatchWithCapitalPosition(articleItem, criteria, hasInvestmentCriteria, isLocationFallback);
    
      // Skip articles with score 0 - they don't provide meaningful matches
      if (matchResult.score === 0) {
        continue;
      }
    
      const articleMatch = {
        ...articleItem,
        // Legacy fields for compatibility
        property_names: articleItem.properties ? articleItem.properties.map((p: any) => p.property_name).filter(Boolean) : null,
        addresses: articleItem.properties ? articleItem.properties.map((p: any) => p.address).filter(Boolean) : null,
        property_states: articleItem.properties ? articleItem.properties.map((p: any) => p.state).filter(Boolean) : null,
        property_cities: articleItem.properties ? articleItem.properties.map((p: any) => p.city).filter(Boolean) : null,
        square_footage: articleItem.properties ? Math.max(...articleItem.properties.map((p: any) => p.square_footage || 0)) : 0,
        unit_count: articleItem.properties ? Math.max(...articleItem.properties.map((p: any) => p.unit_count || 0)) : 0,
        construction_types: null,
        
        deal_types: articleItem.transactions ? articleItem.transactions.map((t: any) => t.deal_type).filter(Boolean) : null,
        deal_sizes: articleItem.transactions ? articleItem.transactions.map((t: any) => t.deal_size).filter(Boolean) : null,
        cap_rates: articleItem.transactions ? articleItem.transactions.map((t: any) => t.cap_rate).filter(Boolean) : null,
        price_per_sf: articleItem.transactions ? Math.max(...articleItem.transactions.map((t: any) => t.price_per_sf || 0)) : 0,
        loan_types: articleItem.transactions ? articleItem.transactions.map((t: any) => t.loan_type).filter(Boolean) : null,
        equity_types: null,
        financing_types: null,
        
        market_cities: articleItem.market_data ? articleItem.market_data.map((m: any) => m.market_city).filter(Boolean) : null,
        market_states: articleItem.market_data ? articleItem.market_data.map((m: any) => m.market_state).filter(Boolean) : null,
        vacancy_rate: articleItem.market_data ? Math.max(...articleItem.market_data.map((m: any) => m.vacancy_rate || 0)) : 0,
        rental_rate: articleItem.market_data ? Math.max(...articleItem.market_data.map((m: any) => m.rental_rate || 0)) : 0,
        cap_rate_avg: 0,
        transaction_volume: 0,
        
        entity_names: articleItem.entities ? articleItem.entities.map((e: any) => e.entity_name).filter(Boolean) : null,
        entity_roles: articleItem.entities ? articleItem.entities.map((e: any) => e.entity_role).filter(Boolean) : null,
        
        score: matchResult.score,
        reasons: matchResult.reasons,
        breakdown: matchResult.breakdown,
        scoring_method: `tier_${tierNum}_hierarchical`,
        max_possible_score: 100,
        actual_score: matchResult.score,
        deal_size_rank: true,
        isPremium: isPremium,
        searchTier: tierNum
      };
      
      // Apply deal size filtering if required
      if (requiresDealSizeFilter && (criteria.min_deal_size || criteria.max_deal_size)) {
        const dealSizes = articleItem.transactions ? 
          articleItem.transactions.map((t: any) => t.deal_size).filter(Boolean) : [];
        
        let hasValidDealSize = false;
        for (const dealSizeStr of dealSizes) {
          const dealSizeNum = parseDealSize(dealSizeStr);
          if (dealSizeNum) {
            const withinMin = !criteria.min_deal_size || dealSizeNum >= criteria.min_deal_size;
            const withinMax = !criteria.max_deal_size || dealSizeNum <= criteria.max_deal_size;
            if (withinMin && withinMax) {
              hasValidDealSize = true;
              break;
            }
          }
        }
        
        if (!hasValidDealSize) {
          continue; // Skip this article if no deal sizes are within range
        }
      }

      tierMatches.push(articleMatch);
    }

    // Return maximum 10 articles per tier
    return tierMatches.slice(0, 10);
  }

  // TIER 1: Capital Position + Property Type + Deal Size + Location (Premium)
  if (hasInvestmentCriteria && criteria.capital_positions.length > 0 && criteria.property_types.length > 0 && (criteria.min_deal_size || criteria.max_deal_size) && (criteria.states.length > 0 || criteria.cities.length > 0 || criteria.countries.length > 0)) {
    // Create flexible matching arrays for capital positions only - property types use exact matching
    const flexibleCapitalPositions: string[] = [];
    for (const cp of criteria.capital_positions) {
      flexibleCapitalPositions.push(cp);
      if (cp.toLowerCase().includes('debt')) {
        flexibleCapitalPositions.push('Senior Debt', 'Junior Debt', 'Mezzanine');
      }
    }
    // Use exact property types only - no flexible mapping
    const exactPropertyTypes = criteria.property_types;
    
    // FIXED: Separate EXISTS conditions instead of requiring all in same transaction
    let conditions = `
      EXISTS (
        SELECT 1 FROM article_transactions at1 
        WHERE at1.article_id = a.article_id 
        AND at1.capital_position = ANY($1::text[])
      )
      AND EXISTS (
        SELECT 1 FROM article_transactions at2 
        WHERE at2.article_id = a.article_id 
        AND at2.property_types ?| $2::text[]
      )
      AND EXISTS (
        SELECT 1 FROM article_transactions at3 
        WHERE at3.article_id = a.article_id 
        AND at3.deal_size IS NOT NULL AND at3.deal_size != ''
        AND at3.deal_size !~ 'square feet|sq ft|sqft|acres|hectares|units|beds|bedrooms|bathrooms|rooms'
        AND at3.deal_size ~ 'M|B|million|billion|thousand|\\$|\\d+[kmb]'
      )
    `;
    
    const params = [flexibleCapitalPositions, exactPropertyTypes];
    
    // Use the new location filtering function
    const locationFilter = createLocationCondition(criteria, params.length);
    if (locationFilter.condition) {
      conditions += locationFilter.condition;
      params.push(...locationFilter.params);
    }

    const tier1Matches = await executeTierQuery(1, conditions, params, 'Capital Position + Property Type + Deal Size + Location', true, true);
    allMatches.push(...tier1Matches);
  }

  // TIER 2: Capital Position + Property Type + Location
  if (hasInvestmentCriteria && criteria.capital_positions.length > 0 && criteria.property_types.length > 0 && (criteria.states.length > 0 || criteria.cities.length > 0 || criteria.countries.length > 0)) {
    // Create flexible matching arrays for capital positions only - property types use exact matching
    const flexibleCapitalPositions: string[] = [];
    for (const cp of criteria.capital_positions) {
      flexibleCapitalPositions.push(cp);
      if (cp.toLowerCase().includes('debt')) {
        flexibleCapitalPositions.push('Senior Debt', 'Junior Debt', 'Mezzanine');
      }
    }
    // Use exact property types only - no flexible mapping
    const exactPropertyTypes = criteria.property_types;
    
    // FIXED: Separate EXISTS conditions instead of requiring all in same transaction
    let conditions = `
      EXISTS (
        SELECT 1 FROM article_transactions at1 
        WHERE at1.article_id = a.article_id 
        AND at1.capital_position = ANY($1::text[])
      )
      AND EXISTS (
        SELECT 1 FROM article_transactions at2 
        WHERE at2.article_id = a.article_id 
        AND at2.property_types ?| $2::text[]
      )
    `;
    
    const params = [flexibleCapitalPositions, exactPropertyTypes];
    
    // Use the new location filtering function
    const locationFilter = createLocationCondition(criteria, params.length);
    if (locationFilter.condition) {
      conditions += locationFilter.condition;
      params.push(...locationFilter.params);
    }

    const tier2Matches = await executeTierQuery(2, conditions, params, 'Capital Position + Property Type + Location', false);
    allMatches.push(...tier2Matches);
  }

  // TIER 3: Property Type + Deal Size + Location
  if (hasInvestmentCriteria && criteria.property_types.length > 0 && (criteria.min_deal_size || criteria.max_deal_size) && (criteria.states.length > 0 || criteria.cities.length > 0 || criteria.countries.length > 0)) {
    // Use exact property types only - no flexible mapping
    const exactPropertyTypes = criteria.property_types;
    
    // FIXED: Separate EXISTS conditions for consistency
    let conditions = `
      EXISTS (
        SELECT 1 FROM article_transactions at1 
        WHERE at1.article_id = a.article_id 
        AND at1.property_types ?| $1::text[]
      )
      AND EXISTS (
        SELECT 1 FROM article_transactions at2 
        WHERE at2.article_id = a.article_id 
        AND at2.deal_size IS NOT NULL AND at2.deal_size != ''
        AND at2.deal_size !~ 'square feet|sq ft|sqft|acres|hectares|units|beds|bedrooms|bathrooms|rooms'
        AND at2.deal_size ~ 'M|B|million|billion|thousand|\\$|\\d+[kmb]'
      )
    `;
    
    const params = [exactPropertyTypes];
    
    // Use the new location filtering function
    const locationFilter = createLocationCondition(criteria, params.length);
    if (locationFilter.condition) {
      conditions += locationFilter.condition;
      params.push(...locationFilter.params);
    }

    const tier3Matches = await executeTierQuery(3, conditions, params, 'Property Type + Deal Size + Location', false, true);
    allMatches.push(...tier3Matches);
  }

  // TIER 4: Property Type + Location
  if (hasInvestmentCriteria && criteria.property_types.length > 0 && (criteria.states.length > 0 || criteria.cities.length > 0 || criteria.countries.length > 0)) {
    // Use exact property types only - no flexible mapping
    const exactPropertyTypes = criteria.property_types;
    
    // FIXED: Use EXISTS condition for consistency
    let conditions = `
      EXISTS (
        SELECT 1 FROM article_transactions at1 
        WHERE at1.article_id = a.article_id 
        AND at1.property_types ?| $1::text[]
      )
    `;
    
    const params = [exactPropertyTypes];
    
    // Use the new location filtering function
    const locationFilter = createLocationCondition(criteria, params.length);
    if (locationFilter.condition) {
      conditions += locationFilter.condition;
      params.push(...locationFilter.params);
    }

    const tier4Matches = await executeTierQuery(4, conditions, params, 'Property Type + Location', false);
    allMatches.push(...tier4Matches);
  }

  // TIER 5: Location Only
  if (criteria.states.length > 0 || criteria.cities.length > 0 || criteria.countries.length > 0) {
    let conditions = `EXISTS (SELECT 1 FROM article_properties ap3 WHERE ap3.article_id = a.article_id`;
    const params: any[] = [];
    
    // Build location conditions with proper logic
    const locationConditions: string[] = [];
    
    if (criteria.states.length > 0) {
      locationConditions.push(`ap3.state = ANY($${params.length + 1}::text[])`);
      params.push(criteria.states);
    }
    if (criteria.cities.length > 0) {
      locationConditions.push(`ap3.city = ANY($${params.length + 1}::text[])`);
      params.push(criteria.cities);
    }
    
    // Only add country filter if no specific states/cities are specified
    // This prevents the "United States" filter from overriding specific location requirements
    if (criteria.countries.length > 0 && criteria.states.length === 0 && criteria.cities.length === 0) {
      // Create flexible country matching: "United States" → "USA", "United States", "U"
      const flexibleCountries: string[] = [];
      for (const country of criteria.countries) {
        flexibleCountries.push(country);
        if (country.toLowerCase() === 'united states') {
          flexibleCountries.push('USA', 'U');
        }
      }
      locationConditions.push(`ap3.country = ANY($${params.length + 1}::text[])`);
      params.push(flexibleCountries);
    }
    
    // Join location conditions with OR - an article matches if it's in ANY of the specified locations
    conditions += ` AND (${locationConditions.join(' OR ')})`;
    conditions += `)`;

    const tier5Matches = await executeTierQuery(5, conditions, params, 'Location Only', false);
    allMatches.push(...tier5Matches);
  }

  // Sort by tier-based quality system
  return sortArticlesByTierAndQuality(allMatches);
}

/**
 * Enhanced article matching function with capital position filtering
 * Supports hierarchical search with capital position matching
 * 
 * IMPORTANT: For smaller deals ($1M-$50M), this function is more flexible with missing 
 * capital position data due to data completeness issues in the database where smaller 
 * transactions often lack capital structure details.
 */
export async function findArticleMatchesWithCapitalPosition(
  criteria: MatchingCriteria, 
  hasInvestmentCriteria: boolean,
  isLocationFallback: boolean = false,
  companyName?: string
): Promise<ArticleMatch[]> {
  // Delegate to the new hierarchical tier system
  return findArticleMatchesWithHierarchicalTiers(criteria, hasInvestmentCriteria, isLocationFallback, companyName);
}

/**
 * Direct match evaluation - focuses on core criteria without complex scoring
 */
function evaluateDirectMatch(
  articleItem: any,
  criteria: MatchingCriteria,
  hasInvestmentCriteria: boolean,
  isLocationFallback: boolean
): { isMatch: boolean, score: number, reasons: string[], breakdown: any[] } {
  const reasons: string[] = [];
  const breakdown: any[] = [];
  let matchCount = 0;
  let totalCriteria = 0;

  // Property Type Matching
  if (hasInvestmentCriteria && criteria.property_types.length > 0) {
    totalCriteria++;
    if (articleItem.properties && Array.isArray(articleItem.properties)) {
      const propertyMatches: string[] = [];
      for (const property of articleItem.properties) {
        if (property.property_name) {
          for (const criteriaType of criteria.property_types) {
            if (isPropertyTypeMatch(property.property_name, criteria.property_types)) {
              propertyMatches.push(`${property.property_name} (matches ${criteriaType})`);
              break;
            }
          }
        }
      }
      
      if (propertyMatches.length > 0) {
        matchCount++;
        reasons.push(`Property Type: ${propertyMatches.join(', ')}`);
        breakdown.push({
          field: 'property_type',
          matched: true,
          values: propertyMatches,
          criteria: criteria.property_types
        });
      }
    }
  }

  // Deal Size Matching  
  if (hasInvestmentCriteria && (criteria.min_deal_size || criteria.max_deal_size)) {
    totalCriteria++;
    if (articleItem.transactions && Array.isArray(articleItem.transactions)) {
      const sizeMatches: string[] = [];
      for (const transaction of articleItem.transactions) {
        if (transaction.deal_size) {
          const isMatch = isDealSizeMatch(transaction.deal_size, criteria.min_deal_size, criteria.max_deal_size);
          if (isMatch) {
            const parsedSize = parseDealSize(transaction.deal_size);
            sizeMatches.push(`${transaction.deal_size} (${parsedSize ? '$' + (parsedSize/1000000).toFixed(1) + 'M' : 'N/A'})`);
          }
        }
      }
      
      if (sizeMatches.length > 0) {
        matchCount++;
        const rangeText = criteria.min_deal_size && criteria.max_deal_size 
          ? `$${(criteria.min_deal_size/1000000).toFixed(1)}M - $${(criteria.max_deal_size/1000000).toFixed(1)}M`
          : criteria.min_deal_size 
            ? `≥ $${(criteria.min_deal_size/1000000).toFixed(1)}M`
            : criteria.max_deal_size
            ? `≤ $${(criteria.max_deal_size/1000000).toFixed(1)}M`
            : 'Unknown range';
        reasons.push(`Deal Size: ${sizeMatches.join(', ')} (criteria: ${rangeText})`);
        breakdown.push({
          field: 'deal_size',
          matched: true,
          values: sizeMatches,
          criteria: rangeText
        });
      }
    }
  }

  // Address/Location Matching
  const locationCriteria = [...criteria.states, ...criteria.cities];
  if (locationCriteria.length > 0) {
    totalCriteria++;
    const addressMatches: string[] = [];
    
    // Check property addresses
    if (articleItem.properties && Array.isArray(articleItem.properties)) {
      for (const property of articleItem.properties) {
        if (property.address) {
          const isMatch = isAddressMatch(property.address, criteria.states, criteria.cities);
          if (isMatch) {
            addressMatches.push(property.address);
          }
        }
        
        // Also check city/state fields directly
        if (property.city && criteria.cities.some((city: string) => city.toLowerCase().includes(property.city.toLowerCase()))) {
          addressMatches.push(`${property.city}, ${property.state || ''}`);
        }
        if (property.state && criteria.states.some((state: string) => state.toLowerCase().includes(property.state.toLowerCase()))) {
          addressMatches.push(`${property.city || ''}, ${property.state}`);
        }
      }
    }
    
    // Check market data locations
    if (articleItem.market_data && Array.isArray(articleItem.market_data)) {
      for (const market of articleItem.market_data) {
        if (market.market_city && criteria.cities.some((city: string) => city.toLowerCase().includes(market.market_city.toLowerCase()))) {
          addressMatches.push(`Market: ${market.market_city}, ${market.market_state || ''}`);
        }
        if (market.market_state && criteria.states.some((state: string) => state.toLowerCase().includes(market.market_state.toLowerCase()))) {
          addressMatches.push(`Market: ${market.market_city || ''}, ${market.market_state}`);
        }
      }
    }
    
    if (addressMatches.length > 0) {
      matchCount++;
      const uniqueAddresses = Array.from(new Set(addressMatches));
      reasons.push(`Location: ${uniqueAddresses.join(', ')}`);
      breakdown.push({
        field: 'location',
        matched: true,
        values: uniqueAddresses,
        criteria: locationCriteria
      });
    }
  }

  // Strategy/Deal Type Matching
  if (hasInvestmentCriteria && criteria.strategies.length > 0) {
    totalCriteria++;
    if (articleItem.transactions && Array.isArray(articleItem.transactions)) {
      const strategyMatches: string[] = [];
      for (const transaction of articleItem.transactions) {
        if (transaction.deal_type) {
          for (const strategy of criteria.strategies) {
            if (transaction.deal_type.toLowerCase().includes(strategy.toLowerCase()) || 
                strategy.toLowerCase().includes(transaction.deal_type.toLowerCase())) {
              strategyMatches.push(`${transaction.deal_type} (matches ${strategy})`);
              break;
            }
          }
        }
      }
      
      if (strategyMatches.length > 0) {
        matchCount++;
        reasons.push(`Strategy: ${strategyMatches.join(', ')}`);
        breakdown.push({
          field: 'strategy',
          matched: true,
          values: strategyMatches,
          criteria: criteria.strategies
        });
      }
    }
  }

  // Calculate enhanced match score with bonus points
  const isMatch = matchCount > 0;
  
  // Define all possible criteria types for comprehensive scoring
  const allPossibleCriteria = [
    'property_type',
    'deal_size', 
    'strategy',
    'location',
    'capital_position'
  ];
  
  // Base score: percentage of available criteria that matched
  const baseScore = totalCriteria > 0 ? Math.round((matchCount / totalCriteria) * 100) : 0;
  
  // Bonus points for multiple matches within the same field
  let bonusPoints = 0;
  if (breakdown.length > 0) {
    // Bonus for having multiple criteria types matched
    if (breakdown.length > 1) {
      bonusPoints += 15;
    }
    
    // Bonus for comprehensive matching (deal size + property type + location)
    const hasDealSize = breakdown.some(b => b.field === 'deal_size');
    const hasPropertyType = breakdown.some(b => b.field === 'property_type');
    const hasLocation = breakdown.some(b => b.field === 'location');
    const hasStrategy = breakdown.some(b => b.field === 'strategy');
    const hasCapitalPosition = breakdown.some(b => b.field === 'capital_position');
    
    // Comprehensive matching bonuses
    if (hasDealSize && hasPropertyType) bonusPoints += 20;
    if (hasDealSize && hasLocation) bonusPoints += 15;
    if (hasPropertyType && hasLocation) bonusPoints += 15;
    if (hasStrategy && (hasDealSize || hasPropertyType)) bonusPoints += 15;
    if (hasCapitalPosition && hasDealSize) bonusPoints += 10;
    
    // Perfect match bonus (all available criteria matched)
    if (matchCount === totalCriteria && totalCriteria > 1) {
      bonusPoints += 25;
    }
  }
  
  // Cap the final score at 100
  const finalScore = Math.min(100, baseScore + bonusPoints);
  
  return {
    isMatch,
    score: finalScore,
    reasons,
    breakdown
  };
}

/**
 * Enhanced direct match evaluation with capital position and property type filtering from transactions
 */
function evaluateDirectMatchWithCapitalPosition(
  articleItem: any,
  criteria: MatchingCriteria,
  hasInvestmentCriteria: boolean,
  isLocationFallback: boolean
): { isMatch: boolean, score: number, reasons: string[], breakdown: any[] } {
  const reasons: string[] = [];
  const breakdown: any[] = [];
  let matchCount = 0;
  let totalCriteria = 0;

  // Capital Position Matching (PRIORITY: Check from transactions)
  if (criteria.capital_positions.length > 0) {
    totalCriteria++;
    if (articleItem.transactions && Array.isArray(articleItem.transactions)) {
      const capitalMatches: string[] = [];
      let hasSmallDealWithoutCapitalPosition = false;
      
      // Check for direct capital position matches
      for (const transaction of articleItem.transactions) {
        if (transaction.capital_position) {
          for (const criteriaPosition of criteria.capital_positions) {
            if (transaction.capital_position.toLowerCase().includes(criteriaPosition.toLowerCase()) || 
                criteriaPosition.toLowerCase().includes(transaction.capital_position.toLowerCase())) {
              capitalMatches.push(`${transaction.capital_position} (matches ${criteriaPosition})`);
              break;
            }
          }
        } else if (!transaction.capital_position && transaction.deal_size) {
          // Check if this is a small deal where capital position might be missing
          const dealSizeNum = parseDealSize(transaction.deal_size);
          if (dealSizeNum && dealSizeNum >= 1000000 && dealSizeNum <= 50000000) { // $1M-$50M range
            hasSmallDealWithoutCapitalPosition = true;
          }
        }
      }
      
      if (capitalMatches.length > 0) {
        matchCount++;
        // Calculate perfect match score for capital positions
        const exactMatches = capitalMatches.filter(match => 
          criteria.capital_positions.some(pos => 
            match.toLowerCase().includes(pos.toLowerCase()) && 
            pos.toLowerCase().includes(match.split(' (matches ')[0].toLowerCase())
          )
        );
        const capitalScore = exactMatches.length > 0 ? 100 : 90;
        
        reasons.push(`Capital Position: ${capitalMatches.join(', ')}`);
        breakdown.push({
          field: 'capital_position',
          matched: true,
          values: capitalMatches,
          criteria: criteria.capital_positions,
          score: capitalScore,
          match_type: exactMatches.length > 0 ? 'perfect' : 'partial'
        });
      } else if (hasSmallDealWithoutCapitalPosition) {
        // For smaller deals with missing capital position data, give partial credit
        matchCount++;
        const partialScore = 75; // Reduced score due to missing data but still valid match
        
        reasons.push(`Capital Position: Compatible with small deal (data incomplete)`);
        breakdown.push({
          field: 'capital_position',
          matched: true,
          values: ['Small deal - capital position not specified'],
          criteria: criteria.capital_positions,
          score: partialScore,
          match_type: 'data_incomplete_but_compatible'
        });
      }
    }
  }

  // Property Type Matching (Enhanced: Check both properties and transactions)
  if (hasInvestmentCriteria && criteria.property_types.length > 0) {
    totalCriteria++;
    let propertyMatches: string[] = [];
    
    // Check property names from article_properties
    if (articleItem.properties && Array.isArray(articleItem.properties)) {
      for (const property of articleItem.properties) {
        if (property.property_name) {
          for (const criteriaType of criteria.property_types) {
            if (isPropertyTypeMatch(property.property_name, [criteriaType])) {
              propertyMatches.push(`${property.property_name} (matches ${criteriaType})`);
              break;
            }
          }
        }
      }
    }
    
    // Enhanced: Also check property_type from transactions
    if (articleItem.transactions && Array.isArray(articleItem.transactions)) {
      for (const transaction of articleItem.transactions) {
        if (transaction.property_type) {
          // Handle both string and array property_type
          const propertyTypes = Array.isArray(transaction.property_type) 
            ? transaction.property_type 
            : [transaction.property_type];
          
          for (const propertyType of propertyTypes) {
          for (const criteriaType of criteria.property_types) {
              // Exact matching only - no partial matching
              if (propertyType.toLowerCase() === criteriaType.toLowerCase()) {
                propertyMatches.push(`${propertyType} (transaction, matches ${criteriaType})`);
              break;
              }
            }
          }
        }
      }
    }
    
    if (propertyMatches.length > 0) {
      matchCount++;
      // Calculate property type match score
      const exactPropertyMatches = propertyMatches.filter(match => 
        criteria.property_types.some(type => 
          match.toLowerCase().includes(type.toLowerCase()) && 
          type.toLowerCase().includes(match.split(' (')[0].toLowerCase())
        )
      );
      const propertyScore = exactPropertyMatches.length > 0 ? 100 : 95;
      
      reasons.push(`Property Type: ${propertyMatches.join(', ')}`);
      breakdown.push({
        field: 'property_type',
        matched: true,
        values: propertyMatches,
        criteria: criteria.property_types,
        score: propertyScore,
        match_type: exactPropertyMatches.length > 0 ? 'perfect' : 'partial'
      });
    }
  }

  // Deal Size Matching  
  if (hasInvestmentCriteria && (criteria.min_deal_size || criteria.max_deal_size)) {
    totalCriteria++;
    if (articleItem.transactions && Array.isArray(articleItem.transactions)) {
      const sizeMatches: string[] = [];
      for (const transaction of articleItem.transactions) {
        if (transaction.deal_size) {
          const isMatch = isDealSizeMatch(transaction.deal_size, criteria.min_deal_size, criteria.max_deal_size);
          if (isMatch) {
            const parsedSize = parseDealSize(transaction.deal_size);
            sizeMatches.push(`${transaction.deal_size} (${parsedSize ? '$' + (parsedSize/1000000).toFixed(1) + 'M' : 'N/A'})`);
          }
        }
      }
      
      if (sizeMatches.length > 0) {
        matchCount++;
        const rangeText = criteria.min_deal_size && criteria.max_deal_size 
          ? `$${(criteria.min_deal_size/1000000).toFixed(1)}M - $${(criteria.max_deal_size/1000000).toFixed(1)}M`
          : criteria.min_deal_size 
            ? `≥ $${(criteria.min_deal_size/1000000).toFixed(1)}M`
            : criteria.max_deal_size
            ? `≤ $${(criteria.max_deal_size/1000000).toFixed(1)}M`
            : 'Unknown range';
        reasons.push(`Deal Size: ${sizeMatches.join(', ')} (criteria: ${rangeText})`);
        breakdown.push({
          field: 'deal_size',
          matched: true,
          values: sizeMatches,
          criteria: rangeText,
          score: 90
        });
      }
    }
  }

  // Address/Location Matching
  const locationCriteria = [...criteria.states, ...criteria.cities];
  if (locationCriteria.length > 0) {
    totalCriteria++;
    const addressMatches: string[] = [];
    
    // Check property addresses
    if (articleItem.properties && Array.isArray(articleItem.properties)) {
      for (const property of articleItem.properties) {
        if (property.address) {
          const isMatch = isAddressMatch(property.address, criteria.states, criteria.cities);
          if (isMatch) {
            addressMatches.push(property.address);
          }
        }
        
        // Also check city/state fields directly
        if (property.city && criteria.cities.some((city: string) => city.toLowerCase().includes(property.city.toLowerCase()))) {
          addressMatches.push(`${property.city}, ${property.state || ''}`);
        }
        if (property.state && criteria.states.some((state: string) => state.toLowerCase().includes(property.state.toLowerCase()))) {
          addressMatches.push(`${property.city || ''}, ${property.state}`);
        }
      }
    }
    
    // Check market data locations
    if (articleItem.market_data && Array.isArray(articleItem.market_data)) {
      for (const market of articleItem.market_data) {
        if (market.market_city && criteria.cities.some((city: string) => city.toLowerCase().includes(market.market_city.toLowerCase()))) {
          addressMatches.push(`Market: ${market.market_city}, ${market.market_state || ''}`);
        }
        if (market.market_state && criteria.states.some((state: string) => state.toLowerCase().includes(market.market_state.toLowerCase()))) {
          addressMatches.push(`Market: ${market.market_city || ''}, ${market.market_state}`);
        }
      }
    }
    
    if (addressMatches.length > 0) {
      matchCount++;
      const uniqueAddresses = Array.from(new Set(addressMatches));
      reasons.push(`Location: ${uniqueAddresses.join(', ')}`);
      breakdown.push({
        field: 'location',
        matched: true,
        values: uniqueAddresses,
        criteria: locationCriteria,
        score: 80
      });
    }
  }

  // Strategy/Deal Type Matching
  if (hasInvestmentCriteria && criteria.strategies.length > 0) {
    totalCriteria++;
    if (articleItem.transactions && Array.isArray(articleItem.transactions)) {
      const strategyMatches: string[] = [];
      for (const transaction of articleItem.transactions) {
        if (transaction.deal_type) {
          for (const strategy of criteria.strategies) {
            if (transaction.deal_type.toLowerCase().includes(strategy.toLowerCase()) || 
                strategy.toLowerCase().includes(transaction.deal_type.toLowerCase())) {
              strategyMatches.push(`${transaction.deal_type} (matches ${strategy})`);
              break;
            }
          }
        }
      }
      
      if (strategyMatches.length > 0) {
        matchCount++;
        reasons.push(`Strategy: ${strategyMatches.join(', ')}`);
        breakdown.push({
          field: 'strategy',
          matched: true,
          values: strategyMatches,
          criteria: criteria.strategies,
          score: 85
        });
      }
    }
  }

  // Calculate enhanced match score with premium bonuses
  const isMatch = matchCount > 0;
  
  // Weighted scoring system based on field importance and match quality
  let weightedScore = 0;
  let totalWeight = 0;
  
  if (breakdown.length > 0) {
    for (const item of breakdown) {
      let fieldWeight = 1;
      let fieldScore = item.score || 80;
      
      // Assign weights based on field importance for investment matching
      switch (item.field) {
        case 'capital_position':
          fieldWeight = 4; // Highest priority - perfect match gets 100% score
          if (item.match_type === 'perfect') fieldScore = 100;
          break;
        case 'property_type':
          fieldWeight = 3.5; // Very high priority 
          if (item.match_type === 'perfect') fieldScore = 100;
          break;
        case 'deal_size':
          fieldWeight = 3; // High priority
          fieldScore = 90; // Deal size matching is always good when it matches
          break;
        case 'location':
          fieldWeight = 2; // Medium priority
          fieldScore = 80;
          break;
        case 'strategy':
          fieldWeight = 2.5; // Medium-high priority
          fieldScore = 85;
          break;
        default:
          fieldWeight = 1;
          fieldScore = 70;
      }
      
      weightedScore += fieldScore * fieldWeight;
      totalWeight += fieldWeight;
    }
  }
  
  // Calculate base weighted score
  const baseScore = totalWeight > 0 ? Math.round(weightedScore / totalWeight) : 0;
  
  // Enhanced bonus system for comprehensive matching
  let bonusPoints = 0;
  if (breakdown.length > 0) {
    const hasCapitalPosition = breakdown.some(b => b.field === 'capital_position');
    const hasPropertyType = breakdown.some(b => b.field === 'property_type');
    const hasDealSize = breakdown.some(b => b.field === 'deal_size');
    const hasLocation = breakdown.some(b => b.field === 'location');
    const hasStrategy = breakdown.some(b => b.field === 'strategy');
    
    // Perfect match combinations (Tier 1: 100% score)
    if (hasCapitalPosition && hasPropertyType && hasDealSize) {
      bonusPoints = Math.max(bonusPoints, 100 - baseScore); // Ensure 100% score
    }
    // Premium combinations (Tier 2: 95% score)
    else if (hasCapitalPosition && hasDealSize && hasLocation) {
      bonusPoints = Math.max(bonusPoints, 95 - baseScore);
    }
    // Good combinations (Tier 3: 90% score)
    else if (hasPropertyType && hasDealSize && hasLocation) {
      bonusPoints = Math.max(bonusPoints, 90 - baseScore);
    }
    
    // Additional bonuses for comprehensive matching
    if (hasStrategy && (hasCapitalPosition || hasPropertyType)) {
      bonusPoints += Math.min(5, 100 - baseScore - bonusPoints);
    }
    
    // Perfect match bonus (all available criteria matched)
    if (matchCount === totalCriteria && totalCriteria > 2) {
      bonusPoints += Math.min(10, 100 - baseScore - bonusPoints);
    }
  }
  
  // Cap the final score at 100
  const finalScore = Math.min(100, Math.max(baseScore + bonusPoints, baseScore));
  
  return {
    isMatch,
    score: finalScore,
    reasons,
    breakdown
  };
}

/**
 * Sort articles by tier-based quality system
 * Tier 1 (Premium) → Tier 2 → Tier 3 → Tier 4
 * Within each tier: Score (highest first) → Publication date (most recent first)
 */
export function sortArticlesByTierAndQuality(matches: ArticleMatch[]): ArticleMatch[] {
  return matches.sort((a, b) => {
    // Primary sort: by search tier (lower tier number = higher priority/quality)
    const tierA = a.searchTier || 4; // Default to lowest tier if not set
    const tierB = b.searchTier || 4;
    if (tierA !== tierB) {
      return tierA - tierB;
    }
    
    // Secondary sort: by isPremium flag (premium first within same tier)
    if (a.isPremium !== b.isPremium) {
      return a.isPremium ? -1 : 1;
    }
    
    // Tertiary sort: by score within the same tier (descending)
    if (b.score !== a.score) {
      return b.score - a.score;
    }
    
    // Quaternary sort: by publication date (most recent first)
    return new Date(b.publication_date).getTime() - new Date(a.publication_date).getTime();
  });
}

/**
 * Get tier description for debugging and UI display
 */
export function getTierDescription(searchTier: 1 | 2 | 3 | 4 | 5 | undefined, isPremium?: boolean): string {
  if (!searchTier) return 'Tier 5: Location-only Match';
  
  const premiumPrefix = isPremium ? 'Premium - ' : '';
  
  switch (searchTier) {
    case 1:
      return `${premiumPrefix}Tier 1: Capital Position + Property Type + Deal Size + Location`;
    case 2:
      return `${premiumPrefix}Tier 2: Capital Position + Property Type + Location`;
    case 3:
      return `${premiumPrefix}Tier 3: Property Type + Deal Size + Location`;
    case 4:
      return `${premiumPrefix}Tier 4: Property Type + Location`;
    case 5:
      return `${premiumPrefix}Tier 5: Location-only Match`;
    default:
      return 'Unknown Tier';
  }
}

/**
 * Check if criteria has meaningful investment criteria (not just location)
 */
export function hasInvestmentCriteria(criteria: MatchingCriteria): boolean {
  return criteria.property_types.length > 0 ||
         criteria.property_sub_categories.length > 0 ||
         criteria.strategies.length > 0 ||
         criteria.loan_types.length > 0 ||
         criteria.capital_positions.length > 0 ||
         criteria.min_deal_size !== null ||
         criteria.max_deal_size !== null;
}