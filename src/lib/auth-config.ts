import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"
import { pool } from "./db"
import * as bcrypt from "bcryptjs"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { 
          label: "Email", 
          type: "email", 
          placeholder: "Enter your email" 
        },
        password: { 
          label: "Password", 
          type: "password",
          placeholder: "Enter your password" 
        }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required")
        }

        try {
          // Query user from database
          const result = await pool.query(
            'SELECT user_id, username, email, password_hash, role, display_name, avatar_url FROM users WHERE email = $1',
            [credentials.email]
          )

          const user = result.rows[0]

          if (!user) {
            throw new Error("Invalid email or password")
          }

          // Verify password
          const isValidPassword = await bcrypt.compare(credentials.password, user.password_hash)
          
          if (!isValidPassword) {
            throw new Error("Invalid email or password")
          }

          // Log successful login (IP address not available in NextAuth callback)
          await pool.query(
            'INSERT INTO user_edit_logs (user_id, action, details, ip_address) VALUES ($1, $2, $3, $4)',
            [
              user.user_id,
              'login',
              JSON.stringify({ 
                email: user.email,
                timestamp: new Date().toISOString(),
                source: 'nextauth-callback'
              }),
              null // IP not available in NextAuth callback, so use NULL
            ]
          )

          // Return user object that will be stored in JWT
          return {
            id: user.user_id.toString(),
            email: user.email,
            name: user.display_name || user.username,
            image: user.avatar_url,
            role: user.role || 'user',
            username: user.username
          }
        } catch (error) {
          console.error("Authentication error:", error)
          throw new Error("Authentication failed")
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  callbacks: {
    async jwt({ token, user }) {
      // Include role in token when user signs in
      if (user) {
        token.role = user.role
        token.username = user.username
        token.userId = user.id
      }
      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token && session.user) {
        session.user.id = token.userId as string
        session.user.role = token.role as string
        session.user.username = token.username as string
      }
      return session
    }
  },
  events: {
    async signOut({ token }) {
      // Log user logout
      if (token?.userId) {
        try {
          await pool.query(
            'INSERT INTO user_edit_logs (user_id, action, details) VALUES ($1, $2, $3)',
            [
              parseInt(token.userId as string),
              'logout',
              JSON.stringify({ 
                timestamp: new Date().toISOString()
              })
            ]
          )
        } catch (error) {
          console.error("Error logging logout:", error)
        }
      }
    }
  }
}
