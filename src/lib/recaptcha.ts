/**
 * reCAPTCHA verification utility
 * Verifies reCAPTCHA tokens with Google's API
 */

interface RecaptchaResponse {
  success: boolean;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
}

/**
 * Verify a reCAPTCHA token with Google's API
 * @param token The reCAPTCHA token to verify
 * @param secretKey The reCAPTCHA secret key (from environment variables)
 * @returns Promise<boolean> Whether the token is valid
 */
export async function verifyRecaptchaToken(token: string, secretKey?: string): Promise<boolean> {
  if (!token) {
    console.error('reCAPTCHA: No token provided');
    return false;
  }

  const secret = secretKey || process.env.RECAPTCHA_SECRET_KEY;
  if (!secret) {
    console.error('reCAPTCHA: No secret key configured');
    return false;
  }

  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        secret,
        response: token,
      }),
    });

    if (!response.ok) {
      console.error('reCAPTCHA: API request failed', response.status, response.statusText);
      return false;
    }

    const data: RecaptchaResponse = await response.json();
    
    if (!data.success) {
      console.error('reCAPTCHA: Verification failed', data['error-codes']);
      return false;
    }

    console.log('reCAPTCHA: Verification successful');
    return true;
  } catch (error) {
    console.error('reCAPTCHA: Verification error', error);
    return false;
  }
}

/**
 * Get client IP address from request headers
 * @param request The incoming request
 * @returns string | null The client IP address
 */
export function getClientIP(request: Request): string | null {
  // Check various headers for the real IP address
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();
  
  return null;
}
