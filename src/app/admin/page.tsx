import { Metadata } from 'next';
import AdminPanel from '@/components/admin/AdminPanel';
import AdminRoute from '@/components/auth/AdminRoute';
import ImpersonationBanner from '@/components/auth/ImpersonationBanner';

export const metadata: Metadata = {
  title: 'Admin Panel - Anax Dashboard',
  description: 'Administration panel for user management and system configuration',
};

export default function AdminPage() {
  return (
    <AdminRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <ImpersonationBanner />
          <AdminPanel />
        </div>
      </div>
    </AdminRoute>
  );
}
