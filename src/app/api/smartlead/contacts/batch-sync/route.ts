import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// Smartlead API configuration (for reference)
const SMARTLEAD_CAMPAIGN_ID = process.env.SMARTLEAD_CAMPAIGN_ID || '1897921';

// Rate limiting configuration
const MAX_CALLS_PER_MINUTE = 200;
const BOTTLENECK_SIZE = 10; // Number of concurrent operations
const CALLS_PER_SECOND = MAX_CALLS_PER_MINUTE / 60; // ~3.33 calls/second

// Helper function to add delay between requests
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Calculate optimal delay between batches to stay under rate limit
const calculateBatchDelay = (batchSize: number): number => {
  // Time needed for batch in milliseconds to stay under rate limit
  const minDelayMs = (batchSize / CALLS_PER_SECOND) * 1000;
  // Add 10% buffer and minimum 3 second delay
  return Math.max(3000, Math.ceil(minDelayMs * 1.1));
};

// Rate tracking for monitoring
class RateTracker {
  private callTimes: number[] = [];
  
  recordCall() {
    const now = Date.now();
    this.callTimes.push(now);
    // Keep only calls from the last minute
    this.callTimes = this.callTimes.filter(time => now - time < 60000);
  }
  
  getCallsInLastMinute(): number {
    const now = Date.now();
    this.callTimes = this.callTimes.filter(time => now - time < 60000);
    return this.callTimes.length;
  }
  
  getCallRate(): number {
    return this.getCallsInLastMinute();
  }
}

const rateTracker = new RateTracker();

/**
 * POST: Batch process multiple contacts for email generation
 * This endpoint prepares contacts for batch processing (EmailGenerationProcessor handles Smartlead sync)
 * Either provide contactIds directly or a campaignId to find contacts from messages table
 * Note: Actual Smartlead sync is handled by EmailGenerationProcessor automatically
 */
export async function POST(req: NextRequest) {
  // DEPRECATED: This endpoint is deprecated in favor of the SmartleadProcessor
  // Use /api/processing/trigger with stage: 'smartlead_sync' instead
  console.log('[DEPRECATED] Smartlead Batch Sync API is deprecated. Use SmartleadProcessor via /api/processing/trigger instead.');
  
  try {
    const body = await req.json();
    const { contactIds: providedContactIds, campaignId = SMARTLEAD_CAMPAIGN_ID } = body;
    
    if (!campaignId) {
      return NextResponse.json(
        { error: 'Campaign ID is required' },
        { status: 400 }
      );
    }
    
    // Note: API key validation is now handled by individual sync API
    
    // If contactIds are provided directly, query for contacts
    if (providedContactIds && Array.isArray(providedContactIds) && providedContactIds.length > 0) {
      console.log(`Using provided contact IDs: ${providedContactIds.length} contacts`);
      
      // Get contact details
      const { rows: contacts } = await pool.query(`
        SELECT 
          contact_id,
          first_name,
          last_name,
          email,
          email_generated,
          smartlead_lead_id,
          smartlead_status
        FROM contacts
        WHERE contact_id = ANY($1::int[])
        AND email IS NOT NULL
        AND email_generated = true
      `, [providedContactIds]);
      
      if (contacts.length === 0) {
        return NextResponse.json({
          success: false,
          message: 'No eligible contacts found for sync',
          syncedCount: 0,
          totalCount: providedContactIds.length
        });
      }
      
      return await processContacts(contacts, campaignId, null, null);
    } 
    // Otherwise, find messages with the specified campaign ID and their associated contacts
    else {
      console.log(`Finding messages for Smartlead campaign ID: ${campaignId}`);
      
      // Get messages with contact IDs based on user's suggested query
      const { rows: messages } = await pool.query(`
        SELECT m.*, tp.contact_id 
        FROM messages AS m 
        JOIN thread_participants AS tp ON m.thread_id = tp.thread_id 
        WHERE tp.contact_id IS NOT NULL
        AND m.smartlead_campaign_id = $1
      `, [campaignId]);
      
      if (messages.length === 0) {
        return NextResponse.json({
          success: false,
          message: 'No messages found for this campaign',
          syncedCount: 0,
          totalCount: 0
        });
      }
      
      console.log(`Found ${messages.length} messages for campaign ${campaignId}`);
      
      // Process each message and contact pair
      const results: any[] = [];
      
      // Process messages in batches with rate limiting
      const batchSize = BOTTLENECK_SIZE; // Use configured bottleneck size
      const batches: any[][] = [];
      for (let i = 0; i < messages.length; i += batchSize) {
        batches.push(messages.slice(i, i + batchSize));
      }
      
      // Calculate optimal delay to stay under rate limit
      const delayBetweenBatches = calculateBatchDelay(batchSize);
      const estimatedDuration = ((batches.length - 1) * delayBetweenBatches) / 1000 / 60; // minutes
      
      console.log(`Processing ${messages.length} messages in ${batches.length} batches of ${batchSize}`);
      console.log(`Rate limit: ${MAX_CALLS_PER_MINUTE} calls/minute, batch delay: ${delayBetweenBatches}ms`);
      console.log(`Estimated completion time: ${estimatedDuration.toFixed(1)} minutes`);
      
      // Process each batch with a delay between batches
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing batch ${i+1}/${batches.length} (${batch.length} messages)`);
        
        // Add a delay between batches, except for the first one
        if (i > 0) {
          const currentRate = rateTracker.getCallRate();
          console.log(`Waiting ${delayBetweenBatches}ms before next batch... (Current rate: ${currentRate}/${MAX_CALLS_PER_MINUTE} calls/min)`);
          await sleep(delayBetweenBatches);
        }
        
        const batchResults = await Promise.allSettled(
          batch.map(async (message) => {
            try {
              // Get contact details to ensure it's valid for syncing
              const { rows: contacts } = await pool.query(`
                SELECT 
                  c.contact_id,
                  c.first_name,
                  c.last_name,
                  c.email,
                  c.phone_number,
                  c.email_generated,
                  c.smartlead_lead_id,
                  c.smartlead_status,
                  c.linkedin_url,
                  c.contact_city,
                  c.contact_state,
                  c.contact_country,
                  co.company_name,
                  co.website as company_website
                FROM contacts c
                LEFT JOIN companies co ON c.company_id = co.company_id
                WHERE c.contact_id = $1
                AND c.email IS NOT NULL
                AND c.email_generated = true
              `, [message.contact_id]);
              
              if (contacts.length === 0) {
                return {
                  contact_id: message.contact_id,
                  message_id: message.message_id,
                  success: false,
                  error: 'Contact not found or has no valid email'
                };
              }
              
              const contact = contacts[0];
              
              console.log(`Syncing contact ${contact.contact_id} (${contact.email}) with message ${message.message_id} to campaign ${campaignId}`);
              
              // Prepare sync payload for individual sync API
              
              // Note: EmailGenerationProcessor handles Smartlead sync automatically
              // This batch sync is for bulk operations, not individual email generation
              console.log(`Contact ${contact.contact_id} ready for batch processing (email generation handles sync)`);
              
              // Return success without calling sync API
              const leadId = null; // No lead ID since we're not syncing here
              
              return {
                contact_id: contact.contact_id,
                email: contact.email,
                message_id: message.message_id,
                success: true,
                lead_id: leadId,
                campaign_id: campaignId,
                message: 'Ready for batch processing (EmailGenerationProcessor handles sync)'
              };
            } catch (error) {
              console.error(`Error syncing message ${message.message_id}:`, error);
              return {
                contact_id: message.contact_id,
                message_id: message.message_id,
                success: false,
                error: (error as Error).message
              };
            }
          })
        );
        
        results.push(...batchResults);
      }
      
      // Count successes and failures
      const successResults = results.filter(r => 
        r.status === 'fulfilled' && (r.value as any).success
      );
      const successCount = successResults.length;
      const failureCount = results.length - successCount;
      const finalRate = rateTracker.getCallRate();
      
      // Get the lead IDs of successfully synced contacts
      const syncedLeadIds = successResults
        .map(r => r.status === 'fulfilled' ? (r.value as any).lead_id : null)
        .filter(Boolean);
      
      console.log(`Message-based batch processing completed: ${successCount} successful, ${failureCount} failed`);
      console.log(`Final API call rate: ${finalRate}/${MAX_CALLS_PER_MINUTE} calls/minute`);
      
      return NextResponse.json({
        success: true,
        message: `Batch processed ${successCount} contacts (EmailGenerationProcessor handles Smartlead sync), ${failureCount} failed`,
        syncedCount: successCount,
        failedCount: failureCount,
        totalCount: messages.length,
        campaign: {
          id: campaignId
        },
        lead_ids: syncedLeadIds,
        rate_limit_info: {
          max_calls_per_minute: MAX_CALLS_PER_MINUTE,
          bottleneck_size: BOTTLENECK_SIZE,
          final_rate: finalRate,
          batch_delay_ms: delayBetweenBatches
        },
        results: results.map(r => {
          if (r.status === 'fulfilled') {
            return r.value;
          } else {
            return { 
              success: false, 
              error: (r as PromiseRejectedResult).reason?.message || 'Unknown error'
            };
          }
        })
      });
    }
  } catch (error) {
    console.error('Error in batch sync:', error);
    return NextResponse.json(
      { error: `Error batch syncing contacts: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

// Helper function to process contacts when contactIds are provided directly
async function processContacts(
  contacts: any[], 
  campaignId: string, 
  subject: string | null, 
  body: string | null
) {
  console.log(`Processing ${contacts.length} contacts for campaign ${campaignId}`);
  
  // Process contacts in batches with rate limiting
  const batchSize = BOTTLENECK_SIZE; // Use configured bottleneck size
  const batches: any[][] = [];
  for (let i = 0; i < contacts.length; i += batchSize) {
    batches.push(contacts.slice(i, i + batchSize));
  }
  
  const results: any[] = [];
  
  // Calculate optimal delay to stay under rate limit
  const delayBetweenBatches = calculateBatchDelay(batchSize);
  const estimatedDuration = ((batches.length - 1) * delayBetweenBatches) / 1000 / 60; // minutes
  
  console.log(`Processing ${contacts.length} contacts in ${batches.length} batches of ${batchSize}`);
  console.log(`Rate limit: ${MAX_CALLS_PER_MINUTE} calls/minute, batch delay: ${delayBetweenBatches}ms`);
  console.log(`Estimated completion time: ${estimatedDuration.toFixed(1)} minutes`);
  
  // Process each batch with a delay between batches
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i+1}/${batches.length} (${batch.length} contacts)`);
    
    // Add a delay between batches, except for the first one
    if (i > 0) {
      const currentRate = rateTracker.getCallRate();
      console.log(`Waiting ${delayBetweenBatches}ms before next batch... (Current rate: ${currentRate}/${MAX_CALLS_PER_MINUTE} calls/min)`);
      await sleep(delayBetweenBatches);
    }
    
    const batchResults = await Promise.allSettled(
      batch.map(async (contact) => {
        try {
          console.log(`Syncing contact ${contact.contact_id} (${contact.email}) to campaign ${campaignId}`);
          
          // Get additional contact details if needed
          const { rows: fullContacts } = await pool.query(`
            SELECT 
              c.contact_id,
              c.first_name,
              c.last_name,
              c.email,
              c.phone_number,
              c.linkedin_url,
              c.contact_city,
              c.contact_state,
              c.contact_country,
              co.company_name,
              co.website as company_website
            FROM contacts c
            LEFT JOIN companies co ON c.company_id = co.company_id
            WHERE c.contact_id = $1
          `, [contact.contact_id]);
          
          const fullContact = fullContacts[0] || contact;
          
          // Prepare sync payload for individual sync API
          
          // Note: EmailGenerationProcessor handles Smartlead sync automatically
          // This batch sync is for bulk operations, not individual email generation
          console.log(`Contact ${contact.contact_id} ready for batch processing (email generation handles sync)`);
          
          // Return success without calling sync API
          const leadId = null; // No lead ID since we're not syncing here
          
          return {
            contact_id: contact.contact_id,
            email: contact.email,
            success: true,
            lead_id: leadId,
            campaign_id: campaignId,
            message: 'Ready for batch processing (EmailGenerationProcessor handles sync)'
          };
        } catch (error) {
          console.error(`Error syncing contact ${contact.contact_id}:`, error);
          return {
            contact_id: contact.contact_id,
            email: contact.email || 'unknown',
            success: false,
            error: (error as Error).message
          };
        }
      })
    );
    
    results.push(...batchResults);
  }
  
  // Count successes and failures
  const successResults = results.filter(r => 
    r.status === 'fulfilled' && (r.value as any).success
  );
  const successCount = successResults.length;
  const failureCount = results.length - successCount;
  const finalRate = rateTracker.getCallRate();
  
  // Get the lead IDs of successfully synced contacts
  const syncedLeadIds = successResults
    .map(r => r.status === 'fulfilled' ? (r.value as any).lead_id : null)
    .filter(Boolean);
  
  console.log(`Batch processing completed: ${successCount} successful, ${failureCount} failed`);
  console.log(`Final API call rate: ${finalRate}/${MAX_CALLS_PER_MINUTE} calls/minute`);
  
  return NextResponse.json({
    success: true,
    message: `Batch processed ${successCount} contacts (EmailGenerationProcessor handles Smartlead sync), ${failureCount} failed`,
    syncedCount: successCount,
    failedCount: failureCount,
    totalCount: contacts.length,
    campaign: {
      id: campaignId
    },
    lead_ids: syncedLeadIds,
    rate_limit_info: {
      max_calls_per_minute: MAX_CALLS_PER_MINUTE,
      bottleneck_size: BOTTLENECK_SIZE,
      final_rate: finalRate,
      batch_delay_ms: delayBetweenBatches
    },
    results: results.map(r => {
      if (r.status === 'fulfilled') {
        return r.value;
      } else {
        return { 
          success: false, 
          error: (r as PromiseRejectedResult).reason?.message || 'Unknown error'
        };
      }
    })
  });
} 