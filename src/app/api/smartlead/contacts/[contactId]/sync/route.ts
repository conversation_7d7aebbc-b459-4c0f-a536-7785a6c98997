import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

/**
 * Smartlead API Integration
 * 
 * This API route handles syncing contacts to the Smartlead email automation platform.
 * 
 * Important formatting note:
 * - Line breaks (\n) in email content are converted to HTML <br/> tags
 *   to ensure proper formatting in the Smartlead UI and prevent text from
 *   displaying as a single long paragraph.
 */

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '';
const SMARTLEAD_BASE_URL = 'https://server.smartlead.ai/api/v1/';
const SMARTLEAD_CAMPAIGN_ID = process.env.SMARTLEAD_CAMPAIGN_ID || '1897921';
const WORKSPACE_ID = process.env.WORKSPACE_ID || 'f8b45e3c-7186-4fdc-a696-c5971b1d2c7f';

// Utility function to process email content with smart newline handling
function processEmailContent(content: string, isHtml?: boolean): string {
  if (!content) return '';
  
  // Auto-detect if content is HTML
  const isHtmlContent = isHtml || (content.includes('<') && content.includes('>'));
  
  if (isHtmlContent) {
    // For HTML content: remove embedded newlines but preserve structure
    return content
      .replace(/\n/g, '') // Remove literal newlines
      .replace(/<br\s*\/?>\s*<br\s*\/?>/gi, '<br>') // Clean up multiple <br> tags
      .trim();
  } else {
    // For plain text: convert newlines to <br> tags
    return content
      .replace(/\n/g, '<br>')
      .replace(/<br\s*\/?>\s*<br\s*\/?>/gi, '<br>') // Clean up multiple <br> tags
      .trim();
  }
}

// Define an interface for the lead data structure
interface SmartleadLead {
  id?: string;
  first_name: string;
  last_name: string;
  email: string;
  created_at?: string;
  phone_number?: string;
  company_name?: string;
  website?: string;
  location?: string;
  custom_fields: Record<string, any>;
  linkedin_profile?: string;
  company_url?: string;
  is_unsubscribed?: boolean;
}

// Utility function to fetch lead ID by email
async function fetchLeadIdByEmail(email: string, campaignId: string): Promise<string | null> {
  try {
    console.log(`Fetching lead ID for email: ${email}`);
    
    // Using direct leads endpoint with email filter
    const apiUrl = `${SMARTLEAD_BASE_URL}leads/?api_key=${SMARTLEAD_API_KEY}&email=${encodeURIComponent(email)}`;
    // console.log(`API URL: ${apiUrl}`);
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching lead ID: ${errorText}`);
      return null;
    }

    const data = await response.json();
    // console.log('Lead lookup response:', JSON.stringify(data));
    
    let leadId: string | null = null;
    
    // Handle different response structures
    if (data && Array.isArray(data) && data.length > 0) {
      // Direct array of leads
      const matchingLead = data.find((lead: any) => lead.email?.toLowerCase() === email.toLowerCase());
      if (matchingLead) {
        leadId = String(matchingLead.id || matchingLead.lead_id);
        console.log(`Found lead with ID: ${leadId}, name: ${matchingLead.first_name} ${matchingLead.last_name}`);
      }
    } else if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
      // Data property containing array of leads
      const matchingLead = data.data.find((lead: any) => lead.email?.toLowerCase() === email.toLowerCase());
      if (matchingLead) {
        leadId = String(matchingLead.id || matchingLead.lead_id);
        console.log(`Found lead with ID: ${leadId}, name: ${matchingLead.first_name} ${matchingLead.last_name}`);
      }
    } else if (data && data.id && data.email?.toLowerCase() === email.toLowerCase()) {
      // Single lead object
      leadId = String(data.id);
      console.log(`Found lead with ID: ${leadId}, name: ${data.first_name} ${data.last_name}`);
    } else if (typeof data === 'string') {
      // Some APIs might return just the ID as a string
      leadId = data;
      console.log(`Found lead ID (direct string): ${leadId}`);
    } else if (typeof data === 'number') {
      // Handle case where a numeric ID is returned directly
      leadId = String(data);
      console.log(`Found lead ID (numeric): ${leadId}`);
    }
    
    // Ensure we always return a string ID or null, never an object or number
    if (leadId !== null && typeof leadId !== 'string') {
      leadId = String(leadId);
    }
    
    return leadId;
  } catch (error) {
    console.error(`Error fetching lead ID for email ${email}:`, error);
    return null;
  }
}

// Generate a fallback ID
function generateFallbackId(email: string, campaignId: string): string {
  // Simple hashing function to create a deterministic ID
  let hash = 0;
  const str = `${email}:${campaignId}`;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(16).substring(0, 8);
}

// Function to fetch latest message for a contact
async function fetchLatestMessageForContact(contactId: string): Promise<{ subject: string, html_body: string } | null> {
  try {
    const result = await pool.query(
      `
      SELECT m.subject, m.body
      FROM messages m
      JOIN thread_participants tp ON m.thread_id = tp.thread_id
      WHERE tp.contact_id = $1 AND m.direction = 'outbound'
      ORDER BY m.sent_at DESC
      LIMIT 1
      `,
      [contactId]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    // Make sure newlines in body are preserved as-is for later processing
    const body = result.rows[0].body || '';
    
    return { 
      subject: result.rows[0].subject,
      html_body: body // Keep original format, will be processed in sendToSmartlead
    };
  } catch (error) {
    console.error(`Error fetching latest message for contact ${contactId}:`, error);
    return null;
  }
}

// Function to send contact to Smartlead API
async function sendToSmartlead(contact: any, emailContent: any, campaignId: string, customFields?: any) {
  try {
    console.log(`Sending contact ${contact.email} to Smartlead campaign ${campaignId}`);
    
    // Process email body with smart content detection
    let processedHtmlBody = '';
    if (emailContent && emailContent.html_body) {
      processedHtmlBody = processEmailContent(emailContent.html_body, true);
    }
    
    // Build the lead data
    const leadData = {
      email: contact.email,
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      company_name: contact.company_name || '',
      website: '',
      job_title: contact.title || '',
      phone_number: '',
      city: contact.contact_city || '',
      state: contact.contact_state || '',
      country: contact.contact_country || '',
      linkedin_url: contact.linkedin_url || '',
      tags: ['anax-sync'],
      custom_fields: {}
    };
    
    // Handle custom fields
    if (customFields && typeof customFields === 'object') {
      // Process custom fields with smart content detection
      const cleanedCustomFields: any = {};
      for (const key in customFields) {
        if (typeof customFields[key] === 'string') {
          const isHtmlField = key.includes('html') || customFields[key].includes('<');
          cleanedCustomFields[key] = processEmailContent(customFields[key], isHtmlField);
        } else {
          cleanedCustomFields[key] = customFields[key];
        }
      }
      // Use the cleaned custom fields
      leadData.custom_fields = cleanedCustomFields;
    } else if (emailContent) {
      // Use the subject and body from emailContent
      leadData.custom_fields = {
        ...(emailContent.subject ? { subject: emailContent.subject } : {}),
        ...(processedHtmlBody ? { html_body: processedHtmlBody } : {})
      };
    }
    
    // Prepare the payload
    const payload = {
      lead_list: [leadData]
    };
    
    // First check if the API key is properly set
    if (!SMARTLEAD_API_KEY || SMARTLEAD_API_KEY.trim() === '') {
      console.error('Smartlead API key is missing or empty');
      return null;
    }
    
    // Log the API endpoint we're using
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}`;
    console.log(`Sending request to: ${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads`);
    
    // Check if campaign exists before trying to add leads
    try {
      const campaignCheckUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}?api_key=${SMARTLEAD_API_KEY}`;
      const campaignResponse = await fetch(campaignCheckUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!campaignResponse.ok) {
        console.error(`Campaign ${campaignId} does not exist or is not accessible. Status: ${campaignResponse.status}`);
        const errorText = await campaignResponse.text();
        console.error(`Campaign check error: ${errorText}`);
      } else {
        console.log(`Campaign ${campaignId} exists and is accessible`);
      }
    } catch (campaignError) {
      console.error(`Error checking campaign: ${campaignError}`);
    }
    
    // Send the lead data to Smartlead
    console.log(`Sending payload:`, JSON.stringify(payload));
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error sending lead to Smartlead (Status: ${response.status}): ${errorText}`);
      
      // Try alternative endpoint formats if the main one fails
      if (response.status === 404 || response.status === 403) {
        console.log('Trying alternative endpoint format...');
        
        // Some APIs might expect the format without 'campaigns/'
        const altApiUrl = `${SMARTLEAD_BASE_URL}${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}`;
        console.log(`Attempting alternative URL: ${altApiUrl}`);
        
        try {
          const altResponse = await fetch(altApiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
          });
          
          if (altResponse.ok) {
            const altData = await altResponse.json();
            console.log('Alternative endpoint successful:', JSON.stringify(altData));
            return processApiResponse(altData, contact, campaignId);
          } else {
            console.error(`Alternative endpoint also failed: ${await altResponse.text()}`);
          }
        } catch (altError) {
          console.error(`Error with alternative endpoint: ${altError}`);
        }
      }
      
      return null;
    }
    
    const data = await response.json();
    console.log('Smartlead API response:', JSON.stringify(data));
    
    return processApiResponse(data, contact, campaignId);
  } catch (error) {
    console.error(`Exception sending to Smartlead: ${error}`);
    return null;
  }
}

// Helper function to process API response and extract lead ID
async function processApiResponse(data: any, contact: any, campaignId: string) {
  // After successful API call, get the lead ID from Smartlead
  let leadId: string | null = null;
  
  // First try to get lead ID directly from the response
  if (data.data && Array.isArray(data.data) && data.data.length > 0) {
    const leadData = data.data[0];
    leadId = leadData.id || leadData.lead_id;
    console.log(`Found lead ID in response: ${leadId}`);
  }
  
  // If we couldn't get the lead ID from the response, fetch it separately
  if (!leadId) {
    try {
      const fetchedLeadId = await fetchLeadIdByEmail(contact.email, campaignId);
      
      if (fetchedLeadId) {
        leadId = fetchedLeadId;
        console.log(`Retrieved lead ID by email lookup: ${leadId}`);
      }
    } catch (error) {
      console.error(`Error getting lead ID by email: ${error}`);
      // We don't fail the request if this part fails, as the lead was still added to Smartlead
    }
  }
  
  // Update contact if we have a lead ID
  if (leadId) {
    try {
      // Update the contact with the Smartlead lead ID and status
      await pool.query(`
        UPDATE contacts
        SET smartlead_lead_id = $1,
            smartlead_status = 'ACTIVE',
            last_email_sent_at = NOW()
        WHERE contact_id = $2
      `, [leadId, contact.contact_id]);
      
      console.log(`Updated contact ${contact.contact_id} with Smartlead lead ID ${leadId}`);
    } catch (error) {
      console.error(`Error updating lead ID for contact ${contact.contact_id}:`, error);
      // We don't fail the request if this part fails, as the lead was still added to Smartlead
    }
  }
  
  return {
    lead_id: leadId,
    contact_id: contact.contact_id,
    email: contact.email,
    custom_fields: contact.custom_fields,
    smartlead_response: data,
    sent_at: new Date().toISOString(),
    campaign_id: campaignId
  };
}

// Function to update database with lead data
async function updateDatabase(leadData: any) {
  if (!leadData) return null;
  
  console.log(`Updating database for contact ${leadData.contact_id}`);
  
  try {
    // Start transaction
    await pool.query('BEGIN');
    
    // Update contact with Smartlead ID and status
    await pool.query(`
      UPDATE contacts
      SET smartlead_lead_id = $1,
          smartlead_status = 'ADDED'
      WHERE contact_id = $2
    `, [leadData.lead_id, leadData.contact_id]);
    
    // Commit transaction
    await pool.query('COMMIT');
    
    console.log(`Database updated for contact ${leadData.contact_id}`);
    return leadData.lead_id;
  } catch (error) {
    await pool.query('ROLLBACK');
    console.error(`Error updating database: ${error}`);
    throw error;
  }
}

// Utility function to fetch campaign details
async function fetchCampaignDetails(campaignId: string): Promise<{id: string, name: string}> {
  try {
    if (!campaignId) {
      console.error('Campaign ID is required to fetch campaign details');
      return {
        id: 'unknown',
        name: 'Unknown Campaign'
      };
    }

    console.log(`Fetching campaign details for ID: ${campaignId}`);
    
    // Make sure campaignId is a string before using it in the URL
    const campaignIdString = String(campaignId);
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignIdString}?api_key=${SMARTLEAD_API_KEY}`;
    
    try {
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error fetching campaign details (${response.status}): ${errorText}`);
        // Return fallback with campaign ID on API error
        return {
          id: campaignIdString,
          name: `${campaignIdString}`
        };
      }

      const data = await response.json();
      // console.log('Campaign details:', JSON.stringify(data));
      
      // Handle different response formats
      if (data && data.id) {
        // Single campaign object with ID
        return {
          id: String(data.id),
          name: data.name || `Campaign ${data.id}`
        };
      } else if (data && Array.isArray(data) && data.length > 0 && data[0].id) {
        // Array of campaigns - take the first one
        return {
          id: String(data[0].id),
          name: data[0].name || `Campaign ${data[0].id}`
        };
      } else if (data && data.data && data.data.id) {
        // Nested data object
        return {
          id: String(data.data.id),
          name: data.data.name || `Campaign ${data.data.id}`
        };
      } else if (data && data.data && Array.isArray(data.data) && data.data.length > 0 && data.data[0].id) {
        // Nested data array
        return {
          id: String(data.data[0].id),
          name: data.data[0].name || `Campaign ${data.data[0].id}`
        };
      }
      
      console.warn('Could not parse campaign details from response:', JSON.stringify(data));
      
      // Fallback with just the ID if we can't get proper details
      return {
        id: campaignIdString,
        name: `Campaign ${campaignIdString}`
      };
    } catch (fetchError) {
      console.error(`Fetch error getting campaign details: ${fetchError}`);
      // Return fallback with campaign ID on fetch error
      return {
        id: campaignIdString,
        name: `Campaign ${campaignIdString}`
      };
    }
  } catch (error) {
    console.error(`Error in fetchCampaignDetails for ID ${campaignId}:`, error);
    // Return a default object with the ID we already have
    return {
      id: String(campaignId),
      name: `Campaign ${campaignId}`
    };
  }
}

// GET: Fetch Smartlead lead info for a contact
export async function GET(
  req: NextRequest,
  context: { params: { contactId: string } }
) {
  const params = await context.params;
  const { contactId } = params;
  
  try {
    if (!contactId || isNaN(Number(contactId))) {
      return NextResponse.json(
        { error: 'Invalid contact ID' },
        { status: 400 }
      );
    }

    // Get sorting parameters from query
    const url = new URL(req.url);
    const sort = url.searchParams.get('sort') || 'created_at';
    const order = url.searchParams.get('order') || 'desc';
    const validSortFields = ['created_at', 'updated_at', 'subject', 'status'];
    
    const sortField = validSortFields.includes(sort) ? sort : 'created_at';
    const sortOrder = order === 'asc' ? 'ASC' : 'DESC';
    
    // First, get the contact details including Smartlead info
    const { rows: contacts } = await pool.query(`
      SELECT 
        c.contact_id,
        c.first_name,
        c.last_name,
        c.email,
        c.smartlead_lead_id,
        c.smartlead_status,
        c.last_email_sent_at,
        co.company_name
      FROM contacts c
      LEFT JOIN companies co USING (company_id)
      WHERE c.contact_id = $1
    `, [contactId]);
    
    if (!contacts.length) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    const contact = contacts[0];
    
    // Fetch available campaigns
    let availableCampaigns: Array<{id: string, name: string}> = [];
    try {
      const campaignsUrl = `${SMARTLEAD_BASE_URL}campaigns?api_key=${SMARTLEAD_API_KEY}`;
      const campaignsResponse = await fetch(campaignsUrl);
      if (campaignsResponse.ok) {
        const campaignsData = await campaignsResponse.json();
        if (Array.isArray(campaignsData)) {
          availableCampaigns = campaignsData.map(c => ({
            id: c.id,
            name: c.name
          }));
        } else if (campaignsData.total_campaigns > 0 && Array.isArray(campaignsData.data)) {
          availableCampaigns = campaignsData.data.map((c: { id: string, name: string }) => ({
            id: c.id,
            name: c.name
          }));
        }
      }
    } catch (error) {
      console.error('Error fetching available campaigns:', error);
    }
    
    // If the contact has a smartlead_lead_id, get the associated threads and campaigns
    let threads: any[] = [];
    const campaigns: Array<{campaign_id: string}> = [];
    
    if (contact.smartlead_lead_id) {
      // Get threads related to Smartlead for this contact
      const { rows: threadRows } = await pool.query(`
        SELECT 
          t.thread_id,
          t.subject,
          t.status,
          t.created_at,
          t.updated_at,
          t.metadata
        FROM threads t
        JOIN thread_participants tp ON t.thread_id = tp.thread_id
        WHERE tp.contact_id = $1
        AND t.metadata->'source' ? 'smartlead'
        ORDER BY t.${sortField} ${sortOrder}
      `, [contactId]);
      
      // Get messages for each thread
      threads = await Promise.all(
        threadRows.map(async (thread) => {
          const { rows: messages } = await pool.query(`
            SELECT 
              message_id,
              thread_id,
              from_email,
              to_email,
              subject,
              body,
              direction,
              role,
              sent_at,
              created_at,
              metadata
            FROM messages
            WHERE thread_id = $1
            ORDER BY created_at ASC
          `, [thread.thread_id]);
          
          // Extract campaign ID from thread metadata if available
          const threadMetadata = typeof thread.metadata === 'string' 
            ? JSON.parse(thread.metadata)
            : thread.metadata;
          
          const campaignId = threadMetadata?.campaign_id;
          
          if (campaignId && !campaigns.some(c => c.campaign_id === campaignId)) {
            campaigns.push({ campaign_id: campaignId });
          }
          
          return {
            ...thread,
            metadata: threadMetadata,
            messages
          };
        })
      );
    }
    
    return NextResponse.json({
      contact,
      threads,
      campaigns,
      availableCampaigns,
      hasSmartleadData: Boolean(contact.smartlead_lead_id)
    });
  } catch (error) {
    console.error(`Error fetching Smartlead data for contact ${params.contactId}:`, error);
    return NextResponse.json(
      { error: `Error fetching Smartlead data: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

  // POST: Sync a contact to Smartlead
export async function POST(
  req: NextRequest,
  context: { params: { contactId: string } }
) {
  try {
    const params = await context.params;
    const { contactId } = params;
    const requestData = await req.json();
    const { campaignId, subject, body, html_body, is_html, custom_fields } = requestData;
    
    if (!campaignId) {
      return NextResponse.json(
        { error: 'Campaign ID is required' },
        { status: 400 }
      );
    }
    
    console.log(`Syncing contact ${contactId} to Smartlead campaign ${campaignId}`);
    
    // Fetch contact details from database
    const contactResult = await pool.query(
      `SELECT * , co.company_name FROM contacts 
      LEFT JOIN companies co USING (company_id)
      WHERE contact_id = $1`,
      [contactId]
    );
    
    if (contactResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }
    
    const contact = contactResult.rows[0];
    
    // Check for required fields
    if (!contact.email) {
      return NextResponse.json(
        { error: 'Contact email is required' },
        { status: 400 }
      );
    }
    
    // 1. Fetch campaign details to get the campaign name
    const campaignDetails = await fetchCampaignDetails(campaignId);
    const campaignName = campaignDetails?.name || `Campaign ${campaignId}`;
    console.log(`Campaign details retrieved: ID ${campaignId}, Name: ${campaignName}`);
    
    // 2. Fetch existing lead ID if available
    const leadId = await fetchLeadIdByEmail(contact.email, campaignId);
    console.log(`Existing lead found for ${contact.email}: ${leadId ? 'Yes' : 'No'}`);
    
    // 3. Get existing custom fields if available
    // Since fetchLeadIdByEmail returns just the ID string, we need to get custom fields separately
    let existingCustomFields: Record<string, any> = {};
    if (leadId) {
      try {
        // Make sure leadId is a string before using it in URL
        const leadIdString = String(leadId);
        
        // Try to fetch the full lead details to get custom fields
        const apiUrl = `${SMARTLEAD_BASE_URL}leads/${leadIdString}?api_key=${SMARTLEAD_API_KEY}`;
        console.log(`Fetching lead details from: ${apiUrl}`);
        
        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (response.ok) {
          const leadData = await response.json();
          console.log('Lead details response:', JSON.stringify(leadData));
          
          if (leadData && leadData.custom_fields) {
            existingCustomFields = leadData.custom_fields;
          }
        } else {
          console.warn(`Failed to fetch lead details: ${response.status}`);
        }
      } catch (error) {
        console.log('Error fetching custom fields, will use empty object', error);
        // Continue with empty custom fields if fetch fails
      }
    } else {
      console.log('No lead ID available, skipping custom fields fetch');
    }
    
    console.log('Existing custom fields:', existingCustomFields);
    
    // Process the body content based on whether it's HTML
    let processedBody = body;
    
    // If is_html flag is true, we need to preserve the HTML content
    if (body && is_html === true) {
      // For HTML content, we keep it as is
      processedBody = body;
      
      // Add a flag to custom fields to indicate this is HTML
      if (!existingCustomFields.is_html) {
        existingCustomFields.is_html = "true";
      }
    } else if (body) {
      // For non-HTML content, we would typically convert newlines to <br/> tags
      // but since this was commented out, we'll keep it as is
      processedBody = body;
    }
    
    // Build the lead data with base fields based on Smartlead's lead schema
    const leadData: SmartleadLead = {
      email: contact.email,
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      phone_number: contact.phone_number || '',
      company_name: contact.company_name || '',
      website: contact.company_website || '',
      location: contact.company_city ? 
        [contact.company_city, contact.company_state, contact.company_country].filter(Boolean).join(', ') : 
        '',
      linkedin_profile: contact.linkedin_url || '',
      company_url: contact.company_website || '',
      custom_fields: { ...existingCustomFields } // Start with existing custom fields
    };
    
    // If we have a valid lead ID, include it to update the existing lead
    if (leadId && typeof leadId === 'string' && !leadId.startsWith('fb-')) {
      leadData.id = leadId;
      console.log(`Using existing lead ID: ${leadId} (type: ${typeof leadId})`);
    } else if (leadId) {
      // Ensure leadId is properly converted to string
      leadData.id = String(leadId);
      console.log(`Converted lead ID to string: ${leadData.id}`);
    }
    
    // 4. Add campaign name-related custom fields
    if (subject || processedBody || html_body) {
      // Create campaign-specific named fields
      const campaignKey = campaignName.replace(/[^a-zA-Z0-9]/g, '_')
      //remove multiple underscores to one underscore
      .replace(/_+/g, '_')
      //remove underscores at the beginning and end of the string
      .replace(/^_+|_+$/g, '')
      //remove multiple underscores to one underscore
      .replace(/_+/g, '_')
      .toLowerCase();
      
      // Add new custom fields with campaign-specific names
      if (subject) {
        leadData.custom_fields[`${campaignKey}_subject`] = subject;
        // Also keep a generic subject field
        leadData.custom_fields.subject = subject;
      }
      
      // Use HTML body if provided, otherwise use processed body
      if (html_body) {
        leadData.custom_fields[`${campaignKey}_html_body`] = html_body;
        // Store HTML version in custom fields
        leadData.custom_fields.html_body = html_body;
      }
      else if (processedBody) {
        leadData.custom_fields[`${campaignKey}_body`] = processedBody;
        // Also keep a generic body field
        leadData.custom_fields.body = processedBody;
      }
    }
    
    // Add any additional custom fields if provided directly in the request
    if (custom_fields && typeof custom_fields === 'object') {
      // console.log('Adding provided custom fields:', custom_fields);
      // Add all custom fields directly to the lead data
      Object.entries(custom_fields).forEach(([key, value]) => {
        // Make sure we don't add null or undefined values
        if (value !== null && value !== undefined) {
          leadData.custom_fields[key] = value;
        }
      });
    }
    
    // Ensure we don't exceed the custom field limit (Smartlead documentation mentions max 20 fields)
    const customFieldCount = Object.keys(leadData.custom_fields).length;
    if (customFieldCount > 20) {
      console.warn(`Custom field count (${customFieldCount}) exceeds Smartlead limit of 20. Some fields may be ignored.`);
    }
    
    // console.log('Final lead data:', leadData);
    
    // Create a copy of leadData without the id field for the API request
    // The Smartlead API doesn't accept 'id' when adding leads to a campaign
    const leadDataForRequest = { ...leadData };
    if (leadDataForRequest.id) {
      delete leadDataForRequest.id;
      console.log('Removed ID from request payload as per API requirements');
    }
    
    // Create the API payload with the lead list
    const apiPayload = {
      lead_list: [leadDataForRequest]
    };
    
    // console.log('Sending API payload:', JSON.stringify(apiPayload));
    
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiPayload),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error syncing contact to Smartlead: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to sync contact: ${response.status}` },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    console.log('Smartlead API response:', JSON.stringify(data));
    
    // After successful API call, get the lead ID from response if we don't already have it
    let responseLeadId = leadId;
    
    // If we don't have a valid lead ID yet, try to get it from the response
    if (!responseLeadId || responseLeadId.startsWith('fb-')) {
      if (data.data && Array.isArray(data.data) && data.data.length > 0) {
        const responseLeadData = data.data[0];
        responseLeadId = responseLeadData.id;
        console.log(`Found lead ID in response: ${responseLeadId}`);
      }
      
      // If we still don't have a lead ID, try to fetch it again
      if (!responseLeadId) {
        try {
          responseLeadId = await fetchLeadIdByEmail(contact.email, campaignId);
          
          if (responseLeadId) {
            console.log(`Retrieved lead ID by email lookup: ${responseLeadId}`);
          }
        } catch (error) {
          console.error(`Error getting lead ID by email: ${error}`);
          // We don't fail the request if this part fails, as the lead was still added to Smartlead
        }
      }
    }
    
    // Update contact if we have a lead ID
    if (responseLeadId) {
      try {
        // Update the contact with the Smartlead lead ID and status
        await pool.query(`
          UPDATE contacts
          SET smartlead_lead_id = $1,
              smartlead_status = 'ACTIVE',
              last_email_sent_at = NOW()
          WHERE contact_id = $2
        `, [responseLeadId, contactId]);
        
        console.log(`Updated contact ${contactId} with Smartlead lead ID ${responseLeadId}`);
      } catch (error) {
        console.error(`Error updating lead ID for contact ${contactId}:`, error);
        // We don't fail the request if this part fails, as the lead was still added to Smartlead
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Contact synced to Smartlead successfully',
      data,
      lead_id: responseLeadId,
      campaign: {
        id: campaignId,
        name: campaignName
      },
      contact: {
        smartlead_lead_id: responseLeadId,
        smartlead_status: 'ACTIVE'
      }
    });
    
  } catch (error) {
    console.error('Error syncing contact to Smartlead:', error);
    return NextResponse.json(
      { error: `Error syncing contact: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 