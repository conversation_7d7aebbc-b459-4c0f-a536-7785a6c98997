import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(
  req: NextRequest,
  { params }: { params: { contactId: string } }
) {
  try {
    const contactId = params.contactId;
    
    if (!contactId || isNaN(Number(contactId))) {
      return NextResponse.json(
        { error: 'Invalid contact ID' },
        { status: 400 }
      );
    }
    
    // First, get the contact details
    const { rows: contacts } = await pool.query(`
      SELECT 
        contact_id,
        first_name,
        last_name,
        email,
        smartlead_lead_id,
        smartlead_status,
        last_email_sent_at
      FROM contacts
      WHERE contact_id = $1
    `, [contactId]);
    
    if (!contacts.length) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }
    
    // Get all threads for this contact
    const { rows: threads } = await pool.query(`
      SELECT 
        t.thread_id,
        t.subject,
        t.created_at,
        t.updated_at
      FROM threads t
      JOIN thread_participants tp ON t.thread_id = tp.thread_id
      WHERE tp.contact_id = $1
      ORDER BY t.updated_at DESC
    `, [contactId]);
    
    // For each thread, get the messages
    const threadsWithMessages = await Promise.all(
      threads.map(async (thread) => {
        const { rows: messages } = await pool.query(`
          SELECT 
            message_id,
            thread_id,
            from_email,
            to_email,
            subject,
            body,
            direction,
            role,
            smartlead_campaign_id,
            created_at
          FROM messages
          WHERE thread_id = $1
          ORDER BY created_at ASC
        `, [thread.thread_id]);
        
        return {
          ...thread,
          messages
        };
      })
    );
    
    return NextResponse.json({
      contact: contacts[0],
      threads: threadsWithMessages
    });
  } catch (error) {
    console.error(`Error fetching threads for contact ${params.contactId}:`, error);
    return NextResponse.json(
      { error: `Error fetching threads: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 