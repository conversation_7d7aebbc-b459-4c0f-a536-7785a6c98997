import { NextRequest, NextResponse } from "next/server";
import { EnhancedFileManager } from "@/lib/utils/EnhancedFileManager";
import { pool } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await params;
    const file = await EnhancedFileManager.getFileById(fileId);

    if (!file) {
      return NextResponse.json(
        { success: false, message: "File not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true, file });

  } catch (error) {
    console.error("Error getting file:", error);
    return NextResponse.json(
      { success: false, message: "Failed to get file" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await params;

    // Get file details
    const file = await EnhancedFileManager.getFileById(fileId);
    if (!file) {
      return NextResponse.json(
        { success: false, message: "File not found" },
        { status: 404 }
      );
    }

    // Check if there are any relationships
    const relationshipsResult = await pool.query(
      'SELECT COUNT(*) as count FROM file_relationships WHERE file_id = $1',
      [fileId]
    );

    const relationshipCount = parseInt(relationshipsResult.rows[0].count);

    if (relationshipCount > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: `Cannot delete file: it has ${relationshipCount} active relationship(s). Please remove all relationships first.` 
        },
        { status: 400 }
      );
    }

    // Check if this is a duplicate file (same content hash exists elsewhere)
    const duplicateCheckResult = await pool.query(
      'SELECT COUNT(*) as count FROM files WHERE content_hash = $1 AND file_id != $2',
      [file.content_hash, fileId]
    );

    const duplicateCount = parseInt(duplicateCheckResult.rows[0].count);

    // Delete from storage provider
    const { StorageProviderRegistry } = await import("@/lib/storage/StorageProviderRegistry");
    const storageRegistry = StorageProviderRegistry.getInstance();
    const provider = storageRegistry.getProvider((file as any).storage_provider);
    
    if (provider) {
      await provider.deleteFile((file as any).storage_path);
    }

    // Delete from database
    await pool.query(
      'DELETE FROM files WHERE file_id = $1',
      [fileId]
    );

    return NextResponse.json({
      success: true,
      message: duplicateCount > 0 
        ? "File deleted successfully (duplicate content exists elsewhere)"
        : "File deleted successfully"
    });

  } catch (error) {
    console.error("Error deleting file:", error);
    return NextResponse.json(
      { success: false, message: "Failed to delete file" },
      { status: 500 }
    );
  }
}