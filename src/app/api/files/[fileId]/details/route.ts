import { NextRequest, NextResponse } from "next/server";
import { EnhancedFileManager } from "@/lib/utils/EnhancedFileManager";
import { pool } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await params;

    // Get file details
    const file = await EnhancedFileManager.getFileById(fileId);
    if (!file) {
      return NextResponse.json(
        { success: false, message: "File not found" },
        { status: 404 }
      );
    }

    // Get file relationships - handle both deals and dealsv2 tables
    const relationshipsResult = await pool.query(`
      SELECT 
        fr.*,
        CASE 
          WHEN fr.target_table_name = 'deals' THEN d.deal_name
          WHEN fr.target_table_name = 'dealsv2' THEN d2.deal_name
          WHEN fr.target_table_name = 'contacts' THEN CONCAT(c.first_name, ' ', c.last_name)
          WHEN fr.target_table_name = 'companies' THEN comp.company_name
          ELSE NULL
        END as linked_entity_name
      FROM file_relationships fr
      LEFT JOIN deals d ON fr.target_table_name = 'deals' AND fr.target_row_id = d.deal_id::text
      LEFT JOIN dealsv2 d2 ON fr.target_table_name = 'dealsv2' AND fr.target_row_id = d2.deal_id::text
      LEFT JOIN contacts c ON fr.target_table_name = 'contacts' AND fr.target_row_id = c.contact_id::text
      LEFT JOIN companies comp ON fr.target_table_name = 'companies' AND fr.target_row_id = comp.company_id::text
      WHERE fr.file_id = $1
      ORDER BY fr.is_primary DESC, fr.display_order, fr.created_at
    `, [fileId]);

    const relationships = relationshipsResult.rows;

    // Get duplicate files (same content hash)
    const duplicatesResult = await pool.query(`
      SELECT 
        f.file_id,
        f.original_name,
        f.uploaded_at,
        f.uploaded_by,
        COUNT(fr.relationship_id) as relationships_count
      FROM files f
      LEFT JOIN file_relationships fr ON f.file_id = fr.file_id
      WHERE f.content_hash = $1 AND f.file_id != $2
      GROUP BY f.file_id, f.original_name, f.uploaded_at, f.uploaded_by
      ORDER BY f.uploaded_at DESC
    `, [file.content_hash, fileId]);

    const duplicateFiles = duplicatesResult.rows;

    // Get storage provider display name
    const getStorageProviderName = (provider: string) => {
      switch (provider) {
        case 'local':
          return 'Local File System';
        case 'azure':
          return 'Azure Blob Storage';
        case 's3':
          return 'AWS S3';
        case 'gdrive':
          return 'Google Drive';
        default:
          return provider;
      }
    };

    // Get storage URL
    const getStorageUrl = (file: any) => {
      // If we have an actual storage URL from migration, use it
      if (file.storage_metadata?.actual_storage_url) {
        return file.storage_metadata.actual_storage_url;
      }
      
      // Fallback to manual construction
      if (file.storage_provider === 'local') {
        return file.storage_path;
      } else if (file.storage_provider === 'azure') {
        const accountName = file.storage_metadata?.account_name || 'unknown';
        const container = file.storage_metadata?.container || 'files';
        return `https://${accountName}.blob.core.windows.net/${container}/${file.storage_path}`;
      } else if (file.storage_provider === 's3') {
        const bucket = file.storage_metadata?.bucket || 'unknown';
        return `https://${bucket}.s3.amazonaws.com/${file.storage_path}`;
      } else if (file.storage_provider === 'gdrive') {
        return file.storage_metadata?.web_view_link || file.storage_path;
      }
      return file.storage_path;
    };

    const fileDetails = {
      ...file,
      storage_provider_name: getStorageProviderName(file.storage_provider),
      storage_url: getStorageUrl(file),
      relationships,
      duplicate_files: duplicateFiles
    };

    return NextResponse.json({
      success: true,
      file: fileDetails
    });

  } catch (error) {
    console.error("Error getting file details:", error);
    return NextResponse.json(
      { success: false, message: "Failed to get file details" },
      { status: 500 }
    );
  }
}
