import { NextRequest, NextResponse } from "next/server";
import { StorageMigrationService } from "@/lib/services/StorageMigrationService";

export async function POST(request: NextRequest) {
  try {
    const { fileIds, targetProvider = 'azure', priority = 0, batchSize = 10 } = await request.json();

    if (!fileIds) {
      return NextResponse.json(
        { success: false, message: "fileIds is required" },
        { status: 400 }
      );
    }

    let migrationIds: string[];

    if (Array.isArray(fileIds)) {
      // Bulk migration
      migrationIds = await StorageMigrationService.migrateBulkFiles(
        fileIds,
        targetProvider,
        batchSize,
        priority
      );
    } else {
      // Single file migration
      const migrationId = await StorageMigrationService.migrateFile(
        fileIds,
        targetProvider,
        priority
      );
      migrationIds = [migrationId];
    }

    return NextResponse.json({
      success: true,
      migrationIds,
      message: `Migration started for ${Array.isArray(fileIds) ? fileIds.length : 1} file(s)`
    });

  } catch (error) {
    console.error("Migration API error:", error);
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const migrationId = searchParams.get('migrationId');
    const fileId = searchParams.get('fileId');

    if (migrationId) {
      const status = await StorageMigrationService.getMigrationStatus(migrationId);
      if (!status) {
        return NextResponse.json(
          { success: false, message: "Migration not found" },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, migration: status });
    }

    if (fileId) {
      const status = await StorageMigrationService.getFileMigrationStatus(fileId);
      if (!status) {
        return NextResponse.json(
          { success: false, message: "No migration found for file" },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, migration: status });
    }

    // Get migration statistics
    const stats = await StorageMigrationService.getMigrationStats();
    return NextResponse.json({ success: true, stats });

  } catch (error) {
    console.error("Migration status API error:", error);
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}
