import { NextRequest, NextResponse } from "next/server";
import { StorageMigrationService } from "@/lib/services/StorageMigrationService";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ migrationId: string }> }
) {
  try {
    const { migrationId } = await params;
    const migration = await StorageMigrationService.getMigrationStatus(migrationId);

    if (!migration) {
      return NextResponse.json(
        { success: false, message: "Migration not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true, migration });

  } catch (error) {
    console.error("Get migration error:", error);
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ migrationId: string }> }
) {
  try {
    const { migrationId } = await params;
    const cancelled = await StorageMigrationService.cancelMigration(migrationId);

    if (!cancelled) {
      return NextResponse.json(
        { success: false, message: "Migration not found or cannot be cancelled" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Migration cancelled successfully"
    });

  } catch (error) {
    console.error("Cancel migration error:", error);
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ migrationId: string }> }
) {
  try {
    const { migrationId } = await params;
    const { action } = await request.json();

    if (action === 'retry') {
      const newMigrationId = await StorageMigrationService.retryMigration(migrationId);
      
      if (!newMigrationId) {
        return NextResponse.json(
          { success: false, message: "Failed to retry migration" },
          { status: 400 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Migration retried successfully",
        newMigrationId
      });
    }

    return NextResponse.json(
      { success: false, message: "Invalid action" },
      { status: 400 }
    );

  } catch (error) {
    console.error("Migration action error:", error);
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}
