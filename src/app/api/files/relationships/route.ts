import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET /api/files/relationships - Get relationships with pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "20");
    const offset = (page - 1) * pageSize;

    // Get total count
    const countResult = await pool.query(
      'SELECT COUNT(*) as count FROM file_relationships'
    );
    const total = parseInt(countResult.rows[0].count);

    // Get relationships with pagination
    const relationshipsResult = await pool.query(
      `SELECT 
        relationship_id,
        file_id,
        target_table_name,
        target_column_name,
        target_row_id,
        relationship_type,
        relationship_title,
        relationship_notes,
        display_order,
        is_primary,
        created_at,
        updated_at
      FROM file_relationships 
      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2`,
      [pageSize, offset]
    );

    const relationships = relationshipsResult.rows;

    return NextResponse.json({
      success: true,
      relationships,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    });
  } catch (error) {
    console.error("Error getting file relationships:", error);
    return NextResponse.json(
      { success: false, message: "Failed to get file relationships" },
      { status: 500 }
    );
  }
}