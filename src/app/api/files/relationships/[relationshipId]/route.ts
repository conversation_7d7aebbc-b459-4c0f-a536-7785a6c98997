import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ relationshipId: string }> }
) {
  try {
    const { relationshipId } = await params;

    // Get the relationship to check if it exists
    const relationshipResult = await pool.query(
      'SELECT * FROM file_relationships WHERE relationship_id = $1',
      [relationshipId]
    );

    if (relationshipResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: "Relationship not found" },
        { status: 404 }
      );
    }

    const relationship = relationshipResult.rows[0];

    // Delete the relationship
    await pool.query(
      'DELETE FROM file_relationships WHERE relationship_id = $1',
      [relationshipId]
    );

    // Check if there are any remaining relationships for this file
    const remainingRelationshipsResult = await pool.query(
      'SELECT COUNT(*) as count FROM file_relationships WHERE file_id = $1',
      [relationship.file_id]
    );

    const remainingCount = parseInt(remainingRelationshipsResult.rows[0].count);

    // If no relationships remain, optionally delete the file
    // This is configurable - you might want to keep orphaned files
    if (remainingCount === 0) {
      // Check if this is a duplicate file (same content hash exists elsewhere)
      const duplicateCheckResult = await pool.query(
        'SELECT COUNT(*) as count FROM files WHERE content_hash = (SELECT content_hash FROM files WHERE file_id = $1) AND file_id != $1',
        [relationship.file_id]
      );

      const duplicateCount = parseInt(duplicateCheckResult.rows[0].count);

      if (duplicateCount > 0) {
        // There are other files with the same content, safe to delete this one
        await pool.query(
          'DELETE FROM files WHERE file_id = $1',
          [relationship.file_id]
        );

        return NextResponse.json({
          success: true,
          message: "Relationship deleted and file removed (duplicate content exists elsewhere)",
          fileDeleted: true
        });
      } else {
        // This is the only file with this content, keep it but mark as orphaned
        await pool.query(
          'UPDATE files SET metadata = COALESCE(metadata, \'{}\') || \'{"orphaned": true, "orphaned_at": $1}\'::jsonb WHERE file_id = $2',
          [new Date().toISOString(), relationship.file_id]
        );

        return NextResponse.json({
          success: true,
          message: "Relationship deleted. File kept as it contains unique content.",
          fileDeleted: false,
          fileOrphaned: true
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: "Relationship deleted successfully",
      fileDeleted: false
    });

  } catch (error) {
    console.error("Error deleting relationship:", error);
    return NextResponse.json(
      { success: false, message: "Failed to delete relationship" },
      { status: 500 }
    );
  }
}