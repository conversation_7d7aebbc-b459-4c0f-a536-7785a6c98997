import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import { ProcessorQueueManager } from '@/lib/queue/ProcessorQueueManager';
import { ProcessingJob } from '@/types/processing';

export interface MigrationRequest {
  sourceProvider: string;
  targetProvider: string;
  fileIds?: string[];
  batchSize?: number;
  dryRun?: boolean;
}

export interface MigrationResult {
  success: boolean;
  jobId: string;
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: MigrationRequest = await request.json();
    const { sourceProvider, targetProvider, fileIds, batchSize = 10, dryRun = false } = body;

    if (!sourceProvider || !targetProvider) {
      return NextResponse.json(
        { success: false, message: 'Source and target providers are required' },
        { status: 400 }
      );
    }

    // Get files to migrate for dry run or validation
    let filesQuery = `
      SELECT file_id, original_name, storage_provider, storage_path, file_size_bytes
      FROM files 
      WHERE storage_provider = $1
    `;
    const queryParams = [sourceProvider];

    if (fileIds && fileIds.length > 0) {
      filesQuery += ` AND file_id = ANY($2)`;
      queryParams.push(fileIds as any);
    }

    const filesResult = await pool.query(filesQuery, queryParams);
    const files = filesResult.rows;

    if (dryRun) {
      const totalSize = files.reduce((sum, file) => sum + parseInt(file.file_size_bytes), 0);
      return NextResponse.json({
        success: true,
        message: `Dry run: Would migrate ${files.length} files (${formatBytes(totalSize)}) from ${sourceProvider} to ${targetProvider}`,
        fileCount: files.length,
        totalSize: totalSize
      });
    }

    // Create migration job in processing queue
    const queueManager = ProcessorQueueManager.getInstance();
    const jobData = {
      sourceProvider,
      targetProvider,
      fileIds,
      batchSize,
      dryRun: false
    };

    const jobId = await queueManager.addProcessorJob('storage_migration_bulk', {
      entityType: 'file' as any,
      singleId: 0, // Not applicable for bulk migration
      ...jobData
    });

    return NextResponse.json({
      success: true,
      jobId: jobId,
      message: `Migration job created for ${files.length} files. Job ID: ${jobId}`
    });

  } catch (error) {
    console.error('Migration error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    const queueManager = ProcessorQueueManager.getInstance();
    
    if (jobId) {
      // Get specific job status
      const job = await queueManager.getJob(jobId);
      if (!job) {
        return NextResponse.json(
          { success: false, message: 'Migration job not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, job });
    }

    // Return all migration jobs
    const jobs = await queueManager.getJobs('storage_migration_bulk', { limit: 50 });
    const migrationJobs = jobs.filter(job => 
      job.name === 'storage_migration_bulk' || 
      job.data?.type === 'storage_migration_bulk'
    );
    
    return NextResponse.json({ success: true, jobs: migrationJobs });

  } catch (error) {
    console.error('Error getting migration status:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

