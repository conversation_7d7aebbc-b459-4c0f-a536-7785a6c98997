import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'
import { sendHomepageWelcomeEmailViaGmail } from '@/lib/email/gmail-api-sender'

export async function PATCH(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: idParam } = await params
    const id = parseInt(idParam)
    const body = await request.json()
    const { status } = body

    // Validate required fields
    if (!status) {
      return NextResponse.json(
        { success: false, error: 'status is required' },
        { status: 400 }
      )
    }

    // Validate status value
    const validStatuses = ['pending', 'valid', 'invalid', 'pending_deal']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status. Must be one of: pending, valid, invalid, pending_deal' },
        { status: 400 }
      )
    }

    // First, get the interaction data
    const getInteractionQuery = `
      SELECT * FROM homepage_interactions WHERE id = $1
    `
    const interactionResult = await pool.query(getInteractionQuery, [id])
    
    if (interactionResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Interaction not found' },
        { status: 404 }
      )
    }

    const interaction = interactionResult.rows[0]

    // Update the interaction status
    const updateQuery = `
      UPDATE homepage_interactions 
      SET status = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `
    
    const result = await pool.query(updateQuery, [status, id])
    
    // If status is being set to 'valid' or 'pending_deal', create a contact and send email
    if (status === 'valid' || status === 'pending_deal') {
      try {
        // Check if contact already exists with this email
        const existingContactQuery = `
          SELECT contact_id FROM contacts WHERE email = $1
        `
        const existingContact = await pool.query(existingContactQuery, [interaction.email])
        
        if (existingContact.rows.length === 0) {
          // Handle company matching - same logic as the main contacts API
          let company_id = null;
          
          if (interaction.company) {
            // First try to find existing company by name
            const existingCompany = await pool.query(
              `SELECT company_id FROM companies WHERE company_name = $1`,
              [interaction.company]
            );

            if (existingCompany.rows.length > 0) {
              // Company exists, use its ID
              company_id = existingCompany.rows[0].company_id;
              
              // Optionally update the company with new information from interaction
              await pool.query(
                `UPDATE companies SET 
                  company_website = COALESCE($1, company_website)
                WHERE company_id = $2`,
                [interaction.company_website, company_id]
              );
            } else {
              // Company doesn't exist, create new one
              const companyResult = await pool.query(
                `INSERT INTO companies (
                  company_name, 
                  company_website
                ) 
                VALUES ($1, $2)
                RETURNING company_id`,
                [interaction.company, interaction.company_website || null]
              );
              company_id = companyResult.rows[0].company_id;
            }
          }

          // Create new contact from interaction data with company_id
          const createContactQuery = `
            INSERT INTO contacts (
              first_name,
              last_name,
              full_name,
              email,
              phone_number,
              linkedin_url,
              title,
              company_id,
              contact_city,
              contact_state,
              contact_country,
              notes,
              source,
              created_at,
              updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING contact_id
          `
          
          const fullName = interaction.first_name && interaction.last_name 
            ? `${interaction.first_name} ${interaction.last_name}`
            : interaction.first_name || interaction.last_name || null

          const contactValues = [
            interaction.first_name || null,
            interaction.last_name || null,
            fullName,
            interaction.email,
            interaction.phonenumber || null,
            interaction.linkedin || null,
            interaction.job_title || null,
            company_id, // Use the matched/created company_id
            interaction.city || null,
            interaction.region || null,
            interaction.country || null,
            interaction.message ? `Homepage form submission: ${interaction.message}` : 'Created from homepage form submission',
            'homepage_form',
            // created_at and updated_at are set automatically
          ]
          
          const contactResult = await pool.query(createContactQuery, contactValues)
          const contactId = contactResult.rows[0].contact_id
          
          console.log(`Created contact with ID ${contactId} from homepage interaction ${id}${company_id ? ` with company_id ${company_id}` : ''}`)
          
            // Send welcome email automatically via Gmail API
            try {
              const emailResult = await sendHomepageWelcomeEmailViaGmail({
                first_name: interaction.first_name,
                last_name: interaction.last_name,
                email: interaction.email,
                company: interaction.company,
                job_title: interaction.job_title,
                message: interaction.message,
                utm: interaction.utm ? JSON.parse(interaction.utm) : undefined
              })
              
              if (emailResult.success) {
                console.log(`Welcome email sent successfully via Gmail API to ${interaction.email}${emailResult.messageId ? ` (Message ID: ${emailResult.messageId})` : ''}`)
              } else {
                console.error(`Failed to send welcome email via Gmail API to ${interaction.email}: ${emailResult.error}`)
              }
            } catch (emailError) {
              console.error('Error sending welcome email via Gmail API:', emailError)
              // Don't fail the contact creation if email sending fails
            }
        } else {
          console.log(`Contact with email ${interaction.email} already exists, skipping creation`)
          
          // Still send welcome email for existing contacts when marked as valid
          try {
            const emailResult = await sendHomepageWelcomeEmailViaGmail({
              first_name: interaction.first_name,
              last_name: interaction.last_name,
              email: interaction.email,
              company: interaction.company,
              job_title: interaction.job_title,
              message: interaction.message,
              utm: interaction.utm ? JSON.parse(interaction.utm) : undefined
            })
            
            if (emailResult.success) {
              console.log(`Welcome email sent via Gmail API to existing contact ${interaction.email}${emailResult.messageId ? ` (Message ID: ${emailResult.messageId})` : ''}`)
            } else {
              console.error(`Failed to send welcome email via Gmail API to existing contact ${interaction.email}: ${emailResult.error}`)
            }
          } catch (emailError) {
            console.error('Error sending welcome email to existing contact:', emailError)
          }
        }
      } catch (contactError) {
        console.error('Error creating contact from homepage interaction:', contactError)
        // Don't fail the status update if contact creation fails
      }
    }
    
    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
    
  } catch (error) {
    console.error('Error updating homepage interaction:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update interaction status' 
      },
      { status: 500 }
    )
  }
}
