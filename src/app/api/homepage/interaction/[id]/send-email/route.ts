import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'
import { sendHomepageWelcomeEmailViaGmail } from '@/lib/email/gmail-api-sender'

export async function POST(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: idParam } = await params
    const id = parseInt(idParam)
    const body = await request.json()
    const { immediate = false } = body

    // Get the interaction data
    const getInteractionQuery = `
      SELECT * FROM homepage_interactions WHERE id = $1
    `
    const interactionResult = await pool.query(getInteractionQuery, [id])
    
    if (interactionResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Interaction not found' },
        { status: 404 }
      )
    }

    const interaction = interactionResult.rows[0]

    // Prepare contact data
    const contactData = {
      first_name: interaction.first_name,
      last_name: interaction.last_name,
      email: interaction.email,
      company: interaction.company,
      job_title: interaction.job_title,
      message: interaction.message,
      utm: interaction.utm ? JSON.parse(interaction.utm) : undefined
    }

    // Send email via Gmail API with optional impersonation
    // You can specify a different sender email here if needed
    const senderEmail = process.env.GMAIL_SENDER_EMAIL || '<EMAIL>'
    const emailResult = await sendHomepageWelcomeEmailViaGmail(contactData, senderEmail)

    if (emailResult.success) {
      return NextResponse.json({
        success: true,
        message: `Welcome email sent successfully via Gmail API to ${interaction.email}`,
        messageId: emailResult.messageId
      })
    } else {
      return NextResponse.json(
        { 
          success: false, 
          error: `Failed to send email via Gmail API: ${emailResult.error}` 
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error sending welcome email:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to send welcome email' 
      },
      { status: 500 }
    )
  }
}
