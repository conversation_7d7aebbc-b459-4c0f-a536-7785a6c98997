import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'
import { verifyRecaptchaToken, getClientIP } from '@/lib/recaptcha'

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
}

// FormData interface matching the new requirements
interface FormData {
  firstName: string
  lastName: string
  message: string
  company: string
  email: string
  phone: string
  jobTitle: string
  companyWebsite: string
  linkedin: string
  utm_source: string
  utm_campaign: string
  utm_medium: string
  utm_term: string
  utm_content: string
  utm_adgroupId: string
  utm_adgroup: string
  utm_placement: string
  ipAddress: string
  country: string
  region: string
  city: string
  gclid: string
  fbclid: string
  slug: string
  recaptcha: string
  // Allow any additional UTM parameters
  [key: string]: string | undefined
}

// Function to extract UTM parameters from form data
function extractUtmParameters(formData: FormData): Record<string, string> {
  const utmParams: Record<string, string> = {}
  
  // Extract all fields that start with 'utm_'
  Object.keys(formData).forEach(key => {
    if (key.startsWith('utm_') && formData[key]) {
      // Convert utm_source to source, utm_campaign to campaign, etc.
      const utmKey = key.replace('utm_', '')
      utmParams[utmKey] = formData[key] as string
    }
  })
  
  return utmParams
}

// Handle preflight OPTIONS requests
export async function OPTIONS(request: Request) {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  })
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const {
      firstName,
      lastName,
      email,
      phone,
      linkedin,
      jobTitle,
      company,
      companyWebsite,
      message,
      utm_source,
      utm_campaign,
      utm_medium,
      utm_term,
      utm_content,
      utm_adgroupId,
      utm_adgroup,
      utm_placement,
      ipAddress,
      country,
      region,
      city,
      gclid,
      fbclid,
      slug,
      recaptcha
    } = body as FormData

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { success: false, error: 'email is required' },
        { status: 400, headers: corsHeaders }
      )
    }

    if (!recaptcha) {
      return NextResponse.json(
        { success: false, error: 'recaptcha token is required' },
        { status: 400, headers: corsHeaders }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400, headers: corsHeaders }
      )
    }

    // Verify reCAPTCHA token
    const recaptchaVerified = await verifyRecaptchaToken(recaptcha)
    if (!recaptchaVerified) {
      return NextResponse.json(
        { success: false, error: 'reCAPTCHA verification failed' },
        { status: 400, headers: corsHeaders }
      )
    }

    // Check if email already exists
    const existingQuery = 'SELECT id FROM homepage_interactions WHERE email = $1'
    const existingResult = await pool.query(existingQuery, [email])
    
    if (existingResult.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Email already exists in our records' },
        { status: 409, headers: corsHeaders }
      )
    }

    // Get client IP if not provided
    const clientIP = ipAddress || getClientIP(request)

    // Extract UTM parameters from the form data
    const utmParams = extractUtmParameters(body as FormData)

    // Insert with all new fields including UTM JSON
    const insertQuery = `
      INSERT INTO homepage_interactions (
        first_name,
        last_name,
        email,
        phonenumber,
        linkedin,
        job_title,
        company,
        company_website,
        message,
        utm_campaign,
        utm_medium,
        utm_term,
        utm_content,
        utm_adgroup_id,
        utm_adgroup,
        utm_placement,
        utm,
        ip_address,
        country,
        region,
        city,
        gclid,
        fbclid,
        slug,
        recaptcha_token,
        recaptcha_verified,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27)
      RETURNING *
    `
    
    const values = [
      firstName || null,
      lastName || null,
      email,
      phone || null,
      linkedin || null,
      jobTitle || null,
      company || null,
      companyWebsite || null,
      message || null,
      utm_campaign || null,
      utm_medium || null,
      utm_term || null,
      utm_content || null,
      utm_adgroupId || null,
      utm_adgroup || null,
      utm_placement || null,
      Object.keys(utmParams).length > 0 ? JSON.stringify(utmParams) : null,
      clientIP,
      country || null,
      region || null,
      city || null,
      gclid || null,
      fbclid || null,
      slug || null,
      recaptcha || null,
      recaptchaVerified || false,
      'pending'
    ]
    
    try {
      const result = await pool.query(insertQuery, values)
      
      return NextResponse.json({
        success: true,
        message: 'Contact information saved successfully',
        data: result.rows[0]
      }, { headers: corsHeaders })
    } catch (dbError: any) {
      console.error('Database error:', dbError)
      throw dbError
    }
    
  } catch (error) {
    console.error('Error saving homepage interaction:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to save contact information' 
      },
      { status: 500, headers: corsHeaders }
    )
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const page = parseInt(searchParams.get('page') || '1')
    const offset = (page - 1) * limit

    // Get homepage interactions with pagination
    const query = `
      SELECT 
        id,
        first_name,
        last_name,
        email,
        phonenumber,
        linkedin,
        job_title,
        company,
        company_website,
        message,
        utm_campaign,
        utm_medium,
        utm_term,
        utm_content,
        utm_adgroup_id,
        utm_adgroup,
        utm_placement,
        utm,
        ip_address,
        country,
        region,
        city,
        gclid,
        fbclid,
        slug,
        recaptcha_token,
        recaptcha_verified,
        status,
        created_at,
        updated_at
      FROM homepage_interactions 
      ORDER BY created_at DESC 
      LIMIT $1 OFFSET $2
    `
    
    const countQuery = `SELECT COUNT(*) as total FROM homepage_interactions`
    
    const [result, countResult] = await Promise.all([
      pool.query(query, [limit, offset]),
      pool.query(countQuery)
    ])
    
    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, { headers: corsHeaders })
    
  } catch (error) {
    console.error('Error fetching homepage interactions:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch homepage interactions' 
      },
      { status: 500, headers: corsHeaders }
    )
  }
}
