import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET() {
  try {
    // Get the latest metrics data
    const query = `
      SELECT 
        sofr,
        sofr_30_day_avg,
        wsj_prime_rate,
        treasury_5_year,
        treasury_10_year,
        effective_date,
        created_at
      FROM homepage_metrics 
      ORDER BY effective_date DESC, created_at DESC 
      LIMIT 1
    `
    
    const result = await pool.query(query)
    
    if (result.rows.length === 0) {
      // Return sample data if no data exists
      return NextResponse.json({
        success: true,
        data: {
          sofr: 4.360,
          sofr_30_day_avg: 4.345,
          wsj_prime_rate: 7.500,
          treasury_5_year: 3.920,
          treasury_10_year: 4.346,
          effective_date: new Date().toISOString().split('T')[0],
          created_at: new Date().toISOString()
        }
      })
    }
    
    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
    
  } catch (error) {
    console.error('Error fetching homepage metrics:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch homepage metrics' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { 
      sofr, 
      sofr_30_day_avg, 
      wsj_prime_rate, 
      treasury_5_year, 
      treasury_10_year 
    } = body

    // Default effective_date to today if not provided
    const effective_date = (body as any)?.effective_date || new Date().toISOString().split('T')[0]

    const insertQuery = `
      INSERT INTO homepage_metrics (
        sofr, 
        sofr_30_day_avg, 
        wsj_prime_rate, 
        treasury_5_year, 
        treasury_10_year, 
        effective_date
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `
    
    const values = [
      sofr ?? null,
      sofr_30_day_avg ?? null,
      wsj_prime_rate ?? null,
      treasury_5_year ?? null,
      treasury_10_year ?? null,
      effective_date
    ]
    
    const result = await pool.query(insertQuery, values)
    
    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
    
  } catch (error) {
    console.error('Error creating homepage metrics:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create homepage metrics' 
      },
      { status: 500 }
    )
  }
}
