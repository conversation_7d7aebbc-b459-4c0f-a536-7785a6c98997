import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const page = parseInt(searchParams.get('page') || '1')
    const offset = (page - 1) * limit

    // Get deals from dealsv2 table with basic information for import
    const query = `
      SELECT 
        deal_id,
        deal_name,
        summary,
        ask_capital_position,
        ask_amount,
        deal_type,
        loan_type,
        loan_amount,
        loan_to_value_ltv,
        property_id,
        created_at
      FROM dealsv2 
      WHERE deal_name IS NOT NULL 
        AND deal_name != ''
        AND (summary IS NOT NULL OR ask_capital_position IS NOT NULL OR ask_amount IS NOT NULL)
      ORDER BY created_at DESC 
      LIMIT $1 OFFSET $2
    `
    
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM dealsv2 
      WHERE deal_name IS NOT NULL 
        AND deal_name != ''
        AND (summary IS NOT NULL OR ask_capital_position IS NOT NULL OR ask_amount IS NOT NULL)
    `
    
    const [result, countResult] = await Promise.all([
      pool.query(query, [limit, offset]),
      pool.query(countQuery)
    ])
    
    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
    
  } catch (error) {
    console.error('Error fetching dealsv2 for import:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch deals for import' 
      },
      { status: 500 }
    )
  }
}
