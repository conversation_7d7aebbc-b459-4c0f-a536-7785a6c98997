import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
}

// Capital Position Decision Logic
function determineCapitalPosition(askCapitalPosition: string[] | null): string {
  if (!askCapitalPosition || askCapitalPosition.length === 0) {
    return 'TBD'
  }

  // Step 1: Check for any Equity Position
  const equityPositions = askCapitalPosition.filter(pos => 
    pos.includes('Preferred Equity') || 
    pos.includes('Common Equity') || 
    pos.includes('LP') || 
    pos.includes('GP') ||
    pos.includes('Limited Partner') ||
    pos.includes('General Partner')
  )

  if (equityPositions.length > 0) {
    // Step 2: Prioritize between Equity Positions
    if (equityPositions.some(pos => pos.includes('Preferred Equity'))) {
      return 'Preferred Equity'
    }
    if (equityPositions.some(pos => pos.includes('Common Equity'))) {
      return 'Common Equity'
    }
    if (equityPositions.some(pos => pos.includes('LP') || pos.includes('Limited Partner'))) {
      return 'Limited Partner'
    }
    if (equityPositions.some(pos => pos.includes('GP') || pos.includes('General Partner'))) {
      return 'General Partner'
    }
  }

  // Step 3: Prioritize between Debt Positions
  const debtPositions = askCapitalPosition.filter(pos => 
    pos.includes('Senior Debt') || 
    pos.includes('Mezzanine')
  )

  if (debtPositions.length > 0) {
    if (debtPositions.some(pos => pos.includes('Senior Debt'))) {
      return 'Senior Debt'
    }
    if (debtPositions.some(pos => pos.includes('Mezzanine'))) {
      return 'Mezzanine'
    }
  }

  // Fallback to first position if no standard positions found
  return askCapitalPosition[0]
}

/**
 * Rounds a number to the nearest 10,000
 * @param {number} num The number to round
 * @returns {number} The rounded number
 */
function roundToNearestTenThousand(num: number): number {
  return Math.round(num / 10000) * 10000
}

// Format amount by rounding to nearest 10,000
function formatAmount(amount: number | null): string {
  if (!amount) return 'TBD'
  
  try {
    // Convert to number if it's a string
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    
    // Check if conversion was successful
    if (isNaN(numAmount)) {
      return amount.toString() // Return original if conversion fails
    }
    
    // Round to nearest 10,000
    const rounded = roundToNearestTenThousand(numAmount)
    
    // Format with commas and no decimal places
    return rounded.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })
  } catch (error) {
    // If any error occurs, return the original amount as string
    return amount.toString()
  }
}

// Get corresponding amount for the selected capital position
function getCorrespondingAmount(askCapitalPosition: string[] | null, askAmount: number[] | null, selectedPosition: string): string {
  if (!askCapitalPosition || !askAmount || askCapitalPosition.length === 0 || askAmount.length === 0) {
    return 'TBD'
  }

  // If only one amount, return it
  if (askAmount.length === 1) {
    return `$${formatAmount(askAmount[0])}`
  }

  // Try to find matching position and amount
  const positionIndex = askCapitalPosition.findIndex(pos => 
    pos.includes(selectedPosition) || selectedPosition.includes(pos)
  )

  if (positionIndex >= 0 && positionIndex < askAmount.length) {
    return `$${formatAmount(askAmount[positionIndex])}`
  }

  // Fallback to first amount
  return `$${formatAmount(askAmount[0])}`
}

// Handle preflight OPTIONS requests
export async function OPTIONS(request: Request) {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  })
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const page = parseInt(searchParams.get('page') || '1')
    const offset = (page - 1) * limit

    // Get published deals from dealsv2 table with required capital positions from deal_nsf_fields
    const query = `
      SELECT 
        d.deal_id as id,
        d.deal_name as position,
        d.deal_type as financing_type,
        CASE 
          WHEN d.loan_to_value_ltv IS NOT NULL 
          THEN (d.loan_to_value_ltv * 100)::text || '%'
          ELSE 'TBD'
        END as ltv,
        p.property_type,
        CASE 
          WHEN p.city IS NOT NULL AND p.state IS NOT NULL 
          THEN p.city || ', ' || p.state
          WHEN p.city IS NOT NULL 
          THEN p.city
          WHEN p.state IS NOT NULL 
          THEN p.state
          ELSE 'TBD'
        END as location,
        d.summary as description,
        NULL as image_url,
        NULL as external_link,
        d.published_time as created_at,
        d.updated_at,
        d.published,
       
        d.loan_amount,
        d.loan_type,
        d.interest_rate,
        d.loan_term,
    
        d.purchase_price,
        d.total_project_cost,
    
    
        p.market,
        p.submarket,
        -- Get required capital positions and amounts from deal_nsf_fields
        COALESCE(
          ARRAY_AGG(
            CASE 
              WHEN nsf.is_required = true AND nsf.nsf_context = 'sources' 
              THEN nsf.source_type 
              ELSE NULL 
            END
          ) FILTER (WHERE nsf.is_required = true AND nsf.nsf_context = 'sources'),
          ARRAY[]::text[]
        ) as ask_capital_position,
        COALESCE(
          ARRAY_AGG(
            CASE 
              WHEN nsf.is_required = true AND nsf.nsf_context = 'sources' 
              THEN nsf.amount 
              ELSE NULL 
            END
          ) FILTER (WHERE nsf.is_required = true AND nsf.nsf_context = 'sources'),
          ARRAY[]::numeric[]
        ) as ask_amount
      FROM dealsv2 d
      LEFT JOIN properties p ON d.property_id = p.property_id
      LEFT JOIN deal_nsf_fields nsf ON d.deal_id = nsf.deal_id
      WHERE d.published = true
      GROUP BY d.deal_id, d.deal_name, d.deal_type, d.loan_to_value_ltv, 
               p.property_type, p.city, p.state, d.summary, d.published_time, 
               d.updated_at, d.published, d.deal_stage, d.deal_status, 
               d.loan_amount, d.loan_type, d.interest_rate, d.loan_term, 
               d.hold_period, d.total_internal_rate_of_return_irr, 
               d.total_equity_multiple, d.purchase_price, d.total_project_cost,
               p.address, p.neighborhood, p.market, p.submarket
      ORDER BY d.published_time DESC 
      LIMIT $1 OFFSET $2
    `
    
    const countQuery = `SELECT COUNT(*) as total FROM dealsv2 WHERE published = true`
    
    const [result, countResult] = await Promise.all([
      pool.query(query, [limit, offset]),
      pool.query(countQuery)
    ])
    
    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / limit)
    
    // Process the results to apply capital position logic and formatting
    const processedData = result.rows.map(deal => {
      // Determine the final capital position using the decision logic
      const finalCapitalPosition = determineCapitalPosition(deal.ask_capital_position)
      
      // Get the corresponding amount for the selected capital position
      const askAmount = getCorrespondingAmount(deal.ask_capital_position, deal.ask_amount, finalCapitalPosition)
      
      // Create the response object, excluding LTV initially
      const { ltv, ...dealWithoutLtv } = deal
      const response: any = {
        ...dealWithoutLtv,
        ask_capital_position: finalCapitalPosition,
        ask_amount: askAmount,
        amount_required: askAmount // Keep for backward compatibility
      }
      
      // Only include LTV if it's not "TBD"
      if (ltv !== 'TBD') {
        response.ltv = ltv
      }
      
      return response
    })
    
    return NextResponse.json({
      success: true,
      data: processedData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, { headers: corsHeaders })
    
  } catch (error) {
    console.error('Error fetching published deals:', error)
    
    // Return sample deals data if database is unavailable
    const sampleDeals = [
      {
        id: 1,
        position: "Manhattan Condo Development",
        financing_type: "Construction Loan", 
        ask_capital_position: ["Senior Debt"],
        ask_amount: [15000000],
        amount_required: "$15,000,000.00",
        ltv: "75%",
        property_type: "Residential",
        location: "Manhattan, NY",
        description: "Prime location condo development opportunity with strong market fundamentals and experienced development team.",
        image_url: "/assets/img/image3.jpg",
        external_link: "https://www.linkedin.com/posts/ericbrody1_not-every-project-is-a-tower-we-didnt-always-activity-7363936569142976515-kFKZ",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        position: "Brooklyn Mixed-Use",
        financing_type: "Bridge Financing",
        ask_capital_position: ["Preferred Equity"],
        ask_amount: [8500000],
        amount_required: "$8,500,000.00", 
        ltv: "70%",
        property_type: "Mixed-Use",
        location: "Brooklyn, NY",
        description: "Mixed-use development in rapidly growing Brooklyn neighborhood with strong rental demand and transit access.",
        image_url: "/assets/img/image3.png",
        external_link: "https://www.linkedin.com/posts/ericbrody1_hells-kitchen-one-of-the-most-successful-activity-7364305196941750272-TLR8",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 3,
        position: "Queens Apartment Complex",
        financing_type: "Permanent Financing",
        ask_capital_position: ["Senior Debt", "Mezzanine"],
        ask_amount: [12000000, 3000000],
        amount_required: "$12,000,000.00",
        ltv: "80%", 
        property_type: "Multifamily",
        location: "Queens, NY",
        description: "Stabilized apartment complex refinancing opportunity with strong cash flow and value-add potential.",
        image_url: "/assets/img/imag4.jpg", 
        external_link: "https://www.linkedin.com/posts/ericbrody1_a-major-chinese-conglomerate-wanted-credibility-activity-7363579742190546944-SWls",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    // Process sample data with capital position logic
    const processedSampleData = sampleDeals.map(deal => {
      const finalCapitalPosition = determineCapitalPosition(deal.ask_capital_position)
      const askAmount = getCorrespondingAmount(deal.ask_capital_position, deal.ask_amount, finalCapitalPosition)
      
      return {
        ...deal,
        ask_capital_position: finalCapitalPosition,
        ask_amount: askAmount,
        amount_required: askAmount
      }
    })

    return NextResponse.json({
      success: true,
      data: processedSampleData,
      pagination: {
        page: 1,
        limit: 10,
        total: 3,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    }, { headers: corsHeaders })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { 
      position,
      financing_type,
      amount_required,
      ltv,
      property_type,
      location,
      description,
      image_url,
      external_link
    } = body

    // Validate required fields
    if (!position || !financing_type || !amount_required) {
      return NextResponse.json(
        { success: false, error: 'position, financing_type, and amount_required are required' },
        { status: 400 }
      )
    }

    const insertQuery = `
      INSERT INTO homepage_deals (
        position,
        financing_type,
        amount_required,
        ltv,
        property_type,
        location,
        description,
        image_url,
        external_link
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `
    
    const values = [
      position,
      financing_type,
      amount_required,
      ltv || null,
      property_type || null,
      location || null,
      description || null,
      image_url || null,
      external_link || null
    ]
    
    const result = await pool.query(insertQuery, values)
    
    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
    
  } catch (error) {
    console.error('Error creating homepage deal:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create homepage deal' 
      },
      { status: 500 }
    )
  }
}
