import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id)
    const body = await request.json()
    const { 
      position,
      financing_type,
      amount_required,
      ltv,
      property_type,
      location,
      description,
      image_url,
      external_link
    } = body

    // Validate required fields
    if (!position || !financing_type || !amount_required) {
      return NextResponse.json(
        { success: false, error: 'position, financing_type, and amount_required are required' },
        { status: 400 }
      )
    }

    const updateQuery = `
      UPDATE homepage_deals SET
        position = $1,
        financing_type = $2,
        amount_required = $3,
        ltv = $4,
        property_type = $5,
        location = $6,
        description = $7,
        image_url = $8,
        external_link = $9,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $10
      RETURNING *
    `
    
    const values = [
      position,
      financing_type,
      amount_required,
      ltv || null,
      property_type || null,
      location || null,
      description || null,
      image_url || null,
      external_link || null,
      id
    ]
    
    const result = await pool.query(updateQuery, values)
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Deal not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
    
  } catch (error) {
    console.error('Error updating homepage deal:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update homepage deal' 
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id)

    const deleteQuery = `DELETE FROM homepage_deals WHERE id = $1 RETURNING id`
    const result = await pool.query(deleteQuery, [id])
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Deal not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Deal deleted successfully'
    })
    
  } catch (error) {
    console.error('Error deleting homepage deal:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete homepage deal' 
      },
      { status: 500 }
    )
  }
}
