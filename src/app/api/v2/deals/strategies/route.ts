import { NextRequest, NextResponse } from "next/server";
import { withType<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/typeorm/middleware";
import { typeORMService } from "@/lib/typeorm/service";

async function getStrategiesHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const query = `
      SELECT DISTINCT 
        COALESCE(value_2, value_1) as value, 
        COALESCE(value_2, value_1) as label
      FROM central_mapping 
      WHERE type = 'Strategies' 
      AND is_active = true 
      AND (value_1 IS NOT NULL AND value_1 != '')
      ORDER BY value ASC
    `;
    
    const results = await typeORMService.getDataSource().query(query);
    
    return NextResponse.json(results);
  } catch (error) {
    console.error("Error fetching strategies:", error);
    return NextResponse.json(
      { error: "Failed to fetch strategies" },
      { status: 500 }
    );
  }
}

export const GET = withTypeORMHandler(getStrategiesHandler);
