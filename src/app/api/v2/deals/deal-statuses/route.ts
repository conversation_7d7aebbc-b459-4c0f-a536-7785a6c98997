import { NextResponse } from "next/server";
import { TypeORMService } from "@/lib/typeorm/service";

export async function GET() {
  try {
    const typeORMService = TypeORMService.getInstance();
    await typeORMService.initialize();
    const dealsRepository = typeORMService.getDealsRepository();

    // Get unique deal statuses from dealsv2 table
    const dealStatuses = await dealsRepository
      .createQueryBuilder("deal")
      .select("DISTINCT deal.dealStatus", "dealStatus")
      .where("deal.dealStatus IS NOT NULL")
      .andWhere("deal.dealStatus != ''")
      .orderBy("deal.dealStatus", "ASC")
      .getRawMany();

    // Format the response and remove duplicates
    const uniqueStatuses = new Set<string>();
    const formattedStatuses = dealStatuses
      .map((item: any) => item.dealStatus)
      .filter((status: string) => {
        if (uniqueStatuses.has(status.toLowerCase())) {
          return false;
        }
        uniqueStatuses.add(status.toLowerCase());
        return true;
      })
      .map((status: string) => ({
        value: status,
        label: status
      }))
      .sort((a, b) => a.label.localeCompare(b.label));

    return NextResponse.json(formattedStatuses);
  } catch (error) {
    console.error("Error fetching deal statuses:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
