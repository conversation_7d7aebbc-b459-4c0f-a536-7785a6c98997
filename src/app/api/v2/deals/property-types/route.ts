import { NextRequest, NextResponse } from "next/server";
import { withType<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/typeorm/middleware";
import { typeORMService } from "@/lib/typeorm/service";

async function getPropertyTypesHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const query = `
      SELECT DISTINCT 
        CASE 
          WHEN value_2 IS NOT NULL AND value_2 != '' 
          THEN value_2 
          ELSE value_1 
        END as value,
        CASE 
          WHEN value_2 IS NOT NULL AND value_2 != '' 
          THEN value_2 
          ELSE value_1 
        END as label
      FROM central_mapping 
      WHERE type = 'Property Type' 
      AND is_active = true 
      AND value_1 IS NOT NULL 
      AND value_1 != ''
      AND value_2 IS NULL
      ORDER BY value ASC
    `;
    
    const results = await typeORMService.getDataSource().query(query);
    
    return NextResponse.json(results);
  } catch (error) {
    console.error("Error fetching property types:", error);
    return NextResponse.json(
      { error: "Failed to fetch property types" },
      { status: 500 }
    );
  }
}

export const GET = withT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(getPropertyTypesHandler);
