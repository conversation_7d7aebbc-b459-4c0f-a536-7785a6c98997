import { NextRequest, NextResponse } from "next/server";
import { withTypeORMHandler } from "@/lib/typeorm/middleware";
import { typeORMService } from "@/lib/typeorm/service";

async function getCitiesHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const states = searchParams.get("states")?.split(',').filter(v => v);
    
    let query = `
      SELECT DISTINCT p.city as value, p.city as label
      FROM properties p
      WHERE p.city IS NOT NULL AND p.city != ''
    `;
    
    const params: any[] = [];
    
    if (states && states.length > 0) {
      // Map states to central mapping values if needed
      query += ` AND p.state = ANY($1)`;
      params.push(states);
    }
    
    query += ` ORDER BY p.city ASC`;
    
    const results = await typeORMService.getDataSource().query(query, params);
    
    return NextResponse.json(results);
  } catch (error) {
    console.error("Error fetching cities:", error);
    return NextResponse.json(
      { error: "Failed to fetch cities" },
      { status: 500 }
    );
  }
}

export const GET = withTypeORMHandler(getCitiesHandler);
