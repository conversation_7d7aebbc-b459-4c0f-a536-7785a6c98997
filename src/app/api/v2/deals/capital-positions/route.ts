import { NextRequest, NextResponse } from "next/server";
import { withType<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/typeorm/middleware";
import { typeORMService } from "@/lib/typeorm/service";

async function getCapitalPositionsHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const query = `
      SELECT DISTINCT value_1 as value, value_1 as label
      FROM central_mapping 
      WHERE type = 'Capital Position' 
      AND is_active = true 
      AND value_1 IS NOT NULL 
      AND value_1 != ''
      ORDER BY value_1 ASC
    `;
    
    const results = await typeORMService.getDataSource().query(query);
    
    return NextResponse.json(results);
  } catch (error) {
    console.error("Error fetching capital positions:", error);
    return NextResponse.json(
      { error: "Failed to fetch capital positions" },
      { status: 500 }
    );
  }
}

export const GET = withTypeORMHandler(getCapitalPositionsHandler);
