import { NextRequest, NextResponse } from "next/server";
import { processorQueueManager } from "@/lib/queue/ProcessorQueueManager";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { dealId } = await request.json();
    
    if (!dealId) {
      return NextResponse.json(
        { error: "Deal ID is required" },
        { status: 400 }
      );
    }

    // Create job data for requirement extraction
    const jobData = {
      dealId: parseInt(dealId),
      files: [], // Empty files for reprocessing existing deal
      jobMetadata: {
        originalFileNames: [],
        fileCount: 0,
        uploadTimestamp: new Date().toISOString(),
        processorVersion: "v2"
      },
    };

    // Add requirement extraction job to queue
    const jobId = await processorQueueManager.addProcessorJob(
      'deal_requirement',
      {
        limit: 1,
        batchSize: 1,
        ...jobData
      },
      {
        priority: 1,
        delay: 0
      }
    );

    return NextResponse.json({
      success: true,
      message: "Requirement extraction job queued successfully",
      jobId: jobId,
    });

  } catch (error) {
    console.error("Error running requirement extraction:", error);
    return NextResponse.json(
      { 
        error: "Failed to run requirement extraction",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
} 