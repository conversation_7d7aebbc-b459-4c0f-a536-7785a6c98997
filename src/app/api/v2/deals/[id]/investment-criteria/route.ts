import { NextRequest, NextResponse } from 'next/server';
import { typeORMService } from '@/lib/typeorm/service';

// DELETE - Delete investment criteria for a specific deal
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    const body = await request.json();
    const { criteria_id, criteria_type } = body;

    if (!criteria_id || !criteria_type) {
      return NextResponse.json({ 
        error: "criteria_id and criteria_type are required" 
      }, { status: 400 });
    }

    console.log(`Deleting ${criteria_type} criteria ${criteria_id} for deal ${dealId}`);

    const dataSource = typeORMService.getDataSource();
    const queryRunner = dataSource.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      // First, check if the criteria exists and get the related IDs
      const checkQuery = `
        SELECT 
          ic.investment_criteria_id,
          ic.investment_criteria_debt_id,
          ic.investment_criteria_equity_id,
          ic.capital_position
        FROM investment_criteria_central ic
        WHERE ic.investment_criteria_id = $1 
        AND ic.entity_type = 'deal_v2' 
        AND ic.entity_id = $2
      `;

      const checkResult = await queryRunner.query(checkQuery, [criteria_id, dealId]);

      if (checkResult.length === 0) {
        await queryRunner.rollbackTransaction();
        return NextResponse.json({ 
          error: "Investment criteria not found for this deal" 
        }, { status: 404 });
      }

      const criteria = checkResult[0];
      console.log('Found criteria:', criteria);

      // Delete based on criteria type
      if (criteria_type === 'debt' && criteria.investment_criteria_debt_id) {
        // Delete debt criteria
        await queryRunner.query(
          'DELETE FROM investment_criteria_debt WHERE investment_criteria_debt_id = $1',
          [criteria.investment_criteria_debt_id]
        );
        console.log(`Deleted debt criteria with ID: ${criteria.investment_criteria_debt_id}`);

        // Update central record to remove debt reference
        await queryRunner.query(
          `UPDATE investment_criteria_central 
           SET investment_criteria_debt_id = NULL 
           WHERE investment_criteria_id = $1`,
          [criteria_id]
        );

      } else if (criteria_type === 'equity' && criteria.investment_criteria_equity_id) {
        // Delete equity criteria
        await queryRunner.query(
          'DELETE FROM investment_criteria_equity WHERE investment_criteria_equity_id = $1',
          [criteria.investment_criteria_equity_id]
        );
        console.log(`Deleted equity criteria with ID: ${criteria.investment_criteria_equity_id}`);

        // Update central record to remove equity reference
        await queryRunner.query(
          `UPDATE investment_criteria_central 
           SET investment_criteria_equity_id = NULL 
           WHERE investment_criteria_id = $1`,
          [criteria_id]
        );

      } else if (criteria_type === 'central') {
        // Delete both debt and equity if they exist
        if (criteria.investment_criteria_debt_id) {
          await queryRunner.query(
            'DELETE FROM investment_criteria_debt WHERE investment_criteria_debt_id = $1',
            [criteria.investment_criteria_debt_id]
          );
          console.log(`Deleted debt criteria with ID: ${criteria.investment_criteria_debt_id}`);
        }

        if (criteria.investment_criteria_equity_id) {
          await queryRunner.query(
            'DELETE FROM investment_criteria_equity WHERE investment_criteria_equity_id = $1',
            [criteria.investment_criteria_equity_id]
          );
          console.log(`Deleted equity criteria with ID: ${criteria.investment_criteria_equity_id}`);
        }

        // Delete the central record
        await queryRunner.query(
          'DELETE FROM investment_criteria_central WHERE investment_criteria_id = $1',
          [criteria_id]
        );
        console.log(`Deleted central criteria with ID: ${criteria_id}`);

      } else {
        await queryRunner.rollbackTransaction();
        return NextResponse.json({ 
          error: `Invalid criteria type: ${criteria_type}` 
        }, { status: 400 });
      }

      await queryRunner.commitTransaction();

      return NextResponse.json({
        success: true,
        message: `${criteria_type} criteria deleted successfully`,
        deletedIds: {
          central: criteria_type === 'central' ? criteria_id : null,
          debt: criteria_type === 'debt' ? criteria.investment_criteria_debt_id : null,
          equity: criteria_type === 'equity' ? criteria.investment_criteria_equity_id : null
        }
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('Database error during deletion:', error);
      throw error;
    } finally {
      await queryRunner.release();
    }

  } catch (error) {
    console.error('Error deleting investment criteria:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete investment criteria',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}




