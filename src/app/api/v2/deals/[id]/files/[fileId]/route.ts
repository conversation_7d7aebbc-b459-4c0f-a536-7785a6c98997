import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";

// DELETE /api/v2/deals/[id]/files/[fileId]
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; fileId: string }> }
) {
  try {
    const { id, fileId } = await params;
    const dealId = id;

    // Use the centralized smart deletion method
    const result = await FileManager.deleteFileRelationshipByTarget(
      fileId,
      "dealsv2",
      "deal_id",
      dealId
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 404 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error deleting V2 deal file:", error);
    return NextResponse.json(
      { error: "Failed to delete file" },
      { status: 500 }
    );
  }
} 