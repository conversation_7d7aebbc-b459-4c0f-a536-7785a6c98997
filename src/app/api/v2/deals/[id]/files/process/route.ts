import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";
import { processorQueueManager } from "@/lib/queue/ProcessorQueueManager";

// POST /api/v2/deals/[id]/files/process
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = id;
    const { selectedFiles } = await request.json();

    if (!selectedFiles || selectedFiles.length === 0) {
      return NextResponse.json(
        { error: "No files selected for processing" },
        { status: 400 }
      );
    }

    // Get the selected files using the generic FileManager
    const tableFiles = await FileManager.getTableFiles({
      table_name: "dealsv2",
      column_name: "deal_id",
      row_id: dealId,
    });

    // Filter to only the selected files
    const files = tableFiles.filter(file => selectedFiles.includes(file.file_id));

    if (files.length === 0) {
      return NextResponse.json(
        { error: "No valid files found for processing" },
        { status: 400 }
      );
    }

    // Create V2 processing jobs for each file
    const jobs: Array<{ id: string; name: string }> = [];
    for (const file of files) {
      // Get file from storage using FileManager
      const fileBuffer = await FileManager.getFileFromDisk(file.file_id);
      
      if (fileBuffer) {
        // Upload file to get storage path for processing
        const uploadRequest = {
          original_name: file.original_name,
          title: `Deal Processing File: ${file.original_name}`,
          description: `Uploaded for deal processing via V2 API`,
          uploaded_by: "deal_processing_api",
          upload_source: "deal_processing_v2",
          access_level: "private" as const,
          metadata: {
            original_file_id: file.file_id,
            processing_timestamp: new Date().toISOString(),
            processor_version: "v2"
          }
        };

        const { filePath } = await FileManager.uploadFile(fileBuffer, uploadRequest);
        
        const jobId = await processorQueueManager.addProcessorJob(
          'deal_processor_v2',
          {
            limit: 1,
            batchSize: 1,
            dealId: parseInt(dealId),
            files: [filePath], // Pass file path directly
            processorVersion: "v2",
            jobMetadata: {
              fileId: file.file_id,
              originalName: file.original_name,
              mimeType: file.mime_type || 'application/pdf',
              processingTimestamp: new Date().toISOString()
            }
          } as any, // Use any to bypass type checking for deal-specific data
          {
            priority: 1,
            delay: 0
          }
        );
        
        jobs.push({ id: jobId, name: `Deal Processing Job ${jobId}` });

        // Note: File processing status tracking would need to be implemented
        // in the File interface and database schema
      }
    }

    return NextResponse.json({ 
      message: `Processing started for ${files.length} files`,
      jobs: jobs.map(job => ({ id: job.id, name: job.name }))
    });
  } catch (error) {
    console.error("Error processing V2 deal files:", error);
    return NextResponse.json(
      { error: "Failed to start processing" },
      { status: 500 }
    );
  }
} 