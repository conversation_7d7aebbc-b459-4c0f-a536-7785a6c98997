import { NextRequest, NextResponse } from "next/server";
import { processorQueueManager } from "@/lib/queue/ProcessorQueueManager";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { withT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/typeorm/middleware";
import { pool } from "@/lib/db";

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
}

async function uploadDealsHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const formData = await request.formData();
    
    // Debug: Log all form data keys
    console.log("🔍 Form data keys received:", Array.from(formData.keys()));
    
    // Extract files from form data - handle both "files" and "file_0", "file_1" patterns
    let files: File[] = [];
    
    // First try the standard "files" field
    const standardFiles = formData.getAll("files") as File[];
    console.log("🔍 Standard files found:", standardFiles?.length || 0);
    
    if (standardFiles && standardFiles.length > 0) {
      files = standardFiles;
    } else {
      // Look for individual file fields like "file_0", "file_1", etc.
      let fileIndex = 0;
      while (true) {
        const file = formData.get(`file_${fileIndex}`) as File;
        if (!file) break;
        console.log(`🔍 Found file_${fileIndex}:`, file.name, file.type);
        files.push(file);
        fileIndex++;
      }
    }
    
    console.log("🔍 Total files extracted:", files.length);
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: "No files provided" },
        { status: 400 }
      );
    }

    // Extract contact information from form data
    let contactIds: number[] = [];
    let contactEmails: string[] = [];
    
    // Extract contact_emails (multi-select) if provided
    const contactEmailsForm = formData.get("contact_emails");
    if (contactEmailsForm && typeof contactEmailsForm === "string") {
      try {
        contactEmails = JSON.parse(contactEmailsForm);
        console.log(`📧 Processing contact emails:`, contactEmails);
      } catch (error) {
        console.error("Error parsing contact_emails:", error);
      }
    }

    // Look up contacts by emails if provided
    if (contactEmails.length > 0) {
      try {
        console.log(`🔍 Looking up ${contactEmails.length} contacts by email`);
        
        // Look up all contacts by their emails
        for (const email of contactEmails) {
          console.log(`🔎 Searching for contact with email: ${email}`);
          const contactResult = await pool.query(
            "SELECT contact_id FROM public.contacts WHERE email = $1 OR personal_email = $1 LIMIT 1",
            [email]
          );

          if (contactResult.rows.length > 0) {
            const foundContactId = contactResult.rows[0].contact_id;
            contactIds.push(foundContactId);
            console.log(`✅ Found contact ${foundContactId} for email ${email}`);
          } else {
            console.warn(`⚠️ Contact with email ${email} not found in contacts table`);
          }
        }
        
        console.log(`📋 Resolved contact IDs:`, contactIds);
      } catch (error) {
        console.error("❌ Error looking up contacts by email:", error);
        return NextResponse.json(
          { error: "Failed to look up contacts by email" },
          { status: 500, headers: corsHeaders }
        );
      }
    }

    // Upload files using FileManager first
    const { FileManager } = await import("@/lib/utils/fileManager");
    const uploadedFiles: Array<{
      filePath: string;
      fileName: string;
      fileId: string;
      mimeType: string;
    }> = [];
    
    for (const file of files) {
      const fileBuffer = Buffer.from(await file.arrayBuffer());
      
      const uploadRequest = {
        original_name: file.name,
        title: `Deal Processing File: ${file.name}`,
        description: `Uploaded for deal processing via V2 API`,
        uploaded_by: "upload_ui",
        upload_source: "deal_processing_v2",
        access_level: "private" as const,
        metadata: {
          upload_timestamp: new Date().toISOString(),
          processor_version: "v2",
          file_type: file.type
        }
      };

      // Upload file with deduplication enabled (default behavior)
      const { file: uploadedFile, filePath, isDuplicate } = await FileManager.uploadFile(
        fileBuffer, 
        uploadRequest,
        { allow_duplicates: false } // Explicitly disable duplicates
      );
      
      console.log(`📁 File upload result for "${file.name}":`, {
        fileId: uploadedFile.file_id,
        fileName: uploadedFile.file_name,
        originalName: uploadedFile.original_name,
        isDuplicate: isDuplicate,
        contentHash: uploadedFile.content_hash,
        message: isDuplicate ? "✅ Reused existing file" : "🆕 Created new file"
      });
      
      uploadedFiles.push({
        filePath: filePath,
        fileName: file.name,
        fileId: uploadedFile.file_id,
        mimeType: file.type
      });
    }

    // Create job data for V2 processing with file IDs
    const jobData = {
      fileIds: uploadedFiles.map(file => file.fileId), // Pass file IDs instead of paths
      processorVersion: "v2",
      llmModel: "gemini-flash" as const,
      createdBy: "upload_ui",
      contactIds: contactIds.length > 0 ? contactIds : undefined,
      contactEmails: contactEmails.length > 0 ? contactEmails : undefined,
      jobMetadata: {
        uploadTimestamp: new Date().toISOString(),
        originalFileNames: files.map(f => f.name),
        fileCount: files.length,
        processorVersion: "v2",
        uploadedFileIds: uploadedFiles.map(f => f.fileId)
      }
    };

    console.log("📦 Created V2 job data:", {
      fileCount: jobData.fileIds.length,
      processorVersion: jobData.processorVersion,
      llmModel: jobData.llmModel
    });

    // Add V2 job to queue using the new ProcessorQueueManager
    const jobId = await processorQueueManager.addProcessorJob(
      'deal_processor_v2',
      {
        limit: 1,
        batchSize: 1,
        fileIds: jobData.fileIds,
        processorVersion: jobData.processorVersion,
        llmModel: jobData.llmModel,
        contactIds: jobData.contactIds,
        contactEmails: jobData.contactEmails,
        jobMetadata: jobData.jobMetadata
      } as any, // Use any to bypass type checking for deal-specific data
      {
        priority: 1,
        delay: 0
      }
    );

    // Return success response with job ID
    return NextResponse.json({
      success: true,
      jobId,
      message: "Files uploaded successfully and V2 processing started",
      fileCount: files.length,
      fileNames: files.map(f => f.name),
      processorVersion: "v2",
      estimatedProcessingTime: files.length * 30 // seconds
    }, { headers: corsHeaders });

  } catch (error) {
    console.error("Error uploading deals v2:", error);
    return NextResponse.json(
      { 
        error: "Failed to upload deals",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500, headers: corsHeaders }
    );
  }
}

// Handle preflight OPTIONS requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  })
}

// Export the handler wrapped with TypeORM middleware
export const POST = withTypeORMHandler(uploadDealsHandler); 