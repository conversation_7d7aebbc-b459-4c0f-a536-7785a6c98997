import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from "@/lib/typeorm/service";
import { withTypeOR<PERSON><PERSON>and<PERSON> } from "@/lib/typeorm/middleware";

async function getDealsQualityHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const dealId = searchParams.get("dealId");

    // Get repositories from the service
    const dealsRepository = typeORMService.getDealsRepository();
    const nsfRepository = typeORMService.getNsfRepository();
    const propertyRepository = typeORMService.getPropertyRepository();

    if (dealId) {
      // Get quality metrics for a specific deal
      const deal = await dealsRepository.findOne({
        where: { dealId: parseInt(dealId) },
        relations: ['property', 'property.owner']
      });

      if (!deal) {
        return NextResponse.json(
          { error: "Deal not found" },
          { status: 404 }
        );
      }

      const qualityMetrics = await calculateDetailedQualityMetrics(deal, nsfRepository);

      return NextResponse.json({
        success: true,
        dealId: parseInt(dealId),
        qualityMetrics
      });
    } else {
      // Get overall quality metrics for all deals
      const deals = await dealsRepository.find({
        relations: ['property', 'property.owner']
      });

      const overallMetrics = await calculateOverallQualityMetrics(deals, nsfRepository);

      return NextResponse.json({
        success: true,
        overallMetrics
      });
    }

  } catch (error) {
    console.error("Error getting deals quality v2:", error);
    return NextResponse.json(
      { 
        error: "Failed to get deals quality",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

async function calculateDetailedQualityMetrics(deal: DealsV2, nsfRepository: any) {
  // Import and use the comprehensive quality calculator for consistency
  const { calculateComprehensiveQuality } = await import('@/lib/utils/comprehensiveQualityCalculator');
  
  // Ensure deal has the required structure for comprehensive calculator
  const dealWithNsfFields = {
    ...deal,
    nsfFields: deal.nsfFields || deal.nsf_fields || [],
  };
  
  const comprehensiveMetrics = calculateComprehensiveQuality(dealWithNsfFields);
  
  // Map comprehensive metrics to the expected category structure for backwards compatibility
  const categoryScores = {
    basic: {
      score: comprehensiveMetrics.overview.qualityScore,
      completed: comprehensiveMetrics.overview.completedFields,
      total: comprehensiveMetrics.overview.totalFields,
      missing: comprehensiveMetrics.overview.missingFields
    },
    financial: {
      score: comprehensiveMetrics.financial.qualityScore,
      completed: comprehensiveMetrics.financial.completedFields,
      total: comprehensiveMetrics.financial.totalFields,
      missing: comprehensiveMetrics.financial.missingFields
    },
    units: {
      score: comprehensiveMetrics.units.qualityScore,
      completed: comprehensiveMetrics.units.completedFields,
      total: comprehensiveMetrics.units.totalFields,
      missing: comprehensiveMetrics.units.missingFields
    },
    nsf: {
      score: comprehensiveMetrics.nsf.qualityScore,
      completed: comprehensiveMetrics.nsf.completedFields,
      total: comprehensiveMetrics.nsf.totalFields,
      missing: comprehensiveMetrics.nsf.missingFields
    },
    property: {
      score: comprehensiveMetrics.property.qualityScore,
      completed: comprehensiveMetrics.property.completedFields,
      total: comprehensiveMetrics.property.totalFields,
      missing: comprehensiveMetrics.property.missingFields
    },
    debt: {
      score: comprehensiveMetrics.debt.qualityScore,
      completed: comprehensiveMetrics.debt.completedFields,
      total: comprehensiveMetrics.debt.totalFields,
      missing: comprehensiveMetrics.debt.missingFields
    },
    equity: {
      score: comprehensiveMetrics.equity.qualityScore,
      completed: comprehensiveMetrics.equity.completedFields,
      total: comprehensiveMetrics.equity.totalFields,
      missing: comprehensiveMetrics.equity.missingFields
    }
  };

  // Calculate overall score using the same logic as detail page
  const sections = [
    comprehensiveMetrics.overview,
    comprehensiveMetrics.debt,
    comprehensiveMetrics.equity,
    comprehensiveMetrics.nsf,
    comprehensiveMetrics.property,
    comprehensiveMetrics.financial
  ].filter(section => section.totalFields > 0);
  
  const totalCompletedFields = sections.reduce((sum, section) => sum + section.completedFields, 0);
  const totalPossibleFields = sections.reduce((sum, section) => sum + section.totalFields, 0);
  const overallScore = totalPossibleFields > 0 ? Math.round((totalCompletedFields / totalPossibleFields) * 100) : 0;

  return {
    overallScore,
    categoryScores,
    nsfFieldsCount: comprehensiveMetrics.nsf.criteriaCount,
    hasProperty: !!deal.property,
    recommendations: generateRecommendations(categoryScores, overallScore)
  };
}

async function calculateOverallQualityMetrics(deals: DealsV2[], nsfRepository: any) {
  const totalDeals = deals.length;
  let totalScore = 0;
  const categoryAverages: any = {};
  const fieldCompleteness: any = {};

  // Initialize field tracking
  const allFields = [
    'dealName', 'dealStage', 'dealStatus', 'strategy', 'priority',
    'yieldOnCost', 'commonEquityInternalRateOfReturnIrr', 'commonEquityEquityMultiple',
    'residentialNsfNetSquareFoot', 'retailNsfNetSquareFoot', 'officeNsfNetSquareFoot',
    'totalNsfNetSquareFoot', 'loanAmount', 'interestRate', 'loanTerm'
  ];

  allFields.forEach(field => {
    fieldCompleteness[field] = { completed: 0, total: totalDeals };
  });

  for (const deal of deals) {
    const metrics = await calculateDetailedQualityMetrics(deal, nsfRepository);
    totalScore += metrics.overallScore;

    // Track field completeness
    allFields.forEach(field => {
      const value = (deal as any)[field];
      if (value !== null && value !== '' && value !== undefined) {
        fieldCompleteness[field].completed++;
      }
    });
  }

  const averageScore = Math.round(totalScore / totalDeals);

  // Calculate field completion percentages
  Object.keys(fieldCompleteness).forEach(field => {
    const { completed, total } = fieldCompleteness[field];
    fieldCompleteness[field].percentage = Math.round((completed / total) * 100);
  });

  return {
    totalDeals,
    averageScore,
    fieldCompleteness,
    qualityDistribution: {
      high: deals.filter(d => d.reviewStatus === 'approved').length,
      medium: deals.filter(d => d.reviewStatus === 'reviewed').length,
      low: deals.filter(d => d.reviewStatus === 'pending').length
    }
  };
}

function generateRecommendations(categoryScores: any, overallScore: number): string[] {
  const recommendations: string[] = [];

  if (overallScore < 50) {
    recommendations.push("Overall data quality is low. Focus on completing basic deal information first.");
  }

  Object.entries(categoryScores).forEach(([category, data]: [string, any]) => {
    if (data.score < 30) {
      recommendations.push(`${category.charAt(0).toUpperCase() + category.slice(1)} data is severely incomplete. Prioritize this category.`);
    } else if (data.score < 60) {
      recommendations.push(`${category.charAt(0).toUpperCase() + category.slice(1)} data needs improvement. Consider adding more ${category} fields.`);
    }
  });

  if (recommendations.length === 0) {
    recommendations.push("Data quality is good across all categories. Consider adding more detailed financial metrics.");
  }

  return recommendations;
}

// Export the handler wrapped with TypeORM middleware
export const GET = withTypeORMHandler(getDealsQualityHandler); 