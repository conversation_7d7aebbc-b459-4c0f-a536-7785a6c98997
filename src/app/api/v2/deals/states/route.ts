import { NextRequest, NextResponse } from "next/server";
import { withT<PERSON><PERSON><PERSON><PERSON>and<PERSON> } from "@/lib/typeorm/middleware";
import { typeORMService } from "@/lib/typeorm/service";

async function getStatesHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const query = `
      SELECT DISTINCT value_2 as value, value_2 as label
      FROM central_mapping 
      WHERE type = 'U.S Regions' 
      AND is_active = true 
      AND value_2 IS NOT NULL 
      AND value_2 != ''
      ORDER BY value_2 ASC
    `;
    
    const results = await typeORMService.getDataSource().query(query);
    
    // Normalize and deduplicate state names
    const stateMap = new Map<string, string>();
    const normalizationMap: { [key: string]: string } = {
      'NewHampshire': 'New Hampshire',
      'NewJersey': 'New Jersey',
      'NewYork': 'New York',
      'NorthDakota': 'North Dakota',
      'RhodeIsland': 'Rhode Island',
      'SouthDakota': 'South Dakota',
      'WashingtonDC': 'Washington D.C.',
      'WestVirginia': 'West Virginia'
    };
    
    results.forEach((item: any) => {
      let state = item.value;
      
      // Apply normalization mapping
      state = normalizationMap[state] || state;
      
      // Skip entries with extra text or invalid states
      if (state.includes('(entire state is included)') || 
          state.length > 50) {
        return;
      }
      
      // Normalize whitespace and trim
      state = state.replace(/\s+/g, ' ').trim();
      
      // Create a normalized key for deduplication
      const stateKey = state.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ');
      
      if (!stateMap.has(stateKey)) {
        stateMap.set(stateKey, state);
      }
    });
    
    const deduplicatedStates = Array.from(stateMap.values())
      .map((state: string) => ({
        value: state,
        label: state
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
    
    return NextResponse.json(deduplicatedStates);
  } catch (error) {
    console.error("Error fetching states:", error);
    return NextResponse.json(
      { error: "Failed to fetch states" },
      { status: 500 }
    );
  }
}

export const GET = withTypeORMHandler(getStatesHandler);
