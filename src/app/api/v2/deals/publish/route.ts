import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { dealId, action } = body

    if (!dealId || !action) {
      return NextResponse.json(
        { success: false, error: 'dealId and action are required' },
        { status: 400 }
      )
    }

    if (action !== 'publish' && action !== 'unpublish') {
      return NextResponse.json(
        { success: false, error: 'action must be either "publish" or "unpublish"' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')

      if (action === 'publish') {
        // Check if deal exists
        const dealCheck = await client.query(
          'SELECT deal_id, published FROM dealsv2 WHERE deal_id = $1',
          [dealId]
        )

        if (dealCheck.rows.length === 0) {
          await client.query('ROLLBACK')
          return NextResponse.json(
            { success: false, error: 'Deal not found' },
            { status: 404 }
          )
        }

        if (dealCheck.rows[0].published) {
          await client.query('ROLLBACK')
          return NextResponse.json(
            { success: false, error: 'Deal is already published' },
            { status: 400 }
          )
        }

        // Count current published deals
        const publishedCount = await client.query(
          'SELECT COUNT(*) as count FROM dealsv2 WHERE published = true'
        )
        const currentPublishedCount = parseInt(publishedCount.rows[0].count)

        let message = 'Deal published successfully'

        // If we have 10 or more published deals, unpublish the oldest one
        if (currentPublishedCount >= 10) {
          const oldestPublished = await client.query(
            'SELECT deal_id FROM dealsv2 WHERE published = true ORDER BY published_time ASC LIMIT 1'
          )
          
          if (oldestPublished.rows.length > 0) {
            const oldestDealId = oldestPublished.rows[0].deal_id
            
            // Unpublish the oldest deal
            await client.query(
              'UPDATE dealsv2 SET published = false, published_time = NULL WHERE deal_id = $1',
              [oldestDealId]
            )
            
            message = `Deal published successfully. Automatically unpublished the oldest published deal (ID: ${oldestDealId}) to maintain the 10 deal limit.`
          }
        }

        // Publish the new deal
        await client.query(
          'UPDATE dealsv2 SET published = true, published_time = CURRENT_TIMESTAMP WHERE deal_id = $1',
          [dealId]
        )

        await client.query('COMMIT')

        return NextResponse.json({
          success: true,
          message,
          data: {
            dealId,
            published: true,
            publishedTime: new Date().toISOString()
          }
        })

      } else if (action === 'unpublish') {
        // Unpublish the deal
        const result = await client.query(
          'UPDATE dealsv2 SET published = false, published_time = NULL WHERE deal_id = $1 RETURNING deal_id',
          [dealId]
        )

        if (result.rows.length === 0) {
          await client.query('ROLLBACK')
          return NextResponse.json(
            { success: false, error: 'Deal not found' },
            { status: 404 }
          )
        }

        await client.query('COMMIT')

        return NextResponse.json({
          success: true,
          message: 'Deal unpublished successfully',
          data: {
            dealId,
            published: false,
            publishedTime: null
          }
        })
      }

    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error updating deal publish status:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update deal publish status' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const page = parseInt(searchParams.get('page') || '1')
    const offset = (page - 1) * limit

    // Get published deals with pagination
    const query = `
      SELECT 
        deal_id,
        deal_name,
        summary,
        deal_type,
        deal_stage,
        deal_status,
        published,
        published_time,
        created_at,
        updated_at
      FROM dealsv2 
      WHERE published = true
      ORDER BY published_time DESC 
      LIMIT $1 OFFSET $2
    `
    
    const countQuery = `SELECT COUNT(*) as total FROM dealsv2 WHERE published = true`
    
    const [result, countResult] = await Promise.all([
      pool.query(query, [limit, offset]),
      pool.query(countQuery)
    ])
    
    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
    
  } catch (error) {
    console.error('Error fetching published deals:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch published deals' 
      },
      { status: 500 }
    )
  }
}
