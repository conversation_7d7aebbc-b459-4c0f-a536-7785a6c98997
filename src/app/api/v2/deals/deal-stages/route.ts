import { NextResponse } from "next/server";
import { TypeORMService } from "@/lib/typeorm/service";

export async function GET() {
  try {
    const typeORMService = TypeORMService.getInstance();
    await typeORMService.initialize();
    const dealsRepository = typeORMService.getDealsRepository();

    // Get unique deal stages from dealsv2 table
    const dealStages = await dealsRepository
      .createQueryBuilder("deal")
      .select("DISTINCT deal.dealStage", "dealStage")
      .where("deal.dealStage IS NOT NULL")
      .andWhere("deal.dealStage != ''")
      .orderBy("deal.dealStage", "ASC")
      .getRawMany();

    // Format the response and remove duplicates
    const uniqueStages = new Set<string>();
    const formattedStages = dealStages
      .map((item: any) => item.dealStage)
      .filter((stage: string) => {
        if (uniqueStages.has(stage.toLowerCase())) {
          return false;
        }
        uniqueStages.add(stage.toLowerCase());
        return true;
      })
      .map((stage: string) => ({
        value: stage,
        label: stage
      }))
      .sort((a, b) => a.label.localeCompare(b.label));

    return NextResponse.json(formattedStages);
  } catch (error) {
    console.error("Error fetching deal stages:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
