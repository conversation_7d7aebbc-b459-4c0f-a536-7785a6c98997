import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { csvData, clearExisting = false } = body;

    if (!csvData || !Array.isArray(csvData) || csvData.length === 0) {
      return NextResponse.json(
        { success: false, error: "Invalid CSV data provided" },
        { status: 400 }
      );
    }

    // Start transaction
    const client = await pool.connect();

    try {
      await client.query("BEGIN");

      // Clear existing data if requested
      if (clearExisting) {
        await client.query("DELETE FROM central_mapping");
      }

      // Process CSV headers to understand the structure
      const headers = csvData[0];
      const rows = csvData.slice(1);

      let insertedCount = 0;
      let skippedCount = 0;
      const errors: string[] = [];

      console.log("CSV Headers:", headers);
      console.log("Sample row:", rows[0]);

      // Process rows in batches for better performance
      const BATCH_SIZE = 50;
      const validRows = [];

      // First, validate and prepare all rows
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        
        if (row.length < 3) {
          skippedCount++;
          continue;
        }

        const type = row[0]?.trim() || '';
        const level_1 = row[1]?.trim() || '';
        const value_1 = row[2]?.trim() || '';
        const level_2 = row[3]?.trim() || '';
        const value_2 = row[4]?.trim() || '';
        const level_3 = row[5]?.trim() || '';
        const value_3 = row[6]?.trim() || '';

        // Skip if no primary values
        if (!type || !level_1 || !value_1) {
          skippedCount++;
          continue;
        }

        validRows.push({
          type,
          level_1,
          value_1,
          level_2: level_2 || null,
          value_2: value_2 || null,
          level_3: level_3 || null,
          value_3: value_3 || null,
          is_active: true
        });
      }

      console.log(`Processing ${validRows.length} valid rows in batches of ${BATCH_SIZE}`);

      // Process in batches using batch insert
      for (let i = 0; i < validRows.length; i += BATCH_SIZE) {
        const batch = validRows.slice(i, i + BATCH_SIZE);
        const batchNum = Math.floor(i / BATCH_SIZE) + 1;
        const totalBatches = Math.ceil(validRows.length / BATCH_SIZE);
        
        console.log(`Processing batch ${batchNum}/${totalBatches} (${batch.length} rows)`);
        
        try {
          // Build bulk insert query
          const placeholders: string[] = [];
          const values: any[] = [];
          
          batch.forEach((row, idx) => {
            const baseIndex = idx * 8;
            placeholders.push(`($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6}, $${baseIndex + 7}, $${baseIndex + 8})`);
            values.push(row.type, row.level_1, row.value_1, row.level_2, row.value_2, row.level_3, row.value_3, row.is_active);
          });

          const batchInsertQuery = `
            INSERT INTO central_mapping (type, level_1, value_1, level_2, value_2, level_3, value_3, is_active)
            VALUES ${placeholders.join(', ')}
            ON CONFLICT ON CONSTRAINT unique_central_mapping DO NOTHING
            RETURNING id
          `;

          const result = await client.query(batchInsertQuery, values);
          const batchInserted = result.rowCount || 0;
          const batchSkipped = batch.length - batchInserted;
          
          insertedCount += batchInserted;
          skippedCount += batchSkipped;
          
          console.log(`Batch ${batchNum} completed: ${batchInserted} inserted, ${batchSkipped} skipped`);
          
        } catch (error) {
          console.error(`Error processing batch ${batchNum}:`, error);
          // If batch fails, try individual inserts as fallback
          for (const row of batch) {
            try {
              const fallbackQuery = `
                INSERT INTO central_mapping (type, level_1, value_1, level_2, value_2, level_3, value_3, is_active)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT ON CONSTRAINT unique_central_mapping DO NOTHING
                RETURNING id
              `;
              
              const fallbackResult = await client.query(fallbackQuery, [
                row.type, row.level_1, row.value_1, row.level_2, row.value_2, row.level_3, row.value_3, row.is_active
              ]);
              
              if (fallbackResult.rowCount && fallbackResult.rowCount > 0) {
                insertedCount++;
              } else {
                skippedCount++;
              }
            } catch (individualError) {
              console.error(`Error processing individual row:`, individualError);
              skippedCount++;
            }
          }
        }
      }

      await client.query("COMMIT");

      return NextResponse.json({
        success: true,
        message: "CSV data uploaded successfully",
        stats: {
          totalRows: rows.length,
          insertedCount,
          skippedCount,
          errors: errors.length > 0 ? errors : undefined,
        },
      });
    } catch (error) {
      await client.query("ROLLBACK");
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error("Error uploading CSV data:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to upload CSV data",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// Alternative endpoint for structured CSV upload with proper mapping objects
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { mappings, clearExisting = false } = body;

    if (!mappings || !Array.isArray(mappings)) {
      return NextResponse.json(
        { success: false, error: "Invalid mappings data provided" },
        { status: 400 }
      );
    }

    const client = await pool.connect();

    try {
      await client.query("BEGIN");

      if (clearExisting) {
        await client.query("DELETE FROM central_mapping");
      }

      let insertedCount = 0;
      let skippedCount = 0;
      const errors: string[] = [];

      // Validate and prepare mappings
      const validMappings = [];
      for (const mapping of mappings) {
        const { type, level_1, value_1, level_2, value_2, level_3, value_3, is_active = true } = mapping;
        
        // Trim all string fields to remove leading/trailing whitespace
        const trimmedType = type?.trim();
        const trimmedLevel1 = level_1?.trim();
        const trimmedValue1 = value_1?.trim();
        const trimmedLevel2 = level_2?.trim() || null;
        const trimmedValue2 = value_2?.trim() || null;
        const trimmedLevel3 = level_3?.trim() || null;
        const trimmedValue3 = value_3?.trim() || null;
        
        if (!trimmedType || !trimmedLevel1 || !trimmedValue1) {
          skippedCount++;
          continue;
        }

        validMappings.push({
          type: trimmedType,
          level_1: trimmedLevel1,
          value_1: trimmedValue1,
          level_2: trimmedLevel2,
          value_2: trimmedValue2,
          level_3: trimmedLevel3,
          value_3: trimmedValue3,
          is_active
        });
      }

      console.log(`Processing ${validMappings.length} valid mappings in batches`);

      // Process in batches for better performance  
      const BATCH_SIZE = 50;
      for (let i = 0; i < validMappings.length; i += BATCH_SIZE) {
        const batch = validMappings.slice(i, i + BATCH_SIZE);
        const batchNum = Math.floor(i / BATCH_SIZE) + 1;
        const totalBatches = Math.ceil(validMappings.length / BATCH_SIZE);
        
        console.log(`Processing batch ${batchNum}/${totalBatches} (${batch.length} mappings)`);
        
        try {
          // Build bulk insert query
          const placeholders: string[] = [];
          const values: any[] = [];
          
          batch.forEach((mapping, idx) => {
            const baseIndex = idx * 8;
            placeholders.push(`($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6}, $${baseIndex + 7}, $${baseIndex + 8})`);
            values.push(mapping.type, mapping.level_1, mapping.value_1, mapping.level_2, mapping.value_2, mapping.level_3, mapping.value_3, mapping.is_active);
          });

          const batchInsertQuery = `
            INSERT INTO central_mapping (type, level_1, value_1, level_2, value_2, level_3, value_3, is_active)
            VALUES ${placeholders.join(', ')}
            ON CONFLICT ON CONSTRAINT unique_central_mapping DO NOTHING
            RETURNING id
          `;  

          const result = await client.query(batchInsertQuery, values);
          const batchInserted = result.rowCount || 0;
          const batchSkipped = batch.length - batchInserted;
          
          insertedCount += batchInserted;
          skippedCount += batchSkipped;
          
          console.log(`Batch ${batchNum} completed: ${batchInserted} inserted, ${batchSkipped} skipped`);
            
        } catch (error) {
          console.error(`Error processing batch ${batchNum}:`, error);
          // If batch fails, try individual inserts as fallback
          for (const mapping of batch) {
            try {
              const fallbackQuery = `
                INSERT INTO central_mapping (type, level_1, value_1, level_2, value_2, level_3, value_3, is_active)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT ON CONSTRAINT unique_central_mapping DO NOTHING
                RETURNING id
              `;
              
              const fallbackResult = await client.query(fallbackQuery, [
                mapping.type, mapping.level_1, mapping.value_1, mapping.level_2, mapping.value_2, mapping.level_3, mapping.value_3, mapping.is_active
              ]);
              
              if (fallbackResult.rowCount && fallbackResult.rowCount > 0) {
                insertedCount++;
              } else {
                skippedCount++;
              }
            } catch (individualError) {
              console.error(`Error processing individual mapping:`, individualError);
              skippedCount++;
            }
          }
        }
      }

      await client.query("COMMIT");

      return NextResponse.json({
        success: true,
        message: "Mappings uploaded successfully",
        stats: {
          totalMappings: mappings.length,
          insertedCount,
          skippedCount,
          errors: errors.length > 0 ? errors : undefined,
        },
      });
    } catch (error) {
      await client.query("ROLLBACK");
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error("Error uploading mappings:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to upload mappings",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
