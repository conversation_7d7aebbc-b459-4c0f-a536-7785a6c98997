import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

// Helper to validate if the table is actually a mapping table
const isValidMappingTable = async (tableName: string): Promise<boolean> => {
  const query = `
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = $1
    AND table_name LIKE '%mapping%'
  `
  const result = await pool.query(query, [tableName])
  return result.rows.length > 0
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tableName: string }> }
) {
  const { tableName } = await params

  try {
    // Validate that this is a real mapping table to prevent SQL injection
    if (!await isValidMappingTable(tableName)) {
      return NextResponse.json(
        { error: 'Invalid table name' },
        { status: 400 }
      )
    }

    // Fetch all rows from the table
    const query = `SELECT * FROM ${tableName} ORDER BY 1`
    const result = await pool.query(query)
    
    return NextResponse.json({ rows: result.rows }, { status: 200 })
  } catch (error) {
    console.error(`Error fetching data from ${tableName}:`, error)
    return NextResponse.json(
      { error: `Failed to fetch data from ${tableName}` },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ tableName: string }> }
) {
  const { tableName } = await params

  try {
    const body = await request.json()
    const { data } = body

    if (!data || !Array.isArray(data)) {
      return NextResponse.json(
        { error: 'Invalid data format' },
        { status: 400 }
      )
    }

    // Validate that this is a real mapping table to prevent SQL injection
    if (!await isValidMappingTable(tableName)) {
      return NextResponse.json(
        { error: 'Invalid table name' },
        { status: 400 }
      )
    }

    // Start a transaction
    const client = await pool.connect()
    try {
      await client.query('BEGIN')
      
      // Get the primary key column(s)
      const pkQuery = `
        SELECT a.attname
        FROM pg_index i
        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
        WHERE i.indrelid = $1::regclass
        AND i.indisprimary
      `
      const pkResult = await client.query(pkQuery, [tableName])
      
      if (pkResult.rows.length === 0) {
        throw new Error('Table has no primary key')
      }
      
      const primaryKeys = pkResult.rows.map(row => row.attname)
      
      // Process each row
      for (const row of data) {
        // Trim string values to remove leading/trailing whitespace
        const trimmedRow = {};
        for (const [key, value] of Object.entries(row)) {
          trimmedRow[key] = typeof value === 'string' ? value.trim() : value;
        }
        
        // Build the SET clause for updateable columns
        const columns = Object.keys(trimmedRow).filter(col => !primaryKeys.includes(col))
        const setClause = columns.map((col, idx) => `${col} = $${idx + primaryKeys.length + 1}`).join(', ')
        
        // Build the WHERE clause for primary key(s)
        const whereClause = primaryKeys.map((pk, idx) => `${pk} = $${idx + 1}`).join(' AND ')
        
        // Build the values array
        const values = [
          ...primaryKeys.map(pk => trimmedRow[pk]),
          ...columns.map(col => trimmedRow[col])
        ]
        
        // Execute update
        const updateQuery = `
          UPDATE ${tableName}
          SET ${setClause}
          WHERE ${whereClause}
        `
        await client.query(updateQuery, values)
      }
      
      await client.query('COMMIT')
      return NextResponse.json({ success: true }, { status: 200 })
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    console.error(`Error updating data in ${tableName}:`, error)
    return NextResponse.json(
      { error: `Failed to update data in ${tableName}` },
      { status: 500 }
    )
  }
} 