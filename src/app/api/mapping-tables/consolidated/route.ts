import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

// Consolidated mapping API: returns structured mappings from central_mapping table
// Output shape:
// {
//   property_type_map: { [value_1: string]: string[] },
//   capital_position: string[],
//   loan_program: string[],
//   loan_type: string[],
//   recourse_loan: string[],
//   strategies: string[],
//   structured_loan_tranches: string[],
//   us_regions: { [region: string]: string[] },
//   us_states: string[]
// }

export async function GET(_req: NextRequest) {
  const client = await pool.connect()
  try {
    // Fetch all active rows once
    const sql = `
      SELECT type, level_1, value_1, level_2, value_2, level_3, value_3
      FROM central_mapping
      WHERE is_active = true
    `
    const res = await client.query(sql)

    // Helpers
    const uniqPush = (arr: string[], v: any) => {
      if (typeof v === 'string') {
        const s = v.trim()
        if (s && !arr.includes(s)) arr.push(s)
      }
    }

    const propertyTypeMap: Record<string, string[]> = {}
    const capitalPosition: string[] = []
    const loanProgram: string[] = []
    const loanType: string[] = []
    const recourseLoan: string[] = []
    const strategies: string[] = []
    const structuredLoanTranches: string[] = []
    const usRegions: Record<string, string[]> = {}
    const usStates: string[] = []

    for (const row of res.rows) {
      const type: string = row.type
      const v1: string | null = row.value_1
      const v2: string | null = row.value_2
      // Normalize type keys to expected names
      switch (type) {
        case 'Property Type': {
          if (v1) {
            if (!propertyTypeMap[v1]) propertyTypeMap[v1] = []
            if (v2) uniqPush(propertyTypeMap[v1], v2)
          }
          break
        }
        case 'Capital Position': {
          if (v1) uniqPush(capitalPosition, v1)
          break
        }
        case 'Loan Program': {
          if (v1) uniqPush(loanProgram, v1)
          break
        }
        case 'Loan Type': {
          if (v1) uniqPush(loanType, v1)
          break
        }
        case 'Recourse Loan': {
          if (v1) uniqPush(recourseLoan, v1)
          break
        }
        case 'Strategies': {
          // Distinct value_2 per request
          if (v2) uniqPush(strategies, v2)
          break
        }
        case 'Structured Loan Tranches': {
          if (v1) uniqPush(structuredLoanTranches, v1)
          break
        }
        case 'U.S Regions': {
          const region = v1
          const state = v2
          if (region) {
            if (!usRegions[region]) usRegions[region] = []
            if (state) uniqPush(usRegions[region], state)
          }
          break
        }
        case 'U.S States': {
          if (v2) uniqPush(usStates, v2)
          break
        }
        default:
          break
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        property_type_map: propertyTypeMap,
        capital_position: capitalPosition,
        loan_program: loanProgram,
        loan_type: loanType,
        recourse_loan: recourseLoan,
        strategies,
        structured_loan_tranches: structuredLoanTranches,
        us_regions: usRegions,
        us_states: usStates
      }
    })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: String(e?.message || e) }, { status: 500 })
  } finally {
    client.release()
  }
}


