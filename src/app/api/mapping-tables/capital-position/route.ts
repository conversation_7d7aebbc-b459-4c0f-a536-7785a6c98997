import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Get capital position mappings
      const capitalPositionResult = await client.query(`
        SELECT *
        FROM central_mapping 
        WHERE type = 'Capital Position' 
        AND is_active = true
        ORDER BY value_1, value_2
      `);

      // Get strategy mappings for capital positions
      const strategyResult = await client.query(`
        SELECT *
        FROM central_mapping 
        WHERE type = 'Capital Position Strategy' 
        AND is_active = true
        ORDER BY value_1, value_2
      `);

      // Process capital position mappings into Map format
      const capitalPositionMap: Record<string, string[]> = {};
      
      if (capitalPositionResult.rows) {
        capitalPositionResult.rows.forEach((mapping: any) => {
          if (mapping.value_1 && mapping.value_2) {
            const capitalPos = mapping.value_1;
            const loanType = mapping.value_2;
            
            if (!capitalPositionMap[capitalPos]) {
              capitalPositionMap[capitalPos] = [];
            }
            capitalPositionMap[capitalPos].push(loanType);
          }
        });
      }

      // Process strategy mappings into Map format
      const strategyMap: Record<string, string[]> = {};
      
      if (strategyResult.rows) {
        strategyResult.rows.forEach((mapping: any) => {
          if (mapping.value_1 && mapping.value_2) {
            const capitalPos = mapping.value_1;
            const strategy = mapping.value_2;
            
            if (!strategyMap[capitalPos]) {
              strategyMap[capitalPos] = [];
            }
            strategyMap[capitalPos].push(strategy);
          }
        });
      }

      return NextResponse.json({
        success: true,
        data: capitalPositionResult.rows,
        mappings: capitalPositionMap,
        strategy_mappings: strategyMap,
        count: capitalPositionResult.rows.length,
        strategy_count: strategyResult.rows.length
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching capital position mappings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch capital position mappings',
        data: [],
        mappings: {},
        strategy_mappings: {}
      },
      { status: 500 }
    );
  }
} 