import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const level = searchParams.get('level');

    if (type) {
      // Get hierarchical data for a specific type
      const hierarchyData = await getHierarchicalDataForType(type, level);
      return NextResponse.json({
        success: true,
        data: hierarchyData
      });
    } else {
      // Get all types with counts
      const typesQuery = `
        SELECT type, COUNT(*) as count
        FROM central_mapping 
        WHERE is_active = true
        GROUP BY type
        ORDER BY type
      `;
      
      const result = await pool.query(typesQuery);
      
      return NextResponse.json({
        success: true,
        data: result.rows
      });
    }
  } catch (error) {
    console.error('Error fetching mapping types:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch mapping types' },
      { status: 500 }
    );
  }
}

async function getHierarchicalDataForType(type: string, level?: string | null) {
  // Special handling for U.S States - return all unique states from U.S Regions
  if (type === 'U.S States') {
    const statesQuery = `
      SELECT DISTINCT value_2 as state
      FROM central_mapping 
      WHERE type = 'U.S Regions' 
        AND is_active = true 
        AND value_2 IS NOT NULL 
        AND value_2 != ''
        AND value_2 NOT LIKE '%(entire state is included)%'
      ORDER BY value_2
    `;
    
    const result = await pool.query(statesQuery);
    const rawStates = result.rows.map(row => row.state);
    
    // Normalize state names
    const stateMap = new Map<string, string>();
    const normalizationMap: { [key: string]: string } = {
      'NewJersey': 'New Jersey',
      'NewYork': 'New York',
      'NorthDakota': 'North Dakota',
      'RhodeIsland': 'Rhode Island',
      'SouthDakota': 'South Dakota',
      'WashingtonDC': 'Washington D.C.',
      'WestVirginia': 'West Virginia'
    };
    
    rawStates.forEach(state => {
      // Apply normalization mapping
      const normalizedState = normalizationMap[state] || state;
      
      // Skip entries with extra text or invalid states
      if (normalizedState.includes('(entire state is included)') || 
          normalizedState.length > 50) {
        return;
      }
      
      // Normalize whitespace and trim
      const cleanState = normalizedState.replace(/\s+/g, ' ').trim();
      
      // Create a normalized key for deduplication
      const stateKey = cleanState.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ');
      
      if (!stateMap.has(stateKey)) {
        stateMap.set(stateKey, cleanState);
      }
    });
    
    const states = Array.from(stateMap.values()).sort();
    
    return {
      type: 'U.S States',
      levels: ['States'],
      hierarchyRows: states.map((state, index) => ({
        id: index,
        values: [state],
        fullMapping: { value_2: state }
      })),
      nestedData: states.reduce((acc, state) => {
        acc[state] = [];
        return acc;
      }, {} as any)
    };
  }

  // Build query to get all data for the type
  let query = `
    SELECT * FROM central_mapping 
    WHERE type = $1 AND is_active = true
  `;
  const params = [type];
  
  
  // Add level filtering if specified
  if (level) {
    if (level === '1') {
      query += ` AND level_1 IS NOT NULL AND value_1 IS NOT NULL AND value_1 != ''`;
    } else if (level === '2') {
      query += ` AND level_2 IS NOT NULL AND value_2 IS NOT NULL AND value_2 != ''`;
    } else if (level === '3') {
      query += ` AND level_3 IS NOT NULL AND value_3 IS NOT NULL AND value_3 != ''`;
    }
  }
  
  query += ` ORDER BY value_1, value_2, value_3`;
  
  const result = await pool.query(query, params);
  const mappings = result.rows;

  if (mappings.length === 0) {
    return {
      type,
      levels: [],
      hierarchyRows: [],
      nestedData: {}
    };
  }

  // Create nested data structure: {value1: {value2: [value3]}}
  const nestedData: any = {};
  
  mappings.forEach(mapping => {
    let value1 = mapping.value_1?.trim();
    let value2 = mapping.value_2?.trim();
    let value3 = mapping.value_3?.trim();
    
    // Special handling for Strategies: use normalized value (value_2) if available
    if (type === 'Strategies' && value2 && value2 !== '') {
      value1 = value2; // Use normalized value as the main value
      value2 = mapping.value_1?.trim(); // Original value becomes secondary
    }
    
    // Special handling for U.S Regions: value_1 is region, value_2 is state
    if (type === 'U.S Regions') {
      // Keep the structure as is: region -> state
      // This will create proper region-state mapping
      // value1 = region name, value2 = state name
    }
    
    if (value1) {
      if (!nestedData[value1]) {
        nestedData[value1] = {};
      }
      
      if (value2) {
        // For U.S Regions, value_2 is the state name, not a category
        if (type === 'U.S Regions') {
          // For U.S Regions, we want: {region: [state1, state2, ...]}
          // This creates a flat array of states under each region
          if (!nestedData[value1].states) {
            nestedData[value1].states = [];
          }
          if (!nestedData[value1].states.includes(value2)) {
            nestedData[value1].states.push(value2);
          }
        } else {
          // For other types, use the normal hierarchy
          if (!nestedData[value1][value2]) {
            nestedData[value1][value2] = [];
          }
          
          if (value3) {
            if (!nestedData[value1][value2].includes(value3)) {
              nestedData[value1][value2].push(value3);
            }
          }
        }
      }
    }
  });

  // Determine the actual levels used in this type
  const levelSet = new Set();
  mappings.forEach(mapping => {
    if (mapping.level_1 && mapping.level_1.trim()) levelSet.add(mapping.level_1.trim());
    if (mapping.level_2 && mapping.level_2.trim()) levelSet.add(mapping.level_2.trim());
    if (mapping.level_3 && mapping.level_3.trim()) levelSet.add(mapping.level_3.trim());
  });

  // Get unique levels from first mapping to maintain order
  const firstMapping: any = mappings[0];
  const levels: string[] = [];
  if (firstMapping.level_1 && firstMapping.level_1.trim()) levels.push(firstMapping.level_1.trim());
  if (firstMapping.level_2 && firstMapping.level_2.trim()) levels.push(firstMapping.level_2.trim());
  if (firstMapping.level_3 && firstMapping.level_3.trim()) levels.push(firstMapping.level_3.trim());

  // If level is specified, return only that level's unique values
  if (level) {
    const valueMap = new Map();
    mappings.forEach(mapping => {
      let value = '';
      if (level === '1' && mapping.value_1) {
        value = mapping.value_1.trim();
      } else if (level === '2' && mapping.value_2) {
        value = mapping.value_2.trim();
      } else if (level === '3' && mapping.value_3) {
        value = mapping.value_3.trim();
      }
      
      if (value && !valueMap.has(value)) {
        valueMap.set(value, {
          id: mapping.id,
          values: [value],
          fullMapping: mapping
        });
      }
    });
    
    return {
      type,
      levels: [level],
      hierarchyRows: Array.from(valueMap.values()),
      nestedData: level === '1' ? Object.keys(nestedData).reduce((acc, key) => {
        acc[key] = Object.keys(nestedData[key]);
        return acc;
      }, {} as any) : nestedData
    };
  }

  // If we only have one level, create a simple single-column structure
  if (levels.length === 1) {
    // Group by value_1 to get unique entries
    const valueMap = new Map();
    mappings.forEach(mapping => {
      if (mapping.value_1 && mapping.value_1.trim()) {
        const key = mapping.value_1.trim();
        if (!valueMap.has(key)) {
          valueMap.set(key, {
            id: mapping.id,
            values: [mapping.value_1.trim()],
            fullMapping: mapping
          });
        }
      }
    });
    
    return {
      type,
      levels,
      hierarchyRows: Array.from(valueMap.values()),
      nestedData: Object.keys(nestedData).reduce((acc, key) => {
        acc[key] = [];
        return acc;
      }, {} as any)
    };
  }

  // For multiple levels, create hierarchical structure
  const hierarchyMap = new Map();
  
  mappings.forEach(mapping => {
    const values: string[] = [];
    if (mapping.value_1 && mapping.value_1.trim()) values.push(mapping.value_1.trim());
    if (mapping.value_2 && mapping.value_2.trim()) values.push(mapping.value_2.trim());
    if (mapping.value_3 && mapping.value_3.trim()) values.push(mapping.value_3.trim());
    
    // Create a key based on the combination of values
    const key = values.join('|');
    
    if (!hierarchyMap.has(key) && values.length > 0) {
      hierarchyMap.set(key, {
        id: mapping.id,
        values: values,
        fullMapping: mapping
      });
    }
  });

  const hierarchyRows = Array.from(hierarchyMap.values());


  return {
    type,
    levels,
    hierarchyRows,
    nestedData
  };
} 