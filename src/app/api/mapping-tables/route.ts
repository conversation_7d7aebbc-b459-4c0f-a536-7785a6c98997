import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const active = searchParams.get('active');
    const level = searchParams.get('level');  

    let query = 'SELECT * FROM central_mapping';
    const params: any[] = [];
    const conditions: string[] = [];

    if (type) {
      conditions.push(`type = $${params.length + 1}`);
      params.push(type);
    }

    if (active !== null) {
      conditions.push(`is_active = $${params.length + 1}`);
      params.push(active === 'true');
    }

    if (level) {
      // Filter by specific level - return only records where the specified level has a value
      if (level === '1') {
        conditions.push(`level_1 IS NOT NULL AND value_1 IS NOT NULL AND value_1 != ''`);
      } else if (level === '2') {
        conditions.push(`level_2 IS NOT NULL AND value_2 IS NOT NULL AND value_2 != ''`);
      } else if (level === '3') {
        conditions.push(`level_3 IS NOT NULL AND value_3 IS NOT NULL AND value_3 != ''`);
      }
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY type, level_1, value_1, level_2, value_2';

    const result = await pool.query(query, params);

    return NextResponse.json({
      success: true,
      data: result.rows
    });
  } catch (error) {
    console.error('Error fetching mapping data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch mapping data' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, level_1, value_1, level_2, value_2, level_3, value_3, is_active = true } = body;

    // Trim all string fields to remove leading/trailing whitespace
    const trimmedType = type?.trim();
    const trimmedLevel1 = level_1?.trim();
    const trimmedValue1 = value_1?.trim();
    const trimmedLevel2 = level_2?.trim() || null;
    const trimmedValue2 = value_2?.trim() || null;
    const trimmedLevel3 = level_3?.trim() || null;
    const trimmedValue3 = value_3?.trim() || null;

    if (!trimmedType || !trimmedLevel1 || !trimmedValue1) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: type, level_1, value_1' },
        { status: 400 }
      );
    }

    const query = `
      INSERT INTO central_mapping (type, level_1, value_1, level_2, value_2, level_3, value_3, is_active)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const result = await pool.query(query, [trimmedType, trimmedLevel1, trimmedValue1, trimmedLevel2, trimmedValue2, trimmedLevel3, trimmedValue3, is_active]);

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error creating mapping data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create mapping data' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, type, level_1, value_1, level_2, value_2, level_3, value_3, is_active } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Missing required field: id' },
        { status: 400 }
      );
    }

    // Trim all string fields to remove leading/trailing whitespace
    const trimmedType = type?.trim();
    const trimmedLevel1 = level_1?.trim();
    const trimmedValue1 = value_1?.trim();
    const trimmedLevel2 = level_2?.trim() || null;
    const trimmedValue2 = value_2?.trim() || null;
    const trimmedLevel3 = level_3?.trim() || null;
    const trimmedValue3 = value_3?.trim() || null;

    const query = `
      UPDATE central_mapping 
      SET type = $2, level_1 = $3, value_1 = $4, level_2 = $5, value_2 = $6, level_3 = $7, value_3 = $8, is_active = $9, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `;

    const result = await pool.query(query, [id, trimmedType, trimmedLevel1, trimmedValue1, trimmedLevel2, trimmedValue2, trimmedLevel3, trimmedValue3, is_active]);

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Mapping not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating mapping data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update mapping data' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameter: id' },
        { status: 400 }
      );
    }

    const query = 'DELETE FROM central_mapping WHERE id = $1 RETURNING *';
    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Mapping not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error deleting mapping data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete mapping data' },
      { status: 500 }
    );
  }
} 