import { NextRequest, NextResponse } from 'next/server'
import { processorQueueManager } from '@/lib/queue/ProcessorQueueManager'
import { ProcessingStage } from '@/types/processing'
import { getProcessorConfig } from '@/lib/queue/config/processor-registry'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      processorType, 
      options = {}, 
      customConfig = {} 
    } = body

    if (!processorType) {
      return NextResponse.json({
        success: false,
        error: 'processorType is required'
      }, { status: 400 })
    }

    // Get processor config from registry
    const processorConfig = getProcessorConfig(processorType)
    
    if (!processorConfig) {
      return NextResponse.json({
        success: false,
        error: `Invalid processor type: ${processorType}`
      }, { status: 400 })
    }

    // Merge default options with provided options
    const mergedOptions = { ...processorConfig.defaultOptions, ...options }
    
    // Add job to queue
    const jobId = await processorQueueManager.addProcessorJob(
      processorType,
      mergedOptions,
      customConfig
    )

    return NextResponse.json({
      success: true,
      jobId,
      processorType,
      queueType: processorConfig.queueType,
      queueName: processorConfig.queueType === 'CUSTOM' ? processorConfig.customQueueName : processorConfig.queueType,
      maxRuntime: customConfig.maxRuntime || 'auto-calculated',
      message: `Job queued for ${processorType} in ${processorConfig.queueType} queue`
    })

  } catch (error) {
    console.error('[API] Error triggering processor job:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Get available processors and queue info
export async function GET(request: NextRequest) {
  try {
    const stats = await processorQueueManager.getQueueStats()
    
    return NextResponse.json({
      success: true,
      queues: stats,
      availableProcessors: [
        'email_validation',
        'contact_enrichment_v2', 
        'company_overview_v2',
        'company_investment_criteria',
        'contact_investment_criteria',
        'email_generation',
        'smartlead_sync',
        'job_tiering',
        'website_scraping',
        'article_html_fetch',
        'article_enrichment',
        'article_link_fetch'
      ],
      queueTypes: {
        SHORT: { maxRuntime: '1 minute', concurrency: 10 },
        MEDIUM: { maxRuntime: '5 minutes', concurrency: 5 },
        LONG: { maxRuntime: '30+ minutes', concurrency: 2 }
      }
    })

  } catch (error) {
    console.error('[API] Error getting processor info:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
