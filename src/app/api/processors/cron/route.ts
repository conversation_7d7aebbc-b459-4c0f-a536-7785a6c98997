import { NextRequest, NextResponse } from 'next/server'
import { processorQueueManager } from '@/lib/queue/ProcessorQueueManager'
import { getEnabledCronJobs, getCronJobById, CRON_JOBS } from '@/lib/scheduler/config/cron-config'

// GET /api/processors/cron - Get all cron jobs and their status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const category = searchParams.get('category')
    
    if (action === 'status') {
      // Get status of all cron jobs
      let allJobs = CRON_JOBS.map(job => ({
        id: job.id,
        processorType: job.processorType,
        queueType: job.queueType,
        cronExpression: job.cronExpression,
        description: job.description,
        enabled: job.enabled,
        options: job.options,
        category: job.category || 'STANDARD'
      }))
      
      // Filter by category if specified
      if (category) {
        allJobs = allJobs.filter(job => job.category === category)
      }
      
      return NextResponse.json({
        success: true,
        jobs: allJobs,
        total: allJobs.length,
        enabled: allJobs.filter(job => job.enabled).length,
        categories: {
          BACKGROUND_SYNC: allJobs.filter(job => job.category === 'BACKGROUND_SYNC').length,
          STANDARD: allJobs.filter(job => job.category === 'STANDARD').length
        }
      })
    }
    
    // Default: return all cron jobs
    const enabledJobs = getEnabledCronJobs()
    
    return NextResponse.json({
      success: true,
      jobs: enabledJobs,
      total: enabledJobs.length,
      message: 'All enabled cron jobs retrieved'
    })
    
  } catch (error) {
    console.error('[API] Error getting cron jobs:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST /api/processors/cron - Enable a cron job
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { jobId } = body
    
    if (!jobId) {
      return NextResponse.json({
        success: false,
        error: 'jobId is required'
      }, { status: 400 })
    }
    
    // Check if job exists in config
    const jobConfig = getCronJobById(jobId)
    if (!jobConfig) {
      return NextResponse.json({
        success: false,
        error: `Cron job with ID ${jobId} not found`
      }, { status: 404 })
    }
    
    // Check if it's a background sync job (these are always enabled)
    if (jobConfig.category === 'BACKGROUND_SYNC') {
      return NextResponse.json({
        success: false,
        error: `Background sync job ${jobId} is always enabled and cannot be manually toggled`
      }, { status: 400 })
    }
    
    // Enable the cron job
    const scheduledJobId = await processorQueueManager.enableCronJob(jobId)
    
    return NextResponse.json({
      success: true,
      jobId: scheduledJobId,
      message: `Cron job ${jobId} enabled successfully`,
      config: {
        processorType: jobConfig.processorType,
        queueType: jobConfig.queueType,
        cronExpression: jobConfig.cronExpression,
        description: jobConfig.description,
        category: jobConfig.category
      }
    })
    
  } catch (error) {
    console.error('[API] Error enabling cron job:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE /api/processors/cron - Disable a cron job
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const jobId = searchParams.get('jobId')
    
    if (!jobId) {
      return NextResponse.json({
        success: false,
        error: 'jobId query parameter is required'
      }, { status: 400 })
    }
    
    // Check if job exists in config
    const jobConfig = getCronJobById(jobId)
    if (!jobConfig) {
      return NextResponse.json({
        success: false,
        error: `Cron job with ID ${jobId} not found`
      }, { status: 404 })
    }
    
    // Check if it's a background sync job (these cannot be disabled)
    if (jobConfig.category === 'BACKGROUND_SYNC') {
      return NextResponse.json({
        success: false,
        error: `Background sync job ${jobId} cannot be disabled - it's a critical system process`
      }, { status: 400 })
    }
    
    // Disable the cron job
    await processorQueueManager.disableCronJob(jobId)
    
    return NextResponse.json({
      success: true,
      message: `Cron job ${jobId} disabled successfully`,
      config: {
        processorType: jobConfig.processorType,
        queueType: jobConfig.queueType,
        cronExpression: jobConfig.cronExpression,
        description: jobConfig.description,
        category: jobConfig.category
      }
    })
    
  } catch (error) {
    console.error('[API] Error disabling cron job:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
