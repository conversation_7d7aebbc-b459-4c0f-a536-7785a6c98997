import { NextRequest, NextResponse } from 'next/server'
import { processorQueueManager } from '../../../../lib/queue/ProcessorQueueManager'
import { getProcessorConfig } from '../../../../lib/queue/config/processor-registry'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      processorType, 
      options = {}, 
      scheduleType, 
      scheduleValue,
      customConfig = {}
    } = body

    // Validate processor type
    const processorConfig = getProcessorConfig(processorType)
    if (!processorConfig) {
      return NextResponse.json({
        success: false,
        error: `Unknown processor type: ${processorType}`
      }, { status: 400 })
    }

    // Merge default options with provided options
    const mergedOptions = { ...processorConfig.defaultOptions, ...options }
    
    let jobId: string
    let message: string

    switch (scheduleType) {
      case 'immediate':
        // Execute immediately
        jobId = await processorQueueManager.addProcessorJob(
          processorType,
          mergedOptions,
          customConfig
        )
        message = `Job queued for immediate execution`
        break

      case 'delayed':
        // Schedule for specific time
        const scheduleTime = new Date(scheduleValue)
        if (isNaN(scheduleTime.getTime())) {
          return NextResponse.json({
            success: false,
            error: 'Invalid schedule time. Use ISO string or timestamp.'
          }, { status: 400 })
        }
        
        jobId = await processorQueueManager.scheduleProcessorJob(
          processorType,
          mergedOptions,
          scheduleTime,
          customConfig
        )
        message = `Job scheduled for ${scheduleTime.toISOString()}`
        break

      case 'cron':
        // Schedule with cron pattern
        jobId = await processorQueueManager.scheduleRepeatableJob(
          processorType,
          mergedOptions,
          scheduleValue, // cron pattern
          {
            ...customConfig,
            jobId: customConfig.jobId || `cron-${processorType}-${Date.now()}`
          }
        )
        message = `Job scheduled with cron pattern: ${scheduleValue}`
        break

      case 'interval':
        // Schedule with interval
        const intervalMs = parseInt(scheduleValue)
        if (isNaN(intervalMs) || intervalMs <= 0) {
          return NextResponse.json({
            success: false,
            error: 'Invalid interval. Must be positive number in milliseconds.'
          }, { status: 400 })
        }
        
        jobId = await processorQueueManager.scheduleIntervalJob(
          processorType,
          mergedOptions,
          intervalMs,
          {
            ...customConfig,
            jobId: customConfig.jobId || `interval-${processorType}-${Date.now()}`
          }
        )
        message = `Job scheduled with interval: ${intervalMs}ms`
        break

      case 'custom':
        // Schedule with custom repeat strategy
        jobId = await processorQueueManager.scheduleCustomRepeatJob(
          processorType,
          mergedOptions,
          scheduleValue, // custom pattern
          {
            ...customConfig,
            jobId: customConfig.jobId || `custom-${processorType}-${Date.now()}`
          }
        )
        message = `Job scheduled with custom pattern: ${scheduleValue}`
        break

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid schedule type. Use: immediate, delayed, cron, interval, or custom'
        }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      jobId,
      processorType,
      scheduleType,
      scheduleValue,
      queueType: processorConfig.queueType,
      queueName: processorConfig.queueType === 'CUSTOM' ? processorConfig.customQueueName : processorConfig.queueType,
      message
    })

  } catch (error) {
    console.error('[Schedule API] Error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Remove a repeatable job
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const processorType = searchParams.get('processorType')
    const jobId = searchParams.get('jobId')

    if (!processorType || !jobId) {
      return NextResponse.json({
        success: false,
        error: 'Missing processorType or jobId'
      }, { status: 400 })
    }

    await processorQueueManager.removeRepeatableJob(processorType as any, jobId)

    return NextResponse.json({
      success: true,
      message: `Removed repeatable job ${jobId} for ${processorType}`
    })

  } catch (error) {
    console.error('[Schedule API] Error removing job:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
