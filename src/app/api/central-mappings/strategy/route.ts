import { NextRequest, NextResponse } from 'next/server';
import { AppDataSource } from '@/lib/typeorm/config';

export async function GET(request: NextRequest) {
  try {
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Query the central_mapping table for strategy options
    // Use normalized value (value_2) if available, otherwise fallback to value_1
    const strategies = await AppDataSource.query(`
      SELECT DISTINCT 
        CASE 
          WHEN value_2 IS NOT NULL AND value_2 != '' 
          THEN value_2 
          ELSE value_1 
        END as strategy
      FROM central_mapping 
      WHERE type = 'Strategies' 
        AND is_active = true 
        AND (value_1 IS NOT NULL AND value_1 != '')
      ORDER BY strategy
    `);

    // Extract strategy values
    const strategyOptions = strategies.map((row: any) => row.strategy);

    return NextResponse.json({
      success: true,
      strategies: strategyOptions,
      count: strategyOptions.length
    });

  } catch (error) {
    console.error('Error fetching strategy options:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch strategy options',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
