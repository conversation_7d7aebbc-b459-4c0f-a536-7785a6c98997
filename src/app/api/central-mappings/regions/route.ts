import { NextRequest, NextResponse } from 'next/server';
import { AppDataSource } from '@/lib/typeorm/config';

export async function GET(request: NextRequest) {
  try {
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Query the central_mapping table for regions and their states
    const regionsData = await AppDataSource.query(`
      SELECT 
        value_1 as region,
        value_2 as state
      FROM central_mapping 
      WHERE type = 'U.S Regions' 
        AND is_active = true 
        AND value_1 IS NOT NULL 
        AND value_1 != ''
        AND value_2 IS NOT NULL 
        AND value_2 != ''
      ORDER BY value_1, value_2
    `);

    // Group states by region
    const regionStateMap: { [key: string]: string[] } = {};
    const allStates: string[] = [];
    const allRegions: string[] = [];

    regionsData.forEach((row: any) => {
      const region = row.region;
      const state = row.state;

      // Add to regions list
      if (!allRegions.includes(region)) {
        allRegions.push(region);
      }

      // Add to states list
      if (!allStates.includes(state)) {
        allStates.push(state);
      }

      // Add to region-state mapping
      if (!regionStateMap[region]) {
        regionStateMap[region] = [];
      }
      if (!regionStateMap[region].includes(state)) {
        regionStateMap[region].push(state);
      }
    });

    // Sort states within each region
    Object.keys(regionStateMap).forEach(region => {
      regionStateMap[region].sort();
    });

    // Sort all regions and states
    allRegions.sort();
    allStates.sort();

    return NextResponse.json({
      success: true,
      data: {
        regions: allRegions,
        states: allStates,
        regionStateMap: regionStateMap
      },
      count: {
        regions: allRegions.length,
        states: allStates.length
      }
    });

  } catch (error) {
    console.error('Error fetching regions and states:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch regions and states',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
