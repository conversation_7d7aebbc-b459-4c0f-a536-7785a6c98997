import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface ReprocessRequest {
  companyId: number
  triggerProcessing?: boolean
}

/**
 * API endpoint to trigger re-processing of contact investment criteria
 * when company investment criteria is updated
 */
export async function POST(request: NextRequest) {
  try {
    const body: ReprocessRequest = await request.json()
    const { companyId, triggerProcessing = false } = body

    if (!companyId) {
      return NextResponse.json(
        { success: false, error: 'Company ID is required' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      // Start transaction
      await client.query('BEGIN')

      // Find all contacts belonging to this company that have completed IC processing
      const contactsResult = await client.query(`
        SELECT contact_id, first_name, last_name, email
        FROM contacts 
        WHERE company_id = $1 
        AND contact_investment_criteria_status = 'completed'
        AND contact_investment_criteria_date IS NOT NULL
      `, [companyId])

      const contactsToReprocess = contactsResult.rows

      if (contactsToReprocess.length === 0) {
        await client.query('COMMIT')
        return NextResponse.json({
          success: true,
          message: 'No contacts with completed IC found for this company',
          contactsReprocessed: 0
        })
      }

      // Reset their status to pending to trigger re-processing
      const contactIds = contactsToReprocess.map(c => c.contact_id)
      await client.query(`
        UPDATE contacts 
        SET 
          contact_investment_criteria_status = 'pending',
          contact_investment_criteria_error = NULL,
          contact_investment_criteria_error_count = 0,
          updated_at = NOW()
        WHERE contact_id = ANY($1::int[])
      `, [contactIds])

      // Optional: Delete existing contact IC records to force fresh processing
      await client.query(`
        DELETE FROM investment_criteria_central
        WHERE entity_id = ANY($1::int[]) AND entity_type = 'contact'
      `, [contactIds])

      await client.query('COMMIT')

      // Log the re-processing trigger
      console.log(`Triggered IC re-processing for ${contactsToReprocess.length} contacts of company ${companyId}`)

      // Optionally trigger the processor immediately
      if (triggerProcessing) {
        try {
          // Call the processing trigger API
          const processingResponse = await fetch(`${process.env.API_BASE_URL || ''}/api/processing/trigger`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'execute_manual',
              stage: 'contact_investment_criteria',
              entityType: 'contact',
              options: {
                multiIds: contactIds,
                filters: {}
              }
            })
          })
          
          if (!processingResponse.ok) {
            console.warn('Failed to trigger immediate processing:', await processingResponse.text())
          }
        } catch (processingError) {
          console.warn('Error triggering immediate processing:', processingError)
          // Don't fail the main operation if processing trigger fails
        }
      }

      return NextResponse.json({
        success: true,
        message: `Successfully queued ${contactsToReprocess.length} contacts for IC re-processing`,
        contactsReprocessed: contactsToReprocess.length,
        contacts: contactsToReprocess.map(c => ({
          id: c.contact_id,
          name: `${c.first_name} ${c.last_name}`.trim() || c.email,
          email: c.email
        })),
        processingTriggered: triggerProcessing
      })

    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error in reprocess-contacts API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      },
      { status: 500 }
    )
  }
}
