import { NextResponse } from "next/server";
import { pool } from "@/lib/db";

interface FilterGroup {
  name: string;
  filters: Filter[];
}

interface Filter {
  name: string;
  type: "range" | "multiselect" | "text";
  options?: string[];
  min?: number;
  max?: number;
  unit?: string;
  hierarchicalMappings?: { [key: string]: any };
}

interface CentralMappingData {
  [key: string]: {
    flat: string[];
    hierarchical?: { [key: string]: string[] };
  };
}

export async function GET() {
  try {
    console.log("Starting investment criteria filters API...");

    // Get hierarchical mappings from central_mapping table
    const client = await pool.connect();

    const hierarchicalQuery = `
      SELECT 
        type,
        level_1,
        value_1,
        level_2,
        value_2
      FROM central_mapping
      WHERE is_active = true AND type IN ('Property Type', 'Capital Position', 'U.S Regions', 'Strategies', 'Loan Program', 'Recourse Loan', 'Structured Loan Tranches')
      ORDER BY type, value_1, value_2
    `;

    const hierarchicalResult = await client.query(hierarchicalQuery);

    // Process hierarchical mappings
    const hierarchicalMappings: CentralMappingData = {};

    hierarchicalResult.rows.forEach((row) => {
      const { type, value_1, value_2 } = row;

      if (!hierarchicalMappings[type]) {
        hierarchicalMappings[type] = { flat: [], hierarchical: {} };
      }

      // Add parent value to flat array if not already there
      if (!hierarchicalMappings[type].flat.includes(value_1)) {
        hierarchicalMappings[type].flat.push(value_1);
      }

      // Build hierarchical structure
      if (value_2) {
        if (!hierarchicalMappings[type].hierarchical) {
          hierarchicalMappings[type].hierarchical = {};
        }
        if (!hierarchicalMappings[type].hierarchical[value_1]) {
          hierarchicalMappings[type].hierarchical[value_1] = [];
        }
        if (
          !hierarchicalMappings[type].hierarchical[value_1].includes(value_2)
        ) {
          hierarchicalMappings[type].hierarchical[value_1].push(value_2);
        }
      }
    });

    // Simple query without complex UNION to avoid column mismatch
    const dataQuery = `
      SELECT 
        -- Numeric ranges
        MIN(target_return) as min_target_return,
        MAX(target_return) as max_target_return,
        MIN(minimum_deal_size) as min_deal_size,
        MAX(maximum_deal_size) as max_deal_size,
        MIN(historical_irr) as min_historical_irr,
        MAX(historical_irr) as max_historical_irr,
        MIN(historical_em) as min_historical_em,
        MAX(historical_em) as max_historical_em,
        
        -- Hold Period Range
        MIN(LEAST(COALESCE(min_hold_period, 999), COALESCE(max_hold_period, 999))) as overall_min_hold_period,
        MAX(GREATEST(COALESCE(min_hold_period, 0), COALESCE(max_hold_period, 0))) as overall_max_hold_period,
        
        -- Loan Term Range
        MIN(LEAST(COALESCE(min_loan_term, 999), COALESCE(max_loan_term, 999))) as overall_min_loan_term,
        MAX(GREATEST(COALESCE(min_loan_term, 0), COALESCE(max_loan_term, 0))) as overall_max_loan_term,
        
        -- Interest Rate Range
        MIN(interest_rate) as min_interest_rate,
        MAX(interest_rate) as max_interest_rate,
        
        -- LTV/LTC Ranges
        MIN(LEAST(COALESCE(loan_to_value_min, 999), COALESCE(loan_to_value_max, 999))) as overall_min_ltv,
        MAX(GREATEST(COALESCE(loan_to_value_min, 0), COALESCE(loan_to_value_max, 0))) as overall_max_ltv,
        MIN(LEAST(COALESCE(loan_to_cost_min, 999), COALESCE(loan_to_cost_max, 999))) as overall_min_ltc,
        MAX(GREATEST(COALESCE(loan_to_cost_min, 0), COALESCE(loan_to_cost_max, 0))) as overall_max_ltc,
        
        -- Fee Ranges
        MIN(LEAST(COALESCE(loan_origination_fee_min, 999), COALESCE(loan_origination_fee_max, 999))) as overall_min_origination_fee,
        MAX(GREATEST(COALESCE(loan_origination_fee_min, 0), COALESCE(loan_origination_fee_max, 0))) as overall_max_origination_fee,
        MIN(LEAST(COALESCE(loan_exit_fee_min, 999), COALESCE(loan_exit_fee_max, 999))) as overall_min_exit_fee,
        MAX(GREATEST(COALESCE(loan_exit_fee_min, 0), COALESCE(loan_exit_fee_max, 0))) as overall_max_exit_fee,
        
        -- DSCR Range
        MIN(LEAST(COALESCE(min_loan_dscr, 999), COALESCE(max_loan_dscr, 999))) as overall_min_dscr,
        MAX(GREATEST(COALESCE(min_loan_dscr, 0), COALESCE(max_loan_dscr, 0))) as overall_max_dscr,
        
        -- Closing Time
        MIN(closing_time_weeks) as min_closing_time,
        MAX(closing_time_weeks) as max_closing_time
        
      FROM investment_criteria 
      WHERE is_active = true
    `;

    // Separate queries for array fields to avoid UNION issues
    const arrayQueries = {
      strategies: `SELECT DISTINCT unnest(strategies) as val FROM investment_criteria WHERE is_active = true AND strategies IS NOT NULL`,
      property_types: `SELECT DISTINCT unnest(property_types) as val FROM investment_criteria WHERE is_active = true AND property_types IS NOT NULL`,
      property_sub_categories: `SELECT DISTINCT unnest(property_sub_categories) as val FROM investment_criteria WHERE is_active = true AND property_sub_categories IS NOT NULL`,
      financial_products: `SELECT DISTINCT unnest(financial_products) as val FROM investment_criteria WHERE is_active = true AND financial_products IS NOT NULL`,
      countries: `SELECT DISTINCT unnest(country) as val FROM investment_criteria WHERE is_active = true AND country IS NOT NULL`,
      regions: `SELECT DISTINCT unnest(region) as val FROM investment_criteria WHERE is_active = true AND region IS NOT NULL`,
      states: `SELECT DISTINCT unnest(state) as val FROM investment_criteria WHERE is_active = true AND state IS NOT NULL`,
      cities: `SELECT DISTINCT unnest(city) as val FROM investment_criteria WHERE is_active = true AND city IS NOT NULL`,
      loan_programs: `SELECT DISTINCT unnest(loan_program) as val FROM investment_criteria WHERE is_active = true AND loan_program IS NOT NULL`,
      loan_types: `SELECT DISTINCT unnest(loan_type) as val FROM investment_criteria WHERE is_active = true AND loan_type IS NOT NULL`,
      structured_loan_tranches: `SELECT DISTINCT unnest(structured_loan_tranche) as val FROM investment_criteria WHERE is_active = true AND structured_loan_tranche IS NOT NULL`,
      recourse_loans: `SELECT DISTINCT unnest(recourse_loan) as val FROM investment_criteria WHERE is_active = true AND recourse_loan IS NOT NULL`,
      capital_positions: `SELECT DISTINCT unnest(capital_position) as val FROM investment_criteria WHERE is_active = true AND capital_position IS NOT NULL`,
      loan_types_normalized: `SELECT DISTINCT unnest(loan_type_normalized) as val FROM investment_criteria WHERE is_active = true AND loan_type_normalized IS NOT NULL`,
      capital_sources: `SELECT DISTINCT capital_source as val FROM investment_criteria WHERE is_active = true AND capital_source IS NOT NULL AND capital_source != ''`,
      // V2 NSF fields data - only actual capital positions
      v2_capital_positions: `SELECT DISTINCT capital_position as val FROM deal_nsf_fields WHERE capital_position IS NOT NULL AND capital_position != '' AND capital_position IN ('Acquisition', 'Bridge', 'Co-GP', 'Common Equity', 'Construction', 'C-Pace', 'General Partner (GP)', 'Joint Venture (JV)', 'Limited Partner (LP)', 'Mezzanine', 'Permanent', 'Preferred Equity', 'Senior Debt')`,
      v2_source_types: `SELECT DISTINCT source_type as val FROM deal_nsf_fields WHERE source_type IS NOT NULL AND source_type != ''`,
      // Deal statuses from actual deals
      v1_deal_statuses: `SELECT DISTINCT status as val FROM deals WHERE status IS NOT NULL AND status != ''`,
      v2_deal_statuses: `SELECT DISTINCT deal_status as val FROM dealsv2 WHERE deal_status IS NOT NULL AND deal_status != ''`,
      // Deal stages from actual deals
      v1_deal_stages: `SELECT DISTINCT deal_stage as val FROM deals WHERE deal_stage IS NOT NULL AND deal_stage != ''`,
      v2_deal_stages: `SELECT DISTINCT deal_stage as val FROM dealsv2 WHERE deal_stage IS NOT NULL AND deal_stage != ''`,
      // Location data from properties and contacts
      v1_addresses: `SELECT DISTINCT address as val FROM deals WHERE address IS NOT NULL AND address != ''`,
      v1_states: `SELECT DISTINCT unnest(contact_state) as val FROM contacts WHERE contact_state IS NOT NULL`,
      v1_regions: `SELECT DISTINCT region as val FROM contacts WHERE region IS NOT NULL AND region != ''`,
      v2_addresses: `SELECT DISTINCT address as val FROM properties WHERE address IS NOT NULL AND address != ''`,
      v2_states: `SELECT DISTINCT state as val FROM properties WHERE state IS NOT NULL AND state != ''`,
      v2_regions: `SELECT DISTINCT region as val FROM properties WHERE region IS NOT NULL AND region != ''`,
      // NSF amounts for V2
      v2_amounts: `SELECT DISTINCT amount as val FROM deal_nsf_fields WHERE amount IS NOT NULL AND amount > 0`,
    };

    // Execute main query
    const dataResult = await client.query(dataQuery);
    const data = dataResult.rows[0];

    // Execute array queries
    const arrayResults: Record<string, string[]> = {};
    for (const [key, query] of Object.entries(arrayQueries)) {
      const result = await client.query(query);
      arrayResults[key] = result.rows.map((row) => row.val).sort();
    }

    client.release();

    // Merge results
    const finalData = {
      ...data,
      ...arrayResults,
    };

    // Helper function to clean and filter null values
    const cleanArray = (arr: any[]) =>
      (arr || []).filter((v) => v != null && v !== "").sort();

    // Build filter groups with proper organization
    const filterGroups: FilterGroup[] = [
      {
        name: "Investment Focus",
        filters: [
          {
            name: "Property Types",
            type: "multiselect",
            options: hierarchicalMappings["Property Type"]?.hierarchical
              ? Object.keys(hierarchicalMappings["Property Type"].hierarchical)
              : hierarchicalMappings["Property Type"]?.flat ||
                cleanArray(finalData.property_types),
            hierarchicalMappings: hierarchicalMappings["Property Type"] || {},
          },
          {
            name: "Property Subcategories",
            type: "multiselect",
            options: cleanArray(finalData.property_sub_categories),
          },
          {
            name: "Strategies",
            type: "multiselect",
            options: (() => {
              // Combine central mapping strategies with database strategies
              const centralStrategies =
                hierarchicalMappings["Strategies"]?.flat || [];
              const dbStrategies = cleanArray(finalData.strategies);
              const allStrategies = [
                ...new Set([...centralStrategies, ...dbStrategies]),
              ];
              return allStrategies.sort();
            })(),
            hierarchicalMappings: hierarchicalMappings["Strategies"] || {},
          },
          {
            name: "Capital Position",
            type: "multiselect",
            options: (() => {
              // Define actual capital positions (not loan types, costs, etc.)
              const actualCapitalPositions = [
                'Acquisition',
                'Bridge', 
                'Co-GP',
                'Common Equity',
                'Construction',
                'C-Pace',
                'General Partner (GP)',
                'Joint Venture (JV)',
                'Limited Partner (LP)',
                'Mezzanine',
                'Permanent',
                'Preferred Equity',
                'Senior Debt',
                'Stretch Senior'
              ];
              
              // Combine central mapping, V1 investment criteria, and V2 NSF fields
              const centralCapitalPositions = hierarchicalMappings["Capital Position"]?.hierarchical
                ? Object.keys(hierarchicalMappings["Capital Position"].hierarchical)
                : hierarchicalMappings["Capital Position"]?.flat || [];
              const v1CapitalPositions = cleanArray(finalData.capital_positions);
              const v2CapitalPositions = cleanArray(finalData.v2_capital_positions);
              
              // Combine all sources, filter to only actual capital positions, and remove duplicates
              const allCapitalPositions = [
                ...new Set([
                  ...centralCapitalPositions,
                  ...v1CapitalPositions,
                  ...v2CapitalPositions,
                  ...actualCapitalPositions
                ])
              ].filter(position => actualCapitalPositions.includes(position));
              
              return allCapitalPositions.sort();
            })(),
            hierarchicalMappings: hierarchicalMappings["Capital Position"] || {},
          },
          {
            name: "Loan Types",
            type: "multiselect",
            options: cleanArray(finalData.loan_types),
          },
          {
            name: "Source Types (V2)",
            type: "multiselect",
            options: cleanArray(finalData.v2_source_types),
          },
        ],
      },
      {
        name: "Deal Status & Stage",
        filters: [
          {
            name: "Deal Status",
            type: "multiselect",
            options: (() => {
              const v1Statuses = cleanArray(finalData.v1_deal_statuses);
              const v2Statuses = cleanArray(finalData.v2_deal_statuses);
              return [...new Set([...v1Statuses, ...v2Statuses])].sort();
            })(),
          },
          {
            name: "Deal Stage",
            type: "multiselect",
            options: (() => {
              const v1Stages = cleanArray(finalData.v1_deal_stages);
              const v2Stages = cleanArray(finalData.v2_deal_stages);
              return [...new Set([...v1Stages, ...v2Stages])].sort();
            })(),
          },
        ],
      },
      {
        name: "Deal Economics",
        filters: [
          {
            name: "Deal Amount",
            type: "range",
            min: Math.min(
              data.min_deal_size ? Number(data.min_deal_size) : 0,
              Math.min(...(finalData.v2_amounts || [0]))
            ),
            max: Math.max(
              data.max_deal_size ? Number(data.max_deal_size) : 1000000,
              Math.max(...(finalData.v2_amounts || [0]))
            ),
            unit: "$",
          },
          {
            name: "Target Return",
            type: "range",
            min: data.min_target_return
              ? Number(data.min_target_return) * 100
              : 0,
            max: data.max_target_return
              ? Number(data.max_target_return) * 100
              : 100,
            unit: "%",
          },
          {
            name: "Hold Period",
            type: "range",
            min: data.overall_min_hold_period || 0,
            max: data.overall_max_hold_period || 120,
            unit: "months",
          },
          {
            name: "Historical IRR",
            type: "range",
            min: data.min_historical_irr
              ? Number(data.min_historical_irr) * 100
              : 0,
            max: data.max_historical_irr
              ? Number(data.max_historical_irr) * 100
              : 50,
            unit: "%",
          },
          {
            name: "Historical Equity Multiple",
            type: "range",
            min: data.min_historical_em ? Number(data.min_historical_em) : 0,
            max: data.max_historical_em ? Number(data.max_historical_em) : 5,
            unit: "x",
          },
        ],
      },
      {
        name: "Geographic Focus",
        filters: [
          {
            name: "Country",
            type: "multiselect",
            options: cleanArray(finalData.countries),
          },
          {
            name: "U.S Regions",
            type: "multiselect",
            options: hierarchicalMappings["U.S Regions"]?.hierarchical
              ? Object.keys(hierarchicalMappings["U.S Regions"].hierarchical)
              : hierarchicalMappings["U.S Regions"]?.flat ||
                cleanArray(finalData.regions),
            hierarchicalMappings: hierarchicalMappings["U.S Regions"] || {},
          },
          {
            name: "States",
            type: "multiselect",
            options: cleanArray(finalData.states),
          },
          {
            name: "Cities",
            type: "multiselect",
            options: cleanArray(finalData.cities),
          },
        ],
      },
      {
        name: "Loan Structure",
        filters: [
          {
            name: "Loan Term",
            type: "range",
            min: data.overall_min_loan_term || 0,
            max: data.overall_max_loan_term || 360,
            unit: "months",
          },
          {
            name: "Interest Rate",
            type: "range",
            min: data.min_interest_rate
              ? Number(data.min_interest_rate) * 100
              : 0,
            max: data.max_interest_rate
              ? Number(data.max_interest_rate) * 100
              : 20,
            unit: "%",
          },
          {
            name: "Loan-to-Value (LTV)",
            type: "range",
            min: data.overall_min_ltv ? Number(data.overall_min_ltv) * 100 : 0,
            max: data.overall_max_ltv
              ? Number(data.overall_max_ltv) * 100
              : 100,
            unit: "%",
          },
          {
            name: "Loan-to-Cost (LTC)",
            type: "range",
            min: data.overall_min_ltc ? Number(data.overall_min_ltc) * 100 : 0,
            max: data.overall_max_ltc
              ? Number(data.overall_max_ltc) * 100
              : 100,
            unit: "%",
          },
          {
            name: "DSCR",
            type: "range",
            min: data.overall_min_dscr ? Number(data.overall_min_dscr) : 0,
            max: data.overall_max_dscr ? Number(data.overall_max_dscr) : 5,
            unit: "x",
          },
          {
            name: "Loan Programs",
            type: "multiselect",
            options:
              hierarchicalMappings["Loan Program"]?.flat ||
              cleanArray(finalData.loan_programs),
          },
          {
            name: "Recourse",
            type: "multiselect",
            options:
              hierarchicalMappings["Recourse Loan"]?.flat ||
              cleanArray(finalData.recourse_loans),
          },
          {
            name: "Structured Loan Tranches",
            type: "multiselect",
            options:
              hierarchicalMappings["Structured Loan Tranches"]?.flat ||
              cleanArray(finalData.structured_loan_tranches),
          },
        ],
      },
      {
        name: "Fees & Timing",
        filters: [
          {
            name: "Origination Fee",
            type: "range",
            min: data.overall_min_origination_fee
              ? Number(data.overall_min_origination_fee) * 100
              : 0,
            max: data.overall_max_origination_fee
              ? Number(data.overall_max_origination_fee) * 100
              : 10,
            unit: "%",
          },
          {
            name: "Exit Fee",
            type: "range",
            min: data.overall_min_exit_fee
              ? Number(data.overall_min_exit_fee) * 100
              : 0,
            max: data.overall_max_exit_fee
              ? Number(data.overall_max_exit_fee) * 100
              : 10,
            unit: "%",
          },
          {
            name: "Closing Time",
            type: "range",
            min: data.min_closing_time || 0,
            max: data.max_closing_time || 52,
            unit: "weeks",
          },
        ],
      },
      {
        name: "Other",
        filters: [
          {
            name: "Financial Products",
            type: "multiselect",
            options: cleanArray(finalData.financial_products),
          },
          {
            name: "Capital Source",
            type: "text",
            options: cleanArray(finalData.capital_sources),
          },
        ],
      },
    ];

    // Transform hierarchicalMappings to the format expected by DealsFilters component
    const mappings: { [key: string]: { parents: string[], children: string[] } } = {};
    
    Object.keys(hierarchicalMappings).forEach(type => {
      const mapping = hierarchicalMappings[type];
      mappings[type] = {
        parents: mapping.flat || [],
        children: mapping.hierarchical ? Object.values(mapping.hierarchical).flat() : []
      };
    });

    // Add combined capital position data to mappings
    const capitalPositionFilter = filterGroups
      .find(group => group.name === "Investment Focus")
      ?.filters.find(filter => filter.name === "Capital Position");
    
    if (capitalPositionFilter && capitalPositionFilter.options) {
      mappings["Capital Position"] = {
        parents: capitalPositionFilter.options,
        children: []
      };
    }

    // Add V2 source types to mappings
    const sourceTypeFilter = filterGroups
      .find(group => group.name === "Investment Focus")
      ?.filters.find(filter => filter.name === "Source Types (V2)");
    
    if (sourceTypeFilter && sourceTypeFilter.options) {
      mappings["Source Types (V2)"] = {
        parents: sourceTypeFilter.options,
        children: []
      };
    }

    // Add deal status to mappings
    const dealStatusFilter = filterGroups
      .find(group => group.name === "Deal Status & Stage")
      ?.filters.find(filter => filter.name === "Deal Status");
    
    if (dealStatusFilter && dealStatusFilter.options) {
      mappings["Deal Status"] = {
        parents: dealStatusFilter.options,
        children: []
      };
    }

    // Add deal stage to mappings
    const dealStageFilter = filterGroups
      .find(group => group.name === "Deal Status & Stage")
      ?.filters.find(filter => filter.name === "Deal Stage");
    
    if (dealStageFilter && dealStageFilter.options) {
      mappings["Deal Stage"] = {
        parents: dealStageFilter.options,
        children: []
      };
    }

    // Add addresses to mappings
    const addressesFilter = filterGroups
      .find(group => group.name === "Geographic Focus")
      ?.filters.find(filter => filter.name === "Addresses");
    
    if (addressesFilter && addressesFilter.options) {
      mappings["Addresses"] = {
        parents: addressesFilter.options,
        children: []
      };
    }

    return NextResponse.json({
      filterGroups,
      mappings,
      hierarchicalMappings,
      totalRecords: data.total_records,
    });
  } catch (error) {
    console.error("Error fetching investment criteria filters:", error);
    return NextResponse.json(
      { error: "Failed to fetch filters" },
      { status: 500 }
    );
  } finally {
    // The original code had client.release() here, but client is not defined in the new_code.
    // Assuming the intent was to remove it as it's no longer in scope.
  }
}
