import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface ResolveDuplicatesRequest {
  duplicateId: number
  action: 'merge' | 'keep_separate'
  mergeStrategy?: {
    primaryRecordId: number
    fieldsToMerge: string[]
    customValues?: Record<string, any>
    autoMerge?: boolean // New option for automated smart merge
  }
  resolvedBy?: string
  resolutionNotes?: string
}

interface MergeDecisionResult {
  canAutoMerge: boolean
  reason: string
  conflictingFields: string[]
  riskLevel: 'low' | 'medium' | 'high'
}

interface MergeResult {
  success: boolean
  mergedRecordId: number
  deletedRecordId: number
  mergedData: any
}

// Helper function to check decision tree for auto-merge validation
async function checkDecisionTree(client: any, duplicateRecord: any, mergeStrategy: any): Promise<MergeDecisionResult> {
  const { record_type, primary_record_id, duplicate_record_id } = duplicateRecord
  const { primaryRecordId } = mergeStrategy
  
  console.log('🔍 checkDecisionTree called with:', { record_type, primary_record_id, duplicate_record_id, primaryRecordId })
  
  // Determine which record to keep and which to merge
  const keepRecordId = primaryRecordId
  const mergeRecordId = keepRecordId === primary_record_id ? duplicate_record_id : primary_record_id
  
  console.log('🔍 Will keep record:', keepRecordId, 'and merge record:', mergeRecordId)
  
  // Fetch both records for decision tree validation
  let keepRecord: any, mergeRecord: any
  
  try {
    if (record_type === 'company') {
      console.log('🔍 Fetching company records...')
      const [keepResult, mergeResult] = await Promise.all([
        client.query('SELECT * FROM companies WHERE company_id = $1', [keepRecordId]),
        client.query('SELECT * FROM companies WHERE company_id = $1', [mergeRecordId])
      ])
      keepRecord = keepResult.rows[0]
      mergeRecord = mergeResult.rows[0]
      console.log('🔍 Company records fetched:', { 
        keepRecordFound: !!keepRecord, 
        mergeRecordFound: !!mergeRecord,
        keepRecordId: keepRecord?.company_id,
        mergeRecordId: mergeRecord?.company_id
      })
    } else if (record_type === 'contact') {
      console.log('🔍 Fetching contact records...')
      const [keepResult, mergeResult] = await Promise.all([
        client.query('SELECT * FROM contacts WHERE contact_id = $1', [keepRecordId]),
        client.query('SELECT * FROM contacts WHERE contact_id = $1', [mergeRecordId])
      ])
      keepRecord = keepResult.rows[0]
      mergeRecord = mergeResult.rows[0]
      console.log('🔍 Contact records fetched:', { 
        keepRecordFound: !!keepRecord, 
        mergeRecordFound: !!mergeRecord,
        keepRecordId: keepRecord?.contact_id,
        mergeRecordId: mergeRecord?.contact_id
      })
    }
  } catch (error) {
    console.error('❌ Error fetching records in checkDecisionTree:', error)
    return {
      canAutoMerge: false,
      reason: `Error fetching records: ${error.message}`,
      conflictingFields: [],
      riskLevel: 'high'
    }
  }
  
  if (keepRecord && mergeRecord) {
    console.log('🔍 Both records found, evaluating merge decision...')
    return evaluateMergeDecision(keepRecord, mergeRecord, record_type)
  }
  
  // Default to blocking if we can't fetch records
  console.log('❌ Records not found, blocking merge:', { keepRecordFound: !!keepRecord, mergeRecordFound: !!mergeRecord })
  return {
    canAutoMerge: false,
    reason: 'Unable to fetch records for decision tree validation',
    conflictingFields: [],
    riskLevel: 'high'
  }
}

// Decision tree for evaluating merge compatibility (same as in OptimizedMergeModal)
function evaluateMergeDecision(primaryRecord: any, duplicateRecord: any, recordType: 'company' | 'contact'): MergeDecisionResult {
  const conflictingFields: string[] = []
  let reason = ''
  let riskLevel: 'low' | 'medium' | 'high' = 'low'
  
  if (recordType === 'contact') {
    // STRICT REQUIREMENT: For contacts, we need either SAME EMAIL or SAME LINKEDIN to auto-merge
    const primaryEmail = primaryRecord.email || primaryRecord.personal_email || primaryRecord.additional_email
    const duplicateEmail = duplicateRecord.email || duplicateRecord.personal_email || duplicateRecord.additional_email
    const primaryLinkedIn = primaryRecord.linkedin_url
    const duplicateLinkedIn = duplicateRecord.linkedin_url
    
    // Helper function to normalize LinkedIn URLs for comparison
    const normalizeLinkedInUrl = (url: string | null | undefined): string => {
      if (!url) return ''
      return url.toLowerCase()
        .replace(/\/$/, '') // Remove trailing slash
        .replace(/^https?:\/\/(www\.)?linkedin\.com\/in\//, '') // Remove base URL
        .replace(/\/.*$/, '') // Remove everything after the username
    }
    
    // Helper function to check if emails are from same domain
    const isSameDomain = (email1: string, email2: string): boolean => {
      if (!email1 || !email2) return false
      const domain1 = email1.split('@')[1]?.toLowerCase()
      const domain2 = email2.split('@')[1]?.toLowerCase()
      return domain1 === domain2
    }
    
    // Helper function to check if names are similar
    const areNamesSimilar = (name1: string, name2: string): boolean => {
      if (!name1 || !name2) return false
      const normalize = (name: string) => name.toLowerCase().replace(/[^a-z]/g, '')
      const n1 = normalize(name1)
      const n2 = normalize(name2)
      
      // Check if one name contains the other or if they're very similar
      return n1.includes(n2) || n2.includes(n1) || 
             Math.abs(n1.length - n2.length) <= 2 // Allow for minor differences
    }
    
    const normalizedPrimaryLinkedIn = normalizeLinkedInUrl(primaryLinkedIn)
    const normalizedDuplicateLinkedIn = normalizeLinkedInUrl(duplicateLinkedIn)
    
    // Case 1: Both have LinkedIn URLs
    if (normalizedPrimaryLinkedIn && normalizedDuplicateLinkedIn) {
      if (normalizedPrimaryLinkedIn === normalizedDuplicateLinkedIn) {
        // Same LinkedIn URL - this is likely the same person
        if (primaryEmail && duplicateEmail) {
          if (primaryEmail.toLowerCase() === duplicateEmail.toLowerCase()) {
            // Same LinkedIn, same email - definitely same person
            return {
              canAutoMerge: true,
              reason: 'Same LinkedIn URL and email address - clearly the same person',
              conflictingFields: [],
              riskLevel: 'low'
            }
          } else if (isSameDomain(primaryEmail, duplicateEmail)) {
            // Same LinkedIn, different emails but same domain - likely work/personal emails
            return {
              canAutoMerge: true,
              reason: 'Same LinkedIn URL with different emails from same domain - likely work/personal email variations',
              conflictingFields: ['email'],
              riskLevel: 'low'
            }
          } else {

              // Different emails, different names - suspicious
              conflictingFields.push('email')
              reason = 'Same LinkedIn URL but different email domains - potential data integrity issue'
              riskLevel = 'high'
            }
        } else {
          // Same LinkedIn, one or both missing emails
          return {
            canAutoMerge: true,
            reason: 'Same LinkedIn URL - merging to complete email information',
            conflictingFields: [],
            riskLevel: 'low'
          }
        }
      } else {
        // Different LinkedIn URLs - different people
        conflictingFields.push('linkedin_url')
        reason = 'Different LinkedIn URLs - these appear to be different people'
        riskLevel = 'high'
      }
    }
    
    // Case 2: Only one has LinkedIn URL
    else if (normalizedPrimaryLinkedIn || normalizedDuplicateLinkedIn) {
      if (primaryEmail && duplicateEmail) {
        if (primaryEmail.toLowerCase() === duplicateEmail.toLowerCase() || !primaryEmail || !duplicateEmail) {
          // Same email or one or both missing, one has LinkedIn - safe to merge
          return {
            canAutoMerge: true,
            reason: 'Same email address or one or both missing - merging to complete LinkedIn information',
            conflictingFields: [],
            riskLevel: 'low'
          }
        } else if (isSameDomain(primaryEmail, duplicateEmail) && 
                   areNamesSimilar(primaryRecord.full_name, duplicateRecord.full_name)) {
          // Same domain, similar names - probably same person
          return {
            canAutoMerge: true,
            reason: 'Similar names and same email domain - likely the same person',
            conflictingFields: ['email'],
            riskLevel: 'medium'
          }
        } else {
          // Different emails - need manual review
          conflictingFields.push('email')
          reason = 'Different email addresses with only one LinkedIn profile - requires manual review'
          riskLevel = 'high'
        }
      } else {
        // CONSERVATIVE POLICY: Allow merge if one has email/LinkedIn and other doesn't
        // This completes missing information without conflicts
        return {
          canAutoMerge: true,
          reason: 'One record has email/LinkedIn, other missing - safe to merge and complete information',
          conflictingFields: [],
          riskLevel: 'low'
        }
      }
    }
    
    // Case 3: No LinkedIn URLs for either record
    else {
      if (primaryEmail && duplicateEmail) {
        if (primaryEmail.toLowerCase() === duplicateEmail.toLowerCase()) {
          // Same email, no LinkedIn - safe to merge if names are similar
          if (areNamesSimilar(primaryRecord.full_name, duplicateRecord.full_name)) {
            return {
              canAutoMerge: true,
              reason: 'Same email address and similar names - likely the same person',
              conflictingFields: [],
              riskLevel: 'low'
            }
          } else {
            conflictingFields.push('name')
            reason = 'Same email but different names - potential shared email account'
            riskLevel = 'high'
          }
        } else if (isSameDomain(primaryEmail, duplicateEmail) && 
                   areNamesSimilar(primaryRecord.full_name, duplicateRecord.full_name)) {
          return {
            canAutoMerge: true,
            reason: 'Similar names and same email domain - likely the same person with multiple emails',
            conflictingFields: ['email'],
            riskLevel: 'medium'
          }
        } else {
          conflictingFields.push('email')
          reason = 'Different emails and no LinkedIn profiles - requires manual review'
          riskLevel = 'high'
        }
      } else {
        // CONSERVATIVE POLICY: Allow merge if one has email and other doesn't
        // This completes missing information without conflicts
        if (primaryEmail || duplicateEmail) {
          return {
            canAutoMerge: true,
            reason: 'One record has email, other missing - safe to merge and complete information',
            conflictingFields: [],
            riskLevel: 'low'
          }
        } else {
          // No emails or LinkedIn - only merge if names are very similar
          if (areNamesSimilar(primaryRecord.full_name, duplicateRecord.full_name)) {
            return {
              canAutoMerge: true,
              reason: 'Similar names with missing contact information - merging to consolidate data',
              conflictingFields: [],
              riskLevel: 'medium'
            }
          } else {
            conflictingFields.push('name')
            reason = 'Different names with insufficient identifying information - requires manual review'
            riskLevel = 'high'
          }
        }
      }
    }
    
    // Check for additional red flags
    if (primaryRecord.company_name && duplicateRecord.company_name) {
      const company1 = primaryRecord.company_name.toLowerCase()
      const company2 = duplicateRecord.company_name.toLowerCase()
      if (company1 !== company2 && !company1.includes(company2) && !company2.includes(company1)) {
        conflictingFields.push('company_name')
        reason += ' Additionally, different company names suggest these may be different people.'
        riskLevel = 'high'
      }
    }
  } else if (recordType === 'company') {
    // Company merge decision logic - STRICT REQUIREMENT: domains must be the same
    const primaryWebsite = primaryRecord.company_website
    const duplicateWebsite = duplicateRecord.company_website
    const primaryName = primaryRecord.company_name
    const duplicateName = duplicateRecord.company_name
    
    // Helper function to extract domain from URL
    const extractDomain = (url: string | null | undefined): string => {
      if (!url) return ''
      return url.toLowerCase()
        .replace(/^https?:\/\//, '') // Remove protocol
        .replace(/^www\./, '') // Remove www prefix
        .replace(/\/.*$/, '') // Remove everything after domain
        .replace(/^\*\./, '') // Remove wildcard subdomain prefix
        .split('/')[0] // Take only the domain part
    }
    
    const primaryDomain = extractDomain(primaryWebsite)
    const duplicateDomain = extractDomain(duplicateWebsite)
    
    // STRICT REQUIREMENT: For companies, domains must be the same to auto-merge
    if (primaryDomain && duplicateDomain) {
      if (primaryDomain === duplicateDomain) {
        return {
          canAutoMerge: true,
          reason: 'Same website domain - clearly the same company',
          conflictingFields: [],
          riskLevel: 'low'
        }
      } else {
        // Check if domains are subdomains of each other (e.g., app.company.com vs company.com)
        const isSubdomain = (domain1: string, domain2: string): boolean => {
          const parts1 = domain1.split('.')
          const parts2 = domain2.split('.')
          
          // Check if one is a subdomain of the other
          if (parts1.length > parts2.length) {
            return parts1.slice(-parts2.length).join('.') === domain2
          } else if (parts2.length > parts1.length) {
            return parts2.slice(-parts1.length).join('.') === domain1
          }
          return false
        }
        
        if (isSubdomain(primaryDomain, duplicateDomain) || isSubdomain(duplicateDomain, primaryDomain)) {
          return {
            canAutoMerge: true,
            reason: 'Related website domains (subdomain relationship) - likely the same company',
            conflictingFields: ['company_website'],
            riskLevel: 'low'
          }
        } else {
          conflictingFields.push('company_website')
          reason = 'Different website domains - likely different companies'
          riskLevel: 'high'
        }
      }
    } else {
      return {
        canAutoMerge: true,
        reason: 'One record has website domain, other missing - safe to merge and complete information',
        conflictingFields: [],
        riskLevel: 'low'
      }
    }
  }
  
  return {
    canAutoMerge: false,
    reason,
    conflictingFields,
    riskLevel
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 POST /api/duplicates/resolve called')
    
    const { 
      duplicateId, 
      action, 
      mergeStrategy, 
      resolvedBy, 
      resolutionNotes 
    }: ResolveDuplicatesRequest = await request.json()

    console.log('📥 Request data:', { duplicateId, action, mergeStrategy: !!mergeStrategy, resolvedBy, resolutionNotes: !!resolutionNotes })

    if (!duplicateId || !action) {
      console.log('❌ Validation failed: missing duplicateId or action')
      return NextResponse.json(
        { error: 'duplicateId and action are required' },
        { status: 400 }
      )
    }

    if (!['merge', 'keep_separate'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "merge" or "keep_separate"' },
        { status: 400 }
      )
    }

    const client = await pool.connect()

    try {
      await client.query('BEGIN')

      // Get the duplicate record
      const duplicateResult = await client.query(`
        SELECT * FROM duplicate_records WHERE id = $1
      `, [duplicateId])

      if (duplicateResult.rows.length === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Duplicate record not found' },
          { status: 404 }
        )
      }

      const duplicateRecord = duplicateResult.rows[0]
      
      let result: any = { success: true }

      switch (action) {
        case 'merge':
          console.log('🔧 Processing merge action...')
          
          if (!mergeStrategy) {
            console.log('❌ No mergeStrategy provided')
            await client.query('ROLLBACK')
            return NextResponse.json(
              { error: 'mergeStrategy is required for merge action' },
              { status: 400 }
            )
          }
          
          console.log('🔧 Merge strategy:', { 
            autoMerge: mergeStrategy.autoMerge, 
            primaryRecordId: mergeStrategy.primaryRecordId,
            fieldsToMerge: mergeStrategy.fieldsToMerge?.length || 0
          })
          
          // For auto-merge, check decision tree first and handle blocking outside transaction
          if (mergeStrategy.autoMerge) {
            console.log('🤖 Auto-merge requested, checking decision tree...')
            const decision = await checkDecisionTree(client, duplicateRecord, mergeStrategy)
            console.log('🤖 Decision tree result:', decision)
            
            if (!decision.canAutoMerge) {
              console.log('🚫 Auto-merge blocked by decision tree, marking as blocked...')
              // Update status to blocked BEFORE rolling back transaction
              await client.query(`
                UPDATE duplicate_records 
                SET 
                  status = 'blocked', 
                  resolved_by = 'decision_tree_system',
                  resolved_at = NOW()
                WHERE id = $1
              `, [duplicateRecord.id])
              
              console.log(`✅ Marked duplicate ${duplicateRecord.id} as blocked due to decision tree rejection`)
              
              // Commit the status update
              await client.query('COMMIT')
              
              return NextResponse.json({
                success: false,
                error: `Auto-merge not recommended: ${decision.reason}. Risk level: ${decision.riskLevel.toUpperCase()}. Duplicate has been marked as blocked for manual review.`,
                mergedRecordId: 0,
                deletedRecordId: 0,
                mergedData: null
              }, { status: 400 })
            }
            
            console.log('✅ Auto-merge approved by decision tree, proceeding...')
          }
          
          console.log('🔧 Calling performMerge...')
          result = await performMerge(client, duplicateRecord, mergeStrategy)
          // console.log('🔧 performMerge result:', result)
          break

        case 'keep_separate':
          result = await markAsSeparate(client, duplicateRecord, resolvedBy, resolutionNotes)
          break
      }

      if (result.success) {
        await client.query('COMMIT')
        return NextResponse.json(result)
      } else {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: result.error || 'Resolution failed' },
          { status: 500 }
        )
      }

    } catch (error) {
      console.error('=== ERROR IN RESOLVE ENDPOINT ===')
      console.error('Error type:', error.constructor.name)
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error resolving duplicate:', error)
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

async function performMerge(
  client: any, 
  duplicateRecord: any, 
  mergeStrategy: any
): Promise<MergeResult> {
  console.log('=== PERFORM MERGE START ===')
  // console.log('Duplicate record:', duplicateRecord)
  // console.log('Merge strategy:', mergeStrategy)
  
  const { record_type, primary_record_id, duplicate_record_id } = duplicateRecord
  const { primaryRecordId, fieldsToMerge, customValues, autoMerge } = mergeStrategy

  // Determine which record to keep and which to merge
  const keepRecordId = primaryRecordId
  const mergeRecordId = keepRecordId === primary_record_id ? duplicate_record_id : primary_record_id

  console.log('Record type:', record_type)
  console.log('Keep record ID:', keepRecordId)
  console.log('Merge record ID:', mergeRecordId)

  // Auto-merge validation is now handled earlier in the main resolve function
  // If we reach here, the decision tree has already approved the merge
  if (autoMerge) {
    console.log('Auto-merge approved by decision tree, proceeding with merge...')
  }

  if (record_type === 'company') {
    console.log('Calling mergeCompanies...')
    return await mergeCompanies(client, keepRecordId, mergeRecordId, fieldsToMerge, customValues, autoMerge)
  } else if (record_type === 'contact') {
    console.log('Calling mergeContacts...')
    return await mergeContacts(client, keepRecordId, mergeRecordId, fieldsToMerge, customValues, autoMerge)
  } else {
    throw new Error(`Unsupported record type: ${record_type}`)
  }
}

// Smart merge function that automatically coalesces data from both records
function performSmartMerge(primaryData: any, duplicateData: any, recordType: 'company' | 'contact'): Record<string, any> {
  console.log('=== PERFORMING SMART MERGE ===')
  
  const mergedData: Record<string, any> = { ...primaryData }
  
  // Define fields to exclude from auto-merge (system fields, IDs, processing fields, etc.)
  const excludeFields = new Set([
    // Primary identifiers and timestamps
    'company_id', 'contact_id', 'id', 'created_at', 'updated_at',
    
    // System-generated fields
    'canonical_handle', 'processed', 'extracted', 'source',
    
    // Processing state and status fields
    'processing_state', 'website_scraping_status', 'website_scraping_date', 'website_scraping_error',
    'company_overview_status', 'company_overview_date', 'company_overview_error',
    'last_processed_stage', 'last_processed_at', 'processing_error_count', 'processing_attempts',
    'web_scraping_error', 'web_scraping_date', 'website_scraping_error_count', 'company_overview_error_count',
    
    // Conflict resolution fields
    'conflicts', 'conflict_status', 'conflict_created_at', 'conflict_resolved_at', 'conflict_source',
    
    // LLM processing fields
    'llm_response', 'llm_token_usage', 'llm_used', 'overview_v2_error', 'overview_v2_date', 
    'overview_v2_error_count', 'overview_v2_status', 'investment_criteria_status', 
    'investment_criteria_error', 'investment_criteria_date', 'investment_criteria_error_count',
    
    // Data processing metadata
    'data_source', 'last_updated_timestamp', 'data_confidence_score',
    
    // Internal tracking fields
    'internal_relationship_manager', 'last_contact_date', 'pipeline_status', 'role_in_previous_deal',
    
    // External service IDs with unique constraints
    'smartlead_lead_id', 'smartlead_status', 'email_batch_identifier'
  ])
  
  // Define fields that should prefer more recent data
  const preferRecentFields = new Set([
    'company_phone', 'main_phone', 'phone_number', 'contact_phone', 'secondary_phone',
    'email', 'main_email', 'secondary_email', 'personal_email',
    'company_website', 'linkedin_url', 'company_linkedin', 'twitter', 'facebook', 'instagram', 'youtube',
    'headquarters_address', 'company_address', 'additional_address'
  ])
  
  // Define numeric fields that should take the higher value (more complete data)
  const maxValueFields = new Set([
    'founded_year', 'number_of_employees', 'number_of_offices', 'number_of_properties',
    'fund_size', 'aum', 'annual_revenue', 'market_capitalization', 'net_income', 'ebitda',
    'fund_size_active_fund', 'annual_loan_volume', 'dry_powder', 'annual_deployment_target',
    'transactions_completed_last_12m', 'total_transaction_volume_ytd', 'deal_count_ytd', 
    'average_deal_size', 'portfolio_size_sqft', 'portfolio_asset_count'
  ])
  
  let mergedFieldsCount = 0
  
  for (const [field, duplicateValue] of Object.entries(duplicateData)) {
    if (excludeFields.has(field)) continue
    
    const primaryValue = primaryData[field]
    
    // Skip if duplicate value is null, undefined, or empty string
    if (duplicateValue == null || duplicateValue === '') continue
    
    // If primary field is empty/null, use duplicate value
    if (primaryValue == null || primaryValue === '') {
      mergedData[field] = duplicateValue
      mergedFieldsCount++
      console.log(`Auto-merged field '${field}': null -> '${duplicateValue}'`)
      continue
    }
    
    // For arrays, merge them if both exist
    if (Array.isArray(primaryValue) && Array.isArray(duplicateValue)) {
      const merged = [...new Set([...primaryValue, ...duplicateValue])] // Remove duplicates
      if (merged.length > primaryValue.length) {
        mergedData[field] = merged
        mergedFieldsCount++
        console.log(`Auto-merged array field '${field}': combined ${primaryValue.length} + ${duplicateValue.length} -> ${merged.length} items`)
      }
      continue
    }
    
    // For numeric fields, prefer higher values (more complete/recent data)
    if (maxValueFields.has(field) && typeof duplicateValue === 'number' && typeof primaryValue === 'number') {
      if (duplicateValue > primaryValue) {
        mergedData[field] = duplicateValue
        mergedFieldsCount++
        console.log(`Auto-merged numeric field '${field}': ${primaryValue} -> ${duplicateValue} (higher value)`)
      }
      continue
    }
    
    // For contact/phone fields, prefer the duplicate if it looks more complete
    if (preferRecentFields.has(field)) {
      if (typeof duplicateValue === 'string' && typeof primaryValue === 'string') {
        // Prefer longer, more formatted values (e.g., "******-123-4567" over "5551234567")
        if (duplicateValue.length > primaryValue.length || 
            (duplicateValue.includes('-') || duplicateValue.includes('(')) && 
            !(primaryValue.includes('-') || primaryValue.includes('('))) {
          mergedData[field] = duplicateValue
          mergedFieldsCount++
          console.log(`Auto-merged contact field '${field}': '${primaryValue}' -> '${duplicateValue}' (more formatted)`)
        }
      }
      continue
    }
    
    // For string fields, prefer longer/more descriptive values
    if (typeof primaryValue === 'string' && typeof duplicateValue === 'string') {
      // If duplicate is significantly longer and contains the primary value, use duplicate
      if (duplicateValue.length > primaryValue.length * 1.5 && 
          duplicateValue.toLowerCase().includes(primaryValue.toLowerCase())) {
        mergedData[field] = duplicateValue
        mergedFieldsCount++
        console.log(`Auto-merged text field '${field}': '${primaryValue}' -> '${duplicateValue}' (more descriptive)`)
      }
    }
  }
  
  console.log(`Smart merge completed: ${mergedFieldsCount} fields automatically merged`)
  return mergedData
}

async function mergeCompanies(
  client: any,
  keepId: number,
  mergeId: number,
  fieldsToMerge: string[],
  customValues?: Record<string, any>,
  autoMerge?: boolean
): Promise<MergeResult> {
  console.log('=== MERGE COMPANIES START ===')
  console.log('Keep ID:', keepId, 'Merge ID:', mergeId)
  console.log('Fields to merge:', fieldsToMerge)
  console.log('Custom values:', customValues)
  console.log('Auto merge enabled:', autoMerge)
  
  // Get both company records
  console.log('Fetching company records...')
  const [keepCompany, mergeCompany] = await Promise.all([
    client.query('SELECT * FROM companies WHERE company_id = $1', [keepId]),
    client.query('SELECT * FROM companies WHERE company_id = $1', [mergeId])
  ])

  if (keepCompany.rows.length === 0 || mergeCompany.rows.length === 0) {
    const missingCompanies: string[] = []
    if (keepCompany.rows.length === 0) missingCompanies.push(`Primary company ID ${keepId}`)
    if (mergeCompany.rows.length === 0) missingCompanies.push(`Duplicate company ID ${mergeId}`)
    
    return { 
      success: false, 
      error: `One or both companies not found: ${missingCompanies.join(', ')}. This may indicate a data integrity issue where duplicate records reference non-existent companies.` 
    } as any
  }

  const keepData = keepCompany.rows[0]
  const mergeData = mergeCompany.rows[0]

  let finalData = keepData
  
  // If auto merge is enabled, perform smart merge
  if (autoMerge) {
    finalData = performSmartMerge(keepData, mergeData, 'company')
  }

  // Build update query for the company to keep
  const updateFields: string[] = []
  const updateValues: any[] = []
  let paramIndex = 1
  const processedFields = new Set<string>() // Track processed fields to avoid duplicates

  // If auto merge was performed, update all changed fields
  if (autoMerge) {
    // Define JSON fields for companies (you may need to adjust this list based on your schema)
    const jsonFields = new Set(['extra_attrs', 'conflicts', 'processing_attempts'])
    
    for (const [field, value] of Object.entries(finalData)) {
      // Skip system fields and ID fields
      if (field === 'company_id' || field === 'created_at') continue
      
      // Only update if the value actually changed
      if (keepData[field] !== value) {
        updateFields.push(`${field} = $${paramIndex}`)
        
        // Handle JSON fields properly - stringify if it's a JSON field
        if (jsonFields.has(field) && value !== null && typeof value === 'object') {
          updateValues.push(JSON.stringify(value))
        } else {
          updateValues.push(value)
        }
        paramIndex++
        processedFields.add(field)
      }
    }
  } else {
    // Original merge logic for manual merging
    
    // Merge specified fields
    for (const field of fieldsToMerge) {
      if (!processedFields.has(field) && field in mergeData && mergeData[field] && !keepData[field]) {
        updateFields.push(`${field} = $${paramIndex}`)
        updateValues.push(mergeData[field])
        paramIndex++
        processedFields.add(field)
      }
    }

    // Apply custom values (only for fields not already processed)
    if (customValues) {
      for (const [field, value] of Object.entries(customValues)) {
        if (!processedFields.has(field)) {
          updateFields.push(`${field} = $${paramIndex}`)
          updateValues.push(value)
          paramIndex++
          processedFields.add(field)
        } else {
          console.log(`Skipping duplicate field assignment for: ${field}`)
        }
      }
    }
  }

  // Update the company to keep
  if (updateFields.length > 0) {
    updateValues.push(keepId)
    // console.log('Update fields:', updateFields)
    // console.log('Update values:', updateValues)
    // console.log('Processed fields:', Array.from(processedFields))
    await client.query(`
      UPDATE companies 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE company_id = $${paramIndex}
    `, updateValues)
  }

  // Update all foreign key references to point to the new company ID
  console.log('Updating foreign key references...')

  // Update contacts that reference the merged company
  await client.query(`
    UPDATE contacts 
    SET company_id = $1, updated_at = CURRENT_TIMESTAMP
    WHERE company_id = $2
  `, [keepId, mergeId])

  // Update investment criteria
  await client.query(`
    UPDATE investment_criteria 
    SET entity_id = $1, updated_at = CURRENT_TIMESTAMP
    WHERE entity_type = 'Company' AND entity_id = $2
  `, [keepId.toString(), mergeId.toString()])

  // Handle company_normalized_data - delete duplicate to avoid unique constraint violation
  // (Each company can only have one normalized data record due to unique constraint)
  await client.query(`
    DELETE FROM company_normalized_data 
    WHERE company_id = $1
  `, [mergeId])

  // Update company_web_pages
  await client.query(`
    UPDATE company_web_pages 
    SET company_id = $1
    WHERE company_id = $2
  `, [keepId, mergeId])

  // Update company_web_chunks
  await client.query(`
    UPDATE company_web_chunks 
    SET company_id = $1
    WHERE company_id = $2
  `, [keepId, mergeId])

  // Update company_extracted_contact
  await client.query(`
    UPDATE company_extracted_contact 
    SET company_id = $1, updated_at = CURRENT_TIMESTAMP
    WHERE company_id = $2
  `, [keepId, mergeId])

  // Now it's safe to delete the merged company since all foreign keys have been updated
  console.log('Deleting merged company...')
  await client.query('DELETE FROM companies WHERE company_id = $1', [mergeId])

  // Update duplicate record status
  await client.query(`
    UPDATE duplicate_records 
    SET status = 'merged', resolved_at = CURRENT_TIMESTAMP
    WHERE ((primary_record_id = $1 AND duplicate_record_id = $2) 
       OR (primary_record_id = $2 AND duplicate_record_id = $1))
       AND record_type = 'company'
  `, [keepId, mergeId])

  // Clean up orphaned duplicate records for companies
  await cleanupOrphanedDuplicateRecords(client, 'company')

  // Get the updated company data
  const updatedResult = await client.query('SELECT * FROM companies WHERE company_id = $1', [keepId])

  console.log('=== MERGE COMPANIES COMPLETE ===')
  return {
    success: true,
    mergedRecordId: keepId,
    deletedRecordId: mergeId,
    mergedData: updatedResult.rows[0]
  }
}

async function mergeContacts(
  client: any,
  keepId: number,
  mergeId: number,
  fieldsToMerge: string[],
  customValues?: Record<string, any>,
  autoMerge?: boolean
): Promise<MergeResult> {
  console.log('=== MERGE CONTACTS START ===')
  console.log('Keep ID:', keepId, 'Merge ID:', mergeId)
  console.log('Fields to merge:', fieldsToMerge)
  console.log('Custom values:', customValues)

  // Get both contact records
  const [keepContact, mergeContact] = await Promise.all([
    client.query('SELECT * FROM contacts WHERE contact_id = $1', [keepId]),
    client.query('SELECT * FROM contacts WHERE contact_id = $1', [mergeId])
  ])

  if (keepContact.rows.length === 0 || mergeContact.rows.length === 0) {
    return { success: false, error: 'One or both contacts not found' } as any
  }

  const keepData = keepContact.rows[0]
  const mergeData = mergeContact.rows[0]

  let finalData = keepData
  
  // If auto merge is enabled, perform smart merge
  if (autoMerge) {
    finalData = performSmartMerge(keepData, mergeData, 'contact')
  }

  // Build update query for the contact to keep
  const updateFields: string[] = []
  const updateValues: any[] = []
  let paramIndex = 1
  const processedFields = new Set<string>() // Track processed fields to avoid duplicates

  // If auto merge was performed, update all changed fields
  if (autoMerge) {
    const jsonFields = new Set(['career_timeline', 'conflicts', 'extra_attrs', 'hobbies', 'honorable_achievements', 'processing_attempts'])
    
    for (const [field, value] of Object.entries(finalData)) {
      // Skip system fields and ID fields
      if (field === 'contact_id' || field === 'created_at') continue
      
      // Only update if the value actually changed
      if (keepData[field] !== value) {
        updateFields.push(`${field} = $${paramIndex}`)
        
        // Handle JSON fields properly - stringify if it's a JSON field
        if (jsonFields.has(field) && value !== null && typeof value === 'object') {
          updateValues.push(JSON.stringify(value))
        } else {
          updateValues.push(value)
        }
        paramIndex++
        processedFields.add(field)
      }
    }
  } else {
    // Original merge logic for manual merging
    
    // Merge specified fields
    for (const field of fieldsToMerge) {
      if (!processedFields.has(field) && field in mergeData && mergeData[field] && !keepData[field]) {
        updateFields.push(`${field} = $${paramIndex}`)
        updateValues.push(mergeData[field])
        paramIndex++
        processedFields.add(field)
      }
    }

    // Apply custom values (only for fields not already processed)
    if (customValues) {
      for (const [field, value] of Object.entries(customValues)) {
        if (!processedFields.has(field)) {
          updateFields.push(`${field} = $${paramIndex}`)
          updateValues.push(value)
          paramIndex++
          processedFields.add(field)
        } else {
          console.log(`Skipping duplicate field assignment for: ${field}`)
        }
      }
    }
  }

  // Update the contact to keep
  if (updateFields.length > 0) {
    updateValues.push(keepId)
    // console.log('Update fields:', updateFields)
    // console.log('Update values:', updateValues)
    await client.query(`
      UPDATE contacts 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE contact_id = $${paramIndex}
    `, updateValues)
  }

  // Update all foreign key references to point to the new contact ID
  console.log('Updating foreign key references...')

  // Update investment criteria
  await client.query(`
    UPDATE investment_criteria 
    SET entity_id = $1, updated_at = CURRENT_TIMESTAMP
    WHERE entity_type = 'Contact' AND entity_id = $2
  `, [keepId.toString(), mergeId.toString()])

  // Update contact-related tables if they exist
  // Note: These tables may not exist in all environments, so we'll handle them gracefully
  
  // Update contact_enrichment if table exists (has updated_at column)
  try {
    await client.query(`
      UPDATE contact_enrichment 
      SET contact_id = $1, updated_at = CURRENT_TIMESTAMP
      WHERE contact_id = $2
    `, [keepId, mergeId])
    console.log('Updated contact_enrichment table')
  } catch (error) {
    console.log('contact_enrichment table not found or no data to update:', error.message)
  }

  // Handle contact_normalized_data - delete duplicate to avoid unique constraint violation
  // (Each contact can only have one normalized data record due to unique constraint)
  try {
    await client.query(`
      DELETE FROM contact_normalized_data 
      WHERE contact_id = $1
    `, [mergeId])
    console.log('Deleted duplicate contact_normalized_data record')
  } catch (error) {
    console.log('contact_normalized_data table not found or no data to delete:', error.message)
  }


  // Update deal_contacts if table exists (has updated_at column)
  try {
    await client.query(`
      UPDATE deal_contacts 
      SET contact_id = $1, updated_at = CURRENT_TIMESTAMP
      WHERE contact_id = $2
    `, [keepId, mergeId])
    console.log('Updated deal_contacts table')
  } catch (error) {
    console.log('deal_contacts table not found or no data to update:', error.message)
  }

  // Update contact_extracted_data table - CRITICAL: This was missing and causing FK constraint violation
  try {
    await client.query(`
      UPDATE contact_extracted_data 
      SET contact_id = $1, updated_at = CURRENT_TIMESTAMP
      WHERE contact_id = $2
    `, [keepId, mergeId])
    console.log('Updated contact_extracted_data table')
  } catch (error) {
    console.log('contact_extracted_data table not found or no data to update:', error.message)
  }

  // Update thread_participants table
  try {
    await client.query(`
      UPDATE thread_participants 
      SET contact_id = $1
      WHERE contact_id = $2
    `, [keepId, mergeId])
    console.log('Updated thread_participants table')
  } catch (error) {
    console.log('thread_participants table not found or no data to update:', error.message)
  }

  // Update contact_searched_data table
  try {
    await client.query(`
      UPDATE contact_searched_data 
      SET contact_id = $1, updated_at = CURRENT_TIMESTAMP
      WHERE contact_id = $2
    `, [keepId, mergeId])
    console.log('Updated contact_searched_data table')
  } catch (error) {
    console.log('contact_searched_data table not found or no data to update:', error.message)
  }

  // Note: person_investment_criteria and person_news_mentions tables don't exist in this environment
  // so we skip updating them to avoid transaction errors

  // All contact-related table updates are handled above - no duplicate processing needed

  // Now it's safe to delete the merged contact since all foreign keys have been updated
  console.log('Deleting merged contact...')
  await client.query('DELETE FROM contacts WHERE contact_id = $1', [mergeId])

  // Update duplicate record status
  await client.query(`
    UPDATE duplicate_records 
    SET status = 'merged', resolved_at = CURRENT_TIMESTAMP
    WHERE ((primary_record_id = $1 AND duplicate_record_id = $2) 
       OR (primary_record_id = $2 AND duplicate_record_id = $1))
       AND record_type = 'contact'
  `, [keepId, mergeId])

  // Clean up orphaned duplicate records for contacts
  await cleanupOrphanedDuplicateRecords(client, 'contact')

  // Get the updated contact data
  const updatedResult = await client.query('SELECT * FROM contacts WHERE contact_id = $1', [keepId])

  console.log('=== MERGE CONTACTS COMPLETE ===')
  return {
    success: true,
    mergedRecordId: keepId,
    deletedRecordId: mergeId,
    mergedData: updatedResult.rows[0]
  }
}

async function markAsSeparate(
  client: any,
  duplicateRecord: any,
  resolvedBy?: string,
  resolutionNotes?: string
): Promise<{ success: boolean }> {
  console.log('=== MARK AS SEPARATE START ===')
  console.log('Duplicate record:', duplicateRecord)
  console.log('Resolved by:', resolvedBy)
  console.log('Resolution notes:', resolutionNotes)
  // Delete the duplicate record instead of just marking it as confirmed
  await client.query(`
    DELETE FROM duplicate_records 
    WHERE id = $1
  `, [duplicateRecord.id])

  console.log('=== MARK AS SEPARATE COMPLETE ===')
  return { success: true }
}



// Function to clean up orphaned duplicate records that reference non-existent entities
async function cleanupOrphanedDuplicateRecords(client: any, recordType: 'company' | 'contact'): Promise<void> {
  console.log(`=== CLEANING UP ORPHANED ${recordType.toUpperCase()} DUPLICATE RECORDS ===`)
  
  try {
    let cleanupQuery: string
    let tableName: string
    let idColumn: string
    
    if (recordType === 'company') {
      tableName = 'companies'
      idColumn = 'company_id'
    } else {
      tableName = 'contacts'
      idColumn = 'contact_id'
    }
    
    // Delete duplicate records where either primary or duplicate record no longer exists
    cleanupQuery = `
      DELETE FROM duplicate_records 
      WHERE record_type = $1 
      AND (
        NOT EXISTS (SELECT 1 FROM ${tableName} WHERE ${idColumn} = duplicate_records.primary_record_id)
        OR NOT EXISTS (SELECT 1 FROM ${tableName} WHERE ${idColumn} = duplicate_records.duplicate_record_id)
      )
    `
    
    const result = await client.query(cleanupQuery, [recordType])
    const deletedCount = result.rowCount || 0
    
    if (deletedCount > 0) {
      console.log(`Cleaned up ${deletedCount} orphaned ${recordType} duplicate records`)
    } else {
      console.log(`No orphaned ${recordType} duplicate records found`)
    }
  } catch (error) {
    console.error(`Error cleaning up orphaned ${recordType} duplicate records:`, error)
    // Don't throw error - cleanup is not critical to merge success
  }
}
