import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'
import { DuplicateRecord, DuplicateStatus, DuplicateRecordType } from '@/types/duplicate'

interface GetDuplicatesRequest {
  type?: 'company' | 'contact'
  status?: DuplicateStatus
  page?: number
  pageSize?: number
  minConfidence?: number
  maxConfidence?: number
  matchType?: string
}

interface DuplicateWithDetails extends DuplicateRecord {
  primary_name: string
  duplicate_name: string
  primary_data?: any
  duplicate_data?: any
}

// GET: List all duplicates with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '20')
    const type = searchParams.get('type') as DuplicateRecordType | null
    const status = searchParams.get('status') as DuplicateStatus || 'pending'
    const minConfidence = parseFloat(searchParams.get('minConfidence') || '0')
    const maxConfidence = parseFloat(searchParams.get('maxConfidence') || '1')
    const matchType = searchParams.get('matchType')
    
    const offset = (page - 1) * pageSize
    const client = await pool.connect()

    try {
      // Build WHERE clause
      const conditions: string[] = ['dr.status = $1']
      const params: any[] = [status]
      let paramIndex = 2

      if (type) {
        conditions.push(`dr.record_type = $${paramIndex}`)
        params.push(type)
        paramIndex++
      }

      if (minConfidence > 0) {
        conditions.push(`dr.confidence_score >= $${paramIndex}`)
        params.push(minConfidence)
        paramIndex++
      }

      if (maxConfidence < 1) {
        conditions.push(`dr.confidence_score <= $${paramIndex}`)
        params.push(maxConfidence)
        paramIndex++
      }

      if (matchType) {
        conditions.push(`dr.match_type = $${paramIndex}`)
        params.push(matchType)
        paramIndex++
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

      // Get total count
      const countResult = await client.query(`
        SELECT COUNT(*) as total
        FROM duplicate_records dr
        ${whereClause}
      `, params)

      const total = parseInt(countResult.rows[0].total)

      // Get paginated results with details
      const duplicatesResult = await client.query(`
        SELECT dr.*,
               CASE 
                 WHEN dr.record_type = 'company' THEN c1.company_name
                 WHEN dr.record_type = 'contact' THEN ct1.full_name
               END as primary_name,
               CASE 
                 WHEN dr.record_type = 'company' THEN c2.company_name
                 WHEN dr.record_type = 'contact' THEN ct2.full_name
               END as duplicate_name,
               CASE 
                 WHEN dr.record_type = 'company' THEN 
                   json_build_object(
                     'company_id', c1.company_id,
                     'company_name', c1.company_name,
                     'company_website', c1.company_website,
                     'industry', c1.industry,
                     'company_phone', c1.company_phone,
                     'main_phone', c1.main_phone,
                     'secondary_phone', c1.secondary_phone,
                     'annual_revenue', c1.annual_revenue,
                     'fund_size', c1.fund_size,
                     'number_of_employees', c1.number_of_employees
                   )
                 WHEN dr.record_type = 'contact' THEN 
                   json_build_object(
                     'contact_id', ct1.contact_id,
                     'full_name', ct1.full_name,
                     'email', ct1.email,
                     'linkedin_url', ct1.linkedin_url,
                     'phone_number', ct1.phone_number,
                     'contact_phone', ct1.contact_phone,
                     'phone_number_secondary', ct1.phone_number_secondary,
                     'title', ct1.title,
                     'headline', ct1.headline,
                     'seniority', ct1.seniority,
                     'contact_category', ct1.contact_category,
                     'company_name', comp1.company_name
                   )
               END as primary_data,
               CASE 
                 WHEN dr.record_type = 'company' THEN 
                   json_build_object(
                     'company_id', c2.company_id,
                     'company_name', c2.company_name,
                     'company_website', c2.company_website,
                     'industry', c2.industry,
                     'company_phone', c2.company_phone,
                     'main_phone', c2.main_phone,
                     'secondary_phone', c2.secondary_phone,
                     'annual_revenue', c2.annual_revenue,
                     'fund_size', c2.fund_size,
                     'number_of_employees', c2.number_of_employees
                   )
                 WHEN dr.record_type = 'contact' THEN 
                   json_build_object(
                     'contact_id', ct2.contact_id,
                     'full_name', ct2.full_name,
                     'email', ct2.email,
                     'linkedin_url', ct2.linkedin_url,
                     'phone_number', ct2.phone_number,
                     'contact_phone', ct2.contact_phone,
                     'phone_number_secondary', ct2.phone_number_secondary,
                     'title', ct2.title,
                     'headline', ct2.headline,
                     'seniority', ct2.seniority,
                     'company_name', comp2.company_name
                   )
               END as duplicate_data
        FROM duplicate_records dr
        LEFT JOIN companies c1 ON dr.record_type = 'company' AND dr.primary_record_id = c1.company_id
        LEFT JOIN companies c2 ON dr.record_type = 'company' AND dr.duplicate_record_id = c2.company_id
        LEFT JOIN contacts ct1 ON dr.record_type = 'contact' AND dr.primary_record_id = ct1.contact_id
        LEFT JOIN contacts ct2 ON dr.record_type = 'contact' AND dr.duplicate_record_id = ct2.contact_id
        LEFT JOIN companies comp1 ON dr.record_type = 'contact' AND ct1.company_id = comp1.company_id
        LEFT JOIN companies comp2 ON dr.record_type = 'contact' AND ct2.company_id = comp2.company_id
        ${whereClause}
        ORDER BY dr.confidence_score DESC, dr.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...params, pageSize, offset])

      // Get summary statistics
      const statsResult = await client.query(`
        SELECT 
          record_type,
          status,
          match_type,
          COUNT(*) as count,
          AVG(confidence_score) as avg_confidence
        FROM duplicate_records dr
        GROUP BY record_type, status, match_type
        ORDER BY record_type, status, match_type
      `)

      console.log('Duplicates API Response:', {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
        stats: statsResult.rows
      });
      
      // Log first few records for debugging
      if (duplicatesResult.rows.length > 0) {
        console.log('First duplicate record sample:', {
          id: duplicatesResult.rows[0].id,
          record_type: duplicatesResult.rows[0].record_type,
          primary_name: duplicatesResult.rows[0].primary_name,
          duplicate_name: duplicatesResult.rows[0].duplicate_name,
          primary_data: duplicatesResult.rows[0].primary_data,
          duplicate_data: duplicatesResult.rows[0].duplicate_data
        });
      }

      return NextResponse.json({
        success: true,
        data: duplicatesResult.rows,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        },
        stats: statsResult.rows
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error fetching duplicates:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST: Create or update duplicate record status
export async function POST(request: NextRequest) {
  try {
    const { duplicateId, status, resolvedBy, resolutionNotes } = await request.json()

    if (!duplicateId || !status) {
      return NextResponse.json(
        { error: 'duplicateId and status are required' },
        { status: 400 }
      )
    }

    if (!['pending', 'confirmed', 'false_positive', 'merged'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      )
    }

    const client = await pool.connect()

    try {
      const result = await client.query(`
        UPDATE duplicate_records 
        SET status = $1,
            resolved_at = CASE WHEN $1 != 'pending' THEN CURRENT_TIMESTAMP ELSE NULL END,
            resolved_by = $2,
            resolution_notes = $3
        WHERE id = $4
        RETURNING *
      `, [status, resolvedBy, resolutionNotes, duplicateId])

      if (result.rows.length === 0) {
        return NextResponse.json(
          { error: 'Duplicate record not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        data: result.rows[0]
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error updating duplicate status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE: Remove duplicate record
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const duplicateId = searchParams.get('id')

    if (!duplicateId) {
      return NextResponse.json(
        { error: 'Duplicate ID is required' },
        { status: 400 }
      )
    }

    const client = await pool.connect()

    try {
      const result = await client.query(`
        DELETE FROM duplicate_records 
        WHERE id = $1
        RETURNING *
      `, [duplicateId])

      if (result.rows.length === 0) {
        return NextResponse.json(
          { error: 'Duplicate record not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Duplicate record deleted successfully'
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error deleting duplicate record:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
