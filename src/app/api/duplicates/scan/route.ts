import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'
import { DuplicateDetectionService, CompanyMatchCriteria, ContactMatchCriteria } from '@/lib/services/duplicateDetectionService'
import { DataNormalizationService } from '@/lib/services/dataNormalizationService'

interface ScanRequest {
  type: 'company' | 'contact' | 'both'
  recordIds?: number[]
  criteria?: CompanyMatchCriteria | ContactMatchCriteria
  normalizeFirst?: boolean
  scanOnly?: boolean // New parameter to enable scan-only mode
}

interface ScanResponse {
  success: boolean
  duplicates: {
    companies: number
    contacts: number
  }
  stats: {
    scanned: number
    duplicatesFound: number
    processingTime: number
    normalizationTime?: number
  }
  error?: string
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { type, recordIds, criteria, normalizeFirst = true, scanOnly = false }: ScanRequest = await request.json()

    if (!type || !['company', 'contact', 'both'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid type. Must be "company", "contact", or "both"' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    let scanned = 0
    let duplicatesFound = 0
    let companyDuplicates = 0
    let contactDuplicates = 0
    let normalizationTime = 0

    try {
      // Only normalize if explicitly requested and not in scan-only mode
      if (normalizeFirst && !scanOnly) {
        const normStartTime = Date.now()
        
        if (type === 'company' || type === 'both') {
          console.log('Normalizing company data...')
          await DataNormalizationService.normalizeAllCompanies(500)
        }
        
        if (type === 'contact' || type === 'both') {
          console.log('Normalizing contact data...')
          await DataNormalizationService.normalizeAllContacts(500)
        }
        
        normalizationTime = Date.now() - normStartTime
        console.log(`Normalization completed in ${normalizationTime}ms`)
      } else if (scanOnly) {
        console.log('Running in scan-only mode - skipping normalization')
      }

      // Scan companies for duplicates
      if (type === 'company' || type === 'both') {
        let companyIds: number[] = []
        
        if (recordIds && recordIds.length > 0) {
          companyIds = recordIds
        } else {
          // Get all company IDs
          const result = await client.query('SELECT company_id FROM companies ORDER BY company_id')
          companyIds = result.rows.map(row => row.company_id)
        }

        console.log(`Scanning ${companyIds.length} companies for duplicates...`)

        for (const companyId of companyIds) {
          try {
            const matches = await DuplicateDetectionService.detectCompanyDuplicates(
              companyId,
              criteria as CompanyMatchCriteria
            )

            for (const match of matches) {
              await DuplicateDetectionService.storeDuplicateRecord(match, 'company')
              duplicatesFound++
              companyDuplicates++
            }

            scanned++

            // Log progress every 100 records
            if (scanned % 100 === 0) {
              console.log(`Scanned ${scanned}/${companyIds.length} companies, found ${duplicatesFound} duplicates`)
            }
          } catch (error) {
            console.error(`Error scanning company ${companyId}:`, error)
          }
        }
      }

      // Scan contacts for duplicates
      if (type === 'contact' || type === 'both') {
        let contactIds: number[] = []
        
        if (recordIds && recordIds.length > 0) {
          contactIds = recordIds
        } else {
          // Get all contact IDs
          const result = await client.query('SELECT contact_id FROM contacts ORDER BY contact_id')
          contactIds = result.rows.map(row => row.contact_id)
        }

        console.log(`Scanning ${contactIds.length} contacts for duplicates...`)

        for (const contactId of contactIds) {
          try {
            const matches = await DuplicateDetectionService.detectContactDuplicates(
              contactId,
              criteria as ContactMatchCriteria
            )

            for (const match of matches) {
              await DuplicateDetectionService.storeDuplicateRecord(match, 'contact')
              duplicatesFound++
              contactDuplicates++
            }

            scanned++

            // Log progress every 100 records
            if (scanned % 100 === 0) {
              console.log(`Scanned ${scanned}/${contactIds.length} contacts, found ${duplicatesFound} duplicates`)
            }
          } catch (error) {
            console.error(`Error scanning contact ${contactId}:`, error)
          }
        }
      }

      const processingTime = Date.now() - startTime

      return NextResponse.json({
        success: true,
        duplicates: {
          companies: companyDuplicates,
          contacts: contactDuplicates
        },
        stats: {
          scanned,
          duplicatesFound,
          processingTime,
          normalizationTime: normalizeFirst && !scanOnly ? normalizationTime : undefined
        }
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error in duplicate scan:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        success: false,
        duplicates: { companies: 0, contacts: 0 },
        stats: {
          scanned: 0,
          duplicatesFound: 0,
          processingTime: Date.now() - startTime
        }
      },
      { status: 500 }
    )
  }
}

// GET endpoint to check scan status or get recent scan results
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'company' | 'contact' | 'both'
    const limit = parseInt(searchParams.get('limit') || '10')

    const client = await pool.connect()

    try {
      let whereClause = ''
      const params: any[] = [limit]

      if (type && ['company', 'contact'].includes(type)) {
        whereClause = 'WHERE record_type = $2'
        params.push(type)
      }

      const result = await client.query(`
        SELECT 
          record_type,
          COUNT(*) as total_duplicates,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
          COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed,
          COUNT(CASE WHEN status = 'false_positive' THEN 1 END) as false_positives,
          AVG(confidence_score) as avg_confidence,
          MAX(created_at) as last_scan
        FROM duplicate_records 
        ${whereClause}
        GROUP BY record_type
        ORDER BY record_type
      `, params.slice(1)) // Remove limit from params for this query

      const recentDuplicates = await client.query(`
        SELECT dr.*, 
               CASE 
                 WHEN dr.record_type = 'company' THEN c.company_name
                 WHEN dr.record_type = 'contact' THEN ct.full_name
               END as primary_name,
               CASE 
                 WHEN dr.record_type = 'company' THEN c2.company_name
                 WHEN dr.record_type = 'contact' THEN ct2.full_name
               END as duplicate_name
        FROM duplicate_records dr
        LEFT JOIN companies c ON dr.record_type = 'company' AND dr.primary_record_id = c.company_id
        LEFT JOIN companies c2 ON dr.record_type = 'company' AND dr.duplicate_record_id = c2.company_id
        LEFT JOIN contacts ct ON dr.record_type = 'contact' AND dr.primary_record_id = ct.contact_id
        LEFT JOIN contacts ct2 ON dr.record_type = 'contact' AND dr.duplicate_record_id = ct2.contact_id
        ${whereClause}
        ORDER BY dr.created_at DESC
        LIMIT $1
      `, params)

      return NextResponse.json({
        success: true,
        summary: result.rows,
        recentDuplicates: recentDuplicates.rows
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error getting scan status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
