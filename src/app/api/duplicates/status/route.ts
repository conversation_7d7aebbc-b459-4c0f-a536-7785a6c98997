import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface UpdateStatusRequest {
  duplicateId: number
  status: 'pending' | 'confirmed' | 'false_positive' | 'merged' | 'blocked'
  resolvedBy: string
  notes?: string
}

export async function POST(request: NextRequest) {
  try {
    const { duplicateId, status, resolvedBy, notes }: UpdateStatusRequest = await request.json()

    if (!duplicateId || !status || !resolvedBy) {
      return NextResponse.json(
        { error: 'duplicateId, status, and resolvedBy are required' },
        { status: 400 }
      )
    }

    // Validate status
    const validStatuses = ['pending', 'confirmed', 'false_positive', 'merged', 'blocked']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      )
    }

    const client = await pool.connect()

    try {
      await client.query('BEGIN')

      // Check if the duplicate record exists
      const duplicateResult = await client.query(`
        SELECT * FROM duplicate_records WHERE id = $1
      `, [duplicateId])

      if (duplicateResult.rows.length === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Duplicate record not found' },
          { status: 404 }
        )
      }

      const duplicateRecord = duplicateResult.rows[0]

      // Update the duplicate record status
      const updateResult = await client.query(`
        UPDATE duplicate_records 
        SET 
          status = $1, 
          resolved_by = $2, 
          resolved_at = CASE 
            WHEN $1 IN ('confirmed', 'false_positive', 'merged', 'blocked') THEN NOW()
            ELSE NULL
          END
        WHERE id = $3
        RETURNING *
      `, [status, resolvedBy, duplicateId])

      if (updateResult.rows.length === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Failed to update duplicate record' },
          { status: 500 }
        )
      }

      const updatedRecord = updateResult.rows[0]

      // Log the status change
      console.log(`✅ Duplicate ${duplicateId} status updated to '${status}' by '${resolvedBy}'`)
      if (notes) {
        console.log(`📝 Notes: ${notes}`)
      }

      await client.query('COMMIT')

      return NextResponse.json({
        success: true,
        message: `Duplicate record status updated to '${status}'`,
        data: {
          id: updatedRecord.id,
          status: updatedRecord.status,
          resolvedBy: updatedRecord.resolved_by,
          resolvedAt: updatedRecord.resolved_at
        }
      })

    } catch (error) {
      console.error('=== ERROR IN STATUS UPDATE ENDPOINT ===')
      console.error('Error type:', error.constructor.name)
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error updating duplicate status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint to retrieve duplicate record status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const duplicateId = searchParams.get('duplicateId')

    if (!duplicateId) {
      return NextResponse.json(
        { error: 'duplicateId is required' },
        { status: 400 }
      )
    }

    const client = await pool.connect()

    try {
      const result = await client.query(`
        SELECT 
          id, 
          status, 
          resolved_by, 
          resolved_at
        FROM duplicate_records 
        WHERE id = $1
      `, [duplicateId])

      if (result.rows.length === 0) {
        return NextResponse.json(
          { error: 'Duplicate record not found' },
          { status: 404 }
        )
      }

      const record = result.rows[0]

      return NextResponse.json({
        success: true,
        data: {
          id: record.id,
          status: record.status,
          resolvedBy: record.resolved_by,
          resolvedAt: record.resolved_at
        }
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error retrieving duplicate status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
