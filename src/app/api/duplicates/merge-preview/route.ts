import { NextRequest, NextResponse } from 'next/server'
import { DuplicateResolutionService } from '@/lib/services/duplicateResolutionService'

interface MergePreviewRequest {
  primaryId: number
  duplicateId: number
  recordType: 'company' | 'contact'
}

// Simple in-memory cache for merge previews (in production, use Redis)
const previewCache = new Map<string, any>()
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

function getCacheKey(primaryId: number, duplicateId: number, recordType: string): string {
  return `${recordType}_${primaryId}_${duplicateId}`
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Merge preview API called with:', body)
    
    const { primaryId, duplicateId, recordType }: MergePreviewRequest = body

    if (!primaryId || !duplicateId || !recordType) {
      console.log('Missing required fields:', { primaryId, duplicateId, recordType })
      return NextResponse.json(
        { error: 'primaryId, duplicateId, and recordType are required' },
        { status: 400 }
      )
    }

    if (!['company', 'contact'].includes(recordType)) {
      console.log('Invalid record type:', recordType)
      return NextResponse.json(
        { error: 'recordType must be "company" or "contact"' },
        { status: 400 }
      )
    }

    // Check cache first
    const cacheKey = getCacheKey(primaryId, duplicateId, recordType)
    const cachedPreview = previewCache.get(cacheKey)
    
    if (cachedPreview && Date.now() - cachedPreview.timestamp < CACHE_TTL) {
      console.log('Returning cached merge preview')
      return NextResponse.json({
        success: true,
        preview: cachedPreview.data,
        cached: true
      })
    }

    console.log('Generating merge preview for:', { primaryId, duplicateId, recordType })
    const preview = await DuplicateResolutionService.generateMergePreview(
      primaryId,
      duplicateId,
      recordType
    )

    // Cache the result
    previewCache.set(cacheKey, {
      data: preview,
      timestamp: Date.now()
    })

    // Clean up old cache entries (keep only last 100 entries)
    if (previewCache.size > 100) {
      const entries = Array.from(previewCache.entries())
      entries.sort((a, b) => b[1].timestamp - a[1].timestamp)
      const toDelete = entries.slice(100)
      toDelete.forEach(([key]) => previewCache.delete(key))
    }

    console.log('Merge preview generated successfully')
    return NextResponse.json({
      success: true,
      preview,
      cached: false
    })

  } catch (error) {
    console.error('Error generating merge preview:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        success: false
      },
      { status: 500 }
    )
  }
}
