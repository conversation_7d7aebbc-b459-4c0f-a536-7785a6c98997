import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'
import { 
  MatchingCriteria, 
  ArticleMatch, 
  CriteriaSource, 
  findArticleMatches, 
  findArticleMatchesWithCapitalPosition,
  findArticleMatchesWithHierarchicalTiers,
  getTierDescription,
  hasInvestmentCriteria as checkHasInvestmentCriteria 
} from '@/lib/article-matching'

/**
 * MANDATORY CAPITAL POSITION FILTERING:
 * This route enforces capital position filtering for ALL deal matching operations.
 * Capital position is critical for proper deal categorization and must be present
 * in the matching criteria to ensure accurate article-to-company matching.
 * 
 * Capital positions include: Senior Debt, Stretch Senior, Mezzanine, Preferred Equity,
 * Common Equity, GP, Co-GP, JV, LP, Third Party, and Undetectable.
 */

/**
 * Extract company-level investment criteria from investment_criteria_central table
 */
function extractCompanyCriteria(companyCriteria: any[]): MatchingCriteria {
  const criteria: MatchingCriteria = {
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    countries: [],
    states: [],
    cities: [],
    loan_types: [],
    capital_positions: [],
    min_deal_size: null,
    max_deal_size: null,
  };

  // Process company-level investment criteria from investment_criteria_central
  companyCriteria.forEach(row => {
    if (row.entity_type === 'company') {
      // Parse PostgreSQL arrays
      if (row.country) {
        const countries = parsePostgresArray(row.country);
        criteria.countries.push(...countries);
      }
      
      if (row.state) {
        const states = parsePostgresArray(row.state);
        criteria.states.push(...states);
      }
      
      if (row.city) {
        const cities = parsePostgresArray(row.city);
        criteria.cities.push(...cities);
      }
      
      if (row.capital_position) {
        criteria.capital_positions.push(row.capital_position);
      }
      
      if (row.property_types) {
        const propertyTypes = parsePostgresArray(row.property_types);
        criteria.property_types.push(...propertyTypes);
      }
      
      // Handle deal sizes - stored as raw dollar amounts in database 
      if (row.minimum_deal_size && !isNaN(parseFloat(row.minimum_deal_size))) {
        const minSize = parseFloat(row.minimum_deal_size); // Keep as raw dollar amount
        if (!criteria.min_deal_size || minSize < criteria.min_deal_size) {
          criteria.min_deal_size = minSize;
        }
      }
      
      if (row.maximum_deal_size && !isNaN(parseFloat(row.maximum_deal_size))) {
        const maxSize = parseFloat(row.maximum_deal_size); // Keep as raw dollar amount
        if (!criteria.max_deal_size || maxSize > criteria.max_deal_size) {
          criteria.max_deal_size = maxSize;
        }
      }
    }
  });

  // Remove duplicates
  (Object.keys(criteria) as Array<keyof MatchingCriteria>).forEach(key => {
    const value = criteria[key];
    if (Array.isArray(value)) {
      criteria[key] = Array.from(new Set(value)) as any;
    }
  });

  return criteria;
}

/**
 * Extract location-based criteria as fallback using enriched company data
 */
function extractLocationCriteria(company: any): MatchingCriteria {
  const criteria: MatchingCriteria = {
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    countries: [],
    states: [],
    cities: [],
    loan_types: [],
    capital_positions: [],
    min_deal_size: null,
    max_deal_size: null,
  };

  // Priority 1: Company headquarters location (from CompanyOverviewProcessorV2)
  if (company.headquarters_state) criteria.states.push(company.headquarters_state);
  if (company.headquarters_city) criteria.cities.push(company.headquarters_city);
  if (company.headquarters_country) criteria.countries.push(company.headquarters_country);
  
  // Priority 2: Additional company locations
  if (criteria.states.length === 0 && criteria.cities.length === 0 && criteria.countries.length === 0) {
    if (company.additional_state) criteria.states.push(company.additional_state);
    if (company.additional_city) criteria.cities.push(company.additional_city);
    if (company.additional_country) criteria.countries.push(company.additional_country);
    
    // Priority 3: Parse office locations array
    if (criteria.states.length === 0 && criteria.cities.length === 0 && criteria.countries.length === 0) {
      if (company.office_locations) {
        const officeLocations = parsePostgresArray(company.office_locations);
        officeLocations.forEach((office: string) => {
          if (typeof office === 'string') {
            const parts = office.split(',').map((part: string) => part.trim());
            if (parts.length >= 2) {
              const city = parts[0];
              const state = parts[1];
              const country = parts[2] || 'United States';
              
              criteria.cities.push(city);
              criteria.states.push(state);
              criteria.countries.push(country);
            }
          }
        });
      }
    }
  }

  // Remove duplicates
  (Object.keys(criteria) as Array<keyof MatchingCriteria>).forEach(key => {
    const value = criteria[key];
    if (Array.isArray(value)) {
      criteria[key] = Array.from(new Set(value)) as any;
    }
  });

  return criteria;
}

/**
 * Fetch and validate capital positions from company's investment criteria
 * This ensures we only filter by capital positions the company actually invests in
 */
async function fetchCompanyCapitalPositions(companyId: number): Promise<string[]> {
  try {
    // Query investment criteria for company to get actual capital positions
    const capitalPositionQuery = `
      SELECT DISTINCT capital_position
      FROM investment_criteria_central
      WHERE entity_id = $1 AND entity_type = 'company'
      AND capital_position IS NOT NULL
      AND capital_position != ''
      ORDER BY capital_position
    `;
    
    const result = await pool.query(capitalPositionQuery, [companyId]);
    
    if (result.rows.length === 0) {
      // If no specific capital positions found, return empty array
      // This will trigger fallback to location-based matching only
      return [];
    }
    
    // Extract unique capital positions
    const capitalPositions = result.rows
      .map(row => row.capital_position)
      .filter((position): position is string => 
        position && typeof position === 'string' && position.trim() !== ''
      );
    
    return [...new Set(capitalPositions)]; // Remove duplicates
  } catch (error) {
    console.error('Error fetching company capital positions:', error);
    return [];
  }
}

/**
 * Ensure capital position filtering is mandatory for deal matching
 * Uses actual capital positions from company's investment criteria
 */
async function enforceCapitalPositionFiltering(criteria: MatchingCriteria, companyId: number): Promise<MatchingCriteria> {
  // Fetch actual capital positions from company's investment criteria
  const companyCapitalPositions = await fetchCompanyCapitalPositions(companyId);
  
  if (companyCapitalPositions.length > 0) {
    // Use the company's actual capital positions for filtering
    criteria.capital_positions = companyCapitalPositions;
  } else {
    // If no capital positions found, this company cannot do deal matching
    // They will fall back to location-based matching only
    criteria.capital_positions = [];
  }
  
  return criteria;
}

/**
 * Parse PostgreSQL array format {item1,item2,item3} to JavaScript array
 */
function parsePostgresArray(pgArray: string | string[]): string[] {
  if (Array.isArray(pgArray)) {
    return pgArray;
  }
  
  if (typeof pgArray !== 'string') {
    return [];
  }
  
  if (pgArray === '{}' || !pgArray) {
    return [];
  }
  
  try {
    // Remove outer braces and split by comma
    const cleaned = pgArray.replace(/^{|}$/g, '');
    if (!cleaned) {
      return [];
    }
    
    // Handle quoted values and split
    const items = cleaned.split(',').map(item => {
      return item.trim().replace(/^"|"$/g, ''); // Remove quotes
    });
    
    return items.filter(item => item.length > 0);
  } catch (error) {
    console.error('Error parsing PostgreSQL array:', pgArray, error);
    return [];
  }
}


export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const { companyId } = await params;

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Get enriched company data with comprehensive fields
    const companyQuery = `
      SELECT 
        -- Basic company info
        c.company_id,
        c.company_name,
        c.industry,
        c.company_city,
        c.company_state,
        c.company_country,
        c.company_address,
        c.company_website,
        c.founded_year,
        
        -- Company V2 enriched data (from CompanyOverviewProcessorV2)
        c.investment_focus,
        c.investment_strategy_mission,
        c.investment_strategy_approach,
        c.company_phone as main_phone,
        c.secondary_phone,
        c.main_email,
        c.secondary_email,
        c.company_linkedin,
        c.twitter as company_twitter,
        c.facebook as company_facebook,
        c.instagram as company_instagram,
        c.youtube as company_youtube,
        c.company_address as headquarters_address,
        c.company_city as headquarters_city,
        c.company_state as headquarters_state,
        c.company_zip as headquarters_zipcode,
        c.company_country as headquarters_country,
        c.additional_address,
        c.additional_city,
        c.additional_state,
        c.additional_zipcode,
        c.additional_country,
        c.office_locations,
        c.fund_size,
        c.aum,
        c.number_of_properties,
        c.number_of_offices,
        c.number_of_employees,
        c.annual_revenue,
        c.net_income,
        c.ebitda,
        c.profit_margin,
        c.market_capitalization,
        c.market_share_percentage,
        c.balance_sheet_strength,
        c.funding_sources,
        c.recent_capital_raises,
        c.typical_debt_to_equity_ratio,
        c.development_fee_structure,
        c.credit_rating,
        c.dry_powder,
        c.annual_deployment_target,
        c.investment_vehicle_type,
        c.active_fund_name_series,
        c.fund_size_active_fund,
        c.fundraising_status,
        c.lender_type,
        c.annual_loan_volume,
        c.lending_origin,
        c.portfolio_health,
        c.partnerships,
        c.key_equity_partners,
        c.key_debt_partners,
        c.board_of_directors,
        c.key_executives,
        c.founder_background,
        c.market_cycle_positioning,
        c.urban_vs_suburban_preference,
        c.sustainability_esg_focus,
        c.technology_proptech_adoption,
        c.adaptive_reuse_experience,
        c.regulatory_zoning_expertise,
        c.corporate_structure,
        c.parent_company,
        c.subsidiaries,
        c.stock_ticker_symbol,
        c.stock_exchange,
        c.products_services_description,
        c.target_customer_profile,
        c.major_competitors,
        c.unique_selling_proposition,
        c.industry_awards_recognitions,
        c.company_history,
        c.transactions_completed_last_12m,
        c.total_transaction_volume_ytd,
        c.deal_count_ytd,
        c.average_deal_size,
        c.portfolio_size_sqft,
        c.portfolio_asset_count,
        c.role_in_previous_deal,
        c.internal_relationship_manager,
        c.last_contact_date,
        c.pipeline_status,
        c.recent_news_sentiment,
        c.data_source,
        c.data_confidence_score,
        c.quarterly_earnings_link,
        
        -- Company processing status fields
        c.overview_v2_status,
        c.investment_criteria_status,
        c.website_scraping_status
      FROM companies c
      WHERE c.company_id = $1
    `;

    const companyResult = await pool.query(companyQuery, [companyId]);

    if (companyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      );
    }
    
    const company = companyResult.rows[0];

    // Get investment criteria from investment_criteria_central table (populated by processors)
    const criteriaQuery = `
      SELECT 
        entity_type,
        capital_position,
        minimum_deal_size,
        maximum_deal_size,
        country,
        region,
        state,
        city,
        property_types,
        property_subcategories,
        strategies,
        decision_making_process,
        notes,
        created_at
      FROM investment_criteria_central
      WHERE (entity_id = $1 AND entity_type = 'company')
      ORDER BY created_at DESC
    `;
    
    const criteriaResult = await pool.query(criteriaQuery, [companyId]);

    let searchResult: {
      matches: ArticleMatch[],
      searchTier: 1 | 2 | 3 | 4 | 5,
      tierDescription: string,
      isPremium: boolean
    };
    let criteriaSource: CriteriaSource | null = null;

    // Step 1: Try company-level investment criteria with new 5-tier hierarchical search
    const companyCriteria = extractCompanyCriteria(criteriaResult.rows);
    if (checkHasInvestmentCriteria(companyCriteria)) {
      const matches = await findArticleMatchesWithHierarchicalTiers(companyCriteria, true, false);
      const tierCounts = matches.reduce((acc, match) => {
        acc[match.searchTier || 5] = (acc[match.searchTier || 5] || 0) + 1;
        return acc;
      }, {} as Record<number, number>);
      
      const primaryTier = Math.min(...Object.keys(tierCounts).map(Number)) as 1 | 2 | 3 | 4 | 5;
      
      searchResult = {
        matches: matches, // Return all matches from all tiers
        searchTier: primaryTier,
        tierDescription: getTierDescription(primaryTier, matches.find(m => m.searchTier === primaryTier)?.isPremium),
        isPremium: matches.find(m => m.searchTier === primaryTier)?.isPremium || false
      };
      criteriaSource = {
        source: 'company',
        description: `${searchResult.tierDescription} using company-level investment criteria from CompanyInvestmentCriteriaProcessor`,
        criteria: companyCriteria,
        hasInvestmentCriteria: true
      };
    } else {
      // Step 2: Use location fallback with new 5-tier hierarchical search
      const locationCriteria = extractLocationCriteria(company);
      if (locationCriteria.states.length > 0 || locationCriteria.cities.length > 0 || locationCriteria.countries.length > 0) {
        const matches = await findArticleMatchesWithHierarchicalTiers(locationCriteria, false, true);
        const tierCounts = matches.reduce((acc, match) => {
          acc[match.searchTier || 5] = (acc[match.searchTier || 5] || 0) + 1;
          return acc;
        }, {} as Record<number, number>);
        
        const primaryTier = Math.min(...Object.keys(tierCounts).map(Number)) as 1 | 2 | 3 | 4 | 5;
        
        searchResult = {
          matches: matches, // Return all matches from all tiers
          searchTier: primaryTier,
          tierDescription: getTierDescription(primaryTier, matches.find(m => m.searchTier === primaryTier)?.isPremium),
          isPremium: matches.find(m => m.searchTier === primaryTier)?.isPremium || false
        };
        criteriaSource = {
          source: 'location',
          description: `${searchResult.tierDescription} using location-based fallback criteria from enriched company data`,
          criteria: locationCriteria,
          hasInvestmentCriteria: false
        };
      } else {
        // No criteria available
        searchResult = {
          matches: [],
          searchTier: 5,
          tierDescription: 'No matching criteria available',
          isPremium: false
        };
      }
    }

    // If no criteria source was determined, return empty results
    if (!criteriaSource) {
      const response: any = {
        company: {
          company_id: company.company_id,
          company_name: company.company_name
        },
        matches: [],
        criteria_source: 'none',
        criteria_description: 'No investment criteria or location data available',
        matching_criteria: {
          used_criteria: {
            property_types: [],
            property_sub_categories: [],
            strategies: [],
            countries: [],
            states: [],
            cities: [],
            loan_types: [],
            capital_positions: [],
            min_deal_size: null,
            max_deal_size: null,
          },
          has_investment_criteria: false,
          property_types: [],
          property_subcategories: [],
          strategies: [],
          deal_size_range: { min: null, max: null },
          locations: { states: [], cities: [], countries: [] }
        },
        total_matches: 0,
        search_tier: 5,
        tier_description: 'No matching criteria available',
        is_premium: false,
        processing_info: {
          overview_v2_status: company.overview_v2_status,
          investment_criteria_status: company.investment_criteria_status,
          website_scraping_status: company.website_scraping_status
        }
      };

      return NextResponse.json(response);
    }

    // Return successful results with enhanced criteria source info and hierarchical search details
    const response: any = {
      company: {
        company_id: company.company_id,
        company_name: company.company_name,
        industry: company.industry,
        founded_year: company.founded_year,
        company_website: company.company_website
      },
      matches: searchResult.matches.slice(0, 50), // Limit to top 50 matches
      criteria_source: criteriaSource.source,
      criteria_description: criteriaSource.description,
      search_tier: searchResult.searchTier,
      tier_description: searchResult.tierDescription,
      is_premium: searchResult.isPremium,
      premium_count: searchResult.matches.filter(match => match.isPremium).length,
      matching_criteria: {
        used_criteria: criteriaSource.criteria,
        has_investment_criteria: criteriaSource.hasInvestmentCriteria,
        // Detailed breakdown for UI display
        property_types: criteriaSource.criteria.property_types,
        property_subcategories: criteriaSource.criteria.property_sub_categories,
        strategies: criteriaSource.criteria.strategies,
        deal_size_range: {
          min: criteriaSource.criteria.min_deal_size ? `$${(criteriaSource.criteria.min_deal_size / 1000000).toFixed(1)}M` : null,
          max: criteriaSource.criteria.max_deal_size ? `$${(criteriaSource.criteria.max_deal_size / 1000000).toFixed(1)}M` : null
        },
        locations: {
          states: criteriaSource.criteria.states,
          cities: criteriaSource.criteria.cities,
          countries: criteriaSource.criteria.countries
        },
        capital_positions: {
          positions: criteriaSource.criteria.capital_positions,
          count: criteriaSource.criteria.capital_positions.length,
          source: criteriaSource.source === 'company' ? 'Company Investment Criteria' : 'Location Fallback'
        }
      },
      total_matches: searchResult.matches.length,
      processing_info: {
        data_sources: [
          'CompanyOverviewProcessorV2',
          'CompanyInvestmentCriteriaProcessor'
        ],
        enriched_fields_used: [
          ...(company.investment_focus ? ['investment_focus'] : []),
          ...(company.office_locations ? ['office_locations'] : []),
          ...(company.headquarters_city ? ['headquarters_location'] : []),
          ...(company.company_city ? ['company_location'] : [])
        ],
        hierarchical_search: {
          applied: true,
          search_tier: searchResult.searchTier,
          tier_description: searchResult.tierDescription,
          is_premium: searchResult.isPremium
        }
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error matching articles for company:', error);
    
    // Provide more specific error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      { 
        error: 'Failed to match articles for company',
        details: errorMessage,
        suggestion: 'Check if company exists and has been processed by enrichment processors'
      },
      { status: 500 }
    );
  }
}
