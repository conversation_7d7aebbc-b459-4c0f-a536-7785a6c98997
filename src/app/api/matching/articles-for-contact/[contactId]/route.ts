import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import { 
  MatchingCriteria, 
  ArticleMatch, 
  CriteriaSource, 
  findArticleMatches, 
  findArticleMatchesWithCapitalPosition,
  findArticleMatchesWithHierarchicalTiers,
  getTierDescription,
  hasInvestmentCriteria as checkHasInvestmentCriteria 
} from '@/lib/article-matching';

/**
 * Extract contact-level investment criteria from investment_criteria_central table
 * and combine with location data from contact/company tables
 */
function extractContactCriteria(contactCriteria: any[], contact?: any): MatchingCriteria {
  
  const debugInfo = {
    contactPassed: !!contact,
    hasContactData: contact ? 'has contact data' : 'no contact data',
    contactLocationFields: contact ? {
      contact_city: contact.contact_city,
      contact_state: contact.contact_state,
      contact_country: contact.contact_country
    } : null
  };
  
  // Store debug info globally for API response
  (global as any).extractContactCriteriaDebug = debugInfo;
  
  const debugSteps: any[] = [];
  const criteria: MatchingCriteria = {
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    countries: [],
    states: [],
    cities: [],
    loan_types: [],
    capital_positions: [],
    min_deal_size: null,
    max_deal_size: null,
  };

  // Process contact-level investment criteria from investment_criteria_central
  contactCriteria.forEach(row => {
    if (row.entity_type === 'contact') {
      // Parse PostgreSQL arrays
      if (row.country) {
        const countries = parsePostgresArray(row.country);
        criteria.countries.push(...countries);
      }
      
      if (row.state) {
        const states = parsePostgresArray(row.state);
        criteria.states.push(...states);
      }
      
      if (row.city) {
        const cities = parsePostgresArray(row.city);
        criteria.cities.push(...cities);
      }
      
      if (row.capital_position) {
        criteria.capital_positions.push(row.capital_position);
      }

      if (row.property_types) {
        const propertyTypes = parsePostgresArray(row.property_types);
        criteria.property_types.push(...propertyTypes);
      }
      
      // Handle deal sizes - stored as raw dollar amounts in database 
      if (row.minimum_deal_size && !isNaN(parseFloat(row.minimum_deal_size))) {
        const minSize = parseFloat(row.minimum_deal_size); // Keep as raw dollar amount
        if (!criteria.min_deal_size || minSize < criteria.min_deal_size) {
          criteria.min_deal_size = minSize;
        }
      }
      
      if (row.maximum_deal_size && !isNaN(parseFloat(row.maximum_deal_size))) {
        const maxSize = parseFloat(row.maximum_deal_size); // Keep as raw dollar amount
        if (!criteria.max_deal_size || maxSize > criteria.max_deal_size) {
          criteria.max_deal_size = maxSize;
        }
      }
    }
  });

  // Add contact location data if available (this was missing!)
  if (contact) {
    if (contact.contact_state && !criteria.states.includes(contact.contact_state)) {
      criteria.states.push(contact.contact_state);
    }
    if (contact.contact_city && !criteria.cities.includes(contact.contact_city)) {
      criteria.cities.push(contact.contact_city);
    }
    if (contact.contact_country && !criteria.countries.includes(contact.contact_country)) {
      criteria.countries.push(contact.contact_country);
    }
    
    // Also add company headquarters location
    if (contact.headquarters_state && !criteria.states.includes(contact.headquarters_state)) {
      criteria.states.push(contact.headquarters_state);
    }
    if (contact.headquarters_city && !criteria.cities.includes(contact.headquarters_city)) {
      criteria.cities.push(contact.headquarters_city);
    }
    if (contact.headquarters_country && !criteria.countries.includes(contact.headquarters_country)) {
      criteria.countries.push(contact.headquarters_country);
    }
  }

  // Remove duplicates
  (Object.keys(criteria) as Array<keyof MatchingCriteria>).forEach(key => {
    const value = criteria[key];
    if (Array.isArray(value)) {
      criteria[key] = Array.from(new Set(value)) as any;
    }
  });

  return criteria;
}

/**
 * Extract company-level investment criteria from investment_criteria_central table and enriched company data
 */
function extractCompanyCriteria(contact: any, companyCriteria: any[]): MatchingCriteria {
  const criteria: MatchingCriteria = {
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    countries: [],
    states: [],
    cities: [],
    loan_types: [],
    capital_positions: [],
    min_deal_size: null,
    max_deal_size: null,
  };

  // Process company-level investment criteria from investment_criteria_central
  companyCriteria.forEach(row => {
    if (row.entity_type === 'company') {
      // Parse PostgreSQL arrays
      
      if (row.country) {
        const countries = parsePostgresArray(row.country);
        criteria.countries.push(...countries);
      }
      
      if (row.state) {
        const states = parsePostgresArray(row.state);
        criteria.states.push(...states);
      }
      
      if (row.city) {
        const cities = parsePostgresArray(row.city);
        criteria.cities.push(...cities);
      }
      
      if (row.capital_position) {
        criteria.capital_positions.push(row.capital_position);
      }
      
      // Handle deal sizes - stored as raw dollar amounts in database 
      if (row.minimum_deal_size && !isNaN(parseFloat(row.minimum_deal_size))) {
        const minSize = parseFloat(row.minimum_deal_size); // Keep as raw dollar amount
        if (!criteria.min_deal_size || minSize < criteria.min_deal_size) {
          criteria.min_deal_size = minSize;
        }
      }
      
      if (row.maximum_deal_size && !isNaN(parseFloat(row.maximum_deal_size))) {
        const maxSize = parseFloat(row.maximum_deal_size); // Keep as raw dollar amount
        if (!criteria.max_deal_size || maxSize > criteria.max_deal_size) {
          criteria.max_deal_size = maxSize;
        }
      }
    }
  });

  // Add criteria from enriched company data (from CompanyOverviewProcessorV2)
  if (contact.investment_focus) {
    const investmentFocus = parsePostgresArray(contact.investment_focus);
    criteria.property_types.push(...investmentFocus);
  }

  // Add contact and company location data if available (this was missing!)
  if (contact) {
    if (contact.contact_state && !criteria.states.includes(contact.contact_state)) {
      criteria.states.push(contact.contact_state);
    }
    if (contact.contact_city && !criteria.cities.includes(contact.contact_city)) {
      criteria.cities.push(contact.contact_city);
    }
    if (contact.contact_country && !criteria.countries.includes(contact.contact_country)) {
      criteria.countries.push(contact.contact_country);
    }
    
    // Also add company headquarters location
    if (contact.headquarters_state && !criteria.states.includes(contact.headquarters_state)) {
      criteria.states.push(contact.headquarters_state);
    }
    if (contact.headquarters_city && !criteria.cities.includes(contact.headquarters_city)) {
      criteria.cities.push(contact.headquarters_city);
    }
    if (contact.headquarters_country && !criteria.countries.includes(contact.headquarters_country)) {
      criteria.countries.push(contact.headquarters_country);
    }
  }

  // Remove duplicates
  (Object.keys(criteria) as Array<keyof MatchingCriteria>).forEach(key => {
    const value = criteria[key];
    if (Array.isArray(value)) {
      criteria[key] = Array.from(new Set(value)) as any;
    }
  });

  return criteria;
}

/**
 * Extract location-based criteria as fallback using enriched contact and company data
 */
function extractLocationCriteria(contact: any): MatchingCriteria {
  const criteria: MatchingCriteria = {
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    countries: [],
    states: [],
    cities: [],
    loan_types: [],
    capital_positions: [],
    min_deal_size: null,
    max_deal_size: null,
  };

  // Priority 1: Contact location (from ContactEnrichmentProcessorV2)
  if (contact.contact_state) criteria.states.push(contact.contact_state);
  if (contact.contact_city) criteria.cities.push(contact.contact_city);
  if (contact.contact_country) criteria.countries.push(contact.contact_country);
  
  // Priority 2: Company headquarters location (from CompanyOverviewProcessorV2)
  if (criteria.states.length === 0 && criteria.cities.length === 0 && criteria.countries.length === 0) {
    if (contact.headquarters_state) criteria.states.push(contact.headquarters_state);
    if (contact.headquarters_city) criteria.cities.push(contact.headquarters_city);
    if (contact.headquarters_country) criteria.countries.push(contact.headquarters_country);
    
    // Priority 3: Additional company locations
    if (criteria.states.length === 0 && criteria.cities.length === 0 && criteria.countries.length === 0) {
      if (contact.additional_state) criteria.states.push(contact.additional_state);
      if (contact.additional_city) criteria.cities.push(contact.additional_city);
      if (contact.additional_country) criteria.countries.push(contact.additional_country);
      
      // Priority 4: Parse office locations array
      if (criteria.states.length === 0 && criteria.cities.length === 0 && criteria.countries.length === 0) {
        if (contact.office_locations) {
          const officeLocations = parsePostgresArray(contact.office_locations);
          officeLocations.forEach((office: string) => {
            if (typeof office === 'string') {
              const parts = office.split(',').map((part: string) => part.trim());
              if (parts.length >= 2) {
                const city = parts[0];
                const state = parts[1];
                const country = parts[2] || 'United States';
                
                criteria.cities.push(city);
                criteria.states.push(state);
                criteria.countries.push(country);
              }
            }
          });
        }
      }
    }
  }

  const debugSteps: any[] = [];

  // Add location data from contact/company tables (prioritize specific location data)
  if (contact) {
    // Always add specific location data from contact/company tables for better matching
    // Priority 1: Contact location (from ContactEnrichmentProcessorV2)
    debugSteps.push({
      step: 'before_contact_location',
      contact_state: contact.contact_state,
      contact_city: contact.contact_city,
      contact_country: contact.contact_country,
      current_states: [...criteria.states],
      current_cities: [...criteria.cities],
      current_countries: [...criteria.countries]
    });
    
    if (contact.contact_state && !criteria.states.includes(contact.contact_state)) {
      criteria.states.push(contact.contact_state);
      debugSteps.push({ step: 'added_contact_state', value: contact.contact_state });
    }
    if (contact.contact_city && !criteria.cities.includes(contact.contact_city)) {
      criteria.cities.push(contact.contact_city);
      debugSteps.push({ step: 'added_contact_city', value: contact.contact_city });
    }
    if (contact.contact_country && !criteria.countries.includes(contact.contact_country)) {
      criteria.countries.push(contact.contact_country);
      debugSteps.push({ step: 'added_contact_country', value: contact.contact_country });
    }
    
    debugSteps.push({
      step: 'after_contact_location',
      states: [...criteria.states],
      cities: [...criteria.cities],
      countries: [...criteria.countries]
    });
    
    // Priority 2: Company headquarters location (from CompanyOverviewProcessorV2)
    if (contact.headquarters_state && !criteria.states.includes(contact.headquarters_state)) {
      criteria.states.push(contact.headquarters_state);
    }
    if (contact.headquarters_city && !criteria.cities.includes(contact.headquarters_city)) {
      criteria.cities.push(contact.headquarters_city);
    }
    if (contact.headquarters_country && !criteria.countries.includes(contact.headquarters_country)) {
      criteria.countries.push(contact.headquarters_country);
    }
    
    // Priority 3: Additional company locations
    if (contact.additional_state && !criteria.states.includes(contact.additional_state)) {
      criteria.states.push(contact.additional_state);
    }
    if (contact.additional_city && !criteria.cities.includes(contact.additional_city)) {
      criteria.cities.push(contact.additional_city);
    }
    if (contact.additional_country && !criteria.countries.includes(contact.additional_country)) {
      criteria.countries.push(contact.additional_country);
    }
    
    // Priority 4: Parse office locations array
    if (contact.office_locations) {
      const officeLocations = parsePostgresArray(contact.office_locations);
      officeLocations.forEach((office: string) => {
        if (typeof office === 'string') {
          const parts = office.split(',').map((part: string) => part.trim());
          if (parts.length >= 2) {
            const city = parts[0];
            const state = parts[1];
            const country = parts[2] || 'United States';
            
            if (!criteria.cities.includes(city)) {
              criteria.cities.push(city);
            }
            if (!criteria.states.includes(state)) {
              criteria.states.push(state);
            }
            if (!criteria.countries.includes(country)) {
              criteria.countries.push(country);
            }
          }
        }
      });
    }
  } else {
    console.log('No contact object passed to extractLocationCriteria');
  }

  // Store debug steps globally for API response
  (global as any).extractContactCriteriaDebugSteps = debugSteps;

  // Remove duplicates
  (Object.keys(criteria) as Array<keyof MatchingCriteria>).forEach(key => {
    const value = criteria[key];
    if (Array.isArray(value)) {
      criteria[key] = Array.from(new Set(value)) as any;
    }
  });

  return criteria;
}

/**
 * Parse PostgreSQL array format {item1,item2,item3} to JavaScript array
 */
function parsePostgresArray(pgArray: string | string[]): string[] {
  if (Array.isArray(pgArray)) {
    return pgArray;
  }
  
  if (typeof pgArray !== 'string') {
    return [];
  }
  
  if (pgArray === '{}' || !pgArray) {
    return [];
  }
  
  try {
    // Remove outer braces and split by comma
    const cleaned = pgArray.replace(/^{|}$/g, '');
    if (!cleaned) {
      return [];
    }
    
    // Handle quoted values and split
    const items = cleaned.split(',').map(item => {
      return item.trim().replace(/^"|"$/g, ''); // Remove quotes
    });
    
    return items.filter(item => item.length > 0);
  } catch (error) {
    console.error('Error parsing PostgreSQL array:', pgArray, error);
    return [];
  }
}

/**
 * Fetch and validate capital positions from contact's investment criteria
 * This ensures we only filter by capital positions the contact actually invests in
 */
async function fetchContactCapitalPositions(contactId: number, companyId: number | null): Promise<string[]> {
  try {
    // Query investment criteria for both contact and company to get actual capital positions
    const capitalPositionQuery = `
      SELECT DISTINCT capital_position
      FROM investment_criteria_central
      WHERE (entity_id = $1 AND entity_type = 'contact')
      AND capital_position IS NOT NULL
      AND capital_position != ''
      ORDER BY capital_position
    `;
    
    const result = await pool.query(capitalPositionQuery, [contactId]);
    
    if (result.rows.length === 0) {
      // If no specific capital positions found, return empty array
      // This will trigger fallback to location-based matching only
      return [];
    }
    
    // Extract unique capital positions
    const capitalPositions = result.rows
      .map(row => row.capital_position)
      .filter((position): position is string => 
        position && typeof position === 'string' && position.trim() !== ''
      );
    
    return [...new Set(capitalPositions)]; // Remove duplicates
  } catch (error) {
    console.error('Error fetching contact capital positions:', error);
    return [];
  }
}

/**
 * Hierarchical article search function with premium tagging for contacts
 * Tier 1 (Premium): capital position + property type + deal size + locations
 * Tier 2: capital position + deal size + locations  
 * Tier 3: property type + deal size + locations
 * Tier 4: only locations
 */
async function performHierarchicalContactArticleSearch(
  baseCriteria: MatchingCriteria, 
  contactId: number,
  companyId: number | null,
  hasInvestmentCriteria: boolean
): Promise<{
  matches: ArticleMatch[],
  searchTier: 1 | 2 | 3 | 4,
  tierDescription: string,
  isPremium: boolean
}> {
  
  // Get contact capital positions first
  const contactCapitalPositions = await fetchContactCapitalPositions(contactId, companyId);
  
  // Tier 1 (Premium): Full criteria matching with capital position + property type + deal size + locations
  if (hasInvestmentCriteria && 
      contactCapitalPositions.length > 0 &&
      baseCriteria.property_types.length > 0 && 
      (baseCriteria.min_deal_size || baseCriteria.max_deal_size) &&
      (baseCriteria.states.length > 0 || baseCriteria.cities.length > 0 || baseCriteria.countries.length > 0)) {
    
    const tier1Criteria = {
      ...baseCriteria,
      capital_positions: contactCapitalPositions
    };
    
    const tier1Matches = await findArticleMatchesWithCapitalPosition(tier1Criteria, true, false);
    if (tier1Matches.length > 0) {
      // Mark as premium
      tier1Matches.forEach(match => {
        match.isPremium = true;
        match.searchTier = 1;
      });
      
      return {
        matches: tier1Matches,
        searchTier: 1,
        tierDescription: 'Premium Match: Capital Position + Property Type + Deal Size + Location',
        isPremium: true
      };
    }
  }
  
  // Tier 2: capital position + deal size + locations (no property type requirement)
  if (contactCapitalPositions.length > 0 &&
      (baseCriteria.min_deal_size || baseCriteria.max_deal_size) &&
      (baseCriteria.states.length > 0 || baseCriteria.cities.length > 0 || baseCriteria.countries.length > 0)) {
    
    const tier2Criteria = {
      ...baseCriteria,
      capital_positions: contactCapitalPositions,
      property_types: [] // Remove property type requirement
    };
    
    const tier2Matches = await findArticleMatchesWithCapitalPosition(tier2Criteria, hasInvestmentCriteria, false);
    if (tier2Matches.length > 0) {
      tier2Matches.forEach(match => {
        match.isPremium = false;
        match.searchTier = 2;
      });
      
      return {
        matches: tier2Matches,
        searchTier: 2,
        tierDescription: 'Capital Position + Deal Size + Location Match',
        isPremium: false
      };
    }
  }
  
  // Tier 3: property type + deal size + locations (no capital position requirement)
  if (hasInvestmentCriteria &&
      baseCriteria.property_types.length > 0 &&
      (baseCriteria.min_deal_size || baseCriteria.max_deal_size) &&
      (baseCriteria.states.length > 0 || baseCriteria.cities.length > 0 || baseCriteria.countries.length > 0)) {
    
    const tier3Criteria = {
      ...baseCriteria,
      capital_positions: [] // Remove capital position requirement
    };
    
    const tier3Matches = await findArticleMatchesWithCapitalPosition(tier3Criteria, hasInvestmentCriteria, false);
    if (tier3Matches.length > 0) {
      tier3Matches.forEach(match => {
        match.isPremium = false;
        match.searchTier = 3;
      });
      
      return {
        matches: tier3Matches,
        searchTier: 3,
        tierDescription: 'Property Type + Deal Size + Location Match',
        isPremium: false
      };
    }
  }
  
  // Tier 4: only locations (fallback)
  if (baseCriteria.states.length > 0 || baseCriteria.cities.length > 0 || baseCriteria.countries.length > 0) {
    const tier4Criteria = {
      ...baseCriteria,
      capital_positions: [],
      property_types: [],
      min_deal_size: null,
      max_deal_size: null
    };
    
    const tier4Matches = await findArticleMatchesWithCapitalPosition(tier4Criteria, false, true);
    if (tier4Matches.length > 0) {
      tier4Matches.forEach(match => {
        match.isPremium = false;
        match.searchTier = 4;
      });
      
      return {
        matches: tier4Matches,
        searchTier: 4,
        tierDescription: 'Location-only Match (Fallback)',
        isPremium: false
      };
    }
  }
  
  // No matches found
  return {
    matches: [],
    searchTier: 4,
    tierDescription: 'No matching articles found',
    isPremium: false
  };
}

/**
 * Ensure capital position filtering is mandatory for deal matching
 * Uses actual capital positions from contact's investment criteria
 */
async function enforceCapitalPositionFiltering(
  criteria: MatchingCriteria, 
  contactId: number, 
  companyId: number | null
): Promise<MatchingCriteria> {
  // Fetch actual capital positions from contact's investment criteria
  const contactCapitalPositions = await fetchContactCapitalPositions(contactId, companyId);
  
  if (contactCapitalPositions.length > 0) {
    // Use the contact's actual capital positions for filtering
    criteria.capital_positions = contactCapitalPositions;
  } else {
    // If no capital positions found, this contact cannot do deal matching
    // They will fall back to location-based matching only
    criteria.capital_positions = [];
  }
  
  return criteria;
}

export async function GET(request: NextRequest, { params }: { params: Promise<{ contactId: string }> }) {
  try {
    const { contactId: contactIdParam } = await params;
    const contactId = parseInt(contactIdParam);
    if (isNaN(contactId)) {
      return NextResponse.json({ error: 'Invalid contact ID' }, { status: 400 });
    }

    // Get enriched contact data with company information
    const contactQuery = `
      SELECT 
        -- Basic contact info
        c.contact_id,
        c.company_id,
        c.first_name,
        c.last_name,
        c.email,
        c.title,
        c.linkedin_url,
        c.phone_number,
        c.contact_city,
        c.contact_state,
        c.contact_country,
        c.contact_address,
        c.contact_zip_code,
        
        -- V2 Enriched contact data (from ContactEnrichmentProcessorV2)
        c.executive_summary,
        c.career_timeline,
        c.additional_email,
        c.phone_number_secondary,
        c.twitter,
        c.facebook,
        c.instagram,
        c.youtube,
        c.education_college,
        c.education_college_year_graduated,
        c.education_high_school,
        c.education_high_school_year_graduated,
        c.honorable_achievements,
        c.hobbies,
        c.age,
        c.contact_type,
        c.relationship_owner,
        c.role_in_decision_making,
        c.source_of_introduction,
        c.accredited_investor_status,
        
        -- Processing status fields
        c.contact_enrichment_v2_status,
        c.contact_investment_criteria_status,
        
        -- Company basic info
        co.company_name,
        co.company_website,
        co.industry,
        co.company_type,
        co.business_model,
        co.founded_year,
        
        -- Company V2 enriched data (from CompanyOverviewProcessorV2)
        co.investment_focus,
        co.investment_strategy_mission,
        co.investment_strategy_approach,
        co.company_phone as main_phone,
        co.secondary_phone,
        co.main_email,
        co.secondary_email,
        co.company_linkedin,
        co.twitter as company_twitter,
        co.facebook as company_facebook,
        co.instagram as company_instagram,
        co.youtube as company_youtube,
        co.company_address as headquarters_address,
        co.company_city as headquarters_city,
        co.company_state as headquarters_state,
        co.company_zip as headquarters_zipcode,
        co.company_country as headquarters_country,
        co.additional_address,
        co.additional_city,
        co.additional_state,
        co.additional_zipcode,
        co.additional_country,
        co.office_locations,
        co.fund_size,
        co.aum,
        co.number_of_properties,
        co.number_of_offices,
        co.number_of_employees,
        co.annual_revenue,
        co.net_income,
        co.ebitda,
        co.profit_margin,
        co.market_capitalization,
        co.market_share_percentage,
        co.balance_sheet_strength,
        co.funding_sources,
        co.recent_capital_raises,
        co.typical_debt_to_equity_ratio,
        co.development_fee_structure,
        co.credit_rating,
        co.dry_powder,
        co.annual_deployment_target,
        co.investment_vehicle_type,
        co.active_fund_name_series,
        co.fund_size_active_fund,
        co.fundraising_status,
        co.lender_type,
        co.annual_loan_volume,
        co.lending_origin,
        co.portfolio_health,
        co.partnerships,
        co.key_equity_partners,
        co.key_debt_partners,
        co.board_of_directors,
        co.key_executives,
        co.founder_background,
        co.market_cycle_positioning,
        co.urban_vs_suburban_preference,
        co.sustainability_esg_focus,
        co.technology_proptech_adoption,
        co.adaptive_reuse_experience,
        co.regulatory_zoning_expertise,
        co.corporate_structure,
        co.parent_company,
        co.subsidiaries,
        co.stock_ticker_symbol,
        co.stock_exchange,
        co.products_services_description,
        co.target_customer_profile,
        co.major_competitors,
        co.unique_selling_proposition,
        co.industry_awards_recognitions,
        co.company_history,
        co.transactions_completed_last_12m,
        co.total_transaction_volume_ytd,
        co.deal_count_ytd,
        co.average_deal_size,
        co.portfolio_size_sqft,
        co.portfolio_asset_count,
        co.role_in_previous_deal,
        co.internal_relationship_manager,
        co.last_contact_date,
        co.pipeline_status,
        co.recent_news_sentiment,
        co.data_source,
        co.data_confidence_score,
        co.quarterly_earnings_link,
        
        -- Company processing status fields
        co.overview_v2_status as company_overview_v2_status,
        co.investment_criteria_status as company_investment_criteria_status
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
      WHERE c.contact_id = $1
    `;
    
    const contactResult = await pool.query(contactQuery, [contactId]);
    if (contactResult.rows.length === 0) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    
    const contact = contactResult.rows[0];
    
    // Debug: Check contact location data (temporary)
    const debugContactData = {
      contact_city: contact?.contact_city,
      contact_state: contact?.contact_state,
      contact_country: contact?.contact_country
    };

    // Get investment criteria from investment_criteria_central table (populated by processors)
    const criteriaQuery = `
      SELECT 
        entity_type,
        capital_position,
        minimum_deal_size,
        maximum_deal_size,
        country,
        region,
        state,
        city,
        property_types,
        property_subcategories,
        strategies,
        decision_making_process,
        notes,
        created_at
      FROM investment_criteria_central
      WHERE (entity_id = $1 AND entity_type = 'contact') 
         OR (entity_id = $2 AND entity_type = 'company')
      ORDER BY 
        CASE WHEN entity_type = 'contact' THEN 1 ELSE 2 END,
        created_at DESC
    `;
    
    const criteriaResult = await pool.query(criteriaQuery, [contactId, contact.company_id || null]);

    let searchResult: {
      matches: ArticleMatch[],
      searchTier: 1 | 2 | 3 | 4 | 5,
      tierDescription: string,
      isPremium: boolean
    };
    let criteriaSource: CriteriaSource | null = null;

    // Step 1: Try contact-level investment criteria with new 5-tier hierarchical search
    const contactCriteria = extractContactCriteria(criteriaResult.rows, contact);
    if (checkHasInvestmentCriteria(contactCriteria)) {
      const matches = await findArticleMatchesWithHierarchicalTiers(contactCriteria, true, false);
      const tierCounts = matches.reduce((acc, match) => {
        acc[match.searchTier || 5] = (acc[match.searchTier || 5] || 0) + 1;
        return acc;
      }, {} as Record<number, number>);
      
      const primaryTier = Math.min(...Object.keys(tierCounts).map(Number)) as 1 | 2 | 3 | 4 | 5;
      
      searchResult = {
        matches: matches, // Return all matches from all tiers
        searchTier: primaryTier,
        tierDescription: getTierDescription(primaryTier, matches.find(m => m.searchTier === primaryTier)?.isPremium),
        isPremium: matches.find(m => m.searchTier === primaryTier)?.isPremium || false
      };
      criteriaSource = {
        source: 'contact',
        description: `${searchResult.tierDescription} using contact-level investment criteria from ContactInvestmentCriteriaProcessor`,
        criteria: contactCriteria,
        hasInvestmentCriteria: true
      };
    } else {
      // Step 2: Try company-level investment criteria with new 5-tier hierarchical search
      const companyCriteria = extractCompanyCriteria(contact, criteriaResult.rows);
      if (checkHasInvestmentCriteria(companyCriteria)) {
        const matches = await findArticleMatchesWithHierarchicalTiers(companyCriteria, true, false);
        const tierCounts = matches.reduce((acc, match) => {
          acc[match.searchTier || 5] = (acc[match.searchTier || 5] || 0) + 1;
          return acc;
        }, {} as Record<number, number>);
        
        const primaryTier = Math.min(...Object.keys(tierCounts).map(Number)) as 1 | 2 | 3 | 4 | 5;
        
        searchResult = {
          matches: matches, // Return all matches from all tiers
          searchTier: primaryTier,
          tierDescription: getTierDescription(primaryTier, matches.find(m => m.searchTier === primaryTier)?.isPremium),
          isPremium: matches.find(m => m.searchTier === primaryTier)?.isPremium || false
        };
        criteriaSource = {
          source: 'company',
          description: `${searchResult.tierDescription} using company-level investment criteria from CompanyInvestmentCriteriaProcessor`,
          criteria: companyCriteria,
          hasInvestmentCriteria: true
        };
      } else {
        // Step 3: Use location fallback with new 5-tier hierarchical search
        const locationCriteria = extractLocationCriteria(contact);
        if (locationCriteria.states.length > 0 || locationCriteria.cities.length > 0 || locationCriteria.countries.length > 0) {
          const matches = await findArticleMatchesWithHierarchicalTiers(locationCriteria, false, true);
          const tierCounts = matches.reduce((acc, match) => {
            acc[match.searchTier || 5] = (acc[match.searchTier || 5] || 0) + 1;
            return acc;
          }, {} as Record<number, number>);
          
          const primaryTier = Math.min(...Object.keys(tierCounts).map(Number)) as 1 | 2 | 3 | 4 | 5;
          
          searchResult = {
            matches: matches, // Return all matches from all tiers
            searchTier: primaryTier,
            tierDescription: getTierDescription(primaryTier, matches.find(m => m.searchTier === primaryTier)?.isPremium),
            isPremium: matches.find(m => m.searchTier === primaryTier)?.isPremium || false
          };
          criteriaSource = {
            source: 'location',
            description: `${searchResult.tierDescription} using location-based fallback criteria from enriched contact/company data`,
            criteria: locationCriteria,
            hasInvestmentCriteria: false
          };
        } else {
          // No criteria available
          searchResult = {
            matches: [],
            searchTier: 5,
            tierDescription: 'No matching criteria available',
            isPremium: false
          };
        }
      }
    }

    // If no criteria source was determined, return empty results
    if (!criteriaSource) {
      const response: any = {
        contact: {
          contact_id: contact.contact_id,
          company_id: contact.company_id,
          company_name: contact.company_name
        },
        matches: [],
        criteria_source: 'none',
        criteria_description: 'No investment criteria or location data available',
        search_tier: 4,
        tier_description: 'No matching criteria available',
        is_premium: false,
        matching_criteria: {
          used_criteria: {
            property_types: [],
            property_sub_categories: [],
            strategies: [],
            countries: [],
            states: [],
            cities: [],
            loan_types: [],
            capital_positions: [],
            min_deal_size: null,
            max_deal_size: null,
          },
          has_investment_criteria: false,
          property_types: [],
          property_subcategories: [],
          strategies: [],
          deal_size_range: { min: null, max: null },
          locations: { states: [], cities: [], countries: [] },
          capital_positions: {
            positions: [],
            count: 0,
            source: 'No Investment Criteria Available'
          }
        },
        total_matches: 0,
        processing_info: {
          contact_enrichment_v2_status: contact.contact_enrichment_v2_status,
          contact_investment_criteria_status: contact.contact_investment_criteria_status,
          company_overview_v2_status: contact.company_overview_v2_status,
          company_investment_criteria_status: contact.company_investment_criteria_status
        }
      };

      return NextResponse.json(response);
    }

    // Return successful results with enhanced criteria source info and hierarchical search details
    const response: any = {
      contact: {
        contact_id: contact.contact_id,
        company_id: contact.company_id,
        company_name: contact.company_name,
        first_name: contact.first_name,
        last_name: contact.last_name,
        email: contact.email,
        title: contact.title
      },
      matches: searchResult.matches.slice(0, 50), // Limit to top 50 matches
      criteria_source: criteriaSource.source,
      criteria_description: criteriaSource.description,
      search_tier: searchResult.searchTier,
      tier_description: searchResult.tierDescription,
      is_premium: searchResult.isPremium,
      premium_count: searchResult.matches.filter(match => match.isPremium).length,
      matching_criteria: {
        used_criteria: criteriaSource.criteria,
        has_investment_criteria: criteriaSource.hasInvestmentCriteria,
        // Detailed breakdown for UI display
        property_types: criteriaSource.criteria.property_types,
        property_subcategories: criteriaSource.criteria.property_sub_categories,
        strategies: criteriaSource.criteria.strategies,
        deal_size_range: {
          min: criteriaSource.criteria.min_deal_size ? `$${(criteriaSource.criteria.min_deal_size / 1000000).toFixed(1)}M` : null,
          max: criteriaSource.criteria.max_deal_size ? `$${(criteriaSource.criteria.max_deal_size / 1000000).toFixed(1)}M` : null
        },
        locations: {
          states: criteriaSource.criteria.states,
          cities: criteriaSource.criteria.cities,
          countries: criteriaSource.criteria.countries
        },
        // Enhanced capital position details for UI display
        capital_positions: {
          positions: criteriaSource.criteria.capital_positions,
          count: criteriaSource.criteria.capital_positions.length,
          source: criteriaSource.source === 'contact' ? 'Contact Investment Criteria' : 
                 criteriaSource.source === 'company' ? 'Company Investment Criteria' : 'Location Fallback'
        }
      },
      total_matches: searchResult.matches.length,
      debug_contact_data: debugContactData,
      debug_final_criteria: contactCriteria,
      debug_extract_function: (global as any).extractContactCriteriaDebug,
      debug_extract_steps: (global as any).extractContactCriteriaDebugSteps,
      processing_info: {
        data_sources: [
          'ContactEnrichmentProcessorV2',
          'ContactInvestmentCriteriaProcessor', 
          'CompanyOverviewProcessorV2',
          'CompanyInvestmentCriteriaProcessor'
        ],
        enriched_fields_used: [
          ...(contact.investment_focus ? ['investment_focus'] : []),
          ...(contact.office_locations ? ['office_locations'] : []),
          ...(contact.headquarters_city ? ['headquarters_location'] : []),
          ...(contact.contact_city ? ['contact_location'] : [])
        ],
        hierarchical_search: {
          applied: true,
          search_tier: searchResult.searchTier,
          tier_description: searchResult.tierDescription,
          is_premium: searchResult.isPremium
        },
        capital_position_filtering: {
          applied: criteriaSource.criteria.capital_positions.length > 0,
          positions_count: criteriaSource.criteria.capital_positions.length,
          source: criteriaSource.source
        }
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error matching articles for contact:', error);
    
    // Provide more specific error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      { 
        error: 'Failed to match articles for contact',
        details: errorMessage,
        suggestion: 'Check if contact exists and has been processed by enrichment processors'
      },
      { status: 500 }
    );
  }
}