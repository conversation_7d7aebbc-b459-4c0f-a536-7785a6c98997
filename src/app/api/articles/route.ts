import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const pageSize = Math.min(parseInt(searchParams.get('pageSize') || '20'), 100)
  const search = searchParams.get('search') || ''
  const source = searchParams.get('source') || ''
  const status = searchParams.get('status') || ''
  const sortBy = searchParams.get('sortBy') || 'created_at'
  const sortOrder = searchParams.get('sortOrder') || 'desc'
  
  const offset = (page - 1) * pageSize

  try {
    // Build WHERE clause
    const whereConditions = ['(is_bad_url IS NULL OR is_bad_url != true)']
    const queryParams: any[] = []
    let paramCount = 0

    if (search) {
      paramCount++
      whereConditions.push(`(headline ILIKE $${paramCount} OR article_body_text ILIKE $${paramCount} OR publication_name ILIKE $${paramCount} OR author ILIKE $${paramCount})`)
      queryParams.push(`%${search}%`)
    }

    if (source && source !== 'all') {
      paramCount++
      whereConditions.push(`publication_name = $${paramCount}`)
      queryParams.push(source)
    }

    // Enhanced status filtering for different stages
    if (status && status !== 'all') {
      switch (status) {
        case 'pending':
          whereConditions.push(`(fetch_status = 'pending' OR extraction_status = 'pending')`)
          break
        case 'processing':
          whereConditions.push(`(fetch_status = 'running' OR extraction_status = 'running')`)
          break
        case 'completed':
          whereConditions.push(`(fetch_status = 'completed' AND extraction_status = 'completed')`)
          break
        case 'failed':
          whereConditions.push(`(fetch_status = 'failed' OR extraction_status = 'failed')`)
          break
        case 'error':
          whereConditions.push(`(fetch_status = 'error' OR extraction_status = 'error')`)
          break
        case 'html_fetch_pending':
          whereConditions.push(`fetch_status = 'pending'`)
          break
        case 'html_fetch_running':
          whereConditions.push(`fetch_status = 'running'`)
          break
        case 'html_fetch_completed':
          whereConditions.push(`fetch_status = 'completed'`)
          break
        case 'html_fetch_failed':
          whereConditions.push(`fetch_status = 'failed'`)
          break
        case 'enrichment_pending':
          whereConditions.push(`fetch_status = 'completed' AND extraction_status = 'pending'`)
          break
        case 'enrichment_running':
          whereConditions.push(`fetch_status = 'completed' AND extraction_status = 'running'`)
          break
        case 'enrichment_completed':
          whereConditions.push(`fetch_status = 'completed' AND extraction_status = 'completed'`)
          break
        case 'enrichment_failed':
          whereConditions.push(`fetch_status = 'completed' AND extraction_status = 'failed'`)
          break
        case 'relevant':
          whereConditions.push(`is_relevant = true`)
          break
        case 'good_urls':
          whereConditions.push(`is_bad_url = false`)
          break
        case 'with_content':
          whereConditions.push(`(headline IS NOT NULL AND headline != '' AND article_body_text IS NOT NULL AND article_body_text != '')`)
          break
        case 'without_content':
          whereConditions.push(`(headline IS NULL OR headline = '' OR article_body_text IS NULL OR article_body_text = '')`)
          break
        case 'with_properties':
          whereConditions.push(`EXISTS (SELECT 1 FROM article_properties WHERE article_id = article.article_id)`)
          break
        case 'with_transactions':
          whereConditions.push(`EXISTS (SELECT 1 FROM article_transactions WHERE article_id = article.article_id)`)
          break
        case 'with_entities':
          whereConditions.push(`EXISTS (SELECT 1 FROM articles_entities WHERE article_id = article.article_id)`)
          break
        case 'with_metrics':
          whereConditions.push(`EXISTS (SELECT 1 FROM article_market_metrics WHERE article_id = article.article_id)`)
          break
      }
    }

    const whereClause = whereConditions.join(' AND ')

    // Build ORDER BY clause
    let orderBy = 'created_at DESC'
    const validSortFields = ['created_at', 'updated_at', 'publication_date', 'headline', 'publication_name', 'fetch_status', 'extraction_status']
    const validSortOrders = ['asc', 'desc']
    
    if (validSortFields.includes(sortBy) && validSortOrders.includes(sortOrder.toLowerCase())) {
      orderBy = `${sortBy} ${sortOrder.toUpperCase()}`
      // Add secondary sort for consistency
      if (sortBy !== 'created_at') {
        orderBy += ', created_at DESC'
      }
    } else {
      orderBy = 'created_at DESC'
    }

    // Get articles with pagination
    paramCount++
    const articlesQuery = `
      SELECT 
        article_id,
        publication_name,
        article_url,
        headline,
        publication_date,
        author,
        summary,
        topic,
        market_trend_tags,
        is_distressed,
        sentiment,
        sentiment_summary,
        key_takeaways,
        llm_tags,
        source_confidence,
        fetch_status,
        extraction_status,
        is_bad_url,
        is_relevant,
        created_at,
        updated_at,
        -- Count related data
        (SELECT COUNT(*) FROM article_properties WHERE article_id = article.article_id) as properties_count,
        (SELECT COUNT(*) FROM article_market_metrics WHERE article_id = article.article_id) as metrics_count,
        (SELECT COUNT(*) FROM article_transactions WHERE article_id = article.article_id) as transactions_count,
        (SELECT COUNT(*) FROM articles_entities WHERE article_id = article.article_id) as entities_count
      FROM article 
      WHERE ${whereClause}
      ORDER BY ${orderBy}
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `
    
    queryParams.push(pageSize, offset)

    const articlesResult = await pool.query(articlesQuery, queryParams)

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM article WHERE ${whereClause}`
    const countResult = await pool.query(countQuery, queryParams.slice(0, -2)) // Remove limit/offset params
    const totalCount = parseInt(countResult.rows[0].count)

    // Get source statistics with enhanced stage breakdown
    const sourceStatsQuery = `
      SELECT 
        publication_name,
        COUNT(*) as article_count,
        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as today_count,
        COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'completed' THEN 1 END) as processed_count,
        COUNT(CASE WHEN fetch_status = 'pending' OR extraction_status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN fetch_status = 'running' OR extraction_status = 'running' THEN 1 END) as running_count,
        COUNT(CASE WHEN fetch_status = 'failed' OR extraction_status = 'failed' THEN 1 END) as failed_count,
        COUNT(CASE WHEN fetch_status = 'error' OR extraction_status = 'error' THEN 1 END) as error_count,
        COUNT(CASE WHEN is_relevant = true THEN 1 END) as relevant_count,
        COUNT(CASE WHEN is_bad_url = false THEN 1 END) as good_urls_count
      FROM article 
      WHERE publication_name IS NOT NULL
        AND (is_bad_url IS NULL OR is_bad_url != true)
      GROUP BY publication_name 
      ORDER BY article_count DESC
    `
    const sourceStatsResult = await pool.query(sourceStatsQuery)

    // Get daily stats for the last 7 days
    const dailyStatsQuery = `
      SELECT 
        DATE(created_at) as date,
        publication_name,
        COUNT(*) as count
      FROM article 
      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
        AND publication_name IS NOT NULL
        AND (is_bad_url IS NULL OR is_bad_url != true)
      GROUP BY DATE(created_at), publication_name 
      ORDER BY date DESC, publication_name
    `
    const dailyStatsResult = await pool.query(dailyStatsQuery)

    return NextResponse.json({
      articles: articlesResult.rows,
      total: totalCount,
      page,
      totalPages: Math.ceil(totalCount / pageSize),
      pageSize,
      sources: sourceStatsResult.rows,
      dailyStats: dailyStatsResult.rows
    })

  } catch (error) {
    console.error('Error fetching articles:', error)
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      publication_name,
      article_url,
      headline,
      publication_date,
      author,
      summary,
      article_body_text,
      topic,
      market_trend_tags = [],
      is_distressed = false,
      sentiment,
      sentiment_summary,
      key_takeaways,
      llm_tags = [],
      source_confidence,
      scraping_source_type = 'Manual'
    } = body

    // Validate required fields
    if (!article_url) {
      return NextResponse.json(
        { error: 'Article URL is required' },
        { status: 400 }
      )
    }

    const insertQuery = `
      INSERT INTO article (
        publication_name, article_url, headline, publication_date, author,
        summary, article_body_text, topic, market_trend_tags, is_distressed,
        sentiment, sentiment_summary, key_takeaways, llm_tags, source_confidence,
        scraping_source_type
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
      ) RETURNING article_id
    `

    const result = await pool.query(insertQuery, [
      publication_name,
      article_url,
      headline,
      publication_date,
      author,
      summary,
      article_body_text,
      topic,
      JSON.stringify(market_trend_tags),
      is_distressed,
      sentiment,
      sentiment_summary,
      key_takeaways,
      JSON.stringify(llm_tags),
      source_confidence,
      scraping_source_type
    ])

    return NextResponse.json({
      success: true,
      article_id: result.rows[0].article_id,
      message: 'Article created successfully'
    })

  } catch (error: any) {
    console.error('Error creating article:', error)
    
    // Handle unique constraint violation for article_url
    if (error.code === '23505' && error.constraint === 'unique_article_url') {
      return NextResponse.json(
        { error: 'Article URL already exists' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    )
  }
}
