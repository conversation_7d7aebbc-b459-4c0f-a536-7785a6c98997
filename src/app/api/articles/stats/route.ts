import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect()

    try {
      // Get comprehensive article stats for all processing stages
      const statsQuery = `
        WITH article_stats AS (
          SELECT
            -- HTML Fetch Stage
            COUNT(*) as total_articles,
            COUNT(CASE WHEN fetch_status = 'pending' THEN 1 END) as html_fetch_pending,
            COUNT(CASE WHEN fetch_status = 'running' THEN 1 END) as html_fetch_running,
            COUNT(CASE WHEN fetch_status = 'completed' THEN 1 END) as html_fetch_completed,
            COUNT(CASE WHEN fetch_status = 'failed' THEN 1 END) as html_fetch_failed,
            COUNT(CASE WHEN fetch_status = 'error' THEN 1 END) as html_fetch_error,
            
            -- Link Fetch Stage (articles that need link fetching)
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'pending' THEN 1 END) as link_fetch_pending,
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'running' THEN 1 END) as link_fetch_running,
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'completed' THEN 1 END) as link_fetch_completed,
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'failed' THEN 1 END) as link_fetch_failed,
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'error' THEN 1 END) as link_fetch_error,
            
            -- Enrichment Stage
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'pending' THEN 1 END) as enrichment_pending,
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'running' THEN 1 END) as enrichment_running,
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'completed' THEN 1 END) as enrichment_completed,
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'failed' THEN 1 END) as enrichment_failed,
            COUNT(CASE WHEN fetch_status = 'completed' AND extraction_status = 'error' THEN 1 END) as enrichment_error,
            
            -- Overall counts
            COUNT(CASE WHEN fetch_status = 'completed' THEN 1 END) as total_fetched,
            COUNT(CASE WHEN extraction_status = 'completed' THEN 1 END) as total_enriched,
            COUNT(CASE WHEN fetch_status = 'failed' OR extraction_status = 'failed' THEN 1 END) as total_failed,
            COUNT(CASE WHEN fetch_status = 'error' OR extraction_status = 'error' THEN 1 END) as total_errors
          FROM article
          WHERE (is_bad_url IS NULL OR is_bad_url != true)
        )
        SELECT * FROM article_stats
      `

      const statsResult = await client.query(statsQuery)
      const stats = statsResult.rows[0]

      // Get additional enrichment metrics
      const enrichmentQuery = `
        SELECT
          (SELECT COUNT(*) FROM article_properties) as properties_extracted,
          (SELECT COUNT(*) FROM article_transactions) as transactions_extracted,
          (SELECT COUNT(*) FROM articles_entities) as entities_extracted,
          (SELECT COUNT(*) FROM article_market_metrics) as market_metrics_extracted,
          (SELECT COUNT(*) FROM article WHERE fetch_status = 'completed' AND extraction_status = 'completed') as fully_processed_articles
      `

      const enrichmentResult = await client.query(enrichmentQuery)
      const enrichment = enrichmentResult.rows[0]

      // Transform to match the expected StageStats format
      const response = {
        article_html_fetch: {
          total: parseInt(stats.total_articles) || 0,
          pending: parseInt(stats.html_fetch_pending) || 0,
          running: parseInt(stats.html_fetch_running) || 0,
          completed: parseInt(stats.html_fetch_completed) || 0,
          failed: parseInt(stats.html_fetch_failed) || 0,
          error: parseInt(stats.html_fetch_error) || 0,
          success_rate: parseInt(stats.html_fetch_completed) > 0 && parseInt(stats.total_articles) > 0 
            ? Math.round((parseInt(stats.html_fetch_completed) / parseInt(stats.total_articles)) * 100) 
            : 0
        },
        article_link_fetch: {
          total: parseInt(stats.total_fetched) || 0,
          pending: parseInt(stats.link_fetch_pending) || 0,
          running: parseInt(stats.link_fetch_running) || 0,
          completed: parseInt(stats.link_fetch_completed) || 0,
          failed: parseInt(stats.link_fetch_failed) || 0,
          error: parseInt(stats.link_fetch_error) || 0,
          success_rate: parseInt(stats.link_fetch_completed) > 0 && parseInt(stats.total_fetched) > 0 
            ? Math.round((parseInt(stats.link_fetch_completed) / parseInt(stats.total_fetched)) * 100) 
            : 0
        },
        article_enrichment: {
          total: parseInt(stats.total_fetched) || 0,
          pending: parseInt(stats.enrichment_pending) || 0,
          running: parseInt(stats.enrichment_running) || 0,
          completed: parseInt(stats.enrichment_completed) || 0,
          failed: parseInt(stats.enrichment_failed) || 0,
          error: parseInt(stats.enrichment_error) || 0,
          success_rate: parseInt(stats.enrichment_completed) > 0 && parseInt(stats.total_fetched) > 0 
            ? Math.round((parseInt(stats.enrichment_completed) / parseInt(stats.total_fetched)) * 100) 
            : 0
        },
        // Overall summary
        summary: {
          total_articles: parseInt(stats.total_articles) || 0,
          total_fetched: parseInt(stats.total_fetched) || 0,
          total_enriched: parseInt(stats.total_enriched) || 0,
          total_failed: parseInt(stats.total_failed) || 0,
          total_errors: parseInt(stats.total_errors) || 0,
          properties_extracted: parseInt(enrichment.properties_extracted) || 0,
          transactions_extracted: parseInt(enrichment.transactions_extracted) || 0,
          entities_extracted: parseInt(enrichment.entities_extracted) || 0,
          market_metrics_extracted: parseInt(enrichment.market_metrics_extracted) || 0,
          fully_processed_articles: parseInt(enrichment.fully_processed_articles) || 0
        }
      }

      return NextResponse.json(response)

    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error fetching article stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch article stats' },
      { status: 500 }
    )
  }
}
