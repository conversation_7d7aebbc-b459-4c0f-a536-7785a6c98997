import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ entityType: string; entityId: string }> }
) {
  try {
    const { entityType, entityId } = await params
    
    // Validate entity type (case-insensitive)
    const validEntityTypes = ['company', 'contact', 'deal']
    const normalizedEntityType = entityType.toLowerCase()
    
    if (!validEntityTypes.includes(normalizedEntityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type. Must be company, contact, or deal' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      // Fetch investment criteria for the specific entity
      const query = `
        SELECT 
          ic.*,
          -- Debt fields with proper prefixes for UI
          d.loan_type as debt_loan_type,
          d.loan_program as debt_loan_program,
          d.min_loan_term as debt_min_loan_term,
          d.max_loan_term as debt_max_loan_term,
          d.loan_interest_rate as debt_interest_rate,
          d.loan_interest_rate_based_off_sofr as debt_loan_interest_rate_based_off_sofr,
          d.loan_interest_rate_based_off_wsj as debt_loan_interest_rate_based_off_wsj,
          d.loan_interest_rate_based_off_prime as debt_loan_interest_rate_based_off_prime,
          d.loan_interest_rate_based_off_3yt as debt_loan_interest_rate_based_off_3yt,
          d.loan_interest_rate_based_off_5yt as debt_loan_interest_rate_based_off_5yt,
          d.loan_interest_rate_based_off_10yt as debt_loan_interest_rate_based_off_10yt,
          d.loan_interest_rate_based_off_30yt as debt_loan_interest_rate_based_off_30yt,
          d.loan_to_value_min as debt_loan_to_value_min,
          d.loan_to_value_max as debt_loan_to_value_max,
          d.loan_to_cost_min as debt_loan_to_cost_min,
          d.loan_to_cost_max as debt_loan_to_cost_max,
          d.loan_origination_min_fee as debt_loan_origination_min_fee,
          d.loan_origination_max_fee as debt_loan_origination_max_fee,
          d.loan_exit_min_fee as debt_loan_exit_min_fee,
          d.loan_exit_max_fee as debt_loan_exit_max_fee,
          d.min_loan_dscr as debt_min_loan_dscr,
          d.max_loan_dscr as debt_max_loan_dscr,
          d.structured_loan_tranche as debt_structured_loan_tranche,
          d.recourse_loan as debt_recourse_loan,
          d.closing_time as debt_closing_time,
          d.debt_program_overview,
          d.lien_position as debt_lien_position,
          d.loan_min_debt_yield as debt_loan_min_debt_yield,
          d.prepayment as debt_prepayment,
          d.yield_maintenance as debt_yield_maintenance,
          d.amortization as debt_amortization,
          d.application_deposit as debt_application_deposit,
          d.good_faith_deposit as debt_good_faith_deposit,
          d.future_facilities as debt_future_facilities,
          d.eligible_borrower as debt_eligible_borrower,
          d.occupancy_requirements as debt_occupancy_requirements,
          d.rate_lock as debt_rate_lock,
          d.rate_type as debt_rate_type,
          d.loan_type_normalized as debt_loan_type_normalized,
          d.notes as debt_notes,
          -- Equity fields with proper prefixes for UI
          e.target_return as equity_target_return,
          e.minimum_internal_rate_of_return as equity_minimum_internal_rate_of_return,
          e.minimum_yield_on_cost as equity_minimum_yield_on_cost,
          e.minimum_equity_multiple as equity_minimum_equity_multiple,
          e.min_hold_period_years as equity_min_hold_period_years,
          e.max_hold_period_years as equity_max_hold_period_years,
          e.ownership_requirement as equity_ownership_requirement,
          e.equity_program_overview,
          e.target_cash_on_cash_min as equity_target_cash_on_cash_min,
          e.attachment_point as equity_attachment_point,
          e.max_leverage_tolerance as equity_max_leverage_tolerance,
          e.typical_closing_timeline_days as equity_typical_closing_timeline_days,
          e.proof_of_funds_requirement as equity_proof_of_funds_requirement,
          e.occupancy_requirements as equity_occupancy_requirements,
          e.notes as equity_notes
        FROM investment_criteria_central ic
        LEFT JOIN investment_criteria_debt d ON ic.investment_criteria_debt_id = d.investment_criteria_debt_id
        LEFT JOIN investment_criteria_equity e ON ic.investment_criteria_equity_id = e.investment_criteria_equity_id
        WHERE LOWER(ic.entity_type) = LOWER($1) AND ic.entity_id = $2
        ORDER BY ic.created_at DESC
      `
      
      const result = await client.query(query, [normalizedEntityType, entityId])
      
      // Group criteria by capital position for better organization
      const groupedCriteria = result.rows.reduce((acc: any, criteria: any) => {
        const capitalPosition = criteria.capital_position || 'General'
        
        if (!acc[capitalPosition]) {
          acc[capitalPosition] = []
        }
        
        // Data is already flat with proper field names from the query
        const enrichedCriteria = { ...criteria }
        
        acc[capitalPosition].push(enrichedCriteria)
        return acc
      }, {})

      return NextResponse.json({
        success: true,
        data: {
          entityType,
          entityId,
          groupedCriteria,
          totalCriteria: result.rows.length,
          criteriaList: result.rows // Also provide flat list for backwards compatibility
        }
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error fetching investment criteria:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch investment criteria',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ entityType: string; entityId: string }> }
) {
  try {
    const { entityType, entityId } = await params
    const body = await request.json()
    
    // Validate entity type
    const validEntityTypes = ['Company', 'Contact', 'Deal']
    const normalizedEntityType = entityType.charAt(0).toUpperCase() + entityType.slice(1).toLowerCase()
    
    if (!validEntityTypes.includes(normalizedEntityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type. Must be Company, Contact, or Deal' },
        { status: 400 }
      )
    }

    const { criteria_id, updates } = body
    
    if (!criteria_id) {
      return NextResponse.json(
        { error: 'criteria_id is required' },
        { status: 400 }
      )
    }

    // Transform form data with prefixes to flat structure (same as POST)
    const transformFormData = (formData: any) => {
      const transformed: any = {}
      
      // Central data (no prefix)
      const centralFields = [
        'capital_position', 'minimum_deal_size', 'maximum_deal_size', 'country', 'region', 
        'state', 'city', 'property_types', 'property_subcategories', 'strategies', 
        'decision_making_process', 'notes'
      ]
      
      centralFields.forEach(field => {
        if (formData[field] !== undefined) {
          transformed[field] = formData[field]
        }
      })
      
      // Debt fields (remove debt_ prefix)
      const debtFields = [
        'debt_notes', 'debt_closing_time', 'debt_future_facilities', 'debt_eligible_borrower',
        'debt_occupancy_requirements', 'debt_lien_position', 'debt_min_loan_dscr', 'debt_max_loan_dscr',
        'debt_recourse_loan', 'debt_loan_min_debt_yield', 'debt_prepayment', 'debt_yield_maintenance',
        'debt_application_deposit', 'debt_good_faith_deposit', 'debt_loan_origination_max_fee',
        'debt_loan_origination_min_fee', 'debt_loan_exit_min_fee', 'debt_loan_exit_max_fee',
        'debt_loan_interest_rate', 'debt_loan_interest_rate_based_off_sofr', 'debt_loan_interest_rate_based_off_wsj',
        'debt_loan_interest_rate_based_off_prime', 'debt_loan_interest_rate_based_off_3yt',
        'debt_loan_interest_rate_based_off_5yt', 'debt_loan_interest_rate_based_off_10yt',
        'debt_loan_interest_rate_based_off_30yt', 'debt_rate_lock', 'debt_rate_type',
        'debt_loan_to_value_max', 'debt_loan_to_value_min', 'debt_loan_to_cost_min', 'debt_loan_to_cost_max',
        'debt_program_overview', 'debt_loan_type_normalized', 'debt_min_loan_term', 'debt_max_loan_term',
        'debt_amortization'
      ]
      
      debtFields.forEach(field => {
        if (formData[field] !== undefined) {
          const cleanField = field.replace('debt_', '')
          transformed[cleanField] = formData[field]
        }
      })
      
      // Special debt fields (no debt_ prefix in form)
      if (formData.loan_type !== undefined) transformed.loan_type = formData.loan_type
      if (formData.loan_program !== undefined) transformed.loan_program = formData.loan_program
      if (formData.structured_loan_tranche !== undefined) transformed.structured_loan_tranche = formData.structured_loan_tranche
      
      // Equity fields (remove equity_ prefix)
      const equityFields = [
        'equity_target_return', 'equity_minimum_internal_rate_of_return', 'equity_minimum_yield_on_cost',
        'equity_minimum_equity_multiple', 'equity_target_cash_on_cash_min', 'equity_min_hold_period_years',
        'equity_max_hold_period_years', 'equity_ownership_requirement', 'equity_attachment_point',
        'equity_max_leverage_tolerance', 'equity_typical_closing_timeline_days', 'equity_proof_of_funds_requirement',
        'equity_notes', 'equity_program_overview', 'equity_occupancy_requirements', 'equity_yield_on_cost',
        'equity_target_return_irr_on_equity', 'equity_equity_multiple', 'equity_position_specific_irr',
        'equity_position_specific_equity_multiple'
      ]
      
      equityFields.forEach(field => {
        if (formData[field] !== undefined) {
          const cleanField = field.replace('equity_', '')
          transformed[cleanField] = formData[field]
        }
      })
      
      return transformed
    }
    
    const transformedUpdates = transformFormData(updates)

    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // Update central criteria fields (matching actual database schema)
      const centralFields = [
        'capital_position', 'minimum_deal_size', 'maximum_deal_size', 'country', 'region', 'state', 'city',
        'property_types', 'property_subcategories', 'strategies', 'decision_making_process', 'notes'
      ]
      
      const centralUpdates: any = {}
      const centralParams: any[] = []
      let paramIndex = 1
      
      for (const field of centralFields) {
        if (transformedUpdates[field] !== undefined) {
          centralUpdates[field] = `$${paramIndex}`
          centralParams.push(transformedUpdates[field])
          paramIndex++
        }
      }
      
      if (Object.keys(centralUpdates).length > 0) {
        const setClause = Object.entries(centralUpdates)
          .map(([field, param]) => `${field} = ${param}`)
          .join(', ')
        
        const updateCentralSql = `
          UPDATE investment_criteria_central 
          SET ${setClause}, updated_at = NOW()
          WHERE investment_criteria_id = $${paramIndex}
        `
        
        await client.query(updateCentralSql, [...centralParams, criteria_id])
      }
      
      // Update debt criteria if present (matching actual database schema)
      const debtFields = [
        'loan_type', 'loan_program', 'min_loan_term', 'max_loan_term', 'loan_interest_rate',
        'loan_interest_rate_based_off_sofr', 'loan_interest_rate_based_off_wsj', 'loan_interest_rate_based_off_prime',
        'loan_interest_rate_based_off_3yt', 'loan_interest_rate_based_off_5yt', 'loan_interest_rate_based_off_10yt',
        'loan_interest_rate_based_off_30yt', 'loan_to_value_min', 'loan_to_value_max', 'loan_to_cost_min',
        'loan_to_cost_max', 'loan_origination_min_fee', 'loan_origination_max_fee', 'loan_exit_min_fee',
        'loan_exit_max_fee', 'min_loan_dscr', 'max_loan_dscr', 'structured_loan_tranche', 'recourse_loan',
        'closing_time', 'debt_program_overview', 'lien_position', 'loan_min_debt_yield', 'prepayment',
        'yield_maintenance', 'amortization', 'application_deposit', 'good_faith_deposit', 'future_facilities',
        'eligible_borrower', 'occupancy_requirements', 'rate_lock', 'rate_type', 'loan_type_normalized', 'notes'
      ]
      
      const hasDebtUpdates = debtFields.some(field => transformedUpdates[field] !== undefined)
      if (hasDebtUpdates) {
        const debtUpdates: any = {}
        const debtParams: any[] = []
        let debtParamIndex = 1
        
        for (const field of debtFields) {
          if (transformedUpdates[field] !== undefined) {
            debtUpdates[field] = `$${debtParamIndex}`
            debtParams.push(transformedUpdates[field])
            debtParamIndex++
          }
        }
        
        if (Object.keys(debtUpdates).length > 0) {
          const setClause = Object.entries(debtUpdates)
            .map(([field, param]) => `${field} = ${param}`)
            .join(', ')
          
                  const updateDebtSql = `
          UPDATE investment_criteria_debt 
          SET ${setClause}, updated_at = NOW()
          WHERE investment_criteria_id = $${debtParamIndex}
        `
        
        await client.query(updateDebtSql, [...debtParams, criteria_id])
        }
      }
      
      // Update equity criteria if present (matching actual database schema)
      const equityFields = [
        'target_return', 'minimum_internal_rate_of_return', 'minimum_yield_on_cost', 'minimum_equity_multiple',
        'target_cash_on_cash_min', 'min_hold_period_years', 'max_hold_period_years', 'ownership_requirement',
        'attachment_point', 'max_leverage_tolerance', 'typical_closing_timeline_days', 'proof_of_funds_requirement',
        'equity_program_overview', 'occupancy_requirements', 'notes', 'yield_on_cost', 'target_return_irr_on_equity',
        'equity_multiple', 'position_specific_irr', 'position_specific_equity_multiple'
      ]
      
      const hasEquityUpdates = equityFields.some(field => transformedUpdates[field] !== undefined)
      if (hasEquityUpdates) {
        const equityUpdates: any = {}
        const equityParams: any[] = []
        let equityParamIndex = 1
        
        for (const field of equityFields) {
          if (transformedUpdates[field] !== undefined) {
            equityUpdates[field] = `$${equityParamIndex}`
            equityParams.push(transformedUpdates[field])
            equityParamIndex++
          }
        }
        
        if (Object.keys(equityUpdates).length > 0) {
          const setClause = Object.entries(equityUpdates)
            .map(([field, param]) => `${field} = ${param}`)
            .join(', ')
          
          const updateEquitySql = `
            UPDATE investment_criteria_equity 
            SET ${setClause}, updated_at = NOW()
            WHERE investment_criteria_id = $${equityParamIndex}
          `
          
          await client.query(updateEquitySql, [...equityParams, criteria_id])
        }
      }
      
      await client.query('COMMIT')
      
      // If this is a company investment criteria update, trigger contact re-processing
      if (normalizedEntityType === 'Company') {
        try {
          const reprocessResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/investment-criteria/reprocess-contacts`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              companyId: parseInt(entityId),
              triggerProcessing: true
            })
          })

          const reprocessResult = await reprocessResponse.json()
          
          if (reprocessResult.success && reprocessResult.contactsReprocessed > 0) {
            console.log(`Company IC updated for company ${entityId}. Triggered re-processing for ${reprocessResult.contactsReprocessed} contacts.`)
          }
        } catch (reprocessError) {
          console.error('Error triggering contact reprocessing:', reprocessError)
          // Don't fail the main update if reprocessing fails
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Investment criteria updated successfully'
      })
      
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
    
  } catch (error) {
    console.error('Error updating investment criteria:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update investment criteria',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ entityType: string; entityId: string }> }
) {
  try {
    const { entityType, entityId } = await params
    console.log('DELETE request received for entity route:', { entityType, entityId })
    const body = await request.json()
    
    // Validate entity type
    const validEntityTypes = ['Company', 'Contact', 'Deal']
    const normalizedEntityType = entityType.charAt(0).toUpperCase() + entityType.slice(1).toLowerCase()
    
    if (!validEntityTypes.includes(normalizedEntityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type. Must be Company, Contact, or Deal' },
        { status: 400 }
      )
    }

    const { criteria_id } = body
    
    if (!criteria_id) {
      return NextResponse.json(
        { error: 'criteria_id is required' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // First, check if the criteria exists and get the debt/equity IDs
      const checkQuery = `
        SELECT investment_criteria_debt_id, investment_criteria_equity_id 
        FROM investment_criteria_central 
        WHERE investment_criteria_id = $1 AND entity_id = $2 AND LOWER(entity_type) = LOWER($3)
      `
      const checkResult = await client.query(checkQuery, [criteria_id, entityId, normalizedEntityType])
      
      if (checkResult.rows.length === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Investment criteria not found' },
          { status: 404 }
        )
      }
      
      const { investment_criteria_debt_id, investment_criteria_equity_id } = checkResult.rows[0]
      
      // Delete debt criteria if it exists
      if (investment_criteria_debt_id) {
        await client.query(
          'DELETE FROM investment_criteria_debt WHERE investment_criteria_debt_id = $1',
          [investment_criteria_debt_id]
        )
        console.log(`Deleted debt criteria with ID: ${investment_criteria_debt_id}`)
      }
      
      // Delete equity criteria if it exists
      if (investment_criteria_equity_id) {
        await client.query(
          'DELETE FROM investment_criteria_equity WHERE investment_criteria_equity_id = $1',
          [investment_criteria_equity_id]
        )
        console.log(`Deleted equity criteria with ID: ${investment_criteria_equity_id}`)
      }
      
      // Delete central criteria
      const centralDeleteResult = await client.query(
        'DELETE FROM investment_criteria_central WHERE investment_criteria_id = $1 AND entity_id = $2 AND LOWER(entity_type) = LOWER($3)',
        [criteria_id, entityId, normalizedEntityType]
      )
      
      if (centralDeleteResult.rowCount === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Failed to delete central criteria' },
          { status: 500 }
        )
      }
      
      console.log(`Deleted central criteria with ID: ${criteria_id}`)
      
      await client.query('COMMIT')
      
      return NextResponse.json({
        success: true,
        message: 'Investment criteria deleted successfully',
        deletedIds: {
          central: criteria_id,
          debt: investment_criteria_debt_id,
          equity: investment_criteria_equity_id
        }
      })
      
    } catch (error) {
      await client.query('ROLLBACK')
      console.error('Database error during deletion:', error)
      throw error
    } finally {
      client.release()
    }
    
  } catch (error) {
    console.error('Error deleting investment criteria:', error)
    return NextResponse.json(
      { 
        error: 'Failed to delete investment criteria',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ entityType: string; entityId: string }> }
) {
  try {
    const { entityType, entityId } = await params
    const body = await request.json()
    
    // Validate entity type
    const validEntityTypes = ['company', 'contact', 'deal']
    const normalizedEntityType = entityType.toLowerCase()
    
    if (!validEntityTypes.includes(normalizedEntityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type. Must be company, contact, or deal' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // Transform form data with prefixes to flat structure
      const transformFormData = (formData: any) => {
        const transformed: any = {}
        
        // Central data (no prefix)
        const centralFields = [
          'capital_position', 'minimum_deal_size', 'maximum_deal_size', 'country', 'region', 
          'state', 'city', 'property_types', 'property_subcategories', 'strategies', 
          'decision_making_process', 'notes'
        ]
        
        centralFields.forEach(field => {
          if (formData[field] !== undefined) {
            transformed[field] = formData[field]
          }
        })
        
        // Debt fields (remove debt_ prefix)
        const debtFields = [
          'debt_notes', 'debt_closing_time', 'debt_future_facilities', 'debt_eligible_borrower',
          'debt_occupancy_requirements', 'debt_lien_position', 'debt_min_loan_dscr', 'debt_max_loan_dscr',
          'debt_recourse_loan', 'debt_loan_min_debt_yield', 'debt_prepayment', 'debt_yield_maintenance',
          'debt_application_deposit', 'debt_good_faith_deposit', 'debt_loan_origination_max_fee',
          'debt_loan_origination_min_fee', 'debt_loan_exit_min_fee', 'debt_loan_exit_max_fee',
          'debt_loan_interest_rate', 'debt_loan_interest_rate_based_off_sofr', 'debt_loan_interest_rate_based_off_wsj',
          'debt_loan_interest_rate_based_off_prime', 'debt_loan_interest_rate_based_off_3yt',
          'debt_loan_interest_rate_based_off_5yt', 'debt_loan_interest_rate_based_off_10yt',
          'debt_loan_interest_rate_based_off_30yt', 'debt_rate_lock', 'debt_rate_type',
          'debt_loan_to_value_max', 'debt_loan_to_value_min', 'debt_loan_to_cost_min', 'debt_loan_to_cost_max',
          'debt_program_overview', 'debt_loan_type_normalized', 'debt_min_loan_term', 'debt_max_loan_term',
          'debt_amortization'
        ]
        
        debtFields.forEach(field => {
          if (formData[field] !== undefined) {
            const cleanField = field.replace('debt_', '')
            transformed[cleanField] = formData[field]
          }
        })
        
        // Special debt fields (no debt_ prefix in form)
        if (formData.loan_type !== undefined) transformed.loan_type = formData.loan_type
        if (formData.loan_program !== undefined) transformed.loan_program = formData.loan_program
        if (formData.structured_loan_tranche !== undefined) transformed.structured_loan_tranche = formData.structured_loan_tranche
        
        // Equity fields (remove equity_ prefix)
        const equityFields = [
          'equity_target_return', 'equity_minimum_internal_rate_of_return', 'equity_minimum_yield_on_cost',
          'equity_minimum_equity_multiple', 'equity_target_cash_on_cash_min', 'equity_min_hold_period_years',
          'equity_max_hold_period_years', 'equity_ownership_requirement', 'equity_attachment_point',
          'equity_max_leverage_tolerance', 'equity_typical_closing_timeline_days', 'equity_proof_of_funds_requirement',
          'equity_notes', 'equity_program_overview', 'equity_occupancy_requirements', 'equity_yield_on_cost',
          'equity_target_return_irr_on_equity', 'equity_equity_multiple', 'equity_position_specific_irr',
          'equity_position_specific_equity_multiple'
        ]
        
        equityFields.forEach(field => {
          if (formData[field] !== undefined) {
            const cleanField = field.replace('equity_', '')
            transformed[cleanField] = formData[field]
          }
        })
        
        return transformed
      }
      
      const transformedData = transformFormData(body)
      
      // Insert into central table FIRST to get the investment_criteria_id
      const centralData = {
        entity_type: normalizedEntityType,
        entity_id: parseInt(entityId),
        capital_position: transformedData.capital_position,
        minimum_deal_size: transformedData.minimum_deal_size,
        maximum_deal_size: transformedData.maximum_deal_size,
        country: transformedData.country,
        region: transformedData.region,
        state: transformedData.state,
        city: transformedData.city,
        property_types: transformedData.property_types,
        property_subcategories: transformedData.property_subcategories,
        strategies: transformedData.strategies,
        decision_making_process: transformedData.decision_making_process,
        notes: transformedData.notes
      }
      
      // Remove undefined values
      Object.keys(centralData).forEach(key => {
        if (centralData[key as keyof typeof centralData] === undefined) {
          delete centralData[key as keyof typeof centralData]
        }
      })
      
      const centralInsertQuery = `
        INSERT INTO investment_criteria_central (${Object.keys(centralData).join(', ')})
        VALUES (${Object.keys(centralData).map((_, i) => `$${i + 1}`).join(', ')})
        RETURNING investment_criteria_id
      `
      
      const centralResult = await client.query(centralInsertQuery, Object.values(centralData))
      const investmentCriteriaId = centralResult.rows[0].investment_criteria_id
      
      // Extract debt and equity specific fields from transformed data
      const debtFields = [
        'loan_type', 'loan_type_normalized', 'loan_program', 'min_loan_term', 'max_loan_term',
        'loan_interest_rate', 'loan_interest_rate_based_off_sofr', 'loan_interest_rate_based_off_wsj', 'loan_interest_rate_based_off_prime',
        'loan_to_value_min', 'loan_to_value_max', 'loan_to_cost_min', 'loan_to_cost_max',
        'loan_origination_min_fee', 'loan_origination_max_fee', 'loan_exit_min_fee', 'loan_exit_max_fee',
        'min_loan_dscr', 'max_loan_dscr', 'structured_loan_tranche', 'recourse_loan',
        'closing_time', 'future_facilities', 'eligible_borrower', 'occupancy_requirements',
        'lien_position', 'loan_min_debt_yield', 'prepayment', 'yield_maintenance',
        'amortization', 'application_deposit', 'good_faith_deposit', 'rate_lock',
        'rate_type', 'debt_program_overview', 'notes'
      ]
      
      const equityFields = [
        'target_return', 'minimum_internal_rate_of_return', 'minimum_yield_on_cost',
        'minimum_equity_multiple', 'target_cash_on_cash_min', 'min_hold_period_years',
        'max_hold_period_years', 'ownership_requirement', 'attachment_point',
        'max_leverage_tolerance', 'typical_closing_timeline_days', 'proof_of_funds_requirement',
        'notes', 'equity_program_overview', 'occupancy_requirements', 'yield_on_cost',
        'target_return_irr_on_equity', 'equity_multiple', 'position_specific_irr',
        'position_specific_equity_multiple'
      ]
      
      let debtId = null
      let equityId = null
      
      // Check if we have debt-related fields
      const hasDebtData = debtFields.some(field => transformedData[field] !== undefined && transformedData[field] !== null && transformedData[field] !== '')
      if (hasDebtData) {
        const debtData = debtFields.reduce((acc: any, field) => {
          if (transformedData[field] !== undefined) {
            acc[field] = transformedData[field]
          }
          return acc
        }, {})
        
        // Add the investment_criteria_id to the debt data
        debtData.investment_criteria_id = investmentCriteriaId
        
        const debtInsertQuery = `
          INSERT INTO investment_criteria_debt (${Object.keys(debtData).join(', ')})
          VALUES (${Object.keys(debtData).map((_, i) => `$${i + 1}`).join(', ')})
          RETURNING investment_criteria_debt_id
        `
        const debtResult = await client.query(debtInsertQuery, Object.values(debtData))
        debtId = debtResult.rows[0].investment_criteria_debt_id
      }
      
      // Check if we have equity-related fields
      const hasEquityData = equityFields.some(field => transformedData[field] !== undefined && transformedData[field] !== null && transformedData[field] !== '')
      if (hasEquityData) {
        const equityData = equityFields.reduce((acc: any, field) => {
          if (transformedData[field] !== undefined) {
            acc[field] = transformedData[field]
          }
          return acc
        }, {})
        
        // Add the investment_criteria_id to the equity data
        equityData.investment_criteria_id = investmentCriteriaId
        
        const equityInsertQuery = `
          INSERT INTO investment_criteria_equity (${Object.keys(equityData).join(', ')})
          VALUES (${Object.keys(equityData).map((_, i) => `$${i + 1}`).join(', ')})
          RETURNING investment_criteria_equity_id
        `
        const equityResult = await client.query(equityInsertQuery, Object.values(equityData))
        equityId = equityResult.rows[0].investment_criteria_equity_id
      }
      
      // Update the central table with the debt and equity IDs
      if (debtId || equityId) {
        const updateData: any = {}
        if (debtId) updateData.investment_criteria_debt_id = debtId
        if (equityId) updateData.investment_criteria_equity_id = equityId
        
        const updateQuery = `
          UPDATE investment_criteria_central 
          SET ${Object.keys(updateData).map(key => `${key} = $${Object.keys(updateData).indexOf(key) + 1}`).join(', ')}
          WHERE investment_criteria_id = $${Object.keys(updateData).length + 1}
        `
        await client.query(updateQuery, [...Object.values(updateData), investmentCriteriaId])
      }
      
      await client.query('COMMIT')
      
      return NextResponse.json({
        success: true,
        data: {
          investment_criteria_id: investmentCriteriaId,
          debt_id: debtId,
          equity_id: equityId
        }
      })
      
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error creating investment criteria:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create investment criteria',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
