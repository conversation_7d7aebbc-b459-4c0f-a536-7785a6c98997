import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";

// DELETE /api/deals/[id]/files/[fileId] - Delete a file relationship from a deal
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; fileId: string }> }
) {
  try {
    const { id, fileId } = await params;
    const dealId = parseInt(id);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Use the centralized smart deletion method
    const result = await FileManager.deleteFileRelationshipByTarget(
      fileId,
      "deals",
      "deal_id",
      dealId.toString()
    );

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 404 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error deleting file relationship:", error);
    return NextResponse.json(
      { success: false, message: "Failed to delete file relationship" },
      { status: 500 }
    );
  }
} 