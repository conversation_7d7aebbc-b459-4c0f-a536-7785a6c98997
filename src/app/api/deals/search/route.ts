import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    const searchQuery = `
      SELECT 
        d.deal_id,
        d.deal_name,
        d.sponsor_name,
        d.address,
        d.neighborhood,
        d.zip_code,
        d.property_description
      FROM public.deals d
      LEFT JOIN contacts c ON c.contact_id = d.contact_id
      WHERE 
        d.deal_name ILIKE $1 OR 
        d.sponsor_name ILIKE $1 OR 
        d.address ILIKE $1 OR
        d.neighborhood ILIKE $1 OR
        d.zip_code ILIKE $1 OR
        d.property_description ILIKE $1 OR
        c.contact_city ILIKE $1 OR
        c.contact_state ILIKE $1 OR
        c.region ILIKE $1
      ORDER BY 
        CASE 
          WHEN d.deal_name ILIKE $1 THEN 1 
          WHEN d.address ILIKE $1 THEN 2
          WHEN d.sponsor_name ILIKE $1 THEN 3
          WHEN d.neighborhood ILIKE $1 THEN 4
          WHEN c.contact_city ILIKE $1 THEN 5
          WHEN c.contact_state ILIKE $1 THEN 6
          WHEN c.region ILIKE $1 THEN 7
          ELSE 8 
        END,
        d.deal_name
      LIMIT 10
    `;

    const result = await pool.query(searchQuery, [`%${query}%`]);

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Error searching deals:", error);
    return NextResponse.json(
      { error: "Failed to search deals" },
      { status: 500 }
    );
  }
}
