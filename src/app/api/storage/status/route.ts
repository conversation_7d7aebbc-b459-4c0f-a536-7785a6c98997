import { NextRequest, NextResponse } from "next/server";
import { StorageProviderRegistry } from "@/lib/storage/StorageProviderRegistry";

export async function GET(request: NextRequest) {
  try {
    const registry = StorageProviderRegistry.getInstance();
    const status = await registry.getStorageStatus();
    
    return NextResponse.json({
      success: true,
      providers: status,
      availableProviders: registry.getProviderNames(),
      defaultProvider: registry.getDefaultProvider()?.name || null
    });
  } catch (error) {
    console.error("Error getting storage status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to get storage status" },
      { status: 500 }
    );
  }
}
