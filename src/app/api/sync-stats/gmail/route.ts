import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Get Gmail sync statistics
    const [accountsResult, messagesResult, threadsResult, recentSyncResult, recentAccountsResult] = await Promise.all([
      // Total accounts
      pool.query('SELECT COUNT(*) as count FROM gmail_accounts'),
      // Total messages
      pool.query('SELECT COUNT(*) as count FROM gmail_messages'),
      // Total threads
      pool.query('SELECT COUNT(*) as count FROM gmail_threads'),
    // Recent sync activity (last 24 hours)
    pool.query(`
      SELECT 
        COUNT(*) as recent_messages,
        COUNT(DISTINCT account_id) as active_accounts
      FROM gmail_messages 
      WHERE created_at > NOW() - INTERVAL '24 hours'
    `),
    // Accounts updated recently (indicating recent sync activity)
    pool.query(`
      SELECT COUNT(*) as recently_updated_accounts
      FROM gmail_accounts 
      WHERE updated_at > NOW() - INTERVAL '24 hours'
    `)
    ])

    const totalAccounts = parseInt(accountsResult.rows[0].count)
    const totalMessages = parseInt(messagesResult.rows[0].count)
    const totalThreads = parseInt(threadsResult.rows[0].count)
    const recentMessages = parseInt(recentSyncResult.rows[0].recent_messages)
    const activeAccounts = parseInt(recentSyncResult.rows[0].active_accounts)
    const recentlyUpdatedAccounts = parseInt(recentAccountsResult.rows[0].recently_updated_accounts)

    // For sync operations, we interpret the stats differently:
    // - "Completed" = successfully synced items
    // - "Pending" = accounts that haven't synced recently
    // - "Running" = accounts currently syncing (we can't easily detect this)
    // - "Failed" = accounts with sync errors
    // - "Error" = accounts with recent errors

    const completed = totalMessages // Successfully synced messages
    const pending = Math.max(0, totalAccounts - recentlyUpdatedAccounts) // Accounts not synced recently
    const running = 0 // We can't easily detect currently running syncs
    const failed = 0 // We don't track failed syncs in the current schema
    const error = 0 // We don't track sync errors in the current schema
    const total = totalMessages + pending
    const success_rate = total > 0 ? Math.round((completed / total) * 100) : 100

    const stats = {
      total,
      pending,
      running,
      completed,
      failed,
      error,
      success_rate,
      // Additional sync-specific stats
      totalAccounts,
      totalMessages,
      totalThreads,
      recentMessages,
      activeAccounts,
      recentlyUpdatedAccounts
    }

    return NextResponse.json({
      success: true,
      stats
    })
  } catch (error: any) {
    console.error('Error fetching Gmail sync stats:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
