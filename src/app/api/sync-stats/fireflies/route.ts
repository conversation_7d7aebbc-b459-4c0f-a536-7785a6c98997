import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Get Fireflies sync statistics
    const [accountsResult, transcriptsResult, recentSyncResult, errorAccountsResult] = await Promise.all([
      // Total accounts
      pool.query('SELECT COUNT(*) as count FROM fireflies_accounts'),
      // Total transcripts
      pool.query('SELECT COUNT(*) as count FROM fireflies_transcripts'),
      // Recent sync activity (last 24 hours)
      pool.query(`
        SELECT 
          COUNT(*) as recent_transcripts,
          COUNT(DISTINCT account_id) as active_accounts
        FROM fireflies_transcripts 
        WHERE created_at > NOW() - INTERVAL '24 hours'
      `),
      // Accounts with errors
      pool.query(`
        SELECT COUNT(*) as count 
        FROM fireflies_accounts 
        WHERE error_count > 0 OR last_error IS NOT NULL
      `)
    ])

    const totalAccounts = parseInt(accountsResult.rows[0].count)
    const totalTranscripts = parseInt(transcriptsResult.rows[0].count)
    const recentTranscripts = parseInt(recentSyncResult.rows[0].recent_transcripts)
    const activeAccounts = parseInt(recentSyncResult.rows[0].active_accounts)
    const errorAccounts = parseInt(errorAccountsResult.rows[0].count)

    // For sync operations, we interpret the stats differently:
    // - "Completed" = successfully synced transcripts
    // - "Pending" = accounts that haven't synced recently
    // - "Running" = accounts currently syncing (we can't easily detect this)
    // - "Failed" = accounts with sync errors
    // - "Error" = accounts with recent errors

    const completed = totalTranscripts // Successfully synced transcripts
    const pending = Math.max(0, totalAccounts - activeAccounts) // Accounts not synced recently
    const running = 0 // We can't easily detect currently running syncs
    const failed = errorAccounts // Accounts with sync errors
    const error = errorAccounts // Same as failed for sync operations
    const total = totalTranscripts + pending
    const success_rate = total > 0 ? Math.round((completed / total) * 100) : 100

    const stats = {
      total,
      pending,
      running,
      completed,
      failed,
      error,
      success_rate,
      // Additional sync-specific stats
      totalAccounts,
      totalTranscripts,
      recentTranscripts,
      activeAccounts,
      errorAccounts
    }

    return NextResponse.json({
      success: true,
      stats
    })
  } catch (error: any) {
    console.error('Error fetching Fireflies sync stats:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
