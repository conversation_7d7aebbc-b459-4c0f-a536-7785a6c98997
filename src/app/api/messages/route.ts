import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

const DEFAULT_WORKSPACE_ID = 'f8b45e3c-7186-4fdc-a696-c5971b1d2c7f';

// Updated to match actual database schema
interface Message {
  message_id: string;
  thread_id: string;
  from_email?: string;
  to_email?: string;
  subject: string;
  body: string;
  direction: string;
  role?: string;
  metadata?: any;
  created_at: string;
  sent_at?: string;
  smartlead_campaign_id?: string;
  derived_hook?: string;
  derived_subject?: string;
}

interface CreateMessageRequest {
  subject: string;
  body: string;
  to_email: string;
  from_email: string;
}

interface UpdateMessageRequest {
  status?: 'pending' | 'sent' | 'failed';
  error_message?: string;
}

/**
 * GET: Fetch all messages (with optional filtering)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const contactIdParam = searchParams.get('contact_id');
    
    // If contact_id is provided, use the thread_participants join logic
    if (contactIdParam) {
      return getContactMessages(contactIdParam, searchParams);
    }
    
    const offset = (page - 1) * limit;
    
    const whereClause = '';
    const queryParams: unknown[] = [];
    const paramIndex = 1;
    
    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM messages
      ${whereClause}
    `;
    const countResult = await pool.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);
    
    // Get messages
    queryParams.push(limit, offset);
    const messagesQuery = `
      SELECT 
        message_id,
        thread_id,
        from_email,
        to_email,
        subject,
        body,
        direction,
        role,
        metadata,
        created_at,
        sent_at,
        smartlead_campaign_id,
        derived_hook,
        derived_subject
      FROM messages
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    const result = await pool.query(messagesQuery, queryParams);
    
    return NextResponse.json({
      messages: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });
  } catch (err) {
    console.error('Error fetching messages:', err);
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to get messages for a specific contact
 */
async function getContactMessages(contactIdParam: string, searchParams: URLSearchParams) {
  try {
    // Get URL parameters for potential filtering
    const limit = parseInt(searchParams.get('limit') || '50', 10);
    const sort = searchParams.get('sort') || 'newest';
    const direction = searchParams.get('direction'); // Optional filter by direction
    
    // Parse contact_id to number
    const contactId = parseInt(contactIdParam, 10);
    
    if (isNaN(contactId)) {
      return NextResponse.json(
        { error: 'Invalid contact_id format' },
        { status: 400 }
      );
    }
    
    // First, get the contact's email
    const contactResult = await pool.query(
      'SELECT email FROM contacts WHERE contact_id = $1',
      [contactId]
    );
    
    if (contactResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }
    
    const contactEmail = contactResult.rows[0].email;
    
    // Build query to find messages matching the contact's email
    let query = `
      SELECT 
        m.message_id,
        m.thread_id,
        m.from_email,
        m.to_email,
        m.subject,
        m.body,
        m.direction,
        m.role,
        m.sent_at,
        m.created_at,
        m.smartlead_campaign_id,
        m.metadata,
        m.derived_hook,
        m.derived_subject
      FROM messages m
      WHERE (m.from_email = $1 OR m.to_email = $1)`;
    
    // Prepare query parameters
    const queryParams: (string | number)[] = [contactEmail];
    
    // Add direction filter if specified
    if (direction && ['inbound', 'outbound'].includes(direction)) {
      query += ` AND m.direction = $${queryParams.length + 1}`;
      queryParams.push(direction);
    }
    
    // Add sorting
    query += ` ORDER BY m.created_at ${sort === 'oldest' ? 'ASC' : 'DESC'}`;
    
    // Add limit
    query += ` LIMIT $${queryParams.length + 1}`;
    queryParams.push(limit);
    
    const result = await pool.query(query, queryParams);
    
    // Ensure all messages have the required fields, setting defaults if needed
    const messages = result.rows.map(msg => ({
      ...msg,
      direction: msg.direction || 'outbound', // Default to outbound if missing
      metadata: msg.metadata || {}
    }));
    
    return NextResponse.json(messages);
    
  } catch (err) {
    console.error('Error fetching messages:', err);
    return NextResponse.json(
      { error: `Error fetching messages: ${(err as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * POST: Create a new message for a contact
 */
export async function POST(req: NextRequest) {
  try {
    // Get the contact_id from query parameters
    const url = new URL(req.url);
    const contactIdParam = url.searchParams.get('contact_id');
    
    console.log('POST /api/messages - Request URL:', req.url);
    console.log('POST /api/messages - Contact ID from params:', contactIdParam);
    
    // Require contact_id parameter
    if (!contactIdParam) {
      console.error('POST /api/messages - Missing contact_id parameter');
      return NextResponse.json(
        { error: 'contact_id is required' },
        { status: 400 }
      );
    }
    
    // Parse contact_id to number
    const contactId = parseInt(contactIdParam, 10);
    
    if (isNaN(contactId)) {
      console.error('POST /api/messages - Invalid contact_id format:', contactIdParam);
      return NextResponse.json(
        { error: 'Invalid contact_id format' },
        { status: 400 }
      );
    }
    
    // Get request body
    let body;
    try {
      body = await req.json();
      // console.log('POST /api/messages - Request body:', JSON.stringify(body));
    } catch (error) {
      // console.error('POST /api/messages - Failed to parse request body:', error);
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }
    
    const { 
      subject, 
      body: messageBody, 
      direction = 'outbound',
      campaign_id,
      campaign_template,
      template_name,
      from_email,
      to_email,
      role = 'user',
      smartlead_campaign_id
    } = body;
    
    if (!subject || !messageBody) {
      console.error('POST /api/messages - Missing required fields:', { subject, messageBody });
      return NextResponse.json(
        { error: 'Subject and message body are required' },
        { status: 400 }
      );
    }
    
    // Begin transaction
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      // Check if the contact already has a thread
      const existingThreadQuery = `
        SELECT t.thread_id
        FROM threads t
        WHERE t.contact_id = $1
        ORDER BY thread_id
        LIMIT 1
      `;
      const existingThreadResult = await client.query(existingThreadQuery, [contactId]);
      
      let threadId;
      
      if (existingThreadResult.rows.length > 0) {
        // Use existing thread
        threadId = existingThreadResult.rows[0].thread_id;
        console.log(`Using existing thread ${threadId} for contact ${contactId}`);
      } else {
        // Create new thread if none exists
        const threadResult = await client.query(
          `INSERT INTO threads (thread_id, workspace_id, subject, status, created_at, contact_id, to_email)
           VALUES (gen_random_uuid(), $1, $2, 'active', CURRENT_TIMESTAMP, $3, $4)
           RETURNING thread_id`,
          [DEFAULT_WORKSPACE_ID, subject, contactId, to_email]
        );
        
        threadId = threadResult.rows[0].thread_id;
    
      }
      
      // Create metadata object for additional fields
      const metadata: Record<string, any> = {};
      if (campaign_id) {
        metadata.campaign_id = campaign_id;
      }
      if (campaign_template) {
        metadata.campaign_template = campaign_template;
      }
      if (template_name) {
        metadata.template_name = template_name;
      }
      
      // Use smartlead_campaign_id from the request if provided, otherwise use campaign_id
      const campaignIdForSmartlead = smartlead_campaign_id || campaign_id;
      
      // Create the message in the existing or new thread
      const messageResult = await client.query(
        `INSERT INTO messages 
         (message_id, thread_id, subject, body, direction, created_at, metadata, smartlead_campaign_id, from_email, to_email, role)
         VALUES (gen_random_uuid(), $1, $2, $3, $4, CURRENT_TIMESTAMP, $5, $6, $7, $8, $9)
         RETURNING *`,
        [threadId, subject, messageBody, direction, metadata, campaignIdForSmartlead || null, from_email || null, to_email || null, role || 'user']
      );
      
      // Mark the contact as having generated email content
      await client.query(
        `UPDATE contacts
         SET email_generated = true
         WHERE contact_id = $1`,
        [contactId]
      );
      
      // Commit transaction
      await client.query('COMMIT');
      
      return NextResponse.json({
        message_id: messageResult.rows[0].message_id,
        thread_id: threadId,
        subject,
        body: messageBody,
        direction,
        from_email,
        to_email,
        role,
        created_at: messageResult.rows[0].created_at,
        metadata,
        smartlead_campaign_id: campaignIdForSmartlead
      });
      
    } catch (error) {
      // Rollback transaction on error
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('Error creating message:', error);
    return NextResponse.json(
      { error: `Error creating message: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 