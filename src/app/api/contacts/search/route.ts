import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get("email");

    if (!email || email.length < 2) {
      return NextResponse.json([]);
    }

    const query = `
      SELECT 
        contact_id,
        first_name,
        last_name,
        email,
        personal_email,
        additional_email
      FROM public.contacts 
      WHERE 
        email ILIKE $1 OR 
        personal_email ILIKE $1
        additional_email ILIKE $1
      ORDER BY 
        CASE 
          WHEN email ILIKE $1 THEN 1 
          WHEN personal_email ILIKE $1 THEN 2
          WHEN additional_email ILIKE $1 THEN 3
          ELSE 2 
        END,
        first_name,
        last_name
      LIMIT 10
    `;

    const result = await pool.query(query, [`%${email}%`]);

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Error searching contacts:", error);
    return NextResponse.json(
      { error: "Failed to search contacts" },
      { status: 500 }
    );
  }
}
