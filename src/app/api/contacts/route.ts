import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const source = searchParams.get('source') || ''
    const category = searchParams.get('category') || ''
    const sort = searchParams.get('sort') || 'updated_at'
    const direction = searchParams.get('direction') || 'desc'
    const enrichedOnly = searchParams.get('enriched_only') === 'true'
    const emailGenerated = searchParams.get('email_generated') === 'true'
    const smartleadLeadId = searchParams.get('smartlead_lead_id')
    const campaignId = searchParams.get('campaign_id')
    const linkedinUrl = searchParams.get('linkedin_url')
    const companyId = searchParams.get('company_id')
    
    // New processing status filters
    const emailVerificationStatus = searchParams.get('email_verification_status')
    const emailGenerationStatus = searchParams.get('email_generation_status')
    const hasProcessingError = searchParams.get('has_processing_error') === 'true'
    
    const offset = (page - 1) * limit

    // Validate sort field to prevent SQL injection
    const validSortFields = [
      'contact_id', 'first_name', 'last_name', 'full_name', 
      'title', 'job_title', 'company_name', 'updated_at', 'source', 'email_status', 
      'enriched_date', 'email_generated', 'smartlead_lead_id'
    ];
    const sortField = validSortFields.includes(sort) ? sort : 'updated_at';
    
    // Validate direction
    const sortDirection = direction.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    // Map frontend sort fields to actual DB columns where needed
    let orderByField = sortField;
    if (sortField === 'job_title') orderByField = 'co.title';
    else if (sortField === 'updated_at') orderByField = 'co.updated_at';
    else if (sortField === 'full_name') orderByField = 'co.full_name';
    else if (sortField === 'company_name') orderByField = 'c.company_name';
    else if (sortField === 'source') orderByField = 'co.source';
    else if (sortField === 'email_status') orderByField = 'co.email_status';
    else if (sortField === 'enriched_date') orderByField = 'ce.created_at';
    else if (sortField === 'email_generated') orderByField = 'co.email_generated';
    else if (sortField === 'smartlead_lead_id') orderByField = 'co.smartlead_lead_id';

    // Use COALESCE to handle NULL values in sorting
    const orderByClause = sortField === 'email_status' 
      ? `COALESCE(${orderByField}, '') ${sortDirection}, co.full_name ASC`
      : sortField === 'enriched_date'
      ? `CASE WHEN ce.created_at IS NULL THEN 0 ELSE 1 END ${sortDirection}, ce.created_at ${sortDirection}, co.full_name ASC`
      : sortField === 'email_generated'
      ? `CASE WHEN co.email_generated IS NULL THEN 0 ELSE 1 END ${sortDirection}, co.email_generated ${sortDirection}, co.searched ${sortDirection}, co.extracted ${sortDirection}, co.full_name ASC`
      : sortField === 'smartlead_lead_id'
      ? `CASE WHEN co.smartlead_lead_id IS NULL THEN 0 ELSE 1 END ${sortDirection}, co.smartlead_lead_id ${sortDirection}, co.email_generated ${sortDirection}, co.searched ${sortDirection}, co.extracted ${sortDirection}, co.full_name ASC`
      : `${orderByField} ${sortDirection}`;

    const whereClauses = [
      `(
        LOWER(co.first_name) LIKE LOWER($3) OR
        LOWER(co.last_name) LIKE LOWER($3) OR
        LOWER(co.full_name) LIKE LOWER($3) OR
        LOWER(c.company_name) LIKE LOWER($3) OR
        LOWER(co.title) LIKE LOWER($3)
      )`
    ];
    const params:any[] = [limit, offset, `%${search}%`];

    if (source) {
      whereClauses.push(`co.source = $${params.length + 1}`);
      params.push(source);
    }
    
    // Add filter for contact category
    if (category) {
      whereClauses.push(`co.category = $${params.length + 1}`);
      params.push(category);
    }
    
    // Add filter for extracted contacts
    if (enrichedOnly) {
      whereClauses.push(`ce.contact_id IS NOT NULL`);
    }

    // Add filter for email generated contacts
    if (emailGenerated) {
      whereClauses.push(`co.email_generated = true`);
    }

    // Add filter for contacts with smartlead_lead_id
    if (smartleadLeadId === 'not_null') {
      whereClauses.push(`co.smartlead_lead_id IS NOT NULL`);
    }

    // Add filter for LinkedIn URL search
    if (linkedinUrl) {
      whereClauses.push(`LOWER(co.linkedin_url) = LOWER($${params.length + 1})`);
      params.push(linkedinUrl);
    }

    // Add filter for company_id search
    if (companyId) {
      whereClauses.push(`co.company_id = $${params.length + 1}`);
      params.push(parseInt(companyId));
    }

    // Add new processing status filters
    if (emailVerificationStatus === 'completed') {
      whereClauses.push(`co.email_verification_status = 'completed'`);
    }
    
    if (emailGenerationStatus === 'completed') {
      whereClauses.push(`co.email_generation_status = 'completed'`);
    }
    
    if (hasProcessingError) {
      whereClauses.push(`(
        co.email_verification_error IS NOT NULL OR
        co.contact_enrichment_v2_error IS NOT NULL OR
        co.contact_investment_criteria_error IS NOT NULL OR
        co.email_generation_error IS NOT NULL OR
        co.email_sending_error IS NOT NULL
      )`);
    }

    if (campaignId) {
      whereClauses.push(`co.contact_id IN (
        SELECT tp.contact_id
        FROM thread_participants tp
        JOIN messages m ON tp.thread_id = m.thread_id
        WHERE m.smartlead_campaign_id = $${params.length + 1}
      )`);
      params.push(campaignId);
    }

    const query = `
      SELECT 
        co.contact_id::integer AS contact_id,
        co.first_name,
        co.last_name,
        co.full_name,
        co.title as job_title,
        co.headline,
        co.seniority,
        co.email,
        co.personal_email,
        co.email_status,
        co.linkedin_url,
        co.phone_number,
        co.contact_city,
        co.contact_state,
        co.contact_country,
        co.region,
        co.notes,
        co.source,
        co.updated_at,
        c.company_name,
        c.company_city,
        c.company_state,
        c.company_website,
        c.industry,
        co.smartlead_lead_id,
        co.smartlead_status,
        ce.created_at as enriched_date,
        co.email_generated as email_generated,
        co.email_validated_date,
        co.email_verification_status,
        co.email_verification_error,
        co.contact_enrichment_v2_status,
        co.contact_enrichment_v2_error,
        co.contact_investment_criteria_status,
        co.contact_investment_criteria_error,
        co.email_generation_status,
        co.email_generation_error,
        COUNT(*) OVER() as total_count
      FROM contacts co
      LEFT JOIN companies c ON co.company_id = c.company_id
      LEFT JOIN contact_enrichment ce ON co.contact_id = ce.contact_id
      WHERE ${whereClauses.join(' AND ')}
      ORDER BY ${orderByClause}
      LIMIT $1 OFFSET $2
    `
    
    console.log('Executing query with sort:', orderByField, sortDirection);
    const result = await pool.query(query, params)
    
    const totalCount = result.rows[0]?.total_count || 0
    console.log('API response constructed with:', {
      contactsCount: result.rows.length,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page
    })
    return NextResponse.json({
      contacts: result.rows,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      totalCount
    })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch contacts' }, 
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const {
      first_name,
      last_name,
      email,
      additional_email,
      personal_email,
      title,
      job_tier,
      phone_number,
      phone_number_secondary,
      company_name,
      company_website,
      industry,
      company_address,
      company_city,
      company_state,
      company_country,
      company_zip,
      contact_address,
      contact_city,
      contact_state,
      contact_country,
      contact_zip_code,
      region,
      linkedin_url,
      twitter,
      facebook,
      instagram,
      youtube,
      // Education fields
      education_college,
      education_college_year_graduated,
      education_high_school,
      education_high_school_year_graduated,
      // Personal details
      age,
      honorable_achievements,
      hobbies,
      // Executive summary and career
      executive_summary,
      career_timeline,
      // Contact details
      contact_type,
      relationship_owner,
      role_in_decision_making,
      last_contact_date,
      source_of_introduction,
      accredited_investor_status,
      kyc_status,
      company_id: providedCompanyId,
      notes
    } = body

    // Validate that at least some basic contact information is provided
    const hasEmail = email || additional_email || personal_email;
    const hasName = first_name || last_name;
    const hasLinkedIn = linkedin_url;
    
    if (!hasEmail && !hasName && !hasLinkedIn) {
      return NextResponse.json(
        { error: 'At least one of the following is required: email, name, or LinkedIn URL' },
        { status: 400 }
      )
    }
    let company_id = providedCompanyId;

    // If no company_id provided, create or update company
    if (!company_id) {
      // First try to find existing company by name
      const existingCompany = await pool.query(
        `SELECT company_id FROM companies WHERE company_name = $1`,
        [company_name]
      );

      if (existingCompany.rows.length > 0) {
        // Company exists, use its ID
        company_id = existingCompany.rows[0].company_id;
        
        // Optionally update the company with new information
        await pool.query(
          `UPDATE companies SET 
            company_website = COALESCE($1, company_website),
            industry = COALESCE($2, industry),
            company_address = COALESCE($3, company_address),
            company_city = COALESCE($4, company_city),
            company_state = COALESCE($5, company_state),
            company_country = COALESCE($6, company_country),
            company_zip = COALESCE($7, company_zip)
          WHERE company_id = $8`,
          [company_website, industry, company_address, company_city, company_state, company_country, company_zip, company_id]
        );
      } else {
        // Company doesn't exist, create new one
        const companyResult = await pool.query(
          `INSERT INTO companies (
            company_name, 
            company_website, 
            industry, 
            company_address, 
            company_city, 
            company_state, 
            company_country,
            company_zip
          ) 
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          RETURNING company_id`,
          [company_name, company_website, industry, company_address, company_city, company_state, company_country, company_zip]
        );
        company_id = companyResult.rows[0].company_id;
      }
    }

    // Prepare extra attributes for additional fields and multiple investment criteria
    const extraAttrs: Record<string, any> = {};
    
    // Store new contact fields in extra_attrs since they don't exist in the database yet
    if (phone_number_secondary) extraAttrs.phone_number_secondary = phone_number_secondary;
    if (contact_address) extraAttrs.contact_address = contact_address;
    if (contact_zip_code) extraAttrs.contact_zip_code = contact_zip_code;
    


    // Helper function to convert empty strings to null for timestamp fields
    const sanitizeTimestamp = (value: any) => {
      return value === '' || value === null || value === undefined ? null : value;
    };

    // Insert the contact with all the new dedicated columns using final processed values
    const contactResult = await pool.query(
      `INSERT INTO contacts (
        first_name, 
        last_name,
        full_name,
        title,
        job_tier,
        email,
        additional_email, 
        personal_email,
        phone_number,
        phone_number_secondary,
        linkedin_url,
        twitter,
        facebook,
        instagram,
        youtube,
        education_college,
        education_college_year_graduated,
        education_high_school,
        education_high_school_year_graduated,
        age,
        honorable_achievements,
        hobbies,
        executive_summary,
        career_timeline,
        contact_address,
        contact_city,
        contact_state,
        contact_country,
        contact_zip_code,
        contact_type,
        relationship_owner,
        role_in_decision_making,
        last_contact_date,
        source_of_introduction,
        accredited_investor_status,
        kyc_status,
        company_id,
        region,
        notes,
        extra_attrs,
        source
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41)
      RETURNING contact_id`,
      [
        first_name, // 1
        last_name, // 2
        // Generate full_name properly - if both first and last name are empty, use null
        (first_name && last_name) ? `${first_name} ${last_name}` : 
        (first_name || last_name) ? `${first_name || ''} ${last_name || ''}`.trim() : null, // 3
        title, // 4
        job_tier, // 5
        email,// 6
        additional_email, // 7
        personal_email, // 8
        phone_number, // 9
        phone_number_secondary, // 10
        linkedin_url,// 11
        twitter, // 12
        facebook, // 13
        instagram, // 14
        youtube, // 15
        education_college, // 16
        education_college_year_graduated, // 17
        education_high_school, // 18
        education_high_school_year_graduated, // 19
        age, // 20
        JSON.stringify(honorable_achievements || []), // 21
        JSON.stringify(hobbies || []), // 22
        executive_summary, // 23
        JSON.stringify(career_timeline || []),//24
        contact_address,// 25
        contact_city,// 26
        contact_state,// 27
        contact_country,// 28
        contact_zip_code,// 29
        contact_type,// 30
        relationship_owner,// 31
        role_in_decision_making,// 32   
        sanitizeTimestamp(last_contact_date),// 33
        source_of_introduction,// 34
        accredited_investor_status,// 35
        kyc_status,// 36
        company_id,// 37
        region,// 38
        notes,// 39
        JSON.stringify(extraAttrs),// 40
        'manual' // 41 Set source as manual for contacts created through the form
      ]
    )

    const contact_id = contactResult.rows[0].contact_id

    // Trigger normalization for the new contact
    try {
      const { DataNormalizationService } = await import('@/lib/services/dataNormalizationService')
      await DataNormalizationService.normalizeContact(contact_id)
      console.log(`Contact normalization completed for contact ID: ${contact_id}`)
    } catch (normalizeError) {
      console.warn('Error triggering contact normalization:', normalizeError)
      // Don't fail the contact creation if normalization fails
    }

    return NextResponse.json(contactResult.rows[0])
  } catch (error) {
    console.error('Error adding contact:', error)
    return NextResponse.json(
      { error: 'Failed to add contact' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const contactId = searchParams.get('contactId')
    
    if (!contactId) {
      return NextResponse.json(
        { error: 'Contact ID is required' },
        { status: 400 }
      )
    }

    // Start a transaction to ensure all deletions succeed or fail together
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')

      // Delete from dependent tables first (in order of dependency)
      // 1. Delete from contact_enrichment
      await client.query(
        'DELETE FROM contact_enrichment WHERE contact_id = $1',
        [contactId]
      )

      // 2. Delete from contact_extracted_data
      await client.query(
        'DELETE FROM contact_extracted_data WHERE contact_id = $1',
        [contactId]
      )

      // 3. Delete from contact_normalized_data
      await client.query(
        'DELETE FROM contact_normalized_data WHERE contact_id = $1',
        [contactId]
      )

      // 4. Delete from contact_searched_data
      await client.query(
        'DELETE FROM contact_searched_data WHERE contact_id = $1',
        [contactId]
      )

      // 5. Delete from deal_contacts
      await client.query(
        'DELETE FROM deal_contacts WHERE contact_id = $1',
        [contactId]
      )

      // Finally, delete the contact
      const result = await client.query(
        'DELETE FROM contacts WHERE contact_id = $1 RETURNING contact_id',
        [contactId]
      )

      if (result.rowCount === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Contact not found' },
          { status: 404 }
        )
      }

      await client.query('COMMIT')

      return NextResponse.json({ 
        success: true, 
        message: 'Contact deleted successfully' 
      })
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error deleting contact:', error)
    return NextResponse.json(
      { error: 'Failed to delete contact' },
      { status: 500 }
    )
  }
} 