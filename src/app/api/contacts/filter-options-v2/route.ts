import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const section = searchParams.get('section');
  
  try {
    let data = {};

    switch (section) {
      case 'core_contacts':
        data = await fetchCoreContactsOptions();
        break;
      case 'enrichment_v2':
        data = await fetchEnrichmentV2Options();
        break;
      case 'investment_criteria':
        data = await fetchInvestmentCriteriaOptions();
        break;
      default:
        return NextResponse.json({ error: 'Invalid section parameter. Use: core_contacts, enrichment_v2, or investment_criteria' }, { status: 400 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching contact filter options V2:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      section: section
    });
    return NextResponse.json(
      { 
        error: 'Failed to fetch filter options',
        details: error instanceof Error ? error.message : 'Unknown error',
        section: section
      },
      { status: 500 }
    );
  }
}

async function fetchEnrichmentV2Options() {
  // console.log('🔍 fetchEnrichmentV2Options: Starting...');
  
  const client = await pool.connect();
  try {
    // console.log('🔍 fetchEnrichmentV2Options: Connected to database');
    
    // Contact Types from V2 enrichment data
    const contactTypesQuery = `
      SELECT DISTINCT contact_type as value, contact_type as label, COUNT(*) as count
      FROM contacts 
      WHERE contact_type IS NOT NULL AND contact_type != ''
      GROUP BY contact_type
      ORDER BY count DESC, contact_type ASC
    `;
    const contactTypesResult = await client.query(contactTypesQuery);

    // Relationship Owners
    const relationshipOwnersQuery = `
      SELECT DISTINCT relationship_owner as value, relationship_owner as label, COUNT(*) as count
      FROM contacts 
      WHERE relationship_owner IS NOT NULL AND relationship_owner != ''
      GROUP BY relationship_owner
      ORDER BY count DESC, relationship_owner ASC
      LIMIT 50
    `;
    const relationshipOwnersResult = await client.query(relationshipOwnersQuery);

    // Roles in Decision Making
    const rolesInDecisionMakingQuery = `
      SELECT DISTINCT role_in_decision_making as value, role_in_decision_making as label, COUNT(*) as count
      FROM contacts 
      WHERE role_in_decision_making IS NOT NULL AND role_in_decision_making != ''
      GROUP BY role_in_decision_making
      ORDER BY count DESC, role_in_decision_making ASC
      LIMIT 30
    `;
    const rolesInDecisionMakingResult = await client.query(rolesInDecisionMakingQuery);

    // Sources of Introduction
    const sourcesOfIntroductionQuery = `
      SELECT DISTINCT source_of_introduction as value, source_of_introduction as label, COUNT(*) as count
      FROM contacts 
      WHERE source_of_introduction IS NOT NULL AND source_of_introduction != ''
      GROUP BY source_of_introduction
      ORDER BY count DESC, source_of_introduction ASC
      LIMIT 50
    `;
    const sourcesOfIntroductionResult = await client.query(sourcesOfIntroductionQuery);

    // Education - Colleges
    const educationCollegesQuery = `
      SELECT DISTINCT education_college as value, education_college as label, COUNT(*) as count
      FROM contacts 
      WHERE education_college IS NOT NULL AND education_college != ''
      GROUP BY education_college
      ORDER BY count DESC, education_college ASC
      LIMIT 200
    `;
    const educationCollegesResult = await client.query(educationCollegesQuery);

    // Education - College Year Graduated
    const educationCollegeYearGraduatedQuery = `
      SELECT DISTINCT education_college_year_graduated as value, education_college_year_graduated as label, COUNT(*) as count
      FROM contacts 
      WHERE education_college_year_graduated IS NOT NULL AND education_college_year_graduated != ''
      GROUP BY education_college_year_graduated
      ORDER BY count DESC, education_college_year_graduated ASC
      LIMIT 100
    `;
    const educationCollegeYearGraduatedResult = await client.query(educationCollegeYearGraduatedQuery);



    // Age Ranges
    const ageRangesQuery = `
      SELECT DISTINCT age as value, age as label, COUNT(*) as count
      FROM contacts 
      WHERE age IS NOT NULL AND age != ''
      GROUP BY age
      ORDER BY count DESC, age ASC
    `;
    const ageRangesResult = await client.query(ageRangesQuery);

    const result = {
      contactTypes: contactTypesResult.rows,
      relationshipOwners: relationshipOwnersResult.rows,
      rolesInDecisionMaking: rolesInDecisionMakingResult.rows,
      sourcesOfIntroduction: sourcesOfIntroductionResult.rows,
      educationColleges: educationCollegesResult.rows,
      educationCollegeYearGraduated: educationCollegeYearGraduatedResult.rows,
      ageRanges: ageRangesResult.rows
    };
    
    // console.log('🔍 fetchEnrichmentV2Options: Returning enhanced V2 result with', Object.keys(result).length, 'categories');
    return result;
    
  } finally {
    // console.log('🔍 fetchEnrichmentV2Options: Releasing database connection');
    client.release();
  }
}

async function fetchCoreContactsOptions() {
  console.log('🔍 fetchCoreContactsOptions: Starting...');
  
  const client = await pool.connect();
  try {
    console.log('🔍 fetchCoreContactsOptions: Connected to database');
    
    // Sources
    const sourcesQuery = `
      SELECT DISTINCT source as value, source as label, COUNT(*) as count
      FROM contacts 
      WHERE source IS NOT NULL AND source != ''
      GROUP BY source
      ORDER BY count DESC, source ASC
    `;
    const sourcesResult = await client.query(sourcesQuery);

    // Email Status
    const emailStatusQuery = `
      SELECT DISTINCT email_status as value, email_status as label, COUNT(*) as count
      FROM contacts 
      WHERE email_status IS NOT NULL AND email_status != ''
      GROUP BY email_status
      ORDER BY count DESC, email_status ASC
    `;
    const emailStatusResult = await client.query(emailStatusQuery);

    // Email Verification Status
    const emailVerificationStatusQuery = `
      SELECT DISTINCT email_verification_status as value, email_verification_status as label, COUNT(*) as count
      FROM contacts 
      WHERE email_verification_status IS NOT NULL AND email_verification_status != ''
      GROUP BY email_verification_status
      ORDER BY count DESC, email_verification_status ASC
    `;
    const emailVerificationStatusResult = await client.query(emailVerificationStatusQuery);

    // Job Tiers
    const jobTiersQuery = `
      SELECT DISTINCT job_tier as value, job_tier as label, COUNT(*) as count
      FROM contacts 
      WHERE job_tier IS NOT NULL AND job_tier != ''
      GROUP BY job_tier
      ORDER BY count DESC, job_tier ASC
    `;
    const jobTiersResult = await client.query(jobTiersQuery);

    // Contact Countries
    const contactCountriesQuery = `
      SELECT DISTINCT contact_country as value, contact_country as label, COUNT(*) as count
      FROM contacts 
      WHERE contact_country IS NOT NULL AND contact_country != ''
      GROUP BY contact_country
      ORDER BY count DESC, contact_country ASC
    `;
    const contactCountriesResult = await client.query(contactCountriesQuery);

    // Contact States
    const contactStatesQuery = `
      SELECT DISTINCT contact_state as value, contact_state as label, COUNT(*) as count
      FROM contacts 
      WHERE contact_state IS NOT NULL AND contact_state != ''
      GROUP BY contact_state
      ORDER BY count DESC, contact_state ASC
    `;
    const contactStatesResult = await client.query(contactStatesQuery);

    // Contact Cities
    const contactCitiesQuery = `
      SELECT DISTINCT contact_city as value, contact_city as label, COUNT(*) as count
      FROM contacts 
      WHERE contact_city IS NOT NULL AND contact_city != ''
      GROUP BY contact_city
      ORDER BY count DESC, contact_city ASC
      LIMIT 200
    `;
    const contactCitiesResult = await client.query(contactCitiesQuery);

    const result = {
      sources: sourcesResult.rows,
      emailStatus: emailStatusResult.rows,
      emailVerificationStatus: emailVerificationStatusResult.rows,
      jobTiers: jobTiersResult.rows,
      contactCountries: contactCountriesResult.rows,
      contactStates: contactStatesResult.rows,
      contactCities: contactCitiesResult.rows
    };
    
    console.log('🔍 fetchCoreContactsOptions: Returning core contacts result with', Object.keys(result).length, 'categories');
    return result;
    
  } finally {
    console.log('🔍 fetchCoreContactsOptions: Releasing database connection');
    client.release();
  }
}

async function fetchInvestmentCriteriaOptions() {
  console.log('🔍 fetchInvestmentCriteriaOptions: Starting...');
  
  const client = await pool.connect();
  try {
    console.log('🔍 fetchInvestmentCriteriaOptions: Connected to database');
    
    // Helper function to extract array elements from investment criteria central
    const extractICArrayElements = (arrayField: string) => {
      return `
        SELECT DISTINCT unnest(${arrayField}) as value, unnest(${arrayField}) as label, COUNT(*) as count
        FROM investment_criteria_central 
        WHERE ${arrayField} IS NOT NULL AND array_length(${arrayField}, 1) > 0 
        AND entity_type = 'contact'
        GROUP BY unnest(${arrayField})
        ORDER BY count DESC, unnest(${arrayField}) ASC
        LIMIT 200
      `;
    };

    // Capital Position from centralized table (text field, not array)
    const capitalPositionsQuery = `
      SELECT DISTINCT capital_position as value, capital_position as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE capital_position IS NOT NULL AND capital_position != '' 
      AND entity_type = 'contact'
      GROUP BY capital_position
      ORDER BY count DESC, capital_position ASC
    `;
    const capitalPositionsResult = await client.query(capitalPositionsQuery);

    // Property Types from central mapping (normalized values)
    const propertyTypesQuery = `
      SELECT DISTINCT 
        CASE 
          WHEN value_2 IS NOT NULL AND value_2 != '' 
          THEN value_2 
          ELSE value_1 
        END as value,
        CASE 
          WHEN value_2 IS NOT NULL AND value_2 != '' 
          THEN value_2 
          ELSE value_1 
        END as label,
        1 as count
      FROM central_mapping 
      WHERE type = 'Property Type' 
        AND is_active = true 
        AND (value_1 IS NOT NULL AND value_1 != '')
      ORDER BY value ASC
    `;
    const propertyTypesResult = await client.query(propertyTypesQuery);

    // Property Subcategories from centralized table
    const propertySubcategoriesResult = await client.query(extractICArrayElements('property_subcategories'));

    // Strategies from central mapping (normalized values)
    const strategiesQuery = `
      SELECT DISTINCT 
        CASE 
          WHEN value_2 IS NOT NULL AND value_2 != '' 
          THEN value_2 
          ELSE value_1 
        END as value,
        CASE 
          WHEN value_2 IS NOT NULL AND value_2 != '' 
          THEN value_2 
          ELSE value_1 
        END as label,
        1 as count
      FROM central_mapping 
      WHERE type = 'Strategies' 
        AND is_active = true 
        AND (value_1 IS NOT NULL AND value_1 != '')
      ORDER BY value ASC
    `;
    const strategiesResult = await client.query(strategiesQuery);

    // Countries from centralized table
    const countriesResult = await client.query(extractICArrayElements('country'));

    // Regions from centralized table
    const regionsResult = await client.query(extractICArrayElements('region'));

    // States from centralized table
    const statesResult = await client.query(extractICArrayElements('state'));

    // Cities from centralized table
    const citiesResult = await client.query(extractICArrayElements('city'));

    // Debt-specific fields from investment_criteria_debt table
    const structuredLoanTranchesQuery = `
      SELECT DISTINCT structured_loan_tranche as value, structured_loan_tranche as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.structured_loan_tranche IS NOT NULL AND icd.structured_loan_tranche != ''
      AND icc.entity_type = 'contact'
      GROUP BY structured_loan_tranche
      ORDER BY count DESC, structured_loan_tranche ASC
    `;
    const structuredLoanTranchesResult = await client.query(structuredLoanTranchesQuery);

    const loanProgramsQuery = `
      SELECT DISTINCT loan_program as value, loan_program as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.loan_program IS NOT NULL AND icd.loan_program != ''
      AND icc.entity_type = 'contact'
      GROUP BY loan_program
      ORDER BY count DESC, loan_program ASC
      LIMIT 100
    `;
    const loanProgramsResult = await client.query(loanProgramsQuery);

    const recourseLoansQuery = `
      SELECT DISTINCT recourse_loan as value, recourse_loan as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.recourse_loan IS NOT NULL AND icd.recourse_loan != ''
      AND icc.entity_type = 'contact'
      GROUP BY recourse_loan
      ORDER BY count DESC, recourse_loan ASC
    `;
    const recourseLoansResult = await client.query(recourseLoansQuery);

    const loanTypesQuery = `
      SELECT DISTINCT loan_type as value, loan_type as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.loan_type IS NOT NULL AND icd.loan_type != ''
      AND icc.entity_type = 'contact'
      GROUP BY loan_type
      ORDER BY count DESC, loan_type ASC
    `;
    const loanTypesResult = await client.query(loanTypesQuery);

    const loanTypesNormalizedQuery = `
      SELECT DISTINCT loan_type_normalized as value, loan_type_normalized as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.loan_type_normalized IS NOT NULL AND icd.loan_type_normalized != ''
      AND icc.entity_type = 'contact'
      GROUP BY loan_type_normalized
      ORDER BY count DESC, loan_type_normalized ASC
    `;
    const loanTypesNormalizedResult = await client.query(loanTypesNormalizedQuery);

    // Additional Central table fields
    const decisionMakingProcessesQuery = `
      SELECT DISTINCT decision_making_process as value, decision_making_process as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE decision_making_process IS NOT NULL AND decision_making_process != ''
      AND entity_type = 'contact'
      GROUP BY decision_making_process
      ORDER BY count DESC, decision_making_process ASC
    `;
    const decisionMakingProcessesResult = await client.query(decisionMakingProcessesQuery);

    // Additional Debt-specific fields - Missing from CSV
    const loanMinDebtYieldQuery = `
      SELECT DISTINCT loan_min_debt_yield as value, loan_min_debt_yield as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.loan_min_debt_yield IS NOT NULL AND icd.loan_min_debt_yield != ''
      AND icc.entity_type = 'contact'
      GROUP BY loan_min_debt_yield
      ORDER BY count DESC, loan_min_debt_yield ASC
    `;
    const loanMinDebtYieldResult = await client.query(loanMinDebtYieldQuery);

    // Additional Debt-specific fields
    const eligibleBorrowersQuery = `
      SELECT DISTINCT eligible_borrower as value, eligible_borrower as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.eligible_borrower IS NOT NULL AND icd.eligible_borrower != ''
      AND icc.entity_type = 'contact'
      GROUP BY eligible_borrower
      ORDER BY count DESC, eligible_borrower ASC
    `;
    const eligibleBorrowersResult = await client.query(eligibleBorrowersQuery);

    const lienPositionsQuery = `
      SELECT DISTINCT lien_position as value, lien_position as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.lien_position IS NOT NULL AND icd.lien_position != ''
      AND icc.entity_type = 'contact'
      GROUP BY lien_position
      ORDER BY count DESC, lien_position ASC
    `;
    const lienPositionsResult = await client.query(lienPositionsQuery);

    const rateLocksQuery = `
      SELECT DISTINCT rate_lock as value, rate_lock as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.rate_lock IS NOT NULL AND icd.rate_lock != ''
      AND icc.entity_type = 'contact'
      GROUP BY rate_lock
      ORDER BY count DESC, rate_lock ASC
    `;
    const rateLocksResult = await client.query(rateLocksQuery);

    const rateTypesQuery = `
      SELECT DISTINCT rate_type as value, rate_type as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.rate_type IS NOT NULL AND icd.rate_type != ''
      AND icc.entity_type = 'contact'
      GROUP BY rate_type
      ORDER BY count DESC, rate_type ASC
    `;
    const rateTypesResult = await client.query(rateTypesQuery);

    const amortizationsQuery = `
      SELECT DISTINCT amortization as value, amortization as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.amortization IS NOT NULL AND icd.amortization != ''
      AND icc.entity_type = 'contact'
      GROUP BY amortization
      ORDER BY count DESC, amortization ASC
    `;
    const amortizationsResult = await client.query(amortizationsQuery);

    // Additional Debt-specific fields from CSV
    const futureFacilitiesQuery = `
      SELECT DISTINCT future_facilities as value, future_facilities as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.future_facilities IS NOT NULL AND icd.future_facilities != ''
      AND icc.entity_type = 'contact'
      GROUP BY future_facilities
      ORDER BY count DESC, future_facilities ASC
    `;
    const futureFacilitiesResult = await client.query(futureFacilitiesQuery);

    const occupancyRequirementsQuery = `
      SELECT DISTINCT occupancy_requirements as value, occupancy_requirements as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.occupancy_requirements IS NOT NULL AND icd.occupancy_requirements != ''
      AND icc.entity_type = 'contact'
      GROUP BY occupancy_requirements
      ORDER BY count DESC, occupancy_requirements ASC
    `;
    const occupancyRequirementsResult = await client.query(occupancyRequirementsQuery);

    const prepaymentQuery = `
      SELECT DISTINCT prepayment as value, prepayment as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.prepayment IS NOT NULL AND icd.prepayment != ''
      AND icc.entity_type = 'contact'
      GROUP BY prepayment
      ORDER BY count DESC, prepayment ASC
    `;
    const prepaymentResult = await client.query(prepaymentQuery);

    const yieldMaintenanceQuery = `
      SELECT DISTINCT yield_maintenance as value, yield_maintenance as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.yield_maintenance IS NOT NULL AND icd.yield_maintenance != ''
      AND icc.entity_type = 'contact'
      GROUP BY yield_maintenance
      ORDER BY count DESC, yield_maintenance ASC
    `;
    const yieldMaintenanceResult = await client.query(yieldMaintenanceQuery);

    // Equity-specific fields
    const ownershipRequirementsQuery = `
      SELECT DISTINCT ownership_requirement as value, ownership_requirement as label, COUNT(*) as count
      FROM investment_criteria_equity ice
      JOIN investment_criteria_central icc ON ice.investment_criteria_id = icc.investment_criteria_id
      WHERE ice.ownership_requirement IS NOT NULL AND ice.ownership_requirement != ''
      AND icc.entity_type = 'contact'
      GROUP BY ownership_requirement
      ORDER BY count DESC, ownership_requirement ASC
    `;
    const ownershipRequirementsResult = await client.query(ownershipRequirementsQuery);

    const result = {
      capitalPositions: capitalPositionsResult.rows,
      propertyTypes: propertyTypesResult.rows,
      propertySubcategories: propertySubcategoriesResult.rows,
      strategies: strategiesResult.rows,
      countries: countriesResult.rows,
      regions: regionsResult.rows,
      states: statesResult.rows,
      cities: citiesResult.rows,
      decisionMakingProcesses: decisionMakingProcessesResult.rows,
      eligibleBorrowers: eligibleBorrowersResult.rows,
      lienPositions: lienPositionsResult.rows,
      recourseLoans: recourseLoansResult.rows,
      loanMinDebtYield: loanMinDebtYieldResult.rows,
      futureFacilities: futureFacilitiesResult.rows,
      occupancyRequirements: occupancyRequirementsResult.rows,
      prepayments: prepaymentResult.rows, // Fixed: UI expects plural
      yieldMaintenances: yieldMaintenanceResult.rows, // Fixed: UI expects plural
      rateLocks: rateLocksResult.rows,
      rateTypes: rateTypesResult.rows,
      ownershipRequirements: ownershipRequirementsResult.rows,
    };
    
    console.log('🔍 fetchInvestmentCriteriaOptions: Returning investment criteria result with', Object.keys(result).length, 'categories');
    return result;
    
  } finally {
    console.log('🔍 fetchInvestmentCriteriaOptions: Releasing database connection');
    client.release();
  }
}