import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q") || "";
    const limit = parseInt(searchParams.get("limit") || "20");

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    // Normalize the search query - ensure proper URL decoding and handle multiple spaces
    const normalizedQuery = decodeURIComponent(query).trim().replace(/\s+/g, ' ').toLowerCase();
    
    // Build search conditions
    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Add ILIKE conditions for each field with normalization
    const searchFields = [
      'TRIM(REGEXP_REPLACE(c.first_name, \'\\s+\', \' \', \'g\'))',
      'TRIM(REGEXP_REPLACE(c.last_name, \'\\s+\', \' \', \'g\'))', 
      'TRIM(REGEXP_REPLACE(c.full_name, \'\\s+\', \' \', \'g\'))',
      'c.email',
      'TRIM(REGEXP_REPLACE(c.title, \'\\s+\', \' \', \'g\'))',
      'TRIM(REGEXP_REPLACE(c.company_name, \'\\s+\', \' \', \'g\'))'
    ];

    for (const field of searchFields) {
      whereConditions.push(`${field} ILIKE $${paramIndex}`);
      queryParams.push(`%${normalizedQuery}%`);
      paramIndex++;
    }

    // Add limit parameter
    queryParams.push(limit);

    const sql = `
      SELECT 
        c.contact_id,
        c.first_name,
        c.last_name,
        c.full_name,
        c.email,
        c.title,
        c.company_name,
        c.contact_city,
        c.contact_state,
        c.contact_country,
        c.linkedin_url,
        c.phone_number,
        c.created_at,
        c.updated_at
      FROM contacts c
      WHERE ${whereConditions.join(' OR ')}
      ORDER BY 
        CASE 
          WHEN TRIM(REGEXP_REPLACE(c.full_name, '\\s+', ' ', 'g')) ILIKE $1 THEN 1
          WHEN TRIM(REGEXP_REPLACE(c.first_name, '\\s+', ' ', 'g')) ILIKE $1 OR TRIM(REGEXP_REPLACE(c.last_name, '\\s+', ' ', 'g')) ILIKE $1 THEN 2
          WHEN c.email ILIKE $1 THEN 3
          ELSE 4
        END,
        TRIM(REGEXP_REPLACE(c.full_name, '\\s+', ' ', 'g')),
        c.created_at DESC
      LIMIT $${paramIndex}
    `;

    const result = await pool.query(sql, queryParams);

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Error searching contacts:", error);
    return NextResponse.json(
      { error: "Failed to search contacts" },
      { status: 500 }
    );
  }
} 