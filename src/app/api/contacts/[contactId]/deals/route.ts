import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params;
    
    // Query to get V2 deals for the contact
    const query = `
      SELECT DISTINCT 
        dv2.deal_id::text as deal_id,
        dv2.deal_name,
        'Real Estate' as deal_type, -- V2 deals are real estate focused
        COALESCE(dv2.ask_amount[1], 0) as deal_size,
        dv2.deal_stage,
        dv2.created_at,
        dv2.updated_at,
        dv2.summary as deal_description,
        CONCAT(
          COALESCE(p.address, ''), 
          CASE WHEN p.city IS NOT NULL THEN ', ' || p.city ELSE '' END,
          CASE WHEN p.state IS NOT NULL THEN ', ' || p.state ELSE '' END
        ) as deal_location,
        'v2' as deal_version,
        dv2.deal_id::text as original_deal_id
      FROM dealsv2 dv2
      INNER JOIN deal_contacts dc ON dv2.deal_id = dc.deal_v2_id
      LEFT JOIN properties p ON dv2.property_id = p.property_id
      WHERE dc.contact_id = $1 
        AND dc.deal_version = 'v2'
        AND dc.deal_v2_id IS NOT NULL
      
      ORDER BY created_at DESC
    `;
    
    const result = await pool.query(query, [contactId]);
    
    // Process the results to ensure proper data formatting
    const deals = result.rows.map(deal => ({
      ...deal,
      deal_size: deal.deal_size ? parseFloat(deal.deal_size) : null,
      deal_location: deal.deal_location ? deal.deal_location.trim().replace(/^,\s*/, '') : null // Clean up location string
    }));
    
    return NextResponse.json({
      deals,
      total: deals.length,
      v2_count: deals.length
    });
  } catch (error) {
    console.error('Error fetching deals for contact:', error);
    return NextResponse.json(
      { error: 'Failed to fetch deals for contact' },
      { status: 500 }
    );
  }
} 