import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params;
    console.log('API route called with ID:', contactId, 'Type:', typeof contactId);

    const contactQuery = `
      SELECT 
        co.contact_id::integer AS contact_id,
        co.title as job_title,
        co.*,
        c.company_id,
        c.company_name,
        c.industry,
        c.company_address,
        c.company_city,
        c.company_state,
        c.company_website,
        c.company_country,
        ce.executive_summary,
        ce.career_timeline,
        ce.notable_activities,
        ce.education,
        ce.personal_tidbits,
        ce.conversation_hooks,
        ce.sources,
        ce.osint_profile as searched_profile,
        ce.input_data as searched_input_data,
        ce.tokens_used as searched_tokens_used,
        ce.created_at as searched_date,
        ce.company_type,
        ce.capital_positions,
        ce.confidence as classification_confidence,
        ce.reasoning as classification_reasoning,
        ce.status,
        ce.completed_at,
        CASE WHEN ce.contact_id IS NOT NULL THEN true ELSE false END as searched,
        CASE WHEN co.email_validated_date IS NOT NULL THEN true ELSE false END as email_generated,
        co.email_validated_date
      FROM contacts co
      LEFT JOIN companies c ON co.company_id = c.company_id
      LEFT JOIN contact_enrichment ce ON co.contact_id = ce.contact_id
      WHERE co.contact_id = $1
    `;

    try {
      console.log('Executing query with contactId:', contactId);
      const contactResult = await pool.query(contactQuery, [contactId]);
      
      if (contactResult.rows.length === 0) {
        console.log('No contact found with ID:', contactId);
        return NextResponse.json(
          { error: 'Contact not found' },
          { status: 404 }
        );
      }

      // Parse potential JSON columns from contact_enrichment
      const raw = contactResult.rows[0];
      const jsonFields = [
        'career_timeline',
        'notable_activities',
        'education',
        'personal_tidbits',
        'conversation_hooks',
        'sources',
        'capital_positions',
        'searched_input_data'
      ] as const;

      jsonFields.forEach((field) => {
        const value = (raw as any)[field];
        if (typeof value === 'string') {
          try {
            (raw as any)[field] = JSON.parse(value);
          } catch (parseError) {
            console.error(`Error parsing JSON for field ${field}:`, parseError);
            // keep as-is if not valid JSON
          }
        }
      });

      // Ensure investment_criteria exists and has the correct format
      if (!raw.investment_criteria) {
        raw.investment_criteria = {
          asset_types: [],
          deal_size: { min: 0, max: 0 },
          markets: []
        };
      } else if (typeof raw.investment_criteria === 'string') {
        try {
          raw.investment_criteria = JSON.parse(raw.investment_criteria);
        } catch (parseError) {
          console.error(`Error parsing investment_criteria:`, parseError);
          raw.investment_criteria = {
            asset_types: [],
            deal_size: { min: 0, max: 0 },
            markets: []
          };
        }
      }

      // Extract social media fields from extra_attrs
      const socialMediaFields = ['twitter', 'facebook', 'instagram', 'youtube'];
      socialMediaFields.forEach(field => {
        if (raw.extra_attrs && raw.extra_attrs[field]) {
          raw[field] = raw.extra_attrs[field];
        }
      });

      const contactData = {
        ...raw,
        recent_activities: [] // Default to empty array, but don't try to fetch from non-existent table
      };

      return NextResponse.json(contactData);
    } catch (dbError) {
      console.error('Database query error:', dbError);
      return NextResponse.json(
        { error: 'Database query failed', details: (dbError as Error).message }, 
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Route handler error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message }, 
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params;
    const body = await request.json();
    // console.log('PATCH request for contactId:', contactId, 'with body:', body);

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Update contact basic information if provided
      const updateFields: string[] = [];
      const values: any[] = [];
      let valueIndex = 1;
      
      // Define all updatable fields from the contacts table (excluding source and company_name)
      const updatableFields = [
        'first_name', 'last_name', 'full_name', 'title', 'headline', 'seniority',
        'email', 'additional_email', 'personal_email', 'email_status', 'linkedin_url', 
        'phone_number', 'phone_number_secondary', 'twitter', 'facebook', 'instagram', 'youtube',
        'education_college', 'education_college_year_graduated', 'education_high_school', 'education_high_school_year_graduated',
        'age', 'honorable_achievements', 'hobbies', 'executive_summary', 'career_timeline',
        'contact_address', 'contact_city', 'contact_state', 'contact_country', 'contact_zip_code',
        'contact_type', 'relationship_owner', 'role_in_decision_making', 'last_contact_date',
        'source_of_introduction', 'accredited_investor_status', 'kyc_status', 'notes',
        'job_tier', 'company_id'
      ];
      
      // Handle social media fields (these need to be stored in extra_attrs JSONB field)
      const socialMediaFields = ['twitter', 'facebook', 'instagram', 'youtube'];
      const hasSocialMediaFields = socialMediaFields.some(field => body[field] !== undefined);
      
      if (hasSocialMediaFields) {
        // Get current extra_attrs
        const currentAttrsResult = await client.query('SELECT extra_attrs FROM contacts WHERE contact_id = $1', [contactId]);
        const currentAttrs = currentAttrsResult.rows[0]?.extra_attrs || {};
        
        // Update with new social media fields
        socialMediaFields.forEach(field => {
          if (body[field] !== undefined) {
            currentAttrs[field] = body[field];
          }
        });
        
        updateFields.push(`extra_attrs = $${valueIndex}`);
        values.push(JSON.stringify(currentAttrs));
        valueIndex++;
      }
      
      // Helper function to convert empty strings to null for timestamp fields
      const sanitizeTimestamp = (value: any) => {
        return value === '' || value === null || value === undefined ? null : value;
      };

      updatableFields.forEach(field => {
        if (body[field] !== undefined) {
          updateFields.push(`${field} = $${valueIndex}`);
          // Handle array fields that need JSON stringification
          if (['honorable_achievements', 'hobbies', 'career_timeline'].includes(field)) {
            values.push(JSON.stringify(body[field] || []));
          } else if (field === 'last_contact_date') {
            // Handle timestamp field to prevent empty string errors
            values.push(sanitizeTimestamp(body[field]));
          } else {
            values.push(body[field]);
          }
          valueIndex++;
        }
      });
      
      if (updateFields.length > 0) {
        values.push(contactId);
        const updateQuery = `
          UPDATE contacts 
          SET ${updateFields.join(', ')}, updated_at = NOW()
          WHERE contact_id = $${valueIndex}
        `;
        
        await client.query(updateQuery, values);
      }
      
      // Update enrichment data if provided
      const enrichmentFields = ['conversation_hooks', 'executive_summary', 'career_timeline', 'education', 'notable_activities', 'personal_tidbits', 'company_type'];
      const hasEnrichmentFields = enrichmentFields.some(field => body[field] !== undefined);
      
      if (hasEnrichmentFields) {
        // Check if contact_enrichment record exists
        const checkQuery = `
          SELECT contact_id FROM contact_enrichment 
          WHERE contact_id = $1
        `;
        
        const checkResult = await client.query(checkQuery, [contactId]);
        
        const enrichmentUpdateFields: string[] = [];
        const enrichmentValues: any[] = [];
        let enrichmentValueIndex = 1;
        
        enrichmentFields.forEach(field => {
          if (body[field] !== undefined) {
            enrichmentUpdateFields.push(`${field} = $${enrichmentValueIndex}`);
            // JSON fields need to be stringified
            if (['conversation_hooks', 'career_timeline', 'education', 'notable_activities', 'personal_tidbits'].includes(field)) {
              enrichmentValues.push(JSON.stringify(body[field]));
            } else {
              enrichmentValues.push(body[field]);
            }
            enrichmentValueIndex++;
          }
        });
        
        if (enrichmentUpdateFields.length > 0) {
          if (checkResult.rows.length === 0) {
            // Insert new record if it doesn't exist
            const insertFields = enrichmentFields.filter(field => body[field] !== undefined);
            const insertPlaceholders = insertFields.map((_, index) => `$${index + 2}`).join(', ');
            const insertValues = [contactId];
            
            insertFields.forEach(field => {
              if (['conversation_hooks', 'career_timeline', 'education', 'notable_activities', 'personal_tidbits'].includes(field)) {
                insertValues.push(JSON.stringify(body[field]));
              } else {
                insertValues.push(body[field]);
              }
            });
            
            await client.query(
              `INSERT INTO contact_enrichment (contact_id, ${insertFields.join(', ')}, created_at, updated_at)
               VALUES ($1, ${insertPlaceholders}, NOW(), NOW())`,
              insertValues
            );
          } else {
            // Update existing record
            enrichmentValues.push(contactId);
            await client.query(
              `UPDATE contact_enrichment
               SET ${enrichmentUpdateFields.join(', ')}, updated_at = NOW()
               WHERE contact_id = $${enrichmentValueIndex}`,
              enrichmentValues
            );
          }
        }
      }
      
      await client.query('COMMIT');
      
      // Fetch and return the updated contact data
      const updatedContactQuery = `
        SELECT 
          co.contact_id::integer AS contact_id,
          co.title as job_title,
          co.*,
          c.company_id,
          c.company_name,
          c.industry,
          c.company_address,
          c.company_city,
          c.company_state,
          c.company_website,
          c.company_country,
          ce.executive_summary,
          ce.career_timeline,
          ce.notable_activities,
          ce.education,
          ce.personal_tidbits,
          ce.conversation_hooks,
          ce.sources,
          ce.osint_profile as searched_profile,
          ce.input_data as searched_input_data,
          ce.tokens_used as searched_tokens_used,
          ce.created_at as searched_date,
          ce.company_type,
          ce.capital_positions,
          ce.confidence as classification_confidence,
          ce.reasoning as classification_reasoning,
          ce.status,
          ce.completed_at,
          CASE WHEN ce.contact_id IS NOT NULL THEN true ELSE false END as searched,
          CASE WHEN co.email_validated_date IS NOT NULL THEN true ELSE false END as email_generated,
          co.email_validated_date
        FROM contacts co
        LEFT JOIN companies c ON co.company_id = c.company_id
        LEFT JOIN contact_enrichment ce ON co.contact_id = ce.contact_id
        WHERE co.contact_id = $1
      `;

      const updatedContactResult = await client.query(updatedContactQuery, [contactId]);
      
      if (updatedContactResult.rows.length === 0) {
        return NextResponse.json(
          { error: 'Contact not found after update' },
          { status: 404 }
        );
      }

      const updatedContact = updatedContactResult.rows[0];

      // Parse potential JSON columns from contact_enrichment
      const jsonFields = [
        'career_timeline',
        'notable_activities',
        'education',
        'personal_tidbits',
        'conversation_hooks',
        'sources',
        'capital_positions',
        'searched_input_data'
      ] as const;

      jsonFields.forEach((field) => {
        const value = (updatedContact as any)[field];
        if (typeof value === 'string') {
          try {
            (updatedContact as any)[field] = JSON.parse(value);
          } catch (parseError) {
            console.error(`Error parsing JSON for field ${field}:`, parseError);
          }
        }
      });

      // Extract social media fields from extra_attrs
      const mediaFields = ['twitter', 'facebook', 'instagram', 'youtube'];
      mediaFields.forEach(field => {
        if (updatedContact.extra_attrs && updatedContact.extra_attrs[field]) {
          updatedContact[field] = updatedContact.extra_attrs[field];
        }
      });

      // Trigger normalization for the updated contact
      try {
        const { DataNormalizationService } = await import('@/lib/services/dataNormalizationService')
        await DataNormalizationService.normalizeContact(parseInt(contactId))
        console.log(`Contact normalization completed for contact ID: ${contactId}`)
      } catch (normalizeError) {
        console.warn('Error triggering contact normalization:', normalizeError)
        // Don't fail the contact update if normalization fails
      }

      return NextResponse.json(updatedContact);
      
    } catch (dbError) {
      await client.query('ROLLBACK');
      console.error('Database update error:', dbError);
      return NextResponse.json(
        { error: 'Failed to update contact', details: (dbError as Error).message },
        { status: 500 }
      );
    } finally {
      client.release();
    }
    
  } catch (error: any) {
    console.error('Route handler error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
} 