import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { contactId, oldCompanyId } = await request.json()

    if (!contactId || !oldCompanyId) {
      return NextResponse.json(
        { error: 'Contact ID and old company ID are required' },
        { status: 400 }
      )
    }

    const client = await pool.connect()

    try {
      // First, find contact IC records that are linked to the old company
      // through investment_criteria_debt_id or investment_criteria_equity_id
      const findLinkedICQuery = `
        SELECT ic.investment_criteria_id, ic.investment_criteria_debt_id, ic.investment_criteria_equity_id
        FROM investment_criteria_central ic
        WHERE ic.entity_id = $1 
        AND ic.entity_type = 'contact'
        AND (
          ic.investment_criteria_debt_id IN (
            SELECT ic2.investment_criteria_debt_id
            FROM investment_criteria_central ic2 
            WHERE ic2.entity_id = $2 AND ic2.entity_type = 'company'
          )
          OR 
          ic.investment_criteria_equity_id IN (
            SELECT ic2.investment_criteria_equity_id
            FROM investment_criteria_central ic2 
            WHERE ic2.entity_id = $2 AND ic2.entity_type = 'company'
          )
        )
      `

      const findResult = await client.query(findLinkedICQuery, [contactId, oldCompanyId])
      
      if (findResult.rows.length === 0) {
        console.log(`No linked contact IC records found for contact ${contactId} and company ${oldCompanyId}`)
        return NextResponse.json({ 
          message: 'No linked investment criteria found',
          removedCount: 0 
        })
      }

      console.log(`Found ${findResult.rows.length} linked contact IC records to remove`)

      // Remove the linked contact IC records
      const removeQuery = `
        DELETE FROM investment_criteria_central 
        WHERE investment_criteria_id = ANY($1)
      `

      const icIdsToRemove = findResult.rows.map(row => row.investment_criteria_id)
      const removeResult = await client.query(removeQuery, [icIdsToRemove])

      // Update contact processing status to reset investment criteria
      await client.query(`
        UPDATE contacts 
        SET 
          contact_investment_criteria_status = 'pending',
          contact_investment_criteria_date = NULL,
          contact_investment_criteria_error = NULL
        WHERE contact_id = $1
      `, [contactId])

      console.log(`Successfully removed ${removeResult.rowCount} contact IC records for contact ${contactId}`)

      return NextResponse.json({
        message: 'Contact investment criteria removed successfully',
        removedCount: removeResult.rowCount,
        removedIds: icIdsToRemove
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error removing contact investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to remove contact investment criteria' },
      { status: 500 }
    )
  }
}
