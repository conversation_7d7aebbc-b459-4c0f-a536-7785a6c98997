import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface WebPageData {
  id: string
  url: string
  parent_url: string | null
  crawl_depth: number
  relevance_rank: number
  rank_factors: any
  extracted_text: string
  last_scraped_at: string
  created_at: string
}

interface WebPagesResponse {
  success: boolean
  data?: {
    pages: WebPageData[]
    total_pages: number
    top_pages: WebPageData[]
    ranking_summary: {
      highest_score: number
      average_score: number
      total_pages: number
    }
  }
  error?: string
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const client = await pool.connect()
  
  try {
    const { id } = await params;
    const companyId = parseInt(id)
    
    if (isNaN(companyId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid company ID' },
        { status: 400 }
      )
    }

    // Get all web pages for the company with ranking data
    const sql = `
      SELECT 
        id,
        url,
        parent_url,
        crawl_depth,
        relevance_rank,
        rank_factors,
        extracted_text,
        last_scraped_at,
        created_at
      FROM company_web_pages
      WHERE company_id = $1
      ORDER BY relevance_rank ASC, created_at DESC
    `
    
    const result = await client.query(sql, [companyId])
    const pages: WebPageData[] = result.rows

    if (pages.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          pages: [],
          total_pages: 0,
          top_pages: [],
          ranking_summary: {
            highest_score: 0,
            average_score: 0,
            total_pages: 0
          }
        }
      })
    }

    // Calculate ranking summary
    const scores = pages
      .map(page => page.rank_factors?.total_score || 0)
      .filter(score => score > 0)
    
    const ranking_summary = {
      highest_score: Math.max(...scores),
      average_score: scores.length > 0 ? Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) * 100) / 100 : 0,
      total_pages: pages.length
    }

    // Get top 5 pages by relevance
    const top_pages = pages
      .filter(page => page.relevance_rank > 0)
      .slice(0, 5)

    return NextResponse.json({
      success: true,
      data: {
        pages,
        total_pages: pages.length,
        top_pages,
        ranking_summary
      }
    })

  } catch (error) {
    console.error('Error fetching web pages:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch web pages' },
      { status: 500 }
    )
  } finally {
    client.release()
  }
}