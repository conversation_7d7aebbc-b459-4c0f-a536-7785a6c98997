import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const parameters = await params
  const companyId = parameters.id
  
  try {
    // Get all contacts for the company with company name
    const query = `
      SELECT 
        co.contact_id,
        co.first_name,
        co.last_name,
        co.full_name,
        co.title,
        co.headline,
        co.seniority,
        co.email,
        co.personal_email,
        co.email_status,
        co.linkedin_url,
        co.contact_city,
        co.contact_state,
        co.contact_country,
        co.phone_number,
        co.email_generated,
        co.smartlead_lead_id,
        co.smartlead_status,
        co.last_email_sent_at,
        co.source,
        co.category,
        co.email_validated_date,
        co.created_at,
        co.updated_at,
        -- Processing status fields
        co.email_verification_status,
        co.email_verification_error,
        co.contact_enrichment_v2_status,
        co.contact_enrichment_v2_error,
        co.email_generation_status,
        co.email_generation_error,
        co.email_sending_status,
        co.email_sending_error,
        co.contact_investment_criteria_status,
        co.contact_investment_criteria_error,
        co.processing_error_count,
        co.conflict_status,
        -- Additional enriched fields (only include fields that exist)
        co.education_college,
        co.education_college_year_graduated,
        co.education_high_school,
        co.education_high_school_year_graduated,
        -- Company name for display
        c.company_name
      FROM contacts co
      LEFT JOIN companies c ON co.company_id = c.company_id
      WHERE co.company_id = $1
      ORDER BY 
        -- Prioritize contacts with more complete information
        CASE WHEN co.email IS NOT NULL THEN 0 ELSE 1 END,
        CASE WHEN co.linkedin_url IS NOT NULL THEN 0 ELSE 1 END,
        CASE WHEN co.title IS NOT NULL THEN 0 ELSE 1 END,
        co.created_at DESC
    `

    const result = await pool.query(query, [companyId])
    
    // Get contact processing stats
    const statsQuery = `
      SELECT 
        COUNT(*) as total_contacts,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as contacts_with_email,
        COUNT(CASE WHEN linkedin_url IS NOT NULL AND linkedin_url != '' THEN 1 END) as contacts_with_linkedin,
        COUNT(CASE WHEN email_verification_status = 'verified' THEN 1 END) as verified_emails,
        COUNT(CASE WHEN contact_enrichment_v2_status = 'completed' THEN 1 END) as enriched_contacts
      FROM contacts 
      WHERE company_id = $1
    `
    
    const statsResult = await pool.query(statsQuery, [companyId])
    
    return NextResponse.json({
      success: true,
      contacts: result.rows,
      stats: statsResult.rows[0] || {
        total_contacts: 0,
        contacts_with_email: 0,
        contacts_with_linkedin: 0,
        verified_emails: 0,
        enriched_contacts: 0
      }
    })
  } catch (error) {
    console.error('Database error:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      companyId
    })
    return NextResponse.json(
      { error: 'Failed to fetch company contacts', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
