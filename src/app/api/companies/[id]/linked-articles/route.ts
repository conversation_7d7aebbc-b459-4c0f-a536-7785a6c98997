import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const companyId = parseInt(id);
    
    if (isNaN(companyId)) {
      return NextResponse.json(
        { error: 'Invalid company ID' },
        { status: 400 }
      );
    }
    
    // Query to get articles linked to this company through articles_entities table
    const linkedArticlesQuery = `
      SELECT DISTINCT
        a.article_id,
        a.headline,
        a.summary,
        a.publication_date,
        a.publication_name,
        a.article_url as source_url,
        a.scraping_source_type as source_name,
        a.topic as property_type,
        a.created_at,
        a.updated_at,
        ae.entity_type,
        ae.entity_role,
        ae.entity_name,
        ae.confidence,
        ae.web_search_verified,
        -- Transaction data
        COALESCE(
          ARRAY_AGG(
            DISTINCT jsonb_build_object(
              'deal_type', at.deal_type,
              'deal_size', at.deal_size,
              'cap_rate', at.cap_rate,
              'price_per_sf', at.price_per_sf,
              'loan_type', at.loan_type,
              'equity_type', at.equity_type,
              'financing_type', at.financing_type,
              'capital_stack_notes', at.capital_stack_notes,
              'capital_position', at.capital_position,
              'property_types', at.property_types
            )
          ) FILTER (WHERE at.article_transaction_id IS NOT NULL),
          ARRAY[]::jsonb[]
        ) as transactions,
        -- Property data
        COALESCE(
          ARRAY_AGG(
            DISTINCT jsonb_build_object( 
              'property_name', ap.property_name,
              'address', ap.address,
              'city', ap.city,
              'state', ap.state,
              'zipcode', ap.zipcode,
              'region', ap.region,
              'country', ap.country,
              'square_footage', ap.square_footage,
              'unit_count', ap.unit_count,
              'construction_type', ap.construction_type,
              'project_timeline', ap.project_timeline,
              'job_creation', ap.job_creation,
              'subsidy_info', ap.subsidy_info
            )
          ) FILTER (WHERE ap.article_property_id IS NOT NULL),
          ARRAY[]::jsonb[]
        ) as properties
      FROM article a
      INNER JOIN articles_entities ae ON a.article_id = ae.article_id
      LEFT JOIN article_transactions at ON a.article_id = at.article_id
      LEFT JOIN article_properties ap ON a.article_id = ap.article_id
      WHERE ae.entity_type = 'company'
        AND ae.entity_id = $1
      GROUP BY a.article_id, a.headline, a.summary, a.publication_date, a.publication_name,
               a.article_url, a.scraping_source_type, a.topic, a.created_at, a.updated_at,
               ae.entity_type, ae.entity_role, ae.entity_name, ae.confidence, ae.web_search_verified
      ORDER BY a.publication_date DESC NULLS LAST, a.created_at DESC
      LIMIT 100
    `;

    const articles = await pool.query(linkedArticlesQuery, [companyId]);

    // Transform the data to match the expected format
    const transformedArticles = articles.rows.map((article: any) => {
      // Extract deal sizes from transactions
      const dealSizes = article.transactions
        .filter((t: any) => t.deal_size)
        .map((t: any) => t.deal_size);
      
      // Extract location data from properties
      const locations = article.properties || [];
      const locationCities = locations.map((p: any) => p.city).filter(Boolean);
      const locationStates = locations.map((p: any) => p.state).filter(Boolean);
      
      return {
        article_id: article.article_id,
        headline: article.headline,
        summary: article.summary,
        publication_date: article.publication_date,
        publication_name: article.publication_name,
        source_name: article.source_name,
        source_url: article.source_url,
        property_type: article.property_type,
        location_city: locationCities.length > 0 ? locationCities : null,
        location_state: locationStates.length > 0 ? locationStates : null,
        deal_sizes: dealSizes,
        created_at: article.created_at,
        updated_at: article.updated_at,
        // Entity-specific information
        entity_type: article.entity_type,
        entity_role: article.entity_role,
        entity_name: article.entity_name,
        confidence: article.confidence,
        web_search_verified: article.web_search_verified,
        // New detailed data
        transactions: article.transactions || [],
        properties: article.properties || []
      };
    });

    // Get company information for response
    const companyQuery = `
      SELECT company_id, company_name, industry, founded_year, company_website
      FROM companies 
      WHERE company_id = $1
    `;
    
    const companyResult = await pool.query(companyQuery, [companyId]);
    const company = companyResult.rows.length > 0 ? companyResult.rows[0] : null;

    return NextResponse.json({
      company: company ? {
        company_id: company.company_id,
        company_name: company.company_name,
        industry: company.industry,
        founded_year: company.founded_year,
        company_website: company.company_website
      } : { company_id: companyId },
      articles: transformedArticles,
      total_matches: transformedArticles.length,
      success: true
    });

  } catch (error) {
    console.error('Error fetching linked articles for company:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch linked articles',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
