import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const parameters = await params
  const id = parameters.id
  try {
    // Lightweight V2 Query - Only essential data for overview/header/edit
    const query = `
      SELECT 
        c.*,
        -- Ensure overview_summary is returned as proper JSON (not text)
        (
          CASE 
            WHEN c.overview_summary IS NULL THEN NULL
            ELSE (c.overview_summary::jsonb)
          END
        ) AS overview_summary,
        -- Just counts for badges/UI, not full data arrays
        (
          SELECT COUNT(icc.investment_criteria_id) > 0
          FROM investment_criteria_central icc
          WHERE icc.entity_type = 'company' AND icc.entity_id = c.company_id
        ) as has_investment_criteria,
        (
          SELECT COUNT(icc.investment_criteria_id)
          FROM investment_criteria_central icc
          WHERE icc.entity_type = 'company' AND icc.entity_id = c.company_id
        ) as investment_criteria_count,
        (
          SELECT COUNT(co.contact_id)
          FROM contacts co
          WHERE co.company_id = c.company_id
        ) as contact_count
      FROM companies c
      WHERE c.company_id = $1
    `

    const result = await pool.query(query, [id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Add error handling for the response
    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company details' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  // Validate that id is a valid integer
  const companyId = parseInt(id, 10);
  if (isNaN(companyId) || companyId <= 0) {
    return NextResponse.json(
      { error: 'Invalid company ID. Must be a positive integer.' },
      { status: 400 }
    );
  }

  try {
    const body = await request.json()
    
    // Start a transaction
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // Update all company information in the companies table
      const companyUpdateQuery = `
        UPDATE companies
        SET 
          company_name = $1,
          company_type = $2,
          industry = $3,
          business_model = $4,
          investment_focus = $5,
          investment_strategy_mission = $6,
          investment_strategy_approach = $7,
          company_website = $8,
          company_phone = $9,
          secondary_phone = $10,
          main_email = $11,
          secondary_email = $12,
          company_linkedin = $13,
          twitter = $14,
          facebook = $15,
          instagram = $16,
          youtube = $17,
          company_address = $18,
          company_city = $19,
          company_state = $20,
          company_zip = $21,
          company_country = $22,
          additional_address = $23,
          additional_city = $24,
          additional_state = $25,
          additional_zipcode = $26,
          additional_country = $27,
          fund_size = $28,
          aum = $29,
          number_of_properties = $30,
          number_of_offices = $31,
          office_locations = $32,
          founded_year = $33,
          number_of_employees = $34,
          partnerships = $35,
          balance_sheet_strength = $36,
          funding_sources = $37,
          recent_capital_raises = $38,
          typical_debt_to_equity_ratio = $39,
          development_fee_structure = $40,
          key_equity_partners = $41,
          key_debt_partners = $42,
          market_cycle_positioning = $43,
          urban_vs_suburban_preference = $44,
          sustainability_esg_focus = $45,
          technology_proptech_adoption = $46,
          adaptive_reuse_experience = $47,
          regulatory_zoning_expertise = $48,
          investment_vehicle_type = $49,
          active_fund_name_series = $50,
          fund_size_active_fund = $51,
          fundraising_status = $52,
          lender_type = $53,
          annual_loan_volume = $54,
          lending_origin = $55,
          portfolio_health = $56,
          board_of_directors = $57,
          key_executives = $58,
          founder_background = $59,
          company_history = $60,
          stock_ticker_symbol = $61,
          stock_exchange = $62,
          market_capitalization = $63,
          annual_revenue = $64,
          net_income = $65,
          ebitda = $66,
          profit_margin = $67,
          credit_rating = $68,
          quarterly_earnings_link = $69,
          products_services_description = $70,
          target_customer_profile = $71,
          major_competitors = $72,
          market_share_percentage = $73,
          unique_selling_proposition = $74,
          industry_awards_recognitions = $75,
          corporate_structure = $76,
          parent_company = $77,
          subsidiaries = $78,
          dry_powder = $79,
          annual_deployment_target = $80,
          transactions_completed_last_12m = $81,
          internal_relationship_manager = $82,
          last_contact_date = $83,
          pipeline_status = $84,
          role_in_previous_deal = $85,
          total_transaction_volume_ytd = $86,
          deal_count_ytd = $87,
          average_deal_size = $88,
          portfolio_size_sqft = $89,
          portfolio_asset_count = $90,
          recent_news_sentiment = $91,
          data_source = $92,
          last_updated_timestamp = $93,
          data_confidence_score = $94,
          updated_at = NOW()
        WHERE company_id = $95
        RETURNING *
      `
      
      // Helper function to format arrays for PostgreSQL
      const formatArrayForDB = (arr) => {
        if (!arr || !Array.isArray(arr) || arr.length === 0) {
          return null;
        }
        return arr;
      };

      const companyValues = [
        body.company_name,
        body.company_type || null,
        body.industry || null,
        body.business_model || null,
        formatArrayForDB(body.investment_focus),
        body.investment_strategy_mission || null,
        body.investment_strategy_approach || null,
        body.company_website || body.website || null,
        body.company_phone || body.main_phone || null,
        body.secondary_phone || null,
        body.main_email || null,
        body.secondary_email || null,
        body.company_linkedin || body.linkedin || null,
        body.twitter || null,
        body.facebook || null,
        body.instagram || null,
        body.youtube || null,
        body.company_address || body.headquarters_address || null,
        body.company_city || body.headquarters_city || null,
        body.company_state || body.headquarters_state || null,
        body.company_zip || body.headquarters_zipcode || null,
        body.company_country || body.headquarters_country || null,
        body.additional_address || null,
        body.additional_city || null,
        body.additional_state || null,
        body.additional_zipcode || null,
        body.additional_country || null,
        body.fund_size || null,
        body.aum || null,
        body.number_of_properties || null,
        body.number_of_offices || null,
        formatArrayForDB(body.office_locations),
        body.founded_year || null,
        body.number_of_employees || null,
        formatArrayForDB(body.partnerships),
        body.balance_sheet_strength || null,
        formatArrayForDB(body.funding_sources),
        body.recent_capital_raises || null,
        body.typical_debt_to_equity_ratio || null,
        body.development_fee_structure || null,
        formatArrayForDB(body.key_equity_partners),
        formatArrayForDB(body.key_debt_partners),
        body.market_cycle_positioning || null,
        body.urban_vs_suburban_preference || null,
        body.sustainability_esg_focus || false,
        body.technology_proptech_adoption || false,
        body.adaptive_reuse_experience || false,
        body.regulatory_zoning_expertise || false,
        body.investment_vehicle_type || null,
        body.active_fund_name_series || null,
        body.fund_size_active_fund || null,
        body.fundraising_status || null,
        body.lender_type || null,
        body.annual_loan_volume || null,
        body.lending_origin_balance_sheet_securitization || null,
        body.portfolio_health || null,
        formatArrayForDB(body.board_of_directors),
        formatArrayForDB(body.key_executives),
        body.founder_background || null,
        body.company_history || null,
        body.stock_ticker_symbol || null,
        body.stock_exchange || null,
        body.market_capitalization || null,
        body.annual_revenue || null,
        body.net_income || null,
        body.ebitda || null,
        body.profit_margin || null,
        body.credit_rating || null,
        body.quarterly_earnings_link || null,
        body.products_services_description || null,
        body.target_customer_profile || null,
        formatArrayForDB(body.major_competitors),
        body.market_share_percentage || null,
        body.unique_selling_proposition || null,
        formatArrayForDB(body.industry_awards_recognitions),
        body.corporate_structure || null,
        body.parent_company || null,
        formatArrayForDB(body.subsidiaries),
        body.dry_powder || null,
        body.annual_deployment_target || null,
        body.transactions_completed_last_12m || null,
        body.internal_relationship_manager || null,
        body.last_contact_date || null,
        body.pipeline_status || null,
        body.role_in_previous_deal || null,
        body.total_transaction_volume_ytd || null,
        body.deal_count_ytd || null,
        body.average_deal_size || null,
        body.portfolio_size_sqft || null,
        body.portfolio_asset_count || null,
        body.recent_news_sentiment || null,
        body.data_source || null,
        body.last_updated_timestamp || null,
        body.data_confidence_score || null,
        companyId
      ]
      
      const companyResult = await client.query(companyUpdateQuery, companyValues)
      
      if (companyResult.rows.length === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        );
      }
      
      // All data is now stored in the companies table, no need for separate extracted data
      
      // Commit the transaction
      await client.query('COMMIT')
      
      // Trigger normalization for the updated company
      try {
        const normalizeResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/duplicates/normalize`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'company',
            companyId: companyId
          })
        })
        
        if (normalizeResponse.ok) {
          console.log(`Company normalization triggered for company ID: ${companyId}`)
        } else {
          console.warn(`Failed to trigger normalization for company ID: ${companyId}`)
        }
      } catch (normalizeError) {
        console.warn('Error triggering company normalization:', normalizeError)
        // Don't fail the company update if normalization fails
      }
      
      // Return the updated company data
      return NextResponse.json(companyResult.rows[0])
    } catch (error) {
      // Rollback in case of error
      await client.query('ROLLBACK')
      throw error
    } finally {
      // Release the client back to the pool
      client.release()
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to update company', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}