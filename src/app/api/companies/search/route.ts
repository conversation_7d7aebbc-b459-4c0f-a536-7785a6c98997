import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    // Accept either 'q' or 'name' as the search parameter
    const query = searchParams.get('q') || searchParams.get('name') || ''
    // Accept 'domain' as a search parameter for domain-based search
    const domain = searchParams.get('domain') || ''
    
    if (query.length < 2 && domain.length < 2) {
      return NextResponse.json([])
    }

    // V2 Search using only companies table
    const searchQuery = `
      SELECT 
        c.company_id,
        c.company_name,
        c.company_website,
        c.industry,
        c.company_address,
        c.company_city,
        c.company_state,
        c.company_country,
        -- Count investment criteria from central table
        COALESCE(ic_count.count, 0) as investment_criteria_count,
        -- Include V2 company data for auto-filling
        jsonb_build_object(
          'companytype', c.company_type,
          'businessmodel', c.business_model,
          'fundsize', c.fund_size,
          'aum', c.aum,
          'headquarters', c.company_address,
          'foundedyear', c.founded_year,
          'numberofemployees', c.number_of_employees,
          'investmentfocus', c.investment_focus,
          'mission', c.investment_strategy_mission,
          'approach', c.investment_strategy_approach,
          'partnerships', c.partnerships,
          'key_executives', c.key_executives,
          'annual_revenue', c.annual_revenue,
          'market_capitalization', c.market_capitalization,
          'data_confidence_score', c.data_confidence_score,
          'office_locations', c.office_locations,
          'transactions_completed_last_12m', c.transactions_completed_last_12m
        ) as extracted_data
      FROM companies c
      LEFT JOIN (
        SELECT 
          entity_id::bigint as company_id,
          COUNT(*) as count
        FROM investment_criteria_central 
        WHERE entity_type = 'company'
        GROUP BY entity_id
      ) ic_count ON c.company_id::bigint = ic_count.company_id
      WHERE 
        (LOWER(c.company_name) LIKE LOWER($1)
        OR LOWER(c.company_name) LIKE LOWER($3)
        OR LOWER(c.company_name) LIKE LOWER($4)
        OR LOWER(c.company_name) LIKE LOWER($5))
        ${domain.length >= 2 ? `
        OR (LOWER(c.company_website) LIKE LOWER($6)
        OR LOWER(c.company_website) LIKE LOWER($7)
        OR LOWER(c.company_website) LIKE LOWER($8))` : ''}
      ORDER BY 
        -- Exact matches first
        CASE WHEN LOWER(c.company_name) = LOWER($2) THEN 0 ELSE 1 END,
        -- Domain matches for domain search
        ${domain.length >= 2 ? `CASE WHEN LOWER(c.company_website) = LOWER($9) THEN 0 ELSE 1 END,` : ''}
        -- Prioritize companies with V2 data (overview_v2_status completed)
        CASE WHEN c.overview_v2_status = 'completed' THEN 0 ELSE 1 END,
        -- Then prioritize companies with investment criteria
        CASE WHEN ic_count.count > 0 THEN 0 ELSE 1 END,
        -- Then by how closely the search term matches at start
        CASE WHEN LOWER(c.company_name) LIKE LOWER($3) THEN 0 
             WHEN LOWER(c.company_name) LIKE LOWER($4) THEN 1
             WHEN LOWER(c.company_name) LIKE LOWER($5) THEN 2
             ELSE 3 END,
        -- Then by how early the search term appears
        POSITION(LOWER($2) IN LOWER(c.company_name)),
        c.company_name
      LIMIT 20
    `

    // Create different pattern matches for better search quality
    const exactTerm = query
    const startsWith = `${query}%`
    const wordsStartWith = `% ${query}%`
    const anywhere = `%${query}%`
    
    // Create domain pattern matches
    const domainExact = domain
    const domainStartsWith = `${domain}%`
    const domainAnywhere = `%${domain}%`

    const params = [
      anywhere,      // $1: match anywhere in name
      exactTerm,     // $2: exact match
      startsWith,    // $3: starts with query
      wordsStartWith,// $4: starts a word in the middle
      anywhere       // $5: anywhere pattern again for the sort
    ]

    // Add domain search parameters if domain is provided
    if (domain.length >= 2) {
      params.push(
        domainAnywhere,   // $6: domain match anywhere
        domainStartsWith, // $7: domain starts with
        domainAnywhere,   // $8: domain anywhere again
        domainExact       // $9: exact domain match
      )
    }

    const result = await pool.query(searchQuery, params)
    
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Error searching companies:', error)
    return NextResponse.json(
      { error: 'Failed to search companies' },
      { status: 500 }
    )
  }
} 