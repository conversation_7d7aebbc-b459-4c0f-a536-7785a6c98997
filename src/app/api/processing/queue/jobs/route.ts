import { NextRequest, NextResponse } from 'next/server'
import { ProcessorQueueManager } from '@/lib/queue/ProcessorQueueManager'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const queueName = searchParams.get('queueName')
    const limit = parseInt(searchParams.get('limit') || '50')
    const state = searchParams.get('state') as 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | undefined

    const queueManager = ProcessorQueueManager.getInstance()
    const jobs = await queueManager.getJobs(queueName, { limit, state })
    
    return NextResponse.json({
      success: true,
      jobs
    })
  } catch (error: any) {
    console.error('Error fetching jobs:', error)
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    )
  }
}

