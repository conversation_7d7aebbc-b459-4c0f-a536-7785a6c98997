import { NextRequest, NextResponse } from 'next/server'
import { ProcessorQueueManager } from '@/lib/queue/ProcessorQueueManager'

export async function POST(request: NextRequest) {
  try {
    const { queueName } = await request.json()

    if (!queueName) {
      return NextResponse.json(
        { success: false, message: 'Queue name is required' },
        { status: 400 }
      )
    }

    const queueManager = ProcessorQueueManager.getInstance()
    await queueManager.pauseQueue(queueName)
    
    return NextResponse.json({
      success: true,
      message: `Queue ${queueName} paused successfully`
    })
  } catch (error: any) {
    console.error('Error pausing queue:', error)
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    )
  }
}

