import { NextRequest, NextResponse } from 'next/server'
import { ProcessorQueueManager } from '@/lib/queue/ProcessorQueueManager'
import { ProcessingStage } from '@/types/processing'

export async function POST(request: NextRequest) {
  try {
    const { processorType, options = {} } = await request.json()

    if (!processorType) {
      return NextResponse.json(
        { success: false, message: 'Processor type is required' },
        { status: 400 }
      )
    }

    const queueManager = ProcessorQueueManager.getInstance()
    const jobId = await queueManager.addProcessorJob(processorType as ProcessingStage, options)
    
    return NextResponse.json({
      success: true,
      jobId: jobId,
      message: `Job ${processorType} queued successfully`
    })
  } catch (error: any) {
    console.error('Error triggering processor:', error)
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    )
  }
}
