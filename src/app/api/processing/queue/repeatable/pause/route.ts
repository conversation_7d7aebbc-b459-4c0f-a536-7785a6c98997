import { NextRequest, NextResponse } from 'next/server'
import { ProcessorQueueManager } from '@/lib/queue/ProcessorQueueManager'
import { ProcessingStage } from '@/types/processing'

export async function POST(request: NextRequest) {
  try {
    const { processorType, pauseAll = false } = await request.json()

    const queueManager = ProcessorQueueManager.getInstance()
    
    if (pauseAll) {
      const results = await queueManager.pauseAllRepeatableJobs()
      return NextResponse.json({
        success: true,
        message: 'All repeatable jobs paused',
        results
      })
    } else if (processorType) {
      const success = await queueManager.pauseRepeatableJob(processorType as ProcessingStage)
      return NextResponse.json({
        success: true,
        message: `Repeatable job for ${processorType} paused`,
        paused: success
      })
    } else {
      return NextResponse.json(
        { success: false, message: 'Either processorType or pauseAll=true is required' },
        { status: 400 }
      )
    }
  } catch (error: any) {
    console.error('Error pausing repeatable job:', error)
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    )
  }
}

