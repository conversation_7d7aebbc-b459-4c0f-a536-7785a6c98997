import { NextRequest, NextResponse } from 'next/server'
import { ProcessorQueueManager } from '@/lib/queue/ProcessorQueueManager'
import { ProcessingStage } from '@/types/processing'

export async function POST(request: NextRequest) {
  try {
    const { processorType, resumeAll = false, cronPattern = '*/30 * * * *' } = await request.json()

    const queueManager = ProcessorQueueManager.getInstance()
    
    if (resumeAll) {
      const results = await queueManager.resumeAllRepeatableJobs()
      return NextResponse.json({
        success: true,
        message: 'All repeatable jobs resumed',
        results
      })
    } else if (processorType) {
      const success = await queueManager.resumeRepeatableJob(processorType as ProcessingStage, cronPattern)
      return NextResponse.json({
        success: true,
        message: `Repeatable job for ${processorType} resumed with pattern ${cronPattern}`,
        resumed: success
      })
    } else {
      return NextResponse.json(
        { success: false, message: 'Either processorType or resumeAll=true is required' },
        { status: 400 }
      )
    }
  } catch (error: any) {
    console.error('Error resuming repeatable job:', error)
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    )
  }
}

