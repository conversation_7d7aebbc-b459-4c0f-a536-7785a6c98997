import { NextRequest, NextResponse } from 'next/server'
import { ProcessorQueueManager } from '@/lib/queue/ProcessorQueueManager'

export async function GET(request: NextRequest) {
  try {
    const queueManager = ProcessorQueueManager.getInstance()
    const status = await queueManager.getRepeatableJobsStatus()
    
    return NextResponse.json({
      success: true,
      status
    })
  } catch (error: any) {
    console.error('Error getting repeatable jobs status:', error)
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    )
  }
}

