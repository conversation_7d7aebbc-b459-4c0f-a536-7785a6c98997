import { NextRequest, NextResponse } from 'next/server'
import { ProcessorQueueManager } from '@/lib/queue/ProcessorQueueManager'

export async function GET(request: NextRequest) {
  try {
    const queueManager = ProcessorQueueManager.getInstance()
    const stats = await queueManager.getQueueStats()
    
    return NextResponse.json({
      success: true,
      stats
    })
  } catch (error: any) {
    console.error('Error fetching queue stats:', error)
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    )
  }
}

