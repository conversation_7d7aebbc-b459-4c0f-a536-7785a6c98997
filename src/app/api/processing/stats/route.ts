import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the base URL for internal API calls
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `${request.nextUrl.protocol}//${request.nextUrl.host}`
    
    try {
      // Fetch stats from individual unified APIs in parallel (no filters - global stats only)
      const [contactsResponse, companiesResponse, articlesResponse] = await Promise.all([
        fetch(`${baseUrl}/api/contacts/unified-filters-v2?stats=only`, {
          signal: AbortSignal.timeout(20000)
        }),
        fetch(`${baseUrl}/api/companies/unified-filters-v2?stats=only`, {
          signal: AbortSignal.timeout(20000)
        }),
        fetch(`${baseUrl}/api/articles/stats`, {
          signal: AbortSignal.timeout(20000)
        })
      ])

      if (!contactsResponse.ok || !companiesResponse.ok || !articlesResponse.ok) {
        throw new Error(`HTTP ${contactsResponse.status} or ${companiesResponse.status} or ${articlesResponse.status}`)
      }

      const [contactsData, companiesData, articlesData] = await Promise.all([
        contactsResponse.json(),
        companiesResponse.json(),
        articlesResponse.json()
      ])

      if (contactsData.error || companiesData.error || articlesData.error) {
        throw new Error(contactsData.error || companiesData.error || articlesData.error)
      }

      // Transform article data to match expected format
      const articleStats = {
        article_html_fetch: articlesData.article_html_fetch || {
          total: 0,
          pending: 0,
          running: 0,
          completed: 0,
          failed: 0,
          error: 0,
          success_rate: 0
        },
        article_link_fetch: articlesData.article_link_fetch || {
          total: 0,
          pending: 0,
          running: 0,
          completed: 0,
          failed: 0,
          error: 0,
          success_rate: 0
        },
        article_enrichment: articlesData.article_enrichment || {
          total: 0,
          pending: 0,
          running: 0,
          completed: 0,
          failed: 0,
          error: 0,
          success_rate: 0
        }
      }
    
    // Format response to match ProcessingStatsRaw interface exactly
    const response = {
      stats: {
          contacts: contactsData.stats || {
            total_contacts: 0,
            email_validation_pending: 0,
            email_validation_running: 0,
            email_validation_completed: 0,
            email_validation_failed: 0,
            email_validation_error: 0,
            contact_enrichment_v2_pending: 0,
            contact_enrichment_v2_running: 0,
            contact_enrichment_v2_completed: 0,
            contact_enrichment_v2_failed: 0,
            contact_enrichment_v2_error: 0,
            contact_investment_criteria_pending: 0,
            contact_investment_criteria_running: 0,
            contact_investment_criteria_completed: 0,
            contact_investment_criteria_failed: 0,
            contact_investment_criteria_error: 0,
            email_generation_pending: 0,
            email_generation_running: 0,
            email_generation_completed: 0,
            email_generation_failed: 0,
            email_generation_error: 0,
            contacts_with_job_tier: 0,
            contacts_needing_job_tier: 0
          },
          companies: companiesData.stats || {
            total_companies: 0,
            website_scraping_pending: 0,
            website_scraping_running: 0,
            website_scraping_completed: 0,
            website_scraping_failed: 0,
            website_scraping_error: 0,
            company_overview_v2_pending: 0,
            company_overview_v2_running: 0,
            company_overview_v2_completed: 0,
            company_overview_v2_failed: 0,
            company_overview_v2_error: 0,
            company_investment_criteria_pending: 0,
            company_investment_criteria_running: 0,
            company_investment_criteria_completed: 0,
            company_investment_criteria_failed: 0,
            company_investment_criteria_error: 0
          },
          articles: articleStats
        },
        processing: {
          total_contacts: contactsData.stats?.total_contacts || 0,
          total_companies: companiesData.stats?.total_companies || 0,
          total_articles: articlesData.summary?.total_articles || 0,
      },
      metadata: {
        timestamp: new Date().toISOString(),
          source: 'unified_apis_aggregation',
          optimized: true,
          note: 'Global stats only - no filters applied'
        }
      }
      
      return NextResponse.json(response)
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch stats from unified APIs:', error)
        throw error
      }
      throw error
    }
  } catch (error) {
    console.error('Error fetching processing stats:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch processing stats',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 
