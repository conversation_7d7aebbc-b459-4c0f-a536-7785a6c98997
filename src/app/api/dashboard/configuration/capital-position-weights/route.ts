import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// GET: Fetch enhanced capital position weights with comprehensive functionality
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const capitalPosition = searchParams.get('capital_position');
    const tableName = searchParams.get('table_name');
    const includeCommon = searchParams.get('include_common') === 'true';
    const getDefaults = searchParams.get('get_defaults') === 'true';

    console.log(`🔍 Capital Position Weights API: Fetching weights for capital position: ${capitalPosition}, table: ${tableName}`);

    if (getDefaults && capitalPosition) {
      // Return default weights for a specific position
      const defaultWeights = getDefaultCapitalPositionWeights(capitalPosition);
      return NextResponse.json({ 
        success: true, 
        data: defaultWeights,
        source: 'defaults',
        message: `Default weights for ${capitalPosition}`
      });
    }

    if (capitalPosition) {
      // Get weights for a specific capital position with table breakdown
      const query = `
        SELECT 
          cpw.capital_position,
          cpw.field_name,
          cpw.weight,
          cpw.description,
          cpw.table_name,
          cpw.field_category,
          cpw.is_active,
          CASE 
            WHEN cpw.table_name = 'dealsv2' THEN 'DEALS'
            WHEN cpw.table_name = 'investment_criteria_debt' THEN 'DEBT'
            WHEN cpw.table_name = 'investment_criteria_equity' THEN 'EQUITY'
            WHEN cpw.table_name = 'properties' THEN 'PROPERTY'
            ELSE 'OTHER'
          END as table_display_name,
          CASE 
            WHEN cpw.field_name = 'location' THEN 'split_location_field(location)'
            ELSE NULL
          END as special_handling
        FROM capital_position_field_weights cpw
        WHERE cpw.capital_position = $1 AND cpw.is_active = true
        ${tableName ? 'AND cpw.table_name = $2' : ''}
        ORDER BY cpw.weight DESC, cpw.table_name, cpw.field_name
      `;

      const params = tableName ? [capitalPosition, tableName] : [capitalPosition];
      const positionResult = await pool.query(query, params);
      
      if (positionResult.rows.length === 0) {
        return NextResponse.json({ 
          success: false, 
          message: `No weights found for ${capitalPosition}${tableName ? ` in table ${tableName}` : ''}` 
        }, { status: 404 });
      }

      // Get COMMON weights if requested or if this isn't COMMON
      let commonWeights: any[] = [];
      if (includeCommon && capitalPosition !== 'COMMON') {
        const commonQuery = `
          SELECT 
            field_name,
            weight,
            description,
            table_name,
            field_category,
            'COMMON' as weight_type,
            'COMMON' as source
          FROM capital_position_field_weights
          WHERE capital_position = 'COMMON' AND is_active = true
          ORDER BY weight DESC
        `;
        const commonResult = await pool.query(commonQuery);
        commonWeights = commonResult.rows;
      }

      // Calculate totals by table
      const tableTotals = positionResult.rows.reduce((acc: any, row: any) => {
        if (!acc[row.table_name]) {
          acc[row.table_name] = { total: 0, fields: [] };
        }
        acc[row.table_name].total += parseFloat(row.weight);
        acc[row.table_name].fields.push(row);
        return acc;
      }, {});

      // Calculate overall totals
      const positionTotal = positionResult.rows.reduce((sum: number, row: any) => sum + parseFloat(row.weight), 0);
      const commonTotal = commonWeights.reduce((sum: number, row: any) => sum + parseFloat(row.weight), 0);
      const finalTotal = positionTotal + commonTotal;

      // Determine weight source and status
      let weightSource = 'database';
      let isUsingDefaults = false;
      let hasCustomOverrides = false;

      if (capitalPosition === 'COMMON') {
        weightSource = 'COMMON base';
      } else if (positionResult.rows.length > 0) {
        weightSource = 'COMMON + position-specific';
        hasCustomOverrides = true;
      } else {
        weightSource = 'defaults';
        isUsingDefaults = true;
      }

      return NextResponse.json({
        success: true,
        data: {
          position: capitalPosition,
          weights: positionResult.rows,
          commonWeights: commonWeights,
          tableBreakdown: tableTotals,
          totals: {
            position: positionTotal,
            common: commonTotal,
            final: finalTotal,
            positionPercent: Math.round(positionTotal * 1000) / 10,
            commonPercent: Math.round(commonTotal * 1000) / 10,
            finalPercent: Math.round(finalTotal * 1000) / 10
          },
          status: {
            weightSource,
            isUsingDefaults,
            hasCustomOverrides,
            isCommon: capitalPosition === 'COMMON',
            hasCommonInheritance: commonWeights.length > 0,
            tableCoverage: Object.keys(tableTotals),
            debtEquitySplit: getDebtEquitySplit(positionResult.rows)
          }
        }
      });
    }

    // Get all capital positions with focus on COMMON
    const allPositionsQuery = `
      SELECT 
        capital_position,
        COUNT(*) as field_count,
        ROUND(SUM(weight) * 100, 1) as total_weight_percent,
        ROUND(SUM(CASE WHEN table_name = 'dealsv2' THEN weight ELSE 0 END) * 100, 1) as dealsv2_weight,
        ROUND(SUM(CASE WHEN table_name = 'investment_criteria_debt' THEN weight ELSE 0 END) * 100, 1) as debt_weight,
        ROUND(SUM(CASE WHEN table_name = 'investment_criteria_equity' THEN weight ELSE 0 END) * 100, 1) as equity_weight,
        ROUND(SUM(CASE WHEN table_name = 'properties' THEN weight ELSE 0 END) * 100, 1) as property_weight,
        CASE 
          WHEN capital_position = 'COMMON' THEN 'COMMON'
          WHEN capital_position != 'COMMON' THEN 'POSITION'
          ELSE 'CUSTOM'
        END as position_type,
        CASE 
          WHEN capital_position = 'COMMON' THEN 'Base weights for all positions (100% total)'
          WHEN capital_position != 'COMMON' THEN 'Position-specific weights (can be added later)'
          ELSE 'Custom configuration'
        END as description
      FROM capital_position_field_weights
      WHERE is_active = true
      GROUP BY capital_position
      ORDER BY 
        CASE WHEN capital_position = 'COMMON' THEN 1 ELSE 2 END,
        capital_position
    `;

    const allPositionsResult = await pool.query(allPositionsQuery);

    // Get table summary
    const tableSummaryQuery = `
      SELECT 
        table_name,
        COUNT(*) as field_count,
        ROUND(SUM(weight) * 100, 1) as total_weight_percentage,
        COUNT(DISTINCT capital_position) as positions_covered
      FROM capital_position_field_weights
      WHERE is_active = true
      GROUP BY table_name
      ORDER BY table_name
    `;

    const tableSummaryResult = await pool.query(tableSummaryQuery);

    return NextResponse.json({
      success: true,
      data: {
        positions: allPositionsResult.rows,
        tableSummary: tableSummaryResult.rows,
        totalPositions: allPositionsResult.rows.length,
        system: 'Enhanced V2 System with COMMON Weights Focus',
        features: [
          'COMMON weights system (100% total)',
          'Table-based weight breakdown (dealsv2, debt, equity, properties)',
          'Position-specific weights can be added later',
          'Proper debt/equity separation in matching',
          'Property weights for comprehensive coverage',
          'Location field auto-splitting (state, city, region)'
        ],
        note: 'Currently focused on COMMON weights. Individual capital positions can be added as needed.'
      }
    });

  } catch (error) {
    console.error('Error fetching capital position weights:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Internal server error' 
    }, { status: 500 });
  }
}

// Helper function to get debt vs equity split
function getDebtEquitySplit(weights: any[]) {
  const debtWeight = weights
    .filter(w => w.table_name === 'investment_criteria_debt')
    .reduce((sum, w) => sum + parseFloat(w.weight), 0);
  
  const equityWeight = weights
    .filter(w => w.table_name === 'investment_criteria_equity')
    .reduce((sum, w) => sum + parseFloat(w.weight), 0);
  
  return {
    debt: Math.round(debtWeight * 1000) / 10,
    equity: Math.round(equityWeight * 1000) / 10
  };
}

// Helper function to get default weights (placeholder)
function getDefaultCapitalPositionWeights(capitalPosition: string) {
  // This would return default weights if none exist in database
  return [];
}

// Helper function to get location breakdown for a property
async function getLocationBreakdown(propertyId: number) {
  try {
    const locationQuery = `
      SELECT 
        p.location,
        (split_location_field(p.location)).state,
        (split_location_field(p.location)).city,
        (split_location_field(p.location)).region
      FROM properties p
      WHERE p.id = $1
    `;
    const result = await pool.query(locationQuery, [propertyId]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error getting location breakdown:', error);
    return null;
  }
}

// POST: Create or update capital position field weights
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { capital_position, field_name, weight, description, table_name, field_category } = body;

    if (!capital_position || !field_name || weight === undefined) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing required fields: capital_position, field_name, weight' 
      }, { status: 400 });
    }

    // Validate weight is between 0 and 1
    if (weight < 0 || weight > 1) {
      return NextResponse.json({ 
        success: false, 
        message: 'Weight must be between 0 and 1' 
      }, { status: 400 });
    }

    // Check if record exists
    const checkQuery = `
      SELECT id FROM capital_position_field_weights 
      WHERE capital_position = $1 AND field_name = $2
    `;
    const checkResult = await pool.query(checkQuery, [capital_position, field_name]);

    if (checkResult.rows.length > 0) {
      // Update existing record
      const updateQuery = `
        UPDATE capital_position_field_weights 
        SET weight = $3, description = $4, table_name = $5, field_category = $6, updated_at = CURRENT_TIMESTAMP
        WHERE capital_position = $1 AND field_name = $2
        RETURNING *
      `;
      const updateResult = await pool.query(updateQuery, [
        capital_position, field_name, weight, description, table_name || 'dealsv2', field_category || 'general'
      ]);

      return NextResponse.json({
        success: true,
        message: 'Weight updated successfully',
        data: updateResult.rows[0]
      });
    } else {
      // Insert new record
      const insertQuery = `
        INSERT INTO capital_position_field_weights 
        (capital_position, field_name, weight, description, table_name, field_category, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
      `;
      const insertResult = await pool.query(insertQuery, [
        capital_position, field_name, weight, description, table_name || 'dealsv2', field_category || 'general'
      ]);

      return NextResponse.json({
        success: true,
        message: 'Weight created successfully',
        data: insertResult.rows[0]
      });
    }
  } catch (error) {
    console.error('Error creating/updating capital position field weight:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Internal server error' 
    }, { status: 500 });
  }
}

// PUT: Update multiple capital position field weights
export async function PUT(req: NextRequest) {
  try {
    const { updates } = await req.json();
    
    if (!Array.isArray(updates)) {
      return NextResponse.json({ 
        success: false,
        error: "Invalid input. 'updates' array is required" 
      }, { status: 400 });
    }

    const results: any[] = [];
    
    for (const update of updates) {
      const { capital_position, field_name, weight, description, table_name, field_category } = update;
      
      if (!capital_position || !field_name || typeof weight !== "number" || weight < 0 || weight > 1) {
        return NextResponse.json({ 
          success: false,
          error: "Invalid update. capital_position, field_name, and weight (0-1) are required" 
        }, { status: 400 });
      }

      const result = await pool.query(
        `INSERT INTO capital_position_field_weights 
         (capital_position, field_name, weight, description, table_name, field_category, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
         ON CONFLICT (capital_position, field_name) 
         DO UPDATE SET 
           weight = EXCLUDED.weight,
           description = EXCLUDED.description,
           table_name = EXCLUDED.table_name,
           field_category = EXCLUDED.field_category,
           updated_at = CURRENT_TIMESTAMP
         RETURNING capital_position, field_name, weight, description, table_name, field_category`,
        [capital_position, field_name, weight, description, table_name || 'dealsv2', field_category || 'general']
      );

      results.push(result.rows[0]);
    }

    return NextResponse.json({ 
      success: true, 
      data: results,
      message: 'Multiple weights updated successfully' 
    });
  } catch (error) {
    console.error('Error updating multiple weights:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Internal server error' 
    }, { status: 500 });
  }
}

// DELETE: Delete a capital position field weight
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const capitalPosition = searchParams.get('capital_position');
    const fieldName = searchParams.get('field_name');

    if (!capitalPosition || !fieldName) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing required parameters: capital_position, field_name' 
      }, { status: 400 });
    }

    const deleteQuery = `
      DELETE FROM capital_position_field_weights 
      WHERE capital_position = $1 AND field_name = $2
      RETURNING *
    `;
    const result = await pool.query(deleteQuery, [capitalPosition, fieldName]);

    if (result.rows.length === 0) {
      return NextResponse.json({ 
        success: false, 
        message: 'Weight not found' 
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Weight deleted successfully',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error deleting weight:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Internal server error' 
    }, { status: 500 });
  }
}


