import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function PUT(request: NextRequest) {
  const client = await pool.connect();
  
  try {
    const { updates } = await request.json();

    if (!Array.isArray(updates) || updates.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Updates array is required' },
        { status: 400 }
      );
    }

    // Validate each update
    for (const update of updates) {
      if (!update.capital_position || !update.field_name || typeof update.weight !== 'number') {
        return NextResponse.json(
          { success: false, message: 'Each update must have capital_position, field_name, and weight' },
          { status: 400 }
        );
      }

      if (update.weight < 0 || update.weight > 1) {
        return NextResponse.json(
          { success: false, message: `Weight for ${update.field_name} must be between 0 and 1` },
          { status: 400 }
        );
      }
    }

    // Begin transaction
    await client.query('BEGIN');

    try {
      // Process updates
      for (const update of updates) {
        // Check if record exists
        const existingCheck = await client.query(
          `SELECT id FROM capital_position_field_weights 
           WHERE capital_position = $1 AND field_name = $2 AND table_name = $3`,
          [update.capital_position, update.field_name, update.table_name]
        );

        if (existingCheck.rows.length === 0) {
          await client.query('ROLLBACK');
          return NextResponse.json(
            { 
              success: false, 
              message: `Weight record not found for ${update.field_name} in ${update.table_name}` 
            },
            { status: 404 }
          );
        }

        // Update the record
        await client.query(
          `UPDATE capital_position_field_weights 
           SET weight = $1, updated_at = NOW()
           WHERE capital_position = $2 AND field_name = $3 AND table_name = $4`,
          [update.weight, update.capital_position, update.field_name, update.table_name]
        );
      }

      // Commit transaction
      await client.query('COMMIT');

      return NextResponse.json({
        success: true,
        message: `Successfully updated ${updates.length} field weights`,
        data: { updatedCount: updates.length }
      });

    } catch (updateError) {
      await client.query('ROLLBACK');
      throw updateError;
    }

  } catch (error) {
    console.error('Error updating capital position weights:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  } finally {
    client.release();
  }
}
