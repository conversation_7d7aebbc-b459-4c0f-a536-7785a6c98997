import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import * as bcrypt from "bcryptjs";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// Helper function to check if user is admin
async function checkAdminPermission(email: string): Promise<{ isAdmin: boolean; userId?: number }> {
  try {
    const result = await pool.query(`
      SELECT 
        user_id, role,
        COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
        COALESCE(is_active, true) as is_active
      FROM users 
      WHERE email = $1 AND deleted_at IS NULL
    `, [email]);
    
    if (result.rows.length > 0) {
      const user = result.rows[0];
      const isActive = user.is_active;
      const isAdmin = user.is_admin || user.role === 'admin';
      
      if (isActive && isAdmin) {
        return { isAdmin: true, userId: user.user_id };
      }
    }
    
    return { isAdmin: false };
  } catch (error) {
    console.error('Error checking admin permission:', error);
    return { isAdmin: false };
  }
}

// Helper function to log user activity
async function logUserActivity(
  userId: number,
  action: string,
  description: string,
  performedBy?: number,
  oldValues?: any,
  newValues?: any,
  request?: NextRequest
) {
  try {
    const clientIP = request?.headers.get('x-forwarded-for') || 
                    request?.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request?.headers.get('user-agent') || 'unknown';

    await pool.query(`
      INSERT INTO user_activity_log (
        user_id, action, description, ip_address, user_agent, 
        performed_by, old_values, new_values
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      userId, action, description, clientIP, userAgent, 
      performedBy, JSON.stringify(oldValues), JSON.stringify(newValues)
    ]);
  } catch (error) {
    console.warn('Could not log user activity (table may not exist yet):', error);
    // Continue execution - logging failure shouldn't break the API
  }
}

// GET /api/admin/users/[userId] - Get specific user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isAdmin } = await checkAdminPermission(session.user.email);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const userQuery = `
      SELECT 
        user_id, 
        COALESCE(first_name, SPLIT_PART(display_name, ' ', 1)) as first_name,
        COALESCE(last_name, SPLIT_PART(display_name, ' ', -1)) as last_name,
        email, username, role, display_name,
        COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
        COALESCE(is_active, true) as is_active, 
        COALESCE(permissions, ARRAY[]::TEXT[]) as permissions, 
        phone, 
        COALESCE(last_login, NULL) as last_login,
        COALESCE(login_count, 0) as login_count,
        COALESCE(failed_login_attempts, 0) as failed_login_attempts,
        locked_until,
        COALESCE(password_changed_at, created_at) as password_changed_at,
        COALESCE(force_password_change, false) as force_password_change,
        COALESCE(two_factor_enabled, false) as two_factor_enabled,
        impersonated_by, impersonation_started_at,
        COALESCE(timezone, 'UTC') as timezone,
        COALESCE(language, 'en') as language, 
        COALESCE(theme, 'light') as theme,
        notes, tags,
        created_at, updated_at, created_by,
        COALESCE(display_name, CONCAT(first_name, ' ', last_name)) as full_name
      FROM users 
      WHERE user_id = $1 AND deleted_at IS NULL
    `;

    const result = await pool.query(userQuery, [userId]);

    if (result.rows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get recent activity for this user (handle gracefully if table doesn't exist)
    let activityResult = { rows: [] };
    try {
      const activityQuery = `
        SELECT 
          action, description, 
          COALESCE(ip_address, 'unknown') as ip_address,
          performed_by, 
          COALESCE(impersonation_active, false) as impersonation_active, 
          created_at,
          (SELECT COALESCE(display_name, CONCAT(first_name, ' ', last_name)) FROM users WHERE user_id = performed_by) as performed_by_name
        FROM user_activity_log 
        WHERE user_id = $1 
        ORDER BY created_at DESC 
        LIMIT 10
      `;
      activityResult = await pool.query(activityQuery, [userId]);
    } catch (activityError) {
      console.warn('Could not fetch user activity (table may not exist yet):', activityError);
    }

    return NextResponse.json({
      user: result.rows[0],
      recentActivity: activityResult.rows
    });

  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/users/[userId] - Update specific user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isAdmin, userId: adminUserId } = await checkAdminPermission(session.user.email);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Get current user data for audit log
    const currentUserResult = await pool.query(
      'SELECT * FROM users WHERE user_id = $1 AND deleted_at IS NULL',
      [userId]
    );

    if (currentUserResult.rows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const currentUser = currentUserResult.rows[0];
    const body = await request.json();

    const {
      first_name, last_name, email, username, role,
      is_admin, is_active, permissions, phone, profile_picture,
      timezone, language, theme, notes, tags, 
      force_password_change, reset_failed_attempts
    } = body;

    // Check if email is being changed and if it already exists
    if (email && email !== currentUser.email) {
      const existingUser = await pool.query(
        'SELECT user_id FROM users WHERE email = $1 AND user_id != $2 AND deleted_at IS NULL',
        [email, userId]
      );

      if (existingUser.rows.length > 0) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 409 }
        );
      }
    }

    // Check if username is being changed and if it already exists
    if (username && username !== currentUser.username) {
      const existingUsername = await pool.query(
        'SELECT user_id FROM users WHERE username = $1 AND user_id != $2 AND deleted_at IS NULL',
        [username, userId]
      );

      if (existingUsername.rows.length > 0) {
        return NextResponse.json(
          { error: 'Username already exists' },
          { status: 409 }
        );
      }
    }

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramCount = 0;

    const fieldsToUpdate = {
      first_name, last_name, email, username, role,
      is_admin, is_active, permissions, phone, profile_picture,
      timezone, language, theme, notes, tags, force_password_change
    };

    Object.entries(fieldsToUpdate).forEach(([field, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        paramCount++;
        updateFields.push(`${field} = $${paramCount}`);
        updateValues.push(value);
      }
    });

    // Reset failed login attempts if requested
    if (reset_failed_attempts) {
      paramCount++;
      updateFields.push(`failed_login_attempts = $${paramCount}`);
      updateValues.push(0);
      
      paramCount++;
      updateFields.push(`locked_until = $${paramCount}`);
      updateValues.push(null);
    }

    // Add updated_by and updated_at
    paramCount++;
    updateFields.push(`updated_by = $${paramCount}`);
    updateValues.push(adminUserId);

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      );
    }

    // Add user_id for WHERE clause
    paramCount++;
    updateValues.push(userId);

    const updateQuery = `
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE user_id = $${paramCount} AND deleted_at IS NULL
      RETURNING user_id, first_name, last_name, email, username, role, 
                is_admin, is_active, permissions, updated_at
    `;

    const result = await pool.query(updateQuery, updateValues);

    if (result.rows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Log activity
    await logUserActivity(
      parseInt(userId),
      'user_updated',
      `User profile updated by admin`,
      adminUserId,
      currentUser,
      fieldsToUpdate,
      request
    );

    return NextResponse.json({
      success: true,
      user: result.rows[0],
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[userId] - Delete (soft delete) specific user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isAdmin, userId: adminUserId } = await checkAdminPermission(session.user.email);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Check if user exists and get user info
    const userResult = await pool.query(
      'SELECT first_name, last_name, email, role FROM users WHERE user_id = $1 AND deleted_at IS NULL',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const userToDelete = userResult.rows[0];

    // Prevent admin from deleting themselves
    if (parseInt(userId) === adminUserId) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Soft delete the user
    const deleteQuery = `
      UPDATE users 
      SET deleted_at = CURRENT_TIMESTAMP, deleted_by = $1, is_active = false
      WHERE user_id = $2 AND deleted_at IS NULL
      RETURNING user_id, first_name, last_name, email
    `;

    const result = await pool.query(deleteQuery, [adminUserId, userId]);

    if (result.rows.length === 0) {
      return NextResponse.json({ error: 'User not found or already deleted' }, { status: 404 });
    }

    // Log activity
    await logUserActivity(
      parseInt(userId),
      'user_deleted',
      `User ${userToDelete.email} deleted by admin`,
      adminUserId,
      userToDelete,
      { deleted: true },
      request
    );

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
