import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import * as bcrypt from "bcryptjs";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// Helper function to check if user is admin
async function checkAdminPermission(email: string): Promise<{ isAdmin: boolean; userId?: number }> {
  try {
    const result = await pool.query(`
      SELECT 
        user_id, role,
        COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
        COALESCE(is_active, true) as is_active
      FROM users 
      WHERE email = $1 AND deleted_at IS NULL
    `, [email]);
    
    if (result.rows.length > 0) {
      const user = result.rows[0];
      const isActive = user.is_active;
      const isAdmin = user.is_admin || user.role === 'admin';
      
      if (isActive && isAdmin) {
        return { isAdmin: true, userId: user.user_id };
      }
    }
    
    return { isAdmin: false };
  } catch (error) {
    console.error('Error checking admin permission:', error);
    return { isAdmin: false };
  }
}

// Helper function to log user activity
async function logUserActivity(
  userId: number,
  action: string,
  description: string,
  performedBy?: number,
  request?: NextRequest
) {
  try {
    const clientIP = request?.headers.get('x-forwarded-for') || 
                    request?.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request?.headers.get('user-agent') || 'unknown';

    await pool.query(`
      INSERT INTO user_activity_log (
        user_id, action, description, ip_address, user_agent, performed_by
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [userId, action, description, clientIP, userAgent, performedBy]);
  } catch (error) {
    console.warn('Could not log user activity (table may not exist yet):', error);
    // Continue execution - logging failure shouldn't break the API
  }
}

// PUT /api/admin/users/[userId]/password - Change user password (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isAdmin, userId: adminUserId } = await checkAdminPermission(session.user.email);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { newPassword, forcePasswordChange = true, notifyUser = true } = body;

    // Validation
    if (!newPassword) {
      return NextResponse.json(
        { error: 'New password is required' },
        { status: 400 }
      );
    }

    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Check if target user exists
    const userResult = await pool.query(`
      SELECT 
        user_id, email, 
        COALESCE(first_name, SPLIT_PART(display_name, ' ', 1)) as first_name,
        COALESCE(last_name, SPLIT_PART(display_name, ' ', -1)) as last_name
      FROM users 
      WHERE user_id = $1 AND deleted_at IS NULL
    `, [userId]);

    if (userResult.rows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const targetUser = userResult.rows[0];

    // Hash new password
    const salt = await bcrypt.genSalt(12);
    const passwordHash = await bcrypt.hash(newPassword, salt);

    // Update password - handle missing columns gracefully
    let result;
    try {
      const updateQuery = `
        UPDATE users 
        SET 
          password_hash = $1,
          salt = $2,
          password_changed_at = CURRENT_TIMESTAMP,
          force_password_change = $3,
          updated_by = $4,
          failed_login_attempts = 0,
          locked_until = NULL
        WHERE user_id = $5 AND deleted_at IS NULL
        RETURNING user_id, email, password_changed_at
      `;
      
      result = await pool.query(updateQuery, [
        passwordHash, salt, forcePasswordChange, adminUserId, userId
      ]);
    } catch (updateError: any) {
      // If some columns don't exist, try with basic update
      if (updateError.message?.includes('column') && updateError.message?.includes('does not exist')) {
        console.warn('Some password-related columns do not exist, using basic update');
        const basicUpdateQuery = `
          UPDATE users 
          SET password_hash = $1
          WHERE user_id = $2 AND deleted_at IS NULL
          RETURNING user_id, email, created_at as password_changed_at
        `;
        
        result = await pool.query(basicUpdateQuery, [passwordHash, userId]);
      } else {
        throw updateError;
      }
    }

    if (result.rows.length === 0) {
      return NextResponse.json({ error: 'Failed to update password' }, { status: 500 });
    }

    // Log activity
    await logUserActivity(
      parseInt(userId),
      'password_reset_by_admin',
      `Password reset by admin${forcePasswordChange ? ' (force change required)' : ''}`,
      adminUserId,
      request
    );

    // TODO: Send notification email if notifyUser is true
    // This would require email service integration

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully',
      forcePasswordChange,
      passwordChangedAt: result.rows[0].password_changed_at
    });

  } catch (error) {
    console.error('Error changing user password:', error);
    return NextResponse.json(
      { error: 'Failed to change password', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
