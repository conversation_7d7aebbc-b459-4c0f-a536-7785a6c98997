import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import * as bcrypt from "bcryptjs";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// Types
interface User {
  user_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  username?: string;
  role: string;
  is_admin: boolean;
  is_active: boolean;
  permissions?: string[];
  phone?: string;
  profile_picture?: string;
  timezone?: string;
  language?: string;
  theme?: string;
  notes?: string;
  tags?: string[];
}

interface CreateUserRequest extends User {
  password: string;
  force_password_change?: boolean;
}

// Helper function to check if user is admin
async function checkAdminPermission(email: string): Promise<boolean> {
  try {
    const result = await pool.query(`
      SELECT 
        user_id, role,
        COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
        COALESCE(is_active, true) as is_active
      FROM users 
      WHERE email = $1 AND deleted_at IS NULL
    `, [email]);
    
    if (result.rows.length > 0) {
      const user = result.rows[0];
      const isActive = user.is_active;
      const isAdmin = user.is_admin || user.role === 'admin';
      return isActive && isAdmin;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking admin permission:', error);
    return false;
  }
}

// Helper function to log user activity
async function logUserActivity(
  userId: number,
  action: string,
  description: string,
  performedBy?: number,
  oldValues?: any,
  newValues?: any,
  request?: NextRequest
) {
  try {
    const clientIP = request?.headers.get('x-forwarded-for') || 
                    request?.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request?.headers.get('user-agent') || 'unknown';

    await pool.query(`
      INSERT INTO user_activity_log (
        user_id, action, description, ip_address, user_agent, 
        performed_by, old_values, new_values
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      userId, action, description, clientIP, userAgent, 
      performedBy, JSON.stringify(oldValues), JSON.stringify(newValues)
    ]);
  } catch (error) {
    console.warn('Could not log user activity (table may not exist yet):', error);
    // Continue execution - logging failure shouldn't break the API
  }
}

// GET /api/admin/users - List all users
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const isAdmin = await checkAdminPermission(session.user.email);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || '';
    const status = searchParams.get('status') || '';
    const offset = (page - 1) * limit;

    let whereConditions = ['deleted_at IS NULL'];
    let queryParams: any[] = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereConditions.push(`(
        LOWER(first_name) LIKE LOWER($${paramCount}) OR 
        LOWER(last_name) LIKE LOWER($${paramCount}) OR 
        LOWER(email) LIKE LOWER($${paramCount}) OR 
        LOWER(username) LIKE LOWER($${paramCount})
      )`);
      queryParams.push(`%${search}%`);
    }

    if (role) {
      paramCount++;
      whereConditions.push(`role = $${paramCount}`);
      queryParams.push(role);
    }

    if (status === 'active') {
      whereConditions.push('is_active = true');
    } else if (status === 'inactive') {
      whereConditions.push('is_active = false');
    }

    const whereClause = whereConditions.join(' AND ');

    // Get users with pagination - use only fields that exist
    const usersQuery = `
      SELECT 
        user_id, 
        COALESCE(first_name, SPLIT_PART(display_name, ' ', 1)) as first_name,
        COALESCE(last_name, SPLIT_PART(display_name, ' ', -1)) as last_name,
        email, username, role, display_name,
        COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
        COALESCE(is_active, true) as is_active, 
        COALESCE(permissions, ARRAY[]::TEXT[]) as permissions, 
        phone, 
        COALESCE(last_login, NULL) as last_login,
        COALESCE(login_count, 0) as login_count,
        COALESCE(failed_login_attempts, 0) as failed_login_attempts,
        COALESCE(password_changed_at, created_at) as password_changed_at,
        COALESCE(force_password_change, false) as force_password_change,
        impersonated_by, impersonation_started_at,
        COALESCE(timezone, 'UTC') as timezone,
        COALESCE(language, 'en') as language, 
        COALESCE(theme, 'light') as theme,
        notes, tags,
        created_at, updated_at, created_by,
        COALESCE(display_name, CONCAT(first_name, ' ', last_name)) as full_name
      FROM users 
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    queryParams.push(limit, offset);
    const usersResult = await pool.query(usersQuery, queryParams);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM users 
      WHERE ${whereClause}
    `;
    const countResult = await pool.query(countQuery, queryParams.slice(0, -2));

    const totalCount = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      users: usersResult.rows,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Create new user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const isAdmin = await checkAdminPermission(session.user.email);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body: CreateUserRequest = await request.json();
    const {
      first_name, last_name, email, username, password, role = 'user',
      is_admin = false, is_active = true, permissions = [],
      phone, profile_picture, timezone = 'UTC', language = 'en', 
      theme = 'light', notes, tags = [], force_password_change = true
    } = body;

    // Validation
    if (!first_name || !last_name || !email || !password) {
      return NextResponse.json(
        { error: 'First name, last name, email, and password are required' },
        { status: 400 }
      );
    }

    // Generate username if not provided (required by current table schema)
    const finalUsername = username || `${first_name.toLowerCase()}.${last_name.toLowerCase()}`;

    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUser = await pool.query(
      'SELECT user_id FROM users WHERE email = $1 AND deleted_at IS NULL',
      [email]
    );

    if (existingUser.rows.length > 0) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Check if username already exists
    const existingUsername = await pool.query(
      'SELECT user_id FROM users WHERE username = $1',
      [finalUsername]
    );

    if (existingUsername.rows.length > 0) {
      return NextResponse.json(
        { error: 'Username already exists' },
        { status: 409 }
      );
    }

    // Get admin user ID
    const adminUser = await pool.query(
      'SELECT user_id FROM users WHERE email = $1',
      [session.user.email]
    );
    const adminUserId = adminUser.rows[0]?.user_id;

    // Hash password
    const salt = await bcrypt.genSalt(12);
    const passwordHash = await bcrypt.hash(password, salt);

    // Create user - include required fields
    const insertQuery = `
      INSERT INTO users (
        username, email, password_hash, role, display_name,
        first_name, last_name, salt, is_admin, is_active
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
      ) RETURNING user_id, username, email, role, display_name, created_at
    `;

    const displayName = `${first_name} ${last_name}`;
    const result = await pool.query(insertQuery, [
      finalUsername, email, passwordHash, role, displayName,
      first_name, last_name, salt, is_admin, is_active
    ]);

    // After basic user creation, update with additional fields if columns exist
    const userID = result.rows[0].user_id;
    
    // Try to update with additional fields if columns exist
    try {
      await pool.query(`
        UPDATE users SET 
          permissions = $1,
          phone = $2,
          timezone = $3,
          language = $4,
          theme = $5,
          notes = $6,
          tags = $7,
          force_password_change = $8,
          profile_picture = $9,
          created_by = $10
        WHERE user_id = $11
      `, [
        permissions, phone, timezone, language, theme, notes, tags, 
        force_password_change, profile_picture, adminUserId, userID
      ]);
    } catch (updateError) {
      // Columns might not exist yet - that's okay for backward compatibility
      console.warn('Could not update extended user fields (migration may be needed):', updateError);
    }

    const newUser = {
      ...result.rows[0],
      first_name: first_name,
      last_name: last_name,
      full_name: displayName,
      is_admin: is_admin,
      is_active: is_active
    };

    // Log activity (try with new table structure, fallback gracefully)
    try {
      await logUserActivity(
        newUser.user_id,
        'user_created',
        `User ${newUser.email} created by admin`,
        adminUserId,
        null,
        { email, role, is_admin },
        request
      );
    } catch (logError) {
      console.warn('Could not log user activity (user_activity_log table may not exist yet):', logError);
    }

    return NextResponse.json({
      success: true,
      user: newUser,
      message: 'User created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
