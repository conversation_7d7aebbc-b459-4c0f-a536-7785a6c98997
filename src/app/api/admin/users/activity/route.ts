import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { pool } from '@/lib/db';

// Helper function to check admin permission
async function checkAdminPermission(email: string): Promise<{ isAdmin: boolean; userId?: number }> {
  try {
    const result = await pool.query(`
      SELECT 
        user_id, role,
        COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
        COALESCE(is_active, true) as is_active
      FROM users 
      WHERE email = $1 AND deleted_at IS NULL
    `, [email]);
    
    if (result.rows.length > 0) {
      const user = result.rows[0];
      const isActive = user.is_active;
      const isAdmin = user.is_admin || user.role === 'admin';
      
      if (isActive && isAdmin) {
        return { 
          isAdmin: true, 
          userId: user.user_id 
        };
      }
    }
    return { isAdmin: false };
  } catch (error) {
    console.error('Error checking admin permission:', error);
    return { isAdmin: false };
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isAdmin } = await checkAdminPermission(session.user.email);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const action = searchParams.get('action') || '';
    const userId = searchParams.get('userId') || '';
    const days = parseInt(searchParams.get('days') || '7');

    const offset = (page - 1) * limit;

    // Build WHERE clause
    let whereConditions = ['1=1'];
    let paramCount = 0;
    const queryParams: any[] = [];

    // Date filter
    whereConditions.push(`ual.created_at >= NOW() - INTERVAL '${days} days'`);

    // Action filter
    if (action) {
      paramCount++;
      whereConditions.push(`ual.action ILIKE $${paramCount}`);
      queryParams.push(`%${action}%`);
    }

    // User filter
    if (userId) {
      paramCount++;
      whereConditions.push(`ual.user_id = $${paramCount}`);
      queryParams.push(userId);
    }

    const whereClause = whereConditions.join(' AND ');

    // Try to fetch from user_activity_log table first
    let activities: any[] = [];
    let totalCount = 0;

    try {
      // Count query
      const countQuery = `
        SELECT COUNT(*) as total
        FROM user_activity_log ual
        WHERE ${whereClause}
      `;
      const countResult = await pool.query(countQuery, queryParams);
      totalCount = parseInt(countResult.rows[0]?.total || '0');

      // Data query
      const activitiesQuery = `
        SELECT 
          ual.log_id as activity_id,
          ual.user_id,
          COALESCE(u.first_name, SPLIT_PART(u.display_name, ' ', 1)) || ' ' || 
          COALESCE(u.last_name, SPLIT_PART(u.display_name, ' ', -1)) as user_name,
          ual.action,
          ual.description,
          ual.ip_address,
          ual.user_agent,
          ual.created_at as timestamp
        FROM user_activity_log ual
        LEFT JOIN users u ON ual.user_id = u.user_id
        WHERE ${whereClause}
        ORDER BY ual.created_at DESC
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
      `;
      
      queryParams.push(limit, offset);
      const activitiesResult = await pool.query(activitiesQuery, queryParams);
      activities = activitiesResult.rows;

    } catch (tableError) {
      console.warn('user_activity_log table may not exist, returning empty results:', tableError);
      // Return empty results if table doesn't exist
      activities = [];
      totalCount = 0;
    }

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      activities,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages
      }
    });

  } catch (error) {
    console.error('Error fetching user activities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user activities' },
      { status: 500 }
    );
  }
}
