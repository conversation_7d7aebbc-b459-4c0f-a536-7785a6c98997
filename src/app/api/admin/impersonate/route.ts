import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { SignJWT } from "jose";

// Helper function to check if user is admin
async function checkAdminPermission(email: string): Promise<{ isAdmin: boolean; userId?: number }> {
  try {
    const result = await pool.query(`
      SELECT 
        user_id, role,
        COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
        COALESCE(is_active, true) as is_active, 
        COALESCE(permissions, ARRAY[]::TEXT[]) as permissions
      FROM users 
      WHERE email = $1 AND deleted_at IS NULL
    `, [email]);
    
    if (result.rows.length > 0) {
      const user = result.rows[0];
      const isActive = user.is_active;
      const isAdmin = user.is_admin || user.role === 'admin';
      const permissions = user.permissions || [];
      const canImpersonate = permissions.includes('admin:impersonate') || isAdmin;
      
      if (isActive && canImpersonate) {
        return { 
          isAdmin: true, 
          userId: user.user_id 
        };
      }
    }
    
    return { isAdmin: false };
  } catch (error) {
    console.error('Error checking admin permission:', error);
    return { isAdmin: false };
  }
}

// Helper function to log user activity
async function logUserActivity(
  userId: number,
  action: string,
  description: string,
  performedBy?: number,
  impersonationActive: boolean = false,
  request?: NextRequest
) {
  try {
    const clientIP = request?.headers.get('x-forwarded-for') || 
                    request?.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request?.headers.get('user-agent') || 'unknown';

    await pool.query(`
      INSERT INTO user_activity_log (
        user_id, action, description, ip_address, user_agent, 
        performed_by, impersonation_active
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [userId, action, description, clientIP, userAgent, performedBy, impersonationActive]);
  } catch (error) {
    console.warn('Could not log user activity (table may not exist yet):', error);
    // Continue execution - logging failure shouldn't break the API
  }
}

// Generate JWT token for impersonation
async function generateImpersonationToken(adminUserId: number, targetUserId: number, targetUserData: any) {
  const secret = new TextEncoder().encode(
    process.env.NEXTAUTH_SECRET || 'fallback-secret-key'
  );
  
  const alg = 'HS256';
  
  const jwt = await new SignJWT({ 
    sub: targetUserId.toString(),
    email: targetUserData.email,
    name: `${targetUserData.first_name} ${targetUserData.last_name}`,
    role: targetUserData.role,
    impersonatedBy: adminUserId,
    originalUserId: adminUserId,
    impersonationStarted: Date.now()
  })
  .setProtectedHeader({ alg })
  .setIssuedAt()
  .setExpirationTime('2h') // Impersonation session expires in 2 hours
  .sign(secret);
  
  return jwt;
}

// POST /api/admin/impersonate - Start impersonating a user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isAdmin, userId: adminUserId } = await checkAdminPermission(session.user.email);
    if (!isAdmin || !adminUserId) {
      return NextResponse.json({ 
        error: 'Admin access required with impersonation permissions' 
      }, { status: 403 });
    }

    const body = await request.json();
    const { targetUserId, reason = '' } = body;

    if (!targetUserId) {
      return NextResponse.json(
        { error: 'Target user ID is required' },
        { status: 400 }
      );
    }

    // Check if target user exists and is active
    const targetUserResult = await pool.query(`
      SELECT 
        user_id, first_name, last_name, email, role, is_active, is_admin,
        permissions, impersonated_by
      FROM users 
      WHERE user_id = $1 AND deleted_at IS NULL
    `, [targetUserId]);

    if (targetUserResult.rows.length === 0) {
      return NextResponse.json({ error: 'Target user not found' }, { status: 404 });
    }

    const targetUser = targetUserResult.rows[0];

    if (!targetUser.is_active) {
      return NextResponse.json({ 
        error: 'Cannot impersonate inactive user' 
      }, { status: 400 });
    }

    // Prevent impersonating another admin (optional security measure)
    if (targetUser.is_admin) {
      return NextResponse.json({ 
        error: 'Cannot impersonate another admin user' 
      }, { status: 400 });
    }

    // Check if user is already being impersonated
    if (targetUser.impersonated_by) {
      const impersonatorResult = await pool.query(
        'SELECT first_name, last_name FROM users WHERE user_id = $1',
        [targetUser.impersonated_by]
      );
      
      const impersonatorName = impersonatorResult.rows[0] 
        ? `${impersonatorResult.rows[0].first_name} ${impersonatorResult.rows[0].last_name}`
        : 'Unknown';

      return NextResponse.json({ 
        error: `User is already being impersonated by ${impersonatorName}` 
      }, { status: 409 });
    }

    // Prevent admin from impersonating themselves
    if (parseInt(targetUserId) === adminUserId) {
      return NextResponse.json({
        error: 'Cannot impersonate yourself'
      }, { status: 400 });
    }

    // Update target user to mark as being impersonated
    await pool.query(`
      UPDATE users 
      SET 
        impersonated_by = $1,
        impersonation_started_at = CURRENT_TIMESTAMP
      WHERE user_id = $2
    `, [adminUserId, targetUserId]);

    // Generate impersonation token
    const impersonationToken = await generateImpersonationToken(
      adminUserId, 
      targetUser.user_id, 
      targetUser
    );

    // Log impersonation start for both admin and target user
    await logUserActivity(
      targetUser.user_id,
      'impersonation_started',
      `Impersonation started by admin. Reason: ${reason}`,
      adminUserId,
      true,
      request
    );

    await logUserActivity(
      adminUserId,
      'impersonation_initiated',
      `Started impersonating user ${targetUser.email}. Reason: ${reason}`,
      adminUserId,
      false,
      request
    );

    return NextResponse.json({
      success: true,
      impersonationToken,
      targetUser: {
        user_id: targetUser.user_id,
        first_name: targetUser.first_name,
        last_name: targetUser.last_name,
        email: targetUser.email,
        role: targetUser.role,
        full_name: `${targetUser.first_name} ${targetUser.last_name}`
      },
      impersonatedBy: {
        user_id: adminUserId,
        email: session.user.email
      },
      impersonationStarted: new Date().toISOString(),
      message: 'Impersonation started successfully'
    });

  } catch (error) {
    console.error('Error starting impersonation:', error);
    return NextResponse.json(
      { error: 'Failed to start impersonation', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/impersonate - Stop impersonating a user
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isAdmin, userId: adminUserId } = await checkAdminPermission(session.user.email);
    if (!isAdmin || !adminUserId) {
      return NextResponse.json({ 
        error: 'Admin access required' 
      }, { status: 403 });
    }

    const body = await request.json();
    const { targetUserId } = body;

    if (!targetUserId) {
      return NextResponse.json(
        { error: 'Target user ID is required' },
        { status: 400 }
      );
    }

    // Check if user is currently being impersonated by this admin
    const impersonationResult = await pool.query(`
      SELECT 
        user_id, first_name, last_name, email, 
        impersonated_by, impersonation_started_at
      FROM users 
      WHERE user_id = $1 AND impersonated_by = $2 AND deleted_at IS NULL
    `, [targetUserId, adminUserId]);

    if (impersonationResult.rows.length === 0) {
      return NextResponse.json({ 
        error: 'User is not being impersonated by you' 
      }, { status: 400 });
    }

    const targetUser = impersonationResult.rows[0];

    // Stop impersonation
    await pool.query(`
      UPDATE users 
      SET 
        impersonated_by = NULL,
        impersonation_started_at = NULL
      WHERE user_id = $1
    `, [targetUserId]);

    // Log impersonation end for both admin and target user
    await logUserActivity(
      targetUser.user_id,
      'impersonation_ended',
      'Impersonation ended by admin',
      adminUserId,
      true,
      request
    );

    await logUserActivity(
      adminUserId,
      'impersonation_terminated',
      `Stopped impersonating user ${targetUser.email}`,
      adminUserId,
      false,
      request
    );

    return NextResponse.json({
      success: true,
      message: 'Impersonation stopped successfully'
    });

  } catch (error) {
    console.error('Error stopping impersonation:', error);
    return NextResponse.json(
      { error: 'Failed to stop impersonation', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// GET /api/admin/impersonate - Get current impersonation status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isAdmin, userId: adminUserId } = await checkAdminPermission(session.user.email);
    if (!isAdmin || !adminUserId) {
      return NextResponse.json({ 
        error: 'Admin access required' 
      }, { status: 403 });
    }

    // Get all users currently being impersonated by this admin
    const impersonationsResult = await pool.query(`
      SELECT 
        user_id, first_name, last_name, email, role,
        impersonation_started_at,
        CONCAT(first_name, ' ', last_name) as full_name
      FROM users 
      WHERE impersonated_by = $1 AND deleted_at IS NULL
      ORDER BY impersonation_started_at DESC
    `, [adminUserId]);

    return NextResponse.json({
      activeImpersonations: impersonationsResult.rows,
      count: impersonationsResult.rows.length
    });

  } catch (error) {
    console.error('Error getting impersonation status:', error);
    return NextResponse.json(
      { error: 'Failed to get impersonation status', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
