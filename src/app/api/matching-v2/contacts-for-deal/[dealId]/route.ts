import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { calculateV2MatchScore, calculateV2MatchScoreWithWeights, fetchCapitalPositionWeights, CapitalPositionFieldWeights } from "../../_lib/matching-utils-v2";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ dealId: string }> }
) {
  try {
    const { dealId } = await context.params;
    
    // Parse query parameters
    const url = new URL(req.url);
    const showAllMatches = url.searchParams.get('show_all') === 'true';
    const minScoreThreshold = 0 // Default to 50% if not showing all
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const isCrmMode = url.searchParams.get('crm_mode') === 'true';



    // Get deal data from dealsv2 table
    let dealQuery = `
      SELECT 
        d.deal_id,
        d.deal_name,
        d.summary,
        d.ask_capital_position,
        d.ask_amount,
        d.strategy,
        d.deal_stage,
        d.deal_status,
        d.hold_period,
        d.yield_on_cost,
        d.common_equity_internal_rate_of_return_irr,
        d.common_equity_equity_multiple,
        d.gp_equity_multiple,
        d.gp_internal_rate_of_return_irr,
        d.lp_equity_multiple,
        d.lp_internal_rate_of_return_irr,
        d.preferred_equity_internal_rate_of_return_irr,
        d.preferred_equity_equity_multiple,
        d.total_internal_rate_of_return_irr,
        d.total_equity_multiple,
        d.loan_amount,
        d.interest_rate,
        d.loan_term,
        d.loan_to_cost_ltc,
        d.loan_to_value_ltv,
        d.loan_type,
        d.dscr,
        d.recourse,
        d.extra_fields,
        d.exit_cap_rate,
        d.is_internal_only,
        d.date_received,
        -- Core NSF measurements moved to properties table
        -- d.total_nsf_net_square_foot,
        d.created_at,
        d.updated_at,
        -- Property data
        p.address,
        p.city,
        p.state,
        p.region,
        p.country,
        p.property_type,
        p.subproperty_type,
        p.building_sqft,
        p.number_of_units,
        -- Core NSF measurements from property table
        p.gsf_gross_square_foot,
        p.zfa_zoning_floor_area,
        p.total_nsf_net_square_foot
      FROM dealsv2 d
      LEFT JOIN properties p ON d.property_id = p.property_id
      WHERE d.deal_id = $1
    `;

    // Add CRM mode filters if enabled
    if (isCrmMode) {
      dealQuery += `
        AND d.is_internal_only = true
        AND d.date_received >= (CURRENT_DATE - INTERVAL '1.6 years')
      `;
    }

    // Get NSF fields for the deal
    const nsfQuery = `
      SELECT 
        capital_position,
        source_type,
        amount,
        use_type,
        is_required
      FROM deal_nsf_fields
      WHERE deal_id = $1
    `;
    
    // Get investment criteria equity for the deal
    const dealCriteriaEquityQuery = `
      SELECT 
        icc.capital_position,
        ice.target_return,
        ice.minimum_internal_rate_of_return,
        ice.minimum_equity_multiple,
        ice.min_hold_period_years,
        ice.max_hold_period_years,
        ice.target_cash_on_cash_min,
        ice.max_leverage_tolerance,
        ice.proof_of_funds_requirement
      FROM investment_criteria_central icc
      LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_equity_id = ice.investment_criteria_equity_id
      WHERE icc.entity_id = $1 AND icc.entity_type = 'deal_v2'
        AND icc.investment_criteria_equity_id IS NOT NULL
    `;

    // Get investment criteria debt for the deal
    const dealCriteriaDebtQuery = `
      SELECT 
        icc.capital_position,
        icd.loan_to_cost_min,
        icd.loan_to_cost_max,
        icd.loan_to_value_min,
        icd.loan_to_value_max,
        icd.min_loan_term,
        icd.max_loan_term,
        icd.min_loan_dscr,
        icd.max_loan_dscr,
        icd.loan_interest_rate,
        icd.loan_type,
        icd.amortization,
        icd.recourse_loan,
        icd.lien_position,
        icd.rate_type,
        icd.loan_program,
        icd.yield_maintenance,
        icd.loan_exit_max_fee,
        icd.loan_exit_min_fee,
        icd.loan_origination_max_fee,
        icd.loan_origination_min_fee,
        icd.occupancy_requirements,
        icd.closing_time,
        icd.loan_min_debt_yield,
        icd.structured_loan_tranche
      FROM investment_criteria_central icc
      LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_debt_id = icd.investment_criteria_debt_id
      WHERE icc.entity_id = $1 AND icc.entity_type = 'deal_v2'
        AND icc.investment_criteria_debt_id IS NOT NULL
    `;

    console.log(`🔍 V2 contacts-for-deal: Fetching deal ${dealId}${isCrmMode ? ' with CRM mode filters' : ''}`);
    
    const dealResult = await pool.query(dealQuery, [dealId]);
    
    if (dealResult.rows.length === 0) {
      const errorMessage = isCrmMode 
        ? 'Deal not found or does not meet CRM mode criteria (must be internal_only=true and received within 1.6 years)'
        : 'Deal not found';
      
      return NextResponse.json(
        { error: errorMessage, crm_mode: isCrmMode },
        { status: 404 }
      );
    }

        const deal = dealResult.rows[0];
    
    if (isCrmMode) {
      console.log(`✅ Deal ${dealId} meets CRM mode criteria:`, {
        is_internal_only: deal.is_internal_only,
        date_received: deal.date_received,
        days_since_received: deal.date_received 
          ? Math.floor((new Date().getTime() - new Date(deal.date_received).getTime()) / (1000 * 60 * 60 * 24))
          : 'N/A'
      });
    }
        
    // Get NSF fields for the deal
    const nsfResult = await pool.query(nsfQuery, [dealId]);
    const nsfFields = nsfResult.rows;
    

    
    // Get investment criteria equity for the deal
    const dealCriteriaEquityResult = await pool.query(dealCriteriaEquityQuery, [dealId]);
    const dealCriteriaEquity = dealCriteriaEquityResult.rows;
    
    // Get investment criteria debt for the deal
    const dealCriteriaDebtResult = await pool.query(dealCriteriaDebtQuery, [dealId]);
    const dealCriteriaDebt = dealCriteriaDebtResult.rows;
    

    
    // Log NSF fields with is_required=true
    const requiredNsfFields = nsfFields.filter(nsf => nsf.is_required === true);
    console.log(`📋 NSF fields with is_required=true:`, requiredNsfFields);
    
    // Log all NSF fields for debugging
    console.log(`📋 All NSF fields for deal ${dealId}:`, nsfFields.map(nsf => ({
      capital_position: nsf.capital_position,
      source_type: nsf.source_type,
      amount: nsf.amount,
      is_required: nsf.is_required,
      use_type: nsf.use_type
    })));
    
    // Add NSF fields and investment criteria to deal object
    deal.nsfFields = nsfFields;
    deal.investmentCriteriaEquity = dealCriteriaEquity;
    deal.investmentCriteriaDebt = dealCriteriaDebt;
    
    // Get capital positions and amounts from NSF fields where is_required = true
    // Use capital_position if available, otherwise fall back to source_type
    const requiredCapitalPositions = nsfFields
      .filter(nsf => nsf.is_required === true)
      .map(nsf => nsf.capital_position || nsf.source_type)
      .filter(Boolean); // Remove any null/undefined values

    // Get deal amounts for each required capital position for deal size filtering
    const dealAmounts = new Map();
    nsfFields
      .filter(nsf => nsf.is_required === true)
      .forEach(nsf => {
        const position = nsf.capital_position || nsf.source_type;
        if (position && nsf.amount) {
          dealAmounts.set(position, parseInt(nsf.amount));
        }
      });

    // Additional validation for capital positions
    console.log('🔍 NSF field validation:');
    nsfFields.forEach((nsf, index) => {
      console.log(`  NSF ${index}:`, {
        id: nsf.id,
        capital_position: nsf.capital_position,
        is_required: nsf.is_required,
        is_required_type: typeof nsf.is_required,
        source_type: nsf.source_type,
        use_type: nsf.use_type
      });
    });

    console.log('🔍 NSF fields with is_required=true:', nsfFields.filter(nsf => nsf.is_required === true));
    console.log('🔍 Required capital positions from NSF:', requiredCapitalPositions);
    console.log('🔍 Required capital positions type:', typeof requiredCapitalPositions);
    console.log('🔍 Required capital positions is array:', Array.isArray(requiredCapitalPositions));

    if (requiredCapitalPositions.length === 0) {
      console.log('⚠️ No required capital positions found in NSF fields, returning empty response');
      return NextResponse.json({
        contacts: [],
        total: 0,
        message: `No required capital positions found in NSF fields for this deal. Found ${nsfFields.length} NSF fields, but none marked as required.`
      });
    }

    // Get all contacts with investment criteria that match the deal's required capital positions
    // Use central investment criteria table and join with debt/equity tables
 
    // First, let's determine which capital positions are debt vs equity
    const debtPositions = ['Senior Debt', 'Mezzanine', 'Bridge Loan', 'Construction Loan', 'Permanent Loan', 'Refinance Loan'];
    const equityPositions = ['Common Equity', 'Preferred Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP'];
    
    const contactsQuery = `
      WITH         matching_contact_criteria AS (
          SELECT DISTINCT 
            icc.investment_criteria_id,
            icc.entity_type,
            icc.entity_id,
            icc.capital_position,
            icc.minimum_deal_size,
            icc.maximum_deal_size,
            icc.property_types,
            icc.strategies,
            -- ICC Location (Highest Priority)
            icc.region as icc_region,
            icc.state as icc_state,
            icc.city as icc_city,
            icc.country as icc_country,
            -- Debt-specific fields (only populated for debt positions)
            icd.loan_to_value_min,
            icd.loan_to_value_max,
            icd.loan_to_cost_min,
            icd.loan_to_cost_max,
            icd.min_loan_term,
            icd.max_loan_term,
            icd.min_loan_dscr,
            icd.max_loan_dscr,
            icd.loan_interest_rate,
            icd.loan_type,
            icd.amortization,
            icd.recourse_loan,
            -- Equity-specific fields (only populated for equity positions)
            ice.target_return,
            ice.min_hold_period_years,
            ice.max_hold_period_years,
            ice.minimum_equity_multiple,
            ice.minimum_yield_on_cost,
            ice.ownership_requirement,
            ice.attachment_point,
            ice.max_leverage_tolerance,
            -- Determine if this is debt or equity based on capital position
            CASE 
              WHEN icc.capital_position = ANY($2::text[]) THEN 'debt'
              WHEN icc.capital_position = ANY($3::text[]) THEN 'equity'
              ELSE 'unknown'
            END as position_type,
            'Contact' as criteria_source
          FROM investment_criteria_central icc
          LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
          LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
          WHERE icc.entity_type = 'contact' 
            AND icc.capital_position IS NOT NULL
            AND icc.capital_position = ANY($1::text[])
        ),
        matching_company_criteria AS (
          SELECT DISTINCT 
            icc.investment_criteria_id,
            icc.entity_type,
            icc.entity_id,
            icc.capital_position,
            icc.minimum_deal_size,
            icc.maximum_deal_size,
            icc.property_types,
            icc.strategies,
            -- ICC Location (Highest Priority)
            icc.region as icc_region,
            icc.state as icc_state,
            icc.city as icc_city,
            icc.country as icc_country,
            -- Debt-specific fields (only populated for debt positions)
            icd.loan_to_value_min,
            icd.loan_to_value_max,
            icd.loan_to_cost_min,
            icd.loan_to_cost_max,
            icd.min_loan_term,
            icd.max_loan_term,
            icd.min_loan_dscr,
            icd.max_loan_dscr,
            icd.loan_interest_rate,
            icd.loan_type,
            icd.amortization,
            icd.recourse_loan,
            -- Equity-specific fields (only populated for equity positions)
            ice.target_return,
            ice.min_hold_period_years,
            ice.max_hold_period_years,
            ice.minimum_equity_multiple,
            ice.minimum_yield_on_cost,
            ice.ownership_requirement,
            ice.attachment_point,
            ice.max_leverage_tolerance,
            -- Determine if this is debt or equity based on capital position
            CASE 
              WHEN icc.capital_position = ANY($2::text[]) THEN 'debt'
              WHEN icc.capital_position = ANY($3::text[]) THEN 'equity'
              ELSE 'unknown'
            END as position_type,
            'Company' as criteria_source
          FROM investment_criteria_central icc
          LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
          LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
          INNER JOIN contacts ct ON icc.entity_id = ct.company_id
          WHERE icc.entity_type = 'company'
            AND icc.capital_position IS NOT NULL
            AND icc.capital_position = ANY($1::text[])
        ),
      all_matching_criteria AS (
        SELECT * FROM matching_contact_criteria
        UNION ALL
        -- Only include company criteria if no contact criteria exist
        SELECT * FROM matching_company_criteria 
        WHERE NOT EXISTS (SELECT 1 FROM matching_contact_criteria)
      )
      SELECT DISTINCT
        c.contact_id,
        c.company_id,
        c.first_name,
        c.last_name,
        c.email,
        c.title AS job_title,
        c.phone_number,
        c.linkedin_url,
        c.updated_at AS contact_updated_at,
        c.created_at AS contact_created_at,
        comp.company_name,
        comp.company_city,
        comp.company_state,
        comp.company_website,
        comp.industry,
        icc.investment_criteria_id,
        icc.criteria_source,
        icc.capital_position,
        icc.minimum_deal_size,
        icc.maximum_deal_size,
        icc.property_types,
        icc.strategies,
        -- ICC Location (Highest Priority)
        icc_region,
        icc_state,
        icc_city,
        icc_country,
        -- Contact/Company Location (Secondary Priority)
        c.contact_city,
        c.contact_state,
        c.contact_country,
        c.region as contact_region,
        comp.company_city as comp_city,
        comp.company_state as comp_state,
        comp.company_country as comp_country,
        -- Debt-specific fields
        loan_to_value_min,
        loan_to_value_max,
        loan_to_cost_min,
        loan_to_cost_max,
        min_loan_term,
        max_loan_term,
        min_loan_dscr,
        max_loan_dscr,
        loan_interest_rate,
        loan_type,
        amortization,
        recourse_loan,
        -- Equity-specific fields
        target_return,
        min_hold_period_years,
        max_hold_period_years,
        minimum_equity_multiple,
        minimum_yield_on_cost,
        ownership_requirement,
        attachment_point,
        max_leverage_tolerance,
        -- Position type for proper matching
        position_type
      FROM contacts c
      INNER JOIN all_matching_criteria icc ON (
        (icc.criteria_source = 'Contact' AND icc.entity_id = c.contact_id) OR
        (icc.criteria_source = 'Company' AND icc.entity_id = c.company_id)
      )
      LEFT JOIN companies comp ON c.company_id = comp.company_id
      ORDER BY c.contact_id, icc.criteria_source DESC, icc.investment_criteria_id
      LIMIT $4 OFFSET $5
    `;

    console.log('🔍 Query parameters:', {
      requiredCapitalPositions,
      debtPositions, 
      equityPositions,
      limit,
      offset
    });
    
    const contactsResult = await pool.query(contactsQuery, [requiredCapitalPositions, debtPositions, equityPositions, limit, offset]);
    const contacts = contactsResult.rows;
    
    // Debug: Check specific contacts
    
    const bradley = contacts.find(c => c.contact_id === '19741');
    const dustin = contacts.find(c => c.contact_id === '45228');
    console.log('🔍 Contact Check Results:');
    console.log('  Bradley (19741):', bradley ? 'FOUND' : 'NOT FOUND');
    console.log('  Dustin (45228):', dustin ? 'FOUND' : 'NOT FOUND');
    console.log('  Required capital positions:', requiredCapitalPositions);
    console.log('  Total contacts returned:', contacts.length);
    if (contacts.length > 0) {
      console.log('  Sample contact IDs:', contacts.slice(0, 3).map(c => c.contact_id));
      console.log('  Sample capital positions:', contacts.slice(0, 3).map(c => c.capital_position));
    }

    // Pre-fetch all unique capital position weights to avoid repeated DB calls
    console.log('🔄 Pre-fetching capital position weights...');
    const allCapitalPositions = new Set<string>(requiredCapitalPositions);
    
    console.log(`📋 Found ${allCapitalPositions.size} unique capital positions: ${Array.from(allCapitalPositions).join(', ')}`);
    
    // Batch fetch all weights
    const weightsCache = new Map<string, CapitalPositionFieldWeights>();
    for (const capitalPosition of allCapitalPositions) {
      try {
        const weights = await fetchCapitalPositionWeights(capitalPosition);
        weightsCache.set(capitalPosition, weights);
        console.log(`✅ Cached weights for ${capitalPosition}: ${Object.keys(weights).length} fields`);
      } catch (error) {
        console.error(`❌ Failed to fetch weights for ${capitalPosition}:`, error);
        weightsCache.set(capitalPosition, {});
      }
    }
    
    console.log(`🎯 Successfully cached weights for ${weightsCache.size} capital positions`);

    // Calculate matches for each contact and group by contact_id
    const contactMatches = new Map(); // contact_id -> array of matches

    for (const contact of contacts) {
      // Check deal size compatibility before processing
      const dealAmount = dealAmounts.get(contact.capital_position);
      if (dealAmount) {
        const minSize = contact.minimum_deal_size;
        const maxSize = contact.maximum_deal_size;
        
        // Skip if deal doesn't fit the contact's size criteria
        if (minSize && dealAmount < minSize) continue;
        if (maxSize && dealAmount > maxSize) continue;
      }
      
      // For each required capital position from NSF fields, calculate match score
      for (const dealCapitalPosition of requiredCapitalPositions) {
        // Check if this contact has criteria for this capital position
        if (contact.capital_position === dealCapitalPosition) {
          console.log(`    ✅ Capital position match found for contact ${contact.contact_id}`);
          console.log(`    📊 Position type: ${contact.position_type} (${dealCapitalPosition})`);
          
          // Get cached weights for this capital position
          const cachedWeights = weightsCache.get(dealCapitalPosition) || {};
          
          // Calculate match score using cached weights (no DB calls)
          const matchResult = calculateV2MatchScoreWithWeights(
            deal,
            contact,
            dealCapitalPosition,
            cachedWeights,
            contact.position_type // Pass position type for proper field matching
          );

          const matchScore = Math.round(matchResult.totalScore * 100);
          console.log(`    📊 Match score calculated: ${matchScore} (threshold: ${minScoreThreshold})`);
          console.log(`    🔍 V2 Detailed Scoring Breakdown:`);
          matchResult.breakdown.forEach((field: any, index: number) => {
            console.log(`      ${index + 1}. ${field.field}: ${Math.round(field.score * 100)}% (weight: ${Math.round(field.weight * 100)}%) - ${field.reason}`);
          });
          console.log(`    📈 Total Score: ${matchResult.totalScore} (${Math.round(matchResult.totalScore * 100)}%)`);

          // Only include matches above threshold
          if (matchScore >= minScoreThreshold) {
            console.log(`    ✅ Match score above threshold, adding to contact matches`);
            
            const matchData = {
              contact_id: contact.contact_id,
              company_id: contact.company_id,
              company_name: contact.company_name,
              contact_name: `${contact.first_name || ''} ${contact.last_name || ''}`.trim(),
              email: contact.email,
              phone: contact.phone_number,
              capital_position: dealCapitalPosition,
              match_score: matchScore / 100, // Convert to 0-1 scale
              confidence: 0.8, // Default confidence
              reasons: matchResult.reasons || [],
              // V2 Detailed Scoring Breakdown - This is the actual scoring data
              detailed_scoring: matchResult.breakdown || [],
              total_score: matchResult.totalScore,
              // Enhanced scoring summary with proper field breakdown
              scoring_summary: (() => {
                const summary = {
                  total_score_percentage: Math.round(matchResult.totalScore * 100),
                  field_breakdown: matchResult.breakdown.map((field: any) => ({
                    field_name: field.field,
                    score_percentage: Math.round(field.score * 100),
                    weight_percentage: Math.round(field.weight * 100),
                    reason: field.reason,
                    confidence: field.confidence
                  })),
                  top_matches: matchResult.breakdown
                    .filter((field: any) => field.score > 0)
                    .sort((a: any, b: any) => b.score - a.score)
                    .slice(0, 5)
                    .map((field: any) => ({
                      field: field.field,
                      score: Math.round(field.score * 100),
                      reason: field.reason
                    })),
                  // Show the actual weights used for this capital position
                  weights_used: matchResult.breakdown.reduce((acc: any, field: any) => {
                    acc[field.field] = Math.round(field.weight * 100);
                    return acc;
                  }, {}),
                  // Position-aware matching info
                  position_type: contact.position_type,
                  capital_position: dealCapitalPosition,
                  // Add comprehensive match analysis
                  match_analysis: {
                    total_fields_evaluated: matchResult.breakdown.length,
                    fields_with_matches: matchResult.breakdown.filter((field: any) => field.score > 0).length,
                    fields_without_matches: matchResult.breakdown.filter((field: any) => field.score === 0).length,
                    average_confidence: matchResult.breakdown.length > 0 ? 
                      matchResult.breakdown.reduce((sum: number, field: any) => sum + field.confidence, 0) / matchResult.breakdown.length : 0
                  }
                };
                return summary;
              })(),
              criteria_source: contact.criteria_source,
              investment_criteria_id: contact.investment_criteria_id,
              contact_data: {
                target_return: contact.target_return,
                property_types: contact.property_types,
                deal_amount_min: contact.minimum_deal_size,
                deal_amount_max: contact.maximum_deal_size,
                strategies: contact.strategies,
                // Location with Priority: ICC > Contact > Company
                region: contact.icc_region || contact.contact_region || contact.contact_country,
                state: contact.icc_state || contact.contact_state || contact.comp_state,
                city: contact.icc_city || contact.contact_city || contact.comp_city,
                country: contact.icc_country || contact.contact_country || contact.comp_country,
                // Position type for proper field matching
                position_type: contact.position_type,
                // Debt fields (only populated for debt positions)
                loan_to_value_min: contact.loan_to_value_min,
                loan_to_value_max: contact.loan_to_value_max,
                loan_to_cost_min: contact.loan_to_cost_min,
                loan_to_cost_max: contact.loan_to_cost_max,
                min_loan_term: contact.min_loan_term,
                max_loan_term: contact.max_loan_term,
                min_loan_dscr: contact.min_loan_dscr,
                max_loan_dscr: contact.max_loan_dscr,
                loan_interest_rate: contact.loan_interest_rate,
                loan_type: contact.loan_type,
                amortization: contact.amortization,
                recourse_loan: contact.recourse_loan,
                // Equity fields (only populated for equity positions)
                min_hold_period_years: contact.min_hold_period_years,
                max_hold_period_years: contact.max_hold_period_years,
                minimum_equity_multiple: contact.minimum_equity_multiple,
                minimum_yield_on_cost: contact.minimum_yield_on_cost,
                ownership_requirement: contact.ownership_requirement,
                attachment_point: contact.attachment_point,
                max_leverage_tolerance: contact.max_leverage_tolerance
              },
              deal_data: {
                deal_amount: (() => {
                  // Find NSF field amount for this capital position
                  if (deal.nsfFields && Array.isArray(deal.nsfFields)) {
                    const matchingNsfField = deal.nsfFields.find((nsf: any) => 
                      nsf.source_type === dealCapitalPosition || 
                      nsf.capital_position === dealCapitalPosition
                    );
                    return matchingNsfField?.amount || 0;
                  }
                  return 0;
                })(),
                location: {
                  city: deal.city,
                  state: deal.state,
                  region: deal.region,
                  country: deal.country,
                  address: deal.address
                },
                property_types: deal.property_type ? [deal.property_type] : []
              }
            };

            // Group by contact_id
            if (!contactMatches.has(contact.contact_id)) {
              contactMatches.set(contact.contact_id, []);
            }
            contactMatches.get(contact.contact_id).push(matchData);
          }
        }
      }
    }

    // Consolidate matches for each contact
    const consolidatedMatches: any[] = [];
    
    for (const [contactId, matches] of contactMatches) {
      if (matches.length === 0) continue;
      
      // Sort matches by score (highest first) to get best score
      matches.sort((a: any, b: any) => b.match_score - a.match_score);
      const bestMatch = matches[0];
      
      // Consolidate all reasons from all criteria
      const allReasons = new Set<string>();
      const allCapitalPositions = new Set<string>();
      const allCriteriaSources = new Set<string>();
      const allInvestmentCriteriaIds = new Set<number>();
      
      matches.forEach((match: any) => {
        match.reasons.forEach((reason: string) => allReasons.add(reason));
        allCapitalPositions.add(match.capital_position);
        allCriteriaSources.add(match.criteria_source);
        allInvestmentCriteriaIds.add(match.investment_criteria_id);
      });

      // Create consolidated contact object
      const consolidatedContact = {
        ...bestMatch,
        // Use best score as overall score
        match_score: bestMatch.match_score,
        // Combine all reasons
        reasons: Array.from(allReasons),
        // Show all capital positions this contact can provide
        capital_positions: Array.from(allCapitalPositions),
        // Show all criteria sources
        criteria_sources: Array.from(allCriteriaSources),
        // Show all investment criteria IDs
        investment_criteria_ids: Array.from(allInvestmentCriteriaIds),
        // Add metadata about consolidation
        total_criteria_matched: matches.length,
        criteria_details: matches.map((match: any) => ({
          capital_position: match.capital_position,
          criteria_source: match.criteria_source,
          investment_criteria_id: match.investment_criteria_id,
          individual_score: match.match_score,
          reasons: match.reasons
        }))
      };
      
      consolidatedMatches.push(consolidatedContact);
    }

    // Sort consolidated matches by score (highest first)
    consolidatedMatches.sort((a, b) => b.match_score - a.match_score);
    
    console.log(`🏁 Final results: ${consolidatedMatches.length} consolidated contacts found`);
    if (consolidatedMatches.length > 0) {
      console.log('📋 First consolidated contact sample:', consolidatedMatches[0]);
      console.log('🔑 First consolidated contact keys:', Object.keys(consolidatedMatches[0]));
    }

    // Get total count for pagination
    const totalQuery = `
      WITH matching_contact_criteria AS (
        SELECT DISTINCT icc.entity_id
        FROM investment_criteria_central icc
        WHERE icc.entity_type = 'contact' 
          AND icc.capital_position IS NOT NULL
          AND icc.capital_position = ANY($1::text[])
      ),
      matching_company_criteria AS (
        SELECT DISTINCT ct.contact_id as entity_id
        FROM investment_criteria_central icc
        INNER JOIN contacts ct ON icc.entity_id = ct.company_id
        WHERE icc.entity_type = 'company' 
          AND icc.capital_position IS NOT NULL
          AND icc.capital_position = ANY($1::text[])
      )
      SELECT COUNT(DISTINCT c.contact_id) as total
      FROM contacts c
      INNER JOIN (
        SELECT entity_id FROM matching_contact_criteria
        UNION
        SELECT entity_id FROM matching_company_criteria
      ) mc ON mc.entity_id = c.contact_id
    `;
    
    const totalResult = await pool.query(totalQuery, [requiredCapitalPositions]);
    const total = parseInt(totalResult.rows[0].total);

    return NextResponse.json({
      contacts: consolidatedMatches,
      total,
      pagination: {
        limit,
        offset,
        hasMore: offset + limit < total
      },
      deal: {
        deal_id: deal.deal_id,
        deal_name: deal.deal_name,
        capital_positions: requiredCapitalPositions,
        ask_amount: deal.ask_amount,
        strategy: deal.strategy,
        deal_stage: deal.deal_stage,
        property_type: deal.property_type,
        location: {
          city: deal.city,
          state: deal.state,
          region: deal.region
        }
      },
      crm_mode: {
        enabled: isCrmMode,
        filters: isCrmMode ? {
          internal_only: true,
          max_age_years: 1.6,
          deal_internal_only: deal.is_internal_only,
          deal_date_received: deal.date_received,
          deal_age_days: deal.date_received 
            ? Math.floor((new Date().getTime() - new Date(deal.date_received).getTime()) / (1000 * 60 * 60 * 24))
            : null
        } : null
      },
      matching_config: {
        position_aware_matching: true,
        debt_positions: debtPositions,
        equity_positions: equityPositions,
        description: "V2 matching system now ensures debt fields are only matched against debt criteria and equity fields against equity criteria",
        scoring_accuracy: "Detailed scoring breakdown now shows actual V2 calculation results, not simplified summaries",
        field_matching: "Only relevant fields for the capital position type are scored (debt vs equity separation)"
      }
    });

  } catch (error) {
    console.error('Error in V2 contacts-for-deal matching:', error);
    return NextResponse.json(
      { error: 'Failed to fetch matching contacts' },
      { status: 500 }
    );
  }
}
