import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// POST: Calculate matching weights based on capital position
export async function POST(req: NextRequest) {
  try {
    const { capitalPosition, dealId, contactId } = await req.json();

    if (!capitalPosition) {
      return NextResponse.json({ 
        success: false, 
        message: 'Capital position is required' 
      }, { status: 400 });
    }

    console.log(`🔍 Matching API: Calculating weights for ${capitalPosition} position`);

    // Get COMMON weights for all tables
    const commonWeightsQuery = `
      SELECT 
        field_name,
        weight,
        description,
        table_name,
        field_category,
        is_active
      FROM capital_position_field_weights
      WHERE capital_position = 'COMMON' AND is_active = true
      ORDER BY table_name, weight DESC
    `;

    const commonResult = await pool.query(commonWeightsQuery);
    const commonWeights = commonResult.rows;

    // Separate weights by table
    const dealWeights = commonWeights.filter(w => w.table_name === 'dealsv2' && parseFloat(w.weight) > 0);
    const propertyWeights = commonWeights.filter(w => w.table_name === 'properties' && parseFloat(w.weight) > 0);
    const debtWeights = commonWeights.filter(w => w.table_name === 'investment_criteria_debt' && parseFloat(w.weight) > 0);
    const equityWeights = commonWeights.filter(w => w.table_name === 'investment_criteria_equity' && parseFloat(w.weight) > 0);

    // Determine which position-specific weights to use
    let positionWeights: any[] = [];
    let positionType = '';
    
    if (capitalPosition.toLowerCase().includes('debt') || 
        capitalPosition.toLowerCase().includes('loan') || 
        capitalPosition.toLowerCase().includes('senior') ||
        capitalPosition.toLowerCase().includes('mezzanine')) {
      positionWeights = debtWeights;
      positionType = 'DEBT';
    } else if (capitalPosition.toLowerCase().includes('equity') || 
               capitalPosition.toLowerCase().includes('gp') || 
               capitalPosition.toLowerCase().includes('lp') ||
               capitalPosition.toLowerCase().includes('preferred')) {
      positionWeights = equityWeights;
      positionType = 'EQUITY';
    } else {
      // Default to equity for other positions
      positionWeights = equityWeights;
      positionType = 'EQUITY';
    }

    // Calculate totals for validation
    const dealTotal = dealWeights.reduce((sum, w) => sum + parseFloat(w.weight), 0);
    const propertyTotal = propertyWeights.reduce((sum, w) => sum + parseFloat(w.weight), 0);
    const positionTotal = positionWeights.reduce((sum, w) => sum + parseFloat(w.weight), 0);
    const totalWeight = dealTotal + propertyTotal + positionTotal;

    // Validate that total equals 100%
    if (Math.abs(totalWeight - 1.0) > 0.001) {
      console.warn(`⚠️ Weight validation failed: Deal(${dealTotal}) + Property(${propertyTotal}) + ${positionType}(${positionTotal}) = ${totalWeight} (should be 1.0)`);
    }

    // Build the final matching weights object
    const matchingWeights = {
      capitalPosition,
      positionType,
      totalWeight: Math.round(totalWeight * 1000) / 10,
      weights: {
        deal: {
          total: Math.round(dealTotal * 1000) / 10,
          fields: dealWeights.map(w => ({
            field: w.field_name,
            weight: Math.round(parseFloat(w.weight) * 1000) / 10,
            description: w.description,
            table: w.table_name
          }))
        },
        property: {
          total: Math.round(propertyTotal * 1000) / 10,
          fields: propertyWeights.map(w => ({
            field: w.field_name,
            weight: Math.round(parseFloat(w.weight) * 1000) / 10,
            description: w.description,
            table: w.table_name
          }))
        },
        position: {
          type: positionType,
          total: Math.round(positionTotal * 1000) / 10,
          fields: positionWeights.map(w => ({
            field: w.field_name,
            weight: Math.round(parseFloat(w.weight) * 1000) / 10,
            description: w.description,
            table: w.table_name
          }))
        }
      },
      validation: {
        isValid: Math.abs(totalWeight - 1.0) <= 0.001,
        message: Math.abs(totalWeight - 1.0) <= 0.001 
          ? `✅ Valid: Deal(${Math.round(dealTotal * 100)})% + Property(${Math.round(propertyTotal * 100)})% + ${positionType}(${Math.round(positionTotal * 100)})% = 100%`
          : `❌ Invalid: Deal(${Math.round(dealTotal * 100)})% + Property(${Math.round(propertyTotal * 100)})% + ${positionType}(${Math.round(positionTotal * 100)})% = ${Math.round(totalWeight * 100)}%`
      },
      matchingLogic: {
        description: `For ${capitalPosition} positions: Deal weights + Property weights + ${positionType} weights = 100%`,
        dealFields: dealWeights.length,
        propertyFields: propertyWeights.length,
        positionFields: positionWeights.length,
        totalFields: dealWeights.length + propertyWeights.length + positionWeights.length
      }
    };

    // Log the matching weights for debugging
    console.log(`📊 Matching weights calculated for ${capitalPosition}:`, {
      dealTotal: Math.round(dealTotal * 100),
      propertyTotal: Math.round(propertyTotal * 100),
      positionTotal: Math.round(positionTotal * 100),
      totalWeight: Math.round(totalWeight * 100),
      positionType,
      fieldCounts: {
        deal: dealWeights.length,
        property: propertyWeights.length,
        position: positionWeights.length
      }
    });

    return NextResponse.json({
      success: true,
      data: matchingWeights,
      message: `Matching weights calculated for ${capitalPosition} position`
    });

  } catch (error) {
    console.error('❌ Error in matching weights API:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to calculate matching weights',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET: Get available capital positions and their types
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const getPositions = searchParams.get('positions') === 'true';

    if (getPositions) {
      // Get all capital positions and their types
      const positionsQuery = `
        SELECT DISTINCT 
          capital_position,
          CASE 
            WHEN capital_position ILIKE '%debt%' OR capital_position ILIKE '%loan%' OR capital_position ILIKE '%senior%' OR capital_position ILIKE '%mezzanine%' THEN 'DEBT'
            WHEN capital_position ILIKE '%equity%' OR capital_position ILIKE '%gp%' OR capital_position ILIKE '%lp%' OR capital_position ILIKE '%preferred%' THEN 'EQUITY'
            ELSE 'EQUITY'
          END as position_type,
          CASE 
            WHEN capital_position ILIKE '%debt%' OR capital_position ILIKE '%loan%' OR capital_position ILIKE '%senior%' OR capital_position ILIKE '%mezzanine%' THEN 'Uses debt-specific weights (loan terms, LTV, DSCR, etc.)'
            WHEN capital_position ILIKE '%equity%' OR capital_position ILIKE '%gp%' OR capital_position ILIKE '%lp%' OR capital_position ILIKE '%preferred%' THEN 'Uses equity-specific weights (returns, multiples, cash-on-cash, etc.)'
            ELSE 'Uses equity-specific weights by default'
          END as description
        FROM capital_position_field_weights
        WHERE is_active = true
        ORDER BY capital_position
      `;

      const positionsResult = await pool.query(positionsQuery);

      return NextResponse.json({
        success: true,
        data: {
          positions: positionsResult.rows,
          matchingLogic: {
            debtPositions: 'Deal + Property + Debt weights = 100%',
            equityPositions: 'Deal + Property + Equity weights = 100%',
            note: 'Only fields with non-0% weights are included in actual matching'
          }
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Matching weights API - Use POST to calculate weights for a specific position',
      endpoints: {
        'POST /': 'Calculate matching weights for a capital position',
        'GET ?positions=true': 'Get all capital positions and their types'
      }
    });

  } catch (error) {
    console.error('❌ Error in matching weights API GET:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to get positions',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}



