import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { calculateV2MatchScore, calculateV2MatchScoreWithWeights, fetchCapitalPositionWeights, CapitalPositionFieldWeights } from "../../_lib/matching-utils-v2";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await context.params;
    
    // Parse query parameters
    const url = new URL(req.url);
    const showAllMatches = url.searchParams.get('show_all') === 'true';
    const minScoreThreshold = showAllMatches ? 0 : 50; // Default to 50% if not showing all
    // const limit = parseInt(url.searchParams.get('limit') || '50');
    // const offset = parseInt(url.searchParams.get('offset') || '0');
    const isCrmMode = url.searchParams.get('crm_mode') === 'true';
    const entityType = url.searchParams.get('entity_type') || 'contact'; // Default to contact for backward compatibility

    console.log(`V2: Fetching matching deals for ${entityType} ${contactId} using central investment criteria tables`);

    // Get entity data and investment criteria - handle both contact and company
    const entityQuery = entityType === 'company' ? `
      WITH matching_company_criteria AS (
        SELECT DISTINCT 
          icc.investment_criteria_id,
          icc.entity_type,
          icc.entity_id,
          icc.capital_position,
          icc.minimum_deal_size,
          icc.maximum_deal_size,
          icc.property_types,
          icc.strategies,
          -- ICC Location (Highest Priority)
          icc.region as icc_region,
          icc.state as icc_state,
          icc.city as icc_city,
          icc.country as icc_country,
          -- Debt-specific fields (only populated for debt positions)
          icd.loan_to_value_min,
          icd.loan_to_value_max,
          icd.loan_to_cost_min,
          icd.loan_to_cost_max,
          icd.min_loan_term,
          icd.max_loan_term,
          icd.min_loan_dscr,
          icd.max_loan_dscr,
          icd.loan_interest_rate,
          icd.loan_type,
          icd.amortization,
          icd.recourse_loan,
          -- Equity-specific fields (only populated for equity positions)
          ice.target_return,
          ice.minimum_internal_rate_of_return,
          ice.min_hold_period_years,
          ice.max_hold_period_years,
          ice.minimum_equity_multiple,
          ice.minimum_yield_on_cost,
          ice.ownership_requirement,
          ice.attachment_point,
          ice.max_leverage_tolerance,
          -- Determine if this is debt or equity based on capital position
          CASE 
            WHEN icc.capital_position = ANY(ARRAY['Senior Debt', 'Mezzanine', 'Bridge Loan', 'Construction Loan', 'Permanent Loan', 'Refinance Loan']) THEN 'debt'
            WHEN icc.capital_position = ANY(ARRAY['Common Equity', 'Preferred Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP']) THEN 'equity'
            ELSE 'unknown'
          END as position_type,
          'Company' as criteria_source
        FROM investment_criteria_central icc
        LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
        LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
        WHERE icc.entity_type = 'company' 
          AND icc.entity_id = $1
      )
      SELECT DISTINCT
        c.company_id,
        c.company_name,
        c.company_city,
        c.company_state,
        c.company_website,
        c.industry,
        icc.investment_criteria_id,
        icc.criteria_source,
        icc.capital_position,
        icc.minimum_deal_size,
        icc.maximum_deal_size,
        icc.property_types,
        icc.strategies,
        -- ICC Location (Highest Priority)
        icc_region,
        icc_state,
        icc_city,
        icc_country,
        -- Company Location (Secondary Priority)
        c.company_city as comp_city,
        c.company_state as comp_state,
        c.company_country as comp_country,
        -- Debt-specific fields
        loan_to_value_min,
        loan_to_value_max,
        loan_to_cost_min,
        loan_to_cost_max,
        min_loan_term,
        max_loan_term,
        min_loan_dscr,
        max_loan_dscr,
        loan_interest_rate,
        loan_type,
        amortization,
        recourse_loan,
        -- Equity-specific fields
        target_return,
        min_hold_period_years,
        max_hold_period_years,
        minimum_equity_multiple,
        minimum_yield_on_cost,
        ownership_requirement,
        attachment_point,
        max_leverage_tolerance,
        -- Position type for proper matching
        position_type
      FROM companies c
      INNER JOIN matching_company_criteria icc ON icc.entity_id = c.company_id
      WHERE c.company_id = $1
      ORDER BY icc.investment_criteria_id
    ` : `
      WITH matching_contact_criteria AS (
        SELECT DISTINCT 
          icc.investment_criteria_id,
          icc.entity_type,
          icc.entity_id,
          icc.capital_position,
          icc.minimum_deal_size,
          icc.maximum_deal_size,
          icc.property_types,
          icc.strategies,
          -- ICC Location (Highest Priority)
          icc.region as icc_region,
          icc.state as icc_state,
          icc.city as icc_city,
          icc.country as icc_country,
          -- Debt-specific fields (only populated for debt positions)
          icd.loan_to_value_min,
          icd.loan_to_value_max,
          icd.loan_to_cost_min,
          icd.loan_to_cost_max,
          icd.min_loan_term,
          icd.max_loan_term,
          icd.min_loan_dscr,
          icd.max_loan_dscr,
          icd.loan_interest_rate,
          icd.loan_type,
          icd.amortization,
          icd.recourse_loan,
          -- Equity-specific fields (only populated for equity positions)
          ice.target_return,
          ice.minimum_internal_rate_of_return,
          ice.min_hold_period_years,
          ice.max_hold_period_years,
          ice.minimum_equity_multiple,
          ice.minimum_yield_on_cost,
          ice.ownership_requirement,
          ice.attachment_point,
          ice.max_leverage_tolerance,
          -- Determine if this is debt or equity based on capital position
          CASE 
            WHEN icc.capital_position = ANY(ARRAY['Senior Debt', 'Mezzanine', 'Bridge Loan', 'Construction Loan', 'Permanent Loan', 'Refinance Loan']) THEN 'debt'
            WHEN icc.capital_position = ANY(ARRAY['Common Equity', 'Preferred Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP']) THEN 'equity'
            ELSE 'unknown'
          END as position_type,
          'Contact' as criteria_source
        FROM investment_criteria_central icc
        LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
        LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
        WHERE icc.entity_type = 'contact' 
          AND icc.entity_id = $1
      ),
      matching_company_criteria AS (
        SELECT DISTINCT 
          icc.investment_criteria_id,
          icc.entity_type,
          icc.entity_id,
          icc.capital_position,
          icc.minimum_deal_size,
          icc.maximum_deal_size,
          icc.property_types,
          icc.strategies,
          -- ICC Location (Highest Priority)
          icc.region as icc_region,
          icc.state as icc_state,
          icc.city as icc_city,
          icc.country as icc_country,
          -- Debt-specific fields (only populated for debt positions)
          icd.loan_to_value_min,
          icd.loan_to_value_max,
          icd.loan_to_cost_min,
          icd.loan_to_cost_max,
          icd.min_loan_term,
          icd.max_loan_term,
          icd.min_loan_dscr,
          icd.max_loan_dscr,
          icd.loan_interest_rate,
          icd.loan_type,
          icd.amortization,
          icd.recourse_loan,
          -- Equity-specific fields (only populated for equity positions)
          ice.target_return,
          ice.minimum_internal_rate_of_return,
          ice.min_hold_period_years,
          ice.max_hold_period_years,
          ice.minimum_equity_multiple,
          ice.minimum_yield_on_cost,
          ice.ownership_requirement,
          ice.attachment_point,
          ice.max_leverage_tolerance,
          -- Determine if this is debt or equity based on capital position
          CASE 
            WHEN icc.capital_position = ANY(ARRAY['Senior Debt', 'Mezzanine', 'Bridge Loan', 'Construction Loan', 'Permanent Loan', 'Refinance Loan']) THEN 'debt'
            WHEN icc.capital_position = ANY(ARRAY['Common Equity', 'Preferred Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP']) THEN 'equity'
            ELSE 'unknown'
          END as position_type,
          'Company' as criteria_source
        FROM investment_criteria_central icc
        LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
        LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
        INNER JOIN contacts ct ON icc.entity_id = ct.company_id
        WHERE icc.entity_type = 'company'
          AND ct.contact_id = $1
      ),
      all_matching_criteria AS (
        SELECT * FROM matching_contact_criteria
        UNION ALL
        -- Only include company criteria if no contact criteria exist
        SELECT * FROM matching_company_criteria 
        WHERE NOT EXISTS (SELECT 1 FROM matching_contact_criteria)
      )
      SELECT DISTINCT
        c.contact_id,
        c.company_id,
        c.first_name,
        c.last_name,
        c.email,
        c.title AS job_title,
        c.phone_number,
        c.linkedin_url,
        c.updated_at AS contact_updated_at,
        c.created_at AS contact_created_at,
        comp.company_name,
        comp.company_city,
        comp.company_state,
        comp.company_website,
        comp.industry,
        icc.investment_criteria_id,
        icc.criteria_source,
        icc.capital_position,
        icc.minimum_deal_size,
        icc.maximum_deal_size,
        icc.property_types,
        icc.strategies,
        -- ICC Location (Highest Priority)
        icc_region,
        icc_state,
        icc_city,
        icc_country,
        -- Contact/Company Location (Secondary Priority)
        c.contact_city,
        c.contact_state,
        c.contact_country,
        c.region as contact_region,
        comp.company_city as comp_city,
        comp.company_state as comp_state,
        comp.company_country as comp_country,
        -- Debt-specific fields
        loan_to_value_min,
        loan_to_value_max,
        loan_to_cost_min,
        loan_to_cost_max,
        min_loan_term,
        max_loan_term,
        min_loan_dscr,
        max_loan_dscr,
        loan_interest_rate,
        loan_type,
        amortization,
        recourse_loan,
        -- Equity-specific fields
        target_return,
        min_hold_period_years,
        max_hold_period_years,
        minimum_equity_multiple,
        minimum_yield_on_cost,
        ownership_requirement,
        attachment_point,
        max_leverage_tolerance,
        -- Position type for proper matching
        position_type
      FROM contacts c
      INNER JOIN all_matching_criteria icc ON (
        (icc.criteria_source = 'Contact' AND icc.entity_id = c.contact_id) OR
        (icc.criteria_source = 'Company' AND icc.entity_id = c.company_id)
      )
      LEFT JOIN companies comp ON c.company_id = comp.company_id
      WHERE c.contact_id = $1
      ORDER BY icc.criteria_source DESC, icc.investment_criteria_id
    `;

    const entityResult = await pool.query(entityQuery, [parseInt(contactId)]);
    
    if (entityResult.rows.length === 0) {
      return NextResponse.json({
        matches: [],
        total: 0,
        message: `No investment criteria found for this ${entityType}`
      });
    }

    const entityCriteria = entityResult.rows;
    console.log(`📊 Found ${entityCriteria.length} investment criteria records for ${entityType} ${contactId}`);

    // Get all deals from dealsv2 table with property data and NSF fields
    // This mirrors the structure from contacts-for-deal but gets all deals instead of a specific one
    let dealsQuery = `
      SELECT 
        d.deal_id,
        d.deal_name,
        d.summary,
        d.ask_capital_position,
        d.ask_amount,
        d.strategy,
        d.deal_stage,
        d.deal_status,
        d.hold_period,
        d.yield_on_cost,
        d.common_equity_internal_rate_of_return_irr,
        d.common_equity_equity_multiple,
        d.gp_equity_multiple,
        d.gp_internal_rate_of_return_irr,
        d.lp_equity_multiple,
        d.lp_internal_rate_of_return_irr,
        d.preferred_equity_internal_rate_of_return_irr,
        d.preferred_equity_equity_multiple,
        d.total_internal_rate_of_return_irr,
        d.total_equity_multiple,
        d.loan_amount,
        d.interest_rate,
        d.loan_term,
        d.loan_to_cost_ltc,
        d.loan_to_value_ltv,
        d.loan_type,
        d.dscr,
        d.recourse,
        d.extra_fields,
        -- NSF fields for required capital positions
        COALESCE(
          JSON_AGG(
            CASE 
              WHEN nsf.nsf_context = 'sources' AND nsf.is_required::boolean = true 
              THEN JSON_BUILD_OBJECT(
                'source_type', nsf.source_type,
                'capital_position', nsf.capital_position,
                'is_required', nsf.is_required,
                'amount', nsf.amount
              )
              ELSE NULL
            END
          ) FILTER (WHERE nsf.nsf_context = 'sources' AND nsf.is_required::boolean = true),
          '[]'::json
        ) as required_nsf_sources,
        d.exit_cap_rate,
        d.is_internal_only,
        d.date_received,
        d.created_at,
        d.updated_at,
        -- Property data
        p.address,
        p.city,
        p.state,
        p.region,
        p.country,
        p.property_type,
        p.subproperty_type,
        p.building_sqft,
        p.number_of_units,
        -- Core NSF measurements from property table
        p.gsf_gross_square_foot,
        p.zfa_zoning_floor_area,
        p.total_nsf_net_square_foot
      FROM dealsv2 d
      LEFT JOIN properties p ON d.property_id = p.property_id
      LEFT JOIN deal_nsf_fields nsf ON d.deal_id = nsf.deal_id
      WHERE 1=1
  

    `;
    
    // Add CRM mode filters if enabled
    if (isCrmMode) {
      dealsQuery += `
        AND (d.is_internal_only = false or d.is_internal_only IS NULL)
        AND (d.date_received >= (CURRENT_DATE - INTERVAL '1 year') OR d.date_received IS NULL)
      `;
    }

    dealsQuery += `GROUP BY d.deal_id, p.property_id  ORDER BY d.created_at DESC 
   `;

    const dealsResult = await pool.query(dealsQuery);
    const deals = dealsResult.rows;
    console.log(`📊 Found ${deals.length} deals to evaluate${isCrmMode ? ' (CRM mode)' : ''}`);

    // Get NSF fields for all deals in one query for efficiency
    const dealIds = deals.map(d => d.deal_id);
    if (dealIds.length === 0) {
      return NextResponse.json({
        matches: [],
        total: 0,
        message: 'No deals found'
      });
    }

    // Pre-fetch all unique capital position weights to avoid repeated DB calls
    console.log('🔄 Pre-fetching capital position weights...');
    const allCapitalPositions = new Set<string>();
    
    // Collect all capital positions from entity criteria
    entityCriteria.forEach(criteria => {
      if (criteria.capital_position) {
        allCapitalPositions.add(criteria.capital_position);
      }
    });
    
    // console.log(`📋 Found ${allCapitalPositions.size} unique capital positions: ${Array.from(allCapitalPositions).join(', ')}`);
    
    // Batch fetch all weights
    const weightsCache = new Map<string, CapitalPositionFieldWeights>();
    for (const capitalPosition of allCapitalPositions) {
      try {
        const weights = await fetchCapitalPositionWeights(capitalPosition);
        weightsCache.set(capitalPosition, weights);
        // console.log(`✅ Cached weights for ${capitalPosition}: ${Object.keys(weights).length} fields`);
      } catch (error) {
        console.error(`❌ Failed to fetch weights for ${capitalPosition}:`, error);
        weightsCache.set(capitalPosition, {});
      }
    }
    
    // console.log(`🎯 Successfully cached weights for ${weightsCache.size} capital positions`);

    const nsfQuery = `
      SELECT 
        deal_id,
        capital_position,
        source_type,
        amount,
        use_type,
        is_required
      FROM deal_nsf_fields
      WHERE deal_id = ANY($1)
    `;
    
    const nsfResult = await pool.query(nsfQuery, [dealIds]);
    const allNsfFields = nsfResult.rows;

    // Group NSF fields by deal_id
    const nsfByDeal = new Map();
    allNsfFields.forEach(nsf => {
      if (!nsfByDeal.has(nsf.deal_id)) {
        nsfByDeal.set(nsf.deal_id, []);
      }
      nsfByDeal.get(nsf.deal_id).push(nsf);
    });

    // Get investment criteria equity data for all deals to enable PRIORITY 1 hold period matching
    const investmentCriteriaEquityQuery = `
      SELECT 
        icc.entity_id as deal_id,
        icc.capital_position,
        ice.target_return,
        ice.minimum_internal_rate_of_return,
        ice.minimum_equity_multiple,
        ice.min_hold_period_years,
        ice.max_hold_period_years,
        ice.target_cash_on_cash_min,
        ice.max_leverage_tolerance,
        ice.proof_of_funds_requirement,
        ice.ownership_requirement,
        ice.attachment_point,
        ice.position_specific_irr,
        ice.minimum_yield_on_cost,
        ice.typical_closing_timeline_days,
        ice.equity_program_overview
      FROM investment_criteria_central icc
      LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_equity_id = ice.investment_criteria_equity_id
      WHERE icc.entity_id = ANY($1) AND icc.entity_type = 'deal_v2'
        AND icc.investment_criteria_equity_id IS NOT NULL
    `;
    
    const investmentCriteriaEquityResult = await pool.query(investmentCriteriaEquityQuery, [dealIds]);
    const allInvestmentCriteriaEquity = investmentCriteriaEquityResult.rows;

    // Get investment criteria debt data for all deals to enable debt field matching
    const investmentCriteriaDebtQuery = `
      SELECT 
        icc.entity_id as deal_id,
        icc.capital_position,
        icd.loan_to_cost_min,
        icd.loan_to_cost_max,
        icd.loan_to_value_min,
        icd.loan_to_value_max,
        icd.min_loan_term,
        icd.max_loan_term,
        icd.min_loan_dscr,
        icd.max_loan_dscr,
        icd.loan_interest_rate,
        icd.loan_type,
        icd.amortization,
        icd.recourse_loan,
        icd.lien_position,
        icd.rate_type,
        icd.loan_program,
        icd.yield_maintenance,
        icd.loan_exit_max_fee,
        icd.loan_exit_min_fee,
        icd.loan_origination_max_fee,
        icd.loan_origination_min_fee,
        icd.occupancy_requirements,
        icd.closing_time,
        icd.loan_min_debt_yield,
        icd.structured_loan_tranche
      FROM investment_criteria_central icc
      LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_debt_id = icd.investment_criteria_debt_id
      WHERE icc.entity_id = ANY($1) AND icc.entity_type = 'deal_v2'
        AND icc.investment_criteria_debt_id IS NOT NULL
    `;
    
    const investmentCriteriaDebtResult = await pool.query(investmentCriteriaDebtQuery, [dealIds]);
    const allInvestmentCriteriaDebt = investmentCriteriaDebtResult.rows;

    // Group investment criteria by deal_id and capital_position
    const investmentCriteriaEquityByDeal = new Map();
    allInvestmentCriteriaEquity.forEach(criteria => {
      if (!investmentCriteriaEquityByDeal.has(criteria.deal_id)) {
        investmentCriteriaEquityByDeal.set(criteria.deal_id, []);
      }
      investmentCriteriaEquityByDeal.get(criteria.deal_id).push(criteria);
    });

    const investmentCriteriaDebtByDeal = new Map();
    allInvestmentCriteriaDebt.forEach(criteria => {
      if (!investmentCriteriaDebtByDeal.has(criteria.deal_id)) {
        investmentCriteriaDebtByDeal.set(criteria.deal_id, []);
      }
      investmentCriteriaDebtByDeal.get(criteria.deal_id).push(criteria);
    });

    // Calculate matches for each deal with consolidation
    const dealMatches = new Map<string, any[]>();

    for (const deal of deals) {
      const nsfFields = nsfByDeal.get(deal.deal_id) || [];
      deal.nsfFields = nsfFields;
      
      // Get investment criteria equity data for this deal and add to deal object
      // This enables PRIORITY 1 hold period matching (investment criteria over deal table)
      const dealInvestmentCriteriaEquity = investmentCriteriaEquityByDeal.get(deal.deal_id) || [];
      deal.investmentCriteriaEquity = dealInvestmentCriteriaEquity;
      
      // Get investment criteria debt data for this deal and add to deal object
      // This enables debt field matching (loan_to_cost, loan_to_value, interest_rate, etc.)
      const dealInvestmentCriteriaDebt = investmentCriteriaDebtByDeal.get(deal.deal_id) || [];
      deal.investmentCriteriaDebt = dealInvestmentCriteriaDebt;
      
      // Get capital positions from NSF fields where is_required = true
      const requiredCapitalPositions = nsfFields
        .filter(nsf => nsf.is_required === true)
        .map(nsf => nsf.capital_position || nsf.source_type)
        .filter(Boolean);

      if (requiredCapitalPositions.length === 0) {
        // console.log(`⚠️ Deal ${deal.deal_id} has no required capital positions, skipping`);
        continue;
      }

      // For each capital position in the deal, find matching criteria
      for (const dealCapitalPosition of requiredCapitalPositions) {
        // Find criteria that match this capital position
        const matchingCriteria = entityCriteria.filter(criteria => 
          criteria.capital_position && 
          criteria.capital_position === dealCapitalPosition
        );

        for (const criteria of matchingCriteria) {
          // console.log(`🔍 Evaluating deal ${deal.deal_id} (${deal.deal_name}) for capital position ${dealCapitalPosition}`);
          
          // Get cached weights for this capital position
          const cachedWeights = weightsCache.get(dealCapitalPosition) || {};
          
          // Calculate match score using cached weights (no DB calls)
          const matchResult = calculateV2MatchScoreWithWeights(
            deal,
            criteria,
            dealCapitalPosition,
            cachedWeights,
            criteria.position_type // Pass position type for proper field matching
          );

          const matchScore = Math.round(matchResult.totalScore * 100);

          // Only include matches above threshold
          if (matchScore >= minScoreThreshold) {
            // console.log(`✅ Match score ${matchScore}% above threshold ${minScoreThreshold}%`);
            
            const matchData = {
              deal_id: deal.deal_id,
              deal_name: deal.deal_name,
              capital_position: dealCapitalPosition,
              match_score: matchScore,
              score: matchScore, // Add both for compatibility
              match_breakdown: matchResult.breakdown,
              match_reasons: matchResult.reasons,
              reasons: matchResult.reasons, // Add both for compatibility
              criteria_source: criteria.criteria_source,
              investment_criteria_id: criteria.investment_criteria_id,
              deal_data: {
                deal_id: deal.deal_id,
                deal_name: deal.deal_name,
                summary: deal.summary,
                ask_capital_position: deal.ask_capital_position,
                ask_amount: deal.ask_amount,
                strategy: deal.strategy,
                deal_stage: deal.deal_stage,
                deal_status: deal.deal_status,
                hold_period: deal.hold_period,
                yield_on_cost: deal.yield_on_cost,
                total_internal_rate_of_return_irr: deal.total_internal_rate_of_return_irr,
                total_equity_multiple: deal.total_equity_multiple,
                exit_cap_rate: deal.exit_cap_rate,
                loan_amount: deal.loan_amount,
                interest_rate: deal.interest_rate,
                loan_term: deal.loan_term,
                loan_to_cost_ltc: deal.loan_to_cost_ltc,
                loan_to_value_ltv: deal.loan_to_value_ltv,
                loan_type: deal.loan_type,
                dscr: deal.dscr,
                recourse: deal.recourse,
                property_address: deal.address,
                property_city: deal.city,
                property_state: deal.state,
                property_region: deal.region,
                property_country: deal.country,
                property_type: deal.property_type,
                property_subtype: deal.subproperty_type,
                building_sqft: deal.building_sqft,
                number_of_units: deal.number_of_units,
                total_nsf_net_square_foot: deal.total_nsf_net_square_foot,
                gsf_gross_square_foot: deal.gsf_gross_square_foot,
                zfa_zoning_floor_area: deal.zfa_zoning_floor_area,
                created_at: deal.created_at,
                updated_at: deal.updated_at
              },
              entity_data: {
                target_return: criteria.target_return,
                property_types: criteria.property_types,
                deal_amount_min: criteria.minimum_deal_size,
                deal_amount_max: criteria.maximum_deal_size,
                strategies: criteria.strategies,
                // Location with Priority: ICC > Contact/Company
                region: entityType === 'company' 
                  ? (criteria.icc_region || criteria.comp_country)
                  : (criteria.icc_region || criteria.contact_region || criteria.contact_country),
                state: entityType === 'company' 
                  ? (criteria.icc_state || criteria.comp_state)
                  : (criteria.icc_state || criteria.contact_state || criteria.comp_state),
                city: entityType === 'company' 
                  ? (criteria.icc_city || criteria.comp_city)
                  : (criteria.icc_city || criteria.contact_city || criteria.comp_city),
                country: entityType === 'company' 
                  ? (criteria.icc_country || criteria.comp_country)
                  : (criteria.icc_country || criteria.contact_country || criteria.comp_country),
                // Position type for proper field matching
                position_type: criteria.position_type,
                // Debt fields (only populated for debt positions)
                loan_to_value_min: criteria.loan_to_value_min,
                loan_to_value_max: criteria.loan_to_value_max,
                loan_to_cost_min: criteria.loan_to_cost_min,
                loan_to_cost_max: criteria.loan_to_cost_max,
                min_loan_term: criteria.min_loan_term,
                max_loan_term: criteria.max_loan_term,
                min_loan_dscr: criteria.min_loan_dscr,
                max_loan_dscr: criteria.max_loan_dscr,
                loan_interest_rate: criteria.loan_interest_rate,
                loan_type: criteria.loan_type,
                amortization: criteria.amortization,
                recourse_loan: criteria.recourse_loan,
                // Equity fields (only populated for equity positions)
                min_hold_period_years: criteria.min_hold_period_years,
                max_hold_period_years: criteria.max_hold_period_years,
                minimum_equity_multiple: criteria.minimum_equity_multiple,
                minimum_yield_on_cost: criteria.minimum_yield_on_cost,
                ownership_requirement: criteria.ownership_requirement,
                attachment_point: criteria.attachment_point,
                max_leverage_tolerance: criteria.max_leverage_tolerance
              },
              // V2 Detailed Scoring Breakdown
              detailed_scoring: matchResult.breakdown || [],
              total_score: matchResult.totalScore,
              // Enhanced scoring summary with proper field breakdown
              scoring_summary: {
                total_score_percentage: Math.round(matchResult.totalScore * 100),
                field_breakdown: matchResult.breakdown.map((field: any) => ({
                  field_name: field.field,
                  score_percentage: Math.round(field.score * 100),
                  weight_percentage: Math.round(field.weight * 100),
                  reason: field.reason,
                  confidence: field.confidence
                })),
                top_matches: matchResult.breakdown
                  .filter((field: any) => field.score > 0)
                  .sort((a: any, b: any) => b.score - a.score)
                  .slice(0, 5)
                  .map((field: any) => ({
                    field: field.field,
                    score: Math.round(field.score * 100),
                    reason: field.reason
                  })),
                // Show the actual weights used for this capital position
                weights_used: matchResult.breakdown.reduce((acc: any, field: any) => {
                  acc[field.field] = Math.round(field.weight * 100);
                  return acc;
                }, {}),
                // Position-aware matching info
                position_type: criteria.position_type,
                capital_position: dealCapitalPosition,
                // Add comprehensive match analysis
                match_analysis: {
                  total_fields_evaluated: matchResult.breakdown.length,
                  fields_with_matches: matchResult.breakdown.filter((field: any) => field.score > 0).length,
                  fields_without_matches: matchResult.breakdown.filter((field: any) => field.score === 0).length,
                  average_confidence: matchResult.breakdown.length > 0 ? 
                    matchResult.breakdown.reduce((sum: number, field: any) => sum + field.confidence, 0) / matchResult.breakdown.length : 0
                }
              }
            };

            // Group by deal_id for consolidation (ensure string key for consistency)
            const dealIdKey = String(deal.deal_id);
            if (!dealMatches.has(dealIdKey)) {
              dealMatches.set(dealIdKey, []);
            }
            dealMatches.get(dealIdKey)!.push(matchData);
          } else {
            // console.log(`❌ Match score ${matchScore}% below threshold ${minScoreThreshold}%`);
          }
        }
      }
    }

    // Consolidate matches for each deal (similar to contacts-for-deal logic)
    const consolidatedMatches: any[] = [];
    
    for (const [dealId, matches] of dealMatches) {
      if (matches.length === 0) continue;
      
      // Sort matches by score (highest first) to get best score
      matches.sort((a: any, b: any) => b.match_score - a.match_score);
      const bestMatch = matches[0];
      
      // Consolidate all reasons from all criteria
      const allReasons = new Set<string>();
      const allCapitalPositions = new Set<string>();
      const allCriteriaSources = new Set<string>();
      const allInvestmentCriteriaIds = new Set<number>();
      
      matches.forEach((match: any) => {
        if (match.match_reasons) {
          match.match_reasons.forEach((reason: string) => allReasons.add(reason));
        }
        allCapitalPositions.add(match.capital_position);
        allCriteriaSources.add(match.criteria_source);
        allInvestmentCriteriaIds.add(match.investment_criteria_id);
      });

      // Create consolidated deal object
      const consolidatedDeal = {
        ...bestMatch,
        // Use best score as overall score
        match_score: bestMatch.match_score,
        score: bestMatch.match_score, // For compatibility
        // Combine all reasons
        match_reasons: Array.from(allReasons),
        reasons: Array.from(allReasons), // For compatibility
        // Show all capital positions this deal requires
        capital_positions: Array.from(allCapitalPositions),
        // Show all criteria sources
        criteria_sources: Array.from(allCriteriaSources),
        // Show all investment criteria IDs
        investment_criteria_ids: Array.from(allInvestmentCriteriaIds),
        // Add metadata about consolidation
        total_criteria_matched: matches.length,
        matching_criteria_count: matches.length,
        best_score: bestMatch.match_score,
        // Add fallback indicator if using company criteria
        fallback_used: matches.some((match: any) => match.criteria_source === 'Company') && 
                      !matches.some((match: any) => match.criteria_source === 'Contact'),
        criteria_details: matches.map((match: any) => ({
          capital_position: match.capital_position,
          criteria_source: match.criteria_source,
          investment_criteria_id: match.investment_criteria_id,
          individual_score: match.match_score,
          reasons: match.match_reasons || match.reasons
        }))
      };
      
      consolidatedMatches.push(consolidatedDeal);
    }

    // Sort consolidated matches by score (highest first)
    consolidatedMatches.sort((a, b) => b.match_score - a.match_score);

    // console.log(`📊 V2 Matching complete: ${consolidatedMatches.length} consolidated deals found above threshold ${minScoreThreshold}%`);

    return NextResponse.json({
      matches: consolidatedMatches, // No limit since we removed pagination
      total: consolidatedMatches.length,
      entity_id: contactId,
      entity_type: entityType,
      min_score_threshold: minScoreThreshold,
      crm_mode: isCrmMode,
      message: `Found ${consolidatedMatches.length} matching deals for ${entityType} ${contactId}`
    });

  } catch (error) {
    console.error('Error in deals-for-contact V2 API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}