import { pool } from "@/lib/db";

// Types for v2 matching
export interface CapitalPositionFieldWeights {
  [fieldName: string]: number;
}

export interface V2MatchResult {
  score: number;
  reason: string;
  confidence: number;
}

export interface V2DealMatch {
  deal_id: number;
  deal_name: string;
  capital_position: string;
  match_score: number;
  match_breakdown: Array<{
    field: string;
    score: number;
    weight: number;
    reason: string;
    confidence: number;
  }>;
  deal_data: any;
  contact_data: any;
}

// Field name normalization mapping for singular/plural variations
const FIELD_NAME_NORMALIZATION: { [key: string]: string } = {
  'strategy': 'strategy',
  'strategies': 'strategy',
  'property_type': 'property_type',
  'property_types': 'property_type',
  'subproperty_type': 'subproperty_type',
  'subproperty_types': 'subproperty_type',
  'num_apartment_units': 'num_apartment_units',
  'number_of_units': 'num_apartment_units',
  'occupancy_rate': 'occupancy_rate',
  'hold_period': 'hold_period',
  'min_hold_period_years': 'hold_period',
  'max_hold_period_years': 'hold_period'
};

// V2 Weight management functions - Updated for COMMON weights + overrides
export async function fetchCapitalPositionWeights(capitalPosition: string): Promise<CapitalPositionFieldWeights> {
  try {
    // console.log(`🔍 Fetching weights for capital position: ${capitalPosition}`);
    
    // First, get the COMMON weights as the base (these are the defaults)
    const commonQuery = `
      SELECT field_name, weight, table_name, description
      FROM capital_position_field_weights
      WHERE capital_position = 'COMMON' AND is_active = true AND weight > 0
      ORDER BY table_name, weight DESC
    `;
    
    const commonResult = await pool.query(commonQuery);
    const weights: CapitalPositionFieldWeights = {};
    
    if (commonResult.rows.length > 0) {
      // console.log(`✅ Found ${commonResult.rows.length} COMMON weight configurations`);
      for (const row of commonResult.rows) {
        const normalizedFieldName = row.field_name;
        const weight = parseFloat(row.weight);
        
        // Store weight for the normalized field name
        weights[normalizedFieldName] = weight;
        // console.log(`  COMMON ${normalizedFieldName}: ${row.weight} (${(weight * 100).toFixed(1)}%) - ${row.description}`);
        
        // Also store weight for all field name variations that normalize to this field
        for (const [variation, normalized] of Object.entries(FIELD_NAME_NORMALIZATION)) {
          if (normalized === normalizedFieldName && variation !== normalizedFieldName) {
            weights[variation] = weight;
            // console.log(`  COMMON ${variation} (→${normalizedFieldName}): ${row.weight} (${(weight * 100).toFixed(1)}%)`);
          }
        }
      }
    } else {
      // console.log(`⚠️ No COMMON weights found, trying to fetch COMMON weights directly`);
      return await getDefaultCapitalPositionWeights(capitalPosition);
    }
    
    // Then, get any specific overrides for this capital position (if not COMMON)
    if (capitalPosition !== 'COMMON') {
      const specificQuery = `
        SELECT field_name, weight, table_name, description
        FROM capital_position_field_weights
        WHERE capital_position = $1 AND is_active = true AND weight > 0
        ORDER BY table_name, weight DESC
      `;
      
      const specificResult = await pool.query(specificQuery, [capitalPosition]);
      
      if (specificResult.rows.length > 0) {
        // console.log(`✅ Found ${specificResult.rows.length} specific overrides for ${capitalPosition}`);
        for (const row of specificResult.rows) {
          const normalizedFieldName = row.field_name;
          const weight = parseFloat(row.weight);
          
          // Store weight for the normalized field name (override)
          weights[normalizedFieldName] = weight;
          // console.log(`  OVERRIDE ${normalizedFieldName}: ${row.weight} (${(weight * 100).toFixed(1)}%) - ${row.description}`);
          
          // Also store weight for all field name variations that normalize to this field
          for (const [variation, normalized] of Object.entries(FIELD_NAME_NORMALIZATION)) {
            if (normalized === normalizedFieldName && variation !== normalizedFieldName) {
              weights[variation] = weight;
              // console.log(`  OVERRIDE ${variation} (→${normalizedFieldName}): ${row.weight} (${(weight * 100).toFixed(1)}%)`);
            }
          }
        }
      } else {
        // console.log(`📝 No specific overrides for ${capitalPosition}, using COMMON weights only`);
      }
    }
    
    // Calculate total weight for validation
    let totalWeight = 0;
    for (const fieldName in weights) {
      totalWeight += weights[fieldName];
    }
    
    // console.log(`📊 Total weight: ${totalWeight} (${(totalWeight * 100).toFixed(1)}%)`);
    
    if (totalWeight === 0) {
      // console.log(`⚠️ No weights found, trying to fetch COMMON weights as fallback`);
      return await getDefaultCapitalPositionWeights(capitalPosition);
    }
    
    // Log final weights
    // console.log(`📊 Final weights for ${capitalPosition}:`);
    for (const [field, weight] of Object.entries(weights)) {
      // console.log(`  ${field}: ${weight} (${(weight * 100).toFixed(1)}%)`);
    }
    
    return weights;
  } catch (error) {
    // console.error(`❌ Error fetching weights for ${capitalPosition}:`, error);
    // console.log(`🔄 Falling back to COMMON weights for ${capitalPosition}`);
    return await getDefaultCapitalPositionWeights(capitalPosition);
  }
}

export async function getDefaultCapitalPositionWeights(capitalPosition: string): Promise<CapitalPositionFieldWeights> {
  // Always use COMMON weights from database - no hardcoded fallbacks
  // console.log(`🔄 Fetching COMMON weights as fallback for ${capitalPosition}`);
  
  try {
    const commonQuery = `
      SELECT field_name, weight
      FROM capital_position_field_weights
      WHERE capital_position = 'COMMON' AND is_active = true AND weight > 0
      ORDER BY weight DESC
    `;
    
    const result = await pool.query(commonQuery);
    const weights: CapitalPositionFieldWeights = {};
    
    if (result.rows.length > 0) {
      // console.log(`✅ Using ${result.rows.length} COMMON weights as defaults`);
      for (const row of result.rows) {
        weights[row.field_name] = parseFloat(row.weight);
        // console.log(`  COMMON ${row.field_name}: ${row.weight} (${(parseFloat(row.weight) * 100).toFixed(1)}%)`);
      }
      return weights;
    } else {
      // console.log(`❌ No COMMON weights found in database`);
      return {};
    }
  } catch (error) {
    // console.error(`❌ Error fetching COMMON weights:`, error);
    return {};
  }
}

// Helper functions to determine field types for position-aware matching
function isDebtField(fieldName: string): boolean {
  const debtFields = [
    'loan_to_value', 'loan_to_cost', 'loan_dscr', 'interest_rate', 'loan_term',
    'amortization', 'recourse', 'debt_service_coverage', 'loan_type'
  ];
  return debtFields.includes(fieldName);
}

function isEquityField(fieldName: string): boolean {
  const equityFields = [
    'target_return', 'equity_multiple', 'hold_period', 'yield_on_cost',
    'ownership_requirement', 'attachment_point', 'max_leverage_tolerance',
    'cash_on_cash', 'preferred_return'
  ];
  return equityFields.includes(fieldName);
}

// V2 Scoring functions for deal v2 tables
export function calculateV2ArrayMatchScore(dealArray: any, contactArray: any, fieldName: string): V2MatchResult {
  if (!dealArray || !contactArray) {
    return {
      score: 0,
      reason: `No ${fieldName} data available`,
      confidence: 0.5
    };
  }

  const dealValues = Array.isArray(dealArray) ? dealArray : [dealArray];
  const contactValues = Array.isArray(contactArray) ? contactArray : [contactArray];

  if (dealValues.length === 0 || contactValues.length === 0) {
    return {
      score: 0,
      reason: `Empty ${fieldName} arrays`,
      confidence: 0.5
    };
  }

  // Ensure all values are strings and handle nested arrays
  const normalizedDealValues = dealValues
    .flat()
    .filter(value => value != null && value !== '')
    .map(value => String(value).toLowerCase().trim());
    
  const normalizedContactValues = contactValues
    .flat()
    .filter(value => value != null && value !== '')
    .map(value => String(value).toLowerCase().trim());

  if (normalizedDealValues.length === 0 || normalizedContactValues.length === 0) {
    return {
      score: 0,
      reason: `No valid ${fieldName} data after normalization`,
      confidence: 0.5
    };
  }

  const intersection = normalizedDealValues.filter(value => 
    normalizedContactValues.some(contactValue => 
      contactValue === value || 
      contactValue.includes(value) || 
      value.includes(contactValue) ||
      // Special property type mapping
      (fieldName === 'property_type' && (
        (value === 'multi-family' && contactValue === 'multifamily') ||
        (value === 'multifamily' && contactValue === 'multi-family') ||
        (value === 'multi-family' && contactValue === 'multi family') ||
        (value === 'multi family' && contactValue === 'multi-family')
      ))
    )
  );

  if (intersection.length === 0) {
    return {
      score: 0,
      reason: `No ${fieldName} matches found`,
      confidence: 0.8
    };
  }

  // For exact matches (intersection found), prioritize quality over quantity
  // If we have a match, give full score rather than penalizing for array size differences
  const score = intersection.length > 0 ? 1.0 : 0.0;
  
  return {
    score,
    reason: `${fieldName} match: ${intersection.join(', ')}`,
    confidence: 0.9
  };
}

export function calculateV2DealAmountScore(
  dealAmount: number | null,
  contactMinAmount: number | null,
  contactMaxAmount: number | null
): V2MatchResult {
  if (!dealAmount || (!contactMinAmount && !contactMaxAmount)) {
    return {
      score: 0,
      reason: 'Missing deal amount or contact amount preferences',
      confidence: 0.5
    };
  }

  // Convert contact amounts from raw dollars to millions if they're too large
  let contactMin = contactMinAmount || 0;
  let contactMax = contactMaxAmount || contactMin || 0;

  // If contact amounts are in raw dollars (very large numbers), convert to millions
  if (contactMin > 1000000 || contactMax > 1000000) {
    contactMin = contactMin / 1000000;
    contactMax = contactMax / 1000000;
    // console.log(`Converted contact amounts from raw dollars to millions: $${contactMin}M - $${contactMax}M`);
  }

  if (dealAmount === 0 || contactMax === 0) {
    return {
      score: 0,
      reason: 'Invalid amount values',
      confidence: 0.5
    };
  }

  // Check if deal amount falls within contact's range
  if (dealAmount >= contactMin && dealAmount <= contactMax) {
    return {
      score: 1.0,
      reason: `Deal amount $${dealAmount}M within range $${contactMin}M - $${contactMax}M`,
      confidence: 0.95
    };
  }

  // Calculate overlap percentage
  const overlap = Math.max(0, Math.min(dealAmount, contactMax) - Math.max(dealAmount, contactMin));
  const totalRange = Math.max(dealAmount, contactMax) - Math.min(dealAmount, contactMin);
  
  if (totalRange === 0) {
    return {
      score: 0,
      reason: 'No amount overlap',
      confidence: 0.8
    };
  }

  const score = overlap / totalRange;
  
  return {
    score,
    reason: `Deal amount $${dealAmount}M has ${Math.round(score * 100)}% overlap with range $${contactMin}M - $${contactMax}M`,
    confidence: 0.85
  };
}

export function calculateV2RangeMatchScore(
  dealMin: number | null,
  dealMax: number | null,
  contactMin: number | null,
  contactMax: number | null,
  fieldName: string
): V2MatchResult {
  if (!dealMin && !dealMax || !contactMin && !contactMax) {
    return {
      score: 0,
      reason: `Missing ${fieldName} range data`,
      confidence: 0.5
    };
  }

  // Use single values if ranges not provided
  const dealValue = dealMax || dealMin || 0;
  const contactMinVal = contactMin || 0;
  const contactMaxVal = contactMax || contactMinVal || 0;

  if (dealValue === 0 || contactMaxVal === 0) {
    return {
      score: 0,
      reason: `Invalid ${fieldName} values`,
      confidence: 0.5
    };
  }

  // Check if deal value falls within contact's range
  if (dealValue >= contactMinVal && dealValue <= contactMaxVal) {
    return {
      score: 1.0,
      reason: `${fieldName} ${dealValue} within range ${contactMinVal} - ${contactMaxVal}`,
      confidence: 0.95
    };
  }

  // Calculate overlap percentage
  const overlap = Math.max(0, Math.min(dealValue, contactMaxVal) - Math.max(dealValue, contactMinVal));
  const totalRange = Math.max(dealValue, contactMaxVal) - Math.min(dealValue, contactMinVal);
  
  if (totalRange === 0) {
    return {
      score: 0,
      reason: `No ${fieldName} overlap`,
      confidence: 0.8
    };
  }

  const score = overlap / totalRange;
  
  return {
    score,
    reason: `${fieldName} ${dealValue} has ${Math.round(score * 100)}% overlap with range ${contactMinVal} - ${contactMaxVal}`,
    confidence: 0.85
  };
}

export function calculateV2LocationScore(
  dealRegion: string | null,
  dealState: string | null,
  dealCity: string | null,
  contactRegion: string[] | null,
  contactState: string[] | null,
  contactCity: string[] | null
): V2MatchResult {
  // console.log('🗺️ Location matching details:');
  // console.log('Deal:', { region: dealRegion, state: dealState, city: dealCity });
  // console.log('Contact:', { region: contactRegion, state: contactState, city: contactCity });

  // Check city match first (highest priority)
  if (dealCity && contactCity) {
    // console.log('Checking city match...');
    const cityScore = calculateV2ArrayMatchScore([dealCity], contactCity, "city");
    // console.log('City score:', cityScore);
    if (cityScore.score > 0) {
      return {
        score: cityScore.score,
        reason: cityScore.reason,
        confidence: 0.95
      };
    }
  }

  // Check state match
  if (dealState && contactState) {
    // console.log('Checking state match...');
    const stateScore = calculateV2ArrayMatchScore([dealState], contactState, "state");
    // console.log('State score:', stateScore);
    if (stateScore.score > 0) {
      return {
        score: stateScore.score * 0.8, // Slightly lower weight for state vs city
        reason: stateScore.reason,
        confidence: 0.9
      };
    }
  }

  // Check region match
  if (dealRegion && contactRegion) {
    // console.log('Checking region match...');
    const regionScore = calculateV2ArrayMatchScore([dealRegion], contactRegion, "region");
    // console.log('Region score:', regionScore);
    if (regionScore.score > 0) {
      return {
        score: regionScore.score * 0.6, // Lower weight for region
        reason: regionScore.reason,
        confidence: 0.85
      };
    }
  }

  // console.log('No location matches found');
  return {
    score: 0,
    reason: 'No location matches found',
    confidence: 0.8
  };
}

// Comprehensive V2 field scorer - Updated to use central investment criteria tables
export function calculateV2FieldScore(
  dealData: any,
  contactData: any,
  fieldName: string,
  fieldWeight: number,
  capitalPosition?: string
): { field: string; score: number; weight: number; reason: string; confidence: number } | null {
  
  if (fieldWeight <= 0) {
    // console.log(`⏭️ Skipping field '${fieldName}' with weight 0`);
    return null; // Skip fields with 0 weight
  }
  
  // console.log(`🔍 Calculating score for field: ${fieldName}`);
  let result: V2MatchResult;
  
  switch (fieldName) {
    case 'location':
      // console.log('🗺️ LOCATION MATCHING:');
      // console.log('Deal Location (from property table):', {
      //   region: dealData.region,
      //   state: dealData.state,
      //   city: dealData.city,
      //   country: dealData.country,
      //   address: dealData.address
      // });
      
      // Use priority order: ICC > Contact > Company
      const contactRegion = contactData.icc_region || contactData.contact_region || null;
      const contactState = contactData.icc_state || contactData.contact_state || contactData.comp_state || null;
      const contactCity = contactData.icc_city || contactData.contact_city || contactData.comp_city || null;
      const contactCountry = contactData.icc_country || contactData.contact_country || contactData.comp_country || null;
      
      // console.log('Contact Location (Priority Order):', {
      //   region: contactRegion,
      //   state: contactState,
      //   city: contactCity,
      //   country: contactCountry
      // });
      
      // Ensure we pass arrays for contact location data
      const contactRegionArray = contactRegion ? (Array.isArray(contactRegion) ? contactRegion : [contactRegion]) : null;
      const contactStateArray = contactState ? (Array.isArray(contactState) ? contactState : [contactState]) : null;
      const contactCityArray = contactCity ? (Array.isArray(contactCity) ? contactCity : [contactCity]) : null;
      
      result = calculateV2LocationScore(
        dealData.region,
        dealData.state,
        dealData.city,
        contactRegionArray,
        contactStateArray,
        contactCityArray
      );
      
      // console.log('Location match result:', result);
      break;

    case 'deal_amount':
      // console.log('💰 DEAL AMOUNT MATCHING:');
      // console.log(`Looking for NSF field amount for capital position: ${capitalPosition}`);
      
      // Use NSF fields amount where source_type matches the capital position
      let dealAmount = 0;
      
      if (dealData.nsfFields && Array.isArray(dealData.nsfFields) && capitalPosition) {
        // console.log('Available NSF fields:', dealData.nsfFields);
        
        // Find NSF field where source_type or capital_position matches the capital position
        const matchingNsfField = dealData.nsfFields.find((nsf: any) => 
          nsf.source_type === capitalPosition || 
          nsf.capital_position === capitalPosition
        );
        
        if (matchingNsfField && matchingNsfField.amount) {
          dealAmount = Number(matchingNsfField.amount);
          // console.log(`✅ Found matching NSF field for ${capitalPosition}:`, matchingNsfField);
          // console.log(`Deal amount for ${capitalPosition}: $${dealAmount}`);
        } else {
          // console.log(`❌ No matching NSF field found for capital position: ${capitalPosition}`);
          // console.log(`Available NSF fields:`, dealData.nsfFields.map((nsf: any) => ({
          //   source_type: nsf.source_type,
          //   capital_position: nsf.capital_position,
          //   amount: nsf.amount
          // })));
          // Don't use fallback - if no NSF field for this capital position, score should be 0
          dealAmount = 0;
        }
      } else {
        // console.log('❌ Missing NSF fields data or capital position');
        dealAmount = 0;
      }
      
      // Convert to millions if needed (assuming amount is in dollars)
      const dealAmountInMillions = dealAmount / 1000000;
      // console.log(`Deal amount in millions: $${dealAmountInMillions}M`);
      // console.log(`Contact deal size range (raw): $${contactData.minimum_deal_size || 0} - $${contactData.maximum_deal_size || 0}`);
      
      result = calculateV2DealAmountScore(
        dealAmountInMillions,
        contactData.minimum_deal_size,
        contactData.maximum_deal_size
      );
      
      // console.log('Deal amount match result:', result);
      break;



    case 'strategy':
    case 'strategies':
      // console.log('🎯 STRATEGY MATCHING (BINARY):');
      // console.log(`Deal strategy: ${dealData.strategy}`);
      // console.log(`Contact strategies: ${contactData.strategies}`);
      
      // Binary strategy matching: 100% if ANY match, 0% if no match
      let strategyMatch = false;
      
      if (dealData.strategy && contactData.strategies && Array.isArray(contactData.strategies)) {
        // Check if deal strategy matches any contact strategy
        strategyMatch = contactData.strategies.some((contactStrategy: string) => 
          contactStrategy && contactStrategy.toLowerCase() === dealData.strategy.toLowerCase()
        );
        
        // console.log(`Strategy match result: ${strategyMatch ? 'MATCH' : 'NO MATCH'}`);
      } else {
        // console.log('Missing deal strategy or contact strategies data');
      }
      
      result = {
        score: strategyMatch ? 1.0 : 0.0, // 100% or 0%
        reason: strategyMatch 
          ? `Strategy match: ${dealData.strategy} matches contact preferences`
          : `No strategy match: ${dealData.strategy} not in contact preferences`,
        confidence: strategyMatch ? 1.0 : 0.0
      };
      break;

    case 'target_return':
    case 'minimum_internal_rate_of_return':
    case 'target_return_irr_on_equity':
      // Compare deal IRR with contact target return or minimum IRR expectations
      // All IRR/target return fields should be treated the same
      
      // Skip minimum_internal_rate_of_return if target_return field has weight to avoid duplication
      // (This logic will be handled by the database weights - only include fields with non-zero weights)
      
      let dealIrr: number | null = null;
      
      // PRIORITY 1: Use investment criteria IRR if available (deal's projected returns)
      if (dealData.investmentCriteriaEquity && dealData.investmentCriteriaEquity.length > 0) {
        // Find criteria matching the capital position
        const relevantCriteria = dealData.investmentCriteriaEquity.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaEquity[0]; // fallback to first criteria
        
        if (relevantCriteria) {
          if (fieldName === 'target_return' && relevantCriteria.target_return) {
            dealIrr = Number(relevantCriteria.target_return);
            // Convert to decimal if stored as percentage
            if (dealIrr > 1) {
              dealIrr = dealIrr / 100;
            }
            // console.log(`Using deal criteria target_return: ${dealIrr} (${dealIrr * 100}%)`);
          } else if (fieldName === 'minimum_internal_rate_of_return' && relevantCriteria.minimum_internal_rate_of_return) {
            dealIrr = Number(relevantCriteria.minimum_internal_rate_of_return);
            // Convert to decimal if stored as percentage
            if (dealIrr > 1) {
              dealIrr = dealIrr / 100;
            }
            // console.log(`Using deal criteria minimum_internal_rate_of_return: ${dealIrr} (${dealIrr * 100}%)`);
          }
        }
      }
      
      // PRIORITY 2: Fallback to deal table IRR if criteria not available
      if (!dealIrr) {
        if (dealData.total_internal_rate_of_return_irr) {
          dealIrr = dealData.total_internal_rate_of_return_irr;
        } else if (dealData.common_equity_internal_rate_of_return_irr) {
          dealIrr = dealData.common_equity_internal_rate_of_return_irr;
        } else if (dealData.gp_internal_rate_of_return_irr) {
          dealIrr = dealData.gp_internal_rate_of_return_irr;
        } else if (dealData.lp_internal_rate_of_return_irr) {
          dealIrr = dealData.lp_internal_rate_of_return_irr;
        } else if (dealData.preferred_equity_internal_rate_of_return_irr) {
          dealIrr = dealData.preferred_equity_internal_rate_of_return_irr;
        }
        // console.log(`Using deal table IRR fallback: ${dealIrr}`);
      }
      
      // Match corresponding fields properly to avoid duplication
      let contactTargetReturn: number | null = null;
      
      if (fieldName === 'target_return') {
        contactTargetReturn = contactData.target_return;
      } else if (fieldName === 'minimum_internal_rate_of_return') {
        contactTargetReturn = contactData.minimum_internal_rate_of_return;
      } else if (fieldName === 'target_return_irr_on_equity') {
        contactTargetReturn = contactData.target_return_irr_on_equity;
      }
      
      // Fallback to any available IRR field if the specific field is not available
      if (!contactTargetReturn) {
        contactTargetReturn = contactData.target_return || 
                             contactData.minimum_internal_rate_of_return || 
                             contactData.target_return_irr_on_equity;
      }
      
      if (dealIrr && contactTargetReturn) {
        const dealIrrNum = Number(dealIrr);
        let contactTargetNum = Number(contactTargetReturn);
        
        if (!isNaN(dealIrrNum) && !isNaN(contactTargetNum)) {
          // Handle case where target_return might be stored as percentage (e.g., 12.0) instead of decimal (0.12)
          if (contactTargetNum > 1) {
            contactTargetNum = contactTargetNum / 100; // Convert percentage to decimal
          }
          
          // Target return is a MINIMUM - higher IRR is better, not exact match
          let score: number;
          if (dealIrrNum >= contactTargetNum) {
            score = 1.0; // Exceeds target
          } else if (dealIrrNum >= (contactTargetNum * 0.95)) {
            score = 0.9; // Very close (95%+)
          } else if (dealIrrNum >= (contactTargetNum * 0.9)) {
            score = 0.8; // Close (90%+)
          } else if (dealIrrNum >= (contactTargetNum * 0.8)) {
            score = 0.6; // Reasonable (80%+)
          } else if (dealIrrNum >= (contactTargetNum * 0.7)) {
            score = 0.4; // Below expectations (70%+)
          } else {
            score = 0.1; // Significantly below
          }
          
          let status: string;
          if (dealIrrNum >= contactTargetNum) {
            status = "exceeds target";
          } else if (dealIrrNum >= (contactTargetNum * 0.95)) {
            status = "very close to target";
          } else if (dealIrrNum >= (contactTargetNum * 0.9)) {
            status = "close to target";
          } else if (dealIrrNum >= (contactTargetNum * 0.8)) {
            status = "below target";
          } else {
            status = "significantly below target";
          }
          
          result = {
            score,
            reason: `IRR ${(dealIrrNum * 100).toFixed(1)}% ${status} ${(contactTargetNum * 100).toFixed(1)}%`,
            confidence: 0.9
          };
        } else {
          result = {
            score: 0,
            reason: 'Invalid IRR or target return data (not numeric)',
            confidence: 0.3
          };
        }
      } else {
        result = {
          score: 0,
          reason: 'Missing IRR or target return data',
          confidence: 0.5
        };
      }
      break;

    case 'loan_to_value':
      // Compare deal LTV with contact expectations
      let dealLtv: number | null = null;
      
      // PRIORITY 1: Use investment criteria if available
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.loan_to_value_max) {
          dealLtv = Number(relevantCriteria.loan_to_value_max);
          if (dealLtv > 1) dealLtv = dealLtv / 100; // Convert percentage to decimal
        }
      }
      
      // PRIORITY 2: Fallback to deal table
      if (!dealLtv && dealData.loan_to_value_ltv) {
        dealLtv = Number(dealData.loan_to_value_ltv);
        if (dealLtv > 1) dealLtv = dealLtv / 100; // Convert percentage to decimal
      }
      
      if (dealLtv && (contactData.loan_to_value_min || contactData.loan_to_value_max)) {
        result = calculateV2RangeMatchScore(
          dealLtv,
          dealLtv,
          contactData.loan_to_value_min ? Number(contactData.loan_to_value_min) / (contactData.loan_to_value_min > 1 ? 100 : 1) : null,
          contactData.loan_to_value_max ? Number(contactData.loan_to_value_max) / (contactData.loan_to_value_max > 1 ? 100 : 1) : null,
          "LTV"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing LTV data',
          confidence: 0.3
        };
      }
      break;

    case 'loan_to_cost':
      // Compare deal LTC with contact expectations
      let dealLtc: number | null = null;
      
      // PRIORITY 1: Use investment criteria if available
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.loan_to_cost_max) {
          dealLtc = Number(relevantCriteria.loan_to_cost_max);
          if (dealLtc > 1) dealLtc = dealLtc / 100; // Convert percentage to decimal
        }
      }
      
      // PRIORITY 2: Fallback to deal table
      if (!dealLtc && dealData.loan_to_cost_ltc) {
        dealLtc = Number(dealData.loan_to_cost_ltc);
        if (dealLtc > 1) dealLtc = dealLtc / 100; // Convert percentage to decimal
      }
      
      if (dealLtc && (contactData.loan_to_cost_min || contactData.loan_to_cost_max)) {
        result = calculateV2RangeMatchScore(
          dealLtc,
          dealLtc,
          contactData.loan_to_cost_min ? Number(contactData.loan_to_cost_min) / (contactData.loan_to_cost_min > 1 ? 100 : 1) : null,
          contactData.loan_to_cost_max ? Number(contactData.loan_to_cost_max) / (contactData.loan_to_cost_max > 1 ? 100 : 1) : null,
          "LTC"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing LTC data',
          confidence: 0.3
        };
      }
      break;

    case 'interest_rate':
      result = calculateV2RangeMatchScore(
        dealData.interest_rate,
        dealData.interest_rate,
        contactData.loan_interest_rate,
        contactData.loan_interest_rate,
        "Interest Rate"
      );
      break;

    case 'loan_term':
      result = calculateV2RangeMatchScore(
        dealData.loan_term,
        dealData.loan_term,
        contactData.min_loan_term,
        contactData.max_loan_term,
        "Loan Term"
      );
      break;

    case 'loan_dscr':
      result = calculateV2RangeMatchScore(
        dealData.dscr,
        dealData.dscr,
        contactData.min_loan_dscr,
        contactData.max_loan_dscr,
        "DSCR"
      );
      break;

    case 'loan_interest_rate':
    case 'min_loan_interest_rate':
    case 'max_loan_interest_rate':
      // Compare deal interest rate with contact expectations
      let dealInterestRate: number | null = null;
      
      // PRIORITY 1: Use investment criteria if available
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.loan_interest_rate) {
          dealInterestRate = Number(relevantCriteria.loan_interest_rate);
          if (dealInterestRate > 1) dealInterestRate = dealInterestRate / 100; // Convert percentage
        }
      }
      
      // PRIORITY 2: Fallback to deal table
      if (!dealInterestRate && dealData.interest_rate) {
        dealInterestRate = Number(dealData.interest_rate);
        if (dealInterestRate > 1) dealInterestRate = dealInterestRate / 100; // Convert percentage
      }
      
      if (dealInterestRate && (contactData.min_loan_interest_rate || contactData.max_loan_interest_rate)) {
        result = calculateV2RangeMatchScore(
          dealInterestRate,
          dealInterestRate,
          contactData.min_loan_interest_rate ? Number(contactData.min_loan_interest_rate) / (contactData.min_loan_interest_rate > 1 ? 100 : 1) : null,
          contactData.max_loan_interest_rate ? Number(contactData.max_loan_interest_rate) / (contactData.max_loan_interest_rate > 1 ? 100 : 1) : null,
          "Interest Rate"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing interest rate data',
          confidence: 0.3
        };
      }
      break;

    case 'loan_type':
      // Compare loan types
      let dealLoanType: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.loan_type) {
          dealLoanType = relevantCriteria.loan_type;
        }
      }
      
      if (!dealLoanType && dealData.loan_type) {
        dealLoanType = dealData.loan_type;
      }
      
      if (dealLoanType && contactData.loan_type) {
        result = calculateV2ArrayMatchScore(
          [dealLoanType],
          contactData.loan_type,
          "loan_type"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing loan type data',
          confidence: 0.3
        };
      }
      break;

    case 'amortization':
      // Compare amortization schedules
      let dealAmortization: number | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.amortization) {
          dealAmortization = Number(relevantCriteria.amortization);
        }
      }
      
      if (!dealAmortization && dealData.takeout_loan_amortization) {
        dealAmortization = Number(dealData.takeout_loan_amortization);
      }
      
      if (dealAmortization && (contactData.min_amortization || contactData.max_amortization)) {
        result = calculateV2RangeMatchScore(
          dealAmortization,
          dealAmortization,
          contactData.min_amortization,
          contactData.max_amortization,
          "Amortization"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing amortization data',
          confidence: 0.3
        };
      }
      break;

    case 'rate_type':
      // Compare rate types (fixed, floating, etc.)
      let dealRateType: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.rate_type) {
          dealRateType = relevantCriteria.rate_type;
        }
      }
      
      if (dealRateType && contactData.rate_type) {
        result = calculateV2ArrayMatchScore(
          [dealRateType],
          contactData.rate_type,
          "rate_type"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing rate type data',
          confidence: 0.3
        };
      }
      break;

    case 'lien_position':
      // Compare lien positions
      let dealLienPosition: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.lien_position) {
          dealLienPosition = relevantCriteria.lien_position;
        }
      }
      
      if (!dealLienPosition && dealData.lien_position) {
        dealLienPosition = dealData.lien_position;
      }
      
      if (dealLienPosition && contactData.lien_position) {
        result = calculateV2ArrayMatchScore(
          [dealLienPosition],
          contactData.lien_position,
          "lien_position"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing lien position data',
          confidence: 0.3
        };
      }
      break;

    case 'recourse_loan':
      // Compare recourse preferences
      let dealRecourse: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.recourse_loan) {
          dealRecourse = relevantCriteria.recourse_loan;
        }
      }
      
      if (!dealRecourse && dealData.recourse) {
        dealRecourse = dealData.recourse;
      }
      
      if (dealRecourse && contactData.recourse_loan) {
        result = calculateV2ArrayMatchScore(
          [dealRecourse],
          contactData.recourse_loan,
          "recourse"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing recourse data',
          confidence: 0.3
        };
      }
      break;

    case 'loan_origination_min_fee':
    case 'loan_origination_max_fee':
      // Compare loan origination fees
      let dealOriginationFee: number | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria) {
          if (fieldName === 'loan_origination_min_fee' && relevantCriteria.loan_origination_min_fee) {
            dealOriginationFee = Number(relevantCriteria.loan_origination_min_fee);
            if (dealOriginationFee > 1) dealOriginationFee = dealOriginationFee / 100; // Convert percentage to decimal
          } else if (fieldName === 'loan_origination_max_fee' && relevantCriteria.loan_origination_max_fee) {
            dealOriginationFee = Number(relevantCriteria.loan_origination_max_fee);
            if (dealOriginationFee > 1) dealOriginationFee = dealOriginationFee / 100; // Convert percentage to decimal
          }
        }
      }
      
      if (dealOriginationFee && (contactData.loan_origination_min_fee || contactData.loan_origination_max_fee)) {
        result = calculateV2RangeMatchScore(
          dealOriginationFee,
          dealOriginationFee,
          contactData.loan_origination_min_fee ? Number(contactData.loan_origination_min_fee) / (contactData.loan_origination_min_fee > 1 ? 100 : 1) : null,
          contactData.loan_origination_max_fee ? Number(contactData.loan_origination_max_fee) / (contactData.loan_origination_max_fee > 1 ? 100 : 1) : null,
          "Origination Fee"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing origination fee data',
          confidence: 0.3
        };
      }
      break;

    case 'loan_exit_min_fee':
    case 'loan_exit_max_fee':
      // Compare loan exit fees
      let dealExitFee: number | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria) {
          if (fieldName === 'loan_exit_min_fee' && relevantCriteria.loan_exit_min_fee) {
            dealExitFee = Number(relevantCriteria.loan_exit_min_fee);
            if (dealExitFee > 1) dealExitFee = dealExitFee / 100; // Convert percentage to decimal
          } else if (fieldName === 'loan_exit_max_fee' && relevantCriteria.loan_exit_max_fee) {
            dealExitFee = Number(relevantCriteria.loan_exit_max_fee);
            if (dealExitFee > 1) dealExitFee = dealExitFee / 100; // Convert percentage to decimal
          }
        }
      }
      
      if (dealExitFee && (contactData.loan_exit_min_fee || contactData.loan_exit_max_fee)) {
        result = calculateV2RangeMatchScore(
          dealExitFee,
          dealExitFee,
          contactData.loan_exit_min_fee ? Number(contactData.loan_exit_min_fee) / (contactData.loan_exit_min_fee > 1 ? 100 : 1) : null,
          contactData.loan_exit_max_fee ? Number(contactData.loan_exit_max_fee) / (contactData.loan_exit_max_fee > 1 ? 100 : 1) : null,
          "Exit Fee"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing exit fee data',
          confidence: 0.3
        };
      }
      break;

    case 'prepayment':
      // Compare prepayment terms
      let dealPrepayment: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.prepayment) {
          dealPrepayment = relevantCriteria.prepayment;
        }
      }
      
      if (dealPrepayment && contactData.prepayment) {
        result = calculateV2ArrayMatchScore(
          [dealPrepayment],
          contactData.prepayment,
          "prepayment"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing prepayment data',
          confidence: 0.3
        };
      }
      break;

    case 'yield_maintenance':
      // Compare yield maintenance terms
      let dealYieldMaintenance: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.yield_maintenance) {
          dealYieldMaintenance = relevantCriteria.yield_maintenance;
        }
      }
      
      if (dealYieldMaintenance && contactData.yield_maintenance) {
        result = calculateV2ArrayMatchScore(
          [dealYieldMaintenance],
          contactData.yield_maintenance,
          "yield_maintenance"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing yield maintenance data',
          confidence: 0.3
        };
      }
      break;

    case 'rate_lock':
      // Compare rate lock terms
      let dealRateLock: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.rate_lock) {
          dealRateLock = relevantCriteria.rate_lock;
        }
      }
      
      if (dealRateLock && contactData.rate_lock) {
        result = calculateV2ArrayMatchScore(
          [dealRateLock],
          contactData.rate_lock,
          "rate_lock"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing rate lock data',
          confidence: 0.3
        };
      }
      break;

    case 'loan_program':
      // Compare loan programs
      let dealLoanProgram: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.loan_program) {
          dealLoanProgram = relevantCriteria.loan_program;
        }
      }
      
      if (dealLoanProgram && contactData.loan_program) {
        result = calculateV2ArrayMatchScore(
          [dealLoanProgram],
          contactData.loan_program,
          "loan_program"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing loan program data',
          confidence: 0.3
        };
      }
      break;

    case 'good_faith_deposit':
      // Compare good faith deposit requirements
      let dealGoodFaithDeposit: string | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.good_faith_deposit) {
          dealGoodFaithDeposit = relevantCriteria.good_faith_deposit;
        }
      }
      
      if (dealGoodFaithDeposit && contactData.good_faith_deposit) {
        result = calculateV2ArrayMatchScore(
          [dealGoodFaithDeposit],
          contactData.good_faith_deposit,
          "good_faith_deposit"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing good faith deposit data',
          confidence: 0.3
        };
      }
      break;

    case 'loan_min_debt_yield':
      // Compare minimum debt yield requirements
      let dealMinDebtYield: number | null = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria.loan_min_debt_yield) {
          dealMinDebtYield = Number(relevantCriteria.loan_min_debt_yield);
        }
      }
      
      if (!dealMinDebtYield && dealData.takeout_loan_debt_yield) {
        dealMinDebtYield = Number(dealData.takeout_loan_debt_yield);
      }
      
      if (dealMinDebtYield && contactData.loan_min_debt_yield) {
        // Minimum debt yield - higher is better
        const score = dealMinDebtYield >= Number(contactData.loan_min_debt_yield) ? 1.0 : 
                     dealMinDebtYield >= (Number(contactData.loan_min_debt_yield) * 0.9) ? 0.8 : 
                     dealMinDebtYield >= (Number(contactData.loan_min_debt_yield) * 0.8) ? 0.6 : 0.2;
        
        result = {
          score,
          reason: `Debt yield ${dealMinDebtYield.toFixed(2)}% vs minimum ${Number(contactData.loan_min_debt_yield).toFixed(2)}%`,
          confidence: 0.9
        };
      } else {
        result = {
          score: 0,
          reason: 'Missing debt yield data',
          confidence: 0.3
        };
      }
      break;

    case 'occupancy_requirements':
    case 'eligible_borrower':
    case 'structured_loan_tranche':
    case 'future_facilities':
    case 'closing_time':
    case 'application_deposit':
    case 'debt_program_overview':
      // Handle text/boolean debt criteria fields
      let dealDebtFieldValue: any = null;
      
      if (dealData.investmentCriteriaDebt && dealData.investmentCriteriaDebt.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaDebt.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaDebt[0];
        
        if (relevantCriteria && relevantCriteria[fieldName]) {
          dealDebtFieldValue = relevantCriteria[fieldName];
        }
      }
      
      // Some fields might have fallbacks to deal table
      if (!dealDebtFieldValue) {
        if (fieldName === 'structured_loan_tranche' && dealData.structured_lien_tranche) {
          dealDebtFieldValue = dealData.structured_lien_tranche;
        } else if (fieldName === 'closing_time' && dealData.closing_time) {
          dealDebtFieldValue = dealData.closing_time;
        }
      }
      
      const contactDebtFieldValue = contactData[fieldName];
      
      if (dealDebtFieldValue && contactDebtFieldValue) {
        if (typeof contactDebtFieldValue === 'object' && Array.isArray(contactDebtFieldValue)) {
          // Array matching for categorical fields
          result = calculateV2ArrayMatchScore(
            [dealDebtFieldValue],
            contactDebtFieldValue,
            fieldName
          );
        } else if (typeof dealDebtFieldValue === 'string' && typeof contactDebtFieldValue === 'string') {
          // String comparison
          const match = dealDebtFieldValue.toLowerCase() === contactDebtFieldValue.toLowerCase();
          result = {
            score: match ? 1.0 : 0.0,
            reason: match ? `${fieldName} match: ${dealDebtFieldValue}` : `${fieldName} mismatch: ${dealDebtFieldValue} vs ${contactDebtFieldValue}`,
            confidence: 0.8
          };
        } else if (typeof dealDebtFieldValue === 'boolean' && typeof contactDebtFieldValue === 'boolean') {
          // Boolean comparison
          const match = dealDebtFieldValue === contactDebtFieldValue;
          result = {
            score: match ? 1.0 : 0.0,
            reason: match ? `${fieldName} match: ${dealDebtFieldValue}` : `${fieldName} mismatch: ${dealDebtFieldValue} vs ${contactDebtFieldValue}`,
            confidence: 0.9
          };
        } else if (typeof dealDebtFieldValue === 'number' && typeof contactDebtFieldValue === 'number') {
          // Numeric comparison for closing_time
          const diff = Math.abs(dealDebtFieldValue - contactDebtFieldValue) / Math.max(dealDebtFieldValue, contactDebtFieldValue);
          const score = Math.max(0, 1 - diff);
          result = {
            score,
            reason: `${fieldName}: ${dealDebtFieldValue} vs ${contactDebtFieldValue}`,
            confidence: 0.8
          };
        } else {
          result = {
            score: 0,
            reason: `${fieldName} type mismatch`,
            confidence: 0.3
          };
        }
      } else {
        result = {
          score: 0,
          reason: `Missing ${fieldName} data`,
          confidence: 0.3
        };
      }
      break;

    case 'hold_period':
      // PRIORITY 1: Use investment criteria hold period if available
      let dealHoldPeriodMin: number | null = null;
      let dealHoldPeriodMax: number | null = null;
      
      if (dealData.investmentCriteriaEquity && dealData.investmentCriteriaEquity.length > 0) {
        // Find criteria matching the capital position
        const relevantCriteria = dealData.investmentCriteriaEquity.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaEquity[0]; // fallback to first criteria
        
        if (relevantCriteria) {
          dealHoldPeriodMin = relevantCriteria.min_hold_period_years;
          dealHoldPeriodMax = relevantCriteria.max_hold_period_years;
          // console.log(`Using deal criteria hold period: ${dealHoldPeriodMin}-${dealHoldPeriodMax} years`);
        }
      }
      
      // PRIORITY 2: Fallback to deal table hold period
      if (!dealHoldPeriodMin && !dealHoldPeriodMax) {
        dealHoldPeriodMin = dealData.hold_period;
        dealHoldPeriodMax = dealData.hold_period;
        // console.log(`Using deal table hold period fallback: ${dealData.hold_period} years`);
      }
      
      result = calculateV2RangeMatchScore(
        dealHoldPeriodMin,
        dealHoldPeriodMax,
        contactData.min_hold_period_years,
        contactData.max_hold_period_years,
        "Hold Period"
      );
      break;

    case 'yield_on_cost':
    case 'minimum_yield_on_cost':
      // Compare deal yield on cost with contact expectations
      // Use both yield_on_cost and minimum_yield_on_cost fields for comparison but only show one result
      let dealYieldOnCost: number | null = null;
      
      // PRIORITY 1: Use investment criteria if available
      if (dealData.investmentCriteriaEquity && dealData.investmentCriteriaEquity.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaEquity.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaEquity[0];
        
        if (relevantCriteria) {
          // Try minimum_yield_on_cost first, then yield_on_cost
          if (relevantCriteria.minimum_yield_on_cost) {
            const yieldValue = Number(relevantCriteria.minimum_yield_on_cost);
            dealYieldOnCost = yieldValue > 1 ? yieldValue / 100 : yieldValue;
            // console.log(`Using deal criteria minimum_yield_on_cost: ${dealYieldOnCost} (${dealYieldOnCost * 100}%)`);
          } else if (relevantCriteria.yield_on_cost) {
            const yieldValue = Number(relevantCriteria.yield_on_cost);
            dealYieldOnCost = yieldValue > 1 ? yieldValue / 100 : yieldValue;
            // console.log(`Using deal criteria yield_on_cost: ${dealYieldOnCost} (${dealYieldOnCost * 100}%)`);
          }
        }
      }
      
      // PRIORITY 2: Fallback to deal table yield_on_cost
      if (!dealYieldOnCost && dealData.yield_on_cost) {
        const yieldValue = Number(dealData.yield_on_cost);
        dealYieldOnCost = yieldValue > 1 ? yieldValue / 100 : yieldValue;
        // console.log(`Using deal table yield_on_cost fallback: ${dealYieldOnCost} (${dealYieldOnCost * 100}%)`);
      }
      
      // Check both yield_on_cost and minimum_yield_on_cost in contact data (use both for comparison)
      const contactYieldExpectation = contactData.minimum_yield_on_cost || 
                                     contactData.yield_on_cost || 
                                     contactData.target_return;
      
      if (dealYieldOnCost && contactYieldExpectation) {
        let contactTargetNum = Number(contactYieldExpectation);
        
        if (!isNaN(contactTargetNum)) {
          // Handle case where target might be stored as percentage
          if (contactTargetNum > 1) {
            contactTargetNum = contactTargetNum / 100;
          }
          
          // Yield on cost is a MINIMUM - higher is better
          const score = dealYieldOnCost >= contactTargetNum ? 1.0 : 
                       dealYieldOnCost >= (contactTargetNum * 0.8) ? 0.5 : 0.0;
          
          const status = dealYieldOnCost >= contactTargetNum ? "exceeds target" : "below target";
          result = {
            score,
            reason: `Yield on cost ${(dealYieldOnCost * 100).toFixed(1)}% ${status} ${(contactTargetNum * 100).toFixed(1)}%`,
            confidence: 0.9
          };
        } else {
          result = {
            score: 0,
            reason: 'Invalid yield on cost data (not numeric)',
            confidence: 0.3
          };
        }
      } else {
        result = {
          score: 0,
          reason: 'Missing yield on cost data',
          confidence: 0.5
        };
      }
      break;

    case 'minimum_equity_multiple':
      // Compare deal equity multiple with contact minimum equity multiple expectations
      // Use both equity_multiple and minimum_equity_multiple fields for comparison but only show one result
      let dealEquityMultiple: number | null = null;
      
      if (dealData.total_equity_multiple) {
        dealEquityMultiple = dealData.total_equity_multiple;
      } else if (dealData.common_equity_equity_multiple) {
        dealEquityMultiple = dealData.common_equity_equity_multiple;
      } else if (dealData.gp_equity_multiple) {
        dealEquityMultiple = dealData.gp_equity_multiple;
      } else if (dealData.lp_equity_multiple) {
        dealEquityMultiple = dealData.lp_equity_multiple;
      } else if (dealData.preferred_equity_equity_multiple) {
        dealEquityMultiple = dealData.preferred_equity_equity_multiple;
      }
      
      // Check both equity_multiple and minimum_equity_multiple in contact data (use both for comparison)
      const contactMinMultiple = contactData.minimum_equity_multiple || contactData.equity_multiple;
      
      if (dealEquityMultiple !== null && contactMinMultiple) {
        // Convert to numbers to ensure we can perform calculations
        const dealMultiple = Number(dealEquityMultiple);
        const contactMinMultipleNum = Number(contactMinMultiple);
        
        if (!isNaN(dealMultiple) && !isNaN(contactMinMultipleNum)) {
          // Simple scoring based on whether multiple meets minimum requirement
          const score = dealMultiple >= contactMinMultipleNum ? 1.0 : 0.5;
          result = {
            score,
            reason: `Equity multiple ${dealMultiple.toFixed(2)}x vs minimum ${contactMinMultipleNum.toFixed(2)}x`,
            confidence: 0.8
          };
        } else {
          result = {
            score: 0,
            reason: 'Invalid equity multiple or minimum requirement data (not numeric)',
            confidence: 0.3
          };
        }
      } else {
        result = {
          score: 0,
          reason: 'Missing equity multiple or minimum requirement data',
          confidence: 0.5
        };
      }
      break;

    case 'target_cash_on_cash_min':
      // Compare deal cash on cash with contact target
      let dealCashOnCash: number | null = null;
      
      // PRIORITY 1: Use investment criteria if available
      if (dealData.investmentCriteriaEquity && dealData.investmentCriteriaEquity.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaEquity.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaEquity[0];
        
        if (relevantCriteria && relevantCriteria.target_cash_on_cash_min) {
          const cashOnCashValue = Number(relevantCriteria.target_cash_on_cash_min);
          // Convert to decimal if stored as percentage
          dealCashOnCash = cashOnCashValue > 1 ? cashOnCashValue / 100 : cashOnCashValue;
          // console.log(`Using deal criteria target_cash_on_cash_min: ${dealCashOnCash} (${dealCashOnCash * 100}%)`);
        }
      }
      
      // No fallback - cash on cash is a specific metric that shouldn't be proxied
      
      if (dealCashOnCash && contactData.target_cash_on_cash_min) {
        let contactTarget = Number(contactData.target_cash_on_cash_min);
        if (contactTarget > 1) {
          contactTarget = contactTarget / 100;
        }
        
        // Target cash on cash is a MINIMUM - higher is better
        const score = dealCashOnCash >= contactTarget ? 1.0 : 
                     dealCashOnCash >= (contactTarget * 0.8) ? 0.5 : 0.0;
        
        const status = dealCashOnCash >= contactTarget ? "exceeds target" : "below target";
        result = {
          score,
          reason: `Cash on Cash ${(dealCashOnCash * 100).toFixed(1)}% ${status} ${(contactTarget * 100).toFixed(1)}%`,
          confidence: 0.8
        };
      } else {
        result = {
          score: 0,
          reason: 'Missing cash on cash data',
          confidence: 0.5
        };
      }
      break;

    case 'proof_of_funds_requirement':
      // Compare proof of funds requirements
      let dealRequiresProofOfFunds = false;
      
      // PRIORITY 1: Use investment criteria if available
      if (dealData.investmentCriteriaEquity && dealData.investmentCriteriaEquity.length > 0) {
        const relevantCriteria = dealData.investmentCriteriaEquity.find((criteria: any) => 
          criteria.capital_position === capitalPosition
        ) || dealData.investmentCriteriaEquity[0];
        
        if (relevantCriteria && relevantCriteria.proof_of_funds_requirement !== null) {
          dealRequiresProofOfFunds = Boolean(relevantCriteria.proof_of_funds_requirement);
          // console.log(`Using deal criteria proof_of_funds_requirement: ${dealRequiresProofOfFunds}`);
        }
      }
      
      if (contactData.proof_of_funds_requirement !== null && contactData.proof_of_funds_requirement !== undefined) {
        const contactHasProofOfFunds = Boolean(contactData.proof_of_funds_requirement);
        
        // If deal requires proof of funds, contact should have it
        // If deal doesn't require it, doesn't matter if contact has it
        const score = dealRequiresProofOfFunds ? 
                     (contactHasProofOfFunds ? 1.0 : 0.0) : 1.0; // No requirement = perfect match
        
        const dealStatus = dealRequiresProofOfFunds ? "required" : "not required";
        const contactStatus = contactHasProofOfFunds ? "available" : "not available";
        
        result = {
          score,
          reason: `Proof of funds ${dealStatus}, contact has ${contactStatus}`,
          confidence: 0.9
        };
      } else {
        result = {
          score: 0,
          reason: 'Missing proof of funds data',
          confidence: 0.3
        };
      }
      break;

    case 'exit_cap_rate':
      // Compare deal exit cap rate with contact expectations
      if (dealData.exit_cap_rate && contactData.target_return) {
        // Lower cap rates typically mean higher property values and potentially better returns
        const expectedCapRate = 0.06; // 6% is a reasonable benchmark
        const diff = Math.abs(dealData.exit_cap_rate - expectedCapRate);
        const score = Math.max(0, 1 - (diff / expectedCapRate));
        result = {
          score,
          reason: `Exit cap rate ${(dealData.exit_cap_rate * 100).toFixed(1)}% vs expected ${(expectedCapRate * 100).toFixed(1)}%`,
          confidence: 0.8
        };
      } else {
        result = {
          score: 0,
          reason: 'Missing exit cap rate data',
          confidence: 0.5
        };
      }
      break;

    case 'deal_stage':
      // Compare deal stage with contact investment preferences
      if (dealData.deal_stage && contactData.deal_stages) {
        result = calculateV2ArrayMatchScore(
          [dealData.deal_stage],
          contactData.deal_stages,
          "deal_stage"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing deal stage or deal stage preferences',
          confidence: 0.5
        };
      }
      break;

    case 'property_size':
      // Compare property size (NSF) with contact preferences
      if (dealData.property?.total_nsf_net_square_foot && contactData.property_size_min && contactData.property_size_max) {
        const propertySizeInKSF = dealData.property.total_nsf_net_square_foot / 1000; // Convert to thousands of square feet
        result = calculateV2RangeMatchScore(
          propertySizeInKSF,
          propertySizeInKSF,
          contactData.property_size_min,
          contactData.property_size_max,
          "Property Size (KSF)"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing property size data or preferences',
          confidence: 0.5
        };
      }
      break;

    default:
      // Dynamic field matching for ALL other fields from weights table
      // console.log(`🔄 DYNAMIC FIELD MATCHING for: ${fieldName}`);
      
      // Get the field value from deal data
      // Special handling for fields that map to different data sources
      let dealFieldValue = dealData[fieldName];
      
      // Map database field names to actual deal data field names
      if (fieldName === 'num_apartment_units' && !dealFieldValue) {
        dealFieldValue = dealData.number_of_units; // Use property.number_of_units
        // console.log(`Mapped num_apartment_units to number_of_units: ${dealFieldValue}`);
      }
      
      // Map property_type variations - Condo should match Multi-Family
      if (fieldName === 'property_type' && dealFieldValue) {
        // Normalize property types for better matching
        const normalizedType = dealFieldValue.toLowerCase();
        if (normalizedType === 'condo' || normalizedType === 'condominium') {
          dealFieldValue = 'Multi-Family'; // Map condo to multi-family for matching
          // console.log(`Mapped property_type 'Condo' to 'Multi-Family' for matching`);
        }
      }
      
      // Map strategy variations for better matching
      if (fieldName === 'strategy' && dealFieldValue) {
        const normalizedStrategy = dealFieldValue.toLowerCase();
        if (normalizedStrategy === 'adaptive reuse') {
          dealFieldValue = 'Opportunistic'; // Map adaptive reuse to opportunistic
          // console.log(`Mapped strategy 'Adaptive Reuse' to 'Opportunistic' for matching`);
        }
      }
      
      // console.log(`Deal ${fieldName}:`, dealFieldValue);
      
      // Get the corresponding contact field value
      let contactFieldValue = null;
      let contactFieldName = fieldName;
      
      // Map field names to their contact equivalents based on the weights table structure
      const fieldMapping: { [key: string]: string } = {
        // Deal fields
        'property_type': 'property_types',
        'subproperty_type': 'property_types',
        'num_apartment_units': 'number_of_units',
        'occupancy_rate': 'occupancy_rate',
        'hold_period': 'min_hold_period_years',
        'strategy': 'strategies',
        
        // Property fields
        'floor_area_ratio': 'floor_area_ratio',
        
        // Debt fields - map to contact field names
        'loan_interest_rate': 'loan_interest_rate',
        'loan_to_cost_min': 'loan_to_cost_min',
        'loan_to_cost_max': 'loan_to_cost_max',
        'loan_to_value_min': 'loan_to_value_min',
        'loan_to_value_max': 'loan_to_value_max',
        'min_loan_term': 'min_loan_term',
        'max_loan_term': 'max_loan_term',
        'loan_type': 'loan_type',
        'amortization': 'amortization',
        'min_loan_dscr': 'min_loan_dscr',
        'max_loan_dscr': 'max_loan_dscr',
        'recourse_loan': 'recourse_loan',
        'lien_position': 'lien_position',
        'rate_type': 'rate_type',
        // Additional debt fields previously missing
        'yield_maintenance': 'yield_maintenance',
        'loan_program': 'loan_program',
        'loan_origination_min_fee': 'loan_origination_min_fee',
        'loan_origination_max_fee': 'loan_origination_max_fee',
        'loan_exit_min_fee': 'loan_exit_min_fee',
        'loan_exit_max_fee': 'loan_exit_max_fee',
        'prepayment': 'prepayment',
        'rate_lock': 'rate_lock',
        'good_faith_deposit': 'good_faith_deposit',
        'application_deposit': 'application_deposit',
        'closing_time': 'closing_time',
        'loan_min_debt_yield': 'loan_min_debt_yield',
        'occupancy_requirements': 'occupancy_requirements',
        'eligible_borrower': 'eligible_borrower',
        'structured_loan_tranche': 'structured_loan_tranche',
        'future_facilities': 'future_facilities',
        'debt_program_overview': 'debt_program_overview',
        
        // Equity fields - map to contact field names
        'target_return': 'target_return',
        'equity_multiple': 'equity_multiple',
        'minimum_equity_multiple': 'minimum_equity_multiple',
        'minimum_internal_rate_of_return': 'minimum_internal_rate_of_return',
        'target_return_irr_on_equity': 'target_return_irr_on_equity',
        'min_hold_period_years': 'min_hold_period_years',
        'max_hold_period_years': 'max_hold_period_years',
        'yield_on_cost': 'yield_on_cost',
        'minimum_yield_on_cost': 'minimum_yield_on_cost',
        'target_cash_on_cash_min': 'target_cash_on_cash_min',
        'position_specific_equity_multiple': 'position_specific_equity_multiple',
        'position_specific_irr': 'position_specific_irr',
        'attachment_point': 'attachment_point',
        'max_leverage_tolerance': 'max_leverage_tolerance',
        'ownership_requirement': 'ownership_requirement',
        'proof_of_funds_requirement': 'proof_of_funds_requirement',
        // Additional equity fields
        'typical_closing_timeline_days': 'typical_closing_timeline_days',
        'equity_program_overview': 'equity_program_overview'
      };
      
      if (fieldMapping[fieldName]) {
        contactFieldName = fieldMapping[fieldName];
        contactFieldValue = contactData[contactFieldName];
        // console.log(`Mapped contact field: ${fieldName} → ${contactFieldName}`);
      } else {
        contactFieldValue = contactData[fieldName];
        // console.log(`Direct contact field: ${fieldName}`);
      }
      
      // console.log(`Contact ${contactFieldName}:`, contactFieldValue);
      
      // Determine the type of field and calculate score accordingly
      if (Array.isArray(contactFieldValue)) {
        // Array field (like property_types, strategies)
        // For property_type, ensure we handle normalized values
        let dealValueForMatching = dealFieldValue;
        if (fieldName === 'property_type' && dealValueForMatching) {
          // Use the already normalized value from our mapping above
          dealValueForMatching = dealFieldValue; // Already mapped Condo → Multi-Family
        }
        
        result = calculateV2ArrayMatchScore(
          dealValueForMatching ? [dealValueForMatching] : null,
          contactFieldValue,
          fieldName
        );
      } else if (typeof contactFieldValue === 'object' && contactFieldValue !== null && 
                 ((contactFieldValue as any).min !== undefined || (contactFieldValue as any).max !== undefined)) {
        // Range field (like min/max values)
        const rangeValue = contactFieldValue as { min?: number; max?: number; minimum?: number; maximum?: number };
        const min = rangeValue.min || rangeValue.minimum || null;
        const max = rangeValue.max || rangeValue.maximum || null;
        result = calculateV2RangeMatchScore(
          dealFieldValue,
          dealFieldValue,
          min,
          max,
          fieldName
        );
      } else if ((dealFieldValue !== null && dealFieldValue !== undefined) && 
                 (contactFieldValue !== null && contactFieldValue !== undefined)) {
        const dealNum = Number(dealFieldValue);
        const contactNum = Number(contactFieldValue);
        
        if (!isNaN(dealNum) && !isNaN(contactNum)) {
          // Numeric comparison
          const diff = Math.abs(dealNum - contactNum);
          const maxValue = Math.max(Math.abs(dealNum), Math.abs(contactNum), 1);
          const score = Math.max(0, 1 - (diff / maxValue));
          result = {
            score,
            reason: `${fieldName}: ${dealNum} vs ${contactNum}`,
            confidence: 0.8
          };
        } else if (typeof dealFieldValue === 'boolean' && typeof contactFieldValue === 'boolean') {
          // Boolean comparison
          const score = dealFieldValue === contactFieldValue ? 1.0 : 0.0;
          result = {
            score,
            reason: `${fieldName}: ${dealFieldValue} vs ${contactFieldValue}`,
            confidence: 0.9
          };
        } else {
          // String comparison
          const normalizedDeal = String(dealFieldValue).toLowerCase().trim();
          const normalizedContact = String(contactFieldValue).toLowerCase().trim();
          const score = normalizedDeal === normalizedContact ? 1.0 : 0.5;
          result = {
            score,
            reason: `${fieldName}: ${dealFieldValue} vs ${contactFieldValue}`,
            confidence: 0.7
          };
        }
      } else {
        // Missing data
        result = {
          score: 0,
          reason: `Missing ${fieldName} data`,
          confidence: 0.3
        };
      }
      
      // console.log(`Dynamic field match result for ${fieldName}:`, result);
      break;
  }

  return {
    field: fieldName,
    score: result.score,
    weight: fieldWeight,
    reason: result.reason,
    confidence: result.confidence
  };
}

// Main V2 matching function with cached weights
export function calculateV2MatchScoreWithWeights(
  dealData: any,
  contactData: any,
  capitalPosition: string,
  weights: CapitalPositionFieldWeights,
  positionType?: string // 'debt', 'equity', or 'unknown'
): {
  totalScore: number;
  breakdown: Array<{
    field: string;
    score: number;
    weight: number;
    reason: string;
    confidence: number;
  }>;
  reasons: string[];
} {
  // console.log('🚀 STARTING V2 MATCH SCORE CALCULATION (WITH CACHED WEIGHTS)');
  // console.log('📊 DEAL DATA:', {
  //   deal_id: dealData.deal_id,
  //   deal_name: dealData.deal_name,
  //   ask_capital_position: dealData.ask_capital_position,
  //   strategy: dealData.strategy,
  //   property_type: dealData.property_type,
  //   nsfFields_count: dealData.nsfFields ? dealData.nsfFields.length : 0
  // });
  // console.log('👤 CONTACT DATA:', {
  //   contact_id: contactData.contact_id,
  //   name: `${contactData.first_name || ''} ${contactData.last_name || ''}`.trim(),
  //   company: contactData.company_name,
  //   capital_position: contactData.capital_position,
  //   criteria_source: contactData.criteria_source,
  //   investment_criteria_id: contactData.investment_criteria_id
  // });
  // console.log('🎯 CAPITAL POSITION:', capitalPosition);
  // console.log('⚖️ USING CACHED WEIGHTS:', weights);
  
  const breakdown: Array<{
    field: string;
    score: number;
    weight: number;
    reason: string;
    confidence: number;
  }> = [];
  
  const reasons: string[] = [];
  let totalScore = 0;
  let totalWeight = 0;

  // Calculate scores for each field with position-aware matching
  // Only use fields with non-zero weights (as requested by user)
  for (const [fieldName, weight] of Object.entries(weights)) {
    if (weight <= 0) {
      // console.log(`⏭️ Skipping field '${fieldName}' with zero weight: ${weight}`);
      continue;
    }
    
    // console.log(`🔍 Evaluating field: ${fieldName} with weight: ${weight} (${(weight * 100).toFixed(1)}%)`);
    
    // Determine if this is a debt or equity field based on table_name in database
    const isDebtField = [
      'loan_interest_rate', 'loan_to_cost_min', 'loan_to_cost_max', 'loan_to_value_min', 'loan_to_value_max', 
      'min_loan_term', 'max_loan_term', 'loan_type', 'amortization', 'min_loan_dscr', 'max_loan_dscr', 
      'recourse_loan', 'lien_position', 'rate_type', 'yield_maintenance', 'loan_program',
      'loan_origination_min_fee', 'loan_origination_max_fee', 'loan_exit_min_fee', 'loan_exit_max_fee',
      'prepayment', 'rate_lock', 'good_faith_deposit', 'application_deposit', 'closing_time',
      'loan_min_debt_yield', 'occupancy_requirements', 'eligible_borrower', 'structured_loan_tranche',
      'future_facilities', 'debt_program_overview'
    ].includes(fieldName);
    
    const isEquityField = [
      'target_return', 'equity_multiple', 'minimum_equity_multiple', 'minimum_internal_rate_of_return',
      'target_return_irr_on_equity', 'min_hold_period_years', 'max_hold_period_years', 'yield_on_cost',
      'minimum_yield_on_cost', 'target_cash_on_cash_min', 'position_specific_equity_multiple',
      'position_specific_irr', 'attachment_point', 'max_leverage_tolerance', 'ownership_requirement',
      'proof_of_funds_requirement', 'typical_closing_timeline_days', 'equity_program_overview'
    ].includes(fieldName);
    
    // Skip fields that don't match the position type for better accuracy
    if (positionType && positionType !== 'unknown') {
      if (positionType === 'debt' && isEquityField) {
        // console.log(`⏭️ Skipping equity field '${fieldName}' for debt position`);
        continue;
      }
      if (positionType === 'equity' && isDebtField) {
        // console.log(`⏭️ Skipping debt field '${fieldName}' for equity position`);
        continue;
      }
    }
    
    const fieldResult = calculateV2FieldScore(dealData, contactData, fieldName, weight, capitalPosition);
    
    if (fieldResult) {
      // console.log(`✅ Field ${fieldName}: Score=${fieldResult.score} (${(fieldResult.score * 100).toFixed(1)}%), Weight=${fieldResult.weight} (${(fieldResult.weight * 100).toFixed(1)}%), Reason="${fieldResult.reason}"`);
      breakdown.push(fieldResult);
      totalScore += fieldResult.score * fieldResult.weight;
      totalWeight += fieldResult.weight;
      
      if (fieldResult.score > 0) {
        reasons.push(fieldResult.reason);
      }
    } else {
      // console.log(`❌ Field ${fieldName}: No result returned (likely zero weight or field not found)`);
    }
  }

  // With 100% normalized weights, use direct weighted sum
  // totalScore is already the sum of (fieldScore × fieldWeight)
  // Since weights now properly add up to 100%, this represents the final percentage
  const finalScore = totalScore;

  // console.log(`📊 FINAL SCORING SUMMARY:`);
  // console.log(`Total Score: ${totalScore} (${Math.round(totalScore * 100)}%)`);
  // console.log(`Total Weight: ${totalWeight}`);
  // console.log(`Fields Evaluated: ${breakdown.length}`);
  // console.log(`Fields with Matches: ${breakdown.filter(f => f.score > 0).length}`);
  // console.log(`Reasons: ${reasons.length}`);
  
  // Debug: Show each field's contribution to total score
  // console.log(`📊 Field Contributions to Total Score:`);
  let cumulativeScore = 0;
  breakdown.forEach(field => {
    const contribution = field.score * field.weight;
    cumulativeScore += contribution;
    // console.log(`  ${field.field}: ${(field.score * 100).toFixed(1)}% × ${(field.weight * 100).toFixed(1)}% = ${(contribution * 100).toFixed(2)}% (cumulative: ${(cumulativeScore * 100).toFixed(2)}%)`);
  });
  
  // console.log(`📊 CALCULATION CHECK:`);
  // console.log(`  Sum of all contributions: ${(cumulativeScore * 100).toFixed(2)}%`);
  // console.log(`  Total weight used: ${(totalWeight * 100).toFixed(2)}%`);
  // console.log(`  Raw weighted score: ${(cumulativeScore * 100).toFixed(2)}%`);
  // console.log(`  Normalized score (if applied): ${totalWeight > 0 ? ((cumulativeScore / totalWeight) * 100).toFixed(2) : 'N/A'}%`);

  return {
    totalScore: finalScore,
    breakdown,
    reasons
  };
}

// Main V2 matching function (async wrapper for backward compatibility)
export async function calculateV2MatchScore(
  dealData: any,
  contactData: any,
  capitalPosition: string,
  positionType?: string // 'debt', 'equity', or 'unknown'
): Promise<{
  totalScore: number;
  breakdown: Array<{
    field: string;
    score: number;
    weight: number;
    reason: string;
    confidence: number;
  }>;
  reasons: string[];
}> {
  // Get weights for this capital position
  const weights = await fetchCapitalPositionWeights(capitalPosition);
  
  // Use the cached version
  return calculateV2MatchScoreWithWeights(dealData, contactData, capitalPosition, weights, positionType);
}
