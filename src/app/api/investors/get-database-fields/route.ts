import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface DatabaseFieldsResponse {
  success: boolean
  companies?: string[]
  contacts?: string[]
  error?: string
}

export async function GET(): Promise<NextResponse<DatabaseFieldsResponse>> {
  try {
    // Fetch companies table columns
    const companiesQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'companies' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `

    // Fetch contacts table columns
    const contactsQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'contacts' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `

    const investmentCriteriaCentralQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'investment_criteria_central' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `

      const investmentCriteriaDebtQuery = `
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'investment_criteria_debt' 
        AND table_schema = 'public'
        ORDER BY ordinal_position
      `

    const investmentCriteriaEquityQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'investment_criteria_equity' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    ` 

    const [companiesResult, contactsResult, investmentCriteriaCentralResult, investmentCriteriaDebtResult, investmentCriteriaEquityResult] = await Promise.all([
      pool.query(companiesQuery),
      pool.query(contactsQuery),
      pool.query(investmentCriteriaCentralQuery),
      pool.query(investmentCriteriaDebtQuery),
      pool.query(investmentCriteriaEquityQuery)
    ])

    // Filter out system columns and internal fields
    const contactSystemColumns = [
      'enriched',
      'contact_id',
      'company_id',
      'headline',
      'email_status',
      'extra_attrs',
      'created_at',
      'updated_at',
      'category',
      'source',
      'email_validated_date',
      'extracted',
      'searched',
      'email_generated',
      'smartlead_lead_id',
      'smartlead_status',
      'last_email_sent_at',
      'email_batch_identifier',
      'email_verification_status',
      'email_verification_date',
      'email_verification_error',
      'email_generation_status',
      'email_generation_date',
      'email_generation_error',
      'email_sending_status',
      'email_sending_date',
      'email_sending_error',
      'last_processed_stage',
      'last_processed_at',
      'processing_error_count',
      'processing_attempts',
      'email_sent_error',
      'email_sent_date',
      'conflicts',
      'conflict_status',
      'conflict_created_at',
      'conflict_resolved_at',
      'conflict_source',
      
    ]
    const investmentCriteriaSystemColumns = [
      'criteria_id',
      'entity_type',
      'entity_id',
      'financial_products',
      'historical_irr',
      'historical_em',
      'capital_source',
      'created_at',
      'updated_at',
      'created_by',
      'updated_by',
      'is_active',
      'loan_type_normalized',
      'location_focus',
      'loan_term_string',
    ]
    const companySystemColumns = [
      'company_id',
      'extra_attrs',
      'created_at',
      'updated_at',
      'canonical_handle',
      'processed',
      'extracted',
      'overview',
      'source',
      'processing_state',
      'website_scraping_status',
      'company_overview_date',
      'company_overview_error',
      'company_overview_date',
      'company_overview_error',
      'last_processed_stage',
      'last_processed_at',
      'processing_error_count',
      'processing_attempts',
      'web_scraping_error',
      'web_scraping_date',
      'conflicts',
      'conflict_status',
      'conflict_created_at',
      'conflict_resolved_at',
      'conflict_source',
      'website_scraping_date',
      'website_scraping_error',
      'company_overview_status',
    ]

    const companiesFields = companiesResult.rows
      .map(row => row.column_name)
      .filter(column => !companySystemColumns.includes(column))

    const contactsFields = contactsResult.rows
      .map(row => row.column_name)
      .filter(column => !contactSystemColumns.includes(column))

    const investmentCriteriaCentralFields = investmentCriteriaCentralResult.rows
      .map(row => row.column_name)
      .filter(column => !investmentCriteriaSystemColumns.includes(column))
    
    const investmentCriteriaDebtFields = investmentCriteriaDebtResult.rows

    const investmentCriteriaEquityFields = investmentCriteriaEquityResult.rows
      .map(row => row.column_name)
      .filter(column => !investmentCriteriaSystemColumns.includes(column))

    return NextResponse.json({
      success: true,
      companies: companiesFields,
      contacts: contactsFields,
      investment_criteria_central: investmentCriteriaCentralFields,
      investment_criteria_debt: investmentCriteriaDebtFields,
      investment_criteria_equity: investmentCriteriaEquityFields
    })

  } catch (error) {
    console.error('Database fields fetch error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
} 