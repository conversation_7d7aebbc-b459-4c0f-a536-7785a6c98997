import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'
import { ConflictData, FieldConflict } from '@/types/conflict'

interface CheckConflictsRequest {
  data: Array<{
    [key: string]: any
  }>
  headerMappings: Record<string, string>
  source: string
}

/**
 * Extract domain from a URL string (copied from CompanyOverviewProcessor)
 */
function extractDomain(url: string): string {
  try {
    if (!url) return "";
    if (!url.startsWith("http")) {
      url = `https://${url}`;
    }
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname;

    // Split the hostname into parts
    const parts = hostname.split(".");

    // Handle cases where the hostname is an IP address or doesn't have enough parts
    if (parts.length < 2 || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
      return hostname;
    }

    const secondLevelTLDs = [
      "co",
      "gov",
      "org",
      "edu",
      "com",
      "net",
      "int",
      "mil",
      "arpa",
    ];

    // Check if the second to last part is a second-level domain (e.g., 'co' in 'co.uk')
    if (parts.length > 2 && secondLevelTLDs.includes(parts[parts.length - 2])) {
      // Return the last three parts (e.g., example.co.uk)
      return parts.slice(-3).join(".");
    }

    // Otherwise, return the last two parts as the domain (e.g., example.com)
    return parts.slice(-2).join(".");
  } catch (error) {
    console.error(`Invalid URL: ${error}`);
    return "";
  }
}

function detectFieldConflicts(
  existingRecord: Record<string, any>,
  newRecord: Record<string, any>,
  fieldMapping: Record<string, string>,
  source: string
): ConflictData {
  const conflicts: ConflictData = {}
  const timestamp = new Date().toISOString()

  Object.entries(fieldMapping).forEach(([csvField, dbField]) => {
    const existingValue = existingRecord[dbField]
    const newValue = newRecord[csvField]
    
    // Convert values to strings for comparison (handle null/undefined)
    const existingStr = existingValue ? String(existingValue).trim() : ''
    const newStr = newValue ? String(newValue).trim() : ''

    // Special handling for company website - use domain matching
    if (dbField === 'company_website') {
      // Only create conflict if both values are non-empty AND have different domains
      if (existingStr && newStr) {
        const existingDomain = extractDomain(existingStr)
        const newDomain = extractDomain(newStr)
        
        // Only conflict if domains are different (and both are valid)
        if (existingDomain && newDomain && existingDomain !== newDomain) {
          conflicts[dbField] = {
            existing_value: existingStr,
            new_value: newStr,
            source,
            created_at: timestamp,
            field_type: 'string'
          }
        }
      }
    } else {
      // Only create conflict if both values are non-empty AND different
      if (existingStr && newStr && existingStr !== newStr) {
        conflicts[dbField] = {
          existing_value: existingStr,
          new_value: newStr,
          source,
          created_at: timestamp,
          field_type: 'string'
        }
      }
    }
  })

  return conflicts
}

// Helper function to get mapped value from row
function getMappedValue(row: Record<string, any>, dbField: string, headerMappings: Record<string, string>): string {
  // Find the CSV header that maps to this database field
  const csvHeader = Object.keys(headerMappings).find(header => headerMappings[header] === dbField)
  return csvHeader && row[csvHeader] ? String(row[csvHeader]).trim() : ''
}

// Fetch database fields dynamically
async function fetchDatabaseFields() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/investors/get-database-fields`)
    const result = await response.json()
    
    if (!result.success) {
      throw new Error('Failed to fetch database fields')
    }
    
    return {
      companies: result.companies,
      contacts: result.contacts
    }
  } catch (error) {
    console.error('Error fetching database fields:', error)
    // Fallback to hardcoded fields
    return {
      companies: [
        'company_name', 'company_website', 'company_linkedin', 'company_address',
        'company_city', 'company_state', 'company_zip', 'company_country',
        'company_phone', 'industry', 'founded_year', 'capital_position'
      ],
      contacts: [
        'first_name', 'last_name', 'full_name', 'title', 'email', 'personal_email',
        'linkedin_url', 'phone_number', 'contact_city', 'contact_state',
        'contact_country', 'region', 'capital_type', 'contact_category', 'notes'
      ]
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const { data, headerMappings, source }: CheckConflictsRequest = await request.json()

    if (!data || !headerMappings) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      )
    }

    // Fetch database fields dynamically
    const dbFields = await fetchDatabaseFields()

    const client = await pool.connect()
    
    try {
      const companyConflicts: Array<{
        existing_id: number
        conflicts: ConflictData
        company_name: string
      }> = []

      const contactConflicts: Array<{
        existing_id: number
        conflicts: ConflictData
        full_name: string
        company_name: string
      }> = []

      const emailConflicts: Array<{
        existing_id: number
        conflicts: ConflictData
        full_name: string
        email: string
      }> = []

      // Check company conflicts
      for (const row of data) {
        const companyName = getMappedValue(row, 'company_name', headerMappings)
        if (!companyName) continue

        // Find existing company
        const companyResult = await client.query(
          'SELECT * FROM companies WHERE company_name = $1',
          [companyName]
        )

        if (companyResult.rows.length > 0) {
          const existingCompany = companyResult.rows[0]
          
          // Create reverse mapping for conflict detection
          const reverseMapping: Record<string, string> = {}
          Object.entries(headerMappings).forEach(([csvHeader, dbField]) => {
            if (dbFields.companies.includes(dbField)) {
              reverseMapping[csvHeader] = dbField
            }
          })

          const conflicts = detectFieldConflicts(
            existingCompany,
            row,
            reverseMapping,
            source
          )

          if (Object.keys(conflicts).length > 0) {
            companyConflicts.push({
              existing_id: existingCompany.company_id,
              conflicts,
              company_name: companyName
            })
          }
        }
      }

      // Check contact conflicts
      for (const row of data) {
        const firstName = getMappedValue(row, 'first_name', headerMappings)
        const lastName = getMappedValue(row, 'last_name', headerMappings)
        const fullName = `${firstName} ${lastName}`.trim()
        const companyName = getMappedValue(row, 'company_name', headerMappings)
        
        if (!fullName || !companyName) continue

        // Find existing contact
        const contactResult = await client.query(`
          SELECT c.*, comp.company_name 
          FROM contacts c
          JOIN companies comp ON c.company_id = comp.company_id
          WHERE c.full_name = $1 AND comp.company_name = $2
        `, [fullName, companyName])

        if (contactResult.rows.length > 0) {
          const existingContact = contactResult.rows[0]
          
          // Create reverse mapping for conflict detection
          const reverseMapping: Record<string, string> = {}
          Object.entries(headerMappings).forEach(([csvHeader, dbField]) => {
            if (dbFields.contacts.includes(dbField)) {
              reverseMapping[csvHeader] = dbField
            }
          })

          const conflicts = detectFieldConflicts(
            existingContact,
            row,
            reverseMapping,
            source
          )

          if (Object.keys(conflicts).length > 0) {
            contactConflicts.push({
              existing_id: existingContact.contact_id,
              conflicts,
              full_name: fullName,
              company_name: companyName
            })
          }
        }
      }

      // Check for email conflicts across different contacts
      for (const row of data) {
        const email = getMappedValue(row, 'email', headerMappings)
        if (!email) continue

        const firstName = getMappedValue(row, 'first_name', headerMappings)
        const lastName = getMappedValue(row, 'last_name', headerMappings)
        const fullName = `${firstName} ${lastName}`.trim()
        const companyName = getMappedValue(row, 'company_name', headerMappings)

        // Check if email exists with different contact
        const emailResult = await client.query(`
          SELECT c.*, comp.company_name 
          FROM contacts c
          JOIN companies comp ON c.company_id = comp.company_id
          WHERE c.email = $1 AND (c.full_name != $2 OR comp.company_name != $3)
        `, [email, fullName, companyName])

        if (emailResult.rows.length > 0) {
          const existingContact = emailResult.rows[0]
          
          emailConflicts.push({
            existing_id: existingContact.contact_id,
            conflicts: {
              email: {
                existing_value: `${existingContact.full_name} (${existingContact.company_name})`,
                new_value: `${fullName} (${companyName})`,
                source,
                created_at: new Date().toISOString(),
                field_type: 'string'
              }
            },
            full_name: existingContact.full_name,
            email: email
          })
        }
      }

      return NextResponse.json({
        success: true,
        conflicts: {
          companies: companyConflicts,
          contacts: contactConflicts,
          emails: emailConflicts
        }
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Conflict check error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check conflicts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 