'use client';

import { Suspense, useState, useEffect } from 'react';
import { 
  CampaignStats 
} from '@/components/dashboard/smartlead/CampaignStats';
import { 
  ContactEmailThreads 
} from '@/components/dashboard/smartlead/ContactEmailThreads';
import LeadsTab from '@/components/dashboard/smartlead/LeadsTab';
import MessageHistoryTab from '@/components/dashboard/smartlead/MessageHistoryTab';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Users, 
  Mail, 
  BarChart3, 
  MessageSquare, 
  Settings,
  RefreshCw,
  ArrowLeft,
  Plus,
  Play,
  Pause,
  Square,
  Target,
  TrendingUp,
  Calendar,
  Clock,
  Eye,
  MousePointer,
  Reply,
  Edit,
  Trash2
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter 
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';

interface Campaign {
  id: string;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  lead_count?: number;
  max_leads_per_day?: number;
  min_time_btwn_emails?: number;
  follow_up_percentage?: number;
  enable_ai_esp_matching?: boolean;
  send_as_plain_text?: boolean;
  stop_lead_settings?: any;
  track_settings?: string;
  scheduler_cron_value?: string;
  unsubscribe_text?: string;
  client_id?: number;
}


interface CampaignOverview {
  campaign: {
    id: string;
    name: string;
    status: string;
    created_at: string;
    updated_at: string;
    track_settings?: string;
    stop_lead_settings?: any;
    max_leads_per_day?: number;
    min_time_btwn_emails?: number;
    follow_up_percentage?: number;
    enable_ai_esp_matching?: boolean;
    send_as_plain_text?: boolean;
  };
  sequence: any;
  leadStats: {
    totalLeads: number;
    statusDistribution: Record<string, number>;
  };
  emailStats: {
    sent: number;
    delivered: number;
    opened: number;
    replied: number;
    bounced: number;
    unsubscribed: number;
    clicked: number;
  };
  performanceMetrics: {
    openRate: number;
    replyRate: number;
    bounceRate: number;
  };
  dailyActivity: Array<{
    date: string;
    sent: number;
    opened: number;
    replied: number;
    clicked: number;
  }>;
  recentActivity: Array<{
    contact_id: number | null;
    first_name: string;
    last_name: string;
    email: string;
    smartlead_status: string;
    last_email_sent_at: string;
    company_name: string | null;
    sequence_number: number;
    email_subject: string;
  }>;
}

export default function SmartleadDashboardPage() {
  const [selectedContactId, setSelectedContactId] = useState<number | null>(null);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string | null>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [campaignOverview, setCampaignOverview] = useState<CampaignOverview | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingOverview, setIsLoadingOverview] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newCampaignName, setNewCampaignName] = useState('');
  // Filters for overview/statistics
  const [filterSequence, setFilterSequence] = useState<string>('all');
  const [filterEmailStatus, setFilterEmailStatus] = useState<string>('all');

  // Fetch campaigns
  useEffect(() => {
    fetchCampaigns();
  }, []);

  // Fetch campaign details when campaign is selected
  useEffect(() => {
    if (selectedCampaignId) {
      fetchCampaignOverview(selectedCampaignId);
    }
  }, [selectedCampaignId]);

  const fetchCampaigns = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/smartlead/campaigns');
      if (!response.ok) {
        throw new Error(`Failed to fetch campaigns: ${response.status}`);
      }
      const data = await response.json();
      setCampaigns(data.campaigns || []);
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      toast.error('Failed to load campaigns');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCampaignOverview = async (campaignId: string) => {
    try {
      setIsLoadingOverview(true);
      // Build query with filters if applied
      const params = new URLSearchParams();
      if (filterSequence !== 'all') params.append('email_sequence_number', filterSequence);
      if (filterEmailStatus !== 'all') params.append('email_status', filterEmailStatus);
      const qs = params.toString();
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}/stats${qs ? `?${qs}` : ''}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch campaign overview: ${response.status}`);
      }
      const data = await response.json();
      setCampaignOverview(data);
    } catch (error) {
      console.error('Error fetching campaign overview:', error);
      toast.error('Failed to load campaign overview');
    } finally {
      setIsLoadingOverview(false);
    }
  };


  const handleContactSelect = (contactId: number) => {
    setSelectedContactId(contactId);
  };

  const handleCampaignSelect = (campaignId: string) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    setSelectedCampaignId(campaignId);
    setSelectedCampaign(campaign || null);
    setActiveTab('overview'); // Always start with overview when selecting a campaign
  };

  const handleBackToCampaigns = () => {
    setSelectedContactId(null);
    setSelectedCampaignId(null);
    setSelectedCampaign(null);
    setCampaignOverview(null);
  };

  const handleMessageHistoryOpen = (leadId: string) => {
    // Store lead and campaign info for the message history tab
    localStorage.setItem('viewMessageCampaignId', selectedCampaignId || '');
    localStorage.setItem('viewMessageLeadId', leadId);
    
    // Switch to the Messages tab
    setActiveTab('messages');
    
    // Show a toast to let user know we're switching tabs
    toast.success('Switching to Messages tab to view conversation history');
  };

  const handleCreateCampaign = async () => {
    if (!newCampaignName.trim()) {
      toast.error('Campaign name is required');
      return;
    }

    try {
      setIsCreating(true);
      const response = await fetch('/api/smartlead/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCampaignName.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create campaign: ${response.status}`);
      }

      toast.success('Campaign created successfully');
      setNewCampaignName('');
      setShowCreateDialog(false);
      fetchCampaigns();
    } catch (error) {
      console.error('Error creating campaign:', error);
      toast.error('Failed to create campaign');
    } finally {
      setIsCreating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800 border-green-300';
      case 'PAUSED': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'COMPLETED': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'STOPPED': return 'bg-red-100 text-red-800 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return <Play className="h-4 w-4" />;
      case 'PAUSED': return <Pause className="h-4 w-4" />;
      case 'COMPLETED': return <Target className="h-4 w-4" />;
      case 'STOPPED': return <Square className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getActivityBadgeClasses = (status?: string) => {
    switch ((status || '').toUpperCase()) {
      case 'REPLIED':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200';
      case 'OPENED':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'CLICKED':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'BOUNCED':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'UNSUBSCRIBED':
        return 'bg-orange-50 text-orange-700 border-orange-200';
      case 'SENT':
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || campaign.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  // If a contact is selected, show the contact detail view
  if (selectedContactId !== null) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white border-b px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => setSelectedContactId(null)}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Campaigns
            </Button>
            <h1 className="text-2xl font-bold">Contact Conversations</h1>
          </div>
        </div>
        
        <div className="p-6">
          <Suspense fallback={<div>Loading contact threads...</div>}>
            <ContactEmailThreads contactId={selectedContactId} />
          </Suspense>
        </div>
      </div>
    );
  }

  // If a campaign is selected, show the campaign details view
  if (selectedCampaignId !== null && selectedCampaign) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Campaign Details Header */}
        <div className="bg-white border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={handleBackToCampaigns}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Campaigns
              </Button>
              <div>
                <h1 className="text-2xl font-bold">{selectedCampaign.name}</h1>
                <p className="text-sm text-muted-foreground">
                  Created {new Date(selectedCampaign.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
            <Badge 
              variant="outline"
              className={getStatusColor(selectedCampaign.status)}
            >
              <div className="flex items-center space-x-1">
                {getStatusIcon(selectedCampaign.status)}
                <span>{selectedCampaign.status}</span>
              </div>
            </Badge>
          </div>
        </div>

        {/* Campaign Details Tabs */}
        <div className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview" className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4" />
                <span>Overview</span>
              </TabsTrigger>
              <TabsTrigger value="leads" className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Leads</span>
              </TabsTrigger>
              <TabsTrigger value="messages" className="flex items-center space-x-2">
                <MessageSquare className="h-4 w-4" />
                <span>Messages</span>
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              {/* Filters */}
              <div className="flex flex-wrap items-center gap-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Sequence</span>
                  <Select value={filterSequence} onValueChange={(v) => { setFilterSequence(v); if (selectedCampaignId) fetchCampaignOverview(selectedCampaignId); }}>
                    <SelectTrigger className="w-[160px]">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="1">#1</SelectItem>
                      <SelectItem value="2">#2</SelectItem>
                      <SelectItem value="3">#3</SelectItem>
                      <SelectItem value="4">#4</SelectItem>
                      <SelectItem value="5">#5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Email Status</span>
                  <Select value={filterEmailStatus} onValueChange={(v) => { setFilterEmailStatus(v); if (selectedCampaignId) fetchCampaignOverview(selectedCampaignId); }}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="SENT">Sent</SelectItem>
                      <SelectItem value="DELIVERED">Delivered</SelectItem>
                      <SelectItem value="OPENED">Opened</SelectItem>
                      <SelectItem value="CLICKED">Clicked</SelectItem>
                      <SelectItem value="REPLIED">Replied</SelectItem>
                      <SelectItem value="BOUNCED">Bounced</SelectItem>
                      <SelectItem value="UNSUBSCRIBED">Unsubscribed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => { setFilterSequence('all'); setFilterEmailStatus('all'); if (selectedCampaignId) fetchCampaignOverview(selectedCampaignId); }}
                  disabled={isLoadingOverview || (filterSequence === 'all' && filterEmailStatus === 'all')}
                >
                  Clear Filters
                </Button>
              </div>
              {isLoadingOverview ? (
                <div className="flex justify-center items-center h-40">
                  <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total Leads</p>
                          <p className="text-3xl font-bold">{campaignOverview?.leadStats?.totalLeads || 0}</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Emails Sent</p>
                          <p className="text-3xl font-bold">{campaignOverview?.emailStats?.sent || 0}</p>
                        </div>
                        <Mail className="h-8 w-8 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Open Rate</p>
                          <p className="text-3xl font-bold">{campaignOverview?.performanceMetrics?.openRate?.toFixed(1) || 0}%</p>
                        </div>
                        <Eye className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Reply Rate</p>
                          <p className="text-3xl font-bold">{campaignOverview?.performanceMetrics?.replyRate?.toFixed(1) || 0}%</p>
                        </div>
                        <Reply className="h-8 w-8 text-emerald-600" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Detailed Stats */}
              {campaignOverview && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Engagement Metrics</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Opened Emails</span>
                          <span className="font-semibold">{campaignOverview.emailStats?.opened || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Clicked Emails</span>
                          <span className="font-semibold">{campaignOverview.emailStats?.clicked || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Replied Emails</span>
                          <span className="font-semibold">{campaignOverview.emailStats?.replied || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Bounced Emails</span>
                          <span className="font-semibold">{campaignOverview.emailStats?.bounced || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Delivered Emails</span>
                          <span className="font-semibold">{campaignOverview.emailStats?.delivered || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Unsubscribed</span>
                          <span className="font-semibold">{campaignOverview.emailStats?.unsubscribed || 0}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Performance Rates</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Open Rate</span>
                          <span className="font-semibold text-green-600">{campaignOverview.performanceMetrics?.openRate?.toFixed(1) || 0}%</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Click Rate</span>
                          <span className="font-semibold text-blue-600">
                            {campaignOverview.emailStats?.sent > 0 
                              ? ((campaignOverview.emailStats.clicked / campaignOverview.emailStats.sent) * 100).toFixed(1) 
                              : 0}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Reply Rate</span>
                          <span className="font-semibold text-emerald-600">{campaignOverview.performanceMetrics?.replyRate?.toFixed(1) || 0}%</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Bounce Rate</span>
                          <span className="font-semibold text-red-600">{campaignOverview.performanceMetrics?.bounceRate?.toFixed(1) || 0}%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Recent Activity */}
              {campaignOverview?.recentActivity && campaignOverview.recentActivity.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {campaignOverview.recentActivity.slice(0, 10).map((act, idx) => (
                        <div key={`${act.email}-${act.last_email_sent_at}-${idx}`} className="flex items-start justify-between p-3 rounded border">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 flex-wrap">
                              <span className="font-medium text-gray-900 truncate">
                                {act.first_name || act.last_name ? `${act.first_name || ''} ${act.last_name || ''}`.trim() : act.email}
                              </span>
                              {act.company_name && (
                                <span className="text-xs text-gray-500 truncate">· {act.company_name}</span>
                              )}
                            </div>
                            <div className="text-xs text-gray-600 mt-1 truncate">
                              {act.email_subject || 'No subject'}
                            </div>
                            <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                              <Calendar className="h-3 w-3" />
                              <span>{new Date(act.last_email_sent_at).toLocaleString()}</span>
                              {typeof act.sequence_number === 'number' && (
                                <>
                                  <span>•</span>
                                  <span>Sequence #{act.sequence_number}</span>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="ml-4 shrink-0 flex items-center gap-2">
                            <Badge variant="outline" className={getActivityBadgeClasses(act.smartlead_status)}>
                              {act.smartlead_status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>


            {/* Leads Tab */}
            <TabsContent value="leads" className="space-y-6">
              <Suspense fallback={<div>Loading leads...</div>}>
                <LeadsTab 
                  selectedCampaignId={selectedCampaignId}
                  onContactSelect={handleContactSelect}
                  onMessageHistoryOpen={handleMessageHistoryOpen}
                />
              </Suspense>
            </TabsContent>

            {/* Messages Tab */}
            <TabsContent value="messages" className="space-y-6">
              <Suspense fallback={<div>Loading message history...</div>}>
                <MessageHistoryTab propSelectedCampaignId={selectedCampaignId || undefined} />
              </Suspense>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  // Main campaigns list view (full screen)
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-1">Email Campaigns</h1>
            <p className="text-muted-foreground">
              Manage and monitor your Smartlead email campaigns
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button 
              variant="outline" 
              size="sm"
              onClick={fetchCampaigns}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Campaign
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Campaign</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="campaign-name">Campaign Name</Label>
                    <Input
                      id="campaign-name"
                      value={newCampaignName}
                      onChange={(e) => setNewCampaignName(e.target.value)}
                      placeholder="Enter campaign name"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateCampaign} disabled={isCreating}>
                    {isCreating ? 'Creating...' : 'Create Campaign'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex items-center space-x-4 mt-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search campaigns..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="paused">Paused</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="stopped">Stopped</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Campaigns Grid */}
      <div className="p-6">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : filteredCampaigns.length === 0 ? (
          <div className="text-center py-12">
            <Mail className="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No campaigns found</h3>
            <p className="text-gray-500 mb-6">
              {campaigns.length === 0 
                ? "Create your first campaign to get started" 
                : "No campaigns match your search criteria"
              }
            </p>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Campaign
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredCampaigns.map((campaign) => (
              <Card 
                key={campaign.id} 
                className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
                onClick={() => handleCampaignSelect(campaign.id)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <Mail className="h-6 w-6 text-white" />
                    </div>
                    <Badge 
                      variant="outline"
                      className={getStatusColor(campaign.status)}
                    >
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(campaign.status)}
                        <span className="text-xs">{campaign.status}</span>
                      </div>
                    </Badge>
                  </div>
                  
                  <h3 className="font-semibold text-lg mb-2 line-clamp-2">{campaign.name}</h3>
                  
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center justify-between">
                      <span>Created</span>
                      <span>{new Date(campaign.created_at).toLocaleDateString()}</span>
                    </div>
                    {campaign.lead_count && (
                      <div className="flex items-center justify-between">
                        <span>Leads</span>
                        <span className="font-medium">{campaign.lead_count}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-4 flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      Last updated {new Date(campaign.updated_at).toLocaleDateString()}
                    </span>
                    <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 