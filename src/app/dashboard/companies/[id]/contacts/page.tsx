'use client';

import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Mail, Phone, Briefcase, ExternalLink, UserPlus } from 'lucide-react';
import Link from 'next/link';

interface Contact {
  contact_id: number;
  first_name: string;
  last_name: string;
  full_name?: string;
  title: string;
  headline?: string;
  seniority?: string;
  email?: string;
  personal_email?: string;
  email_status?: string;
  linkedin_url?: string;
  contact_city?: string;
  contact_state?: string;
  contact_country?: string;
  email_generated?: boolean;
  smartlead_lead_id?: string;
}

interface CompanyBasic {
  company_id: number;
  company_name: string;
}

export default function CompanyContactsPage() {
  const router = useRouter();
  const params = useParams();
  const companyId = params?.id ? 
    Array.isArray(params.id) ? params.id[0] : params.id 
    : '';
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [company, setCompany] = useState<CompanyBasic | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCompanyContacts = async () => {
      try {
        setLoading(true);
        // First get company basic info
        const companyResponse = await fetch(`/api/companies/${companyId}`);
        if (!companyResponse.ok) {
          throw new Error(`Failed to fetch company: ${companyResponse.status}`);
        }
        const companyData = await companyResponse.json();
        setCompany({
          company_id: companyData.company_id,
          company_name: companyData.company_name
        });
        
        // Then get company contacts
        const contactsResponse = await fetch(`/api/companies/${companyId}/contacts`);
        if (!contactsResponse.ok) {
          throw new Error(`Failed to fetch contacts: ${contactsResponse.status}`);
        }
        const data = await contactsResponse.json();
        setContacts(data.contacts || []);
      } catch (error) {
        console.error('Error fetching company contacts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompanyContacts();
  }, [companyId]);

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} size="sm" className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">
            {loading ? 'Loading...' : `${company?.company_name} - Contacts`}
          </h1>
        </div>
        <Button>
          <UserPlus className="h-4 w-4 mr-2" />
          Add Contact
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : contacts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-lg text-gray-500 mb-4">No contacts found for this company</p>
            <Button>
              <UserPlus className="h-4 w-4 mr-2" />
              Add Contact
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {contacts.map((contact) => (
            <Link 
              href={`/dashboard/people/${contact.contact_id}`}
              key={contact.contact_id} 
              className="block"
            >
              <Card className="h-full cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <Avatar className="h-10 w-10 mr-4">
                        <AvatarFallback className="bg-blue-700 text-white">
                          {contact.first_name?.[0]}{contact.last_name?.[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-base">
                          {contact.full_name || `${contact.first_name} ${contact.last_name}`}
                        </CardTitle>
                        <p className="text-sm text-gray-500">{contact.title || 'No title'}</p>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3 pt-0">
                  {contact.email && (
                    <div className="flex items-center text-sm">
                      <Mail className="h-4 w-4 mr-2 text-gray-400" />
                      <span className="truncate">{contact.email}</span>
                    </div>
                  )}
                  
                  {(contact.contact_city || contact.contact_state) && (
                    <div className="flex items-center text-sm">
                      <Briefcase className="h-4 w-4 mr-2 text-gray-400" />
                      <span>
                        {[contact.contact_city, contact.contact_state]
                          .filter(Boolean)
                          .join(', ')}
                      </span>
                    </div>
                  )}

                  <div className="pt-2 flex flex-wrap gap-1.5">
                    {contact.email_generated && (
                      <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                        Email Generated
                      </Badge>
                    )}
                    {contact.smartlead_lead_id && (
                      <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">
                        Smartlead
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
} 