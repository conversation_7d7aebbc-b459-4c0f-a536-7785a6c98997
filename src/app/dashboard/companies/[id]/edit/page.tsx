'use client'

import { usePara<PERSON>, useRouter } from 'next/navigation'
import CompanyForm from '@/components/dashboard/companies/detail-components/CompanyForm'

export default function EditCompanyPage() {
  const params = useParams()
  const router = useRouter()
  const companyId = params?.id as string | undefined

  if (!companyId) {
    return <div>Invalid company ID</div>
  }

  return (
    <CompanyForm 
      companyId={companyId}
      isEmbedded={true}
      onCancel={() => router.back()}
      onSuccess={(companyId) => {
        router.refresh();
      }}
    />
  )
} 