'use client'

import React, { useState, useEffect } from 'react'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Building, User } from 'lucide-react'
import { useRouter, useSearchParams } from 'next/navigation'
import ContactsView from '@/components/dashboard/people/ContactsView'
import CompaniesView from '@/components/dashboard/companies/detail-components/CompaniesView'

export default function EntityPage() {
  const [activeTab, setActiveTab] = useState('contacts')
  const router = useRouter()
  const searchParams = useSearchParams()

  // Initialize active tab from URL
  useEffect(() => {
    if (!searchParams) return
    const tab = searchParams.get('tab') || 'contacts'
    if (tab === 'companies' || tab === 'contacts') {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Handle tab change with URL update
  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    
    if (!searchParams) return
    
    // Preserve existing URL parameters and add/update tab parameter
    const params = new URLSearchParams(searchParams.toString())
    params.set('tab', tab)
    
    const newUrl = `/dashboard/entity?${params.toString()}`
    router.replace(newUrl, { scroll: false })
  }

  return (
    <div className="w-full">
      <div className="bg-white border-b border-gray-200 px-6 py-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Entity Management</h1>
            <p className="text-sm text-gray-600 mt-1">
              Manage contacts and companies in your database
            </p>
          </div>
        </div>
      </div>

      <div className="px-6">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="contacts" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Contacts
            </TabsTrigger>
            <TabsTrigger value="companies" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              Companies
            </TabsTrigger>
          </TabsList>

          <TabsContent value="contacts" className="space-y-4">
            <ContactsView />
          </TabsContent>

          <TabsContent value="companies" className="space-y-4">
            <CompaniesView />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 