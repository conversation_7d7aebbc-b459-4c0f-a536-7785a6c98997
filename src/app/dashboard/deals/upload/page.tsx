"use client";

import SimplifiedDealUpload from "@/components/dashboard/deals/SimplifiedDealUpload";
import { useState } from "react";
import DealConflictsList from "@/components/dashboard/deals/DealConflictsList";

export default function DealsUploadPage() {
  const [tab, setTab] = useState<"upload" | "conflicts">("upload");
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Tab Selector */}
        <div className="flex gap-4 mb-4">
          <button
            className={`px-4 py-2 rounded ${
              tab === "upload"
                ? "bg-blue-600 text-white"
                : "bg-white text-blue-600 border"
            }`}
            onClick={() => setTab("upload")}
          >
            Upload
          </button>
          <button
            className={`px-4 py-2 rounded ${
              tab === "conflicts"
                ? "bg-blue-600 text-white"
                : "bg-white text-blue-600 border"
            }`}
            onClick={() => setTab("conflicts")}
          >
            Conflicts
          </button>
        </div>
        {/* Tab Content */}
        {tab === "upload" && <SimplifiedDealUpload />}
        {tab === "conflicts" && <DealConflictsList />}
      </div>
    </div>
  );
}
