'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Loader2, 
  Edit, 
  Save, 
  X, 
  Database, 
  Building2, 
  CreditCard, 
  TrendingUp,
  Home,
  AlertCircle,
  CheckCircle,
  Settings,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface CapitalPositionWeight {
  id?: number;
  capital_position: string;
  field_name: string;
  weight: number;
  description: string;
  table_name: string;
  field_category: string;
  is_active: boolean;
}

interface TableBreakdown {
  [tableName: string]: {
    total_weight: number;
    field_count: number;
  };
}

interface CapitalPosition {
  capital_position: string;
  position_type: 'COMMON' | 'DEBT' | 'EQUITY';
  total_weight_percent: number;
  description: string;
}

export default function EnhancedCapitalPositionWeightsPage() {
  const [weights, setWeights] = useState<CapitalPositionWeight[]>([]);
  const [tableBreakdown, setTableBreakdown] = useState<TableBreakdown>({});
  const [capitalPositions, setCapitalPositions] = useState<CapitalPosition[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingWeights, setEditingWeights] = useState<{[key: string]: string}>({});
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{debt: string | null, equity: string | null}>({debt: null, equity: null});
  const { toast } = useToast();

  // Table display names and icons
  const tableConfig = {
    dealsv2: { name: 'Deal Fields', icon: Database, color: 'bg-blue-100 text-blue-800' },
    investment_criteria_debt: { name: 'Debt Fields', icon: CreditCard, color: 'bg-red-100 text-red-800' },
    investment_criteria_equity: { name: 'Equity Fields', icon: TrendingUp, color: 'bg-green-100 text-green-800' },
    properties: { name: 'Property Fields', icon: Home, color: 'bg-purple-100 text-purple-800' }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Validate weights whenever editing weights change
  useEffect(() => {
    if (isEditing && !loading && weights.length > 0) {
      validateWeights();
    }
  }, [editingWeights, isEditing, loading, weights]);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Fetch COMMON weights specifically since that's what we're managing
      const response = await fetch('/api/dashboard/configuration/capital-position-weights?capital_position=COMMON');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log('✅ API Response:', data);
          console.log('📊 Weights:', data.data.weights);
          console.log('📋 Table Breakdown:', data.data.tableBreakdown);
          
          // Validate weights data structure
          const weightsData = data.data.weights || [];
          console.log('🔍 Weights validation:');
          weightsData.forEach((w, index) => {
            console.log(`Weight ${index}:`, {
              table_name: w.table_name,
              field_name: w.field_name,
              weight: w.weight,
              weightType: typeof w.weight,
              isNaN: isNaN(w.weight)
            });
          });
          
          setWeights(weightsData);
          setTableBreakdown(data.data.tableBreakdown || {});
          setCapitalPositions([]);
        } else {
          console.error('API returned error:', data.message);
          toast({
            title: 'Error',
            description: data.message || 'Failed to fetch weights',
            variant: 'destructive',
          });
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch capital position weights',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStartEditing = () => {
    setIsEditing(true);
    // Initialize editing weights with current values as percentage strings
    const initialEditingWeights: {[key: string]: string} = {};
    weights.forEach(weight => {
      const key = `${weight.table_name}_${weight.field_name}`;
      initialEditingWeights[key] = (parseFloat(String(weight.weight)) * 100).toString();
    });
    setEditingWeights(initialEditingWeights);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditingWeights({});
    setValidationErrors({debt: null, equity: null});
  };

  const handleInputChange = (fieldName: string, tableName: string, inputValue: string) => {
    const key = `${tableName}_${fieldName}`;
    
    // Store raw string value (what user types)
    setEditingWeights(prev => ({
      ...prev,
      [key]: inputValue
    }));
  };



  const normalizeWeightsInCategory = (tableName: string) => {
    const categoryWeights = weights.filter(w => w.table_name === tableName && !excludedFields.includes(w.field_name));
    const currentTotal = calculateTotalWeight(tableName);
    const universalTotal = calculateScenarioTotals().dealPropertyWeights;
    const remainingPercentage = 100 - universalTotal; // 100% - universal% (raw percentage)
    
    if (currentTotal === 0 || remainingPercentage <= 0) return;
    
    const normalizedWeights: {[key: string]: string} = {};
    categoryWeights.forEach(weight => {
      const key = `${weight.table_name}_${weight.field_name}`;
      const currentWeight = editingWeights[key] !== undefined ? parseFloat(editingWeights[key]) : parseFloat(String(weight.weight)) * 100;
      // Distribute the remaining percentage proportionally
      const newWeight = (currentWeight / currentTotal) * remainingPercentage;
      normalizedWeights[key] = newWeight.toString();
    });
    
    setEditingWeights(prev => ({
      ...prev,
      ...normalizedWeights
    }));
  };

  const getCurrentWeight = (fieldName: string, tableName: string): number => {
    const key = `${tableName}_${fieldName}`;
    if (editingWeights[key] !== undefined) {
      // Convert string percentage to decimal
      return parseFloat(editingWeights[key]) / 100 || 0;
    }
    // Find the original weight from the weights array (already in decimal format)
    const originalWeight = weights.find(w => w.field_name === fieldName && w.table_name === tableName);
    return originalWeight ? parseFloat(String(originalWeight.weight)) : 0;
  };

  const getInputValue = (fieldName: string, tableName: string): string => {
    const key = `${tableName}_${fieldName}`;
    if (editingWeights[key] !== undefined) {
      return editingWeights[key]; // Return raw string value
    }
    // Find the original weight and convert to percentage string
    const originalWeight = weights.find(w => w.field_name === fieldName && w.table_name === tableName);
    return originalWeight ? (parseFloat(String(originalWeight.weight)) * 100).toString() : "0";
  };

  // Convert field names to user-friendly display names
  const getFieldDisplayName = (fieldName: string): string => {
    const displayNames: {[key: string]: string} = {
      'deal_amount': 'Deal Amount',
      'location': 'Location',
      'property_type': 'Property Type',
      'deal_stage': 'Deal Stage',
      'deal_status': 'Deal Status',
      'sponsor_name': 'Sponsor Name',
      'neighborhood': 'Neighborhood',
      'zipcode': 'Zip Code',
      'city': 'City',
      'state': 'State',
      'region': 'Region',
      'address': 'Address',
      'min_deal_size': 'Minimum Deal Size',
      'max_deal_size': 'Maximum Deal Size',
      'min_loan_amount': 'Minimum Loan Amount',
      'max_loan_amount': 'Maximum Loan Amount',
      'loan_to_cost_max': 'Max Loan to Cost',
      'loan_to_value_max': 'Max Loan to Value',
      'interest_rate_min': 'Minimum Interest Rate',
      'interest_rate_max': 'Maximum Interest Rate',
      'min_equity_requirement': 'Minimum Equity Requirement',
      'max_equity_requirement': 'Maximum Equity Requirement',
      'min_irr_target': 'Minimum IRR Target',
      'max_irr_target': 'Maximum IRR Target',
      'min_cash_yield': 'Minimum Cash Yield',
      'max_cash_yield': 'Maximum Cash Yield'
    };
    
    return displayNames[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Fields to exclude from UI (as requested by user)
  const excludedFields = [
    // Universal Weights - removed fields
    'num_apartment_units', // Numbers of Apartments
    
    // Debt Criteria - removed fields
    'loan_to_cost_min',
    'loan_to_value_min', 
    'amortization',
    'max_loan_dscr',
    'loan_exit_fee_max',
    'loan_origination_max_fee',
    'prepayment',
    'rate_lock',
    'application_deposit',
    'eligible_borrower',
    'future_facilities',
    'good_faith_deposit',
    'debt_program_overview',
    
    // Equity Criteria - removed fields
    'max_hold_period_years',
    'ownership_requirement',
    'proof_of_funds_requirement',
    'equity_program_overview'
  ];

  const calculateTotalWeight = (tableName: string): number => {
    const filteredWeights = weights.filter(w => w.table_name === tableName && !excludedFields.includes(w.field_name));
    
    const total = filteredWeights.reduce((sum, weight) => {
      const key = `${weight.table_name}_${weight.field_name}`;
      let currentWeight = 0;
      
      if (editingWeights[key] !== undefined) {
        // Use raw percentage value from editing
        currentWeight = parseFloat(editingWeights[key]) || 0;
      } else {
        // Convert decimal from database to percentage
        currentWeight = (parseFloat(String(weight.weight)) * 100) || 0;
      }
      
      return sum + currentWeight;
    }, 0);
    
    return total;
  };

  const calculateScenarioTotals = () => {
    const dealPropertyWeights = calculateTotalWeight('dealsv2') + calculateTotalWeight('properties');
    const debtCriteria = calculateTotalWeight('investment_criteria_debt');
    const equityCriteria = calculateTotalWeight('investment_criteria_equity');
    const totalSystem = dealPropertyWeights + debtCriteria + equityCriteria;
    
    return { dealPropertyWeights, debtCriteria, equityCriteria, totalSystem };
  };

  const validateWeights = () => {
    const { dealPropertyWeights, debtCriteria, equityCriteria } = calculateScenarioTotals();
    const remainingPercentage = 100 - dealPropertyWeights; // Raw percentage format
    const errors: {debt: string | null, equity: string | null} = {debt: null, equity: null};

    // Check if debt or equity exceed the remaining percentage
    if (debtCriteria > remainingPercentage + 1) {
      errors.debt = `Debt exceeds remaining ${Math.round(remainingPercentage)}%`;
    }

    if (equityCriteria > remainingPercentage + 1) {
      errors.equity = `Equity exceeds remaining ${Math.round(remainingPercentage)}%`;
    }

    setValidationErrors(errors);
  };

  const handleSaveAllWeights = async () => {
    try {
      setSaving(true);
      const { dealPropertyWeights, debtCriteria, equityCriteria } = calculateScenarioTotals();
      const remainingPercentage = 100 - dealPropertyWeights; // 100% - universal% (raw percentage)
      
      // Auto-adjust debt and equity if they exceed remaining percentage
      let adjustedWeights = { ...editingWeights };
      
      if (debtCriteria > remainingPercentage) {
        // Proportionally reduce debt fields
        const debtFields = weights.filter(w => w.table_name === 'investment_criteria_debt' && !excludedFields.includes(w.field_name));
        const reductionFactor = remainingPercentage / debtCriteria;
        
        console.log(`🔧 Auto-reducing debt fields: ${debtCriteria.toFixed(1)}% → ${remainingPercentage.toFixed(1)}% (factor: ${reductionFactor.toFixed(3)})`);
        
        debtFields.forEach(weight => {
          const key = `${weight.table_name}_${weight.field_name}`;
          const currentWeight = adjustedWeights[key] !== undefined ? parseFloat(adjustedWeights[key]) : parseFloat(String(weight.weight)) * 100;
          const newWeight = currentWeight * reductionFactor;
          adjustedWeights[key] = newWeight.toString();
          console.log(`  ${weight.field_name}: ${currentWeight.toFixed(1)}% → ${newWeight.toFixed(1)}%`);
        });
      }
      
      if (equityCriteria > remainingPercentage) {
        // Proportionally reduce equity fields
        const equityFields = weights.filter(w => w.table_name === 'investment_criteria_equity' && !excludedFields.includes(w.field_name));
        const reductionFactor = remainingPercentage / equityCriteria;
        
        console.log(`🔧 Auto-reducing equity fields: ${equityCriteria.toFixed(1)}% → ${remainingPercentage.toFixed(1)}% (factor: ${reductionFactor.toFixed(3)})`);
        
        equityFields.forEach(weight => {
          const key = `${weight.table_name}_${weight.field_name}`;
          const currentWeight = adjustedWeights[key] !== undefined ? parseFloat(adjustedWeights[key]) : parseFloat(String(weight.weight)) * 100;
          const newWeight = currentWeight * reductionFactor;
          adjustedWeights[key] = newWeight.toString();
          console.log(`  ${weight.field_name}: ${currentWeight.toFixed(1)}% → ${newWeight.toFixed(1)}%`);
        });
      }
      
      // Update editing weights with adjustments
      setEditingWeights(adjustedWeights);

      // Prepare weights for saving - API expects { updates: [...] } format
      // Convert string percentages to decimal values for database
      const updates = weights.map(weight => {
        const key = `${weight.table_name}_${weight.field_name}`;
        // Convert string percentage to decimal
        const percentageValue = parseFloat(adjustedWeights[key]) || 0;
        const decimalWeight = percentageValue / 100;
        
        return {
          capital_position: weight.capital_position,
          field_name: weight.field_name,
          weight: decimalWeight, // Convert to decimal for API
          description: weight.description,
          table_name: weight.table_name,
          field_category: weight.field_category
        };
      });

      console.log('💾 Saving weights with auto-adjustments:', updates.slice(0, 3)); // Log first 3 for debugging


      
      const response = await fetch('/api/dashboard/configuration/capital-position-weights', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          toast({
            title: 'Success',
            description: 'Capital position weights updated successfully',
          });
          setIsEditing(false);
          setEditingWeights({});
          setValidationErrors({debt: null, equity: null});
          fetchData(); // Refresh data
        }
      }
    } catch (error) {
      console.error('Error saving weights:', error);
      toast({
        title: 'Error',
        description: 'Failed to save capital position weights',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const getPercentageColor = (percentage: number): string => {
    if (percentage >= 95 && percentage <= 105) return 'text-green-600';
    if (percentage >= 90 && percentage <= 110) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Filter fields by table and exclude removed fields
  const dealFields = weights.filter(w => w.table_name === 'dealsv2' && !excludedFields.includes(w.field_name));
  const debtFields = weights.filter(w => w.table_name === 'investment_criteria_debt' && !excludedFields.includes(w.field_name));
  const equityFields = weights.filter(w => w.table_name === 'investment_criteria_equity' && !excludedFields.includes(w.field_name));
  const propertyFields = weights.filter(w => w.table_name === 'properties' && !excludedFields.includes(w.field_name));

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-lg">Loading capital position weights...</span>
        </div>
      </div>
    );
  }

  // Show message if no weights found
  if (!loading && weights.length === 0) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Weights Found</h2>
          <p className="text-gray-600 mb-4">
            No capital position weights found in the database. This might indicate a configuration issue.
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            <p>• Check if the database table exists</p>
            <p>• Verify API endpoint is working</p>
            <p>• Check browser console for errors</p>
          </div>
          <Button onClick={fetchData} className="mt-4">
            Retry Loading
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Capital Position Weights (100% System)</h1>
          <p className="text-gray-600 mt-2">
            Manage field weights for deal matching. Weights are normalized to 100% for each scenario: Deal + Property, Debt Fields, Equity Fields. Total System must equal 100%.
          </p>
        </div>
        <Badge variant="outline" className="flex items-center space-x-1">
          <Settings className="h-3 w-3" />
          <span>100% Validation System</span>
        </Badge>
      </div>

      {/* Validation Alerts */}
      {isEditing && (validationErrors.debt || validationErrors.equity) && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <h3 className="font-semibold text-red-800">Validation Errors</h3>
          </div>
          <div className="mt-2 space-y-1">
            {validationErrors.debt && (
              <p className="text-red-700 text-sm">{validationErrors.debt}</p>
            )}
            {validationErrors.equity && (
              <p className="text-red-700 text-sm">{validationErrors.equity}</p>
            )}
          </div>
          <p className="text-red-600 text-sm mt-2">Please adjust weights so the total system equals exactly 100%.</p>
        </div>
      )}

      {/* Combination Validation Messages */}
      {isEditing && (
        <div className="space-y-2">
          {Math.abs((calculateScenarioTotals().dealPropertyWeights + calculateScenarioTotals().debtCriteria) - 100) > 1 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">
                  Universal + Debt = {Math.round(calculateScenarioTotals().dealPropertyWeights + calculateScenarioTotals().debtCriteria)}% (should equal 100%)
                </span>
              </div>
            </div>
          )}
          {Math.abs((calculateScenarioTotals().dealPropertyWeights + calculateScenarioTotals().equityCriteria) - 100) > 1 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">
                  Universal + Equity = {Math.round(calculateScenarioTotals().dealPropertyWeights + calculateScenarioTotals().equityCriteria)}% (should equal 100%)
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Auto-Reduction Notice */}
      {isEditing && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Auto-Reduction Notice:</p>
              <p>
                If Debt or Equity fields exceed the remaining percentage (100% - Universal%), 
                they will be automatically reduced proportionally when you save. 
                For example, if Universal = 60% and Debt = 50%, Debt will be reduced to 40% 
                and each debt field will be scaled down proportionally.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Real-time Totals Display */}


      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Deal + Property</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateScenarioTotals().dealPropertyWeights)}`}>
              {Math.round(calculateScenarioTotals().dealPropertyWeights)}%
            </div>
            <p className="text-xs text-gray-500">{dealFields.length + propertyFields.length} fields</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Debt Fields</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateTotalWeight('investment_criteria_debt'))}`}>
              {Math.round(calculateTotalWeight('investment_criteria_debt'))}%
            </div>
            <p className="text-xs text-gray-500">{debtFields.length} fields</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Equity Fields</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateTotalWeight('investment_criteria_equity'))}`}>
              {Math.round(calculateTotalWeight('investment_criteria_equity'))}%
            </div>
            <p className="text-xs text-gray-500">{equityFields.length} fields</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Universal + Debt</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateScenarioTotals().dealPropertyWeights + calculateScenarioTotals().debtCriteria)}`}>
              {Math.round(calculateScenarioTotals().dealPropertyWeights + calculateScenarioTotals().debtCriteria)}%
            </div>
            <p className="text-xs text-gray-500">Must equal 100%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Universal + Equity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateScenarioTotals().dealPropertyWeights + calculateScenarioTotals().equityCriteria)}`}>
              {Math.round(calculateScenarioTotals().dealPropertyWeights + calculateScenarioTotals().equityCriteria)}%
            </div>
            <p className="text-xs text-gray-500">Must equal 100%</p>
          </CardContent>
        </Card>
      </div>



      {/* Action Buttons */}
      <div className="flex items-center justify-center space-x-4 py-4 border-t border-b">
        {!isEditing ? (
          <Button onClick={handleStartEditing} className="flex items-center space-x-2">
            <Edit className="h-4 w-4" />
            <span>Edit All Weights</span>
          </Button>
        ) : (
          <div className="flex items-center space-x-4">
            <Button 
              onClick={handleSaveAllWeights} 
              disabled={saving}
              className="flex items-center space-x-2"
            >
              {saving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
              <span>Save All Changes</span>
            </Button>
            <Button variant="outline" onClick={handleCancelEdit}>
              Cancel
            </Button>
            <div className="flex items-center space-x-2 border-l pl-4">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => normalizeWeightsInCategory('investment_criteria_debt')}
                className="text-xs"
              >
                Normalize Debt
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => normalizeWeightsInCategory('investment_criteria_equity')}
                className="text-xs"
              >
                Normalize Equity
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Tab-Based Layout */}
      <Tabs defaultValue="universal" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="universal" className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>Deal + Property</span>
            <Badge variant="secondary" className="ml-2">
              {Math.round(calculateScenarioTotals().dealPropertyWeights)}%
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="debt" className="flex items-center space-x-2">
            <CreditCard className="h-4 w-4" />
            <span>Debt Fields</span>
            <Badge variant="secondary" className="ml-2">
              {Math.round(calculateScenarioTotals().debtCriteria)}%
            </Badge>
            {calculateScenarioTotals().debtCriteria > (100 - calculateScenarioTotals().dealPropertyWeights) + 1 && (
              <AlertCircle className="h-4 w-4 text-yellow-500" />
            )}
          </TabsTrigger>
          <TabsTrigger value="equity" className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Equity Fields</span>
            <Badge variant="secondary" className="ml-2">
              {Math.round(calculateScenarioTotals().equityCriteria)}%
            </Badge>
            {calculateScenarioTotals().equityCriteria > (100 - calculateScenarioTotals().dealPropertyWeights) + 1 && (
              <AlertCircle className="h-4 w-4 text-yellow-500" />
            )}
          </TabsTrigger>
        </TabsList>

        {/* Universal Weights Tab - Deal + Property Combined */}
        <TabsContent value="universal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-blue-600" />
                <span>Deal Fields</span>
                <Badge variant="outline">{dealFields.length} fields</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {dealFields.map((field) => (
                <div key={field.field_name} className="flex items-center justify-between p-3 border rounded-lg bg-blue-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">{getFieldDisplayName(field.field_name)}</span>
                      {isEditing ? (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={getInputValue(field.field_name, field.table_name)}
                            onChange={(e) => handleInputChange(field.field_name, field.table_name, e.target.value)}


                            className="w-20 h-8 text-sm"
                          />
                          <span className="text-sm text-gray-500">%</span>
                        </div>
                      ) : (
                        <Badge variant="outline">
                          {Math.round(field.weight * 100)}%
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Home className="h-5 w-5 text-purple-600" />
                <span>Property Fields</span>
                <Badge variant="outline">{propertyFields.length} fields</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {propertyFields.map((field) => (
                <div key={field.field_name} className="flex items-center justify-between p-3 border rounded-lg bg-purple-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">{getFieldDisplayName(field.field_name)}</span>
                      {isEditing ? (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={getInputValue(field.field_name, field.table_name)}
                            onChange={(e) => handleInputChange(field.field_name, field.table_name, e.target.value)}


                            className="w-20 h-8 text-sm"
                          />
                          <span className="text-sm text-gray-500">%</span>
                        </div>
                      ) : (
                        <Badge variant="outline">
                          {Math.round(field.weight * 100)}%
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Debt Criteria Tab */}
        <TabsContent value="debt" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-5 w-5 text-red-600" />
                <span>Debt Fields</span>
                <Badge variant="outline">{debtFields.length} fields</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {debtFields.map((field) => (
                <div key={field.field_name} className="flex items-center justify-between p-3 border rounded-lg bg-red-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">{getFieldDisplayName(field.field_name)}</span>
                      {isEditing ? (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={getInputValue(field.field_name, field.table_name)}
                            onChange={(e) => handleInputChange(field.field_name, field.table_name, e.target.value)}


                            className="w-20 h-8 text-sm"
                          />
                          <span className="text-sm text-gray-500">%</span>
                        </div>
                      ) : (
                        <Badge variant="outline">
                          {Math.round(field.weight * 100)}%
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Equity Criteria Tab */}
        <TabsContent value="equity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span>Equity Fields</span>
                <Badge variant="outline">{equityFields.length} fields</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {equityFields.map((field) => (
                <div key={field.field_name} className="flex items-center justify-between p-3 border rounded-lg bg-green-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">{getFieldDisplayName(field.field_name)}</span>
                      {isEditing ? (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={getInputValue(field.field_name, field.table_name)}
                            onChange={(e) => handleInputChange(field.field_name, field.table_name, e.target.value)}


                            className="w-20 h-8 text-sm"
                          />
                          <span className="text-sm text-gray-500">%</span>
                        </div>
                      ) : (
                        <Badge variant="outline">
                          {Math.round(field.weight * 100)}%
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>


    </div>
  );
}
