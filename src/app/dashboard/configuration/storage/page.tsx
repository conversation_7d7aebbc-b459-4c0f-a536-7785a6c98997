'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Cloud, 
  HardDrive, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Settings
} from 'lucide-react';

interface StorageProvider {
  name: string;
  displayName: string;
  isActive: boolean;
  isDefault: boolean;
  status: 'connected' | 'disconnected' | 'error';
  config: any;
}

export default function StorageConfigurationPage() {
  const [providers, setProviders] = useState<StorageProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [migrating, setMigrating] = useState(false);

  useEffect(() => {
    loadStorageProviders();
  }, []);

  const loadStorageProviders = async () => {
    try {
      setLoading(true);
      // This would typically come from an API endpoint
      const mockProviders: StorageProvider[] = [
        {
          name: 'local',
          displayName: 'Local File System',
          isActive: true,
          isDefault: false,
          status: 'connected',
          config: { base_path: 'file_storage' }
        },
        {
          name: 'azure',
          displayName: 'Azure Blob Storage',
          isActive: !!process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING,
          isDefault: true,
          status: process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING ? 'connected' : 'disconnected',
          config: { 
            container: process.env.NEXT_PUBLIC_AZURE_STORAGE_CONTAINER || 'files',
            account_name: process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING?.split(';')[1]?.split('=')[1] || 'Not configured'
          }
        }
      ];
      setProviders(mockProviders);
    } catch (error) {
      console.error('Error loading storage providers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMigrateAllToAzure = async () => {
    try {
      setMigrating(true);
      const response = await fetch('/api/migration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sourceProvider: 'local',
          targetProvider: 'azure',
          batchSize: 10
        })
      });

      const data = await response.json();
      if (data.success) {
        alert(`Migration started successfully. Job ID: ${data.jobId}. Check the processing queue for progress.`);
      } else {
        alert(`Migration failed: ${data.message}`);
      }
    } catch (error) {
      console.error('Migration error:', error);
      alert('Migration failed');
    } finally {
      setMigrating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'disconnected':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-green-100 text-green-800';
      case 'disconnected':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading storage configuration...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Storage Configuration</h1>
        <Button 
          onClick={handleMigrateAllToAzure}
          disabled={migrating || !providers.find(p => p.name === 'azure')?.isActive}
          className="flex items-center gap-2"
        >
          <Cloud className="h-4 w-4" />
          {migrating ? 'Migrating...' : 'Migrate All to Azure'}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {providers.map((provider) => (
          <Card key={provider.name} className="relative">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  {provider.name === 'azure' ? (
                    <Cloud className="h-5 w-5 text-blue-500" />
                  ) : (
                    <HardDrive className="h-5 w-5 text-gray-500" />
                  )}
                  {provider.displayName}
                </CardTitle>
                <div className="flex items-center gap-2">
                  {provider.isDefault && (
                    <Badge variant="default">Default</Badge>
                  )}
                  <Badge className={getStatusColor(provider.status)}>
                    {getStatusIcon(provider.status)}
                    <span className="ml-1 capitalize">{provider.status}</span>
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm text-gray-600">
                  <strong>Status:</strong> {provider.isActive ? 'Active' : 'Inactive'}
                </div>
                {provider.name === 'azure' && (
                  <>
                    <div className="text-sm text-gray-600">
                      <strong>Container:</strong> {provider.config.container}
                    </div>
                    <div className="text-sm text-gray-600">
                      <strong>Account:</strong> {provider.config.account_name}
                    </div>
                  </>
                )}
                {provider.name === 'local' && (
                  <div className="text-sm text-gray-600">
                    <strong>Base Path:</strong> {provider.config.base_path}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Environment Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold mb-2">Required Environment Variables:</h4>
              <div className="space-y-2 text-sm font-mono">
                <div>
                  <code className="bg-white px-2 py-1 rounded">AZURE_STORAGE_CONNECTION_STRING</code>
                  <span className="ml-2 text-gray-600">
                    {process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING ? '✅ Set' : '❌ Not set'}
                  </span>
                </div>
                <div>
                  <code className="bg-white px-2 py-1 rounded">AZURE_STORAGE_CONTAINER</code>
                  <span className="ml-2 text-gray-600">
                    {process.env.NEXT_PUBLIC_AZURE_STORAGE_CONTAINER ? '✅ Set' : '❌ Not set'}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold mb-2">Setup Instructions:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-700">
                <li>Create an Azure Storage Account</li>
                <li>Create a container named "files" (or update the environment variable)</li>
                <li>Get the connection string from Azure Portal</li>
                <li>Add the connection string to your .env.local file</li>
                <li>Restart your application</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
