import { pool } from '../lib/db';
import { StorageProviderRegistry } from '../lib/storage/StorageProviderRegistry';

async function fixMigratedFileUrls() {
  console.log('Starting to fix migrated file URLs...');
  
  try {
    // Get all files that are stored in Azure but have "unknown" in their metadata
    const result = await pool.query(`
      SELECT file_id, storage_path, storage_metadata, original_name
      FROM files 
      WHERE storage_provider = 'azure' 
      AND (
        storage_metadata->>'account_name' = 'unknown' 
        OR storage_metadata->>'actual_storage_url' IS NULL
        OR storage_metadata->>'actual_storage_url' LIKE '%unknown%'
      )
    `);
    
    console.log(`Found ${result.rows.length} files with incorrect URLs`);
    
    if (result.rows.length === 0) {
      console.log('No files need fixing.');
      return;
    }
    
    // Get Azure storage provider
    const registry = StorageProviderRegistry.getInstance();
    
    // Wait a bit for async initialization
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const azureProvider = registry.getProvider('azure');
    
    if (!azureProvider) {
      console.error('Azure storage provider not found. Available providers:', registry.getProviderNames());
      return;
    }
    
    // Get storage info from Azure provider
    const storageInfo = (azureProvider as any).getStorageInfo();
    console.log('Azure storage info:', storageInfo);
    
    let fixedCount = 0;
    
    for (const file of result.rows) {
      try {
        console.log(`Fixing file: ${file.original_name}`);
        
        // Get the actual URL from Azure
        const actualUrl = await azureProvider.getFileUrl(file.storage_path);
        console.log(`Actual URL: ${actualUrl}`);
        
        // Update the file metadata
        const updatedMetadata = {
          ...file.storage_metadata,
          account_name: storageInfo.accountName,
          container: storageInfo.containerName,
          actual_storage_url: actualUrl,
          fixed_at: new Date().toISOString()
        };
        
        await pool.query(
          `UPDATE files 
           SET storage_metadata = $1, updated_at = CURRENT_TIMESTAMP
           WHERE file_id = $2`,
          [JSON.stringify(updatedMetadata), file.file_id]
        );
        
        console.log(`✅ Fixed: ${file.original_name}`);
        fixedCount++;
        
      } catch (error) {
        console.error(`❌ Failed to fix ${file.original_name}:`, error.message);
      }
    }
    
    console.log(`\nFixed ${fixedCount} out of ${result.rows.length} files`);
    
  } catch (error) {
    console.error('Error fixing migrated file URLs:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
fixMigratedFileUrls().catch(console.error);
