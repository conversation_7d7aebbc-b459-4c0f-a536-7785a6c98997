import { Pool } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '../../.env' });

// Database connection (using same config as existing codebase)
const pool = new Pool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || "5432"),
  database: process.env.DB_DATABASE,
  connectionTimeoutMillis: 10000,
  idleTimeoutMillis: 30000,
  max: 15,
  allowExitOnIdle: true,
});

// Region mappings for unmapped regions
const REGION_MAPPINGS: Record<string, string> = {
  'West': 'Western US',
  'North': 'Northeast',
  'South': 'Southeast',
  'East': 'East Coast',
  'National': 'Nationwide',
  'Sunbelt': 'Sun Belt',
  'Southeastern US': 'Southeast',
  'Southeastern United States': 'Southeast',
  'Northeastern US': 'Northeast',
  'Southern US': 'South',
  'Mountain West': 'Rocky Mountain West',
  'New York City': 'Northeast',
  'New York City Metropolitan Area': 'Northeast',
  'New York Metropolitan Area': 'Northeast',
  'Texas': 'Southwest', // Texas is a state, but if used as region, map to Southwest
  'California': 'West Coast', // California is a state, but if used as region, map to West Coast
  'Florida': 'Southeast', // Florida is a state, but if used as region, map to Southeast
  'New York': 'Northeast', // New York is a state, but if used as region, map to Northeast
};

interface RegionStateMapping {
  region_name: string;
  states: string[];
}

interface InvestmentCriteria {
  investment_criteria_id: number;
  entity_type: string;
  entity_id: number;
  region: string[] | null;
  state: string[] | null;
}

class GeographicStateFixer {
  private regionToStatesMap: Map<string, string[]> = new Map();

  /**
   * Initialize by loading region-state mappings from central_mapping table
   */
  async initialize(): Promise<void> {
    const client = await pool.connect();
    try {
      const query = `
        SELECT 
          value_1 as region_name,
          ARRAY_AGG(value_2 ORDER BY value_2) as states
        FROM central_mapping 
        WHERE type = 'U.S Regions' 
          AND is_active = true 
          AND value_2 IS NOT NULL
        GROUP BY value_1
      `;
      
      const result = await client.query<RegionStateMapping>(query);
      
      // Store in map for quick lookup
      result.rows.forEach(row => {
        this.regionToStatesMap.set(row.region_name, row.states);
      });
      
      console.log(`✅ Loaded ${this.regionToStatesMap.size} region mappings from central_mapping`);
    } finally {
      client.release();
    }
  }

  /**
   * Map unmapped regions to valid regions
   */
  private mapRegion(region: string): string {
    return REGION_MAPPINGS[region] || region;
  }

  /**
   * Get all states for a given set of regions
   * Combines ALL states from ALL regions (replaces existing states, doesn't append)
   * Special case: If "Nationwide" is present, includes ALL states from ALL regions
   */
  private getStatesForRegions(regions: string[]): string[] {
    const allStates = new Set<string>();
    
    // Check if "Nationwide" is in the regions
    const hasNationwide = regions.some(region => 
      region.toLowerCase() === 'nationwide' || 
      this.mapRegion(region).toLowerCase() === 'nationwide'
    );
    
    if (hasNationwide) {
      // If Nationwide is present, get ALL states from ALL regions
      console.log(`   🌍 Nationwide detected - including ALL states from ALL regions`);
      for (const [regionName, states] of this.regionToStatesMap.entries()) {
        states.forEach(state => allStates.add(state));
      }
      console.log(`   ✓ Added all states from all ${this.regionToStatesMap.size} regions (${allStates.size} total states)`);
    } else {
      // Normal case: combine states from specified regions only
      for (const region of regions) {
        // First try to map the region if it's unmapped
        const mappedRegion = this.mapRegion(region);
        
        // Get states for this region
        const states = this.regionToStatesMap.get(mappedRegion);
        if (states) {
          // Add ALL states from this region to the combined set
          states.forEach(state => allStates.add(state));
          console.log(`   ✓ Region "${region}" → "${mappedRegion}" (${states.length} states)`);
        } else {
          console.log(`⚠️  No states found for region: ${region} (mapped to: ${mappedRegion})`);
        }
      }
    }
    
    // Return sorted array of unique states (this REPLACES existing states)
    return Array.from(allStates).sort();
  }

  /**
   * Check if any of the test companies have Nationwide regions
   */
  async checkForNationwide(companyIds: number[]): Promise<void> {
    const client = await pool.connect();
    try {
      const query = `
        SELECT 
          investment_criteria_id,
          entity_id,
          region
        FROM investment_criteria_central
        WHERE entity_id = ANY($1)
          AND entity_type = 'company'
          AND region IS NOT NULL
          AND array_length(region, 1) > 0
          AND 'Nationwide' = ANY(region)
      `;
      
      const result = await client.query(query, [companyIds]);
      
      if (result.rows.length > 0) {
        console.log(`   🌍 Found ${result.rows.length} companies with Nationwide regions:`);
        result.rows.forEach(row => {
          console.log(`   - Company ${row.entity_id}: ${row.region.join(', ')}`);
        });
      } else {
        console.log(`   ℹ️  No companies with Nationwide regions in test set`);
      }
    } finally {
      client.release();
    }
  }

  /**
   * Fix states for specific companies (for testing)
   */
  async fixStatesForCompanies(companyIds: number[]): Promise<void> {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      // Get current criteria for these companies
      const query = `
        SELECT 
          investment_criteria_id,
          entity_type,
          entity_id,
          region,
          state
        FROM investment_criteria_central
        WHERE entity_id = ANY($1)
          AND entity_type = 'company'
          AND region IS NOT NULL
          AND array_length(region, 1) > 0
      `;
      
      const result = await client.query<InvestmentCriteria>(query, [companyIds]);
      
      console.log(`\n📊 Found ${result.rows.length} investment criteria records for ${companyIds.length} companies\n`);
      
      let updatedCount = 0;
      
      for (const record of result.rows) {
        if (!record.region || record.region.length === 0) continue;
        
        // Calculate what states should be based on regions
        const expectedStates = this.getStatesForRegions(record.region);
        
        // Check if update is needed
        const currentStates = record.state || [];
        const needsUpdate = JSON.stringify(currentStates.sort()) !== JSON.stringify(expectedStates);
        
        if (needsUpdate) {
          console.log(`\n🔧 Company ${record.entity_id} (ID: ${record.investment_criteria_id})`);
          console.log(`   Regions: ${record.region.join(', ')}`);
          console.log(`   Current states (${currentStates.length}): ${currentStates.slice(0, 5).join(', ')}${currentStates.length > 5 ? '...' : ''}`);
          console.log(`   → REPLACING with combined states from all regions:`);
          console.log(`   New states (${expectedStates.length}): ${expectedStates.slice(0, 5).join(', ')}${expectedStates.length > 5 ? '...' : ''}`);
          
          // Update the record
          const updateQuery = `
            UPDATE investment_criteria_central 
            SET state = $1
            WHERE investment_criteria_id = $2
          `;
          
          await client.query(updateQuery, [expectedStates, record.investment_criteria_id]);
          updatedCount++;
        }
      }
      
      await client.query('COMMIT');
      console.log(`\n✅ Updated ${updatedCount} out of ${result.rows.length} records`);
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Fix states for ALL investment criteria records
   */
  async fixStatesForAll(): Promise<void> {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      // Get all criteria with regions
      const query = `
        SELECT 
          investment_criteria_id,
          entity_type,
          entity_id,
          region,
          state
        FROM investment_criteria_central
        WHERE region IS NOT NULL
          AND array_length(region, 1) > 0
      `;
      
      const result = await client.query<InvestmentCriteria>(query);
      
      console.log(`\n📊 Found ${result.rows.length} total investment criteria records with regions\n`);
      
      let updatedCount = 0;
      let processedCount = 0;
      
      for (const record of result.rows) {
        processedCount++;
        
        if (processedCount % 1000 === 0) {
          console.log(`   Processing... ${processedCount}/${result.rows.length}`);
        }
        
        if (!record.region || record.region.length === 0) continue;
        
        // Calculate what states should be based on regions (REPLACES existing states)
        const expectedStates = this.getStatesForRegions(record.region);
        
        // Check if update is needed
        const currentStates = record.state || [];
        const needsUpdate = JSON.stringify(currentStates.sort()) !== JSON.stringify(expectedStates);
        
        if (needsUpdate) {
          // Update the record (REPLACE states with combined states from all regions)
          const updateQuery = `
            UPDATE investment_criteria_central 
            SET state = $1
            WHERE investment_criteria_id = $2
          `;
          
          await client.query(updateQuery, [expectedStates.length > 0 ? expectedStates : null, record.investment_criteria_id]);
          updatedCount++;
        }
      }
      
      await client.query('COMMIT');
      console.log(`\n✅ Updated ${updatedCount} out of ${result.rows.length} total records`);
      
      // Show summary
      const summaryQuery = `
        SELECT 
          COUNT(*) as total_records,
          COUNT(DISTINCT entity_id) as unique_entities,
          COUNT(DISTINCT entity_type) as entity_types
        FROM investment_criteria_central
        WHERE region IS NOT NULL 
          AND array_length(region, 1) > 0
      `;
      
      const summaryResult = await client.query(summaryQuery);
      console.log('\n📈 Summary:', summaryResult.rows[0]);
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Error:', error);
      throw error;
    } finally {
      client.release();
    }
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting Geographic State Fixer\n');
    
    // Test database connection first
    console.log('🔌 Testing database connection...');
    const testClient = await pool.connect();
    try {
      const testResult = await testClient.query('SELECT NOW() as current_time');
      console.log(`✅ Database connected: ${testResult.rows[0].current_time}`);
    } finally {
      testClient.release();
    }
    
    const fixer = new GeographicStateFixer();
    await fixer.initialize();
    
    // Test with 10 known companies first
    console.log('\n' + '='.repeat(60));
    console.log('STEP 1: Testing with 10 known companies');
    console.log('='.repeat(60));
    
    const testCompanyIds = [
      278022, // Has Southeast, Northeast, West, Central US
      307072, // Has Northeast, Mid-Atlantic, individual states mixed
      347053, // Has Sunbelt
      332620, // Has New York Metropolitan Area, Southeastern United States, Sunbelt
      343307, // Has multiple regions including Sunbelt
      365627, // Has South, Southeast, Florida
      275478, // Known company with Midwest
      296978, // Has Southeast, West Coast
      28,     // Deal with Northeast
      1       // First company
    ];
    
    // Let's also check if any of these companies have Nationwide
    console.log('\n🔍 Checking test companies for Nationwide regions...');
    await fixer.checkForNationwide(testCompanyIds);
    
    await fixer.fixStatesForCompanies(testCompanyIds);
    
    // console.log('\n' + '='.repeat(60));
    console.log('✅ TEST COMPLETED - Only processed test company IDs');
    // console.log('='.repeat(60));
    console.log('The script has only processed the 10 test companies:');
    console.log(testCompanyIds.join(', '));
    console.log('\nTo run for ALL records, uncomment the line below in the code:');
    console.log('// await fixer.fixStatesForAll();');
    
    // Uncomment this line to run for all records
    await fixer.fixStatesForAll();
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the script
main().catch(console.error);

export { GeographicStateFixer };
