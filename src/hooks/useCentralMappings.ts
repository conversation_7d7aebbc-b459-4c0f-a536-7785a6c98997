import { useState, useEffect } from 'react';

interface CentralMapping {
  type: string;
  level_1: string;
  value_1: string;
  level_2?: string;
  value_2?: string;
  level_3?: string;
  value_3?: string;
}

interface UseCentralMappingsReturn {
  strategyOptions: string[];
  propertyTypeOptions: string[];
  subpropertyTypeOptions: string[];
  capitalPositionOptions: string[];
  regionOptions: string[];
  stateOptions: string[];
  regionStateMap: { [key: string]: string[] };
  loading: boolean;
  error: string | null;
  refresh: () => void;
  updateSubpropertyTypes: (propertyType?: string) => Promise<void>;
}

export const useCentralMappings = (): UseCentralMappingsReturn => {
  const [strategyOptions, setStrategyOptions] = useState<string[]>([]);
  const [propertyTypeOptions, setPropertyTypeOptions] = useState<string[]>([]);
  const [subpropertyTypeOptions, setSubpropertyTypeOptions] = useState<string[]>([]);
  const [capitalPositionOptions, setCapitalPositionOptions] = useState<string[]>([]);
  const [regionOptions, setRegionOptions] = useState<string[]>([]);
  const [stateOptions, setStateOptions] = useState<string[]>([]);
  const [regionStateMap, setRegionStateMap] = useState<{ [key: string]: string[] }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fallback options in case central mapping table is empty
  const fallbackStrategies = [
    "Core",
    "Core Plus", 
    "Value-Add",
    "Opportunistic"
  ];

  const fallbackPropertyTypes = [
    "Multi-Family",
    "Office", 
    "Retail",
    "Industrial",
    "Hospitality",
    "Mixed-Use",
    "Land"
  ];

  const fallbackCapitalPositions = [
    "Senior Debt",
    "Mezzanine",
    "Common Equity",
    "General Partner (GP)",
    "Limited Partner (LP)",
    "Stretch Senior",
    "Co-GP",
    "Joint Venture (JV)"
  ];

  const fetchAllMappings = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch all mapping types in parallel
      const [strategyResponse, propertyTypeResponse, capitalPositionResponse, regionsResponse] = await Promise.all([
        fetch('/api/central-mappings/strategy'),
        fetch('/api/central-mappings/property-type'),
        fetch('/api/central-mappings/capital-position'),
        fetch('/api/central-mappings/regions')
      ]);

      // Process strategy options
      if (strategyResponse.ok) {
        const strategyData = await strategyResponse.json();
        const fetchedStrategies = strategyData.strategies || [];
        setStrategyOptions(fetchedStrategies.length > 0 ? fetchedStrategies : fallbackStrategies);
      } else {
        setStrategyOptions(fallbackStrategies);
      }

      // Process property type options
      if (propertyTypeResponse.ok) {
        const propertyTypeData = await propertyTypeResponse.json();
        const fetchedPropertyTypes = propertyTypeData.propertyTypes || [];
        setPropertyTypeOptions(fetchedPropertyTypes.length > 0 ? fetchedPropertyTypes : fallbackPropertyTypes);
      } else {
        setPropertyTypeOptions(fallbackPropertyTypes);
      }

      // Process capital position options
      if (capitalPositionResponse.ok) {
        const capitalPositionData = await capitalPositionResponse.json();
        const fetchedCapitalPositions = capitalPositionData.capitalPositions || [];
        setCapitalPositionOptions(fetchedCapitalPositions.length > 0 ? fetchedCapitalPositions : fallbackCapitalPositions);
      } else {
        setCapitalPositionOptions(fallbackCapitalPositions);
      }

      // Process geographic options
      if (regionsResponse.ok) {
        const regionsData = await regionsResponse.json();
        const fetchedRegions = regionsData.data?.regions || [];
        const fetchedStates = regionsData.data?.states || [];
        const fetchedRegionStateMap = regionsData.data?.regionStateMap || {};
        setRegionOptions(fetchedRegions);
        setStateOptions(fetchedStates);
        setRegionStateMap(fetchedRegionStateMap);
      } else {
        // Set empty arrays as fallback
        setRegionOptions([]);
        setStateOptions([]);
        setRegionStateMap({});
      }

      // Initialize subproperty types with all options
      await fetchSubpropertyTypes();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch mapping options';
      setError(errorMessage);
      console.error('Error fetching mapping options:', err);
      
      // On error, use fallback options to ensure the form still works
      setStrategyOptions(fallbackStrategies);
      setPropertyTypeOptions(fallbackPropertyTypes);
      setCapitalPositionOptions(fallbackCapitalPositions);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch subproperty types based on selected property type
  const fetchSubpropertyTypes = async (propertyType?: string) => {
    try {
      const url = propertyType 
        ? `/api/central-mappings/subproperty-type?propertyType=${encodeURIComponent(propertyType)}`
        : '/api/central-mappings/subproperty-type';
      
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setSubpropertyTypeOptions(data.subpropertyTypes || []);
      } else {
        setSubpropertyTypeOptions([]);
      }
    } catch (err) {
      console.error('Error fetching subproperty types:', err);
      setSubpropertyTypeOptions([]);
    }
  };

  useEffect(() => {
    fetchAllMappings();
  }, []);

  const refresh = () => {
    fetchAllMappings();
  };

  return {
    strategyOptions,
    propertyTypeOptions,
    subpropertyTypeOptions,
    capitalPositionOptions,
    regionOptions,
    stateOptions,
    regionStateMap,
    loading,
    error,
    refresh,
    updateSubpropertyTypes: fetchSubpropertyTypes
  };
};
