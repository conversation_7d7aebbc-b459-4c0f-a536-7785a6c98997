import { useState, useEffect } from 'react'

interface WebPageData {
  id: string
  url: string
  parent_url: string | null
  crawl_depth: number
  relevance_rank: number
  rank_factors: {
    url_structure_score: number
    content_quality_score: number
    page_type_score: number
    investment_criteria_score: number
    financial_numbers_score: number
    business_keywords_score: number
    geographic_relevance_score: number
    url_quality_bonus: number
    total_score: number
  }
  extracted_text: string
  last_scraped_at: string
  created_at: string
}

interface WebPagesResponse {
  success: boolean
  data?: {
    pages: WebPageData[]
    total_pages: number
    top_pages: WebPageData[]
    ranking_summary: {
      highest_score: number
      average_score: number
      total_pages: number
    }
  }
  error?: string
}

export function useCompanyWebPages(companyId: number | null) {
  const [webPages, setWebPages] = useState<WebPageData[]>([])
  const [topPages, setTopPages] = useState<WebPageData[]>([])
  const [rankingSummary, setRankingSummary] = useState<{
    highest_score: number
    average_score: number
    total_pages: number
  } | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!companyId) {
      setWebPages([])
      setTopPages([])
      setRankingSummary(null)
      return
    }

    const fetchWebPages = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const response = await fetch(`/api/companies/${companyId}/web-pages`)
        const data: WebPagesResponse = await response.json()
        
        if (data.success && data.data) {
          setWebPages(data.data.pages)
          setTopPages(data.data.top_pages)
          setRankingSummary(data.data.ranking_summary)
        } else {
          setError(data.error || 'Failed to fetch web pages')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchWebPages()
  }, [companyId])

  return {
    webPages,
    topPages,
    rankingSummary,
    loading,
    error
  }
}
