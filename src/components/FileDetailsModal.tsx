'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { toast } from 'sonner';
import { 
  Download, 
  Trash2, 
  Link, 
  Eye, 
  EyeOff, 
  Calendar,
  HardDrive,
  Cloud,
  Hash,
  FileText,
  Users,
  Building2,
  Handshake,
  Newspaper
} from 'lucide-react';

interface FileDetails {
  file_id: string;
  original_name: string;
  file_name: string;
  title?: string;
  description?: string;
  content_hash: string;
  mime_type: string;
  file_size_bytes: number;
  file_extension?: string;
  uploaded_by?: string;
  uploaded_at: string;
  is_public: boolean;
  access_level: 'private' | 'team' | 'public';
  tags?: string[];
  metadata?: any;
  custom_fields?: any;
  storage_provider: string;
  storage_provider_name: string;
  storage_url: string;
  storage_metadata?: any;
  relationships?: FileRelationship[];
  duplicate_files?: FileDuplicate[];
}

interface FileRelationship {
  relationship_id: string;
  target_table_name: string;
  target_column_name: string;
  target_row_id: string;
  relationship_type?: string;
  relationship_title?: string;
  relationship_notes?: string;
  display_order?: number;
  is_primary?: boolean;
  created_at: string;
  linked_entity_name?: string;
}

interface FileDuplicate {
  file_id: string;
  original_name: string;
  uploaded_at: string;
  uploaded_by?: string;
  relationships_count: number;
}

interface FileDetailsModalProps {
  fileId: string | null;
  isOpen: boolean;
  onClose: () => void;
  onDelete?: (fileId: string) => void;
  onDeleteRelationship?: (relationshipId: string) => void;
}

export function FileDetailsModal({ 
  fileId, 
  isOpen, 
  onClose, 
  onDelete,
  onDeleteRelationship 
}: FileDetailsModalProps) {
  const [file, setFile] = useState<FileDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState(false);
  
  const { showDialog, ConfirmationDialog } = useConfirmationDialog();

  useEffect(() => {
    if (fileId && isOpen) {
      loadFileDetails();
    }
  }, [fileId, isOpen]);

  const loadFileDetails = async () => {
    if (!fileId) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/files/${fileId}/details`);
      const data = await response.json();
      
      if (data.success) {
        setFile(data.file);
      } else {
        console.error('Failed to load file details:', data.message);
      }
    } catch (error) {
      console.error('Error loading file details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (file) {
      window.open(`/api/files/${file.file_id}/smart-download`, '_blank');
    }
  };

  const handleDeleteFile = () => {
    if (!file || !onDelete) return;

    showDialog({
      title: 'Delete File',
      description: `Are you sure you want to delete "${file.original_name}"?\n\nThis will remove the file and all its relationships. If this file is not used elsewhere, it will be permanently deleted.`,
      confirmText: 'Delete File',
      cancelText: 'Cancel',
      variant: 'destructive',
      isLoading: deleting,
      onConfirm: async () => {
        try {
          setDeleting(true);
          const response = await fetch(`/api/files/${file.file_id}`, {
            method: 'DELETE'
          });

          if (response.ok) {
            const result = await response.json();
            
            // Show appropriate success message based on what happened
            if (result.fileDeleted) {
              toast.success(`File "${file.original_name}" has been permanently deleted: ${result.message}`);
            } else if (result.fileOrphaned) {
              toast.warning(`File "${file.original_name}" has been kept as orphaned: ${result.message}`);
            } else {
              toast.success(`File deletion result: ${result.message}`);
            }
            
            onDelete(file.file_id);
            onClose();
          } else {
            const errorData = await response.json();
            toast.error(`Failed to delete file: ${errorData.message || 'Unknown error'}`);
          }
        } catch (error) {
          console.error('Error deleting file:', error);
          toast.error('An error occurred while deleting the file. Please try again.');
        } finally {
          setDeleting(false);
        }
      },
    });
  };

  const handleDeleteRelationship = (relationshipId: string) => {
    if (!onDeleteRelationship) return;

    showDialog({
      title: 'Delete Relationship',
      description: 'Are you sure you want to remove this relationship?\n\nIf this is the last relationship for the file, the file may be deleted or marked as orphaned.',
      confirmText: 'Delete Relationship',
      cancelText: 'Cancel',
      variant: 'destructive',
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/files/relationships/${relationshipId}`, {
            method: 'DELETE'
          });

          if (response.ok) {
            const result = await response.json();
            
            // Show appropriate success message based on what happened
            if (result.fileDeleted) {
              toast.success(`Relationship deleted and file permanently deleted: ${result.message}`);
              if (onDelete && file?.file_id) {
                onDelete(file.file_id); // Close modal if file was deleted
              }
              onClose();
            } else if (result.fileOrphaned) {
              toast.warning(`Relationship deleted. File kept as orphaned: ${result.message}`);
            } else {
              toast.success(`Relationship deleted: ${result.message}`);
            }
            
            onDeleteRelationship(relationshipId);
            loadFileDetails(); // Refresh the file details
          } else {
            const errorData = await response.json();
            toast.error(`Failed to delete relationship: ${errorData.message || 'Unknown error'}`);
          }
        } catch (error) {
          console.error('Error deleting relationship:', error);
          toast.error('An error occurred while deleting the relationship. Please try again.');
        }
      },
    });
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getTableIcon = (tableName: string) => {
    switch (tableName) {
      case 'deals':
      case 'dealsv2':
        return <Handshake className="h-4 w-4" />;
      case 'contacts':
        return <Users className="h-4 w-4" />;
      case 'companies':
        return <Building2 className="h-4 w-4" />;
      case 'articles':
        return <Newspaper className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'public':
        return 'bg-green-100 text-green-800';
      case 'team':
        return 'bg-blue-100 text-blue-800';
      case 'private':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'azure':
        return <Cloud className="h-4 w-4 text-blue-500" />;
      case 'local':
        return <HardDrive className="h-4 w-4 text-gray-500" />;
      default:
        return <HardDrive className="h-4 w-4 text-gray-500" />;
    }
  };

  if (!isOpen || !fileId) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            File Details
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2">Loading file details...</span>
          </div>
        ) : file ? (
          <div className="space-y-6">
            {/* File Header */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold">{file.original_name}</h3>
                    {file.title && (
                      <p className="text-sm text-gray-600 mt-1">{file.title}</p>
                    )}
                    {file.description && (
                      <p className="text-sm text-gray-500 mt-2">{file.description}</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleDownload} size="sm">
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                    {onDelete && (
                      <Button 
                        onClick={handleDeleteFile} 
                        variant="destructive" 
                        size="sm"
                        disabled={deleting}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        {deleting ? 'Deleting...' : 'Delete'}
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Size</label>
                    <p className="text-sm">{formatFileSize(file.file_size_bytes)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Type</label>
                    <p className="text-sm">{file.mime_type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Uploaded</label>
                    <p className="text-sm">{new Date(file.uploaded_at).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Uploaded By</label>
                    <p className="text-sm">{file.uploaded_by || 'Unknown'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Access & Storage Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Access Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Access Level:</span>
                    <Badge className={getAccessLevelColor(file.access_level)}>
                      {file.access_level}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Public:</span>
                    {file.is_public ? (
                      <Eye className="h-4 w-4 text-green-500" />
                    ) : (
                      <EyeOff className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                  {file.tags && file.tags.length > 0 && (
                    <div>
                      <span className="text-sm font-medium">Tags:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {file.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Storage Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Provider:</span>
                    <div className="flex items-center gap-1">
                      {getProviderIcon(file.storage_provider)}
                      <span className="text-sm">{file.storage_provider_name}</span>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Storage URL:</span>
                    <p className="text-xs text-gray-600 font-mono break-all">{file.storage_url}</p>
                  </div>
                  {file.storage_url && (
                    <div>
                      <span className="text-sm font-medium">URL:</span>
                      <p className="text-xs text-blue-600 break-all">{file.storage_url}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Content Hash & Duplicates */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Content Hash & Duplicates
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium">Content Hash:</span>
                    <p className="text-xs text-gray-600 font-mono break-all mt-1">
                      {file.content_hash}
                    </p>
                  </div>
                  {file.duplicate_files && file.duplicate_files.length > 0 && (
                    <div>
                      <span className="text-sm font-medium">Duplicate Files ({file.duplicate_files.length}):</span>
                      <div className="mt-2 space-y-2">
                        {file.duplicate_files.map((duplicate) => (
                          <div key={duplicate.file_id} className="p-2 bg-gray-50 rounded text-sm">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{duplicate.original_name}</p>
                                <p className="text-xs text-gray-500">
                                  Uploaded: {new Date(duplicate.uploaded_at).toLocaleDateString()}
                                  {duplicate.uploaded_by && ` by ${duplicate.uploaded_by}`}
                                </p>
                              </div>
                              <Badge variant="outline">
                                {duplicate.relationships_count} relationships
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Relationships */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Link className="h-4 w-4" />
                  Linked Entities ({file.relationships?.length || 0})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {file.relationships && file.relationships.length > 0 ? (
                  <div className="space-y-3">
                    {file.relationships.map((relationship) => (
                      <div key={relationship.relationship_id} className="p-3 border rounded-lg">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center gap-2">
                            {getTableIcon(relationship.target_table_name)}
                            <div>
                              <p className="font-medium capitalize">
                                {relationship.target_table_name}
                              </p>
                              <p className="text-sm text-gray-600">
                                ID: {relationship.target_row_id}
                                {relationship.linked_entity_name && (
                                  <span className="ml-2 text-blue-600">
                                    ({relationship.linked_entity_name})
                                  </span>
                                )}
                              </p>
                              {relationship.relationship_type && (
                                <p className="text-xs text-gray-500">
                                  Type: {relationship.relationship_type}
                                </p>
                              )}
                              {relationship.relationship_title && (
                                <p className="text-xs text-gray-500">
                                  Title: {relationship.relationship_title}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {relationship.is_primary && (
                              <Badge variant="default" className="text-xs">Primary</Badge>
                            )}
                            {onDeleteRelationship && (
                              <Button
                                onClick={() => handleDeleteRelationship(relationship.relationship_id)}
                                variant="ghost"
                                size="sm"
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No relationships found</p>
                )}
              </CardContent>
            </Card>

            {/* Metadata */}
            {file.metadata && Object.keys(file.metadata).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Metadata</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-gray-50 p-3 rounded overflow-auto">
                    {JSON.stringify(file.metadata, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">File not found</p>
          </div>
        )}
      </DialogContent>
      <ConfirmationDialog />
    </Dialog>
  );
}
