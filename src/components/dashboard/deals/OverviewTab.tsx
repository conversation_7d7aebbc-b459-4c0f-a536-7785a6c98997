import React, { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import {
  Building2,
  TrendingUp,
  DollarSign,
  MapPin,
  Calendar,
  Percent,
  Users,
  Home,
  Ruler,
  CreditCard,
  BarChart3,
  Square,
  Building,
  ChevronDown,
  AlertTriangle,
  AlertCircle,
  Database,
  Plus,
  Trash2,
} from "lucide-react";
import { DealV2 } from "./shared/types-v2";
import CapitalPositionSelector from "./CapitalPositionSelector";
import ConflictsDisplay from "./ConflictsDisplay";
import { ConflictIndicator } from "./ConflictIndicator";
import DealFiles from "./DealFiles";
import { useCentralMappings } from "@/hooks/useCentralMappings";
import { Skeleton } from "@/components/ui/skeleton";

// Utility function to safely format dates
const safeFormatDate = (dateValue: any, format: 'date' | 'datetime' | 'iso' = 'date'): string => {
  if (!dateValue) return "";
  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "";
    
    switch (format) {
      case 'date':
        return date.toISOString().split('T')[0];
      case 'datetime':
        return date.toISOString().slice(0, 16);
      case 'iso':
        return date.toISOString();
      default:
        return date.toISOString().split('T')[0];
    }
  } catch {
    return "";
  }
};

// Utility function to safely format dates for display
const safeFormatDateForDisplay = (dateValue: any, format: 'date' | 'datetime' = 'date'): string => {
  if (!dateValue) return "N/A";
  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "N/A";
    
    switch (format) {
      case 'date':
        return date.toLocaleDateString();
      case 'datetime':
        return date.toLocaleString();
      default:
        return date.toLocaleDateString();
    }
  } catch {
    return "N/A";
  }
};

interface OverviewTabProps {
  deal: DealV2;
  currentDeal: DealV2 | null;
  dealId: string;
  isEditing: boolean;
  isNsfSyncing: boolean;
  onFieldChange: (field: string, value: any) => void;
  onPropertyFieldChange: (field: string, value: any) => void;
  updateIsRequiredForNsfFields: (askCapitalPosition: string[]) => void;
  getAllConflictsWithAlternatives: () => any[];
  hasConflict: (field: string) => boolean;
  getConflictData: (field: string) => any;
  formatNumber: (value: number | string | null | number[], unit?: string) => string;
  formatRawCurrency: (value: number | null) => string;
  formatPercentage: (value: number | null) => string;
}

const OverviewTab: React.FC<OverviewTabProps> = ({
  deal,
  currentDeal,
  dealId,
  isEditing,
  isNsfSyncing,
  onFieldChange,
  onPropertyFieldChange,
  updateIsRequiredForNsfFields,
  getAllConflictsWithAlternatives,
  hasConflict,
  getConflictData,
  formatNumber,
  formatRawCurrency,
  formatPercentage,
}) => {
  const handleFieldChange = onFieldChange;
  const handlePropertyFieldChange = onPropertyFieldChange;
  
  // Fetch all mapping options from central mapping table
  const { 
    strategyOptions, 
    propertyTypeOptions, 
    subpropertyTypeOptions, 
    
    loading: mappingsLoading, 
    error: mappingsError,
    updateSubpropertyTypes
  } = useCentralMappings();
  
const capitalPositionOptions = useMemo(() => {
  return currentDeal?.nsfFields?.filter(nsf => nsf.nsfContext === 'sources' )
  .map(nsf => nsf)
  
}, [currentDeal?.nsfFields])
console.log('Capital Position Options:', capitalPositionOptions)
  // Show loading skeleton when mappings are loading
  if (mappingsLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Deal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Loading skeleton for Basic Information */}
              <div>
                <Skeleton className="h-4 w-32 mb-3" />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-20 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-28 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
              </div>
              
              {/* Loading skeleton for Financial Information */}
              <div>
                <Skeleton className="h-4 w-36 mb-3" />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-28 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-20 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
              </div>
              
              {/* Loading skeleton for Property Information */}
              <div>
                <Skeleton className="h-4 w-32 mb-3" />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-28 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-20 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
              </div>
              
              {/* Loading skeleton for Property Metrics */}
              <div>
                <Skeleton className="h-4 w-36 mb-3" />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-28 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-20 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-36 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-40 mb-2" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Loading skeleton for right sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-8 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-28 mb-2" />
                <Skeleton className="h-8 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-8 w-full" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-28" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-8 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-8 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Deal Information
            </CardTitle>
          </CardHeader>
          
          {/* Debug Info */}
          {/* <Card className="bg-gray-50 border-gray-200 mx-6 mb-6">
            <CardContent className="pt-4">
              <div className="text-xs text-gray-600 space-y-1">
                <div><strong>Debug Info:</strong></div>
                <div>Deal ID: {dealId}</div>
                <div>Mappings Loading: {mappingsLoading ? 'Yes' : 'No'}</div>
                <div>Mappings Error: {mappingsError || 'None'}</div>
                <div>Strategy Options: {strategyOptions.length}</div>
                <div>Property Type Options: {propertyTypeOptions.length}</div>
                <div>Subproperty Type Options: {subpropertyTypeOptions.length}</div>
                <div>Capital Position Options: {capitalPositionOptions.length}</div>
                <div>Sample Strategies: {strategyOptions.slice(0, 3).join(', ') || 'None'}</div>
                <div>Sample Property Types: {propertyTypeOptions.slice(0, 3).join(', ') || 'None'}</div>
              </div>
            </CardContent>
          </Card> */}
          
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-3">Basic Information</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Deal Name</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.dealName || ""}
                      onChange={(e) => handleFieldChange('dealName', e.target.value)}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.dealName || "N/A"}</p>
                  )}
                </div>

                <div>
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium text-gray-600">Strategy</Label>
                    {isEditing && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.location.reload()}
                        className="h-6 px-2 text-xs"
                        title="Refresh strategy options from central mapping"
                      >
                        🔄
                      </Button>
                    )}
                  </div>
                  {isEditing ? (
                    <Select
                      value={currentDeal?.strategy || ""}
                      onValueChange={(value) => handleFieldChange('strategy', value)}
                      disabled={mappingsLoading}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder={mappingsLoading ? "Loading..." : "Select strategy"} />
                      </SelectTrigger>
                      <SelectContent>
                        {mappingsLoading ? (
                          <SelectItem value="loading-strategies" disabled>Loading strategies...</SelectItem>
                        ) : mappingsError ? (
                          <SelectItem value="error-strategies" disabled>Error loading strategies</SelectItem>
                        ) : strategyOptions.length === 0 ? (
                          <SelectItem value="no-strategies" disabled>No strategies available</SelectItem>
                        ) : (
                          strategyOptions.map((strategy) => (
                            <SelectItem key={strategy} value={strategy}>
                              {strategy}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.strategy || "N/A"}</p>
                  )}
                  {mappingsError && (
                    <p className="text-xs text-red-500 mt-1">
                      Error loading strategies: {mappingsError}
                    </p>
                  )}
                  {!mappingsLoading && !mappingsError && (
                    <p className="text-xs text-gray-500 mt-1">
                      {strategyOptions.length} strategy options loaded from central mapping
                    </p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Deal Type</Label>
                  {isEditing ? (
                    <Select
                      value={currentDeal?.dealType || ""}
                      onValueChange={(value) => handleFieldChange('dealType', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select deal type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Acquisition">Acquisition</SelectItem>
                        <SelectItem value="Construction">Construction</SelectItem>
                        <SelectItem value="Development">Development</SelectItem>
                        <SelectItem value="Redevelopment">Redevelopment</SelectItem>
                        <SelectItem value="Adaptive Use">Adaptive Use</SelectItem>
                        <SelectItem value="Land Assemblage">Land Assemblage</SelectItem>
                        <SelectItem value="Special Situation (Distressed)">Special Situation (Distressed)</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.dealType || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Deal Stage</Label>
                  {isEditing ? (
                    <Select
                      value={currentDeal?.dealStage || ""}
                      onValueChange={(value) => handleFieldChange('dealStage', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select stage" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Pre-Acquisition">Pre-Acquisition</SelectItem>
                        <SelectItem value="Acquisition">Acquisition</SelectItem>
                        <SelectItem value="Pre-Development">Pre-Development</SelectItem>
                        <SelectItem value="Construction">Construction</SelectItem>
                        <SelectItem value="Stabilization">Stabilization</SelectItem>
                        <SelectItem value="Ongoing Operations">Ongoing Operations</SelectItem>
                        <SelectItem value="Exit">Exit</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.dealStage || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Deal Status</Label>
                  {isEditing ? (
                    <Select
                      value={currentDeal?.dealStatus || ""}
                      onValueChange={(value) => handleFieldChange('dealStatus', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Deal Review">Deal Review</SelectItem>
                        <SelectItem value="Advisory Agreement">Advisory Agreement</SelectItem>
                        <SelectItem value="Campaign Outreach">Campaign Outreach</SelectItem>
                        <SelectItem value="Term Sheet">Term Sheet</SelectItem>
                        <SelectItem value="Due-Diligence">Due-Diligence</SelectItem>
                        <SelectItem value="Closed">Closed</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.dealStatus || "N/A"}</p>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Ask Capital Position</Label>
                  <CapitalPositionSelector
                    currentDeal={currentDeal}
                    isEditing={isEditing}
                    onFieldChange={handleFieldChange}
                    updateIsRequiredForNsfFields={updateIsRequiredForNsfFields}
                    capitalPositionOptions={capitalPositionOptions}
                    loading={mappingsLoading}
                  />
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Ask Amount (Required Sources)</Label>
                  <div className="space-y-1">
                    {currentDeal?.askAmount && currentDeal.askAmount.length > 0 ? (
                      capitalPositionOptions?.map((source, index) => {
                        // Find the corresponding source to get its capital position
                        if(!source.isRequired){return null}
                        const capitalPosition = source?.sourceType || 'Unknown';
                        
                        return (
                          <div key={index} className="flex items-center justify-between">
                            <div className="flex-1">
                              <span className="text-sm text-gray-900">{formatRawCurrency(source.amount)}</span>
                              <span className="text-xs text-gray-500 ml-2">({capitalPosition})</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              Required
                            </Badge>
                          </div>
                        );
                      })
                    ) : (
                      <p className="text-sm text-gray-500">No required sources found</p>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    This field shows individual amounts from NSF source fields marked as required. 
                    Each amount corresponds to a specific capital position.
                  </p>
                  {currentDeal?.askAmount && currentDeal.askAmount.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">Total Required:</span>
                        <span className="text-lg font-semibold text-gray-900">
                          {formatRawCurrency(currentDeal.askAmount.reduce((sum, amount) => sum + (parseFloat(amount?.toString() || '0') || 0), 0))}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Capital Raise Timeline</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.capitalRaiseTimeline || ""}
                      onChange={(e) => handleFieldChange('capitalRaiseTimeline', e.target.value)}
                      className="mt-1"
                      placeholder="e.g., 3 months"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.capitalRaiseTimeline || "N/A"}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Financial Metrics */}
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-3">Financial Metrics</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Purchase Price</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={currentDeal?.purchasePrice?.toString() || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('purchasePrice', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('purchasePrice', value);
                          }
                        }
                      }}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(currentDeal?.purchasePrice || null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Total Project Cost</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={currentDeal?.totalProjectCost?.toString() || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('totalProjectCost', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('totalProjectCost', value);
                          }
                        }
                      }}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(currentDeal?.totalProjectCost || null)}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-3">Timeline</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Date Received</Label>
                  {isEditing ? (
                    <Input
                      type="date"
                      value={safeFormatDate(currentDeal?.dateReceived, 'date')}
                      onChange={(e) => handleFieldChange('dateReceived', new Date(e.target.value))}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">
                      {safeFormatDateForDisplay(currentDeal?.dateReceived, 'date')}
                    </p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Date Closed</Label>
                  {isEditing ? (
                    <Input
                      type="date"
                      value={safeFormatDate(currentDeal?.dateClosed, 'date')}
                      onChange={(e) => handleFieldChange('dateClosed', new Date(e.target.value))}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">
                      {safeFormatDateForDisplay(currentDeal?.dateClosed, 'date')}
                    </p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Date Under Contract</Label>
                  {isEditing ? (
                    <Input
                      type="date"
                      value={safeFormatDate(currentDeal?.dateUnderContract, 'date')}
                      onChange={(e) => handleFieldChange('dateUnderContract', new Date(e.target.value))}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">
                      {safeFormatDateForDisplay(currentDeal?.dateUnderContract, 'date')}
                    </p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Hold Period</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.1"
                      value={currentDeal?.holdPeriod || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('holdPeriod', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('holdPeriod', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.holdPeriod ? `${currentDeal.holdPeriod} years` : "N/A"}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Summary */}
            <div>
              <Label className="text-sm font-medium text-gray-600">Summary</Label>
              {isEditing ? (
                <Textarea
                  value={currentDeal?.summary || ""}
                  onChange={(e) => handleFieldChange('summary', e.target.value)}
                  className="mt-1"
                  rows={3}
                  placeholder="Enter deal summary..."
                />
              ) : (
                <div className="mt-1 p-3 bg-gray-50 rounded-md min-h-[80px]">
                  {currentDeal?.summary ? (
                    <p className="text-sm text-gray-900 whitespace-pre-wrap">{currentDeal.summary}</p>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No summary provided</p>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Property Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Property Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Location */}
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-3">Location</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Address</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.property?.address || ""}
                      onChange={(e) => handlePropertyFieldChange('address', e.target.value)}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.address || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">City</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.property?.city || ""}
                      onChange={(e) => handlePropertyFieldChange('city', e.target.value)}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.city || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">State</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.property?.state || ""}
                      onChange={(e) => handlePropertyFieldChange('state', e.target.value)}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.state || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">ZIP Code</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.property?.zipcode || ""}
                      onChange={(e) => handlePropertyFieldChange('zipcode', e.target.value)}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.zipcode || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Neighborhood</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.property?.neighborhood || ""}
                      onChange={(e) => handlePropertyFieldChange('neighborhood', e.target.value)}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.neighborhood || "N/A"}</p>
                  )}
                </div>
                <div>
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium text-gray-600">Property Type</Label>
                    {isEditing && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.location.reload()}
                        className="h-6 px-2 text-xs"
                        title="Refresh property type options from central mapping"
                      >
                        🔄
                      </Button>
                    )}
                  </div>
                  {isEditing ? (
                    <Select
                      value={currentDeal?.property?.propertyType || ""}
                      onValueChange={(value) => {
                        handlePropertyFieldChange('propertyType', value);
                        // Update subproperty types based on selected property type
                        updateSubpropertyTypes(value).catch((error) => {
                          console.error('Error updating subproperty types:', error);
                          // Don't throw the error, just log it to prevent breaking the UI
                        });
                        // Clear subproperty type when property type changes
                        handlePropertyFieldChange('subpropertyType', '');
                      }}
                      disabled={mappingsLoading}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder={mappingsLoading ? "Loading..." : "Select property type"} />
                      </SelectTrigger>
                      <SelectContent>
                        {mappingsLoading ? (
                          <SelectItem value="loading-property-types" disabled>Loading property types...</SelectItem>
                        ) : mappingsError ? (
                          <SelectItem value="error-property-types" disabled>Error loading property types</SelectItem>
                        ) : propertyTypeOptions.length === 0 ? (
                          <SelectItem value="no-property-types" disabled>No property types available</SelectItem>
                        ) : (
                          propertyTypeOptions.map((propertyType) => (
                            <SelectItem key={propertyType} value={propertyType}>
                              {propertyType}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.propertyType || "N/A"}</p>
                  )}
                  {mappingsError && (
                    <p className="text-xs text-red-500 mt-1">
                      Error loading property types: {mappingsError}
                    </p>
                  )}
                  {!mappingsLoading && !mappingsError && (
                    <p className="text-xs text-gray-500 mt-1">
                      {propertyTypeOptions.length} property type options loaded from central mapping
                    </p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Region</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.property?.region || ""}
                      onChange={(e) => handlePropertyFieldChange('region', e.target.value)}
                      className="mt-1"
                      placeholder="e.g., Midtown West"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.region || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Market</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.property?.market || ""}
                      onChange={(e) => handlePropertyFieldChange('market', e.target.value)}
                      className="mt-1"
                      placeholder="e.g., New York"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.market || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Submarket</Label>
                  {isEditing ? (
                    <Input
                      value={currentDeal?.property?.submarket || ""}
                      onChange={(e) => handlePropertyFieldChange('submarket', e.target.value)}
                      className="mt-1"
                      placeholder="e.g., Midtown West"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.submarket || "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Subproperty Type</Label>
                  {isEditing ? (
                    <Select
                      value={currentDeal?.property?.subpropertyType || ""}
                      onValueChange={(value) => {
                        try {
                          handlePropertyFieldChange('subpropertyType', value);
                        } catch (error) {
                          console.error('Error updating subproperty type:', error);
                        }
                      }}
                      disabled={mappingsLoading}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder={mappingsLoading ? "Loading..." : "Select subproperty type"} />
                      </SelectTrigger>
                      <SelectContent>
                        {mappingsLoading ? (
                          <SelectItem value="loading-subproperty-types" disabled>Loading subproperty types...</SelectItem>
                        ) : mappingsError ? (
                          <SelectItem value="error-subproperty-types" disabled>Error loading subproperty types</SelectItem>
                        ) : subpropertyTypeOptions.length === 0 ? (
                          <SelectItem value="no-subproperty-types" disabled>
                            {currentDeal?.property?.propertyType === 'Land' 
                              ? 'No subproperty types for Land' 
                              : 'No subproperty types available'
                            }
                          </SelectItem>
                        ) : (
                          subpropertyTypeOptions.map((subpropertyType) => (
                            <SelectItem key={subpropertyType} value={subpropertyType}>
                              {subpropertyType}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.subpropertyType || "N/A"}</p>
                  )}
                  {mappingsError && (
                    <p className="text-xs text-red-500 mt-1">
                      Error loading subproperty types: {mappingsError}
                    </p>
                  )}
                  {!mappingsLoading && !mappingsError && (
                    <p className="text-xs text-gray-500 mt-1">
                      {subpropertyTypeOptions.length} subproperty type options loaded from central mapping
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Property Metrics */}
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-3">Property Metrics</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Building Square Feet</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={currentDeal?.property?.buildingSqft || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('buildingSqft', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('buildingSqft', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.property?.buildingSqft ?? null, 'sq ft')}</p>
                  )}
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-600">Land Acres</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={currentDeal?.property?.landAcres || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('landAcres', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('landAcres', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0.00"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.property?.landAcres ?? null, 'acres')}</p>
                  )}
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-600">GSF (Gross Square Foot)</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={currentDeal?.property?.gsfGrossSquareFoot || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('gsfGrossSquareFoot', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('gsfGrossSquareFoot', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0.00"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.property?.gsfGrossSquareFoot ?? null, 'sq ft')}</p>
                  )}
                </div>
                

                
                <div>
                  <Label className="text-sm font-medium text-gray-600">Total NSF (Net Square Foot)</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={currentDeal?.property?.totalNsfNetSquareFoot || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('totalNsfNetSquareFoot', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('totalNsfNetSquareFoot', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0.00"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.property?.totalNsfNetSquareFoot ?? null, 'sq ft')}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Number of Units</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      value={currentDeal?.property?.numberOfUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('numberOfUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('numberOfUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.property?.numberOfUnits ?? null)}</p>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Lot Area</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      value={currentDeal?.property?.lotArea || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('lotArea', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('lotArea', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.property?.lotArea ?? null, 'sq ft')}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Floor Area Ratio</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      value={currentDeal?.property?.floorAreaRatio || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('floorAreaRatio', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('floorAreaRatio', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.property?.floorAreaRatio ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Residential NSF</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={currentDeal?.residentialNsfNetSquareFoot || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('residentialNsfNetSquareFoot', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('residentialNsfNetSquareFoot', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.residentialNsfNetSquareFoot ?? null, 'sq ft')}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Retail NSF</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={currentDeal?.retailNsfNetSquareFoot || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('retailNsfNetSquareFoot', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('retailNsfNetSquareFoot', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.retailNsfNetSquareFoot ?? null, 'sq ft')}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Office NSF</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={currentDeal?.officeNsfNetSquareFoot || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('officeNsfNetSquareFoot', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('officeNsfNetSquareFoot', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.officeNsfNetSquareFoot ?? null, 'sq ft')}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Community Facility NSF</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={currentDeal?.communityFacilityNsfNetSquareFoot || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('communityFacilityNsfNetSquareFoot', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('communityFacilityNsfNetSquareFoot', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.communityFacilityNsfNetSquareFoot ?? null, 'sq ft')}</p>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Occupancy Rate</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={currentDeal?.occupancyRate ? (currentDeal.occupancyRate * 100) : ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('occupancyRate', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('occupancyRate', value / 100);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatPercentage(currentDeal?.occupancyRate ?? null)}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Unit Counts */}
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-3">Unit Counts</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Affordable Housing - Studios</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      value={currentDeal?.numAffordableHousingStudiosUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('numAffordableHousingStudiosUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('numAffordableHousingStudiosUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.numAffordableHousingStudiosUnits ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Affordable Housing - 1BR</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      value={currentDeal?.numAffordableHousing1bedroomUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('numAffordableHousing1bedroomUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('numAffordableHousing1bedroomUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.numAffordableHousing1bedroomUnits ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Affordable Housing - 2BR</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      value={currentDeal?.numAffordableHousing2bedroomUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('numAffordableHousing2bedroomUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('numAffordableHousing2bedroomUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.numAffordableHousing2bedroomUnits ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Affordable Housing - 3BR</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      value={currentDeal?.numAffordableHousing3bedroomUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('numAffordableHousing3bedroomUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('numAffordableHousing3bedroomUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.numAffordableHousing3bedroomUnits ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Market Rate - Studios</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      value={currentDeal?.numMarketRateStudiosUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('numMarketRateStudiosUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('numMarketRateStudiosUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.numMarketRateStudiosUnits ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Market Rate - 1BR</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      value={currentDeal?.numMarketRate1bedroomUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('numMarketRate1bedroomUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('numMarketRate1bedroomUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.numMarketRate1bedroomUnits ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Market Rate - 2BR</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      value={currentDeal?.numMarketRate2bedroomUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('numMarketRate2bedroomUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('numMarketRate2bedroomUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.numMarketRate2bedroomUnits ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Market Rate - 3BR</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="0"
                      value={currentDeal?.numMarketRate3bedroomUnits || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handleFieldChange('numMarketRate3bedroomUnits', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handleFieldChange('numMarketRate3bedroomUnits', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatNumber(currentDeal?.numMarketRate3bedroomUnits ?? null)}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Additional Property Details */}
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-3">Additional Details</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Year Built</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="1800"
                      max="2100"
                      value={currentDeal?.property?.yearBuilt || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('yearBuilt', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('yearBuilt', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.yearBuilt ?? "N/A"}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Year Renovated</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      min="1800"
                      max="2100"
                      value={currentDeal?.property?.yearRenovated || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('yearRenovated', null);
                        } else {
                          const value = parseInt(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('yearRenovated', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.yearRenovated ?? "N/A"}</p>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Appraisal Value</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      value={currentDeal?.property?.appraisalValue || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('appraisalValue', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('appraisalValue', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(currentDeal?.property?.appraisalValue ?? null)}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Environmental Risk Score</Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      value={currentDeal?.property?.environmentalRiskScore || ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        // Allow empty input to clear the field
                        if (inputValue === "") {
                          handlePropertyFieldChange('environmentalRiskScore', null);
                        } else {
                          const value = parseFloat(inputValue);
                          // Only allow non-negative values
                          if (!isNaN(value) && value >= 0) {
                            handlePropertyFieldChange('environmentalRiskScore', value);
                          }
                        }
                      }}
                      className="mt-1"
                      placeholder="0"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.property?.environmentalRiskScore || "N/A"}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Property Description */}
            <div>
              <Label className="text-sm font-medium text-gray-600">Property Description</Label>
              {isEditing ? (
                <Textarea
                  value={currentDeal?.property?.propertyDescription || ""}
                  onChange={(e) => handlePropertyFieldChange('propertyDescription', e.target.value)}
                  className="mt-1"
                  rows={3}
                  placeholder="Enter property description..."
                />
              ) : (
                <div className="mt-1 p-3 bg-gray-50 rounded-md min-h-[80px]">
                  {currentDeal?.property?.propertyDescription ? (
                    <p className="text-sm text-gray-900 whitespace-pre-wrap">{currentDeal.property.propertyDescription}</p>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No property description provided</p>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Add Deals Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Add Deals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <DealFiles dealId={dealId} isV2Deal={true} />
          </CardContent>
        </Card>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Sources Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Sources</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {(() => {
              const nsfFields = isEditing ? currentDeal?.nsfFields : deal?.nsfFields;
              const sourceFields = nsfFields?.filter(nsf => nsf.nsfContext === 'sources') || [];
              
              if (sourceFields.length === 0) {
                return <p className="text-sm text-gray-500">No NSF sources found</p>;
              }
              
              // Enhanced logic: Show all sources but handle Common Equity differently for totals
              const hasLP = sourceFields.some(nsf => 
                nsf.sourceType === 'Limited Partner (LP)' || 
                (nsf as any).capitalPosition === 'Limited Partner (LP)'
              );
              const hasGP = sourceFields.some(nsf => 
                nsf.sourceType === 'General Partner (GP)' || 
                (nsf as any).capitalPosition === 'General Partner (GP)'
              );
              const hasCommonEquity = sourceFields.some(nsf => 
                nsf.sourceType === 'Common Equity' || 
                (nsf as any).capitalPosition === 'Common Equity'
              );
              
              // Show all sources (don't filter out Common Equity)
              const filteredSourceFields = sourceFields;
              
              // Calculate total sources excluding Common Equity if LP/GP are present
              const totalSources = sourceFields.reduce((sum, nsf) => {
                const isCommonEquity = nsf.sourceType === 'Common Equity' || (nsf as any).capitalPosition === 'Common Equity';
                const amount = parseFloat(nsf.amount?.toString() || '0') || 0;
                
                console.log(`🔍 Source ${nsf.id} (${nsf.sourceType}): amount=${amount}, isCommonEquity=${isCommonEquity}, hasLP=${hasLP}, hasGP=${hasGP}`);
                
                // If LP/GP are present, exclude Common Equity from total
                if ((hasLP || hasGP) && isCommonEquity) {
                  console.log(`🚫 Excluding Common Equity from total: ${sum}`);
                  return sum; // Don't add Common Equity to total
                }
                
                // Include all other sources (LP, GP, debt, and Common Equity when LP/GP not present)
                const newSum = sum + amount;
                console.log(`✅ Including in total: ${sum} + ${amount} = ${newSum}`);
                return newSum;
              }, 0);
              
              console.log(`📊 Final totalSources: ${totalSources}`);
              

              
              return (
                <div className="space-y-2">
                  {sourceFields.map((nsf, index) => (
                    <div key={nsf.id} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">
                        {nsf.sourceType || 'Unknown Source'}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {nsf.amount ? formatRawCurrency(nsf.amount) : 'N/A'}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {nsf.percentageOfTotal > 0 ? `${formatPercentage(nsf.percentageOfTotal)}` : 'N/A'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  <div className="pt-2 border-t border-gray-200">
                    <div className="flex items-center justify-between text-sm font-medium">
                      <span>Total</span>
                      <span>{formatRawCurrency(totalSources)}</span>
                    </div>
                    {/* Show warning when Common Equity is being ignored due to GP/LP presence */}
                    {hasCommonEquity && (hasLP || hasGP) && (
                      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                        <div className="flex items-center gap-1">
                          <AlertTriangle className="h-3 w-3" />
                          <span className="font-medium">Note:</span>
                        </div>
                        <p className="mt-1">
                          Common Equity is being ignored in total sources and percentage calculations as GP/LP sources are present. 
                          Total includes GP + LP + debt sources only. Common Equity percentage shows as N/A.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              );
            })()}
          </CardContent>
        </Card>

        {/* Uses Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Uses</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {(() => {
              const nsfFields = isEditing ? currentDeal?.nsfFields : deal?.nsfFields;
              const useFields = nsfFields?.filter(nsf => nsf.nsfContext === 'uses_total') || [];
              
              if (useFields.length === 0) {
                return <p className="text-sm text-gray-500">No NSF uses found</p>;
              }
              
              // Calculate total uses
              const totalUses = useFields.reduce((sum, nsf) => sum + (parseFloat(nsf.amount?.toString() || '0') || 0), 0);
              
              return (
                <div className="space-y-2">
                  {useFields.map((nsf, index) => (
                    <div key={nsf.id} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 capitalize">{nsf.useType || nsf.dealType}</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {nsf.amount ? formatRawCurrency(nsf.amount) : 'N/A'}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {nsf.percentageOfTotal > 0 ? `${formatPercentage(nsf.percentageOfTotal)}` : 'N/A'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  <div className="pt-2 border-t border-gray-200">
                    <div className="flex items-center justify-between text-sm font-medium">
                      <span>Total</span>
                      <span>{formatRawCurrency(totalUses)}</span>
                    </div>
                  </div>
                </div>
              );
            })()}
          </CardContent>
        </Card>

        {/* Conflicts Display - Compact Tooltip Style */}
        {(() => {
          const allConflicts = getAllConflictsWithAlternatives();
          if (allConflicts.length === 0) return null;
          
          return (
            <div className="relative group">
              <div className="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg cursor-pointer hover:bg-orange-100 transition-colors">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium text-orange-700">Conflicts</span>
                <Badge variant="destructive" className="text-xs">
                  {allConflicts.length}
                </Badge>
              </div>
              
              {/* Tooltip/Popover Content */}
              <div className="absolute bottom-full left-0 mb-2 w-96 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="p-4 max-h-80 overflow-y-auto">
                  <h4 className="font-medium text-sm text-gray-700 mb-3">Conflicts with Alternatives ({allConflicts.length})</h4>
                  <div className="space-y-3">
                    {allConflicts.map((conflict, index) => (
                      <div key={index} className="bg-orange-50 border border-orange-200 rounded p-3">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-orange-700 text-sm">
                            {conflict.conflictType === 'nsf' 
                              ? `NSF ${conflict.context} - ${conflict.nsfType} - ${conflict.field}`
                              : conflict.fieldName
                            }
                          </span>
                          <Badge variant="outline" className="text-xs text-orange-600">
                            {conflict.conflictData.chosen_file}
                          </Badge>
                        </div>
                        <div className="text-xs text-gray-700 mb-1">
                          <strong>Chosen:</strong> {JSON.stringify(conflict.conflictData.chosen_value)}
                        </div>
                        <div className="text-xs text-orange-600 mb-1">
                          <strong>Reason:</strong> {conflict.conflictData.reason}
                        </div>
                        <div className="text-xs text-gray-600">
                          <strong>Alternatives:</strong> {conflict.conflictData.alternatives.length} found
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                {/* Arrow */}
                <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
              </div>
            </div>
          );
        })()}

        {/* System & Processing Information */}
        <Card>
          <CardHeader>
            <Collapsible>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="flex items-center gap-2 p-0 h-auto">
                  <Database className="h-4 w-4" />
                  <span className="text-sm font-medium">System & Processing Information</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Processing Duration (ms)</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        min="0"
                        value={currentDeal?.processingDurationMs || ""}
                        onChange={(e) => {
                          const inputValue = e.target.value;
                          // Allow empty input to clear the field
                          if (inputValue === "") {
                            handleFieldChange('processingDurationMs', null);
                          } else {
                            const value = parseFloat(inputValue);
                            // Only allow non-negative values
                            if (!isNaN(value) && value >= 0) {
                              handleFieldChange('processingDurationMs', value);
                            }
                          }
                        }}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">{currentDeal?.processingDurationMs?.toLocaleString() || "N/A"}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Processor Version</Label>
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.processorVersion || "N/A"}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Review Status</Label>
                    {isEditing ? (
                      <Select
                        value={currentDeal?.reviewStatus || ""}
                        onValueChange={(value) => handleFieldChange('reviewStatus', value)}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="approved">Approved</SelectItem>
                          <SelectItem value="rejected">Rejected</SelectItem>
                          <SelectItem value="in_review">In Review</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">{currentDeal?.reviewStatus || "N/A"}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Reviewed By</Label>
                    {isEditing ? (
                      <Input
                        value={currentDeal?.reviewedBy || ""}
                        onChange={(e) => handleFieldChange('reviewedBy', e.target.value)}
                        className="mt-1"
                        placeholder="Reviewer name"
                      />
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">{currentDeal?.reviewedBy || "N/A"}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Reviewed At</Label>
                    {isEditing ? (
                      <Input
                        type="datetime-local"
                        value={safeFormatDate(currentDeal?.reviewedAt, 'datetime')}
                        onChange={(e) => handleFieldChange('reviewedAt', new Date(e.target.value))}
                        className="mt-1"
                      />
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">
                        {safeFormatDateForDisplay(currentDeal?.reviewedAt, 'datetime')}
                      </p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Is Distressed</Label>
                    {isEditing ? (
                      <Select
                        value={currentDeal?.isDistressed?.toString() || ""}
                        onValueChange={(value) => handleFieldChange('isDistressed', value === 'true')}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">Yes</SelectItem>
                          <SelectItem value="false">No</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">{currentDeal?.isDistressed ? "Yes" : "No"}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Is Internal Only</Label>
                    {isEditing ? (
                      <Select
                        value={currentDeal?.isInternalOnly?.toString() || ""}
                        onValueChange={(value) => handleFieldChange('isInternalOnly', value === 'true')}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">Yes</SelectItem>
                          <SelectItem value="false">No</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">{currentDeal?.isInternalOnly ? "Yes" : "No"}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">LLM Model Used</Label>
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.llmModelUsed || "N/A"}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">LLM Provider</Label>
                    <p className="text-sm text-gray-900 mt-1">{currentDeal?.llmProvider || "N/A"}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Document Size (bytes)</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        min="0"
                        value={currentDeal?.documentSizeBytes || ""}
                        onChange={(e) => {
                          const inputValue = e.target.value;
                          // Allow empty input to clear the field
                          if (inputValue === "") {
                            handleFieldChange('documentSizeBytes', null);
                          } else {
                            const value = parseFloat(inputValue);
                            // Only allow non-negative values
                            if (!isNaN(value) && value >= 0) {
                              handleFieldChange('documentSizeBytes', value);
                            }
                          }
                        }}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">{currentDeal?.documentSizeBytes?.toLocaleString() || "N/A"}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Property ID</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        min="0"
                        value={currentDeal?.propertyId || ""}
                        onChange={(e) => {
                          const inputValue = e.target.value;
                          // Allow empty input to clear the field
                          if (inputValue === "") {
                            handleFieldChange('propertyId', null);
                          } else {
                            const value = parseFloat(inputValue);
                            // Only allow non-negative values
                            if (!isNaN(value) && value >= 0) {
                              handleFieldChange('propertyId', value);
                            }
                          }
                        }}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">{currentDeal?.propertyId || "N/A"}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Created At</Label>
                    <p className="text-sm text-gray-900 mt-1">
                      {safeFormatDateForDisplay(currentDeal?.createdAt, 'datetime')}
                    </p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Updated At</Label>
                    <p className="text-sm text-gray-900 mt-1">
                      {safeFormatDateForDisplay(currentDeal?.updatedAt, 'datetime')}
                    </p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Extraction Timestamp</Label>
                    <p className="text-sm text-gray-900 mt-1">
                      {safeFormatDateForDisplay(currentDeal?.extractionTimestamp, 'datetime')}
                    </p>
                  </div>
                </div>
                
                {/* Array fields */}
                <div className="mt-6 space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Document Filenames</Label>
                    {isEditing ? (
                      <div className="space-y-2">
                        {Array.isArray(currentDeal?.documentFilename) ? currentDeal.documentFilename.map((filename, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Input
                              value={filename || ""}
                              onChange={(e) => {
                                if (!currentDeal) return;
                                const newFilenames = [...(currentDeal?.documentFilename || [])];
                                newFilenames[index] = e.target.value;
                                handleFieldChange('documentFilename', newFilenames);
                              }}
                              className="flex-1"
                              placeholder="Filename"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                if (!currentDeal) return;
                                const newFilenames = [...(currentDeal?.documentFilename || [])];
                                newFilenames.splice(index, 1);
                                handleFieldChange('documentFilename', newFilenames);
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )) : (
                          <Input
                            value=""
                            onChange={(e) => handleFieldChange('documentFilename', [e.target.value])}
                            placeholder="Add filename"
                          />
                        )}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (!currentDeal) return;
                            const newFilenames = [...(currentDeal?.documentFilename || []), ""];
                            handleFieldChange('documentFilename', newFilenames);
                          }}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Filename
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-1">
                        {Array.isArray(currentDeal?.documentFilename) ? currentDeal.documentFilename.map((filename, index) => (
                          <p key={index} className="text-sm text-gray-900">{filename}</p>
                        )) : (
                          <p className="text-sm text-gray-500">No filenames</p>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Document Sources</Label>
                    {isEditing ? (
                      <div className="space-y-2">
                        {Array.isArray(currentDeal?.documentSource) ? currentDeal.documentSource.map((source, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Input
                              value={source || ""}
                              onChange={(e) => {
                                if (!currentDeal) return;
                                const newSources = [...(currentDeal?.documentSource || [])];
                                newSources[index] = e.target.value;
                                handleFieldChange('documentSource', newSources);
                              }}
                              className="flex-1"
                              placeholder="Source"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                if (!currentDeal) return;
                                const newSources = [...(currentDeal?.documentSource || [])];
                                newSources.splice(index, 1);
                                handleFieldChange('documentSource', newSources);
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )) : (
                          <Input
                            value=""
                            onChange={(e) => handleFieldChange('documentSource', [e.target.value])}
                            placeholder="Add source"
                          />
                        )}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (!currentDeal) return;
                            const newSources = [...(currentDeal?.documentSource || []), ""];
                            handleFieldChange('documentSource', newSources);
                          }}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Source
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-1">
                        {Array.isArray(currentDeal?.documentSource) ? currentDeal.documentSource.map((source, index) => (
                          <p key={index} className="text-sm text-gray-900">{source}</p>
                        )) : (
                          <p className="text-sm text-gray-500">No sources</p>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Document Types</Label>
                    {isEditing ? (
                      <div className="space-y-2">
                        {Array.isArray(currentDeal?.documentType) ? currentDeal.documentType.map((type, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Input
                              value={type || ""}
                              onChange={(e) => {
                                if (!currentDeal) return;
                                const newTypes = [...(currentDeal?.documentType || [])];
                                newTypes[index] = e.target.value;
                                handleFieldChange('documentType', newTypes);
                              }}
                              className="flex-1"
                              placeholder="Type"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                if (!currentDeal) return;
                                const newTypes = [...(currentDeal?.documentType || [])];
                                newTypes.splice(index, 1);
                                handleFieldChange('documentType', newTypes);
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )) : (
                          <Input
                            value=""
                            onChange={(e) => handleFieldChange('documentType', [e.target.value])}
                            placeholder="Add type"
                          />
                        )}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (!currentDeal) return;
                            const newTypes = [...(currentDeal?.documentType || []), ""];
                            handleFieldChange('documentType', newTypes);
                          }}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Type
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-1">
                        {Array.isArray(currentDeal?.documentType) ? currentDeal.documentType.map((type, index) => (
                          <p key={index} className="text-sm text-gray-900">{type}</p>
                        )) : (
                          <p className="text-sm text-gray-500">No types</p>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Review Notes</Label>
                    {isEditing ? (
                      <Textarea
                        value={currentDeal?.reviewNotes || ""}
                        onChange={(e) => handleFieldChange('reviewNotes', e.target.value)}
                        className="mt-1"
                        rows={3}
                        placeholder="Review notes..."
                      />
                    ) : (
                      <p className="text-sm text-gray-900 mt-1">{currentDeal?.reviewNotes || "N/A"}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Extra Fields</Label>
                    {isEditing ? (
                      <Textarea
                        value={currentDeal?.extraFields ? JSON.stringify(currentDeal.extraFields, null, 2) : ""}
                        onChange={(e) => {
                          if (!currentDeal) return;
                          try {
                            const parsed = JSON.parse(e.target.value);
                            handleFieldChange('extraFields', parsed);
                          } catch (error) {
                            // Handle invalid JSON
                          }
                        }}
                        className="mt-1"
                        rows={3}
                        placeholder="Enter JSON data..."
                      />
                    ) : (
                      <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded mt-1">
                        <pre>{currentDeal?.extraFields ? JSON.stringify(currentDeal.extraFields, null, 2) : "N/A"}</pre>
                      </div>
                    )}
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </CardHeader>
        </Card>

        {/* AI Notes Accordion - moved to bottom */}
        {(deal.processingNotes || deal.dataQualityIssues?.length > 0) && (
          <Card>
            <CardHeader>
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2 p-0 h-auto">
                    <Database className="h-4 w-4" />
                    <span className="text-sm font-medium">AI Processing Notes</span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-4">
                  <div className="space-y-4">
                    {deal.extractionConfidence && (
                      <div className="flex items-center gap-2">
                        <Badge variant={deal.extractionConfidence === 'high' ? 'default' : deal.extractionConfidence === 'medium' ? 'secondary' : 'destructive'}>
                          {deal.extractionConfidence.toUpperCase()} Confidence
                        </Badge>
                        <span className="text-sm text-gray-600">
                          AI extraction confidence level
                        </span>
                      </div>
                    )}
                    
                    {deal.processingNotes && (
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 mb-2">Processing Notes</h4>
                        <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">{deal.processingNotes}</p>
                      </div>
                    )}
                    
                    {deal.dataQualityIssues && deal.dataQualityIssues.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 mb-2">Data Quality Issues</h4>
                        <ul className="space-y-2">
                          {deal.dataQualityIssues.map((issue: string, index: number) => (
                            <li key={index} className="text-sm text-amber-600 bg-amber-50 p-2 rounded-md flex items-start gap-2">
                              <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                              {issue}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {deal.missingCriticalFields && deal.missingCriticalFields.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 mb-2">Missing Critical Fields</h4>
                        <div className="flex flex-wrap gap-2">
                          {deal.missingCriticalFields.map((field: string, index: number) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {field}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <ConflictsDisplay getAllConflictsWithAlternatives={getAllConflictsWithAlternatives} />
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </CardHeader>
          </Card>
        )}


      </div>
    </div>
  );
};

export default OverviewTab;