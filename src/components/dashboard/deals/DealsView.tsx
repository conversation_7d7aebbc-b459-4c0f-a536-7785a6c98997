"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, Filter } from "lucide-react";
import { DealV2 } from "./shared/types-v2";
import DealCard from "./list-components/DealCard";
import Pagination from "@/components/dashboard/deals/list-components/Pagination";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter, useSearchParams } from "next/navigation";
import DealFiltersComponent from "./DealsFilters";
import { InvestmentCriteriaFilters } from "@/types/investment-criteria";

// Define the deal filters interface extending investment criteria
interface DealUnifiedFilters extends InvestmentCriteriaFilters {
  // Search fields
  searchTerm?: string;
  contactName?: string;
  address?: string;
  
  // Deal specific fields - merged into searchTerm
  matchType?: string[];
  status?: string[];
  dealStage?: string[];
  priority?: string[];
  reviewStatus?: string[];
  extractionConfidence?: string[];

  // Deal document fields
  documentType?: string[];
  documentSource?: string[];
  extractionMethod?: string[];

  // Deal financial fields
  yieldOnCostMin?: number;
  yieldOnCostMax?: number;
  projectedGpIrrMin?: number;
  projectedGpIrrMax?: number;
  projectedLpIrrMin?: number;
  projectedLpIrrMax?: number;
  projectedGpEmMin?: number;
  projectedGpEmMax?: number;
  projectedLpEmMin?: number;
  projectedLpEmMax?: number;
  projectedTotalIrrMin?: number;
  projectedTotalIrrMax?: number;
  projectedTotalEmMin?: number;
  projectedTotalEmMax?: number;

  // Deal property fields
  zipCode?: string;
  neighborhood?: string;
  lotAreaMin?: number;
  lotAreaMax?: number;
  floorAreaRatioMin?: number;
  floorAreaRatioMax?: number;
  zoningSqFtMin?: number;
  zoningSqFtMax?: number;

  // Deal metadata
  processingDurationMin?: number;
  processingDurationMax?: number;
  documentSizeBytesMin?: number;
  documentSizeBytesMax?: number;
  published?: boolean;
  minAmount?: number;
  maxAmount?: number;

  // New unified filter fields (override the InvestmentCriteriaFilters fields)
  state?: string[];           // Override states from InvestmentCriteriaFilters
  city?: string[];            // Override cities from InvestmentCriteriaFilters  
  propertyType?: string[];    // Override propertyTypes from InvestmentCriteriaFilters
  strategy?: string[];        // Override strategies from InvestmentCriteriaFilters
  amount?: number;            // Single amount field (replaces minAmount/maxAmount)

  // Date ranges
  extractionTimestampFrom?: string;
  extractionTimestampTo?: string;
  reviewedAtFrom?: string;
  reviewedAtTo?: string;

  // Pagination and sorting
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export default function DealsView() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get initial pagination state from URL parameters
  const initialPage = Math.max(1, parseInt(searchParams?.get('page') || '1'));
  const initialPageSize = Math.max(1, parseInt(searchParams?.get('limit') || '25'));
  
  // State for deals
  const [deals, setDeals] = useState<DealV2[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalDeals, setTotalDeals] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);
  
  const [mappings, setMappings] = useState<any>({});

  // Initialize filters from URL parameters
  const [filters, setFilters] = useState<DealUnifiedFilters>(() => {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      page: Math.max(1, parseInt(urlParams.get('page') || '1')),
      limit: Math.max(1, parseInt(urlParams.get('limit') || '25')),
      sortBy: urlParams.get('sortBy') || 'updated_at',
      sortOrder: (urlParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      searchTerm: urlParams.get('search') || undefined,
      contactName: urlParams.get('contactName') || undefined,
      address: urlParams.get('address') || undefined,
      capitalPosition: urlParams.get('capitalPosition')?.split(',').filter(v => v) || undefined,
      state: urlParams.get('state')?.split(',').filter(v => v) || undefined,
      city: urlParams.get('city')?.split(',').filter(v => v) || undefined,
      propertyType: urlParams.get('propertyType')?.split(',').filter(v => v) || undefined,
      strategy: urlParams.get('strategy')?.split(',').filter(v => v) || undefined,
      amount: urlParams.get('amount') ? parseFloat(urlParams.get('amount')!) : undefined,
      published: urlParams.get('published') === 'true' ? true : urlParams.get('published') === 'false' ? false : undefined,
    };
  });

  // Fetch deals function
  const fetchDeals = useCallback(async (page: number = 1, search: string = '') => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('pageSize', pageSize.toString());
      params.append('sortBy', filters.sortBy || 'updatedAt');
      params.append('sortOrder', (filters.sortOrder || 'desc').toUpperCase());
      
      // Always send search parameter to ensure we use the comprehensive search path
      params.append('search', search);
      
      // Add separate contact name and address search parameters
      if (filters.contactName) {
        params.append('contactName', filters.contactName);
      }
      if (filters.address) {
        params.append('address', filters.address);
      }
      
      if (filters.neighborhood) {
        params.append('neighborhood', filters.neighborhood);
      }
      if (filters.zipCode) {
        params.append('zipCode', filters.zipCode);
      }
      if (filters.capitalPosition && filters.capitalPosition.length > 0) {
        params.append('capitalPosition', filters.capitalPosition.join(','));
      }
      if (filters.sourceType && filters.sourceType.length > 0) {
        params.append('sourceType', filters.sourceType.join(','));
      }
      if (filters.status && filters.status.length > 0) {
        params.append('dealStatus', filters.status.join(','));
      }
      if (filters.dealStage && filters.dealStage.length > 0) {
        params.append('dealStage', filters.dealStage.join(','));
      }
      if (filters.state && filters.state.length > 0) {
        params.append('state', filters.state.join(','));
      }
      if (filters.city && filters.city.length > 0) {
        params.append('city', filters.city.join(','));
      }
      if (filters.propertyType && filters.propertyType.length > 0) {
        params.append('propertyType', filters.propertyType.join(','));
      }
      if (filters.strategy && filters.strategy.length > 0) {
        params.append('strategy', filters.strategy.join(','));
      }
      if (filters.amount) {
        params.append('amount', filters.amount.toString());
      }
      if (filters.published !== undefined) {
        params.append('published', filters.published.toString());
      }

      const response = await fetch(`/api/deals?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setDeals(data.deals || []);
        setTotalDeals(data.total || 0);
        setTotalPages(data.totalPages || 0);
        setCurrentPage(page);
      } else {
        console.error('Failed to fetch deals:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching deals:', error);
    } finally {
      setLoading(false);
    }
  }, [filters, pageSize]);

  // Listen for URL changes (browser back/forward)
  useEffect(() => {
    const handlePopState = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const newFilters: DealUnifiedFilters = {
        page: Math.max(1, parseInt(urlParams.get('page') || '1')),
        limit: Math.max(1, parseInt(urlParams.get('limit') || '25')),
        sortBy: urlParams.get('sortBy') || 'updated_at',
        sortOrder: (urlParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
        searchTerm: urlParams.get('search') || undefined,
        contactName: urlParams.get('contactName') || undefined,
        address: urlParams.get('address') || undefined,
        capitalPosition: urlParams.get('capitalPosition')?.split(',').filter(v => v) || undefined,
        state: urlParams.get('state')?.split(',').filter(v => v) || undefined,
        city: urlParams.get('city')?.split(',').filter(v => v) || undefined,
        propertyType: urlParams.get('propertyType')?.split(',').filter(v => v) || undefined,
        strategy: urlParams.get('strategy')?.split(',').filter(v => v) || undefined,
        amount: urlParams.get('amount') ? parseFloat(urlParams.get('amount')!) : undefined,
        published: urlParams.get('published') === 'true' ? true : urlParams.get('published') === 'false' ? false : undefined,
      };
      setFilters(newFilters);
      setCurrentPage(newFilters.page || 1);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Load deals when filters or search changes
  useEffect(() => {
    fetchDeals(currentPage, filters.searchTerm || '');
  }, [fetchDeals, currentPage, filters]);

  // Update URL with current filters
  const updateURL = useCallback((newFilters: DealUnifiedFilters) => {
    const url = new URL(window.location.href);
    const params = new URLSearchParams();
    
    // Add all filter parameters to URL
    if (newFilters.page && newFilters.page > 1) params.set('page', newFilters.page.toString());
    if (newFilters.limit && newFilters.limit !== 25) params.set('limit', newFilters.limit.toString());
    if (newFilters.sortBy && newFilters.sortBy !== 'updated_at') params.set('sortBy', newFilters.sortBy);
    if (newFilters.sortOrder && newFilters.sortOrder !== 'desc') params.set('sortOrder', newFilters.sortOrder);
    if (newFilters.searchTerm) params.set('search', newFilters.searchTerm);
    if (newFilters.contactName) params.set('contactName', newFilters.contactName);
    if (newFilters.address) params.set('address', newFilters.address);
    if (newFilters.capitalPosition && newFilters.capitalPosition.length > 0) params.set('capitalPosition', newFilters.capitalPosition.join(','));
    if (newFilters.state && newFilters.state.length > 0) params.set('state', newFilters.state.join(','));
    if (newFilters.city && newFilters.city.length > 0) params.set('city', newFilters.city.join(','));
    if (newFilters.propertyType && newFilters.propertyType.length > 0) params.set('propertyType', newFilters.propertyType.join(','));
    if (newFilters.strategy && newFilters.strategy.length > 0) params.set('strategy', newFilters.strategy.join(','));
    if (newFilters.amount) params.set('amount', newFilters.amount.toString());
    if (newFilters.published !== undefined) params.set('published', newFilters.published.toString());
    
    // Update URL without page reload
    const newURL = `${url.pathname}${params.toString() ? '?' + params.toString() : ''}`;
    window.history.replaceState({}, '', newURL);
  }, []);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<DealUnifiedFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    
    // Update URL with new filters
    updateURL(updatedFilters);
    
    // Reset to first page when filters change
    setCurrentPage(1);
  };

  // Handle filter clear
  const handleFilterClear = () => {
    const clearedFilters: DealUnifiedFilters = {
      page: 1,
      limit: pageSize,
      sortBy: 'updated_at',
      sortOrder: 'desc',
    };
    setFilters(clearedFilters);
    setCurrentPage(1);
    
    // Clear URL parameters
    window.history.replaceState({}, '', window.location.pathname);
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    const updatedFilters = { ...filters, page };
    setFilters(updatedFilters);
    updateURL(updatedFilters);
  };

  // Handle publish status change
  const handlePublishChange = (dealId: string, published: boolean) => {
    setDeals(prevDeals => 
      prevDeals.map(deal => 
        deal.dealId === parseInt(dealId) 
          ? { ...deal, published, publishedTime: published ? new Date() : new Date(0) }
          : deal
      )
    );
  };

  // Get current deals
  const getCurrentDeals = () => {
    return deals;
  };

  const getCurrentTotal = () => {
    return totalDeals;
  };

  const getCurrentTotalPages = () => {
    return totalPages;
  };

  const getCurrentPage = () => {
    return currentPage;
  };

  // Handle delete deal
  const handleDelete = async (dealId: string) => {
    try {
      const response = await fetch(`/api/deals/${dealId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        // Remove the deal from the local state
        setDeals(prevDeals => prevDeals.filter(deal => deal.dealId.toString() !== dealId));
        setTotalDeals(prev => prev - 1);
      } else {
        console.error('Failed to delete deal');
      }
    } catch (error) {
      console.error('Error deleting deal:', error);
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Deals</h1>
          <p className="text-gray-600">
            {loading ? 'Loading...' : `${getCurrentTotal()} deals found`}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => router.push('/dashboard/deals/upload')}
            className="flex items-center space-x-2"
          >
            <PlusCircle className="h-4 w-4" />
            <span>Add Deal</span>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <DealFiltersComponent
        filters={filters}
        onFiltersChange={handleFilterChange}
        onClearFilters={handleFilterClear}
        mappings={mappings}
      />

      {/* Results */}
      <div className="space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading deals...</p>
            </div>
          </div>
        ) : getCurrentDeals().length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No deals found</p>
            <p className="text-gray-400 text-sm mt-1">
              Try adjusting your filters or search terms
            </p>
          </div>
        ) : (
          <>
            {/* Deals Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2  gap-6">
              <AnimatePresence>
                {getCurrentDeals().map((deal) => (
                  <motion.div
                    key={deal.dealId}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <DealCard
                      deal={deal}
                      onDelete={handleDelete}
                      onPublishChange={handlePublishChange}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {/* Pagination */}
            {getCurrentTotalPages() > 1 && (
              <div className="flex justify-center mt-8">
                <Pagination
                  currentPage={getCurrentPage()}
                  totalPages={getCurrentTotalPages()}
                  onPageChange={handlePageChange}
                  pageSize={pageSize}
                  onPageSizeChange={setPageSize}
                  totalItems={getCurrentTotal()}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}