import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { 
  Target, 
  Users, 
  Search, 
  Filter, 
  RefreshCw, 
  TrendingUp, 
  MapPin, 
  DollarSign, 
  Building2, 
  Clock,
  Percent,
  Zap,
  AlertCircle,
  CheckCircle,
  ChevronRight,
  XCircle,
  Info
} from "lucide-react";
import { toast } from "sonner";

// Collapsible component for non-matched fields
const CollapsibleNonMatchedFields: React.FC<{ fields: any[] }> = ({ fields }) => {
  const [showNonMatched, setShowNonMatched] = useState(false);
  
  if (fields.length === 0) return null;
  
  return (
    <div>
      <button
        onClick={() => setShowNonMatched(!showNonMatched)}
        className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 mb-2 transition-colors"
      >
        <ChevronRight className={`h-4 w-4 transform transition-transform ${showNonMatched ? 'rotate-90' : ''}`} />
        {fields.length} fields didn't match - click to {showNonMatched ? 'collapse' : 'expand'}
      </button>
      
      {showNonMatched && (
        <div className="space-y-2">
          {fields.map((field: any, index: number) => (
            <div key={index} className="p-2 bg-gray-50 rounded border border-gray-200">
              <div className="flex justify-between items-center mb-1">
                <span className="text-gray-500 capitalize">{field.field_name.replace(/_/g, ' ')}:</span>
                <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600 border-gray-300">
                  0% (weight: {field.weight_percentage}%)
                </Badge>
              </div>
              <div className="text-xs text-gray-400">
                {field.reason}
              </div>
              <div className="text-xs text-gray-400">
                Confidence: {Math.round(field.confidence * 100)}%
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Collapsible component for Match Reasons & Scoring
const CollapsibleMatchReasons: React.FC<{ contact: any; formatScore: (score: number) => string }> = ({ contact, formatScore }) => {
  const [showMatchReasons, setShowMatchReasons] = useState(false);
  
  return (
    <div className="p-6 pb-4 border-b border-gray-100">
      <button
        onClick={() => setShowMatchReasons(!showMatchReasons)}
        className="w-full flex items-center justify-between mb-3 hover:bg-gray-50 p-2 rounded transition-colors"
      >
        <h5 className="font-semibold text-gray-900 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          Match Reasons & Scoring
        </h5>
        <ChevronRight className={`h-5 w-5 transform transition-transform ${showMatchReasons ? 'rotate-90' : ''} text-gray-400`} />
      </button>
      
      {showMatchReasons && (
        <div className="space-y-4">
          {/* Score Breakdown - Use Actual V2 Data */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h6 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Score Breakdown: {formatScore(contact.match_score)}
            </h6>
            {contact.scoring_summary ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                {contact.scoring_summary.field_breakdown
                  .filter((field: any) => field.score_percentage > 0)
                  .map((field: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-white rounded border">
                    <span className="text-blue-700 capitalize">{field.field_name.replace(/_/g, ' ')}</span>
                    <Badge 
                      variant="outline" 
                      className="bg-green-100 text-green-700 border-green-300 text-xs"
                    >
                      +{field.score_percentage}% (w: {field.weight_percentage}%)
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-gray-600 italic">
                Detailed scoring breakdown not available
              </div>
            )}
          </div>

          {/* Specific Match Reasons - Use Actual V2 Data */}
          <div>
            <h6 className="font-medium text-gray-800 mb-2">What Matched:</h6>
            {contact.scoring_summary ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {contact.scoring_summary.field_breakdown
                  .filter((field: any) => field.score_percentage > 0)
                  .map((field: any, index: number) => (
                    <div key={index} className="flex items-start gap-2 text-sm bg-green-50 p-3 rounded-lg border border-green-200">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium text-gray-700 capitalize">{field.field_name.replace(/_/g, ' ')}</div>
                        <div className="text-gray-600">{field.reason}</div>
                        <div className="text-xs text-gray-500">
                          Score: {field.score_percentage}% (Weight: {field.weight_percentage}%)
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {contact.reasons.map((reason: string, index: number) => (
                  <div key={index} className="flex items-start gap-2 text-sm bg-green-50 p-3 rounded-lg border border-green-200">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{reason}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Collapsible component for V2 Detailed Scoring Breakdown
const CollapsibleDetailedScoring: React.FC<{ contact: any }> = ({ contact }) => {
  const [showDetailedScoring, setShowDetailedScoring] = useState(false);
  
  return (
    <div className="p-6 pb-4 border-b border-gray-100">
      <button
        onClick={() => setShowDetailedScoring(!showDetailedScoring)}
        className="w-full flex items-center justify-between mb-4 hover:bg-gray-50 p-2 rounded transition-colors"
      >
        <h5 className="font-semibold text-gray-900 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-purple-600" />
          V2 Detailed Scoring Breakdown
          {contact.scoring_summary && (
            <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 border-blue-300">
              Total: {contact.scoring_summary.total_score_percentage}%
            </Badge>
          )}
        </h5>
        <ChevronRight className={`h-5 w-5 transform transition-transform ${showDetailedScoring ? 'rotate-90' : ''} text-gray-400`} />
      </button>
      
      {showDetailedScoring && (
        <>
          {/* Show V2 Scoring Summary if available */}
          {contact.scoring_summary ? (
            <div className="space-y-4">
              {/* Position Type Info */}
              <div className="p-3 bg-blue-50 rounded border border-blue-200">
                <div className="text-sm text-blue-800">
                  <strong>Position Type:</strong> {contact.scoring_summary.position_type} | 
                  <strong>Capital Position:</strong> {contact.scoring_summary.capital_position}
                </div>
              </div>
              
              {/* Field-by-Field Breakdown */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide mb-3">Field Scoring Breakdown</h6>
                  
                  {/* Matched Fields (Score > 0) */}
                  <div className="space-y-2 mb-4">
                    {contact.scoring_summary.field_breakdown
                      .filter((field: any) => field.score_percentage > 0)
                      .map((field: any, index: number) => (
                      <div key={index} className="p-2 bg-green-50 rounded border border-green-200">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-green-700 capitalize font-medium">{field.field_name.replace(/_/g, ' ')}:</span>
                          <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                            {field.score_percentage}% (weight: {field.weight_percentage}%)
                          </Badge>
                        </div>
                        <div className="text-xs text-green-600">
                          {field.reason}
                        </div>
                        <div className="text-xs text-green-500">
                          Confidence: {Math.round(field.confidence * 100)}%
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Non-Matched Fields (Score = 0) - Collapsible */}
                  <CollapsibleNonMatchedFields 
                    fields={contact.scoring_summary.field_breakdown.filter((field: any) => field.score_percentage === 0)} 
                  />
                </div>
                
                <div>
                  <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide mb-3">Top Matching Fields</h6>
                  <div className="space-y-2">
                    {contact.scoring_summary.top_matches.map((match: any, index: number) => (
                      <div key={index} className="p-2 bg-green-50 rounded border border-green-200">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-gray-700 capitalize">{match.field.replace(/_/g, ' ')}</span>
                          <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                            {match.score}%
                          </Badge>
                        </div>
                        <div className="text-xs text-gray-600">
                          {match.reason}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Weights Used */}
                  {contact.scoring_summary.weights_used && (
                    <div className="mt-4">
                      <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide mb-2">Weights Used</h6>
                      <div className="text-xs text-gray-600 space-y-1">
                        {Object.entries(contact.scoring_summary.weights_used).map(([field, weight]) => (
                          <div key={field} className="flex justify-between">
                            <span className="capitalize">{field.replace(/_/g, ' ')}:</span>
                            <span>{weight as number}%</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-gray-600 italic">
              V2 detailed scoring data not available for this contact.
            </div>
          )}
        </>
      )}
    </div>
  );
};

interface DealMatchingTabProps {
  dealId: string;
  deal?: any;
}

interface MatchingContact {
  contact_id: number;
  company_id: number;
  company_name: string;
  contact_name: string;
  email: string;
  phone: string;
  capital_position: string;
  capital_positions: string[]; // All capital positions this contact can provide
  match_score: number;
  confidence: number;
  reasons: string[];
  criteria_source: string;
  criteria_sources: string[]; // All criteria sources (Contact, Company, etc.)
  investment_criteria_id: number;
  investment_criteria_ids: number[]; // All investment criteria IDs
  total_criteria_matched: number; // How many criteria matched
  criteria_details: Array<{ // Details for each individual criteria match
    capital_position: string;
    criteria_source: string;
    investment_criteria_id: number;
    individual_score: number;
    reasons: string[];
  }>;
  contact_data: any;
  deal_data: any;
  // V2 Detailed Scoring Data
  scoring_summary?: {
    total_score_percentage: number;
    field_breakdown: Array<{
      field_name: string;
      score_percentage: number;
      weight_percentage: number;
      reason: string;
      confidence: number;
    }>;
    top_matches: Array<{
      field: string;
      score: number;
      reason: string;
    }>;
    weights_used: { [key: string]: number };
    position_type: string;
    capital_position: string;
  };
  detailed_scoring?: any[];
  total_score?: number;
}

const DealMatchingTab: React.FC<DealMatchingTabProps> = ({ dealId, deal }) => {
  const router = useRouter();
  const [matchingContacts, setMatchingContacts] = useState<MatchingContact[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    minScore: 0.0, // Start with no score filter to see all results
    capitalPosition: '',
    showOnlyHighConfidence: false,
    searchTerm: ''
  });
  const [expandedContactId, setExpandedContactId] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [isCrmMode, setIsCrmMode] = useState(false);

  // Navigate to contact detail page
  const navigateToContact = (contactId: number) => {
    router.push(`/dashboard/people/${contactId}`);
  };

  // Navigate to company detail page
  const navigateToCompany = (companyId: number) => {
    router.push(`/dashboard/companies/${companyId}`);
  };

  // Fetch matching contacts for this deal
  const fetchMatchingContacts = async () => {
    if (!dealId) return;
    
    console.log('🔍 Fetching matching contacts for deal:', dealId);
    setLoading(true);
    setError(null);
    
    try {
      const crmModeParam = isCrmMode ? '?crm_mode=true' : '';
      const response = await fetch(`/api/matching-v2/contacts-for-deal/${dealId}${crmModeParam}`);
      console.log('📡 API Response status:', response.status);
      console.log('🔧 CRM mode enabled:', isCrmMode);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch matching contacts: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('📊 API Response data:', data);
      console.log('👥 Contacts array:', data.contacts);
      console.log('📈 Contacts count:', data.contacts ? data.contacts.length : 'undefined');
      console.log('🔍 Full response structure:', Object.keys(data));
      console.log('📝 Response message:', data.message);
      console.log('📊 Total count:', data.total);
      console.log('📄 Pagination:', data.pagination);
      
      // Check if contacts is an array and has content
      if (Array.isArray(data.contacts)) {
        console.log('✅ Contacts is an array with length:', data.contacts.length);
        if (data.contacts.length > 0) {
          console.log('📋 First contact sample:', data.contacts[0]);
          console.log('🔑 First contact keys:', Object.keys(data.contacts[0]));
        }
      } else {
        console.log('❌ Contacts is not an array:', typeof data.contacts);
        console.log('📋 Contacts value:', data.contacts);
      }
      
      setMatchingContacts(data.contacts || []);
      
      if (data.contacts && data.contacts.length > 0) {
        console.log('✅ Setting matching contacts:', data.contacts.length);
        toast.success(`Found ${data.contacts.length} matching contacts`);
      } else {
        console.log('⚠️ No contacts found in response');
        console.log('🔍 Checking if this is due to no capital positions or other issues');
        if (data.deal) {
          console.log('📋 Deal info from response:', data.deal);
        }
        toast.info("No matching contacts found for this deal");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch matching contacts';
      console.error('❌ Error fetching contacts:', err);
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      console.log('🏁 Fetch completed, loading set to false');
    }
  };

  // Filter contacts based on current filters
  const filteredContacts = matchingContacts.filter(contact => {
    let shouldInclude = true;
    let filterReason = '';
    
    if (filters.minScore > 0 && contact.match_score < filters.minScore) {
      shouldInclude = false;
      filterReason = `Score ${contact.match_score} below threshold ${filters.minScore}`;
    }
    if (filters.capitalPosition) {
      const hasMatchingCapitalPosition = contact.capital_positions && contact.capital_positions.length > 0 
        ? contact.capital_positions.includes(filters.capitalPosition)
        : contact.capital_position === filters.capitalPosition;
      
      if (!hasMatchingCapitalPosition) {
        shouldInclude = false;
        filterReason = `Capital positions [${contact.capital_positions?.join(', ') || contact.capital_position}] don't include ${filters.capitalPosition}`;
      }
    }
    if (filters.showOnlyHighConfidence && contact.confidence < 0.8) {
      shouldInclude = false;
      filterReason = `Confidence ${contact.confidence} below 0.8 threshold`;
    }
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const matchesSearch = (
        contact.contact_name.toLowerCase().includes(searchLower) ||
        contact.company_name.toLowerCase().includes(searchLower) ||
        contact.email.toLowerCase().includes(searchLower)
      );
      if (!matchesSearch) {
        shouldInclude = false;
        filterReason = `Search term "${filters.searchTerm}" not found`;
      }
    }
    
    if (!shouldInclude) {
      console.log(`🔍 Contact ${contact.contact_id} filtered out: ${filterReason}`);
    }
    
    return shouldInclude;
  });

  // Get unique capital positions from matching contacts (including multiple positions per contact)
  const capitalPositions = [...new Set(
    matchingContacts.flatMap(c => 
      c.capital_positions && c.capital_positions.length > 0 
        ? c.capital_positions 
        : [c.capital_position]
    )
  )].sort();

  // Get score color based on match score
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return "bg-green-100 text-green-800 border-green-200";
    if (score >= 0.6) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    if (score >= 0.4) return "bg-orange-100 text-orange-800 border-orange-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  // Get confidence color
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-600";
    if (confidence >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  // Format score as percentage
  const formatScore = (score: number) => `${Math.round(score * 100)}%`;

  // Load matching contacts when component mounts
  useEffect(() => {
    if (dealId) {
      console.log('🚀 Component mounted/CRM mode changed, fetching contacts for deal:', dealId);
      fetchMatchingContacts();
    }
  }, [dealId, isCrmMode]);

  // Debug logging for state changes
  useEffect(() => {
    console.log('📊 State update - matchingContacts:', matchingContacts.length);
    console.log('📊 State update - loading:', loading);
    console.log('📊 State update - error:', error);
    console.log('📊 State update - filteredContacts:', filteredContacts.length);
    
    // Additional debugging for matching contacts
    if (matchingContacts.length > 0) {
      console.log('📋 First matching contact:', matchingContacts[0]);
      console.log('🔑 First contact structure:', Object.keys(matchingContacts[0]));
    }
    
    // Debug filtered contacts
    if (filteredContacts.length !== matchingContacts.length) {
      console.log('🔍 Filtering applied - original:', matchingContacts.length, 'filtered:', filteredContacts.length);
      console.log('🔍 Filter settings:', filters);
    }
  }, [matchingContacts, loading, error, filteredContacts, filters]);

  return (
    <div className="space-y-6">
      {/* Header with action buttons */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Deal Matching (V2)</h3>
          <p className="text-sm text-gray-600">
            Find contacts that match this deal's investment criteria
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="crm-mode-deal-matching"
              checked={isCrmMode}
              onCheckedChange={(checked) => setIsCrmMode(checked as boolean)}
            />
            <label htmlFor="crm-mode-deal-matching" className="text-sm font-medium text-gray-700">
              CRM Mode
            </label>
          </div>
          {isCrmMode && (
            <div className="text-xs text-gray-600 bg-blue-50 px-2 py-1 rounded">
              Internal deals only, within 1.6 years
            </div>
          )}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </Button>
            <Button
              onClick={fetchMatchingContacts}
              disabled={loading}
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Debug Info */}
      <Card className="bg-gray-50 border-gray-200">
        <CardContent className="pt-4">
          <div className="text-xs text-gray-600 space-y-1">
            <div><strong>Debug Info:</strong></div>
            <div>Deal ID: {dealId}</div>
            <div>CRM Mode: {isCrmMode ? 'Enabled' : 'Disabled'}</div>
            <div>Loading: {loading ? 'Yes' : 'No'}</div>
            <div>Error: {error || 'None'}</div>
            <div>Matching Contacts: {matchingContacts.length}</div>
            <div>Filtered Contacts: {filteredContacts.length}</div>
            <div>Capital Positions: {capitalPositions.join(', ') || 'None'}</div>
          </div>
        </CardContent>
      </Card>

      {/* Filters Section */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Matching Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="minScore">Minimum Score</Label>
                <Select
                  value={filters.minScore.toString()}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, minScore: parseFloat(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0.0">Any Score</SelectItem>
                    <SelectItem value="0.05">5%+</SelectItem>
                    <SelectItem value="0.1">10%+</SelectItem>
                    <SelectItem value="0.2">20%+</SelectItem>
                    <SelectItem value="0.3">30%+</SelectItem>
                    <SelectItem value="0.5">50%+</SelectItem>
                    <SelectItem value="0.7">70%+</SelectItem>
                    <SelectItem value="0.8">80%+</SelectItem>
                    <SelectItem value="0.9">90%+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="capitalPosition">Capital Position</Label>
                <Select
                  value={filters.capitalPosition}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, capitalPosition: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Positions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Positions</SelectItem>
                    {capitalPositions.map(position => (
                      <SelectItem key={position} value={position}>
                        {position}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="searchTerm">Search</Label>
                <Input
                  id="searchTerm"
                  placeholder="Name, company, email..."
                  value={filters.searchTerm}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                />
              </div>
              
              <div className="flex items-center space-x-2 pt-6">
                <Checkbox
                  id="highConfidence"
                  checked={filters.showOnlyHighConfidence}
                  onCheckedChange={(checked) => 
                    setFilters(prev => ({ ...prev, showOnlyHighConfidence: checked === true }))
                  }
                />
                <Label htmlFor="highConfidence">High Confidence Only (80%+)</Label>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Error:</span>
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600">Finding matching contacts...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {(() => {
        console.log('🎨 Results summary condition check:', {
          loading,
          matchingContactsLength: matchingContacts.length,
          shouldShowSummary: !loading && matchingContacts.length > 0
        });
        return null;
      })()}
      {!loading && matchingContacts.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="font-medium">
                    {filteredContacts.length} of {matchingContacts.length} contacts
                  </span>
                </div>
                {filters.minScore > 0 && (
                  <Badge variant="outline">
                    Score: {filters.minScore * 100}%+
                  </Badge>
                )}
                {filters.capitalPosition && (
                  <Badge variant="outline">
                    Position: {filters.capitalPosition}
                  </Badge>
                )}
              </div>
              <div className="text-sm text-gray-600">
                Using V2 matching with capital position field weights
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Results */}
      {(() => {
        console.log('🎨 No results condition check:', {
          loading,
          matchingContactsLength: matchingContacts.length,
          shouldShowNoResults: !loading && matchingContacts.length === 0
        });
        return null;
      })()}
      {/* No Results - Either no contacts found or all filtered out */}
      {!loading && matchingContacts.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Target className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Matching Contacts Found</h3>
              <p className="text-gray-600 max-w-md mx-auto mb-6">
                This could be because:
              </p>
              <ul className="text-sm text-gray-600 max-w-md mx-auto text-left space-y-1">
                <li>• No contacts have matching investment criteria</li>
                <li>• Capital position requirements don't align</li>
                <li>• Deal parameters are outside contact preferences</li>
                <li>• Investment criteria data is incomplete</li>
              </ul>
              <Button onClick={fetchMatchingContacts} className="mt-4">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {!loading && matchingContacts.length > 0 && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-900">
                  Found {matchingContacts.length} matching contacts
                </span>
                {filteredContacts.length !== matchingContacts.length && (
                  <Badge variant="outline" className="text-green-700 border-green-300">
                    {filteredContacts.length} after filtering
                  </Badge>
                )}
              </div>
              <div className="text-sm text-green-700">
                Showing contacts with {filters.minScore > 0 ? `${Math.round(filters.minScore * 100)}%+` : 'any'} match score
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Matching Contacts List - V1 + V2 Hybrid */}
      {(() => {
        console.log('🎨 Render condition check:', {
          loading,
          filteredContactsLength: filteredContacts.length,
          matchingContactsLength: matchingContacts.length,
          shouldRender: !loading && filteredContacts.length > 0
        });
        return null;
      })()}
      {!loading && filteredContacts.length > 0 && (
        <div className="space-y-4">
          {filteredContacts.map((contact) => (
            <Card key={contact.contact_id} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
              <CardContent className="p-0">
                {/* Header Section with Score and Basic Info */}
                <div className="p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-white">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={`${getScoreColor(contact.match_score)} text-sm font-semibold px-3 py-1`}>
                            {formatScore(contact.match_score)} Match
                          </Badge>
                          <Badge variant="outline" className={`${getConfidenceColor(contact.confidence)} text-sm px-3 py-1`}>
                            {formatScore(contact.confidence)} Confidence
                          </Badge>
                        </div>
                        {contact.capital_positions && contact.capital_positions.length > 1 ? (
                          <div className="flex flex-wrap gap-1">
                            {contact.capital_positions.map((pos, index) => (
                              <Badge key={index} variant="secondary" className="px-2 py-1 text-xs">
                                {pos}
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          <Badge variant="secondary" className="px-3 py-1">
                            {contact.capital_position}
                          </Badge>
                        )}
                        <Badge variant="outline" className="text-xs px-2 py-1 border-gray-300">
                          {contact.criteria_source}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-6">
                        <div>
                          <h4 
                            className="text-xl font-semibold text-gray-900 mb-1 cursor-pointer hover:text-blue-600 transition-colors flex items-center gap-2 group"
                            onClick={() => navigateToContact(contact.contact_id)}
                            title="Click to view contact details"
                          >
                            {contact.contact_name}
                            <Users className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                          </h4>
                          <p 
                            className="text-lg text-gray-700 font-medium cursor-pointer hover:text-blue-600 transition-colors flex items-center gap-2 group"
                            onClick={() => navigateToCompany(contact.company_id)}
                            title="Click to view company details"
                          >
                            {contact.company_name}
                            <Building2 className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                          </p>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          {contact.email && (
                            <div className="flex items-center gap-2">
                              <span className="text-blue-600">📧</span>
                              <span className="font-medium">{contact.email}</span>
                            </div>
                          )}
                          {contact.phone && (
                            <div className="flex items-center gap-2">
                              <span className="text-green-600">📞</span>
                              <span className="font-medium">{contact.phone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex flex-col gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (expandedContactId === contact.contact_id) {
                            setExpandedContactId(null);
                          } else {
                            setExpandedContactId(contact.contact_id);
                          }
                        }}
                        className="min-w-[120px]"
                      >
                        {expandedContactId === contact.contact_id ? 'Hide Details' : 'View Details'}
                      </Button>
                      <Button 
                        size="sm" 
                        className="min-w-[120px] bg-blue-600 hover:bg-blue-700"
                        onClick={() => navigateToContact(contact.contact_id)}
                      >
                        <Users className="h-4 w-4 mr-2" />
                        View Contact
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Match Reasons Section - Now Collapsible */}
                <CollapsibleMatchReasons contact={contact} formatScore={formatScore} />

                {/* V2 Detailed Scoring Breakdown - Now Collapsible */}
                <CollapsibleDetailedScoring contact={contact} />

                {/* Investment Criteria Section - Always Visible */}
                <div className="p-6 pb-4 border-b border-gray-100">
                  {/* <h5 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Target className="h-5 w-5 text-blue-600" />
                    Investment Criteria
                  </h5> */}
                  </div>
                  {/* Show V2 Scoring Summary if available */}
                  {contact.scoring_summary ? (
                    <div className="space-y-4">
                      {/* Position Type Info */}
                      <div className="p-3 bg-blue-50 rounded border border-blue-200">
                        <div className="text-sm text-blue-800">
                          <strong>Position Type:</strong> {contact.scoring_summary.position_type} | 
                          <strong>Capital Position:</strong> {contact.scoring_summary.capital_position}
                        </div>
                      </div>
                      
                      {/* Field-by-Field Breakdown */}
                    
                    </div>
                  ) : (
                    /* Fallback to old display if no V2 scoring data */
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-3 bg-yellow-50 rounded border border-yellow-200">
                        <div className="text-sm text-yellow-800">
                          <strong>Note:</strong> V2 detailed scoring data not available. 
                          Showing simplified breakdown.
                        </div>
                      </div>
                      
                      {/* Deal vs Contact Matching */}
                      <div className="space-y-3">
                        <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Deal vs Contact Matching</h6>
                        <div className="space-y-2 text-sm">
                          {/* Property Type Match */}
                          {contact.contact_data?.property_types && contact.deal_data?.property_types && (
                            <div className="p-2 bg-gray-50 rounded border">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-gray-600">Property Type Match:</span>
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                  +20%
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                Deal: <span className="font-medium">{contact.deal_data.property_types.join(', ')}</span>
                              </div>
                              <div className="text-xs text-gray-500">
                                Contact: <span className="font-medium">{contact.contact_data.property_types.join(', ')}</span>
                              </div>
                            </div>
                          )}

                          {/* Strategy Match */}
                          {contact.contact_data?.strategies && contact.deal_data?.strategy && (
                            <div className="p-2 bg-gray-50 rounded border">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-gray-600">Strategy Match:</span>
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                  +10%
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                Deal: <span className="font-medium">{contact.deal_data.strategy}</span>
                              </div>
                              <div className="text-xs text-gray-500">
                                Contact: <span className="font-medium">{contact.contact_data.strategies.join(', ')}</span>
                              </div>
                            </div>
                          )}

                          {/* Deal Amount Match */}
                          {contact.contact_data?.deal_amount_min && contact.contact_data?.deal_amount_max && contact.deal_data?.deal_amount && (
                            <div className="p-2 bg-gray-50 rounded border">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-gray-600">Deal Amount Match:</span>
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                  +15%
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                Deal: <span className="font-medium">${(contact.deal_data.deal_amount / 1000000).toFixed(1)}M</span>
                              </div>
                              <div className="text-xs text-gray-500">
                                Contact Range: <span className="font-medium">${(contact.contact_data.deal_amount_min / 1000000).toFixed(1)}M - ${(contact.contact_data.deal_amount_max / 1000000).toFixed(1)}M</span>
                              </div>
                            </div>
                          )}

                          {/* Geographic Match */}
                          {contact.contact_data?.state && contact.deal_data?.location && (
                            <div className="p-2 bg-gray-50 rounded border">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-gray-600">Geographic Match:</span>
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                  +10%
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                Deal: <span className="font-medium">{contact.deal_data.location.state}</span>
                              </div>
                              <div className="text-xs text-gray-500">
                                Contact: <span className="font-medium">
                                  {Array.isArray(contact.contact_data.state) ? 
                                    contact.contact_data.state.join(', ') : 
                                    contact.contact_data.state
                                  }
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Return & Financial Matching */}
                      <div className="space-y-3">
                        <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Financial Criteria Matching</h6>
                        <div className="space-y-2 text-sm">
                          {/* Target Return Match */}
                          {contact.contact_data?.target_return && contact.deal_data?.totalInternalRateOfReturnIrr && (
                            <div className="p-2 bg-gray-50 rounded border">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-gray-600">Return Target Match:</span>
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                  +20%
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                Deal IRR: <span className="font-medium">{parseFloat(contact.deal_data.totalInternalRateOfReturnIrr) * 100}%</span>
                              </div>
                              <div className="text-xs text-gray-500">
                                Contact Target: <span className="font-medium">{contact.contact_data.target_return}%</span>
                              </div>
                            </div>
                          )}

                          {/* Capital Position Match */}
                          <div className="p-2 bg-gray-50 rounded border">
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-gray-600">Capital Position Match:</span>
                              <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                +25%
                              </Badge>
                            </div>
                            <div className="text-xs text-gray-500">
                              Deal Needs: <span className="font-medium">{contact.capital_position}</span>
                            </div>
                            <div className="text-xs text-gray-500">
                              Contact Provides: <span className="font-medium">{contact.capital_position}</span>
                            </div>
                          </div>

                          {/* Hold Period Match */}
                          {contact.contact_data?.min_hold_period_years && contact.contact_data?.max_hold_period_years && (
                            <div className="p-2 bg-gray-50 rounded border">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-gray-600">Hold Period Match:</span>
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                  +5%
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                Contact Range: <span className="font-medium">{contact.contact_data.min_hold_period_years}-{contact.contact_data.max_hold_period_years} years</span>
                              </div>
                            </div>
                          )}

                          {/* Equity Multiple Match */}
                          {contact.contact_data?.minimum_equity_multiple && (
                            <div className="p-2 bg-gray-50 rounded border">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-gray-600">Equity Multiple Match:</span>
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                  +10%
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                Contact Target: <span className="font-medium">{contact.contact_data.minimum_equity_multiple}x</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                {/* Investment Criteria Section - Always Visible */}
                <div className="p-6 pb-4 border-b border-gray-100">
                  <h5 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Target className="h-5 w-5 text-blue-600" />
                    Investment Criteria
                  </h5>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Deal Amount & Financial Criteria */}
                    <div className="space-y-3">
                      <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Financial Criteria</h6>
                      <div className="space-y-2 text-sm">
                        {contact.contact_data?.target_return && (
                          <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span className="text-gray-600">Target Return:</span>
                            <span className="font-semibold text-green-700">{contact.contact_data.target_return}%</span>
                          </div>
                        )}
                        {contact.contact_data?.deal_amount_min && contact.contact_data?.deal_amount_max && (
                          <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span className="text-gray-600">Deal Amount:</span>
                            <span className="font-semibold text-blue-700">
                              ${(contact.contact_data.deal_amount_min / 1000000).toFixed(1)}M - ${(contact.contact_data.deal_amount_max / 1000000).toFixed(1)}M
                            </span>
                          </div>
                        )}
                        {contact.contact_data?.minimum_equity_multiple && (
                          <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span className="text-gray-600">Min Equity Multiple:</span>
                            <span className="font-semibold text-purple-700">{contact.contact_data.minimum_equity_multiple}x</span>
                          </div>
                        )}
                        {contact.contact_data?.min_hold_period_years && contact.contact_data?.max_hold_period_years && (
                          <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span className="text-gray-600">Hold Period:</span>
                            <span className="font-semibold text-orange-700">
                              {contact.contact_data.min_hold_period_years}-{contact.contact_data.max_hold_period_years} years
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Property Types & Strategies */}
                    <div className="space-y-3">
                      <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Investment Preferences</h6>
                      <div className="space-y-2 text-sm">
                        {contact.contact_data?.property_types && (
                          <div className="p-2 bg-gray-50 rounded">
                            <span className="text-gray-600 block mb-1">Property Types:</span>
                            <div className="flex flex-wrap gap-1">
                              {contact.contact_data.property_types.map((type, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs px-2 py-1">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        {contact.contact_data?.strategies && (
                          <div className="p-2 bg-gray-50 rounded">
                            <span className="text-gray-600 block mb-1">Strategies:</span>
                            <div className="flex flex-wrap gap-1">
                              {contact.contact_data.strategies.map((strategy, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">
                                  {strategy}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Geographic Preferences */}
                    <div className="space-y-3">
                      <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Geographic Focus</h6>
                      <div className="space-y-2 text-sm">
                        {contact.contact_data?.region && (
                          <div className="p-2 bg-gray-50 rounded">
                            <span className="text-gray-600 block mb-1">Regions:</span>
                            <div className="flex flex-wrap gap-1">
                              {Array.isArray(contact.contact_data.region) ? 
                                contact.contact_data.region.map((region, idx) => (
                                  <Badge key={idx} variant="outline" className="text-xs px-2 py-1">
                                    {region}
                                  </Badge>
                                )) :
                                <Badge variant="outline" className="text-xs px-2 py-1">
                                  {contact.contact_data.region}
                                </Badge>
                              }
                            </div>
                          </div>
                        )}
                        {contact.contact_data?.state && (
                          <div className="p-2 bg-gray-50 rounded">
                            <span className="text-gray-600 block mb-1">States:</span>
                            <div className="flex flex-wrap gap-1">
                              {Array.isArray(contact.contact_data.state) ? 
                                contact.contact_data.state.slice(0, 5).map((state, idx) => (
                                  <Badge key={idx} variant="outline" className="text-xs px-2 py-1">
                                    {state}
                                  </Badge>
                                )) :
                                <Badge variant="outline" className="text-xs px-2 py-1">
                                  {contact.contact_data.state}
                                </Badge>
                              }
                              {Array.isArray(contact.contact_data.state) && contact.contact_data.state.length > 5 && (
                                <Badge variant="outline" className="text-xs px-2 py-1 text-gray-500">
                                  +{contact.contact_data.state.length - 5} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Deal Match Details - Always Visible */}
                <div className="p-6 pb-4 border-b border-gray-100">
                  <h5 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-purple-600" />
                    Deal Match Details
                  </h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Deal Information</h6>
                      <div className="space-y-2 text-sm">
                        {contact.deal_data?.deal_amount && (
                          <div className="flex justify-between items-center p-2 bg-purple-50 rounded border border-purple-200">
                            <span className="text-gray-700">Deal Amount:</span>
                            <span className="font-semibold text-purple-700">
                              ${(contact.deal_data.deal_amount / 1000000).toFixed(1)}M
                            </span>
                          </div>
                        )}
                        {contact.deal_data?.location && (
                          <div className="flex justify-between items-center p-2 bg-purple-50 rounded border border-purple-200">
                            <span className="text-gray-700">Location:</span>
                            <span className="font-semibold text-purple-700">
                              {contact.deal_data.location.city}, {contact.deal_data.location.state}
                            </span>
                          </div>
                        )}
                        {contact.deal_data?.property_types && (
                          <div className="p-2 bg-purple-50 rounded border border-purple-200">
                            <span className="text-gray-700 block mb-1">Property Types:</span>
                            <div className="flex flex-wrap gap-1">
                              {contact.deal_data.property_types.map((type, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs px-2 py-1 bg-purple-100 text-purple-700 border-purple-300">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Match Analysis</h6>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between items-center p-2 bg-blue-50 rounded border border-blue-200">
                          <span className="text-gray-700">Match Score:</span>
                          <span className="font-semibold text-blue-700">{formatScore(contact.match_score)}</span>
                        </div>
                        <div className="flex justify-between items-center p-2 bg-blue-50 rounded border border-blue-200">
                          <span className="text-gray-700">Confidence:</span>
                          <span className="font-semibold text-blue-700">{formatScore(contact.confidence)}</span>
                        </div>
                        <div className="flex justify-between items-center p-2 bg-blue-50 rounded border border-blue-200">
                          <span className="text-gray-700">Capital Position:</span>
                          <span className="font-semibold text-blue-700">{contact.capital_position}</span>
                        </div>
                        <div className="flex justify-between items-center p-2 bg-blue-50 rounded border border-blue-200">
                          <span className="text-gray-700">Criteria Source:</span>
                          <span className="font-semibold text-blue-700">{contact.criteria_source}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Multiple Criteria Summary Section */}
                {contact.total_criteria_matched > 1 && (
                  <div className="p-6 bg-gradient-to-r from-purple-50 to-indigo-50 border-t border-gray-200">
                    <div className="flex items-center gap-3 mb-4">
                      <Users className="h-6 w-6 text-purple-600" />
                      <div>
                        <h5 className="font-semibold text-gray-900">Multiple Criteria Matched</h5>
                        <p className="text-sm text-gray-600">
                          This contact matched {contact.total_criteria_matched} different investment criteria
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Capital Positions */}
                      <div className="bg-white p-3 rounded-lg border border-purple-200">
                        <h6 className="font-medium text-purple-900 mb-2">Capital Positions Available</h6>
                        <div className="flex flex-wrap gap-2">
                          {contact.capital_positions.map((pos, index) => (
                            <Badge key={index} variant="outline" className="bg-purple-100 text-purple-700 border-purple-300">
                              {pos}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Criteria Sources */}
                      <div className="bg-white p-3 rounded-lg border border-purple-200">
                        <h6 className="font-medium text-purple-900 mb-2">Criteria Sources</h6>
                        <div className="flex flex-wrap gap-2">
                          {contact.criteria_sources.map((source, index) => (
                            <Badge key={index} variant="outline" className="bg-indigo-100 text-indigo-700 border-indigo-300">
                              {source}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Individual Criteria Breakdown */}
                    <div className="mt-4">
                      <h6 className="font-medium text-purple-900 mb-3">Individual Criteria Scores</h6>
                      <div className="space-y-2">
                        {contact.criteria_details.map((criteria, index) => (
                          <div key={index} className="bg-white p-3 rounded-lg border border-purple-200">
                            <div className="flex justify-between items-center mb-2">
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="bg-purple-100 text-purple-700 border-purple-300">
                                  {criteria.capital_position}
                                </Badge>
                                <Badge variant="outline" className="bg-indigo-100 text-indigo-700 border-indigo-300">
                                  {criteria.criteria_source}
                                </Badge>
                                <span className="text-xs text-gray-500">ID: {criteria.investment_criteria_id}</span>
                              </div>
                              <div className="text-right">
                                <span className="text-sm font-medium text-purple-700">
                                  {formatScore(criteria.individual_score)}
                                </span>
                              </div>
                            </div>
                            <div className="text-xs text-gray-600">
                              <span className="font-medium">Reasons:</span> {criteria.reasons.join(', ')}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Score Summary Section */}
                <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <TrendingUp className="h-6 w-6 text-green-600" />
                      <div>
                        <h5 className="font-semibold text-gray-900">Final Match Score: {formatScore(contact.match_score)}</h5>
                        <p className="text-sm text-gray-600">
                          Based on {contact.total_criteria_matched || 1} matching criteria
                          {contact.total_criteria_matched > 1 && ` (Best: ${formatScore(contact.match_score)})`}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">{formatScore(contact.match_score)}</div>
                      <div className="text-xs text-gray-500">Match Score</div>
                    </div>
                  </div>
                </div>

                {/* Additional Details Section - Expandable */}
                {expandedContactId === contact.contact_id && (
                  <div className="p-6 bg-gray-50">
                    <h5 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Info className="h-5 w-5 text-gray-600" />
                      Additional Details
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Debt-Specific Criteria */}
                      {contact.contact_data?.loan_to_value_min || contact.contact_data?.loan_to_value_max ? (
                        <div className="space-y-3">
                          <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Debt Criteria</h6>
                          <div className="space-y-2 text-sm">
                            {contact.contact_data?.loan_to_value_min && contact.contact_data?.loan_to_value_max && (
                              <div className="flex justify-between items-center p-2 bg-gray-100 rounded">
                                <span className="text-gray-600">LTV Range:</span>
                                <span className="font-medium">{contact.contact_data.loan_to_value_min}% - {contact.contact_data.loan_to_value_max}%</span>
                              </div>
                            )}
                            {contact.contact_data?.loan_to_cost_min && contact.contact_data?.loan_to_cost_max && (
                              <div className="flex justify-between items-center p-2 bg-gray-100 rounded">
                                <span className="text-gray-600">LTC Range:</span>
                                <span className="font-medium">{contact.contact_data.loan_to_cost_min}% - {contact.contact_data.loan_to_cost_max}%</span>
                              </div>
                            )}
                            {contact.contact_data?.min_loan_term && contact.contact_data?.max_loan_term && (
                              <div className="flex justify-between items-center p-2 bg-gray-100 rounded">
                                <span className="text-gray-600">Loan Term:</span>
                                <span className="font-medium">{contact.contact_data.min_loan_term} - {contact.contact_data.max_loan_term} years</span>
                              </div>
                            )}
                            {contact.contact_data?.min_loan_dscr && contact.contact_data?.max_loan_dscr && (
                              <div className="flex justify-between items-center p-2 bg-gray-100 rounded">
                                <span className="text-gray-600">DSCR Range:</span>
                                <span className="font-medium">{contact.contact_data.min_loan_dscr} - {contact.contact_data.max_loan_dscr}</span>
                              </div>
                            )}
                            {contact.contact_data?.loan_interest_rate && (
                              <div className="flex justify-between items-center p-2 bg-gray-100 rounded">
                                <span className="text-gray-600">Interest Rate:</span>
                                <span className="font-medium">{contact.contact_data.loan_interest_rate}%</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ) : null}

                      {/* Company & Contact Details */}
                      <div className="space-y-3">
                        <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide">Contact Details</h6>
                        <div className="space-y-2 text-sm">
                          <div className="p-2 bg-gray-100 rounded">
                            <span className="text-gray-600 block mb-1">Investment Criteria ID:</span>
                            <span className="font-medium">{contact.investment_criteria_id}</span>
                          </div>
                          {contact.contact_data?.country && (
                            <div className="p-2 bg-gray-100 rounded">
                              <span className="text-gray-600 block mb-1">Countries:</span>
                              <div className="flex flex-wrap gap-1">
                                {Array.isArray(contact.contact_data.country) ? 
                                  contact.contact_data.country.map((country, idx) => (
                                    <Badge key={idx} variant="outline" className="text-xs px-2 py-1">
                                      {country}
                                    </Badge>
                                  )) :
                                  <Badge variant="outline" className="text-xs px-2 py-1">
                                    {contact.contact_data.country}
                                  </Badge>
                                }
                              </div>
                            </div>
                          )}
                          {contact.contact_data?.city && (
                            <div className="p-2 bg-gray-100 rounded">
                              <span className="text-gray-600 block mb-1">Cities:</span>
                              <div className="flex flex-wrap gap-1">
                                {Array.isArray(contact.contact_data.city) ? 
                                  contact.contact_data.city.slice(0, 8).map((city, idx) => (
                                    <Badge key={idx} variant="outline" className="text-xs px-2 py-1">
                                      {city}
                                    </Badge>
                                  )) :
                                  <Badge variant="outline" className="text-xs px-2 py-1">
                                    {contact.contact_data.city}
                                  </Badge>
                                }
                                {Array.isArray(contact.contact_data.city) && contact.contact_data.city.length > 8 && (
                                  <Badge variant="outline" className="text-xs px-2 py-1 text-gray-500">
                                    +{contact.contact_data.city.length - 8} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* No Results After Filtering */}
      {!loading && matchingContacts.length > 0 && filteredContacts.length === 0 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <AlertCircle className="h-16 w-16 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-yellow-900 mb-2">No Contacts Match Current Filters</h3>
              <p className="text-yellow-700 max-w-md mx-auto mb-4">
                {matchingContacts.length} contacts were found, but none match your current filter criteria.
              </p>
              <div className="text-sm text-yellow-600 mb-4">
                <p><strong>Current filters:</strong></p>
                <ul className="mt-2 space-y-1">
                  {filters.minScore > 0 && <li>• Minimum score: {Math.round(filters.minScore * 100)}%+</li>}
                  {filters.capitalPosition && <li>• Capital position: {filters.capitalPosition}</li>}
                  {filters.showOnlyHighConfidence && <li>• High confidence only (80%+)</li>}
                  {filters.searchTerm && <li>• Search term: "{filters.searchTerm}"</li>}
                </ul>
              </div>
              <Button 
                variant="outline" 
                onClick={() => setFilters({
                  minScore: 0.0,
                  capitalPosition: '',
                  showOnlyHighConfidence: false,
                  searchTerm: ''
                })}
                className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Reset Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* V2 System Info */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-2">V2 Matching System Features</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <p>• <strong>Capital Position Field Weights:</strong> Uses configurable weights per capital position from MCP</p>
                <p>• <strong>Smart Scoring:</strong> Advanced matching based on deal amount, location, property types, and more</p>
                <p>• <strong>Central Investment Criteria:</strong> Matches against debt and equity specific investment criteria tables</p>
                <p>• <strong>Confidence Scoring:</strong> Each match includes a confidence level based on data quality</p>
                <p>• <strong>Real-time Updates:</strong> Automatically refreshes when deal criteria changes</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DealMatchingTab;
