import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { TrendingUp, Trash2 } from "lucide-react";
import { DealV2, DealNsfFieldV2, InvestmentCriteriaEquityV2 } from "./shared/types-v2";
import { formatNumber, formatRawCurrency, formatPercentage } from "@/lib/utils/formatters";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface EquityTabProps {
  deal: DealV2;
  currentDeal: DealV2 | null;
  isEditing: boolean;
  onEquityCriteriaChange?: (criteriaId: number, field: keyof InvestmentCriteriaEquityV2, value: any) => void;
  onNsfFieldChange?: (nsfId: number, field: keyof DealNsfFieldV2, value: any) => void;
  onEquityCriteriaDelete?: (criteriaId: number) => void;
}

const EquityTab: React.FC<EquityTabProps> = ({
  deal,
  currentDeal,
  isEditing,
  onEquityCriteriaChange,
  onNsfFieldChange,
  onEquityCriteriaDelete,
}) => {
  const [percentageInputs, setPercentageInputs] = useState<{ [key: string]: string }>({});
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [criteriaToDelete, setCriteriaToDelete] = useState<InvestmentCriteriaEquityV2 | null>(null);

  // Initialize percentage inputs when component mounts or nsfFields change
  useEffect(() => {
    const nsfFields = deal?.nsfFields || [];
    const initialInputs: { [key: string]: string } = {};
    nsfFields.forEach(nsf => {
      if (nsf.percentageOfTotal !== undefined && nsf.percentageOfTotal !== null) {
        // Store raw percentage directly (no conversion needed)
        initialInputs[nsf.id] = nsf.percentageOfTotal ? Number(nsf.percentageOfTotal).toFixed(2) : "0.00";
      }
    });
    setPercentageInputs(initialInputs);
  }, [deal?.nsfFields]);

  // Group equity criteria by capital position
  const equityCriteriaByPosition = new Map<string, any[]>();
  
  if (deal.investmentCriteriaEquity) {
    deal.investmentCriteriaEquity.forEach(criteria => {
      // Handle both array and string formats for capital position
      const capitalPositions = Array.isArray(criteria.capitalPosition) 
        ? criteria.capitalPosition 
        : criteria.capitalPosition ? [criteria.capitalPosition] : [];
      
      capitalPositions.forEach(position => {
        if (!equityCriteriaByPosition.has(position)) {
          equityCriteriaByPosition.set(position, []);
        }
        equityCriteriaByPosition.get(position)!.push(criteria);
      });
    });
  }

  const renderCapitalPositionBreakdown = (capitalPosition: string, criteriaList: any[]) => {
    return (
      <div key={capitalPosition} className="border rounded-lg p-4 bg-gray-50 mb-4">
        <div className="flex justify-between items-center mb-3">
          <h4 className="font-medium text-gray-900 text-lg">{capitalPosition} Breakdown</h4>
          <Badge variant="outline" className="text-xs">
            {criteriaList.length} Investment Criteria Records
          </Badge>
        </div>
        
        {/* Show equity investment criteria for this capital position */}
        {criteriaList.map((criteria, criteriaIndex) => {
          // Get associated NSF data for this capital position - match sourceType against investment criteria capitalPosition
          const nsfFields = deal.nsfFields || (deal as any).nsf_fields || [];
          const associatedNsfData = nsfFields.filter(nsf => {
            // Match sourceType (e.g., "GP Equity", "LP Equity") against investment criteria capitalPosition
            const matchesSourceType = 
              nsf.sourceType === capitalPosition || 
              (nsf as any).source_type === capitalPosition;
            
            // If dealType is missing, infer from sourceType
            // Senior Debt, Mezzanine = debt type
            // GP Equity, LP Equity = equity type
            const inferredDealType = nsf.dealType || (nsf as any).deal_type || 
              (nsf.sourceType && ['Senior Debt', 'Mezzanine'].includes(nsf.sourceType) ? 'debt' : 
               nsf.sourceType && ['GP Equity', 'LP Equity'].includes(nsf.sourceType) ? 'equity' : null);
            
            const matchesDealType = inferredDealType === 'equity';
            
            return matchesSourceType && matchesDealType;
          }) || [];
          
          return (
            <div key={`criteria-${criteriaIndex}`} className="ml-4 p-3 bg-white rounded border mb-3">
              <h6 className="font-medium text-sm text-gray-700 mb-2">Investment Criteria</h6>
              
              {/* NSF Data - Show on top */}
              {associatedNsfData.length > 0 && (
                <div className="mb-4">
                  <h6 className="font-medium text-sm text-gray-700 mb-3">Sources</h6>
                  {associatedNsfData.map((nsf, nsfIndex) => (
                    <div key={`nsf-${nsfIndex}`} className="border rounded-lg p-4 bg-gray-50 mb-3">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">

                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Amount</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={nsf.amount || ''}
                              onChange={(e) => onNsfFieldChange?.(nsf.id, 'amount', parseFloat(e.target.value) || 0)}
                              className="mt-1"
                              placeholder="0"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amount)}</p>
                          )}
                        </div>
                        
                        {/* Second Row - Per GSF, Per ZFA, Per NSF */}
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Per GSF</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={nsf.amountPerGsf || ""}
                              onChange={(e) => onNsfFieldChange?.(nsf.id, 'amountPerGsf', parseFloat(e.target.value) || 0)}
                              className="mt-1"
                              placeholder="0"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerGsf)}</p>
                          )}
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Per ZFA</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={nsf.amountPerZfa || ""}
                              onChange={(e) => onNsfFieldChange?.(nsf.id, 'amountPerZfa', parseFloat(e.target.value) || 0)}
                              className="mt-1"
                              placeholder="0"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerZfa)}</p>
                          )}
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Per NSF</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={nsf.amountPerNsf || ""}
                              onChange={(e) => onNsfFieldChange?.(nsf.id, 'amountPerNsf', parseFloat(e.target.value) || 0)}
                              className="mt-1"
                              placeholder="0"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerNsf)}</p>
                          )}
                        </div>
                        {/* Third Row - Percentage and Measurements */}
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Percent</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              max="100"
                              value={percentageInputs[nsf.id] ?? (nsf.percentageOfTotal ? Number(nsf.percentageOfTotal).toFixed(2) : "")}
                              onChange={(e) => {
                                setPercentageInputs(prev => ({
                                  ...prev,
                                  [nsf.id]: e.target.value
                                }));
                              }}
                              onBlur={(e) => {
                                const value = parseFloat(e.target.value) || 0;
                                // Store raw percentage directly (no conversion needed)
                                onNsfFieldChange?.(nsf.id, 'percentageOfTotal', value);
                                // Clear the local input state after saving
                                setPercentageInputs(prev => {
                                  const newState = { ...prev };
                                  delete newState[nsf.id];
                                  return newState;
                                });
                              }}
                              className="mt-1"
                              placeholder="0.00"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {nsf.percentageOfTotal ? `${Number(nsf.percentageOfTotal).toFixed(2)}%` : "0.00%"}
                            </p>
                          )}
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">GSF (Gross Square Foot)</Label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatNumber(deal?.property?.gsfGrossSquareFoot || null, 'sq ft')} 
                            <span className="text-xs text-gray-500 ml-2">(from property)</span>
                          </p>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">ZFA (Zoning Floor Area)</Label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatNumber(deal?.property?.zfaZoningFloorArea || null, 'sq ft')} 
                            <span className="text-xs text-gray-500 ml-2">(from property)</span>
                          </p>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Total NSF</Label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatNumber(deal?.property?.totalNsfNetSquareFoot || null, 'sq ft')} 
                            <span className="text-xs text-gray-500 ml-2">(from property)</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
                             {/* Equity Return Metrics */}
               <div className="pt-4 border-t border-gray-200">
                 <h6 className="font-medium text-sm text-gray-700 mb-3">Equity Return Metrics</h6>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">

                  <div>
                    <span className="text-gray-600">Target Return:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.targetReturn || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'targetReturn', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.targetReturn ? `${criteria.targetReturn}%` : 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Min IRR:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.minimumInternalRateOfReturn || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'minimumInternalRateOfReturn', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.minimumInternalRateOfReturn ? `${criteria.minimumInternalRateOfReturn}%` : 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Min Yield on Cost:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.minimumYieldOnCost || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'minimumYieldOnCost', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.minimumYieldOnCost ? `${criteria.minimumYieldOnCost}%` : 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Min Equity Multiple:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.minimumEquityMultiple || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'minimumEquityMultiple', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.minimumEquityMultiple || 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Target Cash on Cash:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.targetCashOnCashMin || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'targetCashOnCashMin', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.targetCashOnCashMin ? `${criteria.targetCashOnCashMin}%` : 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Hold Period:</span>
                    {isEditing ? (
                      <div className="flex gap-2 mt-1">
                        <Input
                          type="number"
                          value={criteria.minHoldPeriodYears || ''}
                          onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'minHoldPeriodYears', parseInt(e.target.value) || 0)}
                          placeholder="Min"
                          className="flex-1"
                        />
                        <Input
                          type="number"
                          value={criteria.maxHoldPeriodYears || ''}
                          onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'maxHoldPeriodYears', parseInt(e.target.value) || 0)}
                          placeholder="Max"
                          className="flex-1"
                        />
                      </div>
                    ) : (
                      <p className="text-gray-900">{criteria.minHoldPeriodYears ? `${criteria.minHoldPeriodYears}-${criteria.maxHoldPeriodYears || 'N/A'} years` : 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Ownership:</span>
                    {isEditing ? (
                      <Input
                        value={criteria.ownershipRequirement || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'ownershipRequirement', e.target.value)}
                        className="mt-1"
                        placeholder="e.g., Majority, Minority"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.ownershipRequirement || 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Attachment Point:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.attachmentPoint || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'attachmentPoint', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.attachmentPoint ? `${criteria.attachmentPoint}%` : 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Max Leverage:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.maxLeverageTolerance || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'maxLeverageTolerance', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.maxLeverageTolerance ? `${criteria.maxLeverageTolerance}%` : 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Closing Timeline:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        value={criteria.typicalClosingTimelineDays || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'typicalClosingTimelineDays', parseInt(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.typicalClosingTimelineDays ? `${criteria.typicalClosingTimelineDays} days` : 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Proof of Funds:</span>
                    {isEditing ? (
                      <Select
                        value={criteria.proofOfFundsRequirement ? 'true' : 'false'}
                        onValueChange={(value) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'proofOfFundsRequirement', value === 'true')}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select requirement" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">Required</SelectItem>
                          <SelectItem value="false">Not Required</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-gray-900">{criteria.proofOfFundsRequirement ? 'Required' : 'Not Required'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Equity Program Overview:</span>
                    {isEditing ? (
                      <Textarea
                        value={criteria.equityProgramOverview || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'equityProgramOverview', e.target.value)}
                        className="mt-1"
                        placeholder="Program overview"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.equityProgramOverview || 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <span className="text-gray-600">Occupancy Requirements:</span>
                    {isEditing ? (
                      <Input
                        value={criteria.occupancyRequirements || ''}
                        onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'occupancyRequirements', e.target.value)}
                        className="mt-1"
                        placeholder="e.g., 90% occupancy"
                      />
                    ) : (
                      <p className="text-gray-900">{criteria.occupancyRequirements || 'N/A'}</p>
                    )}
                  </div>
                                     <div>
                     <span className="text-gray-600">Notes:</span>
                     {isEditing ? (
                       <Textarea
                         value={criteria.notes || ''}
                         onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'notes', e.target.value)}
                         className="mt-1"
                         placeholder="Additional notes"
                       />
                     ) : (
                       <p className="text-gray-900">{criteria.notes || 'N/A'}</p>
                     )}
                   </div>
                   
                   {/* Deal-Specific Equity Return Fields */}
                   <div>
                     <span className="text-gray-600">Yield on Cost:</span>
                     {isEditing ? (
                       <Input
                         type="number"
                         step="0.01"
                         value={criteria.yieldOnCost || ''}
                         onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'yieldOnCost', parseFloat(e.target.value) || 0)}
                         className="mt-1"
                         placeholder="0"
                       />
                     ) : (
                       <p className="text-gray-900">{criteria.yieldOnCost ? `${criteria.yieldOnCost}%` : 'N/A'}</p>
                     )}
                   </div>
                   <div>
                     <span className="text-gray-600">Target IRR on Equity:</span>
                     {isEditing ? (
                       <Input
                         type="number"
                         step="0.01"
                         value={criteria.targetReturnIrrOnEquity || ''}
                         onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'targetReturnIrrOnEquity', parseFloat(e.target.value) || 0)}
                         className="mt-1"
                         placeholder="0"
                       />
                     ) : (
                       <p className="text-gray-900">{criteria.targetReturnIrrOnEquity ? `${criteria.targetReturnIrrOnEquity}%` : 'N/A'}</p>
                     )}
                   </div>
                   <div>
                     <span className="text-gray-600">Equity Multiple:</span>
                     {isEditing ? (
                       <Input
                         type="number"
                         step="0.01"
                         value={criteria.equityMultiple || ''}
                         onChange={(e) => onEquityCriteriaChange?.(criteria.investmentCriteriaEquityId, 'equityMultiple', parseFloat(e.target.value) || 0)}
                         className="mt-1"
                         placeholder="0"
                       />
                     ) : (
                       <p className="text-gray-900">{criteria.equityMultiple || 'N/A'}</p>
                     )}
                   </div>
                </div>
              </div>
              
              {/* Delete Button */}
              {onEquityCriteriaDelete && (
                <div className="flex justify-end mt-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setCriteriaToDelete(criteria);
                      setDeleteConfirmOpen(true);
                    }}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 className="h-4 w-4 mr-1" /> Delete
                  </Button>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Equity Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Capital Position Breakdown */}
          {equityCriteriaByPosition.size > 0 && (
            <div>
              <h4 className="font-medium text-sm text-gray-700 mb-3">Capital Position Breakdown</h4>
              {Array.from(equityCriteriaByPosition.entries()).map(([capitalPosition, criteriaList]) => (
                renderCapitalPositionBreakdown(capitalPosition, criteriaList)
              ))}
            </div>
          )}

          {/* Legacy NSF Fields Display - Removed since NSF is now integrated into Capital Position Breakdown */}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your investment criteria record.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setCriteriaToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => {
              if (criteriaToDelete) {
                onEquityCriteriaDelete?.(criteriaToDelete.investmentCriteriaEquityId);
                setDeleteConfirmOpen(false);
                setCriteriaToDelete(null);
              }
            }}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default EquityTab;
