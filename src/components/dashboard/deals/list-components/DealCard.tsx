"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import QualityDetailsModal from "../QualityDetailsModal";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DollarSign,
  MapPin,
  Building2,
  User,
  Calendar,
  ArrowRight,
  TrendingUp,
  Percent,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Database,
  Edit,
  Globe,
  EyeOff,
} from "lucide-react";
import { DealV2 } from "../shared/types-v2";
import { useRouter } from "next/navigation";
import { formatCurrency } from "@/lib/utils";

interface DealCardProps {
  deal: DealV2;
  onDelete?: (dealId: string) => void;
  onEdit?: (deal: DealV2) => void;
  onPublishChange?: (dealId: string, published: boolean) => void;
}

const DealCard: React.FC<DealCardProps> = ({ deal, onDelete, onEdit, onPublishChange }) => {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isQualityModalOpen, setIsQualityModalOpen] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click navigation

    if (!onDelete) {
      console.warn("No onDelete handler provided to DealCard");
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/v2/deals/${deal.dealId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Delete response:", result);
        onDelete(deal.dealId.toString());
        setIsDeleteDialogOpen(false);
      } else {
        const error = await response.json();
        console.error("Failed to delete deal:", error);
        alert(`Failed to delete deal: ${error.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Error deleting deal:", error);
      alert("An error occurred while deleting the deal");
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePublishToggle = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click navigation

    setIsPublishing(true);
    try {
      const action = deal.published ? 'unpublish' : 'publish';
      const response = await fetch('/api/v2/deals/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dealId: deal.dealId,
          action: action
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Publish response:', result);
        
        // Show success message
        if (result.message) {
          alert(result.message);
        }
        
        // Update the deal state locally instead of reloading the page
        if (onPublishChange) {
          onPublishChange(deal.dealId.toString(), result.data?.published || !deal.published);
        }
      } else {
        const error = await response.json();
        console.error('Failed to update publish status:', error);
        alert(`Failed to ${action} deal: ${error.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error('Error updating publish status:', error);
      alert(`An error occurred while ${deal.published ? 'unpublishing' : 'publishing'} the deal`);
    } finally {
      setIsPublishing(false);
    }
  };

  // Helper function to get value from either core fields or extra_fields
  const getValue = (coreField: any, extraFieldPath: string): any => {
    if (coreField !== null && coreField !== undefined && coreField !== "") {
      return coreField;
    }

    // Try to get from extraFields
    if (deal.extraFields && typeof deal.extraFields === "object") {
      const paths = extraFieldPath.split(".");
      let value = deal.extraFields;

      for (const path of paths) {
        if (value && typeof value === "object" && path in value) {
          value = (value as any)[path];
        } else {
          return null;
        }
      }
      return value;
    }

    return null;
  };

  // Get deal name from core field or extra_fields
  const getDealName = (): string => {
    return (
      getValue(deal.dealName, "dealName") ||
      getValue(null, "extraFields.dealName") ||
      "Unnamed Deal"
    );
  };

  // Get sponsor name from core field or extra_fields - DealV2 doesn't have sponsorName, use property or other fields
  const getSponsorName = (): string => {
    return (
      getValue(deal.property?.owner?.ownerName, "sponsorName") ||
      getValue(null, "extraFields.sponsorName") ||
      ""
    );
  };

  // Get location from deal fields or investment criteria
  const getLocation = (): string => {
    // Try to get zip code and neighborhood from property or extra_fields
    const zipCode =
      getValue(deal.property?.zipcode, "zipCode") ||
      getValue(null, "extraFields.zipCode");
    const neighborhood =
      getValue(deal.property?.neighborhood, "neighborhood") ||
      getValue(null, "extraFields.neighborhood");

    // Try from property location
    const city = deal.property?.city;
    const state = deal.property?.state;
    
    if (city && state) {
      return `${city}, ${state}`;
    }
    if (city) return city;
    if (state) return state;

    // Fallback to neighborhood and zip
    if (neighborhood && zipCode) {
      return `${neighborhood}, ${zipCode}`;
    }
    if (neighborhood) return neighborhood;
    if (zipCode) return zipCode;

    return "";
  };

  // Get property description
  const getPropertyDescription = (): string => {
    return (
      getValue(deal.property?.propertyDescription, "propertyDescription") ||
      getValue(null, "extraFields.propertyDescription") ||
      ""
    );
  };

  // Get property types from DealV2 property fields
  const getPropertyTypes = (): string[] => {
    const propertyType = deal.property?.propertyType;
    const subPropertyType = deal.property?.subpropertyType;
    
    const types: string[] = [];
    if (propertyType) types.push(propertyType);
    if (subPropertyType) types.push(subPropertyType);
    
    return types;
  };

  // Get capital position from DealV2 fields
  const getCapitalPosition = (): string[] => {
    return deal.askCapitalPosition || [];
  };

  // Get deal size from DealV2 fields
  const getDealSize = (): { min?: number; max?: number } => {
    const convertToNumber = (
      value: string | number | null | undefined
    ): number | undefined => {
      if (value === null || value === undefined) return undefined;
      const num = typeof value === "string" ? parseFloat(value) : value;
      return isNaN(num) ? undefined : num;
    };

    // Use askAmount as deal size
    const amounts = deal.askAmount || [];
    if (amounts.length === 0) return {};
    
    const min = Math.min(...amounts);
    const max = Math.max(...amounts);
    
    return {
      min: min === max ? undefined : min,
      max: max
    };
  };

  // Get financial metrics
  const getFinancialMetrics = () => {
    return {
      targetReturn: null, // Not available in DealV2
      historicalIrr: null, // Not available in DealV2
      loanToValue: deal.loanToValueLtv,
      loanToCost: deal.loanToCostLtc,
      projectedGpIrr: (deal as any).gpInternalRateOfReturnIrr,
      projectedLpIrr: (deal as any).lpInternalRateOfReturnIrr,
      projectedTotalIrr: deal.totalInternalRateOfReturnIrr,
    };
  };

  // Helper function to get data quality score
  const getDataQualityScore = (): number => {
    // Use data_quality_metrics from DealV2
    if (deal.data_quality_metrics?.qualityScore !== undefined && deal.data_quality_metrics.qualityScore !== null) {
      return deal.data_quality_metrics.qualityScore;
    }
    return 0;
  };

  // Helper function to get data quality color
  const getDataQualityColor = (score: number): string => {
    if (score >= 90) return "bg-emerald-100 text-emerald-800 border-emerald-200";
    if (score >= 70) return "bg-blue-100 text-blue-800 border-blue-200";
    if (score >= 50) return "bg-amber-100 text-amber-800 border-amber-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  // Helper function to get data quality icon
  const getDataQualityIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-3 w-3" />;
    if (score >= 70) return <Database className="h-3 w-3" />;
    if (score >= 50) return <AlertTriangle className="h-3 w-3" />;
    return <AlertTriangle className="h-3 w-3" />;
  };

  const qualityScore = getDataQualityScore();

  // Debug logging to see what data we're recei

  // Use the unified formatCurrency function from utils
  const formatCurrencyValue = (amount: number | string | null): string => {
    if (amount === null || amount === undefined) return formatCurrency(0);
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return formatCurrency(isNaN(numAmount) ? 0 : numAmount);
  };

  const formatPercentage = (value: number | string | null): string => {
    if (value === null || value === undefined) return "N/A";

    // Convert string to number if needed
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(numValue)) return "N/A";

    // If value is already a percentage (like 0.75 for 75%), multiply by 100
    // If value is greater than 1, assume it's already in percentage form
    const percentage = numValue > 1 ? numValue : numValue * 100;
    return `${percentage.toFixed(1)}%`;
  };

  const getStatusColor = (status: string | null): string => {
    switch (status?.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "live":
        return "bg-green-100 text-green-800 border-green-200";
      case "seeking financing":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "financing":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "closed":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "inactive":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getCapitalTypeColor = (type: string | null): string => {
    switch (type?.toLowerCase()) {
      case "equity":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "debt":
      case "senior debt":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "hybrid":
        return "bg-indigo-100 text-indigo-800 border-indigo-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const dealSize = getDealSize();
  const location = getLocation();
  const propertyTypes = getPropertyTypes();
  const capitalPosition = getCapitalPosition();
  const financialMetrics = getFinancialMetrics();
  const propertyDescription = getPropertyDescription();

  return (
    <>
      <Card
        className="hover:shadow-lg transition-shadow cursor-pointer group"
        onClick={() => {
          // Preserve the current URL with all parameters
          const currentUrl = window.location.href;
          router.push(`/dashboard/deals/v2/${deal.dealId}`);
        }}
      >
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                {getDealName()}
              </h3>
              {getSponsorName() && (
                <p className="text-sm text-gray-600 truncate">
                  {getSponsorName()}
                </p>
              )}
            </div>
            <div className="flex items-center gap-1">
              {/* Data Quality Badge */}
              {/* V2 Badge */}
              <Badge 
                variant="outline" 
                className="text-xs font-medium bg-purple-50 text-purple-700 border-purple-200"
              >
                V2
              </Badge>
              
              <Badge 
                variant="outline" 
                className={`text-xs font-medium cursor-pointer hover:scale-105 transition-transform ${getDataQualityColor(qualityScore)}`}
                title={`Click to view detailed data quality breakdown`}
                onClick={(e) => {
                  e.stopPropagation();
                  setIsQualityModalOpen(true);
                }}
              >
                {getDataQualityIcon(qualityScore)}
                <span className="ml-1">{qualityScore}%</span>
              </Badge>
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-gray-400 hover:text-blue-600 hover:bg-blue-50 opacity-0 group-hover:opacity-100 transition-all"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(deal);
                  }}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-gray-400 hover:text-red-600 hover:bg-red-50 opacity-0 group-hover:opacity-100 transition-all"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsDeleteDialogOpen(true);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePublishToggle}
                disabled={isPublishing}
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-all"
                title={deal.published ? "Unpublish deal" : "Publish deal"}
              >
                {isPublishing ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
                ) : deal.published ? (
                  <EyeOff className="h-4 w-4 text-orange-600" />
                ) : (
                  <Globe className="h-4 w-4 text-green-600" />
                )}
              </Button>
              <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-3">
          {/* Location */}
          {location && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <MapPin className="h-4 w-4" />
              <span className="truncate">{location}</span>
            </div>
          )}

          {/* Property Description */}
          {propertyDescription && (
            <div className="text-sm text-gray-600">
              <p className="line-clamp-2">{propertyDescription}</p>
            </div>
          )}

          {/* Deal Size */}
          {(dealSize.min || dealSize.max) && (
            <div className="flex items-center gap-2 text-sm">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="font-medium text-gray-900">
                {dealSize.min && dealSize.max
                  ? `${formatCurrencyValue(dealSize.min)} - ${formatCurrencyValue(
                      dealSize.max
                    )}`
                  : formatCurrencyValue(dealSize.min || dealSize.max || null)}
              </span>
            </div>
          )}

          {/* Property Type & Capital Position */}
          <div className="flex flex-wrap gap-2">
            {propertyTypes.length > 0 && (
              <Badge
                variant="outline"
                className="bg-blue-50 text-blue-700 border-blue-200"
              >
                <Building2 className="h-3 w-3 mr-1" />
                {propertyTypes[0]} {/* Show first property type */}
                {propertyTypes.length > 1 && (
                  <span className="ml-1 text-xs">
                    +{propertyTypes.length - 1}
                  </span>
                )}
              </Badge>
            )}
            {capitalPosition.length > 0 && (
              <Badge
                variant="outline"
                className={getCapitalTypeColor(capitalPosition[0])}
              >
                {capitalPosition[0]}
                {capitalPosition.length > 1 && (
                  <span className="ml-1 text-xs">
                    +{capitalPosition.length - 1}
                  </span>
                )}
              </Badge>
            )}
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-3 pt-2 border-t border-gray-100">
            {/* Target Return */}
            {financialMetrics.targetReturn && (
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
                  <TrendingUp className="h-3 w-3" />
                  <span>Target Return</span>
                </div>
                <div className="font-semibold text-gray-900">
                  {formatPercentage(financialMetrics.targetReturn)}
                </div>
              </div>
            )}

            {/* Historical IRR */}
            {financialMetrics.historicalIrr && (
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
                  <Percent className="h-3 w-3" />
                  <span>Historical IRR</span>
                </div>
                <div className="font-semibold text-gray-900">
                  {formatPercentage(financialMetrics.historicalIrr)}
                </div>
              </div>
            )}

            {/* LTV */}
            {financialMetrics.loanToValue && (
              <div className="text-center">
                <div className="text-sm text-gray-600">LTV</div>
                <div className="font-semibold text-gray-900">
                  {formatPercentage(financialMetrics.loanToValue)}
                </div>
              </div>
            )}

            {/* LTC */}
            {financialMetrics.loanToCost && (
              <div className="text-center">
                <div className="text-sm text-gray-600">LTC</div>
                <div className="font-semibold text-gray-900">
                  {formatPercentage(financialMetrics.loanToCost)}
                </div>
              </div>
            )}

            {/* Projected GP IRR */}
            {financialMetrics.projectedGpIrr && (
              <div className="text-center">
                <div className="text-sm text-gray-600">GP IRR</div>
                <div className="font-semibold text-gray-900">
                  {formatPercentage(financialMetrics.projectedGpIrr)}
                </div>
              </div>
            )}

            {/* Projected LP IRR */}
            {financialMetrics.projectedLpIrr && (
              <div className="text-center">
                <div className="text-sm text-gray-600">LP IRR</div>
                <div className="font-semibold text-gray-900">
                  {formatPercentage(financialMetrics.projectedLpIrr)}
                </div>
              </div>
            )}

            {/* Projected Total IRR */}
            {financialMetrics.projectedTotalIrr && (
              <div className="text-center">
                <div className="text-sm text-gray-600">Total IRR</div>
                <div className="font-semibold text-gray-900">
                  {formatPercentage(financialMetrics.projectedTotalIrr)}
                </div>
              </div>
            )}
          </div>

          {/* Status & Contact */}
          <div className="flex justify-between items-center pt-2 border-t border-gray-100">
            <div className="flex items-center gap-2">
              {deal.published && (
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800 border-green-200"
                >
                  Published
                </Badge>
              )}
              {deal.dealStatus && (
                <Badge
                  variant="outline"
                  className={getStatusColor(deal.dealStatus)}
                >
                  {deal.dealStatus}
                </Badge>
              )}
              {getSponsorName() && (
                <div className="flex items-center gap-1 text-xs text-gray-600">
                  <User className="h-3 w-3" />
                  <span className="truncate">
                    {getSponsorName()}
                  </span>
                </div>
              )}
            </div>

            <div className="text-xs text-gray-500">
              {deal.createdAt
                ? new Date(deal.createdAt).toLocaleDateString()
                : "N/A"}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent onClick={(e) => e.stopPropagation()}>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Deal</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{getDealName()}"? This action
              will permanently remove the deal and all its associated investment
              criteria. This cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isDeleting ? "Deleting..." : "Delete Deal"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Quality Details Modal */}
      <QualityDetailsModal
        isOpen={isQualityModalOpen}
        onClose={() => setIsQualityModalOpen(false)}
        deal={deal}
      />
    </>
  );
};

export default DealCard;
