'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>ertTitle, AlertDescription } from "@/components/ui/alert";
import { 
  FileText, 
  ExternalLink, 
  Calendar, 
  DollarSign, 
  Globe, 
  Target, 
  AlertCircle,
  MapPin,
  Shield,
  Sparkles,
  Crown,
  Star
} from 'lucide-react';
import { Tooltip } from "@/components/ui/tooltip";
import { useRouter } from 'next/navigation';

interface ArticleItem {
  article_id: string | number;
  headline?: string;
  summary?: string;
  publication_date?: string;
  publication_name?: string;
  source_name?: string;
  source_url?: string;
  property_type?: string | string[];
  location_city?: string | string[];
  location_state?: string | string[];
  deal_sizes?: string[];
  score?: number;
  breakdown?: Array<{
    field: string;
    score: number;
    reason?: string;
  }>;
  reasons?: string[];
  
  // Hierarchical search fields
  isPremium?: boolean;
  searchTier?: 1 | 2 | 3 | 4;
  
  transactions?: Array<{
    deal_type?: string;
    deal_size?: string;
    cap_rate?: string;
    price_per_sf?: number;
    loan_type?: string;
    equity_type?: string;
    financing_type?: any;
    capital_stack_notes?: any;
    capital_position?: string;
    property_type?: string; // Enhanced with property_type
    property_types?: any;
  }>;
  properties?: Array<{
    property_name?: string;
    address?: string;
    city?: string;
    state?: string;
    zipcode?: string;
    region?: string;
    country?: string;
    square_footage?: number;
    unit_count?: number;
    construction_type?: string;
    project_timeline?: string;
    job_creation?: number;
    subsidy_info?: string;
  }>;
}

interface MatchingCriteria {
  criteria_source?: string;
  criteria_description?: string;
  search_tier?: 1 | 2 | 3 | 4;
  tier_description?: string;
  is_premium?: boolean;
  premium_count?: number;
  used_criteria?: {
    property_types?: string[];
    strategies?: string[];
    states?: string[];
    cities?: string[];
  };
  capital_positions?: {
    positions: string[];
    count: number;
    source: string;
  };
  deal_size_range?: {
    min?: string;
    max?: string;
  };
  processing_info?: {
    hierarchical_search?: {
      applied: boolean;
      search_tier: 1 | 2 | 3 | 4;
      tier_description: string;
      is_premium: boolean;
    };
    capital_position_filtering?: {
      applied: boolean;
      positions_count: number;
      source: string;
    };
  };
}

interface ArticleTabProps {
  entityType: 'contact' | 'company';
  entityId: string | number;
  entityName?: string;
  linkedArticlesApiEndpoint?: string;
  matchedArticlesApiEndpoint?: string;
}

const ArticleTab: React.FC<ArticleTabProps> = ({
  entityType,
  entityId,
  entityName,
  linkedArticlesApiEndpoint,
  matchedArticlesApiEndpoint
}) => {
  const router = useRouter();
  
  const [activeSection, setActiveSection] = useState<'linked' | 'matched'>('matched');
  
  // Linked Articles State
  const [linkedArticles, setLinkedArticles] = useState<ArticleItem[]>([]);
  const [linkedArticlesLoading, setLinkedArticlesLoading] = useState(false);
  const [linkedArticlesError, setLinkedArticlesError] = useState<string | null>(null);
  
  // Matched Articles State
  const [matchedArticles, setMatchedArticles] = useState<ArticleItem[]>([]);
  const [matchedArticlesLoading, setMatchedArticlesLoading] = useState(false);
  const [matchedArticlesError, setMatchedArticlesError] = useState<string | null>(null);
  const [matchingCriteria, setMatchingCriteria] = useState<MatchingCriteria | null>(null);
  
  // Expanded state
  const [expandedArticleId, setExpandedArticleId] = useState<string | number | null>(null);

  // Fetch Linked Articles
  const fetchLinkedArticles = async () => {
    if (!linkedArticlesApiEndpoint) return;
    
    setLinkedArticlesLoading(true);
    setLinkedArticlesError(null);
    
    try {
      const response = await fetch(linkedArticlesApiEndpoint);
      const data = await response.json();
      
      if (response.ok) {
        setLinkedArticles(data.articles || data.matches || []);
      } else {
        setLinkedArticlesError(data.error || 'Failed to load linked articles');
      }
    } catch (error) {
      setLinkedArticlesError('Error loading linked articles');
    } finally {
      setLinkedArticlesLoading(false);
    }
  };

  // Fetch Matched Articles
  const fetchMatchedArticles = async () => {
    if (!matchedArticlesApiEndpoint) return;
    
    setMatchedArticlesLoading(true);
    setMatchedArticlesError(null);
    
    try {
      const response = await fetch(matchedArticlesApiEndpoint);
      const data = await response.json();
      
      if (response.ok) {
        setMatchedArticles(data.matches || data.articles || []);
        if (data.matching_criteria) {
          setMatchingCriteria({
            ...data.matching_criteria,
            criteria_source: data.criteria_source,
            criteria_description: data.criteria_description,
            search_tier: data.search_tier,
            tier_description: data.tier_description,
            is_premium: data.is_premium,
            premium_count: data.premium_count,
            processing_info: {
              ...data.matching_criteria.processing_info,
              hierarchical_search: data.processing_info?.hierarchical_search
            }
          });
        }
      } else {
        setMatchedArticlesError(data.error || 'Failed to load matched articles');
      }
    } catch (error) {
      setMatchedArticlesError('Error loading matched articles');
    } finally {
      setMatchedArticlesLoading(false);
    }
  };

  // Load data when section changes
  useEffect(() => {
    if (activeSection === 'linked') {
      fetchLinkedArticles();
    } else if (activeSection === 'matched') {
      fetchMatchedArticles();
    }
  }, [activeSection, linkedArticlesApiEndpoint, matchedArticlesApiEndpoint]);

  // Render article card
  const renderArticleCard = (article: ArticleItem, isMatched: boolean = false) => {
    const isExpanded = expandedArticleId === article.article_id;
    
    // Don't show scores for linked articles - only show scores greater than 0
    const shouldShowScore = isMatched && article.score && article.score > 0 && activeSection === 'matched';
    
    const scoreColor = shouldShowScore && article.score ? (
      article.score >= 80 ? 'bg-green-600' : 
      article.score >= 60 ? 'bg-blue-600' :
      article.score >= 40 ? 'bg-yellow-600' : 'bg-orange-600'
    ) : 'bg-gray-600';

    return (
      <Card key={article.article_id} className={`border shadow-sm hover:shadow-md transition-shadow ${
        article.isPremium ? 'border-amber-300 bg-gradient-to-r from-amber-50 to-yellow-50' : ''
      }`}>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div className="flex-1 min-w-0 mr-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-lg font-medium line-clamp-2 flex-1">
                      <span className="text-blue-700 hover:underline text-left hover:text-blue-800 transition-colors">
                        {article.headline || 'Untitled Article'}
                      </span>
                    </h3>
                    {/* Search Tier Badge - positioned after headline */}
                    {isMatched && article.searchTier && (
                        <Badge 
                          variant="outline" 
                          className={`text-xs cursor-help ml-2 ${
                            article.searchTier === 1 ? 'bg-amber-50 text-amber-700 border-amber-200' :
                            article.searchTier === 2 ? 'bg-blue-50 text-blue-700 border-blue-200' :
                            article.searchTier === 3 ? 'bg-green-50 text-green-700 border-green-200' :
                            article.searchTier === 4 ? 'bg-purple-50 text-purple-700 border-purple-200' :
                            'bg-gray-50 text-gray-700 border-gray-200'
                          }`}
                        >
                          Tier {article.searchTier}
                        </Badge>
                    )}
                    {/* Premium Article Badge */}
                    {article.isPremium && (
                      <Badge className="bg-gradient-to-r from-amber-500 to-yellow-500 text-white border-0 shadow-sm flex items-center gap-1">
                        <Crown className="h-3 w-3" />
                        Premium
                      </Badge>
                    )}
                  </div>
                </div>
                {article.source_url && (
                  <a
                    href={article.source_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    title="View original article"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                )}
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                <div className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  <span className="font-semibold">
                    {article.deal_sizes && Array.isArray(article.deal_sizes) && article.deal_sizes.length > 0 
                      ? (() => {
                          const firstDealSize = article.deal_sizes[0];
                          if (typeof firstDealSize === 'string') {
                            const match = firstDealSize.match(/(\d+(?:\.\d+)?)([MBK])?/i);
                            if (match) {
                              const value = parseFloat(match[1]);
                              const unit = match[2]?.toUpperCase() || 'M';
                              if (!isNaN(value)) {
                                if (unit === 'B') return `$${value}B`;
                                if (unit === 'K') return `$${(value / 1000).toFixed(1)}M`;
                                return `$${value}M`;
                              }
                            }
                          }
                          return 'N/A';
                        })()
                      : 'N/A'}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {article.publication_date 
                      ? new Date(article.publication_date).toLocaleDateString() 
                      : 'N/A'}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Globe className="h-4 w-4" />
                  <span>{article.publication_name || article.source_name || 'Unknown Source'}</span>
                </div>
              </div>

              {/* Property and Location Details */}
              <div className="flex flex-wrap gap-1 mb-2">
                {article.property_type && (
                  <Badge variant="outline" className="text-xs">
                    {Array.isArray(article.property_type) 
                      ? article.property_type.join(', ') 
                      : article.property_type}
                  </Badge>
                )}
                {article.location_city && (
                  <Badge className="text-xs">
                    📍 {Array.isArray(article.location_city) 
                      ? article.location_city.join(', ') 
                      : article.location_city}
                  </Badge>
                )}
                {article.location_state && (
                  <Badge className="text-xs">
                    {Array.isArray(article.location_state) 
                      ? article.location_state.join(', ') 
                      : article.location_state}
                  </Badge>
                )}
              </div>

              {/* Summary */}
              {article.summary && (
                <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                  {article.summary}
                </p>
              )}
            </div>

            {/* Score Badge for matched articles only */}
            {shouldShowScore && (
              <div className="flex-shrink-0 flex flex-col items-end">
                <div className={`${scoreColor} text-white text-xs font-semibold px-2 py-1 rounded shadow`}>
                  {article.score}%
                </div>
                <div className="text-xs text-gray-500 mt-1">Match Score</div>
              </div>
            )}
          </div>

          {/* Top Match Indicators for matched articles only */}
          {shouldShowScore && article.breakdown && article.breakdown.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {article.breakdown.slice(0, 3).map((b, idx) => (
                b.score > 0 && (
                  <Badge key={idx} variant="outline" className="text-xs">
                    {b.field.replace('_', ' ')}: {b.score}%
                  </Badge>
                )
              ))}
            </div>
          )}
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="flex gap-2 mb-3">
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={() => setExpandedArticleId(isExpanded ? null : article.article_id)}
            >
              {isExpanded ? "Hide Details" : "Show Details"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/dashboard/articles/${article.article_id}`, '_blank')}
              className="flex items-center gap-1"
            >
              <ExternalLink className="h-4 w-4" />
              View Full
            </Button>
          </div>

          {isExpanded && (
            <div className="space-y-4 border-t pt-4">
              {/* For matched articles - show match reasons */}
              {shouldShowScore && article.reasons && article.reasons.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded p-2">
                  <div className="font-semibold text-sm text-blue-800 mb-1">Key Matches:</div>
                  <ul className="text-xs text-blue-700 space-y-0.5">
                    {article.reasons.map((reason: string, idx: number) => (
                      <li key={idx} className="flex items-center gap-1">
                        <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                        {reason}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* For linked articles - show transaction and property details */}
              {activeSection === 'linked' && (
                <div className="space-y-4">
                  {/* Transaction Details */}
                  {article.transactions && article.transactions.length > 0 && (
                    <div className="bg-green-50 border border-green-200 rounded p-3">
                      <div className="font-semibold text-sm text-green-800 mb-2 flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Transaction Details ({article.transactions.length})
                      </div>
                      <div className="space-y-2">
                        {article.transactions.map((transaction, idx) => (
                          <div key={idx} className="bg-white rounded p-2 text-xs">
                            <div className="grid grid-cols-2 gap-2">
                              {transaction.deal_type && (
                                <div><span className="font-medium text-green-700">Type:</span> {transaction.deal_type}</div>
                              )}
                              {transaction.deal_size && (
                                <div><span className="font-medium text-green-700">Size:</span> {transaction.deal_size}</div>
                              )}
                              {transaction.cap_rate && (
                                <div><span className="font-medium text-green-700">Cap Rate:</span> {transaction.cap_rate}</div>
                              )}
                              {transaction.price_per_sf && (
                                <div><span className="font-medium text-green-700">$/SF:</span> ${transaction.price_per_sf}</div>
                              )}
                              {transaction.loan_type && (
                                <div><span className="font-medium text-green-700">Loan:</span> {transaction.loan_type}</div>
                              )}
                              {transaction.equity_type && (
                                <div><span className="font-medium text-green-700">Equity:</span> {transaction.equity_type}</div>
                              )}
                              {transaction.capital_position && (
                                <div><span className="font-medium text-green-700">Capital Position:</span> {transaction.capital_position}</div>
                              )}
                              {transaction.property_type && (
                                <div><span className="font-medium text-green-700">Property Type:</span> {transaction.property_type}</div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Property Details */}
                  {article.properties && article.properties.length > 0 && (
                    <div className="bg-purple-50 border border-purple-200 rounded p-3">
                      <div className="font-semibold text-sm text-purple-800 mb-2 flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        Property Details ({article.properties.length})
                      </div>
                      <div className="space-y-2">
                        {article.properties.map((property, idx) => (
                          <div key={idx} className="bg-white rounded p-2 text-xs">
                            {property.property_name && (
                              <div className="font-medium text-purple-700 mb-1">{property.property_name}</div>
                            )}
                            <div className="grid grid-cols-2 gap-2">
                              {property.address && (
                                <div><span className="font-medium text-purple-700">Address:</span> {property.address}</div>
                              )}
                              {(property.city || property.state) && (
                                <div><span className="font-medium text-purple-700">Location:</span> {[property.city, property.state].filter(Boolean).join(', ')}</div>
                              )}
                              {property.square_footage && (
                                <div><span className="font-medium text-purple-700">Size:</span> {property.square_footage.toLocaleString()} SF</div>
                              )}
                              {property.unit_count && (
                                <div><span className="font-medium text-purple-700">Units:</span> {property.unit_count}</div>
                              )}
                              {property.construction_type && (
                                <div><span className="font-medium text-purple-700">Construction:</span> {property.construction_type}</div>
                              )}
                              {property.project_timeline && (
                                <div><span className="font-medium text-purple-700">Timeline:</span> {property.project_timeline}</div>
                              )}
                              {property.job_creation && (
                                <div><span className="font-medium text-purple-700">Jobs:</span> {property.job_creation}</div>
                              )}
                            </div>
                            {property.subsidy_info && (
                              <div className="mt-1 text-purple-600">
                                <span className="font-medium">Subsidies:</span> {property.subsidy_info}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Show message if no transaction or property data */}
                  {(!article.transactions || article.transactions.length === 0) && 
                   (!article.properties || article.properties.length === 0) && (
                    <div className="bg-gray-50 border border-gray-200 rounded p-3 text-center text-sm text-gray-600">
                      No detailed transaction or property information available for this article.
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Render matching criteria for matched articles only
  const renderMatchingCriteria = () => {
    if (!matchingCriteria || activeSection !== 'matched') return null;
    
    // Don't show matching criteria for linked articles
    if (matchingCriteria.criteria_source === 'linked') return null;

    return (
      <div className="mb-6 space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg border">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <Target className="h-5 w-5" />
            Matching Criteria Information
          </h3>

          {/* Active Matching Criteria */}
          <div className="border-t pt-4">
            <h4 className="text-md font-semibold text-gray-800 mb-3">Active Matching Criteria:</h4>
            <div className="bg-blue-50 p-3 rounded border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-blue-900">Matching Parameters</span>
              </div>
              <div className="text-xs text-blue-700 space-y-1">
                {matchingCriteria.used_criteria?.property_types && matchingCriteria.used_criteria.property_types.length > 0 && (
                  <div>Property Types: {matchingCriteria.used_criteria.property_types.join(', ')}</div>
                )}
                {matchingCriteria.used_criteria?.strategies && matchingCriteria.used_criteria.strategies.length > 0 && (
                  <div>Strategies: {matchingCriteria.used_criteria.strategies.join(', ')}</div>
                )}
                {matchingCriteria.used_criteria?.states && matchingCriteria.used_criteria.states.length > 0 && (
                  <div>States: {matchingCriteria.used_criteria.states.join(', ')}</div>
                )}
                {matchingCriteria.used_criteria?.cities && matchingCriteria.used_criteria.cities.length > 0 && (
                  <div>Cities: {matchingCriteria.used_criteria.cities.join(', ')}</div>
                )}
                {matchingCriteria.deal_size_range?.min && (
                  <div>Deal Size: {matchingCriteria.deal_size_range.min} - {matchingCriteria.deal_size_range.max || '∞'}</div>
                )}
                
                {/* Enhanced Capital Position Information */}
                {matchingCriteria.capital_positions?.positions && matchingCriteria.capital_positions.positions.length > 0 && (
                  <div className="mt-2 pt-2 border-t border-blue-200">
                    <div className="font-medium text-blue-800 mb-1">Capital Positions:</div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                          {matchingCriteria.capital_positions?.positions?.join(', ')}
                        </span>
                        <span className="text-xs text-blue-600">
                          ({matchingCriteria.capital_positions?.count} position{matchingCriteria.capital_positions?.count !== 1 ? 's' : ''})
                        </span>
                      </div>
                      <div className="text-xs text-blue-600 italic">
                        Source: {matchingCriteria.capital_positions?.source}
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Show message if no criteria */}
                {(!matchingCriteria.used_criteria?.property_types?.length && 
                  !matchingCriteria.used_criteria?.strategies?.length && 
                  !matchingCriteria.used_criteria?.states?.length && 
                  !matchingCriteria.used_criteria?.cities?.length && 
                  !matchingCriteria.deal_size_range?.min &&
                  !matchingCriteria.capital_positions?.positions?.length) && (
                  <div className="text-gray-500">No active criteria</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render summary stats
  const renderSummaryStats = (articles: ArticleItem[], isMatched: boolean) => {
    if (articles.length === 0) return null;

    // For linked articles, show simpler stats
    const shouldShowMatchStats = isMatched && activeSection === 'matched';

    return (
      <div className={`mb-6 grid grid-cols-1 ${shouldShowMatchStats ? 'sm:grid-cols-3' : activeSection === 'linked' ? 'sm:grid-cols-3' : 'sm:grid-cols-2'} gap-4`}>
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <div className="text-sm font-medium text-blue-900">Total Articles</div>
          <div className="text-2xl font-bold text-blue-700">{articles.length}</div>
        </div>
        
        <div className="bg-green-50 p-3 rounded-lg border border-green-200">
          <div className="text-sm font-medium text-green-900">
            {activeSection === 'linked' ? 'With Transactions' : 'With Deal Sizes'}
          </div>
          <div className="text-2xl font-bold text-green-700">
            {activeSection === 'linked' 
              ? articles.filter((article) => 
                  article.transactions && article.transactions.length > 0
                ).length
              : articles.filter((article) => 
                  article.deal_sizes && Array.isArray(article.deal_sizes) && article.deal_sizes.length > 0
                ).length
            }
          </div>
        </div>

        {/* For linked articles, show property count */}
        {activeSection === 'linked' && (
          <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
            <div className="text-sm font-medium text-purple-900">With Properties</div>
            <div className="text-2xl font-bold text-purple-700">
              {articles.filter((article) => 
                article.properties && article.properties.length > 0
              ).length}
            </div>
          </div>
        )}

        {shouldShowMatchStats && (
          <>
            <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
              <div className="text-sm font-medium text-purple-900">High Quality</div>
              <div className="text-2xl font-bold text-purple-700">
                {articles.filter((article) => (article.score || 0) >= 70).length}
              </div>
            </div>
            
            {/* Premium Articles Count */}
            {articles.some(article => article.isPremium) && (
              <div className="bg-amber-50 p-3 rounded-lg border border-amber-200">
                <div className="text-sm font-medium text-amber-900 flex items-center gap-1">
                  <Crown className="h-3 w-3" />
                  Premium Articles
                </div>
                <div className="text-2xl font-bold text-amber-700">
                  {articles.filter((article) => article.isPremium).length}
                </div>
              </div>
            )}
          </>
        )}
        
        {/* Always show avg deal size if we have deal data */}
        {articles.some(article => article.deal_sizes && Array.isArray(article.deal_sizes) && article.deal_sizes.length > 0) && (
          <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
            <div className="text-sm font-medium text-orange-900">Avg Deal Size</div>
            <div className="text-2xl font-bold text-orange-700">
              {(() => {
                const articlesWithDealSizes = articles.filter((article) => 
                  article.deal_sizes && Array.isArray(article.deal_sizes) && article.deal_sizes.length > 0
                );
                
                if (articlesWithDealSizes.length === 0) return 'N/A';
                
                const totalDealSize = articlesWithDealSizes.reduce((sum: number, article) => {
                  const firstDealSize = article.deal_sizes![0];
                  if (typeof firstDealSize === 'string') {
                    const match = firstDealSize.match(/(\d+(?:\.\d+)?)([MBK])?/i);
                    if (match) {
                      const value = parseFloat(match[1]);
                      const unit = match[2]?.toUpperCase() || 'M';
                      if (!isNaN(value)) {
                        if (unit === 'B') return sum + (value * 1000);
                        if (unit === 'K') return sum + (value / 1000);
                        return sum + value;
                      }
                    }
                  }
                  return sum;
                }, 0);
                
                const avgDealSize = totalDealSize / articlesWithDealSizes.length;
                
                if (avgDealSize >= 1000) {
                  return `$${(avgDealSize / 1000).toFixed(1)}B`;
                }
                return `$${Math.round(avgDealSize)}M`;
              })()}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          {/* Section Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <Button
              variant={activeSection === 'matched' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveSection('matched')}
              className="text-xs"
            >
              <Sparkles className="h-3 w-3 mr-1" />
              Matched Articles
            </Button>
            <Button
              variant={activeSection === 'linked' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveSection('linked')}
              className="text-xs"
            >
              <FileText className="h-3 w-3 mr-1" />
              Linked Articles
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Linked Articles Section */}
        {activeSection === 'linked' && (
          <>
            {linkedArticlesLoading ? (
              <div className="text-center py-8">
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-gray-600">Loading linked articles...</p>
                </div>
              </div>
            ) : linkedArticlesError ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-300 mx-auto mb-4" />
                <div className="text-red-600">{linkedArticlesError}</div>
              </div>
            ) : linkedArticles.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <div className="text-gray-500">No linked articles found for this {entityType}.</div>
                <div className="text-sm text-gray-400 mt-2">
                  Articles will appear here when they are directly associated with this {entityType}
                </div>
              </div>
            ) : (
              <>
                {renderSummaryStats(linkedArticles, false)}
                <div className="space-y-4">
                  {linkedArticles.map((article) => renderArticleCard(article, false))}
                </div>
              </>
            )}
          </>
        )}

        {/* Matched Articles Section */}
        {activeSection === 'matched' && (
          <>
            {matchedArticlesLoading ? (
              <div className="text-center py-8">
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-gray-600">Loading matched articles...</p>
                </div>
              </div>
            ) : matchedArticlesError ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-300 mx-auto mb-4" />
                <div className="text-red-600">{matchedArticlesError}</div>
              </div>
            ) : matchedArticles.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <div className="text-gray-500">No matching articles found for this {entityType}.</div>
                <div className="text-sm text-gray-400 mt-2">
                  Articles must match investment criteria to appear here
                </div>
              </div>
            ) : (
              <>
                {renderMatchingCriteria()}
                {renderSummaryStats(matchedArticles, true)}
                <div className="space-y-4">
                  {matchedArticles.map((article) => renderArticleCard(article, true))}
                </div>
              </>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ArticleTab;
