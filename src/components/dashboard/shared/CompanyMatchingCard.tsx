import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  ChevronDown, 
  ChevronRight, 
  Building, 
  Users, 
  TrendingUp, 
  CheckCircle, 
  XCircle,
  User,
  Building2,
  Target,
  MapPin,
  DollarSign,
  Calendar,
  Percent
} from 'lucide-react';

interface CompanyMatchingCardProps {
  company: any;
  onContactClick: (contactId: number) => void;
  onCompanyClick: (companyId: number) => void;
  formatScore: (score: number) => string;
}

const CompanyMatchingCard: React.FC<CompanyMatchingCardProps> = ({
  company,
  onContactClick,
  onCompanyClick,
  formatScore
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedContactId, setExpandedContactId] = useState<number | null>(null);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-600 text-white';
    if (score >= 60) return 'bg-blue-600 text-white';
    if (score >= 40) return 'bg-yellow-600 text-white';
    return 'bg-orange-600 text-white';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800 border-green-300';
    if (confidence >= 0.6) return 'bg-blue-100 text-blue-800 border-blue-300';
    if (confidence >= 0.4) return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    return 'bg-orange-100 text-orange-800 border-orange-300';
  };

  const renderCriteriaDetails = (criteria: any, type: 'company' | 'contact', contactData?: any) => {
    // Get the investment criteria data from the new structure
    const investmentData = criteria.investment_criteria || contactData || criteria;
    
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {criteria.capital_position}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {formatScore(criteria.individual_score)}
            </Badge>
          </div>
          {type === 'company' && (
            <div className="text-xs text-gray-500">
              {criteria.contact_count} contact{criteria.contact_count !== 1 ? 's' : ''}
            </div>
          )}
        </div>

        {/* NSF Amount (Deal Amount) */}
        {criteria.nsf_amount && (
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <h6 className="font-medium text-sm text-blue-700 mb-1">Deal Amount (NSF)</h6>
            <div className="flex items-center gap-2 text-sm">
              <DollarSign className="h-3 w-3 text-blue-600" />
              <span className="text-blue-600">Amount:</span>
              <span className="font-medium text-blue-800">
                ${(criteria.nsf_amount / 1000000).toFixed(1)}M
              </span>
            </div>
          </div>
        )}

        {/* Investment Criteria Central Data */}
        <div className="bg-gray-50 p-3 rounded-lg">
          <h6 className="font-medium text-sm text-gray-700 mb-2">Investment Criteria Details</h6>
          
          {/* Property Types */}
          {investmentData.property_types && investmentData.property_types.length > 0 && (
            <div className="flex items-center gap-2 text-sm mb-1">
              <Building className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600">Property Types:</span>
              <div className="flex gap-1">
                {investmentData.property_types.map((type: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {type}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Location */}
          {investmentData.region && (
            <div className="flex items-center gap-2 text-sm mb-1">
              <MapPin className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600">Region:</span>
              <span className="font-medium">{Array.isArray(investmentData.region) ? investmentData.region.join(', ') : investmentData.region}</span>
            </div>
          )}

          {/* State */}
          {investmentData.state && (
            <div className="flex items-center gap-2 text-sm mb-1">
              <MapPin className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600">State:</span>
              <span className="font-medium">{investmentData.state}</span>
            </div>
          )}

          {/* City */}
          {investmentData.city && (
            <div className="flex items-center gap-2 text-sm mb-1">
              <MapPin className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600">City:</span>
              <span className="font-medium">{investmentData.city}</span>
            </div>
          )}

          {/* Country */}
          {investmentData.country && (
            <div className="flex items-center gap-2 text-sm mb-1">
              <MapPin className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600">Country:</span>
              <span className="font-medium">{Array.isArray(investmentData.country) ? investmentData.country.join(', ') : investmentData.country}</span>
            </div>
          )}

          {/* Strategies */}
          {investmentData.strategies && investmentData.strategies.length > 0 && (
            <div className="flex items-center gap-2 text-sm mb-1">
              <Target className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600">Strategies:</span>
              <div className="flex gap-1">
                {investmentData.strategies.map((strategy: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {strategy}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Deal Size Range (from criteria, not NSF) */}
          {investmentData.minimum_deal_size && investmentData.maximum_deal_size && (
            <div className="flex items-center gap-2 text-sm mb-1">
              <DollarSign className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600">Deal Size Range:</span>
              <span className="font-medium">
                ${(parseFloat(investmentData.minimum_deal_size) / 1000000).toFixed(1)}M - ${(parseFloat(investmentData.maximum_deal_size) / 1000000).toFixed(1)}M
              </span>
            </div>
          )}
        </div>

        {/* Equity Criteria Subcard */}
        {investmentData.equity_criteria && Object.values(investmentData.equity_criteria).some(value => value !== null && value !== undefined) && (
          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
            <h6 className="font-medium text-sm text-green-700 mb-2">Equity Criteria</h6>
            <div className="space-y-1">
              {investmentData.equity_criteria.target_return && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">Target Return:</span>
                  <span className="font-medium text-green-800">{investmentData.equity_criteria.target_return}%</span>
                </div>
              )}
              {investmentData.equity_criteria.min_hold_period_years && investmentData.equity_criteria.max_hold_period_years && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">Hold Period:</span>
                  <span className="font-medium text-green-800">{investmentData.equity_criteria.min_hold_period_years} - {investmentData.equity_criteria.max_hold_period_years} years</span>
                </div>
              )}
              {investmentData.equity_criteria.minimum_equity_multiple && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">Min Equity Multiple:</span>
                  <span className="font-medium text-green-800">{investmentData.equity_criteria.minimum_equity_multiple}x</span>
                </div>
              )}
              {investmentData.equity_criteria.minimum_yield_on_cost && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">Min Yield on Cost:</span>
                  <span className="font-medium text-green-800">{investmentData.equity_criteria.minimum_yield_on_cost}%</span>
                </div>
              )}
              {investmentData.equity_criteria.ownership_requirement && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">Ownership Requirement:</span>
                  <span className="font-medium text-green-800">{investmentData.equity_criteria.ownership_requirement}%</span>
                </div>
              )}
              {investmentData.equity_criteria.attachment_point && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">Attachment Point:</span>
                  <span className="font-medium text-green-800">{investmentData.equity_criteria.attachment_point}%</span>
                </div>
              )}
              {investmentData.equity_criteria.max_leverage_tolerance && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">Max Leverage:</span>
                  <span className="font-medium text-green-800">{investmentData.equity_criteria.max_leverage_tolerance}%</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Debt Criteria Subcard */}
        {investmentData.debt_criteria && Object.values(investmentData.debt_criteria).some(value => value !== null && value !== undefined) && (
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <h6 className="font-medium text-sm text-blue-700 mb-2">Debt Criteria</h6>
            <div className="space-y-1">
              {investmentData.debt_criteria.loan_to_value_min && investmentData.debt_criteria.loan_to_value_max && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-blue-600" />
                  <span className="text-blue-600">LTV:</span>
                  <span className="font-medium text-blue-800">{investmentData.debt_criteria.loan_to_value_min}% - {investmentData.debt_criteria.loan_to_value_max}%</span>
                </div>
              )}
              {investmentData.debt_criteria.loan_to_cost_min && investmentData.debt_criteria.loan_to_cost_max && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-blue-600" />
                  <span className="text-blue-600">LTC:</span>
                  <span className="font-medium text-blue-800">{investmentData.debt_criteria.loan_to_cost_min}% - {investmentData.debt_criteria.loan_to_cost_max}%</span>
                </div>
              )}
              {investmentData.debt_criteria.min_loan_term && investmentData.debt_criteria.max_loan_term && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-3 w-3 text-blue-600" />
                  <span className="text-blue-600">Loan Term:</span>
                  <span className="font-medium text-blue-800">{investmentData.debt_criteria.min_loan_term} - {investmentData.debt_criteria.max_loan_term} years</span>
                </div>
              )}
              {investmentData.debt_criteria.min_loan_dscr && investmentData.debt_criteria.max_loan_dscr && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-blue-600" />
                  <span className="text-blue-600">DSCR:</span>
                  <span className="font-medium text-blue-800">{investmentData.debt_criteria.min_loan_dscr} - {investmentData.debt_criteria.max_loan_dscr}</span>
                </div>
              )}
              {investmentData.debt_criteria.loan_interest_rate && (
                <div className="flex items-center gap-2 text-sm">
                  <Percent className="h-3 w-3 text-blue-600" />
                  <span className="text-blue-600">Interest Rate:</span>
                  <span className="font-medium text-blue-800">{investmentData.debt_criteria.loan_interest_rate}%</span>
                </div>
              )}
              {investmentData.debt_criteria.loan_type && (
                <div className="flex items-center gap-2 text-sm">
                  <Building className="h-3 w-3 text-blue-600" />
                  <span className="text-blue-600">Loan Type:</span>
                  <span className="font-medium text-blue-800">{investmentData.debt_criteria.loan_type}</span>
                </div>
              )}
              {investmentData.debt_criteria.amortization && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-3 w-3 text-blue-600" />
                  <span className="text-blue-600">Amortization:</span>
                  <span className="font-medium text-blue-800">{investmentData.debt_criteria.amortization}</span>
                </div>
              )}
              {investmentData.debt_criteria.recourse_loan !== null && (
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-3 w-3 text-blue-600" />
                  <span className="text-blue-600">Recourse:</span>
                  <span className="font-medium text-blue-800">{investmentData.debt_criteria.recourse_loan ? 'Yes' : 'No'}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Match Reasons */}
        {criteria.reasons && criteria.reasons.length > 0 && (
          <div className="space-y-2">
            <h6 className="font-medium text-sm text-gray-700">Match Reasons:</h6>
            <div className="space-y-1">
              {criteria.reasons.map((reason: string, index: number) => (
                <div key={index} className="flex items-start gap-2 text-sm bg-green-50 p-2 rounded border border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{reason}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <Badge variant="outline" className={`${getScoreColor(company.best_match_score * 100)} text-sm font-semibold px-3 py-1`}>
                {formatScore(company.best_match_score)} Best Match
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                {company.total_contacts} contact{company.total_contacts !== 1 ? 's' : ''}
              </Badge>
            </div>
            
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-gray-500" />
                <span className="text-lg font-semibold text-gray-900">
                  {company.company_name}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCompanyClick(company.company_id)}
              className="min-w-[120px]"
            >
              <Building2 className="h-4 w-4 mr-2" />
              View Company
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="min-w-[120px]"
            >
              {isExpanded ? 'Hide Details' : 'View Details'}
            </Button>
          </div>
        </div>
      </CardHeader>

      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleContent>
          <CardContent className="pt-0">
            <div className="space-y-6">
              {/* Company Criteria Section */}
              {company.company_criteria && company.company_criteria.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <Building2 className="h-4 w-4 text-blue-600" />
                    <h5 className="font-semibold text-gray-800">Company Criteria</h5>
                    <Badge variant="outline" className="text-xs">
                      {company.company_criteria.length} criteria
                    </Badge>
                  </div>
                  <div className="space-y-4">
                    {company.company_criteria.map((criteria: any, index: number) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        {renderCriteriaDetails(criteria, 'company', criteria)}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Contact Criteria Section */}
              {company.contact_criteria && company.contact_criteria.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <User className="h-4 w-4 text-green-600" />
                    <h5 className="font-semibold text-gray-800">Contact Criteria</h5>
                    <Badge variant="outline" className="text-xs">
                      {company.contact_criteria.length} criteria
                    </Badge>
                  </div>
                  <div className="space-y-4">
                    {/* Group contact criteria by contact */}
                    {(() => {
                      const contactGroups = new Map();
                      company.contact_criteria.forEach((criteria: any) => {
                        const contactId = criteria.contact_id;
                        if (!contactGroups.has(contactId)) {
                          contactGroups.set(contactId, {
                            contact_id: contactId,
                            contact_name: criteria.contact_name,
                            criteria: []
                          });
                        }
                        contactGroups.get(contactId).criteria.push(criteria);
                      });
                      
                      return Array.from(contactGroups.values()).map((contactGroup: any, index: number) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-green-600" />
                              <span className="text-sm font-medium text-gray-900">
                                {contactGroup.contact_name}
                              </span>
                              <Badge variant="outline" className="text-xs">
                                {contactGroup.criteria.length} criteria
                              </Badge>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onContactClick(contactGroup.contact_id)}
                              className="text-xs"
                            >
                              <User className="h-3 w-3 mr-1" />
                              View Contact
                            </Button>
                          </div>
                          
                          {/* Render multiple criteria for this contact */}
                          <div className="space-y-3">
                            {contactGroup.criteria.map((criteria: any, criteriaIndex: number) => (
                              <div key={criteriaIndex} className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                {renderCriteriaDetails(criteria, 'contact')}
                              </div>
                            ))}
                          </div>
                        </div>
                      ));
                    })()}
                  </div>
                </div>
              )}

              {/* All Contacts List */}
              {company.contacts && company.contacts.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <Users className="h-4 w-4 text-purple-600" />
                    <h5 className="font-semibold text-gray-800">All Contacts</h5>
                    <Badge variant="outline" className="text-xs">
                      {company.contacts.length} contact{company.contacts.length !== 1 ? 's' : ''}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {company.contacts.map((contact: any) => (
                      <div key={contact.contact_id} className="border border-gray-200 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <span className="text-sm font-medium text-gray-900">
                              {contact.contact_name}
                            </span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {formatScore(contact.match_score)}
                          </Badge>
                        </div>
                        {contact.email && (
                          <div className="text-xs text-gray-500 mb-2">{contact.email}</div>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onContactClick(contact.contact_id)}
                          className="w-full text-xs"
                        >
                          <User className="h-3 w-3 mr-1" />
                          View Contact
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default CompanyMatchingCard;
