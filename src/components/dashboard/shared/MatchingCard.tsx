import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Users, 
  Building2, 
  ChevronRight, 
  TrendingUp, 
  CheckCircle,
  Target,
  Building,
  DollarSign,
  MapPin,
  Calendar,
  Briefcase
} from "lucide-react";
import { useRouter } from 'next/navigation';

interface MatchingCardProps {
  data: any;
  variant: 'contact' | 'deal'; // 'contact' for deals-for-contact, 'deal' for contacts-for-deal
  onExpand?: (id: string | number) => void;
  isExpanded?: boolean;
  formatScore?: (score: number) => string;
}

// Collapsible component for Match Reasons & Scoring
const CollapsibleMatchReasons: React.FC<{ 
  data: any; 
  formatScore: (score: number) => string;
  variant: 'contact' | 'deal';
}> = ({ data, formatScore, variant }) => {
  const [showMatchReasons, setShowMatchReasons] = useState(false);
  
  return (
    <div className="p-6 pb-4 border-b border-gray-100">
      <button
        onClick={() => setShowMatchReasons(!showMatchReasons)}
        className="w-full flex items-center justify-between mb-4 hover:bg-gray-50 p-2 rounded transition-colors"
      >
        <h5 className="font-semibold text-gray-900 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          Match Reasons & Scoring
        </h5>
        <ChevronRight className={`h-5 w-5 transform transition-transform ${showMatchReasons ? 'rotate-90' : ''} text-gray-400`} />
      </button>
      
      {showMatchReasons && (
        <div className="space-y-4">
          {/* Score Breakdown - Use Actual V2 Data */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h6 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Score Breakdown: {formatScore(data.match_score || data.score || 0)}
            </h6>
            {data.scoring_summary ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                {data.scoring_summary.field_breakdown
                  .filter((field: any) => field.score_percentage > 0)
                  .map((field: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-white rounded border">
                    <span className="text-blue-700 capitalize">{field.field_name.replace(/_/g, ' ')}</span>
                    <Badge 
                      variant="outline" 
                      className="bg-green-100 text-green-700 border-green-300 text-xs"
                    >
                      +{field.score_percentage}% (w: {field.weight_percentage}%)
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-gray-600 italic">
                Detailed scoring breakdown not available
              </div>
            )}
          </div>

          {/* Specific Match Reasons - Use Actual V2 Data */}
          <div>
            <h6 className="font-medium text-gray-800 mb-2">What Matched:</h6>
            {data.scoring_summary ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {data.scoring_summary.field_breakdown
                  .filter((field: any) => field.score_percentage > 0)
                  .map((field: any, index: number) => (
                    <div key={index} className="flex items-start gap-2 text-sm bg-green-50 p-3 rounded-lg border border-green-200">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium text-gray-700 capitalize">{field.field_name.replace(/_/g, ' ')}</div>
                        <div className="text-gray-600">{field.reason}</div>
                        <div className="text-xs text-gray-500">
                          Score: {field.score_percentage}% (Weight: {field.weight_percentage}%)
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {(data.match_reasons || data.reasons || []).map((reason: string, index: number) => (
                  <div key={index} className="flex items-start gap-2 text-sm bg-green-50 p-3 rounded-lg border border-green-200">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{reason}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Collapsible component for V2 Detailed Scoring Breakdown
const CollapsibleDetailedScoring: React.FC<{ data: any; variant: 'contact' | 'deal' }> = ({ data, variant }) => {
  const [showDetailedScoring, setShowDetailedScoring] = useState(false);
  
  return (
    <div className="p-6 pb-4 border-b border-gray-100">
      <button
        onClick={() => setShowDetailedScoring(!showDetailedScoring)}
        className="w-full flex items-center justify-between mb-4 hover:bg-gray-50 p-2 rounded transition-colors"
      >
        <h5 className="font-semibold text-gray-900 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-purple-600" />
          V2 Detailed Scoring Breakdown
          {data.scoring_summary && (
            <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 border-blue-300">
              Total: {data.scoring_summary.total_score_percentage}%
            </Badge>
          )}
        </h5>
        <ChevronRight className={`h-5 w-5 transform transition-transform ${showDetailedScoring ? 'rotate-90' : ''} text-gray-400`} />
      </button>
      
      {showDetailedScoring && data.scoring_summary && (
        <div className="space-y-4">
          {/* Position Type Info */}
          <div className="p-3 bg-blue-50 rounded border border-blue-200">
            <div className="text-sm text-blue-800">
              <strong>Position Type:</strong> {data.scoring_summary.position_type} | 
              <strong>Capital Position:</strong> {data.scoring_summary.capital_position}
            </div>
          </div>
          
          {/* Field-by-Field Breakdown */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide mb-3">Field Scoring Breakdown</h6>
              
              {/* Matched Fields (Score > 0) */}
              <div className="space-y-2 mb-4">
                <h7 className="text-xs text-green-700 font-semibold uppercase">Matched Fields</h7>
                {data.scoring_summary.field_breakdown
                  .filter((field: any) => field.score_percentage > 0)
                  .map((field: any, index: number) => (
                    <div key={index} className="p-2 bg-green-50 rounded border border-green-200">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium text-green-800 capitalize">{field.field_name.replace(/_/g, ' ')}</span>
                        <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                          +{field.score_percentage}% (w: {field.weight_percentage}%)
                        </Badge>
                      </div>
                      <div className="text-xs text-green-600">{field.reason}</div>
                    </div>
                  ))}
              </div>

              {/* Non-matched Fields (Score = 0) */}
              <div className="space-y-2">
                <h7 className="text-xs text-gray-500 font-semibold uppercase">Non-matched Fields</h7>
                {data.scoring_summary.field_breakdown
                  .filter((field: any) => field.score_percentage === 0)
                  .slice(0, 3) // Show only first 3 to avoid clutter
                  .map((field: any, index: number) => (
                    <div key={index} className="p-2 bg-gray-50 rounded border border-gray-200">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm text-gray-600 capitalize">{field.field_name.replace(/_/g, ' ')}</span>
                        <Badge variant="outline" className="text-xs bg-gray-100 text-gray-500 border-gray-300">
                          0% (w: {field.weight_percentage}%)
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500">{field.reason}</div>
                    </div>
                  ))}
              </div>
            </div>

            {/* Match Analysis Summary */}
            <div>
              <h6 className="font-medium text-gray-800 text-sm uppercase tracking-wide mb-3">Match Analysis</h6>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded border border-blue-200">
                  <div className="text-sm text-blue-800">
                    <div><strong>Fields Evaluated:</strong> {data.scoring_summary.match_analysis?.total_fields_evaluated || 0}</div>
                    <div><strong>Fields Matched:</strong> {data.scoring_summary.match_analysis?.fields_with_matches || 0}</div>
                    <div><strong>Fields Not Matched:</strong> {data.scoring_summary.match_analysis?.fields_without_matches || 0}</div>
                  </div>
                </div>
                
                {/* Top Matches */}
                {data.scoring_summary.top_matches && data.scoring_summary.top_matches.length > 0 && (
                  <div>
                    <h7 className="text-xs text-blue-700 font-semibold uppercase mb-2 block">Top Matches</h7>
                    <div className="space-y-1">
                      {data.scoring_summary.top_matches.slice(0, 3).map((match: any, index: number) => (
                        <div key={index} className="flex justify-between items-center text-xs p-2 bg-blue-50 rounded">
                          <span className="capitalize">{match.field.replace(/_/g, ' ')}</span>
                          <span className="font-semibold text-blue-700">{match.score}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const MatchingCard: React.FC<MatchingCardProps> = ({ 
  data, 
  variant, 
  onExpand, 
  isExpanded = false,
  formatScore = (score: number) => `${score}%`
}) => {
  const router = useRouter();
  const score = data.match_score || data.score || 0;
  
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 border-green-300';
    if (score >= 60) return 'bg-blue-100 text-blue-800 border-blue-300';
    if (score >= 40) return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    return 'bg-orange-100 text-orange-800 border-orange-300';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-700 border-green-300';
    if (confidence >= 0.6) return 'bg-blue-100 text-blue-700 border-blue-300';
    return 'bg-yellow-100 text-yellow-700 border-yellow-300';
  };

  const handleNavigate = () => {
    if (variant === 'contact') {
      router.push(`/dashboard/people/${data.contact_id}`);
    } else {
      // For deals, check if it's V2 or V1
      if (data.deal_data?.deal_version === 'v2' || data.deal_id) {
        router.push(`/dashboard/deals/v2/${data.deal_id}`);
      } else {
        router.push(`/dashboard/deals/${data.deal_id}`);
      }
    }
  };

  const renderContactCard = () => (
    <>
      {/* Header Section with Score and Basic Info */}
      <div className="p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-white">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className={`${getScoreColor(score)} text-sm font-semibold px-3 py-1`}>
                  {formatScore(score)} Match
                </Badge>
                {data.confidence && (
                  <Badge variant="outline" className={`${getConfidenceColor(data.confidence)} text-sm px-3 py-1`}>
                    {formatScore(data.confidence * 100)} Confidence
                  </Badge>
                )}
              </div>
              {data.capital_positions && data.capital_positions.length > 1 ? (
                <div className="flex flex-wrap gap-1">
                  {data.capital_positions.map((pos: string, index: number) => (
                    <Badge key={index} variant="secondary" className="px-2 py-1 text-xs">
                      {pos}
                    </Badge>
                  ))}
                </div>
              ) : (
                <Badge variant="secondary" className="px-3 py-1">
                  {data.capital_position}
                </Badge>
              )}
              {data.criteria_source && (
                <Badge variant="outline" className="text-xs px-2 py-1 border-gray-300">
                  {data.criteria_source}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-6">
              <div>
                <h4 
                  className="text-xl font-semibold text-gray-900 mb-1 cursor-pointer hover:text-blue-600 transition-colors flex items-center gap-2 group"
                  onClick={handleNavigate}
                  title="Click to view contact details"
                >
                  {data.contact_name || `${data.first_name} ${data.last_name}`.trim()}
                  <Users className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                </h4>
                <p 
                  className="text-lg text-gray-700 font-medium cursor-pointer hover:text-blue-600 transition-colors flex items-center gap-2 group"
                  onClick={() => router.push(`/dashboard/companies/${data.company_id}`)}
                  title="Click to view company details"
                >
                  {data.company_name}
                  <Building2 className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                </p>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600">
                {data.email && (
                  <div className="flex items-center gap-2">
                    <span className="text-blue-600">📧</span>
                    <span className="font-medium">{data.email}</span>
                  </div>
                )}
                {data.phone && (
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">📞</span>
                    <span className="font-medium">{data.phone}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-col gap-2 ml-4">
            {onExpand && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExpand(data.contact_id)}
                className="min-w-[120px]"
              >
                {isExpanded ? 'Hide Details' : 'View Details'}
              </Button>
            )}
            <Button 
              size="sm" 
              className="min-w-[120px] bg-blue-600 hover:bg-blue-700"
              onClick={handleNavigate}
            >
              <Users className="h-4 w-4 mr-2" />
              View Contact
            </Button>
          </div>
        </div>
      </div>
    </>
  );

  const renderDealCard = () => (
    <>
      {/* Header Section with Score and Basic Info */}
      <div className="p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-green-50 to-white">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className={`${getScoreColor(score)} text-sm font-semibold px-3 py-1`}>
                  {formatScore(score)} Match
                </Badge>
                {data.confidence && (
                  <Badge variant="outline" className={`${getConfidenceColor(data.confidence)} text-sm px-3 py-1`}>
                    {formatScore(data.confidence * 100)} Confidence
                  </Badge>
                )}
              </div>
              <Badge variant="secondary" className="px-3 py-1">
                {data.capital_position}
              </Badge>
              {data.criteria_source && (
                <Badge variant="outline" className="text-xs px-2 py-1 border-gray-300">
                  {data.criteria_source}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-6">
              <div>
                <h4 
                  className="text-xl font-semibold text-gray-900 mb-1 cursor-pointer hover:text-green-600 transition-colors flex items-center gap-2 group"
                  onClick={handleNavigate}
                  title="Click to view deal details"
                >
                  {data.deal_name || `Deal #${data.deal_id}`}
                  <Building className="h-4 w-4 text-gray-400 group-hover:text-green-600 transition-colors" />
                </h4>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  {data.deal_data?.ask_amount && (
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4" />
                      <span className="font-medium">${(data.deal_data.ask_amount / 1000000).toFixed(1)}M</span>
                    </div>
                  )}
                  {data.deal_data?.property_city && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      <span className="font-medium">{data.deal_data.property_city}, {data.deal_data.property_state}</span>
                    </div>
                  )}
                  {data.deal_data?.strategy && (
                    <div className="flex items-center gap-1">
                      <Briefcase className="h-4 w-4" />
                      <span className="font-medium">{data.deal_data.strategy}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-col gap-2 ml-4">
            {onExpand && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExpand(data.deal_id)}
                className="min-w-[120px]"
              >
                {isExpanded ? 'Hide Details' : 'View Details'}
              </Button>
            )}
            <Button 
              size="sm" 
              className="min-w-[120px] bg-green-600 hover:bg-green-700"
              onClick={handleNavigate}
            >
              <Building className="h-4 w-4 mr-2" />
              View Deal
            </Button>
          </div>
        </div>
      </div>
    </>
  );

  return (
    <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
      <CardContent className="p-0">
        {variant === 'contact' ? renderContactCard() : renderDealCard()}
        
        {/* Match Reasons Section - Collapsible */}
        <CollapsibleMatchReasons data={data} formatScore={formatScore} variant={variant} />

        {/* V2 Detailed Scoring Breakdown - Collapsible */}
        <CollapsibleDetailedScoring data={data} variant={variant} />
      </CardContent>
    </Card>
  );
};

export default MatchingCard;

