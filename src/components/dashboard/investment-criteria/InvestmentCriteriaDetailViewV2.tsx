'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  Building2, 
  MapPin, 
  DollarSign, 
  TrendingUp, 
  Calculator,
  Percent,
  Clock,
  FileText,
  Loader2,
  Edit,
  Trash2,
  Target,
  Plus,
  Save,
  X,
  AlertCircle,
  ExternalLink,
  Shield,
  Info,
  Database,
  ChevronUp,
  ChevronDown,
  Calendar,
  CalendarDays
} from 'lucide-react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { Tooltip } from '@/components/ui/tooltip'
import { useToast } from '@/hooks/use-toast'
import InvestmentCriteriaForm from './InvestmentCriteriaForm'

interface InvestmentCriteriaData {
  investment_criteria_id: number;
  entity_type: string;
  entity_id: number;
  entity_name?: string;
  entity_location?: string;
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  sources?: {
    // Consolidated sources structure (new format)
    all_sources?: Array<{
      field: string;
      url: string;
      page_section: string;
      evidence_quote: string;
      source_type: string;
      date_found: string;
      confidence_score: number;
    }>;
    total_sources?: number;
    source_types?: string[];
    fields_with_sources?: string[];
    extraction_date?: string;
    
    // Legacy source structure (for backward compatibility)
    capital_position_sources?: string[];
    minimum_deal_size_sources?: string[];
    maximum_deal_size_sources?: string[];
    country_sources?: string[];
    region_sources?: string[];
    state_sources?: string[];
    city_sources?: string[];
    property_types_sources?: string[];
    property_subcategories_sources?: string[];
    strategies_sources?: string[];
    decision_making_process_sources?: string[];
    notes_sources?: string[];
    target_return_sources?: string[];
    min_hold_period_sources?: string[];
    max_hold_period_sources?: string[];
    financial_products_sources?: string[];
    historical_irr_sources?: string[];
    historical_em_sources?: string[];
    loan_program_sources?: string[];
    loan_type_sources?: string[];
    capital_source_sources?: string[];
    structured_loan_tranche_sources?: string[];
    min_loan_term_sources?: string[];
    max_loan_term_sources?: string[];
    interest_rate_sources?: string[];
    interest_rate_sofr_sources?: string[];
    interest_rate_wsj_sources?: string[];
    interest_rate_prime_sources?: string[];
    loan_to_value_max_sources?: string[];
    loan_to_value_min_sources?: string[];
    loan_to_cost_max_sources?: string[];
    loan_to_cost_min_sources?: string[];
    loan_origination_fee_max_sources?: string[];
    loan_origination_fee_min_sources?: string[];
    loan_exit_fee_max_sources?: string[];
    loan_exit_fee_min_sources?: string[];
    min_loan_dscr_sources?: string[];
    max_loan_dscr_sources?: string[];
    recourse_loan_sources?: string[];
    loan_type_normalized_sources?: string[];
    closing_time_weeks_sources?: string[];
    general_sources?: string[];
    
    // New evidence-grounded structure (matching CompanyInvestmentCriteriaProcessor schema)
    capital_position?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    minimum_deal_size?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    maximum_deal_size?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    country?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    region?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    state?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    city?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    property_types?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    property_subcategories?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    strategies?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    decision_making_process?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
    notes?: {
      evidence?: string | null;
      confidence_score?: number;
      source_url?: string | null;
      sources?: Array<{
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
      }>;
    };
  };
  created_at: string;
  updated_at: string;
  
  // Relationship IDs to debt/equity records
  investment_criteria_debt_id?: number;
  investment_criteria_equity_id?: number;
  
  // Debt-specific fields
  loan_type?: string;
  loan_program?: string;
  min_loan_term?: number;
  max_loan_term?: number;
  loan_interest_rate?: number;
  loan_interest_rate_based_off_sofr?: number;
  loan_interest_rate_based_off_wsj?: number;
  loan_interest_rate_based_off_prime?: number;
  loan_interest_rate_based_off_3yt?: number;
  loan_interest_rate_based_off_5yt?: number;
  loan_interest_rate_based_off_10yt?: number;
  loan_interest_rate_based_off_30yt?: number;
  loan_to_value_min?: number;
  loan_to_value_max?: number;
  loan_to_cost_min?: number;
  loan_to_cost_max?: number;
  loan_origination_min_fee?: number;
  loan_origination_max_fee?: number;
  loan_exit_min_fee?: number;
  loan_exit_max_fee?: number;
  min_loan_dscr?: number;
  max_loan_dscr?: number;
  structured_loan_tranche?: string;
  recourse_loan?: string;
  closing_time?: number;
  debt_program_overview?: string;
  lien_position?: string;
  loan_min_debt_yield?: string;
  prepayment?: string;
  yield_maintenance?: string;
  amortization?: string;
  application_deposit?: number;
  good_faith_deposit?: number;
  future_facilities?: string;
  eligible_borrower?: string;
  occupancy_requirements?: string;
  rate_lock?: string;
  rate_type?: string;
  loan_type_normalized?: string;
  debt_notes?: string;
  
  // Equity-specific fields
  target_return?: number;
  minimum_internal_rate_of_return?: number;
  min_hold_period_years?: number;
  max_hold_period_years?: number;
  minimum_yield_on_cost?: number;
  minimum_equity_multiple?: number;
  ownership_requirement?: string;
  equity_program_overview?: string;
  target_cash_on_cash_min?: number;
  attachment_point?: number;
  max_leverage_tolerance?: number;
  typical_closing_timeline_days?: number;
  proof_of_funds_requirement?: boolean;
  equity_occupancy_requirements?: string;
  equity_notes?: string;
  yield_on_cost?: number;
  target_return_irr_on_equity?: number;
  equity_multiple?: number;
  position_specific_irr?: number;
  position_specific_equity_multiple?: number;
}

interface GroupedCriteria {
  [capitalPosition: string]: InvestmentCriteriaData[];
}

interface InvestmentCriteriaDetailViewV2Props {
  entityType: 'company' | 'contact' | 'deal';
  entityId: string | number;
  onBack?: () => void;
  className?: string;
  isAddingNew?: boolean;
  onSave?: (formData: any) => Promise<void>;
  initialCriteria?: InvestmentCriteriaData;
  onDataUpdate?: () => void; // Callback to notify parent of data updates
  onDelete?: (criteriaId: number) => Promise<void>; // Callback for deletion
}

export default function InvestmentCriteriaDetailViewV2({ 
  entityType,
  entityId,
  onBack,
  className = '',
  isAddingNew = false,
  onSave,
  initialCriteria,
  onDataUpdate,
  onDelete
}: InvestmentCriteriaDetailViewV2Props) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [groupedCriteria, setGroupedCriteria] = useState<GroupedCriteria>({})
  const [totalCriteria, setTotalCriteria] = useState(0)
  const [isEditing, setIsEditing] = useState(false)
  const [editingCriteria, setEditingCriteria] = useState<InvestmentCriteriaData | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  
  // Admin mode state - In production, this should be based on user roles/permissions
  const [isAdminMode, setIsAdminMode] = useState(false)
  
  // Expanded sections state for collapsible sections
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})
  
  // Check if user is admin (this is a simple implementation - replace with actual auth check)
  useEffect(() => {
    // In a real application, check user permissions here
    // For now, we'll use a simple localStorage or environment check
    const adminMode = localStorage.getItem('admin_mode') === 'true' || process.env.NODE_ENV === 'development'
    setIsAdminMode(adminMode)
  }, [])

  const fetchInvestmentCriteria = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Use the correct API endpoint based on entity type
      const endpoint = entityType === 'contact' 
        ? `/api/investment-criteria/by-contact/${entityId}`
        : `/api/investment-criteria/by-company/${entityId}`
      
      const response = await fetch(endpoint)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch investment criteria: ${response.statusText}`)
      }
      
      const criteria = await response.json()
      
      // Group criteria by capital position
      const grouped = criteria.reduce((acc: any, item: any) => {
        const position = item.capital_position || 'Unknown'
        if (!acc[position]) {
          acc[position] = []
        }
        acc[position].push(item)
        return acc
      }, {})
      
      setGroupedCriteria(grouped)
      setTotalCriteria(criteria.length)
    } catch (err) {
      console.error('Error fetching investment criteria:', err)
      setError(err instanceof Error ? err.message : 'Failed to load investment criteria')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (initialCriteria) {
      // Use the provided initial criteria
      const grouped = {
        [initialCriteria.capital_position || 'Unknown']: [initialCriteria]
      }
      setGroupedCriteria(grouped)
      setTotalCriteria(1)
      setLoading(false)
      
      // If we're currently editing, update the editing criteria with the new data
      if (isEditing && editingCriteria) {
        setEditingCriteria(initialCriteria)
      }
    } else {
      // Fetch from API as before
      fetchInvestmentCriteria()
    }
  }, [entityType, entityId, initialCriteria])

  const handleEdit = (criteria: InvestmentCriteriaData) => {
    setIsEditing(true)
    // Use the most up-to-date data - prefer initialCriteria if available
    const dataToEdit = initialCriteria || criteria
    setEditingCriteria({ ...dataToEdit })
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditingCriteria(null)
  }

  const handleSaveEdit = async (formData: any, hasChangedCentralCriteria?: boolean) => {
    setIsSaving(true)
    try {
      const response = await fetch(`/api/investment-criteria-entity/${entityType}/${entityId}`, {
        method: editingCriteria?.investment_criteria_id ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          criteria_id: editingCriteria?.investment_criteria_id,
          updates: formData
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Failed to ${editingCriteria?.investment_criteria_id ? 'update' : 'create'} investment criteria: ${response.statusText}`)
      }
      
      // If this is a company and central criteria changed, trigger contact reprocessing
      if (entityType === 'company' && hasChangedCentralCriteria) {
        try {
          const reprocessResponse = await fetch('/api/investment-criteria/reprocess-contacts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              companyId: entityId,
              triggerProcessing: true
            })
          })
          
          if (reprocessResponse.ok) {
            const reprocessResult = await reprocessResponse.json()
            if (reprocessResult.contactsReprocessed > 0) {
              toast({
                title: "Success",
                description: `Investment criteria updated and ${reprocessResult.contactsReprocessed} contacts queued for reprocessing`,
              })
            } else {
              toast({
                title: "Success",
                description: "Investment criteria updated successfully",
              })
            }
          } else {
            // Still show success but mention reprocessing issue
            toast({
              title: "Partial Success",
              description: "Investment criteria updated but contact reprocessing may have failed",
              variant: "destructive",
            })
          }
        } catch (reprocessError) {
          console.warn('Contact reprocessing failed:', reprocessError)
          toast({
            title: "Partial Success",
            description: "Investment criteria updated but contact reprocessing failed",
            variant: "destructive",
          })
        }
      } else {
        toast({
          title: "Success",
          description: `Investment criteria ${editingCriteria?.investment_criteria_id ? 'updated' : 'created'} successfully`,
        })
      }
      
      // If we have initialCriteria, we need to notify the parent to refresh data
      if (initialCriteria) {
        onDataUpdate?.()
        // Exit edit mode immediately after notifying parent
        setIsEditing(false)
        setEditingCriteria(null)
      } else {
        await fetchInvestmentCriteria()
        setIsEditing(false)
        setEditingCriteria(null)
      }
    } catch (error) {
      console.error('Error saving investment criteria:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to save investment criteria',
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleDelete = async (criteriaId: number) => {
    setIsSaving(true)
    try {
      // If we have an onDelete prop, use it (for parent component handling)
      if (onDelete) {
        await onDelete(criteriaId)
        return
      }
      
      // Otherwise, handle deletion internally with confirmation and toast
      if (!confirm('Are you sure you want to delete this investment criteria? This action cannot be undone and will permanently remove all associated data including debt and equity information.')) {
        return
      }
      
      const response = await fetch(`/api/investment-criteria-entity/${entityType}/${entityId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          criteria_id: criteriaId
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Failed to delete investment criteria: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Investment criteria deleted successfully",
        })
        
        // If we have initialCriteria, we need to notify the parent to refresh data
        if (initialCriteria) {
          onDataUpdate?.()
        } else {
          await fetchInvestmentCriteria()
        }
      } else {
        throw new Error(result.error || 'Failed to delete investment criteria')
      }
    } catch (error) {
      console.error('Error deleting investment criteria:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to delete investment criteria',
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleAddNew = () => {
    setIsEditing(true)
    setEditingCriteria(null) // null means creating new
  }

  // Format currency display
  const formatCurrency = (value?: number) => {
    if (!value) return 'Not specified'
    if (value >= 1000000000) {
      return `$${(value / 1000000000).toFixed(1)}B`
    }
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`
    }
    return `$${value.toLocaleString()}`
  }

  // Format percentage display
  const formatPercentage = (value?: number) => {
    if (!value) return 'Not specified'
    return `${value}%`
  }

  // Check if a field has page metadata sources
  const hasPageMetadataSource = (fieldSources?: Array<{source_type: string}>) => {
    return fieldSources?.some(source => source.source_type === 'page_metadata') || false
  }

  // Render metadata indicator badge
  const renderMetadataIndicator = (fieldSources?: Array<{source_type: string}>) => {
    if (!hasPageMetadataSource(fieldSources)) return null
    return (
      <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 text-xs ml-2">
        📊 Metadata
      </Badge>
    )
  }

  // Format array display
  const formatArray = (arr?: string[], fieldSources?: Array<{source_type: string}>) => {
    if (!arr || arr.length === 0) return <span className="text-slate-400">Not specified</span>
    return (
      <div className="flex flex-wrap gap-2 items-center">
        {arr.map((item, index) => (
          <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            {item}
          </Badge>
        ))}
        {hasPageMetadataSource(fieldSources) && (
          <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 text-xs">
            📊 Metadata
          </Badge>
        )}
      </div>
    )
  }

  // Format date display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  // Get confidence score color
  const getConfidenceColor = (score?: number) => {
    if (!score) return 'text-gray-500'
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Get confidence badge variant
  const getConfidenceBadgeVariant = (score?: number) => {
    if (!score) return 'secondary'
    if (score >= 0.9) return 'default'
    if (score >= 0.7) return 'secondary'
    return 'destructive'
  }

  // Map UI field names to database source field names
  const getSourceFieldName = (uiFieldName: string): string => {
    const fieldMapping: Record<string, string> = {
      // Basic fields
      'capital_position': 'capital_position',
      'deal_size_range': 'minimum_deal_size', // Use minimum_deal_size as primary for deal size
      'country': 'country',
      'region': 'region', 
      'state': 'state',
      'city': 'city',
      'property_types': 'property_types',
      'property_subcategories': 'property_subcategories',
      'strategies': 'strategies',
      
      // Debt fields
      'loan_type': 'loan_type',
      'loan_program': 'loan_program',
      'structured_loan_tranche': 'structured_loan_tranche',
      'recourse_loan': 'recourse_loan',
      'closing_time': 'closing_time_weeks',
      'lien_position': 'lien_position',
      
      // Equity fields
      'ownership_requirement': 'ownership_requirement',
      'max_leverage_tolerance': 'max_leverage_tolerance',
      'typical_closing_timeline_days': 'typical_closing_timeline_days',
      'proof_of_funds_requirement': 'proof_of_funds_requirement',
      
      // Additional debt fields that might be used
      'loan_to_value_max': 'loan_to_value_max',
      'loan_to_value_min': 'loan_to_value_min',
      'loan_to_cost_max': 'loan_to_cost_max',
      'loan_to_cost_min': 'loan_to_cost_min',
      'min_loan_dscr': 'min_loan_dscr',
      'max_loan_dscr': 'max_loan_dscr',
      'loan_interest_rate': 'interest_rate',
      'loan_interest_rate_sofr': 'interest_rate_sofr',
      'loan_interest_rate_wsj': 'interest_rate_wsj',
      'loan_interest_rate_prime': 'interest_rate_prime',
      'loan_origination_fee_max': 'loan_origination_fee_max',
      'loan_origination_fee_min': 'loan_origination_fee_min',
      'loan_exit_fee_max': 'loan_exit_fee_max',
      'loan_exit_fee_min': 'loan_exit_fee_min',
      
      // Specific debt loan ratio fields
      'debt_loan_to_value_max': 'loan_to_value_max',
      'debt_loan_to_value_min': 'loan_to_value_min',
      'debt_loan_to_cost_max': 'loan_to_cost_max',
      'debt_loan_to_cost_min': 'loan_to_cost_min',
      
      // Additional equity fields
      'target_return': 'target_return',
      'minimum_internal_rate_of_return': 'minimum_internal_rate_of_return',
      'min_hold_period_years': 'min_hold_period_years',
      'max_hold_period_years': 'max_hold_period_years',
      'minimum_yield_on_cost': 'minimum_yield_on_cost',
      'minimum_equity_multiple': 'minimum_equity_multiple',
      'target_cash_on_cash_min': 'target_cash_on_cash_min',
      'attachment_point': 'attachment_point'
    }
    
    return fieldMapping[uiFieldName] || uiFieldName
  }

  // Check if a field has evidence-grounded source information
  const hasSourceInfo = (fieldName: string, criteria: InvestmentCriteriaData) => {
    const sources = criteria.sources
    if (!sources) return false
    
    try {
      // Check for consolidated sources structure
      if (sources.all_sources && Array.isArray(sources.all_sources)) {
        const mappedFieldName = getSourceFieldName(fieldName)
        return sources.all_sources.some((source: any) => source.field === mappedFieldName)
      }
      
      // Check for evidence-grounded structure
      const mappedFieldName = getSourceFieldName(fieldName)
      const evidenceField = sources[mappedFieldName as keyof typeof sources]
      if (evidenceField && typeof evidenceField === 'object' && !Array.isArray(evidenceField)) {
        const evidenceData = evidenceField as any
        return 'evidence' in evidenceData && (evidenceData.evidence || (evidenceData.sources && evidenceData.sources.length > 0))
      }
    } catch (e) {
      console.error('Error checking source info:', e)
    }
    
    return false
  }

  // Get field source information
  const getFieldSourceInfo = (fieldName: string, criteria: InvestmentCriteriaData) => {
    const sources = criteria.sources
    if (!sources) return null
    
    try {
      const mappedFieldName = getSourceFieldName(fieldName)
      
      // Check for consolidated sources structure
      if (sources.all_sources && Array.isArray(sources.all_sources)) {
        const fieldSources = sources.all_sources.filter((source: any) => source.field === mappedFieldName)
        if (fieldSources.length > 0) {
          return {
            sources: fieldSources,
            averageConfidence: fieldSources.reduce((sum, s) => sum + (s.confidence_score || 0), 0) / fieldSources.length
          }
        }
      }
      
      // Check for evidence-grounded structure
      const evidenceField = sources[mappedFieldName as keyof typeof sources]
      if (evidenceField && typeof evidenceField === 'object' && !Array.isArray(evidenceField)) {
        const evidenceData = evidenceField as any
        if ('sources' in evidenceData && evidenceData.sources && evidenceData.sources.length > 0) {
          return {
            sources: evidenceData.sources,
            averageConfidence: evidenceData.confidence_score || 0
          }
        }
      }
    } catch (e) {
      console.error('Error getting field source info:', e)
    }
    
    return null
  }

  // Get source type color and display name
  const getSourceTypeInfo = (sourceType: string) => {
    switch (sourceType) {
      case 'page_metadata':
        return {
          color: 'bg-emerald-50 text-emerald-700 border-emerald-200',
          displayName: 'Page Metadata Analysis',
          priority: 0 // Highest priority as per prompt instructions
        }
      case 'web_scraped_text':
        return {
          color: 'bg-green-50 text-green-700 border-green-200',
          displayName: 'Web Scraped Text',
          priority: 1
        }
      case 'enrichment_data':
        return {
          color: 'bg-purple-50 text-purple-700 border-purple-200',
          displayName: 'Enrichment Data',
          priority: 2
        }
      case 'company_website':
        return {
          color: 'bg-blue-50 text-blue-700 border-blue-200',
          displayName: 'Company Website',
          priority: 3
        }
      case 'news_article':
        return {
          color: 'bg-orange-50 text-orange-700 border-orange-200',
          displayName: 'News Article',
          priority: 4
        }
      case 'press_release':
        return {
          color: 'bg-indigo-50 text-indigo-700 border-indigo-200',
          displayName: 'Press Release',
          priority: 5
        }
      case 'web_search':
        return {
          color: 'bg-cyan-50 text-cyan-700 border-cyan-200',
          displayName: 'Web Search',
          priority: 6
        }
      default:
        return {
          color: 'bg-gray-50 text-gray-700 border-gray-200',
          displayName: sourceType.replace('_', ' '),
          priority: 7
        }
    }
  }

  const getCapitalPositionColor = (position: string) => {
    if (!position) return 'bg-gray-50 text-gray-700 border-gray-200'
    switch (position.toLowerCase()) {
      case 'debt':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'equity':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'mezzanine':
        return 'bg-purple-50 text-purple-700 border-purple-200'
      case 'preferred equity':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  // Render info field with source button
  const renderInfoField = (label: string, value: any, icon: React.ReactNode, colorClass: string = 'blue', isLink: boolean = false, fieldName?: string, criteria?: InvestmentCriteriaData) => {
    if (!value) return null

    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600',
      red: 'bg-red-50 text-red-600',
      yellow: 'bg-yellow-50 text-yellow-600',
      gray: 'bg-gray-50 text-gray-600',
      indigo: 'bg-indigo-50 text-indigo-600',
      emerald: 'bg-emerald-50 text-emerald-600',
      pink: 'bg-pink-50 text-pink-600',
      cyan: 'bg-cyan-50 text-cyan-600'
    }

    // Check if field has source information
    const hasSource = fieldName && criteria && hasSourceInfo(fieldName, criteria)
    const sourceInfo = fieldName && criteria ? getFieldSourceInfo(fieldName, criteria) : null

    return (
      <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
        <div className={`p-2 rounded-md ${colorClasses[colorClass as keyof typeof colorClasses]}`}>
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <div className="text-sm font-medium text-gray-500">{label}</div>
            {hasSource && sourceInfo && (
              <div className="flex items-center gap-2">
                <Badge 
                  variant={getConfidenceBadgeVariant(sourceInfo.averageConfidence)}
                  className={`text-xs ${getConfidenceColor(sourceInfo.averageConfidence)} px-2 py-0.5`}
                >
                  {Math.round((sourceInfo.averageConfidence || 0) * 100)}%
                </Badge>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-blue-50"
                    >
                      <Info className="h-3 w-3 text-blue-500 hover:text-blue-700" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Source Information: {label}</DialogTitle>
                      <DialogDescription>
                        Evidence and sources used to extract this information
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h4 className="font-medium text-blue-900 mb-2">Extracted Value</h4>
                        <p className="text-blue-800">
                          {Array.isArray(value) ? value.join(', ') : String(value)}
                        </p>
                      </div>
                      
                      <div className="space-y-3">
                        <h4 className="font-medium text-gray-900">Sources ({sourceInfo.sources.length})</h4>
                        {sourceInfo.sources.map((source: any, idx: number) => {
                          const typeInfo = getSourceTypeInfo(source.source_type)
                          return (
                            <div key={idx} className="border rounded-lg p-3 bg-gray-50">
                              <div className="flex items-center justify-between mb-2">
                                <Badge variant="outline" className={`text-xs ${typeInfo.color}`}>
                                  {typeInfo.displayName}
                                </Badge>
                                {source.confidence_score && (
                                  <Badge 
                                    variant={getConfidenceBadgeVariant(source.confidence_score)}
                                    className="text-xs"
                                  >
                                    {Math.round(source.confidence_score * 100)}%
                                  </Badge>
                                )}
                              </div>
                              
                              {source.evidence_quote && (
                                <div className="mb-2">
                                  <p className="text-sm text-gray-700 italic">"{source.evidence_quote}"</p>
                                </div>
                              )}
                              
                              {source.page_section && (
                                <p className="text-xs text-gray-500 mb-1">Section: {source.page_section}</p>
                              )}
                              
                              {source.url && (
                                <a 
                                  href={source.url} 
                                  target="_blank" 
                                  rel="noopener noreferrer" 
                                  className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                                >
                                  <ExternalLink className="h-3 w-3" />
                                  <span className="truncate">{source.url}</span>
                                </a>
                              )}
                              
                              {source.date_found && (
                                <p className="text-xs text-gray-400 mt-1">Found: {source.date_found}</p>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            )}
          </div>
          <div className="text-sm text-gray-900">
            {isLink && typeof value === 'string' && value.startsWith('http') ? (
              <a 
                href={value} 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-blue-600 hover:text-blue-800 break-all"
              >
                {value}
              </a>
            ) : (
              <span className="break-words">
                {Array.isArray(value) ? value.join(', ') : String(value)}
              </span>
            )}
          </div>
        </div>
      </div>
    )
  }


  // Render all sources in one centralized collapsible section
  const renderAllSources = (criteria: InvestmentCriteriaData) => {
    if (!criteria.sources) return null

    const sources = criteria.sources
    
    // Check for consolidated sources structure first
    if (sources.all_sources && Array.isArray(sources.all_sources) && sources.all_sources.length > 0) {
      // New evidence-grounded structure
      const allSources = sources.all_sources as Array<{
        field: string;
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
        confidence_score: number;
      }>

      if (allSources.length === 0) return null

      // Group sources by field for better organization
      const groupedSources = allSources.reduce((groups, source) => {
        const fieldName = source.field
        let category = 'General'
        
        // Define categories based on field names for investment criteria
        if (fieldName.includes('capital_position')) {
          category = 'Capital Position'
        } else if (fieldName.includes('deal_size') || fieldName.includes('minimum_deal') || fieldName.includes('maximum_deal')) {
          category = 'Deal Size'
        } else if (fieldName.includes('country') || fieldName.includes('region') || fieldName.includes('state') || fieldName.includes('city')) {
          category = 'Geographic Focus'
        } else if (fieldName.includes('property_type') || fieldName.includes('property_subcategory')) {
          category = 'Property Types'
        } else if (fieldName.includes('strategy') || fieldName.includes('decision_making')) {
          category = 'Investment Strategy'
        } else if (fieldName.includes('return') || fieldName.includes('hold_period') || fieldName.includes('historical')) {
          category = 'Return & Performance'
        } else if (fieldName.includes('loan') || fieldName.includes('interest_rate') || fieldName.includes('lending') || fieldName.includes('debt')) {
          category = 'Lending & Debt'
        } else if (fieldName.includes('financial_product') || fieldName.includes('capital_source')) {
          category = 'Financial Products'
        } else if (fieldName.includes('note')) {
          category = 'Additional Notes'
        }
        
        if (!groups[category]) {
          groups[category] = []
        }
        
        // Group by field name
        const existingField = groups[category].find(f => f.fieldName === fieldName)
        if (existingField) {
          existingField.sources.push(source)
        } else {
          groups[category].push({ 
            fieldName, 
            sources: [source],
            fieldKey: fieldName
          })
        }
        
        return groups
      }, {} as Record<string, Array<{ fieldKey: string; fieldName: string; sources: any[] }>>)

      return (
        <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-white">
          <CardHeader 
            className="cursor-pointer hover:bg-purple-50 transition-colors"
            onClick={() => setExpandedSections(prev => ({
              ...prev,
              'data-sources': !prev['data-sources']
            }))}
          >
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database className="h-5 w-5 text-purple-600" />
                Data Sources
                {!expandedSections['data-sources'] && (
                  <div className="flex items-center gap-1 ml-3">
                    <Badge className="bg-purple-100 text-purple-700 border border-purple-300 font-bold text-xs px-2 py-0.5">
                      {allSources.length}
                    </Badge>
                    <span className="text-xs text-purple-500">
                      sources across {Object.keys(groupedSources).length} categories
                    </span>
                  </div>
                )}
              </div>
              {expandedSections['data-sources'] ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
            </CardTitle>
          </CardHeader>
          {expandedSections['data-sources'] && (
            <CardContent>
            <div className="space-y-6">
              <Alert className="border-green-200 bg-green-50">
                <Info className="h-4 w-4 text-green-600" />
                  <AlertTitle className="text-green-800">Evidence-Grounded Data</AlertTitle>
                <AlertDescription className="text-green-700">
                    This data includes confidence scores and direct evidence quotes for each field.
                </AlertDescription>
              </Alert>

                {Object.entries(groupedSources).map(([category, fields]) => (
                  <div key={category} className="border border-purple-200 rounded-lg p-4 bg-purple-50/30">
                    <h3 className="text-lg font-semibold text-purple-900 mb-4 flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                      {category}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {fields.map(({ fieldKey, sources, fieldName }) => {
                        const displayName = fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                        
                        return (
                          <div key={fieldKey} className="bg-white border border-purple-100 rounded-lg p-3 shadow-sm">
                            <h4 className="text-sm font-medium text-purple-800 mb-2 flex items-center gap-1">
                              <div className="w-1 h-1 bg-purple-400 rounded-full"></div>
                              {displayName}
                            </h4>
                            <div className="space-y-2">
                              {sources.map((source, index) => (
                                <div key={index} className="text-xs text-gray-700 bg-gray-50 rounded px-2 py-1">
                                  <div className="flex items-start justify-between mb-1">
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xxs">
                                        {source.source_type || 'Unknown'}
                                      </Badge>
                                      {source.confidence_score && (
                                        <Badge variant="outline" className="text-xxs">
                                          {Math.round(source.confidence_score * 100)}%
                                        </Badge>
                                      )}
                </div>
                </div>
                                  
                                  {source.evidence_quote && (
                                    <div className="mb-1 text-gray-600 italic">
                                      "{source.evidence_quote}"
                </div>
                                  )}
                                  
                                  {source.page_section && (
                                    <div className="text-gray-500 text-xxs mb-1">
                                      Section: {source.page_section}
                </div>
                                  )}
                                  
                                  {source.url && (
                                    <div className="flex items-center gap-1">
                                      {source.url.startsWith('http') ? (
                                        <a 
                                          href={source.url} 
                                          target="_blank" 
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 break-all flex items-center gap-1 hover:bg-blue-50 rounded px-1 py-0.5 -mx-1 -my-0.5"
                                        >
                                          <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                          <span className="truncate">{source.url}</span>
                                        </a>
                                      ) : (
                                        <span className="flex items-center gap-1">
                                          <FileText className="h-3 w-3 text-gray-500 flex-shrink-0" />
                                          <span className="truncate">{source.url}</span>
                                        </span>
                                      )}
                                    </div>
                                  )}
                                  
                                  {source.date_found && (
                                    <div className="text-gray-400 text-xxs mt-1">
                                      Found: {source.date_found}
                                    </div>
                                  )}
                                </div>
                              ))}
                              </div>
                            </div>
                          )
                        })}
                      </div>
                  </div>
                ))}
            </div>
          </CardContent>
        )}
        </Card>
      )
    }
    
    // Check for new evidence-grounded structure
    const evidenceEntries = Object.entries(sources).filter(([key, value]) => {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        const evidenceValue = value as any
        return 'evidence' in evidenceValue && (evidenceValue.evidence || (evidenceValue.sources && evidenceValue.sources.length > 0))
      }
      return false
    })
    
    // Fallback to legacy structure
    const legacySourceEntries = Object.entries(sources).filter(([_, value]) => 
      Array.isArray(value) && value.length > 0
    ) as Array<[string, string[]]>

    if (evidenceEntries.length === 0 && legacySourceEntries.length === 0) return null

    return (
      <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-white">
        <CardHeader 
          className="cursor-pointer hover:bg-purple-50 transition-colors"
          onClick={() => setExpandedSections(prev => ({
            ...prev,
            'data-sources': !prev['data-sources']
          }))}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5 text-purple-600" />
              Data Sources
              {/* Show available fields chips when collapsed */}
              {!expandedSections['data-sources'] && (evidenceEntries.length > 0 || legacySourceEntries.length > 0) && (
                <div className="flex items-center gap-1 ml-3">
                  <Badge className="bg-purple-100 text-purple-700 border border-purple-300 font-bold text-xs px-2 py-0.5">
                    {evidenceEntries.length > 0 ? evidenceEntries.length : legacySourceEntries.length}
                  </Badge>
                  <span className="text-xs text-purple-500">
                    {evidenceEntries.length > 0 ? 'evidence fields' : 'sources'}
                  </span>
                </div>
              )}
            </div>
            {expandedSections['data-sources'] ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </CardTitle>
        </CardHeader>
        {expandedSections['data-sources'] && (
          <CardContent>
            {(() => {
              // Handle new evidence-grounded structure
              if (evidenceEntries.length > 0) {
                return (
                  <div className="space-y-6">
                    <Alert className="border-green-200 bg-green-50">
                      <Info className="h-4 w-4 text-green-600" />
                      <AlertTitle className="text-green-800">Evidence-Grounded Data</AlertTitle>
                      <AlertDescription className="text-green-700">
                        This data includes confidence scores and direct evidence quotes for each field.
                      </AlertDescription>
                    </Alert>
                    
                    {evidenceEntries.map(([fieldKey, evidenceData]) => {
                      const fieldName = fieldKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                      const data = evidenceData as any
                      
                      return (
                        <div key={fieldKey} className="bg-white border border-purple-100 rounded-lg p-4 shadow-sm">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-sm font-medium text-purple-800 flex items-center gap-2">
                              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                              {fieldName}
                            </h4>
                            {data.confidence_score !== undefined && data.confidence_score !== null && (
                              <Badge 
                                variant={getConfidenceBadgeVariant(data.confidence_score)}
                                className="text-xs"
                              >
                                {Math.round(data.confidence_score * 100)}% confidence
                              </Badge>
                            )}
                          </div>
                          
                          <div className="space-y-3">
                            {data.evidence && data.evidence !== null && (
                              <div className="bg-gray-50 rounded-lg p-3">
                                <h5 className="text-xs font-medium text-gray-600 mb-1">Primary Evidence:</h5>
                                <p className="text-sm text-gray-800 italic">"{data.evidence}"</p>
                              </div>
                            )}
                            
                            {data.source_url && data.source_url !== null && (
                              <div className="flex items-center gap-2">
                                <ExternalLink className="h-3 w-3 text-blue-500" />
                                <a 
                                  href={data.source_url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-xs text-blue-600 hover:text-blue-800 break-all"
                                >
                                  {data.source_url}
                                </a>
                              </div>
                            )}

                            {/* Multiple Sources Display */}
                            {data.sources && Array.isArray(data.sources) && data.sources.length > 0 && (
                              <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
                                <h5 className="text-xs font-medium text-blue-700 mb-2 flex items-center gap-1">
                                  <Database className="h-3 w-3" />
                                  Multiple Sources ({data.sources.length})
                                  <span className="text-xs text-blue-500 ml-1">
                                    (Ordered by priority: Company Website → Web Search → Others)
                                  </span>
                                </h5>
                                <div className="space-y-2">
                                  {data.sources
                                    .sort((a, b) => {
                                      const aInfo = getSourceTypeInfo(a.source_type)
                                      const bInfo = getSourceTypeInfo(b.source_type)
                                      return aInfo.priority - bInfo.priority
                                    })
                                    .map((source, sourceIndex) => {
                                      const sourceInfo = getSourceTypeInfo(source.source_type)
                                      return (
                                        <div key={sourceIndex} className="bg-white rounded p-2 border border-blue-100">
                                          <div className="flex items-start justify-between mb-1">
                                            <div className="flex items-center gap-1">
                                              <Badge 
                                                variant="outline" 
                                                className={`text-xs ${sourceInfo.color}`}
                                              >
                                                {sourceInfo.displayName}
                                              </Badge>
                                              <span className="text-xs text-gray-500">{source.date_found}</span>
                                              {sourceInfo.priority <= 3 && (
                                                <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                                                  Priority {sourceInfo.priority}
                                                </Badge>
                                              )}
                                            </div>
                                            <span className="text-xs text-gray-500">{source.page_section}</span>
                                          </div>
                                          <p className="text-xs text-gray-700 italic mb-1">"{source.evidence_quote}"</p>
                                          <a 
                                            href={source.url} 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="text-xs text-blue-600 hover:text-blue-800 break-all flex items-center gap-1"
                                          >
                                            <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                            <span className="truncate">{source.url}</span>
                                          </a>
                                        </div>
                                      )
                                    })}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )
              }
              
              // Handle legacy structure
              if (legacySourceEntries.length > 0) {
                const groupedSources = legacySourceEntries.reduce((groups, [fieldKey, fieldSources]) => {
                  const fieldName = fieldKey.replace('_sources', '')
                  let category = 'General'
                  
                  // Define categories based on field names
                  if (fieldName.includes('capital_position')) {
                    category = 'Capital Position'
                  } else if (fieldName.includes('deal_size') || fieldName.includes('minimum_deal') || fieldName.includes('maximum_deal')) {
                    category = 'Deal Size'
                  } else if (fieldName.includes('country') || fieldName.includes('region') || fieldName.includes('state') || fieldName.includes('city')) {
                    category = 'Geographic Focus'
                  } else if (fieldName.includes('property_type') || fieldName.includes('property_subcategory')) {
                    category = 'Property Types'
                  } else if (fieldName.includes('strategy') || fieldName.includes('decision_making')) {
                    category = 'Investment Strategy'
                  } else if (fieldName.includes('return') || fieldName.includes('hold_period') || fieldName.includes('historical')) {
                    category = 'Return & Performance'
                  } else if (fieldName.includes('loan') || fieldName.includes('interest_rate') || fieldName.includes('lending') || fieldName.includes('debt')) {
                    category = 'Lending & Debt'
                  } else if (fieldName.includes('financial_product') || fieldName.includes('capital_source')) {
                    category = 'Financial Products'
                  } else if (fieldName.includes('note')) {
                    category = 'Additional Notes'
                  }
                  
                  if (!groups[category]) {
                    groups[category] = []
                  }
                  groups[category].push({ fieldKey, fieldSources, fieldName })
                  return groups
                }, {} as Record<string, Array<{ fieldKey: string, fieldSources: string[], fieldName: string }>>)

                return (
                  <div className="space-y-6">
                    <Alert className="border-amber-200 bg-amber-50">
                      <Info className="h-4 w-4 text-amber-600" />
                      <AlertTitle className="text-amber-800">Legacy Source Data</AlertTitle>
                      <AlertDescription className="text-amber-700">
                        This data uses the legacy source format. Consider upgrading to evidence-grounded extraction.
                      </AlertDescription>
                    </Alert>
                    
                    {Object.entries(groupedSources).map(([category, fields]) => (
                      <div key={category} className="border border-purple-200 rounded-lg p-4 bg-purple-50/30">
                        <h3 className="text-lg font-semibold text-purple-900 mb-4 flex items-center gap-2">
                          <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                          {category}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {fields.map(({ fieldKey, fieldSources, fieldName }) => {
                            const displayName = fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                            
                            return (
                              <div key={fieldKey} className="bg-white border border-purple-100 rounded-lg p-3 shadow-sm">
                                <h4 className="text-sm font-medium text-purple-800 mb-2 flex items-center gap-1">
                                  <div className="w-1 h-1 bg-purple-400 rounded-full"></div>
                                  {displayName}
                                </h4>
                                <div className="space-y-1">
                                  {(fieldSources as string[]).map((source, index) => (
                                    <div key={index} className="text-xs text-gray-700 bg-gray-50 rounded px-2 py-1">
                                      {source.startsWith('http') ? (
                                        <a 
                                          href={source} 
                                          target="_blank" 
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 break-all flex items-center gap-1 hover:bg-blue-50 rounded px-1 py-0.5 -mx-1 -my-0.5"
                                        >
                                          <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                          <span className="truncate">{source}</span>
                                        </a>
                                      ) : (
                                        <span className="flex items-center gap-1">
                                          <FileText className="h-3 w-3 text-gray-500 flex-shrink-0" />
                                          <span className="truncate">{source}</span>
                                        </span>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                )
              }
              
              return null
            })()}
          </CardContent>
        )}
      </Card>
    )
  }

  // Render sources information for admin users
  const renderSourcesSection = (criteria: InvestmentCriteriaData) => {
    if (!isAdminMode || !criteria.sources) {
      return null
    }

    const sources = criteria.sources
    const allSources = [
      ...(sources.capital_position_sources || []),
      ...(sources.minimum_deal_size_sources || []),
      ...(sources.maximum_deal_size_sources || []),
      ...(sources.country_sources || []),
      ...(sources.region_sources || []),
      ...(sources.state_sources || []),
      ...(sources.city_sources || []),
      ...(sources.property_types_sources || []),
      ...(sources.property_subcategories_sources || []),
      ...(sources.strategies_sources || []),
      ...(sources.decision_making_process_sources || []),
      ...(sources.notes_sources || []),
      ...(sources.target_return_sources || []),
      ...(sources.min_hold_period_sources || []),
      ...(sources.max_hold_period_sources || []),
      ...(sources.financial_products_sources || []),
      ...(sources.historical_irr_sources || []),
      ...(sources.historical_em_sources || []),
      ...(sources.loan_program_sources || []),
      ...(sources.loan_type_sources || []),
      ...(sources.capital_source_sources || []),
      ...(sources.structured_loan_tranche_sources || []),
      ...(sources.min_loan_term_sources || []),
      ...(sources.max_loan_term_sources || []),
      ...(sources.interest_rate_sources || []),
      ...(sources.interest_rate_sofr_sources || []),
      ...(sources.interest_rate_wsj_sources || []),
      ...(sources.interest_rate_prime_sources || []),
      ...(sources.loan_to_value_max_sources || []),
      ...(sources.loan_to_value_min_sources || []),
      ...(sources.loan_to_cost_max_sources || []),
      ...(sources.loan_to_cost_min_sources || []),
      ...(sources.loan_origination_fee_max_sources || []),
      ...(sources.loan_origination_fee_min_sources || []),
      ...(sources.loan_exit_fee_max_sources || []),
      ...(sources.loan_exit_fee_min_sources || []),
      ...(sources.min_loan_dscr_sources || []),
      ...(sources.max_loan_dscr_sources || []),
      ...(sources.recourse_loan_sources || []),
      ...(sources.loan_type_normalized_sources || []),
      ...(sources.closing_time_weeks_sources || []),
      ...(sources.general_sources || [])
    ]

    // Remove duplicates
    const uniqueSources = Array.from(new Set(allSources)).filter(Boolean)

    if (uniqueSources.length === 0) {
      return null
    }

    const renderSourceCategory = (title: string, sourceList: string[] | undefined) => {
      if (!sourceList || sourceList.length === 0) return null
      
      return (
        <div className="space-y-2">
          <h4 className="text-xs font-semibold text-amber-800 uppercase tracking-wider">{title}</h4>
          <div className="space-y-1">
            {sourceList.map((source, index) => (
              <div key={index} className="flex items-start gap-2">
                <ExternalLink className="h-3 w-3 text-amber-600 mt-1 flex-shrink-0" />
                <span className="text-xs text-amber-700 break-all">{source}</span>
              </div>
            ))}
          </div>
        </div>
      )
    }

    return (
      <Card className="shadow-sm border-amber-200 bg-amber-50/70 mt-6">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3">
            <Shield className="h-5 w-5 text-amber-600" />
            <span className="text-amber-800">Sources (Admin Only)</span>
            <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
              Admin View
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="border-amber-200 bg-amber-100/50">
            <Info className="h-4 w-4 text-amber-600" />
            <AlertTitle className="text-amber-800">Source Information</AlertTitle>
            <AlertDescription className="text-amber-700">
              These sources were identified by the AI during data extraction. They represent where each piece of information was found.
            </AlertDescription>
          </Alert>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {renderSourceCategory('Capital Position', sources.capital_position_sources)}
            {renderSourceCategory('Minimum Deal Size', sources.minimum_deal_size_sources)}
            {renderSourceCategory('Maximum Deal Size', sources.maximum_deal_size_sources)}
            {renderSourceCategory('Country', sources.country_sources)}
            {renderSourceCategory('Region', sources.region_sources)}
            {renderSourceCategory('State', sources.state_sources)}
            {renderSourceCategory('City', sources.city_sources)}
            {renderSourceCategory('Property Types', sources.property_types_sources)}
            {renderSourceCategory('Property Subcategories', sources.property_subcategories_sources)}
            {renderSourceCategory('Strategies', sources.strategies_sources)}
            {renderSourceCategory('Decision Making Process', sources.decision_making_process_sources)}
            {renderSourceCategory('Notes', sources.notes_sources)}
            {renderSourceCategory('Target Return', sources.target_return_sources)}
            {renderSourceCategory('Min Hold Period', sources.min_hold_period_sources)}
            {renderSourceCategory('Max Hold Period', sources.max_hold_period_sources)}
            {renderSourceCategory('Financial Products', sources.financial_products_sources)}
            {renderSourceCategory('Historical IRR', sources.historical_irr_sources)}
            {renderSourceCategory('Historical EM', sources.historical_em_sources)}
            {renderSourceCategory('Loan Program', sources.loan_program_sources)}
            {renderSourceCategory('Loan Type', sources.loan_type_sources)}
            {renderSourceCategory('Capital Source', sources.capital_source_sources)}
            {renderSourceCategory('Structured Loan Tranche', sources.structured_loan_tranche_sources)}
            {renderSourceCategory('Min Loan Term', sources.min_loan_term_sources)}
            {renderSourceCategory('Max Loan Term', sources.max_loan_term_sources)}
            {renderSourceCategory('Interest Rate', sources.interest_rate_sources)}
            {renderSourceCategory('Interest Rate SOFR', sources.interest_rate_sofr_sources)}
            {renderSourceCategory('Interest Rate WSJ', sources.interest_rate_wsj_sources)}
            {renderSourceCategory('Interest Rate Prime', sources.interest_rate_prime_sources)}
            {renderSourceCategory('LTV Max', sources.loan_to_value_max_sources)}
            {renderSourceCategory('LTV Min', sources.loan_to_value_min_sources)}
            {renderSourceCategory('LTC Max', sources.loan_to_cost_max_sources)}
            {renderSourceCategory('LTC Min', sources.loan_to_cost_min_sources)}
            {renderSourceCategory('Origination Fee Max', sources.loan_origination_fee_max_sources)}
            {renderSourceCategory('Origination Fee Min', sources.loan_origination_fee_min_sources)}
            {renderSourceCategory('Exit Fee Max', sources.loan_exit_fee_max_sources)}
            {renderSourceCategory('Exit Fee Min', sources.loan_exit_fee_min_sources)}
            {renderSourceCategory('Min DSCR', sources.min_loan_dscr_sources)}
            {renderSourceCategory('Max DSCR', sources.max_loan_dscr_sources)}
            {renderSourceCategory('Recourse Loan', sources.recourse_loan_sources)}
            {renderSourceCategory('Loan Type Normalized', sources.loan_type_normalized_sources)}
            {renderSourceCategory('Closing Time Weeks', sources.closing_time_weeks_sources)}
            {renderSourceCategory('General', sources.general_sources)}
          </div>
          
          {uniqueSources.length > 0 && (
            <div className="pt-4 border-t border-amber-200">
              <h4 className="text-xs font-semibold text-amber-800 uppercase tracking-wider mb-3">
                All Sources ({uniqueSources.length})
              </h4>
              <div className="flex flex-wrap gap-2">
                {uniqueSources.map((source, index) => (
                  <Badge key={index} variant="outline" className="bg-amber-100 text-amber-800 border-amber-300 text-xs">
                    {source.length > 50 ? `${source.substring(0, 50)}...` : source}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  const renderCriteriaDetails = (criteria: InvestmentCriteriaData) => (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card className="shadow-sm border-0 bg-white/70">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Target className="h-5 w-5 text-purple-600" />
              <span>Basic Information</span>
              {/* Show evidence-grounded indicator if available */}
              {criteria.sources && Object.values(criteria.sources).some(source => {
                if (typeof source === 'object' && source !== null && !Array.isArray(source)) {
                  const evidenceSource = source as any
                  return 'confidence_score' in evidenceSource && evidenceSource.confidence_score !== undefined
                }
                return false
              }) && (
                <div className="flex items-center gap-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs">
                    <Shield className="h-3 w-3 mr-1" />
                    Evidence-Grounded
                  </Badge>
                  {(() => {
                    const evidenceEntries = Object.values(criteria.sources!).filter(source => {
                      if (typeof source === 'object' && source !== null && !Array.isArray(source)) {
                        const evidenceSource = source as any
                        return 'sources' in evidenceSource && evidenceSource.sources && evidenceSource.sources.length > 0
                      }
                      return false
                    })
                    const totalMultipleSources = evidenceEntries.reduce((total, source) => {
                      const evidenceSource = source as any
                      return total + evidenceSource.sources.length
                    }, 0)
                    
                    if (totalMultipleSources > 0) {
                      return (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                          <Database className="h-3 w-3 mr-1" />
                          {totalMultipleSources} Sources
                        </Badge>
                      )
                    }
                    return null
                  })()}
                </div>
              )}
            </div>
            {!isEditing && (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" onClick={() => handleEdit(criteria)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => handleDelete(criteria.investment_criteria_id)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {renderInfoField("Capital Position", criteria.capital_position, <Target className="h-5 w-5" />, 'purple', false, 'capital_position', criteria)}
            {renderInfoField("Deal Size Range", 
              criteria.minimum_deal_size && criteria.maximum_deal_size
                  ? `${formatCurrency(criteria.minimum_deal_size)} - ${formatCurrency(criteria.maximum_deal_size)}`
                  : criteria.minimum_deal_size
                  ? `${formatCurrency(criteria.minimum_deal_size)}+`
                  : criteria.maximum_deal_size
                  ? `Up to ${formatCurrency(criteria.maximum_deal_size)}`
                : 'Not specified',
              <DollarSign className="h-5 w-5" />, 'green', false, 'deal_size_range', criteria
            )}
          </div>
          
          {/* Creation and Update Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200">
            <div>
              <span className="text-sm font-medium text-gray-600">Created</span>
              <p className="text-sm mt-1 text-gray-700 flex items-center gap-2">
                <Clock className="h-3 w-3" />
                {formatDate(criteria.created_at)}
              </p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-600">Last Updated</span>
              <p className="text-sm mt-1 text-gray-700 flex items-center gap-2">
                <Clock className="h-3 w-3" />
                {formatDate(criteria.updated_at)}
              </p>
            </div>
          </div>
          
          {criteria.decision_making_process && (
            <div>
              <span className="text-sm font-medium">Decision Making Process</span>
              <p className="text-sm mt-1 bg-gray-50 p-3 rounded">{criteria.decision_making_process}</p>
            </div>
          )}
          
          {criteria.notes && (
            <div>
              <span className="text-sm font-medium">Notes</span>
              <p className="text-sm mt-1 bg-gray-50 p-3 rounded">{criteria.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Geographic Focus */}
      {(criteria.country || criteria.region || criteria.state || criteria.city) && (
        <Card className="shadow-sm border-0 bg-white/70">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-red-600" />
              <span>Geographic Focus</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {renderInfoField("Countries", criteria.country, <MapPin className="h-5 w-5" />, 'red', false, 'country', criteria)}
              {renderInfoField("Regions", criteria.region, <MapPin className="h-5 w-5" />, 'orange', false, 'region', criteria)}
              {renderInfoField("States", criteria.state, <MapPin className="h-5 w-5" />, 'blue', false, 'state', criteria)}
              {renderInfoField("Cities", criteria.city, <MapPin className="h-5 w-5" />, 'green', false, 'city', criteria)}
            </div>
          </CardContent>
        </Card>
      )}

            {/* Property & Investment Focus */}
      {(criteria.property_types || criteria.property_subcategories || criteria.strategies) && (
        <Card className="shadow-sm border-0 bg-white/70">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3">
              <Building2 className="h-5 w-5 text-orange-600" />
              <span>Property & Investment Focus</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {renderInfoField("Property Types", criteria.property_types, <Building2 className="h-5 w-5" />, 'orange', false, 'property_types', criteria)}
              {renderInfoField("Property Subcategories", criteria.property_subcategories, <Building2 className="h-5 w-5" />, 'purple', false, 'property_subcategories', criteria)}
              {renderInfoField("Investment Strategies", criteria.strategies, <Target className="h-5 w-5" />, 'green', false, 'strategies', criteria)}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Debt-specific Details */}
      {criteria.investment_criteria_debt_id && (
        <Card className="shadow-sm border-0 bg-blue-50/70">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3">
              <Calculator className="h-5 w-5 text-blue-600" />
              <span className="text-blue-800">Debt Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Loan Terms */}
            {(criteria.min_loan_term || criteria.max_loan_term) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Loan Terms</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {criteria.min_loan_term && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Minimum Term</span>
                      <p className="font-medium">{criteria.min_loan_term} months</p>
                    </div>
                  )}
                  {criteria.max_loan_term && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Maximum Term</span>
                      <p className="font-medium">{criteria.max_loan_term} months</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Interest Rates */}
            {(criteria.loan_interest_rate || criteria.loan_interest_rate_based_off_sofr || criteria.loan_interest_rate_based_off_wsj || criteria.loan_interest_rate_based_off_prime) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Interest Rates</span>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                  {criteria.loan_interest_rate && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Base Rate</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_interest_rate)}</p>
                    </div>
                  )}
                  {criteria.loan_interest_rate_based_off_sofr && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">SOFR Based</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_interest_rate_based_off_sofr)}</p>
                    </div>
                  )}
                  {criteria.loan_interest_rate_based_off_wsj && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">WSJ Based</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_interest_rate_based_off_wsj)}</p>
                    </div>
                  )}
                  {criteria.loan_interest_rate_based_off_prime && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Prime Based</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_interest_rate_based_off_prime)}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Loan Ratios */}
            {(criteria.loan_to_value_min || criteria.loan_to_value_max || criteria.loan_to_cost_min || criteria.loan_to_cost_max) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Loan Ratios</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {renderInfoField("Min LTV", criteria.loan_to_value_min ? formatPercentage(criteria.loan_to_value_min) : null, <Percent className="h-5 w-5" />, 'blue', false, 'loan_to_value_min', criteria)}
                  {renderInfoField("Max LTV", criteria.loan_to_value_max ? formatPercentage(criteria.loan_to_value_max) : null, <Percent className="h-5 w-5" />, 'blue', false, 'loan_to_value_max', criteria)}
                  {renderInfoField("Min LTC", criteria.loan_to_cost_min ? formatPercentage(criteria.loan_to_cost_min) : null, <Percent className="h-5 w-5" />, 'green', false, 'loan_to_cost_min', criteria)}
                  {renderInfoField("Max LTC", criteria.loan_to_cost_max ? formatPercentage(criteria.loan_to_cost_max) : null, <Percent className="h-5 w-5" />, 'green', false, 'loan_to_cost_max', criteria)}
                </div>
              </div>
            )}

            {/* DSCR */}
            {(criteria.min_loan_dscr || criteria.max_loan_dscr) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Debt Service Coverage Ratio</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {renderInfoField("Minimum DSCR", criteria.min_loan_dscr ? `${criteria.min_loan_dscr}x` : null, <Calculator className="h-5 w-5" />, 'blue', false, 'min_loan_dscr', criteria)}
                  {renderInfoField("Maximum DSCR", criteria.max_loan_dscr ? `${criteria.max_loan_dscr}x` : null, <Calculator className="h-5 w-5" />, 'blue', false, 'max_loan_dscr', criteria)}
                </div>
              </div>
            )}

            {/* Fees */}
            {(criteria.loan_origination_min_fee || criteria.loan_origination_max_fee || criteria.loan_exit_min_fee || criteria.loan_exit_max_fee) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Fees</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {renderInfoField("Min Origination Fee", criteria.loan_origination_min_fee ? formatPercentage(criteria.loan_origination_min_fee) : null, <DollarSign className="h-5 w-5" />, 'blue', false, 'loan_origination_fee_min', criteria)}
                  {renderInfoField("Max Origination Fee", criteria.loan_origination_max_fee ? formatPercentage(criteria.loan_origination_max_fee) : null, <DollarSign className="h-5 w-5" />, 'blue', false, 'loan_origination_fee_max', criteria)}
                  {renderInfoField("Min Exit Fee", criteria.loan_exit_min_fee ? formatPercentage(criteria.loan_exit_min_fee) : null, <DollarSign className="h-5 w-5" />, 'green', false, 'loan_exit_fee_min', criteria)}
                  {renderInfoField("Max Exit Fee", criteria.loan_exit_max_fee ? formatPercentage(criteria.loan_exit_max_fee) : null, <DollarSign className="h-5 w-5" />, 'green', false, 'loan_exit_fee_max', criteria)}
                </div>
              </div>
            )}

            {/* Additional Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {renderInfoField("Loan Type", criteria.loan_type, <Calculator className="h-5 w-5" />, 'blue', false, 'loan_type', criteria)}
              {renderInfoField("Loan Program", criteria.loan_program, <Calculator className="h-5 w-5" />, 'indigo', false, 'loan_program', criteria)}
              {renderInfoField("Structured Loan Tranche", criteria.structured_loan_tranche, <Calculator className="h-5 w-5" />, 'purple', false, 'structured_loan_tranche', criteria)}
              {renderInfoField("Recourse Loan", criteria.recourse_loan, <Calculator className="h-5 w-5" />, 'orange', false, 'recourse_loan', criteria)}
              {renderInfoField("Closing Time", criteria.closing_time ? `${criteria.closing_time} weeks` : null, <Clock className="h-5 w-5" />, 'green', false, 'closing_time', criteria)}
              {renderInfoField("Lien Position", criteria.lien_position, <Calculator className="h-5 w-5" />, 'red', false, 'lien_position', criteria)}
            </div>

            {/* Program Overview */}
            {criteria.debt_program_overview && (
              <div>
                <span className="text-sm font-medium text-blue-700">Program Overview</span>
                <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.debt_program_overview}</p>
              </div>
            )}

            {/* Notes */}
            {criteria.debt_notes && (
              <div>
                <span className="text-sm font-medium text-blue-700">Notes</span>
                <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.debt_notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Equity-specific Details */}
      {criteria.investment_criteria_equity_id && (
        <Card className="shadow-sm border-0 bg-green-50/70">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span className="text-green-800">Equity Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Performance Targets */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {criteria.target_return && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Target Return</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.target_return)}</p>
                </div>
              )}
              {criteria.minimum_internal_rate_of_return && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Minimum IRR</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.minimum_internal_rate_of_return)}</p>
                </div>
              )}
              {criteria.minimum_yield_on_cost && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Min Yield on Cost</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.minimum_yield_on_cost)}</p>
                </div>
              )}
              {criteria.minimum_equity_multiple && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Min Equity Multiple</span>
                  <p className="font-medium text-lg">{criteria.minimum_equity_multiple}x</p>
                </div>
              )}
              {criteria.target_cash_on_cash_min && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Target Cash on Cash</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.target_cash_on_cash_min)}</p>
                </div>
              )}
              {criteria.attachment_point && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Attachment Point</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.attachment_point)}</p>
                </div>
              )}
            </div>

            {/* Hold Periods */}
            {(criteria.min_hold_period_years || criteria.max_hold_period_years) && (
              <div>
                <span className="text-sm font-medium text-green-700">Hold Period</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {criteria.min_hold_period_years && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-green-600">Minimum</span>
                      <p className="font-medium">{criteria.min_hold_period_years} years</p>
                    </div>
                  )}
                  {criteria.max_hold_period_years && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-green-600">Maximum</span>
                      <p className="font-medium">{criteria.max_hold_period_years} years</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Additional Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {renderInfoField("Ownership Requirement", criteria.ownership_requirement, <Target className="h-5 w-5" />, 'green', false, 'ownership_requirement', criteria)}
              {renderInfoField("Max Leverage Tolerance", criteria.max_leverage_tolerance ? formatPercentage(criteria.max_leverage_tolerance) : null, <Percent className="h-5 w-5" />, 'green', false, 'max_leverage_tolerance', criteria)}
              {renderInfoField("Typical Closing Timeline", criteria.typical_closing_timeline_days ? `${criteria.typical_closing_timeline_days} days` : null, <Clock className="h-5 w-5" />, 'blue', false, 'typical_closing_timeline_days', criteria)}
              {renderInfoField("Proof of Funds Required", criteria.proof_of_funds_requirement !== undefined ? (criteria.proof_of_funds_requirement ? 'Yes' : 'No') : null, <Shield className="h-5 w-5" />, 'purple', false, 'proof_of_funds_requirement', criteria)}
            </div>

            {/* Program Overview */}
            {criteria.equity_program_overview && (
              <div>
                <span className="text-sm font-medium text-green-700">Program Overview</span>
                <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.equity_program_overview}</p>
              </div>
            )}

            {/* Notes */}
            {criteria.equity_notes && (
              <div>
                <span className="text-sm font-medium text-green-700">Notes</span>
                <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.equity_notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Centralized Sources Section */}
      {renderAllSources(criteria)}
    </div>
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600">Loading investment criteria...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-6">
            <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Investment Criteria
            </h3>
            <p className="text-red-500 mb-4">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (totalCriteria === 0) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">
              No Investment Criteria Found
            </h3>
            <p className="text-gray-500 max-w-md mx-auto">
              No investment criteria records have been found for this {entityType}.
            </p>
            <Button onClick={handleAddNew} className="mt-4">
              <Plus className="h-4 w-4 mr-2" />
              Add Investment Criteria
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Transform InvestmentCriteriaData to InvestmentCriteriaFormData format
  const transformToFormData = (criteria: InvestmentCriteriaData) => {
    const result = {
      // Central data
      capital_position: criteria.capital_position,
      minimum_deal_size: criteria.minimum_deal_size ? Number(criteria.minimum_deal_size) : undefined,
      maximum_deal_size: criteria.maximum_deal_size ? Number(criteria.maximum_deal_size) : undefined,
      country: criteria.country,
      region: criteria.region,
      state: criteria.state,
      city: criteria.city,
      property_types: criteria.property_types,
      property_subcategories: criteria.property_subcategories,
      strategies: criteria.strategies,
      decision_making_process: criteria.decision_making_process,
      notes: criteria.notes,
      
      // Debt fields - transform from API format to form format
      debt_notes: criteria.debt_notes,
      debt_closing_time: criteria.closing_time ? Number(criteria.closing_time) : undefined,
      debt_future_facilities: criteria.future_facilities,
      debt_eligible_borrower: criteria.eligible_borrower,
      debt_occupancy_requirements: criteria.occupancy_requirements,
      debt_lien_position: criteria.lien_position,
      debt_min_loan_dscr: criteria.min_loan_dscr ? Number(criteria.min_loan_dscr) : undefined,
      debt_max_loan_dscr: criteria.max_loan_dscr ? Number(criteria.max_loan_dscr) : undefined,
      debt_recourse_loan: criteria.recourse_loan,
      debt_loan_min_debt_yield: criteria.loan_min_debt_yield,
      debt_prepayment: criteria.prepayment,
      debt_yield_maintenance: criteria.yield_maintenance,
      debt_application_deposit: criteria.application_deposit ? Number(criteria.application_deposit) : undefined,
      debt_good_faith_deposit: criteria.good_faith_deposit ? Number(criteria.good_faith_deposit) : undefined,
      debt_loan_origination_max_fee: criteria.loan_origination_max_fee ? Number(criteria.loan_origination_max_fee) : undefined,
      debt_loan_origination_min_fee: criteria.loan_origination_min_fee ? Number(criteria.loan_origination_min_fee) : undefined,
      debt_loan_exit_min_fee: criteria.loan_exit_min_fee ? Number(criteria.loan_exit_min_fee) : undefined,
      debt_loan_exit_max_fee: criteria.loan_exit_max_fee ? Number(criteria.loan_exit_max_fee) : undefined,
      debt_loan_interest_rate: criteria.loan_interest_rate ? Number(criteria.loan_interest_rate) : undefined,
      debt_loan_interest_rate_based_off_sofr: criteria.loan_interest_rate_based_off_sofr ? Number(criteria.loan_interest_rate_based_off_sofr) : undefined,
      debt_loan_interest_rate_based_off_wsj: criteria.loan_interest_rate_based_off_wsj ? Number(criteria.loan_interest_rate_based_off_wsj) : undefined,
      debt_loan_interest_rate_based_off_prime: criteria.loan_interest_rate_based_off_prime ? Number(criteria.loan_interest_rate_based_off_prime) : undefined,
      debt_loan_interest_rate_based_off_3yt: criteria.loan_interest_rate_based_off_3yt ? Number(criteria.loan_interest_rate_based_off_3yt) : undefined,
      debt_loan_interest_rate_based_off_5yt: criteria.loan_interest_rate_based_off_5yt ? Number(criteria.loan_interest_rate_based_off_5yt) : undefined,
      debt_loan_interest_rate_based_off_10yt: criteria.loan_interest_rate_based_off_10yt ? Number(criteria.loan_interest_rate_based_off_10yt) : undefined,
      debt_loan_interest_rate_based_off_30yt: criteria.loan_interest_rate_based_off_30yt ? Number(criteria.loan_interest_rate_based_off_30yt) : undefined,
      debt_rate_lock: criteria.rate_lock,
      debt_rate_type: criteria.rate_type,
      debt_loan_to_value_max: criteria.loan_to_value_max ? Number(criteria.loan_to_value_max) : undefined,
      debt_loan_to_value_min: criteria.loan_to_value_min ? Number(criteria.loan_to_value_min) : undefined,
      debt_loan_to_cost_min: criteria.loan_to_cost_min ? Number(criteria.loan_to_cost_min) : undefined,
      debt_loan_to_cost_max: criteria.loan_to_cost_max ? Number(criteria.loan_to_cost_max) : undefined,
      debt_program_overview: criteria.debt_program_overview,
      loan_type: criteria.loan_type,
      debt_loan_type_normalized: criteria.loan_type_normalized,
      structured_loan_tranche: criteria.structured_loan_tranche,
      loan_program: criteria.loan_program,
      debt_min_loan_term: criteria.min_loan_term ? Number(criteria.min_loan_term) : undefined,
      debt_max_loan_term: criteria.max_loan_term ? Number(criteria.max_loan_term) : undefined,
      debt_amortization: criteria.amortization,
      
      // Equity fields - transform from API format to form format
      equity_target_return: criteria.target_return ? Number(criteria.target_return) : undefined,
      equity_minimum_internal_rate_of_return: criteria.minimum_internal_rate_of_return ? Number(criteria.minimum_internal_rate_of_return) : undefined,
      equity_minimum_yield_on_cost: criteria.minimum_yield_on_cost ? Number(criteria.minimum_yield_on_cost) : undefined,
      equity_minimum_equity_multiple: criteria.minimum_equity_multiple ? Number(criteria.minimum_equity_multiple) : undefined,
      equity_target_cash_on_cash_min: criteria.target_cash_on_cash_min ? Number(criteria.target_cash_on_cash_min) : undefined,
      equity_min_hold_period_years: criteria.min_hold_period_years ? Number(criteria.min_hold_period_years) : undefined,
      equity_max_hold_period_years: criteria.max_hold_period_years ? Number(criteria.max_hold_period_years) : undefined,
      equity_ownership_requirement: criteria.ownership_requirement,
      equity_attachment_point: criteria.attachment_point ? Number(criteria.attachment_point) : undefined,
      equity_max_leverage_tolerance: criteria.max_leverage_tolerance ? Number(criteria.max_leverage_tolerance) : undefined,
      equity_typical_closing_timeline_days: criteria.typical_closing_timeline_days ? Number(criteria.typical_closing_timeline_days) : undefined,
      equity_proof_of_funds_requirement: criteria.proof_of_funds_requirement,
      equity_notes: criteria.equity_notes,
      equity_program_overview: criteria.equity_program_overview,
      equity_occupancy_requirements: criteria.equity_occupancy_requirements,
      equity_yield_on_cost: criteria.yield_on_cost ? Number(criteria.yield_on_cost) : undefined,
      equity_target_return_irr_on_equity: criteria.target_return_irr_on_equity ? Number(criteria.target_return_irr_on_equity) : undefined,
      equity_equity_multiple: criteria.equity_multiple ? Number(criteria.equity_multiple) : undefined,
      equity_position_specific_irr: criteria.position_specific_irr ? Number(criteria.position_specific_irr) : undefined,
      equity_position_specific_equity_multiple: criteria.position_specific_equity_multiple ? Number(criteria.position_specific_equity_multiple) : undefined,
    };
    
    return result;
  }

  // Show the form when editing or adding new
  if (isEditing || isAddingNew) {
    return (
      <InvestmentCriteriaForm
        key={`${editingCriteria?.investment_criteria_id || 'new'}-${editingCriteria?.updated_at || Date.now()}`} // Force re-render when criteria changes
        initialData={editingCriteria ? transformToFormData(editingCriteria) : undefined}
        onSave={isAddingNew ? onSave! : handleSaveEdit}
        onCancel={isAddingNew ? onBack! : handleCancelEdit}
        isEditing={!!editingCriteria}
        isSaving={isSaving}
        showCompanySelection={false}
        entityType={entityType === 'deal' ? 'company' : entityType}
        entityId={entityId}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Target className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold">Investment Criteria Details</h2>
          
          {/* Date Information Tooltips */}
          <div className="flex items-center space-x-1">
            {/* Created At Tooltip */}
            <Tooltip
              content={
                <div className="text-center">
                  <div className="font-medium">Created</div>
                  <div className="text-xs opacity-90">
                    {(() => {
                      const allCriteria = Object.values(groupedCriteria).flat()
                      if (allCriteria.length === 0) return 'N/A'
                      
                      // Get the earliest created date
                      const earliestCreated = allCriteria.reduce((earliest, criteria) => {
                        const createdDate = new Date(criteria.created_at)
                        const earliestDate = new Date(earliest.created_at)
                        return createdDate < earliestDate ? criteria : earliest
                      })
                      
                      return formatDate(earliestCreated.created_at)
                    })()}
                  </div>
                </div>
              }
            >
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 w-8 p-0 hover:bg-blue-50"
              >
                <Calendar className="h-4 w-4 text-blue-600" />
              </Button>
            </Tooltip>
            
            {/* Updated At Tooltip */}
            <Tooltip
              content={
                <div className="text-center">
                  <div className="font-medium">Last Updated</div>
                  <div className="text-xs opacity-90">
                    {(() => {
                      const allCriteria = Object.values(groupedCriteria).flat()
                      if (allCriteria.length === 0) return 'N/A'
                      
                      // Get the most recent updated date
                      const mostRecentUpdated = allCriteria.reduce((latest, criteria) => {
                        const updatedDate = new Date(criteria.updated_at)
                        const latestDate = new Date(latest.updated_at)
                        return updatedDate > latestDate ? criteria : latest
                      })
                      
                      return formatDate(mostRecentUpdated.updated_at)
                    })()}
                  </div>
                </div>
              }
            >
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 w-8 p-0 hover:bg-green-50"
              >
                <CalendarDays className="h-4 w-4 text-green-600" />
              </Button>
            </Tooltip>
          </div>
        </div>
        
        {/* Admin Mode Toggle - Only show if admin permissions are available */}
        {(localStorage.getItem('admin_mode') === 'true' || process.env.NODE_ENV === 'development') && (
          <div className="flex items-center space-x-2">
            <Badge 
              variant={isAdminMode ? "default" : "outline"} 
              className={`cursor-pointer transition-colors ${
                isAdminMode 
                  ? 'bg-amber-500 hover:bg-amber-600 text-white' 
                  : 'hover:bg-amber-50 text-amber-700 border-amber-300'
              }`}
              onClick={() => setIsAdminMode(!isAdminMode)}
            >
              <Shield className="h-3 w-3 mr-1" />
              {isAdminMode ? 'Admin Mode: ON' : 'Admin Mode: OFF'}
            </Badge>
          </div>
        )}
      </div>

      {/* Criteria Details */}
      {Object.entries(groupedCriteria).map(([capitalPosition, criteriaList]) => (
        <div key={capitalPosition} className="space-y-4">
          <div className="flex items-center space-x-3">
            <Badge className={`${getCapitalPositionColor(capitalPosition)} text-lg px-4 py-2 font-semibold`}>
              {capitalPosition}
            </Badge>
            <span className="text-gray-600">
              {criteriaList.length} {criteriaList.length === 1 ? 'criterion' : 'criteria'}
            </span>
          </div>

          <div className="space-y-6">
            {criteriaList.map((criteria, index) => (
              <div key={criteria.investment_criteria_id} className="space-y-6">
                {index > 0 && <Separator className="my-8" />}
                {renderCriteriaDetails(criteria)}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}
