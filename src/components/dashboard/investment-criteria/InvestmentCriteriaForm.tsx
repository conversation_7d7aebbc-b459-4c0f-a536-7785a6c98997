'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Target, 
  Building2, 
  Calculator, 
  TrendingUp, 
  MapPin, 
  Loader2,
  Save,
  X,
  Copy,
  Edit,
  CheckCircle,
  ArrowLeft
} from "lucide-react"
import { toast } from "sonner"
import { ReactMultiSelect } from '@/components/ui/react-multi-select'
import NestedMappingSelector from "./NestedMappingSelector"

interface InvestmentCriteriaFormData {
  // Central data
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  
  // All debt fields (corrected field names)
  debt_notes?: string;
  debt_closing_time?: number;
  debt_future_facilities?: string;
  debt_eligible_borrower?: string;
  debt_occupancy_requirements?: string;
  debt_lien_position?: string;
  debt_min_loan_dscr?: number;
  debt_max_loan_dscr?: number;
  debt_recourse_loan?: string;
  debt_loan_min_debt_yield?: string;
  debt_prepayment?: string;
  debt_yield_maintenance?: string;
  debt_application_deposit?: number;
  debt_good_faith_deposit?: number;
  debt_loan_origination_max_fee?: number;
  debt_loan_origination_min_fee?: number;
  debt_loan_exit_min_fee?: number;
  debt_loan_exit_max_fee?: number;
  debt_loan_interest_rate?: number;
  debt_loan_interest_rate_based_off_sofr?: number;
  debt_loan_interest_rate_based_off_wsj?: number;
  debt_loan_interest_rate_based_off_prime?: number;
  debt_loan_interest_rate_based_off_3yt?: number;
  debt_loan_interest_rate_based_off_5yt?: number;
  debt_loan_interest_rate_based_off_10yt?: number;
  debt_loan_interest_rate_based_off_30yt?: number;
  debt_rate_lock?: string;
  debt_rate_type?: string;
  debt_loan_to_value_max?: number;
  debt_loan_to_value_min?: number;
  debt_loan_to_cost_min?: number;
  debt_loan_to_cost_max?: number;
  debt_program_overview?: string;
  loan_type?: string; // Fixed: was debt_loan_type
  debt_loan_type_normalized?: string;
  structured_loan_tranche?: string; // Fixed: was debt_structured_loan_tranche
  loan_program?: string; // Fixed: was debt_loan_program
  debt_min_loan_term?: number;
  debt_max_loan_term?: number;
  debt_amortization?: string;
  
  // All equity fields
  equity_target_return?: number;
  equity_minimum_internal_rate_of_return?: number;
  equity_minimum_yield_on_cost?: number;
  equity_minimum_equity_multiple?: number;
  equity_target_cash_on_cash_min?: number;
  equity_min_hold_period_years?: number;
  equity_max_hold_period_years?: number;
  equity_ownership_requirement?: string;
  equity_attachment_point?: number;
  equity_max_leverage_tolerance?: number;
  equity_typical_closing_timeline_days?: number;
  equity_proof_of_funds_requirement?: boolean;
  equity_notes?: string;
  equity_program_overview?: string;
  equity_occupancy_requirements?: string;
  equity_yield_on_cost?: number;
  equity_target_return_irr_on_equity?: number;
  equity_equity_multiple?: number;
  equity_position_specific_irr?: number;
  equity_position_specific_equity_multiple?: number;
}

interface MappingData {
  capitalPositions: string[];
  propertyTypes: string[];
  strategies: string[];
  loanPrograms: string[];
  recourseLoans: string[];
  structuredLoanTranches: string[];
  regions: string[];
  states: string[];
  loading: boolean;
  // Raw API data for NestedMappingSelector components
  capitalPositionRaw?: any;
  propertyTypeRaw?: any;
  subpropertyTypesRaw?: any;
  strategiesRaw?: any;
  loanProgramRaw?: any;
  recourseRaw?: any;
  tranchesRaw?: any;
  regionsRaw?: any;
  statesRaw?: any;
}

interface CompanyInvestmentCriteria {
  investment_criteria_id: number;
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  [key: string]: any; // For debt and equity fields
}

interface SelectedCompany {
  company_id: number;
  company_name: string;
  industry?: string;
  investment_criteria?: CompanyInvestmentCriteria[];
}

interface InvestmentCriteriaFormProps {
  initialData?: Partial<InvestmentCriteriaFormData>;
  onSave: (data: InvestmentCriteriaFormData, hasChangedCentralCriteria?: boolean) => Promise<void>;
  onChange?: (data: Partial<InvestmentCriteriaFormData>) => void; // New callback for real-time changes
  onCancel: () => void;
  isEditing?: boolean;
  isSaving?: boolean;
  selectedCompany?: SelectedCompany | null;
  showCompanySelection?: boolean;
  isEditingCopiedIC?: boolean; // New prop to identify add contact context
  entityType?: 'contact' | 'company';
  entityId?: string | number;
  hideHeader?: boolean; // New prop to hide header when used in batch mode
}

const DEBT_POSITIONS = ['Senior Debt', 'Stretch Senior', 'Mezzanine'];
const EQUITY_POSITIONS = ['Preferred Equity', 'Common Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP'];

export default function InvestmentCriteriaForm({ 
  initialData, 
  onSave, 
  onChange,
  onCancel, 
  isEditing = false,
  isSaving = false,
  selectedCompany = null,
  showCompanySelection = false,
  isEditingCopiedIC = false,
  entityType,
  entityId,
  hideHeader = false,
}: InvestmentCriteriaFormProps) {
  const [formData, setFormData] = useState<InvestmentCriteriaFormData>({
    capital_position: '',
    country: [],
    region: [],
    state: [],
    city: [],
    property_types: [],
    property_subcategories: [],
    strategies: [],
    ...initialData
  });

  const [mappingData, setMappingData] = useState<MappingData>({
    capitalPositions: [],
    propertyTypes: [],
    strategies: [],
    loanPrograms: [],
    recourseLoans: [],
    structuredLoanTranches: [],
    regions: [],
    states: [],
    loading: false, // Changed: UI will show immediately
    // Initialize raw API data fields
    capitalPositionRaw: null,
    propertyTypeRaw: null,
    subpropertyTypesRaw: null,
    strategiesRaw: null,
    loanProgramRaw: null,
    recourseRaw: null,
    tranchesRaw: null,
    regionsRaw: null,
    statesRaw: null
  });

  // State for company IC selection
  const [selectedCompanyCriteria, setSelectedCompanyCriteria] = useState<CompanyInvestmentCriteria[]>([]);
  const [editingCompanyCriteria, setEditingCompanyCriteria] = useState<CompanyInvestmentCriteria | null>(null);

  // Track changes to central criteria fields
  const [initialCentralData, setInitialCentralData] = useState<Partial<InvestmentCriteriaFormData>>({});
  const [hasChangedCentralCriteria, setHasChangedCentralCriteria] = useState(false);

  useEffect(() => {
    fetchMappingData();
  }, []);

  useEffect(() => {
    if (initialData) {
      // Always update form data from initialData when it's provided
      // This ensures the form loads the correct data for both new and editing modes
      setFormData(prev => ({ ...prev, ...initialData }));
      
      // Store initial central criteria data for change tracking
      const centralFields = {
        capital_position: initialData.capital_position,
        minimum_deal_size: initialData.minimum_deal_size,
        maximum_deal_size: initialData.maximum_deal_size,
        country: initialData.country,
        region: initialData.region,
        state: initialData.state,
        city: initialData.city,
        property_types: initialData.property_types,
        property_subcategories: initialData.property_subcategories,
        strategies: initialData.strategies,
        decision_making_process: initialData.decision_making_process,
        notes: initialData.notes
      };
      setInitialCentralData(centralFields);
    }
  }, [initialData]);

  useEffect(() => {
    if (selectedCompany?.investment_criteria) {
      setSelectedCompanyCriteria([]);
    }
  }, [selectedCompany]);



  const fetchMappingData = async () => {
    try {
      // Don't block the UI - this runs in background
      setMappingData(prev => ({ ...prev, loading: true }));
      
      const [
        capitalPositionRes,
        propertyTypeRes,
        strategiesRes,
        loanProgramRes,
        recourseRes,
        tranchesRes,
        regionsRes,
        statesRes
      ] = await Promise.all([
        fetch('/api/mapping-tables/types?type=Capital Position'),
        fetch('/api/mapping-tables/types?type=Property Type'),
        fetch('/api/mapping-tables/types?type=Strategies'),
        fetch('/api/mapping-tables/types?type=Loan Program'),
        fetch('/api/mapping-tables/types?type=Recourse Loan'),
        fetch('/api/mapping-tables/types?type=Structured Loan Tranches'),
        fetch('/api/mapping-tables/types?type=U.S Regions'),
        fetch('/api/mapping-tables/types?type=U.S States')
      ]);

      const [
        capitalPositionData,
        propertyTypeData,
        strategiesData,
        loanProgramData,
        recourseData,
        tranchesData,
        regionsData,
        statesData
      ] = await Promise.all([
        capitalPositionRes.json(),
        propertyTypeRes.json(),
        strategiesRes.json(),
        loanProgramRes.json(),
        recourseRes.json(),
        tranchesRes.json(),
        regionsRes.json(),
        statesRes.json()
      ]);

      // Process data using the new nested data structure
      const capitalPositions: string[] = capitalPositionData.success && capitalPositionData.data.nestedData
        ? Object.keys(capitalPositionData.data.nestedData).sort()
        : [];

      const propertyTypes: string[] = propertyTypeData.success && propertyTypeData.data.nestedData
        ? Object.keys(propertyTypeData.data.nestedData).sort()
        : [];

      const strategies: string[] = strategiesData.success && strategiesData.data.nestedData
        ? Object.keys(strategiesData.data.nestedData).sort()
        : [];

      const loanPrograms: string[] = loanProgramData.success && loanProgramData.data.nestedData
        ? Object.keys(loanProgramData.data.nestedData).sort()
        : [];

      const recourseLoans: string[] = recourseData.success && recourseData.data.nestedData
        ? Object.keys(recourseData.data.nestedData).sort()
        : [];

      const structuredLoanTranches: string[] = tranchesData.success && tranchesData.data.nestedData
        ? Object.keys(tranchesData.data.nestedData).sort()
        : [];

      const regions: string[] = regionsData.success && regionsData.data.nestedData
        ? Object.keys(regionsData.data.nestedData).sort()
        : [];

      // Extract states from U.S States mapping
      const states: string[] = statesData.success && statesData.data.hierarchyRows
        ? statesData.data.hierarchyRows.map((row: any) => row.values[0]).sort()
        : [];



      setMappingData({
        capitalPositions: capitalPositions.sort(),
        propertyTypes,
        strategies: strategies.sort(),
        loanPrograms: loanPrograms.sort(),
        recourseLoans: recourseLoans.sort(),
        structuredLoanTranches: structuredLoanTranches.sort(),
        regions: regions.sort(),
        states: states.sort(),
        loading: false,
        
        // Store raw API data for NestedMappingSelector components (now with nestedData)
        capitalPositionRaw: capitalPositionData.success ? capitalPositionData.data : null,
        propertyTypeRaw: propertyTypeData.success ? propertyTypeData.data : null,
        subpropertyTypesRaw: propertyTypeData.success ? propertyTypeData.data : null, // Use same data for subcategories
        strategiesRaw: strategiesData.success ? strategiesData.data : null,
        loanProgramRaw: loanProgramData.success ? loanProgramData.data : null,
        recourseRaw: recourseData.success ? recourseData.data : null,
        tranchesRaw: tranchesData.success ? tranchesData.data : null,
        regionsRaw: regionsData.success ? regionsData.data : null,
        statesRaw: statesData.success ? statesData.data : null // Use U.S States data for states
      });

    } catch (error) {
      console.error('Error fetching mapping data:', error);
      setMappingData(prev => ({ ...prev, loading: false }));
      toast.error('Failed to load mapping data');
    }
  };

  // Central criteria fields that trigger contact reprocessing when changed
  const centralCriteriaFields = [
    'capital_position', 'minimum_deal_size', 'maximum_deal_size', 'country', 'region', 
    'state', 'city', 'property_types', 'property_subcategories', 'strategies', 
    'decision_making_process', 'notes'
  ];

  // Function to get all unique states for selected regions
  const getAllStatesForRegions = (selectedRegions: string[]): string[] => {
    if (!mappingData.regionsRaw?.nestedData || selectedRegions.length === 0) {
      return [];
    }

    const allStates = new Set<string>();
    
    selectedRegions.forEach(region => {
      if (mappingData.regionsRaw?.nestedData[region]?.states) {
        // Access the states array directly from nestedData[region].states
        mappingData.regionsRaw.nestedData[region].states.forEach((state: string) => {
          if (state && state.trim()) {
            allStates.add(state.trim());
          }
        });
      }
    });

    return Array.from(allStates).sort();
  };

  // Function to get all unique subcategories for selected property types
  const getAllSubcategoriesForPropertyTypes = (selectedPropertyTypes: string[]): string[] => {
    if (!mappingData.propertyTypeRaw?.nestedData || selectedPropertyTypes.length === 0) {
      return [];
    }

    const allSubcategories = new Set<string>();
    
    selectedPropertyTypes.forEach(propertyType => {
      if (mappingData.propertyTypeRaw?.nestedData[propertyType]) {
        // The subcategories are the keys of the propertyTypeMap[propertyType] object
        Object.keys(mappingData.propertyTypeRaw.nestedData[propertyType]).forEach((subcategory: string) => {
          if (subcategory && subcategory.trim()) {
            allSubcategories.add(subcategory.trim());
          }
        });
      }
    });

    return Array.from(allSubcategories).sort();
  };

  // Function to get filtered states based on selected regions
  const getFilteredStates = useMemo(() => {
    if (!mappingData.regionsRaw || !formData.region || formData.region.length === 0) {
      // No regions selected, return all states from U.S States mapping
      if (mappingData.statesRaw?.hierarchyRows) {
        return mappingData.statesRaw.hierarchyRows.map((row: any) => ({
          value: row.values[0],
          label: row.values[0]
        }));
      }
      return [];
    }

    // Regions selected, filter states based on selected regions
    const regionStateMap = mappingData.regionsRaw?.nestedData || {};
    const allStates = new Set<string>();
    
    formData.region.forEach((region: string) => {
      if (regionStateMap[region]?.states) {
        regionStateMap[region].states.forEach((state: string) => {
          if (state && state.trim()) {
            allStates.add(state.trim());
          }
        });
      }
    });

    return Array.from(allStates).map(state => ({
      value: state,
      label: state
    }));
  }, [mappingData.regionsRaw, mappingData.statesRaw, formData.region]);

  // Function to get filtered subcategories based on selected property types
  const getFilteredSubcategories = useMemo(() => {
    if (!mappingData.propertyTypeRaw || !formData.property_types || formData.property_types.length === 0) {
      // No property types selected, return all subcategories from Property Type mapping
      if (mappingData.subpropertyTypesRaw?.hierarchyRows) {
        return mappingData.subpropertyTypesRaw.hierarchyRows.map((row: any) => ({
          value: row.values[0],
          label: row.values[0]
        }));
      }
      return [];
    }

    // Property types selected, filter subcategories based on selected property types
    const propertyTypeMap = mappingData.propertyTypeRaw?.nestedData || {};
    const allSubcategories = new Set<string>();
    
    formData.property_types.forEach((propertyType: string) => {
      if (propertyTypeMap[propertyType]) {
        // The subcategories are the keys of the propertyTypeMap[propertyType] object
        Object.keys(propertyTypeMap[propertyType]).forEach((subcategory: string) => {
          if (subcategory && subcategory.trim()) {
            allSubcategories.add(subcategory.trim());
          }
        });
      }
    });

    return Array.from(allSubcategories).map(subcategory => ({
      value: subcategory,
      label: subcategory
    }));
  }, [mappingData.propertyTypeRaw, mappingData.subpropertyTypesRaw, formData.property_types]);

  const handleInputChange = (field: keyof InvestmentCriteriaFormData, value: any) => {
    const newData = {
      ...formData,
      [field]: value
    };
    
    // Handle region changes and auto-manage related states
    if (field === 'region' && Array.isArray(value)) {
      const currentStates = newData.state || [];
      
      if (value.length > 0) {
        // When regions are selected, get all states for selected regions
        const allRelatedStates = getAllStatesForRegions(value);
        
        // Filter out states that don't belong to any selected region
        const validStates = currentStates.filter(state => {
          // Check if this state belongs to any of the selected regions
          return allRelatedStates.includes(state);
        });
        
        // Add any new states from the selected regions
        const mergedStates = Array.from(new Set([...validStates, ...allRelatedStates]));
        newData.state = mergedStates;
        
        console.log('🔍 Managing states for regions:', {
          selectedRegions: value,
          allRelatedStates,
          currentStates,
          validStates,
          mergedStates
        });
      } else {
        // When no regions are selected, clear all states
        newData.state = [];
        
        console.log('🔍 No regions selected, clearing all states');
      }
    }

    // Handle property type changes and auto-manage related subcategories
    if (field === 'property_types' && Array.isArray(value)) {
      const currentSubcategories = newData.property_subcategories || [];
      
      if (value.length > 0) {
        // When property types are selected, get all subcategories for selected property types
        const allRelatedSubcategories = getAllSubcategoriesForPropertyTypes(value);
        
        // Filter out subcategories that don't belong to any selected property type
        const validSubcategories = currentSubcategories.filter(subcategory => {
          // Check if this subcategory belongs to any of the selected property types
          return allRelatedSubcategories.includes(subcategory);
        });
        
        // Add any new subcategories from the selected property types
        const mergedSubcategories = Array.from(new Set([...validSubcategories, ...allRelatedSubcategories]));
        newData.property_subcategories = mergedSubcategories;
        
        console.log('🔍 Managing subcategories for property types:', {
          selectedPropertyTypes: value,
          allRelatedSubcategories,
          currentSubcategories,
          validSubcategories,
          mergedSubcategories
        });
      } else {
        // When no property types are selected, clear all subcategories
        newData.property_subcategories = [];
        
        console.log('🔍 No property types selected, clearing all subcategories');
      }
    }
    
    setFormData(newData);

    // Call onChange callback to update parent state immediately
    if (onChange) {
      onChange(newData);
    }

    // Check if this is a central criteria field and if it changed
    if (centralCriteriaFields.includes(field) && isEditing) {
      const initialValue = initialCentralData[field as keyof typeof initialCentralData];
      const hasChanged = JSON.stringify(initialValue) !== JSON.stringify(value);
      
      if (hasChanged && !hasChangedCentralCriteria) {
        setHasChangedCentralCriteria(true);
      }
    }
  };

  const handleCopyCompanyCriteria = (criteria: CompanyInvestmentCriteria) => {
    const copiedData = {
      capital_position: criteria.capital_position,
      minimum_deal_size: criteria.minimum_deal_size,
      maximum_deal_size: criteria.maximum_deal_size,
      country: criteria.country || [],
      region: criteria.region || [],
      state: criteria.state || [],
      city: criteria.city || [],
      property_types: criteria.property_types || [],
      property_subcategories: criteria.property_subcategories || [],
      strategies: criteria.strategies || [],
      decision_making_process: criteria.decision_making_process,
      notes: criteria.notes,
      // Copy debt fields if they exist
      ...(criteria.loan_type && { loan_type: criteria.loan_type }),
      ...(criteria.loan_program && { loan_program: criteria.loan_program }),
      ...(criteria.structured_loan_tranche && { structured_loan_tranche: criteria.structured_loan_tranche }),
      // Copy other relevant fields
      ...Object.keys(criteria).reduce((acc, key) => {
        if (key.startsWith('debt_') || key.startsWith('equity_')) {
          acc[key] = criteria[key];
        }
        return acc;
      }, {} as any)
    };
    
    setFormData(prev => ({ ...prev, ...copiedData }));
    toast.success('Investment criteria copied from company');
  };

  const handleSelectCompanyCriteria = (criteria: CompanyInvestmentCriteria) => {
    if (selectedCompanyCriteria.find(c => c.investment_criteria_id === criteria.investment_criteria_id)) {
      setSelectedCompanyCriteria(prev => 
        prev.filter(c => c.investment_criteria_id !== criteria.investment_criteria_id)
      );
    } else {
      setSelectedCompanyCriteria(prev => [...prev, criteria]);
    }
  };

  const handleEditCompanyCriteria = (criteria: CompanyInvestmentCriteria) => {
    setEditingCompanyCriteria({ ...criteria });
  };



  const handleSave = async () => {
    if (!formData.capital_position) {
      toast.error('Please select a capital position');
      return;
    }

    try {
      await onSave(formData, hasChangedCentralCriteria);
    } catch (error) {
      console.error('Error saving investment criteria:', error);
      toast.error('Failed to save investment criteria');
    }
  };

  const handleAddNewOption = async (mappingType: string, newValue: string) => {
    try {
      // Here you would typically save the new option to the database
      // For now, we'll just show a success message
      toast.success(`Added new ${mappingType.toLowerCase()}: ${newValue}`);
      
      // Optionally, you could refresh the mapping data here
      // await fetchMappingData();
    } catch (error) {
      console.error('Error adding new option:', error);
      toast.error('Failed to add new option');
    }
  };

  const isDebtPosition = DEBT_POSITIONS.includes(formData.capital_position);
  const isEquityPosition = EQUITY_POSITIONS.includes(formData.capital_position);

  const getCapitalPositionColor = (position: string) => {
    if (DEBT_POSITIONS.includes(position)) {
      return 'bg-blue-50 text-blue-700 border-blue-200';
    } else if (EQUITY_POSITIONS.includes(position)) {
      return 'bg-green-50 text-green-700 border-green-200';
    }
    return 'bg-gray-50 text-gray-700 border-gray-200';
  };

  // Modern two-column layout component
  const renderCompanyICSection = () => {
    if (!showCompanySelection || !selectedCompany) return null;

    return (
      <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden h-fit">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-xl">
              <Building2 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="text-lg font-semibold text-slate-900">Company Investment Criteria</div>
              <div className="text-sm text-slate-600">{selectedCompany.company_name}</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4 max-h-[70vh] overflow-y-auto">
          {selectedCompany.investment_criteria && selectedCompany.investment_criteria.length > 0 ? (
            <div className="space-y-3">
              {selectedCompany.investment_criteria.map((criteria) => (
                <div key={criteria.investment_criteria_id} className="border rounded-lg p-4 bg-gradient-to-br from-white to-slate-50">
                  <div className="flex items-center justify-between mb-3">
                    <Badge className={getCapitalPositionColor(criteria.capital_position)}>
                      {criteria.capital_position}
                    </Badge>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyCompanyCriteria(criteria)}
                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        Copy
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditCompanyCriteria(criteria)}
                        className="text-green-600 hover:text-green-700 hover:bg-green-50"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    {criteria.minimum_deal_size && (
                      <div>Min Deal Size: ${criteria.minimum_deal_size.toLocaleString()}</div>
                    )}
                    {criteria.maximum_deal_size && (
                      <div>Max Deal Size: ${criteria.maximum_deal_size.toLocaleString()}</div>
                    )}
                    {criteria.property_types && criteria.property_types.length > 0 && (
                      <div>Property Types: {criteria.property_types.slice(0, 3).join(', ')}{criteria.property_types.length > 3 ? '...' : ''}</div>
                    )}
                    {criteria.strategies && criteria.strategies.length > 0 && (
                      <div>Strategies: {criteria.strategies.slice(0, 2).join(', ')}{criteria.strategies.length > 2 ? '...' : ''}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-slate-500">
              <Building2 className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No investment criteria found for this company</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className={`w-full ${hideHeader ? 'p-0' : 'p-6'} ${hideHeader ? 'space-y-0' : 'space-y-8'}`}>
        {/* Modern Header Section - Conditionally rendered */}
        {!hideHeader && (
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-xl">
                    <Target className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                      {isEditing ? 'Edit Investment Criteria' : 'Add Investment Criteria'}
                    </h1>
                    <div className="text-slate-600 mt-1 flex items-center gap-2">
                      {formData.capital_position ? (
                        <>
                          Create criteria for 
                          <Badge className={getCapitalPositionColor(formData.capital_position)}>
                            {formData.capital_position}
                          </Badge>
                        </>
                      ) : (
                        'Configure investment parameters and preferences'
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Top Action Buttons */}
              <div className="flex items-center space-x-3">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={onCancel}
                  disabled={isSaving}
                  className="h-11 px-6 rounded-xl border-2 border-slate-200 hover:bg-slate-50 transition-all duration-200 font-medium"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleSave}
                  disabled={isSaving}
                  className="h-11 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Save Criteria
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Main Content - Two Column Layout */}
        <div className="w-full space-y-8 transition-all duration-300">
                      {/* Investment Criteria Form (Full Width) */}
            <div className="space-y-6 w-full">
            
            {/* CENTRAL TABLE - Investment Criteria Central */}
            <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b border-purple-100">
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-xl">
                    <Target className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-slate-900">Central Investment Criteria</div>
                    <div className="text-sm text-slate-600">Core investment parameters and preferences</div>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-6">
                {/* Capital Position */}
                <div>
                  <Label className="text-sm font-medium text-purple-700">Capital Position *</Label>
                  <NestedMappingSelector
                    mappingType="Capital Position"
                    selectedValues={formData.capital_position ? [formData.capital_position] : []}
                    onSelectionChange={(values) => {
                      handleInputChange('capital_position', values[0] || '');
                    }}
                    label=""
                    placeholder="Select capital position..."
                    showSelectAll={false}
                    mappingData={mappingData.capitalPositionRaw}
                    allowNewOptions={true}
                    onAddNewOption={(newValue) => handleAddNewOption('Capital Position', newValue)}
                  />
                </div>

                {/* Deal Size */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Minimum Deal Size ($)</Label>
                    <Input
                      type="number"
                      className="mt-1"
                      value={formData.minimum_deal_size || ''}
                      onChange={(e) => handleInputChange('minimum_deal_size', e.target.value ? parseFloat(e.target.value) : undefined)}
                      placeholder="Enter minimum deal size"
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Maximum Deal Size ($)</Label>
                    <Input
                      type="number"
                      className="mt-1"
                      value={formData.maximum_deal_size || ''}
                      onChange={(e) => handleInputChange('maximum_deal_size', e.target.value ? parseFloat(e.target.value) : undefined)}
                      placeholder="Enter maximum deal size"
                    />
                  </div>
                </div>

                {/* Geographic Focus */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium text-gray-700">Geographic Focus</Label>
                  
                  {/* Countries */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-gray-600">Countries</Label>
                    <ReactMultiSelect
                      options={[
                        'Asia Pacific', 'Australia', 'Austria', 'Belgium', 'Brazil', 'Canada', 'China', 'Denmark',
                        'Estonia', 'Europe', 'Finland', 'France', 'Germany', 'Global', 'India', 'Indonesia',
                        'Ireland', 'Israel', 'Italy', 'Japan', 'Korea', 'Latvia', 'Lithuania', 'Mexico',
                        'Netherlands', 'New Zealand', 'Norway', 'Other International', 'Singapore', 'South Korea',
                        'Spain', 'Sweden', 'Switzerland', 'United Kingdom', 'United States', 'United States of America',
                        'USA', 'Vietnam'
                      ].map(country => ({ value: country, label: country }))}
                      selected={formData.country || []}
                      onChange={(values) => {
                        handleInputChange('country', values);
                      }}
                      placeholder="Select countries or type to add new..."
                      showSelectAll={true}
                      selectAllLabel="Select All Countries"
                      allowCustomValues={true}
                      onCustomValueAdd={(value) => {
                        toast.success(`Added custom country: ${value}`);
                      }}
                    />
                  </div>

                  {/* U.S. Regions */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-gray-600">U.S. Regions</Label>
                    <NestedMappingSelector
                      mappingType="U.S Regions"
                      selectedValues={formData.region || []}
                      onSelectionChange={(values) => {
                        handleInputChange('region', values);
                      }}
                      label=""
                      placeholder="Select regions or type to add new..."
                      showSelectAll={true}
                      selectAllLabel="Select All Regions"
                      mappingData={mappingData.regionsRaw}
                      allowNewOptions={true}
                      onAddNewOption={(newValue) => handleAddNewOption('Region', newValue)}
                    />
                  </div>

                  {/* U.S. States - Filtered by Regions */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-gray-600">U.S. States</Label>
                    <ReactMultiSelect
                      options={getFilteredStates}
                      selected={formData.state || []}
                      onChange={(values) => {
                        handleInputChange('state', values);
                      }}
                      placeholder={formData.region && formData.region.length > 0 
                        ? `Select states for selected regions...` 
                        : "Select states..."}
                      showSelectAll={true}
                      selectAllLabel="Select All States"
                      allowCustomValues={true}
                      onCustomValueAdd={(value) => {
                        toast.success(`Added custom state: ${value}`);
                      }}
                    />
                  </div>

                  {/* Cities */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-gray-600">Cities</Label>
                    <ReactMultiSelect
                      options={[
                        // Major US Cities
                        'Albany', 'Arlington, VA', 'Aspen', 'Atlanta', 'Aurora', 'Austin', 'Baltimore',
                        'Bellevue', 'Berkeley, CA', 'Beverly Hills', 'Birmingham', 'Bismarck', 'Boca Raton',
                        'Bonita Springs', 'Boston', 'Bozeman', 'Brentwood', 'Bronx', 'Brooklyn', 'Cambridge',
                        'Chapel Hill', 'Charleston', 'Charlotte', 'Chicago', 'College Park', 'Columbia',
                        'Dallas', 'Dallas-Fort Worth', 'Delray Beach', 'Denver', 'Doral', 'Eagle Pass',
                        'Eden Prairie', 'Edgewater', 'El Monte', 'Emeryville', 'Evanston', 'Fayetteville',
                        'Fisher Island', 'Fort Lauderdale', 'Fort Mill', 'Fort Wayne', 'Frederick', 'Frisco',
                        'Ft. Lauderdale', 'Gainesville', 'Gastonia', 'Goose Creek', 'Grapevine', 'Greenwich',
                        'Hackensack', 'Hamilton Township', 'Herndon', 'Hoboken', 'Hollywood', 'Houston',
                        'Indianapolis', 'Jacksonville', 'Jamaica', 'Jersey City', 'Kissimmee', 'Las Vegas',
                        'Laurel', 'Lawrence', 'Levittown', 'Lombard', 'Long Branch', 'Long Island City',
                        'Los Angeles', 'Macon', 'Manhattan', 'Maple Grove', 'Meriden', 'Metro DC', 'Miami',
                        'Miami Beach', 'Middletown', 'Nashville', 'New Canaan, CT', 'New Orleans', 'New York',
                        'New York City', 'North Bergen', 'North Bethesda', 'North Miami Beach', 'Ocala',
                        'Oklahoma City', 'Orange', 'Orlando', 'Overland Park', 'Palm Beach Gardens',
                        'Palm Springs', 'Panama City', 'Paradise Valley', 'Pendergrass', 'Philadelphia',
                        'Phoenix', 'Plantation', 'Pompano Beach', 'Portland', 'Port Orange', 'Port St. Lucie',
                        'Providence', 'Queens', 'Quincy', 'Raleigh', 'Raleigh-Durham', 'Redwood City',
                        'Rochester', 'Rolling Meadows', 'Rye Brook', 'Sag Harbor', 'San Angelo', 'San Antonio',
                        'San Bruno', 'San Diego', 'San Francisco', 'San Jose', 'Savannah', 'Scottsdale',
                        'Seattle', 'Seattle, WA', 'Silver Spring', 'Stamford', 'Staten Island', 'St. Charles',
                        'St. Petersburg', 'Summerville', 'Sunrise', 'Surfside', 'Tamarac', 'Tampa',
                        'Thorofare', 'Toms River', 'Upper Darby', 'Washington', 'Washington D.C.',
                        'Washington DC', 'Washington D.C. Metro Area', 'Watermill', 'Wayland', 'Westchester',
                        'West Melbourne', 'West Palm Beach', 'Wood-Ridge, NJ', 'Yonkers',
                        // International Cities
                        'Amsterdam', 'Astoria', 'Bangkok', 'Beijing', 'Binghamton', 'Blacksburg', 'Bristol',
                        'Brussels', 'Camberley', 'Camp Springs', 'Carmel', 'Chorley', 'Corte Madera',
                        'Dubai', 'Dublin', 'Edmonton', 'Elmhurst', 'Exeter', 'Frankfurt', 'Geneva',
                        'Gurugram', 'Hong Kong', 'Jakarta', 'Jõhvi', 'Kaohsiung', 'Keystone', 'Lancashire',
                        'London', 'Long Island City', 'Madrid', 'Mammoth Mountain', 'Mayfair', 'Melbourne',
                        'Milan', 'Mumbai', 'Munich', 'Narva', 'North Bethesda', 'Paris', 'Pärnu',
                        'Portland', 'Riga', 'Rio de Janeiro', 'Saaremaa', 'São Paulo', 'Seoul', 'Shanghai',
                        'Sheffield', 'Singapore', 'Stockholm', 'Sydney', 'Taipei', 'Tallinn', 'Tokyo',
                        'Toronto', 'Vancouver', 'Vilnius', 'Warsaw', 'West Drayton'
                      ].map(city => ({ value: city, label: city }))}
                      selected={formData.city || []}
                      onChange={(values) => {
                        handleInputChange('city', values);
                      }}
                      placeholder="Select cities or type to add new..."
                      showSelectAll={true}
                      selectAllLabel="Select All Cities"
                      allowCustomValues={true}
                      onCustomValueAdd={(value) => {
                        toast.success(`Added custom city: ${value}`);
                      }}
                    />
                  </div>
                </div>

                {/* Property & Investment Focus */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium text-gray-700">Property & Investment Focus</Label>
                  
                  {/* Property Types */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-gray-600">Property Types</Label>
                    <NestedMappingSelector
                      mappingType="Property Type"
                      selectedValues={formData.property_types || []}
                      onSelectionChange={(values) => {
                        handleInputChange('property_types', values);
                      }}
                      label=""
                      placeholder="Select property types or type to add new..."
                      showSelectAll={true}
                      selectAllLabel="Select All Property Types"
                      mappingData={mappingData.propertyTypeRaw}
                      allowNewOptions={true}
                      onAddNewOption={(newValue) => handleAddNewOption('Property Type', newValue)}
                    />
                  </div>

                  {/* Property Subcategories - Filtered by Property Types */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-gray-600">Property Subcategories</Label>
                    <ReactMultiSelect
                      options={getFilteredSubcategories}
                      selected={formData.property_subcategories || []}
                      onChange={(values) => {
                        handleInputChange('property_subcategories', values);
                      }}
                      placeholder={formData.property_types && formData.property_types.length > 0 
                        ? `Select subcategories for selected property types...` 
                        : "Select property subcategories..."}
                      showSelectAll={true}
                      selectAllLabel="Select All Subcategories"
                      allowCustomValues={true}
                      onCustomValueAdd={(value) => {
                        toast.success(`Added custom subcategory: ${value}`);
                      }}
                    />
                  </div>

                  {/* Investment Strategies */}
                  <div className="space-y-2">
                    <NestedMappingSelector
                      mappingType="Strategies"
                      selectedValues={formData.strategies || []}
                      onSelectionChange={(values) => {
                        handleInputChange('strategies', values);
                      }}
                      label="Investment Strategies"
                      placeholder="Select strategies..."
                      mappingData={mappingData.strategiesRaw}
                      allowNewOptions={true}
                      onAddNewOption={(newValue) => handleAddNewOption('Strategy', newValue)}
                    />
                  </div>
                </div>

                {/* Decision Making Process */}
                <div>
                  <Label className="text-sm font-medium">Decision Making Process</Label>
                  <Textarea
                    className="mt-1"
                    rows={3}
                    value={formData.decision_making_process || ''}
                    onChange={(e) => handleInputChange('decision_making_process', e.target.value)}
                    placeholder="Describe the decision making process"
                  />
                </div>

                {/* Notes */}
                <div>
                  <Label className="text-sm font-medium">Notes</Label>
                  <Textarea
                    className="mt-1"
                    rows={3}
                    value={formData.notes || ''}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="Additional notes"
                  />
                </div>
              </CardContent>
            </Card>



            {/* DEBT TABLE - Investment Criteria Debt */}
            {isDebtPosition && (
              <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl border-0 rounded-2xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-blue-100 to-indigo-100 border-b border-blue-200">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-200 rounded-xl">
                        <Calculator className="h-5 w-5 text-blue-700" />
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-blue-900">Debt Investment Criteria</div>
                        <div className="text-sm text-blue-700">Loan terms, rates, and debt-specific requirements</div>
                      </div>
                    </div>
                    {isEditingCopiedIC && (
                      <div className="flex items-center gap-2 bg-yellow-100 border border-yellow-300 rounded-lg px-3 py-2">
                        <div className="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">🔒</span>
                        </div>
                        <span className="text-sm font-medium text-yellow-800">Debt details locked (Copied from Company)</span>
                      </div>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-6">
                  {/* Loan Type & Program */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Loan Type</Label>
                      <Input
                        className="mt-1"
                        value={formData.loan_type || ''}
                        onChange={(e) => handleInputChange('loan_type', e.target.value)}
                        placeholder="e.g., Bridge, Permanent"
                        disabled={isEditingCopiedIC}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-blue-700">Loan Program</Label>
                      <NestedMappingSelector
                        mappingType="Loan Program"
                        selectedValues={formData.loan_program ? [formData.loan_program] : []}
                        onSelectionChange={(values) => {
                          handleInputChange('loan_program', values[0] || '');
                        }}
                        label=""
                        placeholder="Select loan program..."
                        showSelectAll={false}
                        mappingData={mappingData.loanProgramRaw}
                        allowNewOptions={true}
                        onAddNewOption={(newValue) => handleAddNewOption('Loan Program', newValue)}
                        disabled={isEditingCopiedIC}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-blue-700">Structured Loan Tranche</Label>
                      <NestedMappingSelector
                        mappingType="Structured Loan Tranches"
                        selectedValues={formData.structured_loan_tranche ? [formData.structured_loan_tranche] : []}
                        onSelectionChange={(values) => {
                          handleInputChange('structured_loan_tranche', values[0] || '');
                        }}
                        label=""
                        placeholder="Select tranche..."
                        showSelectAll={false}
                        mappingData={mappingData.tranchesRaw}
                        allowNewOptions={true}
                        onAddNewOption={(newValue) => handleAddNewOption('Structured Loan Tranches', newValue)}
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* Loan Terms */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Min Loan Term (months)</Label>
                      <Input
                        type="number"
                        className="mt-1"
                        value={formData.debt_min_loan_term || ''}
                        onChange={(e) => handleInputChange('debt_min_loan_term', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder="e.g., 12"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Max Loan Term (months)</Label>
                      <Input
                        type="number"
                        className="mt-1"
                        value={formData.debt_max_loan_term || ''}
                        onChange={(e) => handleInputChange('debt_max_loan_term', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder="e.g., 120"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Interest Rate (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_interest_rate || ''}
                        onChange={(e) => handleInputChange('debt_loan_interest_rate', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 5.5"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* Loan to Value/Cost */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Min LTV (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_to_value_min || ''}
                        onChange={(e) => handleInputChange('debt_loan_to_value_min', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 50"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Max LTV (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_to_value_max || ''}
                        onChange={(e) => handleInputChange('debt_loan_to_value_max', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 75"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Min LTC (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_to_cost_min || ''}
                        onChange={(e) => handleInputChange('debt_loan_to_cost_min', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 60"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Max LTC (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_to_cost_max || ''}
                        onChange={(e) => handleInputChange('debt_loan_to_cost_max', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 80"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* Fees */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Min Origination Fee (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_origination_min_fee || ''}
                        onChange={(e) => handleInputChange('debt_loan_origination_min_fee', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 1.0"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Max Origination Fee (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_origination_max_fee || ''}
                        onChange={(e) => handleInputChange('debt_loan_origination_max_fee', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 1.5"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Min Exit Fee (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_exit_min_fee || ''}
                        onChange={(e) => handleInputChange('debt_loan_exit_min_fee', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 0.5"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Max Exit Fee (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_loan_exit_max_fee || ''}
                        onChange={(e) => handleInputChange('debt_loan_exit_max_fee', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 1.0"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* DSCR */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Min DSCR</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_min_loan_dscr || ''}
                        onChange={(e) => handleInputChange('debt_min_loan_dscr', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 1.25"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Max DSCR</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.debt_max_loan_dscr || ''}
                        onChange={(e) => handleInputChange('debt_max_loan_dscr', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 2.0"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* Additional Debt Fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Lien Position</Label>
                      <Input
                        className="mt-1"
                        value={formData.debt_lien_position || ''}
                        onChange={(e) => handleInputChange('debt_lien_position', e.target.value)}
                        placeholder="e.g., First Lien, Second Lien"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-blue-700">Recourse Loan</Label>
                      <Input
                        className="mt-1"
                        value={formData.debt_recourse_loan || ''}
                        onChange={(e) => handleInputChange('debt_recourse_loan', e.target.value)}
                        placeholder="e.g., Non-recourse, Limited recourse"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* Debt Notes */}
                  <div>
                    <Label className="text-sm font-medium text-blue-700">Debt Notes</Label>
                    <Textarea
                      className="mt-1"
                      rows={3}
                      value={formData.debt_notes || ''}
                      onChange={(e) => handleInputChange('debt_notes', e.target.value)}
                      placeholder="Additional debt-specific notes"
                      disabled={isEditingCopiedIC}
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* EQUITY TABLE - Investment Criteria Equity */}
            {isEquityPosition && (
              <Card className="bg-gradient-to-br from-green-50 to-emerald-50 shadow-xl border-0 rounded-2xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-green-100 to-emerald-100 border-b border-green-200">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-200 rounded-xl">
                        <TrendingUp className="h-5 w-5 text-green-700" />
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-green-900">Equity Investment Criteria</div>
                        <div className="text-sm text-green-700">Return targets, hold periods, and equity-specific requirements</div>
                      </div>
                    </div>
                    {isEditingCopiedIC && (
                      <div className="flex items-center gap-2 bg-yellow-100 border border-yellow-300 rounded-lg px-3 py-2">
                        <div className="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">🔒</span>
                        </div>
                        <span className="text-sm font-medium text-yellow-800">Equity details locked (Copied from Company)</span>
                      </div>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-6">
                  {/* Return Targets */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-green-700">Target Return (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.equity_target_return || ''}
                        onChange={(e) => handleInputChange('equity_target_return', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 15"
                        disabled={isEditingCopiedIC}
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-green-700">Minimum IRR (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.equity_minimum_internal_rate_of_return || ''}
                        onChange={(e) => handleInputChange('equity_minimum_internal_rate_of_return', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 12"
                        disabled={isEditingCopiedIC}
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-green-700">Minimum Yield on Cost (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.equity_minimum_yield_on_cost || ''}
                        onChange={(e) => handleInputChange('equity_minimum_yield_on_cost', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 8"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* Equity Multiples & Cash Flow */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-green-700">Minimum Equity Multiple</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.equity_minimum_equity_multiple || ''}
                        onChange={(e) => handleInputChange('equity_minimum_equity_multiple', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 2.5"
                        disabled={isEditingCopiedIC}
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-green-700">Target Cash on Cash Min (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.equity_target_cash_on_cash_min || ''}
                        onChange={(e) => handleInputChange('equity_target_cash_on_cash_min', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 10"
                        disabled={isEditingCopiedIC}
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-green-700">Attachment Point (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.equity_attachment_point || ''}
                        onChange={(e) => handleInputChange('equity_attachment_point', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 80"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* Hold Periods */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-green-700">Min Hold Period (years)</Label>
                      <Input
                        type="number"
                        className="mt-1"
                        value={formData.equity_min_hold_period_years || ''}
                        onChange={(e) => handleInputChange('equity_min_hold_period_years', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder="e.g., 3"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-green-700">Max Hold Period (years)</Label>
                      <Input
                        type="number"
                        className="mt-1"
                        value={formData.equity_max_hold_period_years || ''}
                        onChange={(e) => handleInputChange('equity_max_hold_period_years', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder="e.g., 7"
                        disabled={isEditingCopiedIC}
                      />
                    </div>
                  </div>

                  {/* Leverage & Timeline */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-green-700">Max Leverage Tolerance (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        className="mt-1"
                        value={formData.equity_max_leverage_tolerance || ''}
                        onChange={(e) => handleInputChange('equity_max_leverage_tolerance', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="e.g., 75"
                        disabled={isEditingCopiedIC }
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-green-700">Typical Closing Timeline (days)</Label>
                      <Input
                        type="number"
                        className="mt-1"
                        value={formData.equity_typical_closing_timeline_days || ''}
                        onChange={(e) => handleInputChange('equity_typical_closing_timeline_days', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder="e.g., 90"
                        disabled={isEditingCopiedIC }
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="proof_of_funds"
                        checked={formData.equity_proof_of_funds_requirement || false}
                        onCheckedChange={(checked) => handleInputChange('equity_proof_of_funds_requirement', checked)}
                        disabled={isEditingCopiedIC }
                      />
                      <Label htmlFor="proof_of_funds" className="text-sm font-medium text-green-700">
                        Proof of Funds Required
                      </Label>
                    </div>
                  </div>

                  {/* Ownership & Requirements */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-green-700">Ownership Requirement</Label>
                      <Textarea
                        className="mt-1"
                        rows={3}
                        value={formData.equity_ownership_requirement || ''}
                        onChange={(e) => handleInputChange('equity_ownership_requirement', e.target.value)}
                        placeholder="e.g., Minority to controlling interests through joint ventures"
                        disabled={isEditingCopiedIC }
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-green-700">Occupancy Requirements</Label>
                      <Textarea
                        className="mt-1"
                        rows={3}
                        value={formData.equity_occupancy_requirements || ''}
                        onChange={(e) => handleInputChange('equity_occupancy_requirements', e.target.value)}
                        placeholder="e.g., Stabilized or value-add assets preferred"
                        disabled={isEditingCopiedIC }
                      />
                    </div>
                  </div>

                  {/* Equity Notes */}
                  <div>
                    <Label className="text-sm font-medium text-green-700">Equity Notes</Label>
                    <Textarea
                      className="mt-1"
                      rows={3}
                      value={formData.equity_notes || ''}
                      onChange={(e) => handleInputChange('equity_notes', e.target.value)}
                      placeholder="Additional equity-specific notes"
                      disabled={isEditingCopiedIC }
                    />
                  </div>
                </CardContent>
              </Card>
            )}

          </div>
        </div>
      </div>
    </div>
  );
}
