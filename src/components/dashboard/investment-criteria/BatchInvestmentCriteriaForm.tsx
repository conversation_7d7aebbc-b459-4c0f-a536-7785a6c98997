'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Target, 
  Plus, 
  Trash2, 
  Co<PERSON>, 
  X, 
  <PERSON><PERSON>ircle,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { toast } from 'sonner'
import InvestmentCriteriaForm from './InvestmentCriteriaForm'

interface InvestmentCriteriaFormData {
  // Central data
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  
  // All debt fields (corrected field names)
  debt_notes?: string;
  debt_closing_time?: number;
  debt_future_facilities?: string;
  debt_eligible_borrower?: string;
  debt_occupancy_requirements?: string;
  debt_lien_position?: string;
  debt_min_loan_dscr?: number;
  debt_max_loan_dscr?: number;
  debt_recourse_loan?: string;
  debt_loan_min_debt_yield?: string;
  debt_prepayment?: string;
  debt_yield_maintenance?: string;
  debt_application_deposit?: number;
  debt_good_faith_deposit?: number;
  debt_loan_origination_max_fee?: number;
  debt_loan_origination_min_fee?: number;
  debt_loan_exit_min_fee?: number;
  debt_loan_exit_max_fee?: number;
  debt_loan_interest_rate?: number;
  debt_loan_interest_rate_based_off_sofr?: number;
  debt_loan_interest_rate_based_off_wsj?: number;
  debt_loan_interest_rate_based_off_prime?: number;
  debt_loan_interest_rate_based_off_3yt?: number;
  debt_loan_interest_rate_based_off_5yt?: number;
  debt_loan_interest_rate_based_off_10yt?: number;
  debt_loan_interest_rate_based_off_30yt?: number;
  debt_rate_lock?: string;
  debt_rate_type?: string;
  debt_loan_to_value_max?: number;
  debt_loan_to_value_min?: number;
  debt_loan_to_cost_min?: number;
  debt_loan_to_cost_max?: number;
  debt_program_overview?: string;
  loan_type?: string;
  debt_loan_type_normalized?: string;
  structured_loan_tranche?: string;
  loan_program?: string;
  debt_min_loan_term?: number;
  debt_max_loan_term?: number;
  debt_amortization?: string;
  
  // All equity fields
  equity_target_return?: number;
  equity_minimum_internal_rate_of_return?: number;
  equity_minimum_yield_on_cost?: number;
  equity_minimum_equity_multiple?: number;
  equity_target_cash_on_cash_min?: number;
  equity_min_hold_period_years?: number;
  equity_max_hold_period_years?: number;
  equity_ownership_requirement?: string;
  equity_attachment_point?: number;
  equity_max_leverage_tolerance?: number;
  equity_typical_closing_timeline_days?: number;
  equity_proof_of_funds_requirement?: boolean;
  equity_notes?: string;
  equity_program_overview?: string;
  equity_occupancy_requirements?: string;
  equity_yield_on_cost?: number;
  equity_target_return_irr_on_equity?: number;
  equity_equity_multiple?: number;
  equity_position_specific_irr?: number;
  equity_position_specific_equity_multiple?: number;
}

interface CriteriaEntry {
  id: string;
  formData: Partial<InvestmentCriteriaFormData>;
  isModified?: boolean;
}

interface BatchInvestmentCriteriaFormProps {
  entityType: 'contact' | 'company';
  entityId: string | number;
  onSave: (criteriaList: InvestmentCriteriaFormData[]) => Promise<void>;
  onCancel: () => void;
  isSaving?: boolean;
}

const DEBT_POSITIONS = ['Senior Debt', 'Stretch Senior', 'Mezzanine'];
const EQUITY_POSITIONS = ['Preferred Equity', 'Common Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP'];

export default function BatchInvestmentCriteriaForm({
  entityType,
  entityId,
  onSave,
  onCancel,
  isSaving = false
}: BatchInvestmentCriteriaFormProps) {
  const [criteriaEntries, setCriteriaEntries] = useState<CriteriaEntry[]>([
    { id: 'entry-1', formData: { capital_position: '' } }
  ]);
  const [activeEntryId, setActiveEntryId] = useState<string>('entry-1');
  const [copiedEntry, setCopiedEntry] = useState<CriteriaEntry | null>(null);

  const addNewEntry = () => {
    const newId = `entry-${Date.now()}`;
    setCriteriaEntries(prev => [
      ...prev,
      { id: newId, formData: { capital_position: '' } }
    ]);
    setActiveEntryId(newId);
  };

  const removeEntry = (entryId: string) => {
    if (criteriaEntries.length <= 1) {
      toast.error('At least one criteria entry is required');
      return;
    }
    
    setCriteriaEntries(prev => prev.filter(entry => entry.id !== entryId));
    
    // If we removed the active entry, switch to the first remaining one
    if (activeEntryId === entryId) {
      const remainingEntries = criteriaEntries.filter(entry => entry.id !== entryId);
      if (remainingEntries.length > 0) {
        setActiveEntryId(remainingEntries[0].id);
      }
    }
  };

  const updateEntryData = (entryId: string, formData: Partial<InvestmentCriteriaFormData>) => {
    console.log('Updating entry data:', { entryId, formData });
    setCriteriaEntries(prev => {
      const updated = prev.map(entry => 
        entry.id === entryId 
          ? { ...entry, formData: { ...entry.formData, ...formData }, isModified: true }
          : entry
      );
      console.log('Updated entries:', updated);
      return updated;
    });
  };

  const getCapitalPositionColor = (position: string) => {
    if (DEBT_POSITIONS.includes(position)) {
      return 'bg-blue-50 text-blue-700 border-blue-200';
    } else if (EQUITY_POSITIONS.includes(position)) {
      return 'bg-green-50 text-green-700 border-green-200';
    }
    return 'bg-gray-50 text-gray-700 border-gray-200';
  };


  // New simplified copy/paste functions
  const copyEntry = (entryId: string) => {
    const entryToCopy = criteriaEntries.find(entry => entry.id === entryId);
    if (entryToCopy && entryToCopy.formData.capital_position) {
      setCopiedEntry(entryToCopy);
      toast.success(`Copied criteria for ${entryToCopy.formData.capital_position}! Click on other entries to paste.`);
    } else {
      toast.error('Cannot copy - entry has no capital position selected');
    }
  };

  const pasteToEntry = (targetEntryId: string) => {
    if (!copiedEntry) {
      toast.error('No criteria copied. Please copy an entry first.');
      return;
    }

    setCriteriaEntries(prev => 
      prev.map(entry => 
        entry.id === targetEntryId
          ? {
              ...entry,
              formData: {
                ...copiedEntry.formData,
                // Keep the original capital_position if it exists
                capital_position: entry.formData.capital_position || copiedEntry.formData.capital_position
              },
              isModified: true
            }
          : entry
      )
    );

    toast.success(`Pasted criteria to entry! Capital position preserved.`);
    
    // Clear copied entry after successful paste
    setCopiedEntry(null);
  };

  const handleBatchSave = async () => {
    // Validate all entries
    const validEntries: InvestmentCriteriaFormData[] = [];
    const invalidEntries: string[] = [];
    
    criteriaEntries.forEach((entry, index) => {
      if (!entry.formData.capital_position) {
        invalidEntries.push(`Entry ${index + 1}: Capital position is required`);
      } else {
        validEntries.push(entry.formData as InvestmentCriteriaFormData);
      }
    });
    
    if (invalidEntries.length > 0) {
      toast.error(`Please fix the following errors:\n${invalidEntries.join('\n')}`);
      return;
    }
    
    try {
      await onSave(validEntries);
    } catch (error) {
      console.error('Error saving batch criteria:', error);
      toast.error('Failed to save investment criteria');
    }
  };

  const activeEntry = criteriaEntries.find(entry => entry.id === activeEntryId);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="w-full">
        {/* Top Navigation Bar */}
        <div className="bg-white shadow-lg border-b border-slate-200 sticky top-0 z-10">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              {/* Left Side - Add Entry Button */}
              <div className="flex items-center gap-3">
                <Button
                  size="sm"
                  onClick={addNewEntry}
                  className="h-8 px-4 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Entry
                </Button>
                
                {/* Copy Status Banner */}
                {copiedEntry && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg px-3 py-1">
                    <div className="flex items-center gap-2">
                      <Copy className="h-3 w-3 text-blue-600" />
                      <div className="text-xs text-blue-800">
                        <span className="font-medium">Copied:</span> {copiedEntry.formData.capital_position}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Center - Entry Slider */}
              <div className="flex items-center gap-3 flex-1 justify-center">
                <div className="text-sm text-slate-600 font-medium">
                  {criteriaEntries.findIndex(entry => entry.id === activeEntryId) + 1} of {criteriaEntries.length}
                </div>
                <div className="flex items-center gap-2 overflow-x-auto max-w-[600px] px-4">
                  {criteriaEntries.map((entry, index) => (
                    <div
                      key={entry.id}
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg border-2 cursor-pointer transition-all relative whitespace-nowrap min-w-[120px] ${
                        activeEntryId === entry.id
                          ? 'border-blue-300 bg-blue-50 shadow-sm'
                          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
                      }`}
                      onClick={() => setActiveEntryId(entry.id)}
                    >
                      {entry.isModified && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                      )}
                      
                      <div className="flex items-center gap-2 flex-1">
                        <div className="flex-1 min-w-0">
                          {entry.formData.capital_position ? (
                            <div className="font-medium text-sm truncate">
                              {entry.formData.capital_position}
                            </div>
                          ) : (
                            <div className="font-medium text-sm text-gray-500">
                              Entry {index + 1}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-1">
                          {/* Copy Button */}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              copyEntry(entry.id);
                            }}
                            disabled={!entry.formData.capital_position}
                            className="h-6 w-6 p-0 hover:bg-blue-100"
                            title="Copy criteria"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          
                          {/* Paste Button */}
                          {copiedEntry && copiedEntry.id !== entry.id && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                pasteToEntry(entry.id);
                              }}
                              className="h-6 w-6 p-0 text-green-600 hover:text-green-700 hover:bg-green-100"
                              title={`Paste from ${copiedEntry.formData.capital_position}`}
                            >
                              📋
                            </Button>
                          )}
                          
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeEntry(entry.id);
                            }}
                            disabled={criteriaEntries.length <= 1}
                            className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-100"
                            title="Delete entry"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Right Side - Action Buttons */}
              <div className="flex items-center space-x-3">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={onCancel}
                  disabled={isSaving}
                  className="h-8 px-4 rounded-lg border border-slate-200 hover:bg-slate-50 transition-all duration-200 font-medium text-sm"
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleBatchSave}
                  disabled={isSaving}
                  className="h-8 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 min-w-[120px] disabled:opacity-50 disabled:cursor-not-allowed font-medium text-sm"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Save All ({criteriaEntries.length})
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Full Screen Form */}
        <div className="w-full">
          {activeEntry ? (
            <InvestmentCriteriaForm
              key={activeEntry.id}
              initialData={activeEntry.formData}
              onChange={(formData) => {
                // Update the entry data in real-time as user types
                updateEntryData(activeEntry.id, formData);
              }}
              onSave={async (formData) => {
                // Update the entry data immediately when form is saved
                updateEntryData(activeEntry.id, formData);
                toast.success('Entry updated - changes will be saved when you click "Save All"');
              }}
              onCancel={() => {
                // Reset the entry to its previous state
                // For now, we'll just show a message
                toast.info('Changes discarded for this entry');
              }}
              isEditing={false}
              isSaving={false}
              showCompanySelection={false}
              isEditingCopiedIC={false}
              entityType={entityType}
              entityId={entityId}
              hideHeader={true}
            />
          ) : (
            <div className="flex items-center justify-center min-h-[60vh]">
              <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden max-w-md">
                <CardContent className="p-8 text-center">
                  <AlertCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-700 mb-2">
                    No Entry Selected
                  </h3>
                  <p className="text-gray-500">
                    Select an entry from the top navigation to edit its criteria
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
