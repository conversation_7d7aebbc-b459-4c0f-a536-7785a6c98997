"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ReactMultiSelect } from '@/components/ui/react-multi-select';
import NestedMappingSelector from '../../investment-criteria/NestedMappingSelector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  getInvestmentFieldVisibility, 
  isFieldVisible,
  getVisibleDebtFields,
  getVisibleEquityFields
} from '@/lib/investment-criteria-helpers';
import { 
  X, Filter, RotateCcw, Search, SlidersHorizontal, ChevronRight, Sparkles,
  Building, DollarSign, MapPin, Settings, BarChart3, TrendingUp, Building2,
  Target, Briefcase, Calculator, Calendar, Users, Globe, ArrowUp, ArrowDown, 
  Clock, Banknote, Percent, Timer, LineChart, Activity, PieChart, User, UserCheck,
  Mail, CheckCircle, AlertCircle, MessageSquare, Send, Loader2, Brain, Eye,
  Database, Table, Shield, Minus, Plus, Eye as EyeIcon, EyeOff, Factory,
  TwitterIcon, Facebook, Instagram, Youtube, Phone, MapPinIcon, FileText,
  Briefcase as BriefcaseIcon, TrendingDown, Network, HandHeart, Zap, TreePine,
  Gavel, Landmark, Star, Award, Home, BarChart4, Wallet, CreditCard, TrendingUpIcon,
  Layers, Crown, Building2Icon, Building as Bank, Scale, Handshake, UserCog,
  Gauge, Lightbulb, Recycle, Smartphone, Construction, CheckSquare, ArchiveIcon,
  PieChartIcon, FileBarChartIcon
} from 'lucide-react';
import type { CompanyUnifiedFiltersV2 } from "../../../../types/unified-filters-v2";

interface MappingsData {
  [type: string]: {
    parents: string[]
    children: string[]
    hierarchical: {
      [parent: string]: string[]
    }
  }
}

interface CompanyUnifiedFiltersV2Props {
  filters: CompanyUnifiedFiltersV2;
  mappings?: MappingsData;
  onFiltersChange: (filters: CompanyUnifiedFiltersV2) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

export default function CompanyUnifiedFiltersV2({
  filters,
  mappings,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: CompanyUnifiedFiltersV2Props) {
  const { data: session } = useSession();
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<CompanyUnifiedFiltersV2>(filters);
  const [pendingFilters, setPendingFilters] = useState<CompanyUnifiedFiltersV2>(filters);
  const [localRangeInputs, setLocalRangeInputs] = useState<{[key: string]: string}>({});

  // Search functionality state
  const [searchTerm, setSearchTerm] = useState('');
  const [searchCriteria, setSearchCriteria] = useState<'companyNameSearch' | 'companyWebsiteSearch'>('companyNameSearch');


  // Core Company Filter Options
  const [coreCompanyOptions, setCoreCompanyOptions] = useState<{
    companyAddresses: Array<{value: string, label: string}>,
    companyCities: Array<{value: string, label: string}>,
    companyStates: Array<{value: string, label: string}>,
    companyWebsites: Array<{value: string, label: string}>,
    industries: Array<{value: string, label: string}>,
    companyCountries: Array<{value: string, label: string}>,
    sources: Array<{value: string, label: string}>,
    websiteScrapingStatuses: Array<{value: string, label: string}>,
    companyOverviewStatuses: Array<{value: string, label: string}>,
    overviewV2Statuses: Array<{value: string, label: string}>,
  }>({
    companyAddresses: [],
    companyCities: [],
    companyStates: [],
    companyWebsites: [],
    industries: [],
    companyCountries: [],
    sources: [],
    websiteScrapingStatuses: [],
    companyOverviewStatuses: [],
    overviewV2Statuses: [],
  });
  const [loadingCoreOptions, setLoadingCoreOptions] = useState(false);

  // Company Overview V2 Filter Options
  const [overviewV2Options, setOverviewV2Options] = useState<{
    companyTypes: Array<{value: string, label: string}>,
    investmentFocus: Array<{value: string, label: string}>,
    headquartersAddresses: Array<{value: string, label: string}>,
    headquartersCities: Array<{value: string, label: string}>,
    headquartersStates: Array<{value: string, label: string}>,
    headquartersCountries: Array<{value: string, label: string}>,
    officeLocations: Array<{value: string, label: string}>,
    balanceSheetStrengths: Array<{value: string, label: string}>,
    fundingSources: Array<{value: string, label: string}>,
    developmentFeeStructures: Array<{value: string, label: string}>,
    creditRatings: Array<{value: string, label: string}>,
    investmentVehicleTypes: Array<{value: string, label: string}>,
    activeFundNameSeries: Array<{value: string, label: string}>,
    fundraisingStatuses: Array<{value: string, label: string}>,
    lenderTypes: Array<{value: string, label: string}>,
    lendingOrigins: Array<{value: string, label: string}>,
    portfolioHealths: Array<{value: string, label: string}>,
    partnerships: Array<{value: string, label: string}>,
    keyEquityPartners: Array<{value: string, label: string}>,
    keyDebtPartners: Array<{value: string, label: string}>,
    boardOfDirectors: Array<{value: string, label: string}>,
    keyExecutives: Array<{value: string, label: string}>,
    founderBackgrounds: Array<{value: string, label: string}>,

    corporateStructures: Array<{value: string, label: string}>,
    parentCompanies: Array<{value: string, label: string}>,
    subsidiaries: Array<{value: string, label: string}>,
    stockTickerSymbols: Array<{value: string, label: string}>,
    stockExchanges: Array<{value: string, label: string}>,
    targetCustomerProfiles: Array<{value: string, label: string}>,
    majorCompetitors: Array<{value: string, label: string}>,
    industryAwardsRecognitions: Array<{value: string, label: string}>,
    rolesInPreviousDeals: Array<{value: string, label: string}>,
    internalRelationshipManagers: Array<{value: string, label: string}>,
    pipelineStatuses: Array<{value: string, label: string}>,
    recentNewsSentiments: Array<{value: string, label: string}>,
    dataSources: Array<{value: string, label: string}>,
  }>({
    companyTypes: [],
    investmentFocus: [],
    headquartersAddresses: [],
    headquartersCities: [],
    headquartersStates: [],
    headquartersCountries: [],
    officeLocations: [],
    balanceSheetStrengths: [],
    fundingSources: [],
    developmentFeeStructures: [],
    creditRatings: [],
    investmentVehicleTypes: [],
    activeFundNameSeries: [],
    fundraisingStatuses: [],
    lenderTypes: [],
    lendingOrigins: [],
    portfolioHealths: [],
    partnerships: [],
    keyEquityPartners: [],
    keyDebtPartners: [],
    boardOfDirectors: [],
    keyExecutives: [],
    founderBackgrounds: [],
    corporateStructures: [],
    parentCompanies: [],
    subsidiaries: [],
    stockTickerSymbols: [],
    stockExchanges: [],
    targetCustomerProfiles: [],
    majorCompetitors: [],
    industryAwardsRecognitions: [],
    rolesInPreviousDeals: [],
    internalRelationshipManagers: [],
    pipelineStatuses: [],
    recentNewsSentiments: [],
    dataSources: [],
  });
  const [loadingOverviewV2Options, setLoadingOverviewV2Options] = useState(false);

  // Processing status options with null handling
  const processingStatusOptions = [
    { value: 'not_started', label: 'Not Started', icon: Minus, color: 'from-gray-50 to-slate-50' },
    { value: 'pending', label: 'Pending', icon: Clock, color: 'from-yellow-50 to-amber-50' },
    { value: 'running', label: 'Running', icon: Loader2, color: 'from-blue-50 to-indigo-50' },
    { value: 'completed', label: 'Completed', icon: CheckCircle, color: 'from-green-50 to-emerald-50' },
    { value: 'failed', label: 'Failed', icon: AlertCircle, color: 'from-red-50 to-pink-50' },
    { value: 'error', label: 'Error', icon: AlertCircle, color: 'from-red-50 to-pink-50' }
  ];

  // Contact Processor Options - Using predefined status options
  const [contactProcessorOptions, setContactProcessorOptions] = useState<{
    contactsEmailVerificationStatuses: Array<{value: string, label: string}>,
    contactsEnrichmentV2Statuses: Array<{value: string, label: string}>,
    contactsEmailGenerationStatuses: Array<{value: string, label: string}>,
    contactsEmailSendingStatuses: Array<{value: string, label: string}>,
  }>({
    contactsEmailVerificationStatuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
    contactsEnrichmentV2Statuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
    contactsEmailGenerationStatuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
    contactsEmailSendingStatuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
  });
  const [loadingContactProcessorOptions, setLoadingContactProcessorOptions] = useState(false);

  // Investment Criteria Filter Options (Enhanced with debt & equity fields)
  const [investmentCriteriaOptions, setInvestmentCriteriaOptions] = useState<{
    capitalPositions: Array<{value: string, label: string}>,
    propertyTypes: Array<{value: string, label: string}>,
    propertySubcategories: Array<{value: string, label: string}>,
    strategies: Array<{value: string, label: string}>,
    loanTypes: Array<{value: string, label: string}>,
    structuredLoanTranches: Array<{value: string, label: string}>,
    loanPrograms: Array<{value: string, label: string}>,
    recourseLoans: Array<{value: string, label: string}>,
    countries: Array<{value: string, label: string}>,
    regions: Array<{value: string, label: string}>,
    states: Array<{value: string, label: string}>,
    cities: Array<{value: string, label: string}>,
    decisionMakingProcesses: Array<{value: string, label: string}>,
    // Debt specific fields
    eligibleBorrowers: Array<{value: string, label: string}>,
    lienPositions: Array<{value: string, label: string}>,
    rateLocks: Array<{value: string, label: string}>,
    rateTypes: Array<{value: string, label: string}>,
    loanTypeNormalized: Array<{value: string, label: string}>,
    amortizations: Array<{value: string, label: string}>,
    loanMinDebtYield: Array<{value: string, label: string}>,
    futureFacilities: Array<{value: string, label: string}>,
    occupancyRequirements: Array<{value: string, label: string}>,
    prepayment: Array<{value: string, label: string}>,
    yieldMaintenance: Array<{value: string, label: string}>,
    // Equity specific fields
    ownershipRequirements: Array<{value: string, label: string}>,
  }>({
    capitalPositions: [],
    propertyTypes: [],
    propertySubcategories: [],
    strategies: [],
    loanTypes: [],
    structuredLoanTranches: [],
    loanPrograms: [],
    recourseLoans: [],
    countries: [],
    regions: [],
    states: [],
    cities: [],
    decisionMakingProcesses: [],
    eligibleBorrowers: [],
    lienPositions: [],
    rateLocks: [],
    rateTypes: [],
    loanTypeNormalized: [],
    amortizations: [],
    loanMinDebtYield: [],
    futureFacilities: [],
    occupancyRequirements: [],
    prepayment: [],
    yieldMaintenance: [],
    ownershipRequirements: [],
  });
  const [loadingInvestmentCriteriaOptions, setLoadingInvestmentCriteriaOptions] = useState(false);

  // Raw mapping data for NestedMappingSelector components
  const [mappingData, setMappingData] = useState<{
    propertyTypeRaw?: any;
    regionsRaw?: any;
    statesRaw?: any;
  }>({
    propertyTypeRaw: null,
    regionsRaw: null,
    statesRaw: null
  });

  // Function to get filtered states based on selected regions
  const getFilteredStates = useMemo(() => {
    if (!mappingData.regionsRaw || !pendingFilters.regions || pendingFilters.regions.length === 0) {
      // No regions selected, return all states from U.S States mapping
      if (mappingData.statesRaw?.hierarchyRows) {
        return mappingData.statesRaw.hierarchyRows.map((row: any) => ({
          value: row.values[0],
          label: row.values[0],
          count: 1
        }));
      }
      return [];
    }

    // Regions selected, filter states based on selected regions
    const regionStateMap = mappingData.regionsRaw?.nestedData || {};
    const allStates = new Set<string>();
    
    pendingFilters.regions.forEach((region: string) => {
      if (regionStateMap[region]?.states) {
        regionStateMap[region].states.forEach((state: string) => {
          allStates.add(state);
        });
      }
    });

    return Array.from(allStates).map(state => ({
      value: state,
      label: state,
      count: 1
    }));
  }, [mappingData.regionsRaw, mappingData.statesRaw, pendingFilters.regions]);

  // Individual NOT filter modes
  const [filterNotModes, setFilterNotModes] = useState<{[key: string]: boolean}>({
    source: false,
    industry: false,
    companyType: false,
    websiteScrapingStatus: false,
    companyOverviewStatus: false,
    overviewV2Status: false,
    investmentCriteriaStatus: false,
    contactsEmailVerificationStatus: false,
    contactsEnrichmentV2Status: false,
    contactsEmailGenerationStatus: false,
    contactsEmailSendingStatus: false,
    contactsInvestmentCriteriaStatus: false,
    investmentFocus: false,
    capitalPosition: false,
    propertyTypes: false,
    strategies: false,
    partnerships: false,
    fundraisingStatus: false,
    lenderType: false,
  });

  // Track which table sections are expanded
  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
    companies: true,
    company_overview_v2: false,
    contact_processors: false,
    investment_criteria: false,
  });

  // Determine field visibility based on capital position and user role
  const fieldVisibility = React.useMemo(() => {
    const capitalPositions = pendingFilters.capitalPosition || [];
    return getInvestmentFieldVisibility(capitalPositions, session);
  }, [pendingFilters.capitalPosition, session]);

  // Helper function to check if a field should be visible for current user
  const shouldShowField = React.useCallback((fieldName: string) => {
    const capitalPositions = pendingFilters.capitalPosition || [];
    return isFieldVisible(fieldName, capitalPositions, session);
  }, [pendingFilters.capitalPosition, session]);

  // Fetch all filter options on component mount
  useEffect(() => {
    fetchAllFilterOptions();
  }, []);

  const fetchAllFilterOptions = async () => {
    await Promise.all([
      fetchCoreCompanyOptions(),
      fetchOverviewV2Options(),
      fetchInvestmentCriteriaOptions(),
    ]);
  };

  async function fetchCoreCompanyOptions() {
    setLoadingCoreOptions(true);
    try {
      const response = await fetch('/api/companies/filter-options-v2?section=core_companies');
      if (response.ok) {
        const data = await response.json();
        setCoreCompanyOptions(data);
      }
    } catch (error) {
      console.error('Error fetching core company options:', error);
    } finally {
      setLoadingCoreOptions(false);
    }
  }

  async function fetchOverviewV2Options() {
    setLoadingOverviewV2Options(true);
    try {
      const response = await fetch('/api/companies/filter-options-v2?section=overview_v2');
      if (response.ok) {
        const data = await response.json();
        setOverviewV2Options(data);
      }
    } catch (error) {
      console.error('Error fetching overview V2 options:', error);
    } finally {
      setLoadingOverviewV2Options(false);
    }
  }

  async function fetchInvestmentCriteriaOptions() {
    setLoadingInvestmentCriteriaOptions(true);
    try {
      const [investmentCriteriaRes, propertyTypeRes, regionsRes] = await Promise.all([
        fetch('/api/companies/filter-options-v2?section=investment_criteria'),
        fetch('/api/mapping-tables/types?type=Property Type'),
        fetch('/api/mapping-tables/types?type=U.S Regions')
      ]);

      if (investmentCriteriaRes.ok) {
        const data = await investmentCriteriaRes.json();
        setInvestmentCriteriaOptions(data);
      }

      // Fetch raw mapping data for NestedMappingSelector
      if (propertyTypeRes.ok) {
        const propertyTypeData = await propertyTypeRes.json();
        if (propertyTypeData.success) {
          setMappingData(prev => ({ ...prev, propertyTypeRaw: propertyTypeData.data }));
        }
      }

      if (regionsRes.ok) {
        const regionsData = await regionsRes.json();
        if (regionsData.success) {
          setMappingData(prev => ({ ...prev, regionsRaw: regionsData.data }));
        }
      }

      // Fetch states mapping data
      const statesRes = await fetch('/api/mapping-tables/types?type=U.S%20States');
      if (statesRes.ok) {
        const statesData = await statesRes.json();
        if (statesData.success) {
          setMappingData(prev => ({ ...prev, statesRaw: statesData.data }));
        }
      }
    } catch (error) {
      console.error('Error fetching investment criteria options:', error);
    } finally {
      setLoadingInvestmentCriteriaOptions(false);
    }
  }

  // Sync local filters with prop changes
  useEffect(() => {
    setLocalFilters(filters);
    setPendingFilters(filters);
  }, [filters]);

  // Update pending filters (not applied yet)
  const updatePendingFilters = (newFilters: Partial<CompanyUnifiedFiltersV2>) => {
    const updatedPendingFilters = { ...pendingFilters, ...newFilters, page: 1 };
    setPendingFilters(updatedPendingFilters);
  };

  // Apply filters when user clicks Apply button
  const applyFilters = () => {
    setLocalFilters(pendingFilters);
    onFiltersChange(pendingFilters);
    setIsFilterPanelOpen(false);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    const defaultFilters = {
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc' as const
    };
    
    setLocalFilters(defaultFilters);
    setPendingFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  // Handle range input changes with local state
  const updateLocalRangeInput = (key: string, value: string) => {
    setLocalRangeInputs(prev => ({ ...prev, [key]: value }));
  };

  // Apply range filter when user finishes input
  const applyRangeFilter = (key: string, value: string) => {
    const numericValue = value.trim() === '' ? undefined : Number(value);
    updatePendingFilters({ [key]: numericValue } as Partial<CompanyUnifiedFiltersV2>);
    
    setLocalRangeInputs(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Get current value for range input
  const getRangeInputValue = (key: string, filterValue?: number) => {
    return localRangeInputs[key] !== undefined ? localRangeInputs[key] : (filterValue || '');
  };



  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    
    // Search filters
    if (pendingFilters.companyNameSearch) count++;
    if (pendingFilters.companyWebsiteSearch) count++;
    
    // Basic filters
    if (pendingFilters.sortBy && pendingFilters.sortBy !== 'updated_at') count++;
    if (pendingFilters.sortOrder && pendingFilters.sortOrder !== 'desc') count++;
    
    // Core company table filters
    if (pendingFilters.companyAddress?.length) count++;
    if (pendingFilters.companyCity?.length) count++;
    if (pendingFilters.companyState?.length) count++;
    if (pendingFilters.companyWebsite?.length) count++;
    if (pendingFilters.industry?.length) count++;
    if (pendingFilters.companyCountry?.length) count++;
    if (pendingFilters.source?.length) count++;
    if (pendingFilters.websiteScrapingStatus?.length) count++;
    if (pendingFilters.companyOverviewStatus?.length) count++;
    if (pendingFilters.overviewV2Status?.length) count++;
    if (pendingFilters.investmentCriteriaStatus?.length) count++;
    
    // New filters
    if (pendingFilters.notEmptyCompanyWebsite !== undefined) count++;
    if (pendingFilters.companyIds?.length) count++;
    if (pendingFilters.companyWebsites?.length) count++;
    
    // Overview V2 filters
    if (pendingFilters.companyType?.length) count++;
    if (pendingFilters.foundedYearMin !== undefined) count++;
    if (pendingFilters.foundedYearMax !== undefined) count++;
    if (pendingFilters.investmentFocus?.length) count++;
    
    // V2 Contact Information filters
    if (pendingFilters.hasMainPhone !== undefined) count++;
    if (pendingFilters.hasSecondaryPhone !== undefined) count++;
    if (pendingFilters.hasMainEmail !== undefined) count++;
    if (pendingFilters.hasSecondaryEmail !== undefined) count++;
    if (pendingFilters.hasCompanyLinkedin !== undefined) count++;
    if (pendingFilters.hasTwitter !== undefined) count++;
    if (pendingFilters.hasFacebook !== undefined) count++;
    if (pendingFilters.hasInstagram !== undefined) count++;
    if (pendingFilters.hasYoutube !== undefined) count++;
    
    // V2 Location filters
    if (pendingFilters.headquartersAddress?.length) count++;
    if (pendingFilters.headquartersCity?.length) count++;
    if (pendingFilters.headquartersState?.length) count++;
    if (pendingFilters.headquartersCountry?.length) count++;
    if (pendingFilters.officeLocations?.length) count++;
    
    // V2 Financial metrics filters
    if (pendingFilters.fundSizeMin !== undefined) count++;
    if (pendingFilters.fundSizeMax !== undefined) count++;
    if (pendingFilters.aumMin !== undefined) count++;
    if (pendingFilters.aumMax !== undefined) count++;
    if (pendingFilters.numberOfPropertiesMin !== undefined) count++;
    if (pendingFilters.numberOfPropertiesMax !== undefined) count++;
    if (pendingFilters.numberOfOfficesMin !== undefined) count++;
    if (pendingFilters.numberOfOfficesMax !== undefined) count++;
    if (pendingFilters.numberOfEmployeesMin !== undefined) count++;
    if (pendingFilters.numberOfEmployeesMax !== undefined) count++;
    if (pendingFilters.annualRevenueMin !== undefined) count++;
    if (pendingFilters.annualRevenueMax !== undefined) count++;
    
    // V2 Financial information filters
    if (pendingFilters.balanceSheetStrength?.length) count++;
    if (pendingFilters.fundingSources?.length) count++;
    if (pendingFilters.creditRating?.length) count++;
    if (pendingFilters.dryPowderMin !== undefined) count++;
    if (pendingFilters.dryPowderMax !== undefined) count++;
    
    // V2 Investment & Fund information filters
    if (pendingFilters.investmentVehicleType?.length) count++;
    if (pendingFilters.fundraisingStatus?.length) count++;
    if (pendingFilters.lenderType?.length) count++;
    if (pendingFilters.annualLoanVolumeMin !== undefined) count++;
    if (pendingFilters.annualLoanVolumeMax !== undefined) count++;
    if (pendingFilters.portfolioHealth?.length) count++;
    
    // V2 Partnership & Leadership filters
    if (pendingFilters.partnerships?.length) count++;
    if (pendingFilters.keyEquityPartners?.length) count++;
    if (pendingFilters.keyDebtPartners?.length) count++;
    if (pendingFilters.keyExecutives?.length) count++;
    
    // V2 Market positioning filters
      if (pendingFilters.sustainabilityEsgFocus !== undefined) count++;
    if (pendingFilters.technologyProptechAdoption !== undefined) count++;
    if (pendingFilters.adaptiveReuseExperience !== undefined) count++;
    if (pendingFilters.regulatoryZoningExpertise !== undefined) count++;
    
    // V2 Corporate structure filters
    if (pendingFilters.corporateStructure?.length) count++;
    if (pendingFilters.parentCompany?.length) count++;
    if (pendingFilters.stockTickerSymbol?.length) count++;
    if (pendingFilters.stockExchange?.length) count++;
    
    // Contact processor flags
    if (pendingFilters.hasContacts !== undefined) count++;
    if (pendingFilters.contactsEmailVerificationStatus?.length) count++;
    if (pendingFilters.contactsEnrichmentV2Status?.length) count++;
    if (pendingFilters.contactsEmailGenerationStatus?.length) count++;
    if (pendingFilters.contactsEmailSendingStatus?.length) count++;
    if (pendingFilters.contactsInvestmentCriteriaStatus?.length) count++;
    
    // Investment criteria filters
    if (pendingFilters.capitalPosition?.length) count++;
    if (pendingFilters.propertyTypes?.length) count++;
    if (pendingFilters.strategies?.length) count++;
    if (pendingFilters.regions?.length) count++;
    if (pendingFilters.states?.length) count++;
    if (pendingFilters.cities?.length) count++;
    if (pendingFilters.countries?.length) count++;
    if (pendingFilters.dealSizeMin !== undefined) count++;
    if (pendingFilters.dealSizeMax !== undefined) count++;
    
    // Investment criteria debt fields
    if (pendingFilters.loanTypes?.length) count++;
    if (pendingFilters.loanProgram?.length) count++;
    if (pendingFilters.structuredLoanTranche?.length) count++;
    if (pendingFilters.recourseLoan?.length) count++;
    if (pendingFilters.eligibleBorrower?.length) count++;
    if (pendingFilters.lienPosition?.length) count++;
    if (pendingFilters.rateLock?.length) count++;
    if (pendingFilters.rateType?.length) count++;
    if (pendingFilters.amortization?.length) count++;
    if (pendingFilters.loanTypeNormalized?.length) count++;
    if (pendingFilters.loanMinDebtYield?.length) count++;
    if (pendingFilters.futureFacilities?.length) count++;
    if (pendingFilters.occupancyRequirements?.length) count++;
    if (pendingFilters.prepayment?.length) count++;
    if (pendingFilters.yieldMaintenance?.length) count++;
    
    // Investment criteria debt range fields
    if (pendingFilters.closingTimeMin !== undefined) count++;
    if (pendingFilters.closingTimeMax !== undefined) count++;
    if (pendingFilters.minLoanDscrMin !== undefined) count++;
    if (pendingFilters.minLoanDscrMax !== undefined) count++;
    if (pendingFilters.maxLoanDscrMin !== undefined) count++;
    if (pendingFilters.maxLoanDscrMax !== undefined) count++;
    if (pendingFilters.loanOriginationMaxFeeMin !== undefined) count++;
    if (pendingFilters.loanOriginationMaxFeeMax !== undefined) count++;
    if (pendingFilters.loanOriginationMinFeeMin !== undefined) count++;
    if (pendingFilters.loanOriginationMinFeeMax !== undefined) count++;
    if (pendingFilters.loanExitMinFeeMin !== undefined) count++;
    if (pendingFilters.loanExitMinFeeMax !== undefined) count++;
    if (pendingFilters.loanExitMaxFeeMin !== undefined) count++;
    if (pendingFilters.loanExitMaxFeeMax !== undefined) count++;
    if (pendingFilters.loanInterestRateSofrMin !== undefined) count++;
    if (pendingFilters.loanInterestRateSofrMax !== undefined) count++;
    if (pendingFilters.loanInterestRateWsjMin !== undefined) count++;
    if (pendingFilters.loanInterestRateWsjMax !== undefined) count++;
    if (pendingFilters.loanInterestRatePrimeMin !== undefined) count++;
    if (pendingFilters.loanInterestRatePrimeMax !== undefined) count++;
    if (pendingFilters.loanInterestRate3ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate3ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate5ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate5ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate10ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate10ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate30ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate30ytMax !== undefined) count++;
    if (pendingFilters.loanToValueMinMin !== undefined) count++;
    if (pendingFilters.loanToValueMinMax !== undefined) count++;
    if (pendingFilters.loanToValueMaxMin !== undefined) count++;
    if (pendingFilters.loanToValueMaxMax !== undefined) count++;
    if (pendingFilters.loanToCostMinMin !== undefined) count++;
    if (pendingFilters.loanToCostMinMax !== undefined) count++;
    if (pendingFilters.loanToCostMaxMin !== undefined) count++;
    if (pendingFilters.loanToCostMaxMax !== undefined) count++;
    if (pendingFilters.minLoanTermMin !== undefined) count++;
    if (pendingFilters.minLoanTermMax !== undefined) count++;
    if (pendingFilters.maxLoanTermMin !== undefined) count++;
    if (pendingFilters.maxLoanTermMax !== undefined) count++;
    
    // Investment criteria equity fields
    if (pendingFilters.ownershipRequirement?.length) count++;
    if (pendingFilters.minimumYieldOnCostMin !== undefined) count++;
    if (pendingFilters.minimumYieldOnCostMax !== undefined) count++;
    if (pendingFilters.maxLeverageToleranceMin !== undefined) count++;
    if (pendingFilters.maxLeverageToleranceMax !== undefined) count++;
    
    // Investment criteria equity range fields
    if (pendingFilters.targetReturnMin !== undefined) count++;
    if (pendingFilters.targetReturnMax !== undefined) count++;
    if (pendingFilters.minimumIrrMin !== undefined) count++;
    if (pendingFilters.minimumIrrMax !== undefined) count++;
    if (pendingFilters.targetCashOnCashMin !== undefined) count++;
    if (pendingFilters.targetCashOnCashMax !== undefined) count++;
    if (pendingFilters.minHoldPeriodYearsMin !== undefined) count++;
    if (pendingFilters.minHoldPeriodYearsMax !== undefined) count++;
    if (pendingFilters.maxHoldPeriodYearsMin !== undefined) count++;
    if (pendingFilters.maxHoldPeriodYearsMax !== undefined) count++;
    
    // Investment criteria additional fields
    if (pendingFilters.decisionMakingProcess?.length) count++;
    if (pendingFilters.investmentCriteriaNotes?.length) count++;
    
    // NOT filters
    if (pendingFilters.notSource?.length) count++;
    if (pendingFilters.notIndustry?.length) count++;
    if (pendingFilters.notCompanyType?.length) count++;
    if (pendingFilters.notWebsiteScrapingStatus?.length) count++;
    if (pendingFilters.notCompanyOverviewStatus?.length) count++;
    if (pendingFilters.notOverviewV2Status?.length) count++;
    if (pendingFilters.notInvestmentCriteriaStatus?.length) count++;
    if (pendingFilters.notContactsEmailVerificationStatus?.length) count++;
    if (pendingFilters.notContactsEnrichmentV2Status?.length) count++;
    if (pendingFilters.notContactsEmailGenerationStatus?.length) count++;
    if (pendingFilters.notContactsEmailSendingStatus?.length) count++;
    if (pendingFilters.notContactsInvestmentCriteriaStatus?.length) count++;
    if (pendingFilters.notInvestmentFocus?.length) count++;
    if (pendingFilters.notCapitalPosition?.length) count++;
    if (pendingFilters.notPropertyTypes?.length) count++;
    if (pendingFilters.notStrategies?.length) count++;
    if (pendingFilters.notPartnerships?.length) count++;
    if (pendingFilters.notFundraisingStatus?.length) count++;
    if (pendingFilters.notLenderType?.length) count++;
    if (pendingFilters.notLoanTypes?.length) count++;
    if (pendingFilters.notStructuredLoanTranche?.length) count++;
    if (pendingFilters.notLoanProgram?.length) count++;
    if (pendingFilters.notRecourseLoan?.length) count++;
    if (pendingFilters.notEligibleBorrower?.length) count++;
    if (pendingFilters.notLienPosition?.length) count++;
    if (pendingFilters.notOwnershipRequirement?.length) count++;
    if (pendingFilters.notRateType?.length) count++;
    if (pendingFilters.notAmortization?.length) count++;
    
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  // Enhanced sort options
  const UNIFIED_SORT_OPTIONS = [
    { value: 'company_name', label: 'Company Name', icon: Building },
    { value: 'created_at', label: 'Created Date', icon: Calendar },
    { value: 'updated_at', label: 'Last Updated Date', icon: Clock },
    { value: 'minimum_deal_size', label: 'Deal Size (Min)', icon: DollarSign },
  ];
  // Initialize search term from current filters
  useEffect(() => {
    if (filters.companyNameSearch) {
      setSearchTerm(filters.companyNameSearch);
      setSearchCriteria('companyNameSearch');
    } else if (filters.companyWebsiteSearch) {
      setSearchTerm(filters.companyWebsiteSearch);
      setSearchCriteria('companyWebsiteSearch');
    }
  }, [filters.companyNameSearch, filters.companyWebsiteSearch]);

  // Handle search functionality - trigger search when search term or criteria changes
  useEffect(() => {
    const timer = setTimeout(() => {
      // Clear other search fields when switching criteria
      const updatedFilters = {
        ...pendingFilters,
        companyNameSearch: searchCriteria === 'companyNameSearch' ? (searchTerm.trim() || undefined) : undefined,
        companyWebsiteSearch: searchCriteria === 'companyWebsiteSearch' ? (searchTerm.trim() || undefined) : undefined,
        page: 1 // Reset to first page when searching
      };
      
      setPendingFilters(updatedFilters);
      setLocalFilters(updatedFilters);
      onFiltersChange(updatedFilters);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchTerm, searchCriteria]);

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  // Get NOT filter state for specific filter
  const getFilterNotState = (filterKey: string): boolean => {
    return filterNotModes[filterKey] || false;
  };

  // Toggle NOT filter mode for specific filter
  const toggleFilterNotMode = (filterKey: string) => {
    setFilterNotModes(prev => ({
      ...prev,
      [filterKey]: !prev[filterKey]
    }));
  };

  // Multi-select component with NOT filter support
  const EnhancedMultiSelect = ({ 
    options, 
    selected, 
    notSelected,
    onChange, 
    onNotChange,
    placeholder, 
    disabled,
    label,
    showNotFilter = false,
    filterKey
  }: {
    options: Array<{value: string, label: string}>,
    selected: string[],
    notSelected?: string[],
    onChange: (values: string[]) => void,
    onNotChange?: (values: string[]) => void,
    placeholder: string,
    disabled?: boolean,
    label: string,
    showNotFilter?: boolean,
    filterKey: string
  }) => {
    const isNotMode = getFilterNotState(filterKey);
    
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium text-gray-700">{label}</Label>
          {showNotFilter && (
            <div className="flex items-center gap-2">
              <EyeIcon className="h-4 w-4 text-gray-400" />
              <Switch
                checked={isNotMode}
                onCheckedChange={() => toggleFilterNotMode(filterKey)}
              />
              <EyeOff className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>
        
        {!isNotMode ? (
          <ReactMultiSelect
            options={options}
            selected={selected || []}
            onChange={onChange}
            placeholder={placeholder}
            disabled={disabled}
            showSelectAll={true}
            selectAllLabel="Select All"
          />
        ) : (
          <ReactMultiSelect
            options={options}
            selected={notSelected || []}
            onChange={onNotChange || (() => {})}
            placeholder={`NOT ${placeholder.toLowerCase()}`}
            disabled={disabled}
            showSelectAll={true}
            selectAllLabel="Exclude All"
          />
        )}
      </div>
    );
  };

  return (
    <>
      {/* Unified Filter Bar V2 */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Unified Filter Button V2 */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen 
                  ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white' 
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
              }`}
            >
              <Database className="h-5 w-5" />
              <span className="font-medium">Filters</span>
              {/* <Badge className="bg-amber-100 text-amber-800 border border-amber-200 ml-1 font-semibold">
                Enhanced
              </Badge> */}
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>

            {/* Search Bar */}
            <div className="flex items-center gap-2">
              <Select 
                value={searchCriteria} 
                onValueChange={(value: 'companyNameSearch' | 'companyWebsiteSearch') => setSearchCriteria(value)}
              >
                <SelectTrigger className="w-auto min-w-[140px] border-gray-200 bg-white shadow-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="companyNameSearch">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Company Name
                    </div>
                  </SelectItem>
                  <SelectItem value="companyWebsiteSearch">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Website
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              
              <Input
                type="text"
                placeholder={`Search by ${searchCriteria === 'companyNameSearch' ? 'company name' : 'website'}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64 border-gray-200 bg-white shadow-sm"
              />
              
              {searchTerm && (
                <Button
                  onClick={() => {
                    setSearchTerm('');
                    setSearchCriteria('companyNameSearch');
                  }}
                  className="text-gray-500 hover:text-red-600 bg-transparent border border-gray-200 hover:border-red-200 hover:bg-red-50 px-3 py-1 text-sm"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Active Filters Count */}
            {activeFilterCount > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied
                </span>
                <Button
                  onClick={handleClearFilters}
                  className="text-gray-500 hover:text-red-600 bg-transparent border border-gray-200 hover:border-red-200 hover:bg-red-50 px-3 py-1 text-sm"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              </div>
            )}

            {/* Enhanced Sort Controls */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
              </div>
              <div className="flex items-center gap-2">
                <Select 
                  value={pendingFilters.sortBy || 'updated_at'} 
                  onValueChange={(value) => {
                    // Instant sorting - apply immediately when field changes
                    const newSortBy = value;
                    const newSortOrder: 'asc' | 'desc' = 'asc'; // Default to ascending on field change
                    const updatedFilters = {
                      ...pendingFilters,
                      sortBy: newSortBy,
                      sortOrder: newSortOrder,
                      page: 1 // Reset to first page when sorting
                    };
                    setPendingFilters(updatedFilters);
                    // Apply filters immediately for instant sorting
                    setLocalFilters(updatedFilters);
                    onFiltersChange(updatedFilters);
                  }}
                >
                  <SelectTrigger className="w-auto min-w-[250px] border-gray-200 bg-white shadow-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    {UNIFIED_SORT_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <option.icon className="h-4 w-4" />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {/* Sort Order Toggle */}
                <Button
                  onClick={() => {
                    // Instant sort order toggle - apply immediately
                    const newSortOrder: 'asc' | 'desc' = pendingFilters.sortOrder === 'asc' ? 'desc' : 'asc';
                    const updatedFilters = {
                      ...pendingFilters,
                      sortOrder: newSortOrder,
                      page: 1 // Reset to first page when changing sort order
                    };
                    setPendingFilters(updatedFilters);
                    // Apply filters immediately for instant sorting
                    setLocalFilters(updatedFilters);
                    onFiltersChange(updatedFilters);
                  }}
                  className={`p-2 rounded-lg border transition-all ${
                    pendingFilters.sortOrder === 'asc' 
                      ? 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100' 
                      : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                  }`}
                  title={`Sort ${pendingFilters.sortOrder === 'asc' ? 'Ascending' : 'Descending'}`}
                >
                  {pendingFilters.sortOrder === 'asc' ? (
                    <ArrowUp className="h-4 w-4" />
                  ) : (
                    <ArrowDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Unified Right Side Filter Panel V2 */}
      <div className={`fixed top-0 right-0 h-full w-[900px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
        isFilterPanelOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Enhanced Panel Header V2 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 rounded-xl bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg">
                  <Database className="h-6 w-6" />
                </div>
                {activeFilterCount > 0 && (
                  <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                    {activeFilterCount > 9 ? '9+' : activeFilterCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  Filters
                  <Badge className="bg-amber-100 text-amber-800 border border-amber-200 ml-2 font-semibold">
                    Enhanced Analytics
                  </Badge>
                </h2>
                <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                  <Table className="h-4 w-4" />
                  Advanced multi-table company filtering with insights data
                </p>
              </div>
            </div>
            <Button
              onClick={() => setIsFilterPanelOpen(false)}
              className="text-gray-500 hover:text-gray-700 bg-white border border-gray-200 p-2 hover:bg-gray-50 transition-colors"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Panel Content - Scrollable */}
          <div className="flex-1 overflow-y-auto p-6 space-y-6">

            {/* Company Search */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-gray-50 to-blue-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Search className="h-5 w-5 text-blue-600" />
                  Company Search
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Company Name Search */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Building className="h-4 w-4 text-blue-600" />
                    Company Name
                  </Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by company name..."
                      value={pendingFilters.companyNameSearch || ''}
                      onChange={(e) => updatePendingFilters({ companyNameSearch: e.target.value || undefined })}
                      onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
                      className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Company Website Search */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Globe className="h-4 w-4 text-green-600" />
                    Company Website
                  </Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by company website..."
                      value={pendingFilters.companyWebsiteSearch || ''}
                      onChange={(e) => updatePendingFilters({ companyWebsiteSearch: e.target.value || undefined })}
                      onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
                      className="pl-10 border-gray-200 focus:border-green-500 focus:ring-1 focus:ring-green-500"
                      disabled={isLoading}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Core Companies Table Filters - Admin Only */}
            {fieldVisibility.isAdmin && (
              <Card className="border-0 shadow-sm bg-gradient-to-r from-cyan-50 to-blue-50">
                <CardHeader 
                  className="pb-4 cursor-pointer hover:bg-cyan-100/50 transition-colors rounded-t-lg" 
                  onClick={() => toggleSection('companies')}
                  title={`Click to ${expandedSections.companies ? 'collapse' : 'expand'} Companies filters`}
                >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Building className="h-5 w-5 text-cyan-600" />
                    Core Companies Table
                    <Badge className="bg-cyan-100 text-cyan-700 border border-cyan-200">
                      Primary Data
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.companies ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.companies ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.companies && (
                <CardContent className="space-y-6">
                  {/* Company Address Filter */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Company Address</Label>
                    <ReactMultiSelect
                      options={coreCompanyOptions.companyAddresses}
                      selected={pendingFilters.companyAddress || []}
                      onChange={(selected: string[]) => updatePendingFilters({ companyAddress: selected })}
                      placeholder={loadingCoreOptions ? "Loading addresses..." : "Select company addresses..."}
                      disabled={isLoading || loadingCoreOptions}
                      showSelectAll={true}
                      selectAllLabel="Select All Addresses"
                    />
                  </div>

                  {/* Company Location */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company City</Label>
                      <ReactMultiSelect
                        options={coreCompanyOptions.companyCities}
                        selected={pendingFilters.companyCity || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyCity: selected })}
                        placeholder={loadingCoreOptions ? "Loading cities..." : "Select cities..."}
                        disabled={isLoading || loadingCoreOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All Cities"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company State</Label>
                      <ReactMultiSelect
                        options={coreCompanyOptions.companyStates}
                        selected={pendingFilters.companyState || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyState: selected })}
                        placeholder={loadingCoreOptions ? "Loading states..." : "Select states..."}
                        disabled={isLoading || loadingCoreOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All States"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Country</Label>
                      <ReactMultiSelect
                        options={coreCompanyOptions.companyCountries}
                        selected={pendingFilters.companyCountry || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyCountry: selected })}
                        placeholder={loadingCoreOptions ? "Loading countries..." : "Select countries..."}
                        disabled={isLoading || loadingCoreOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All Countries"
                      />
                    </div>
                  </div>

                  {/* Industry & Source */}
                  <div className="grid grid-cols-2 gap-4">
                    <EnhancedMultiSelect
                      label="Industry"
                      options={coreCompanyOptions.industries}
                      selected={pendingFilters.industry || []}
                      notSelected={pendingFilters.notIndustry || []}
                      onChange={(selected: string[]) => updatePendingFilters({ industry: selected })}
                      onNotChange={(selected: string[]) => updatePendingFilters({ notIndustry: selected })}
                      placeholder={loadingCoreOptions ? "Loading industries..." : "Select industries..."}
                      disabled={isLoading || loadingCoreOptions}
                      showNotFilter={true}
                      filterKey="industry"
                    />

                    <EnhancedMultiSelect
                      label="Source"
                      options={coreCompanyOptions.sources}
                      selected={pendingFilters.source || []}
                      notSelected={pendingFilters.notSource || []}
                      onChange={(selected: string[]) => updatePendingFilters({ source: selected })}
                      onNotChange={(selected: string[]) => updatePendingFilters({ notSource: selected })}
                      placeholder={loadingCoreOptions ? "Loading sources..." : "Select sources..."}
                      disabled={isLoading || loadingCoreOptions}
                      showNotFilter={true}
                      filterKey="source"
                    />
                  </div>

                  {/* New Filters - Website and Multi-ID */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium">Has Non-Empty Website</span>
                      </div>
                      <Switch
                        checked={pendingFilters.notEmptyCompanyWebsite === true}
                        onCheckedChange={(checked) => updatePendingFilters({ notEmptyCompanyWebsite: checked || undefined })}
                        disabled={isLoading}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company IDs (comma-separated)</Label>
                      <Input
                        placeholder="Enter company IDs separated by commas (e.g., 1,2,3)"
                        value={pendingFilters.companyIds?.join(',') || ''}
                        onChange={(e) => updatePendingFilters({ 
                          companyIds: e.target.value ? e.target.value.split(',').map(id => id.trim()).filter(Boolean) : undefined 
                        })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <p className="text-xs text-gray-500">
                        Filter by specific company IDs. Enter multiple IDs separated by commas.
                      </p>
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Websites (comma-separated)</Label>
                      <Input
                        placeholder="Enter website URLs separated by commas"
                        value={pendingFilters.companyWebsites?.join(',') || ''}
                        onChange={(e) => updatePendingFilters({ 
                          companyWebsites: e.target.value ? e.target.value.split(',').map(website => website.trim()).filter(Boolean) : undefined 
                        })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <p className="text-xs text-gray-500">
                        Filter by specific company websites. Enter multiple URLs separated by commas.
                      </p>
                    </div>
                  </div>

                  {/* Processing Status Groups */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Website Scraping</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.websiteScrapingStatus || []}
                        notSelected={pendingFilters.notWebsiteScrapingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ websiteScrapingStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notWebsiteScrapingStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="websiteScrapingStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Overview</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.overviewV2Status || []}
                        notSelected={pendingFilters.notOverviewV2Status || []}
                        onChange={(selected: string[]) => updatePendingFilters({ overviewV2Status: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notOverviewV2Status: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="overviewV2Status"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Investment Criteria</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.investmentCriteriaStatus || []}
                        notSelected={pendingFilters.notInvestmentCriteriaStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ investmentCriteriaStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notInvestmentCriteriaStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="investmentCriteriaStatus"
                      />
                    </div>
                  </div>
                </CardContent>
              )}
              </Card>
            )}

            {/* Company Overview V2 Filters - Admin Only */}
            {fieldVisibility.isAdmin && (
            <Card className="border-0 shadow-sm bg-gradient-to-r from-emerald-50 to-teal-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-emerald-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('company_overview_v2')}
                title={`Click to ${expandedSections.company_overview_v2 ? 'collapse' : 'expand'} Company Overview V2 filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Brain className="h-5 w-5 text-emerald-600" />
                    Company Overview
                    <Badge className="bg-emerald-100 text-emerald-700 border border-emerald-200">
                      AI Enhanced
                    </Badge>
                    <Badge className="bg-amber-100 text-amber-800 border border-amber-200 text-xs font-semibold">
                      NEW
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.company_overview_v2 ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.company_overview_v2 ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.company_overview_v2 && (
                <CardContent className="space-y-6">
                  {/* Core Company Information V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Building2Icon className="h-5 w-5 text-blue-600" />
                      <h4 className="font-medium text-gray-800">Core Company Information V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-1 gap-4">
                      <EnhancedMultiSelect
                        label="Company Type"
                        options={overviewV2Options.companyTypes}
                        selected={pendingFilters.companyType || []}
                        notSelected={pendingFilters.notCompanyType || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyType: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notCompanyType: selected })}
                        placeholder={loadingOverviewV2Options ? "Loading company types..." : "Select company types..."}
                        disabled={isLoading || loadingOverviewV2Options}
                        showNotFilter={true}
                        filterKey="companyType"
                      />
                    </div>

                    {/* Founded Year Range */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Founded Year Range</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <Input
                          type="number"
                          placeholder="Min Year"
                          value={getRangeInputValue('foundedYearMin', pendingFilters.foundedYearMin)}
                          onChange={(e) => updateLocalRangeInput('foundedYearMin', e.target.value)}
                          onBlur={(e) => applyRangeFilter('foundedYearMin', e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('foundedYearMin', e.currentTarget.value)}
                          disabled={isLoading}
                          className="bg-white border-gray-200"
                        />
                        <Input
                          type="number"
                          placeholder="Max Year"
                          value={getRangeInputValue('foundedYearMax', pendingFilters.foundedYearMax)}
                          onChange={(e) => updateLocalRangeInput('foundedYearMax', e.target.value)}
                          onBlur={(e) => applyRangeFilter('foundedYearMax', e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('foundedYearMax', e.currentTarget.value)}
                          disabled={isLoading}
                          className="bg-white border-gray-200"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Investment & Strategy V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-green-600" />
                      <h4 className="font-medium text-gray-800">Investment & Strategy V2</h4>
                    </div>
                    
                    <EnhancedMultiSelect
                      label="Investment Focus"
                      options={overviewV2Options.investmentFocus}
                      selected={pendingFilters.investmentFocus || []}
                      notSelected={pendingFilters.notInvestmentFocus || []}
                      onChange={(selected: string[]) => updatePendingFilters({ investmentFocus: selected })}
                      onNotChange={(selected: string[]) => updatePendingFilters({ notInvestmentFocus: selected })}
                      placeholder={loadingOverviewV2Options ? "Loading investment focus..." : "Select investment focus..."}
                      disabled={isLoading || loadingOverviewV2Options}
                      showNotFilter={true}
                      filterKey="investmentFocus"
                    />
                  </div>

                  {/* Contact Information V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Phone className="h-5 w-5 text-purple-600" />
                      <h4 className="font-medium text-gray-800">Contact Information V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Main Phone</span>
                          <Switch
                            checked={pendingFilters.hasMainPhone === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasMainPhone: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Secondary Phone</span>
                          <Switch
                            checked={pendingFilters.hasSecondaryPhone === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasSecondaryPhone: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Main Email</span>
                          <Switch
                            checked={pendingFilters.hasMainEmail === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasMainEmail: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Secondary Email</span>
                          <Switch
                            checked={pendingFilters.hasSecondaryEmail === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasSecondaryEmail: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <TwitterIcon className="h-4 w-4 text-blue-500" />
                            <span className="text-sm">Has Twitter</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasTwitter === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasTwitter: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has LinkedIn</span>
                          <Switch
                            checked={pendingFilters.hasCompanyLinkedin === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasCompanyLinkedin: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Financial Metrics V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-green-600" />
                      <h4 className="font-medium text-gray-800">Financial Metrics V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      {/* Fund Size Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Fund Size Range (USD)</Label>
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            type="number"
                            placeholder="Min (USD)"
                            value={getRangeInputValue('fundSizeMin', pendingFilters.fundSizeMin)}
                            onChange={(e) => updateLocalRangeInput('fundSizeMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('fundSizeMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('fundSizeMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max (USD)"
                            value={getRangeInputValue('fundSizeMax', pendingFilters.fundSizeMax)}
                            onChange={(e) => updateLocalRangeInput('fundSizeMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('fundSizeMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('fundSizeMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* AUM Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">AUM Range (USD)</Label>
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            type="number"
                            placeholder="Min (USD)"
                            value={getRangeInputValue('aumMin', pendingFilters.aumMin)}
                            onChange={(e) => updateLocalRangeInput('aumMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('aumMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('aumMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max (USD)"
                            value={getRangeInputValue('aumMax', pendingFilters.aumMax)}
                            onChange={(e) => updateLocalRangeInput('aumMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('aumMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('aumMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      {/* Number of Properties Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Properties</Label>
                        <div className="space-y-2">
                          <Input
                            type="number"
                            placeholder="Min"
                            value={getRangeInputValue('numberOfPropertiesMin', pendingFilters.numberOfPropertiesMin)}
                            onChange={(e) => updateLocalRangeInput('numberOfPropertiesMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfPropertiesMin', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max"
                            value={getRangeInputValue('numberOfPropertiesMax', pendingFilters.numberOfPropertiesMax)}
                            onChange={(e) => updateLocalRangeInput('numberOfPropertiesMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfPropertiesMax', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Number of Offices Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Offices</Label>
                        <div className="space-y-2">
                          <Input
                            type="number"
                            placeholder="Min"
                            value={getRangeInputValue('numberOfOfficesMin', pendingFilters.numberOfOfficesMin)}
                            onChange={(e) => updateLocalRangeInput('numberOfOfficesMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfOfficesMin', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max"
                            value={getRangeInputValue('numberOfOfficesMax', pendingFilters.numberOfOfficesMax)}
                            onChange={(e) => updateLocalRangeInput('numberOfOfficesMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfOfficesMax', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Number of Employees Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Employees</Label>
                        <div className="space-y-2">
                          <Input
                            type="number"
                            placeholder="Min"
                            value={getRangeInputValue('numberOfEmployeesMin', pendingFilters.numberOfEmployeesMin)}
                            onChange={(e) => updateLocalRangeInput('numberOfEmployeesMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfEmployeesMin', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max"
                            value={getRangeInputValue('numberOfEmployeesMax', pendingFilters.numberOfEmployeesMax)}
                            onChange={(e) => updateLocalRangeInput('numberOfEmployeesMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfEmployeesMax', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Partnership & Leadership V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Handshake className="h-5 w-5 text-blue-600" />
                      <h4 className="font-medium text-gray-800">Partnership & Leadership V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <EnhancedMultiSelect
                        label="Partnerships"
                        options={overviewV2Options.partnerships}
                        selected={pendingFilters.partnerships || []}
                        notSelected={pendingFilters.notPartnerships || []}
                        onChange={(selected: string[]) => updatePendingFilters({ partnerships: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notPartnerships: selected })}
                        placeholder={loadingOverviewV2Options ? "Loading partnerships..." : "Select partnerships..."}
                        disabled={isLoading || loadingOverviewV2Options}
                        showNotFilter={true}
                        filterKey="partnerships"
                      />

                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Key Executives</Label>
                        <ReactMultiSelect
                          options={overviewV2Options.keyExecutives}
                          selected={pendingFilters.keyExecutives || []}
                          onChange={(selected: string[]) => updatePendingFilters({ keyExecutives: selected })}
                          placeholder={loadingOverviewV2Options ? "Loading executives..." : "Select executives..."}
                          disabled={isLoading || loadingOverviewV2Options}
                          showSelectAll={true}
                          selectAllLabel="Select All Executives"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Key Equity Partners</Label>
                        <ReactMultiSelect
                          options={overviewV2Options.keyEquityPartners}
                          selected={pendingFilters.keyEquityPartners || []}
                          onChange={(selected: string[]) => updatePendingFilters({ keyEquityPartners: selected })}
                          placeholder={loadingOverviewV2Options ? "Loading equity partners..." : "Select equity partners..."}
                          disabled={isLoading || loadingOverviewV2Options}
                          showSelectAll={true}
                          selectAllLabel="Select All Equity Partners"
                        />
                      </div>
                      
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Key Debt Partners</Label>
                        <ReactMultiSelect
                          options={overviewV2Options.keyDebtPartners}
                          selected={pendingFilters.keyDebtPartners || []}
                          onChange={(selected: string[]) => updatePendingFilters({ keyDebtPartners: selected })}
                          placeholder={loadingOverviewV2Options ? "Loading debt partners..." : "Select debt partners..."}
                          disabled={isLoading || loadingOverviewV2Options}
                          showSelectAll={true}
                          selectAllLabel="Select All Debt Partners"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Market Positioning & Strategy V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-indigo-600" />
                      <h4 className="font-medium text-gray-800">Market Positioning & Strategy V2</h4>
                    </div>
                    

                    {/* Boolean Strategy Filters */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <TreePine className="h-4 w-4 text-green-500" />
                            <span className="text-sm">Sustainability/ESG Focus</span>
                          </div>
                          <Switch
                            checked={pendingFilters.sustainabilityEsgFocus === true}
                            onCheckedChange={(checked) => updatePendingFilters({ sustainabilityEsgFocus: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Smartphone className="h-4 w-4 text-blue-500" />
                            <span className="text-sm">Technology/PropTech Adoption</span>
                          </div>
                          <Switch
                            checked={pendingFilters.technologyProptechAdoption === true}
                            onCheckedChange={(checked) => updatePendingFilters({ technologyProptechAdoption: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Construction className="h-4 w-4 text-orange-500" />
                            <span className="text-sm">Adaptive Reuse Experience</span>
                          </div>
                          <Switch
                            checked={pendingFilters.adaptiveReuseExperience === true}
                            onCheckedChange={(checked) => updatePendingFilters({ adaptiveReuseExperience: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Gavel className="h-4 w-4 text-purple-500" />
                            <span className="text-sm">Regulatory/Zoning Expertise</span>
                          </div>
                          <Switch
                            checked={pendingFilters.regulatoryZoningExpertise === true}
                            onCheckedChange={(checked) => updatePendingFilters({ regulatoryZoningExpertise: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Fund & Investment Information V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <PieChart className="h-5 w-5 text-yellow-600" />
                      <h4 className="font-medium text-gray-800">Fund & Investment Information V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <EnhancedMultiSelect
                        label="Fundraising Status"
                        options={overviewV2Options.fundraisingStatuses}
                        selected={pendingFilters.fundraisingStatus || []}
                        notSelected={pendingFilters.notFundraisingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ fundraisingStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notFundraisingStatus: selected })}
                        placeholder={loadingOverviewV2Options ? "Loading fundraising statuses..." : "Select fundraising statuses..."}
                        disabled={isLoading || loadingOverviewV2Options}
                        showNotFilter={true}
                        filterKey="fundraisingStatus"
                      />

                      <EnhancedMultiSelect
                        label="Lender Type"
                        options={overviewV2Options.lenderTypes}
                        selected={pendingFilters.lenderType || []}
                        notSelected={pendingFilters.notLenderType || []}
                        onChange={(selected: string[]) => updatePendingFilters({ lenderType: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notLenderType: selected })}
                        placeholder={loadingOverviewV2Options ? "Loading lender types..." : "Select lender types..."}
                        disabled={isLoading || loadingOverviewV2Options}
                        showNotFilter={true}
                        filterKey="lenderType"
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
            )}

            {/* Contact Processors Table Filters - Admin Only */}
            {fieldVisibility.isAdmin && (
            <Card className="border-0 shadow-sm bg-gradient-to-r from-orange-50 to-red-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-orange-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('contact_processors')}
                title={`Click to ${expandedSections.contact_processors ? 'collapse' : 'expand'} Contact Processors filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <UserCog className="h-5 w-5 text-orange-600" />
                    Contact Processors Table
                    <Badge className="bg-orange-100 text-orange-700 border border-orange-200">
                      Related Contact Data
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.contact_processors ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.contact_processors ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.contact_processors && (
                <CardContent className="space-y-4">
                  {/* Has Contacts Filter */}
                  <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Has Associated Contacts</span>
                    </div>
                    <Switch
                      checked={pendingFilters.hasContacts === true}
                      onCheckedChange={(checked) => updatePendingFilters({ hasContacts: checked || undefined })}
                      disabled={isLoading}
                    />
                  </div>

                  {/* Contact Processing Status Groups */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Email Verification Status</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEmailVerificationStatuses}
                        selected={pendingFilters.contactsEmailVerificationStatus || []}
                        notSelected={pendingFilters.notContactsEmailVerificationStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEmailVerificationStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEmailVerificationStatus: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEmailVerificationStatus"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Enrichment Status</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEnrichmentV2Statuses}
                        selected={pendingFilters.contactsEnrichmentV2Status || []}
                        notSelected={pendingFilters.notContactsEnrichmentV2Status || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEnrichmentV2Status: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEnrichmentV2Status: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEnrichmentV2Status"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Investment Criteria Status</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ value: option.value, label: option.label }))}
                        selected={pendingFilters.contactsInvestmentCriteriaStatus || []}
                        notSelected={pendingFilters.notContactsInvestmentCriteriaStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsInvestmentCriteriaStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsInvestmentCriteriaStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="contactsInvestmentCriteriaStatus"
                      />
                  </div>
                  </div>
                  
                  {/* Email Processing Status Groups */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Email Generation Status</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEmailGenerationStatuses}
                        selected={pendingFilters.contactsEmailGenerationStatus || []}
                        notSelected={pendingFilters.notContactsEmailGenerationStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEmailGenerationStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEmailGenerationStatus: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEmailGenerationStatus"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Email Sending Status</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEmailSendingStatuses}
                        selected={pendingFilters.contactsEmailSendingStatus || []}
                        notSelected={pendingFilters.notContactsEmailSendingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEmailSendingStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEmailSendingStatus: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEmailSendingStatus"
                      />
                    </div>
                  </div>

                </CardContent>
              )}
            </Card>
            )}

            {/* Investment Criteria V2 Enhanced Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-purple-50 to-pink-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-purple-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('investment_criteria')}
                title={`Click to ${expandedSections.investment_criteria ? 'collapse' : 'expand'} Investment Criteria V2 Enhanced filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Target className="h-5 w-5 text-purple-600" />
                    Investment Criteria
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.investment_criteria ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.investment_criteria ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.investment_criteria && (
                <CardContent className="space-y-6">
                  {/* Capital Position Selection - Always visible */}
                  <EnhancedMultiSelect
                    label="Capital Position"
                    options={investmentCriteriaOptions.capitalPositions}
                    selected={pendingFilters.capitalPosition || []}
                    notSelected={pendingFilters.notCapitalPosition || []}
                    onChange={(selected: string[]) => updatePendingFilters({ capitalPosition: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notCapitalPosition: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading capital positions..." : "Select capital positions..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={fieldVisibility.isAdmin}
                    filterKey="capitalPosition"
                  />
                  
                  {/* Basic Investment Criteria - Always visible */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 pb-2 border-b border-gray-200">
                      <Target className="h-4 w-4 text-blue-600" />
                      <h4 className="font-medium text-gray-800">Basic Investment Criteria</h4>
                    </div>
                    
                    {/* Deal Size Range */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Deal Size Range (enter whole numbers, e.g., 1000000 for $1M)</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <Input
                          type="number"
                          placeholder="Min (whole number)"
                          value={getRangeInputValue('dealSizeMin', pendingFilters.dealSizeMin)}
                          onChange={(e) => updateLocalRangeInput('dealSizeMin', e.target.value)}
                          onBlur={(e) => applyRangeFilter('dealSizeMin', e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('dealSizeMin', e.currentTarget.value)}
                          disabled={isLoading}
                          className="bg-white border-gray-200"
                        />
                        <Input
                          type="number"
                          placeholder="Max (whole number)"
                          value={getRangeInputValue('dealSizeMax', pendingFilters.dealSizeMax)}
                          onChange={(e) => updateLocalRangeInput('dealSizeMax', e.target.value)}
                          onBlur={(e) => applyRangeFilter('dealSizeMax', e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('dealSizeMax', e.currentTarget.value)}
                          disabled={isLoading}
                          className="bg-white border-gray-200"
                        />
                      </div>
                    </div>

                    {/* Property Types */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Property Types</Label>
                      <NestedMappingSelector
                        mappingType="Property Type"
                        selectedValues={pendingFilters.propertyTypes || []}
                        onSelectionChange={(values) => {
                          updatePendingFilters({ propertyTypes: values });
                        }}
                        label=""
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading property types..." : "Select property types or type to add new..."}
                        showSelectAll={true}
                        selectAllLabel="Select All Property Types"
                        mappingData={mappingData.propertyTypeRaw}
                        allowNewOptions={true}
                        onAddNewOption={(newValue) => {
                          console.log(`Added new property type: ${newValue}`);
                        }}
                      />
                    </div>

                    {/* Strategies */}
                    <EnhancedMultiSelect
                      label="Strategies"
                      options={investmentCriteriaOptions.strategies}
                      selected={pendingFilters.strategies || []}
                      notSelected={pendingFilters.notStrategies || []}
                      onChange={(selected: string[]) => updatePendingFilters({ strategies: selected })}
                      onNotChange={(selected: string[]) => updatePendingFilters({ notStrategies: selected })}
                      placeholder={loadingInvestmentCriteriaOptions ? "Loading strategies..." : "Select strategies..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions}
                      showNotFilter={fieldVisibility.isAdmin}
                      filterKey="strategies"
                    />

                    {/* Geographic Filters */}
                    <div className="grid grid-cols-2 gap-4">
                      <EnhancedMultiSelect
                        label="Countries"
                        options={investmentCriteriaOptions.countries}
                        selected={pendingFilters.countries || []}
                        onChange={(selected: string[]) => updatePendingFilters({ countries: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading countries..." : "Select countries..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showNotFilter={false}
                        filterKey="countries"
                      />
                      
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-gray-700">U.S. Regions</Label>
                        <NestedMappingSelector
                          mappingType="U.S Regions"
                          selectedValues={pendingFilters.regions || []}
                          onSelectionChange={(values) => {
                            updatePendingFilters({ regions: values });
                          }}
                          label=""
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading regions..." : "Select regions or type to add new..."}
                          showSelectAll={true}
                          selectAllLabel="Select All Regions"
                          mappingData={mappingData.regionsRaw}
                          allowNewOptions={true}
                          onAddNewOption={(newValue) => {
                            console.log(`Added new region: ${newValue}`);
                          }}
                        />
                      </div>
                    </div>

                    {/* States and Cities */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-gray-700">U.S. States</Label>
                        <EnhancedMultiSelect
                          label=""
                          options={getFilteredStates}
                          selected={pendingFilters.states || []}
                          onChange={(values) => {
                            updatePendingFilters({ states: values });
                          }}
                          placeholder={pendingFilters.regions && pendingFilters.regions.length > 0 
                            ? `Select states for selected regions...` 
                            : "Select states..."}
                          filterKey="states"
                        />
                      </div>

                      <EnhancedMultiSelect
                        label="Cities"
                        options={investmentCriteriaOptions.cities}
                        selected={pendingFilters.cities || []}
                        onChange={(selected: string[]) => updatePendingFilters({ cities: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading cities..." : "Select cities..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showNotFilter={false}
                        filterKey="cities"
                      />
                    </div>
                  </div>

                  {/* Equity Investment Criteria Section - Only visible when equity capital position is selected */}
                  {fieldVisibility.showEquitySection && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 pb-2 border-b border-green-200 bg-green-50 p-3 rounded-lg">
                        <TrendingUp className="h-5 w-5 text-green-600" />
                        <h4 className="font-medium text-green-800">Equity Investment Criteria</h4>
                        <Badge className="bg-green-100 text-green-800 border border-green-200 text-xs">
                          {fieldVisibility.isAdmin ? 'All Fields' : 'Limited Fields'}
                        </Badge>
                      </div>
                      
                      {!fieldVisibility.hasCapitalPosition && (
                        <div className="text-sm text-gray-600 bg-yellow-50 p-3 rounded border-l-4 border-yellow-400">
                          ⚠️ Select equity capital positions above to access equity-specific filters
                        </div>
                      )}

                      {/* Target Return Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Target Return Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            placeholder="Min (%)"
                            value={getRangeInputValue('targetReturnMin', pendingFilters.targetReturnMin)}
                            onChange={(e) => updateLocalRangeInput('targetReturnMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('targetReturnMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('targetReturnMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max (%)"
                            value={getRangeInputValue('targetReturnMax', pendingFilters.targetReturnMax)}
                            onChange={(e) => updateLocalRangeInput('targetReturnMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('targetReturnMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('targetReturnMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Minimum IRR Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Minimum IRR Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min IRR (%)"
                            value={getRangeInputValue('minimumIrrMin', pendingFilters.minimumIrrMin)}
                            onChange={(e) => updateLocalRangeInput('minimumIrrMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minimumIrrMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minimumIrrMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max IRR (%)"
                            value={getRangeInputValue('minimumIrrMax', pendingFilters.minimumIrrMax)}
                            onChange={(e) => updateLocalRangeInput('minimumIrrMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minimumIrrMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minimumIrrMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Minimum Yield on Cost Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Minimum Yield on Cost Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min yield (%)"
                            value={getRangeInputValue('minimumYieldOnCostMin', pendingFilters.minimumYieldOnCostMin)}
                            onChange={(e) => updateLocalRangeInput('minimumYieldOnCostMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minimumYieldOnCostMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minimumYieldOnCostMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max yield (%)"
                            value={getRangeInputValue('minimumYieldOnCostMax', pendingFilters.minimumYieldOnCostMax)}
                            onChange={(e) => updateLocalRangeInput('minimumYieldOnCostMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minimumYieldOnCostMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minimumYieldOnCostMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Target Cash on Cash Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Target Cash on Cash Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min Cash on Cash (%)"
                            value={getRangeInputValue('targetCashOnCashMin', pendingFilters.targetCashOnCashMin)}
                            onChange={(e) => updateLocalRangeInput('targetCashOnCashMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('targetCashOnCashMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('targetCashOnCashMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max Cash on Cash (%)"
                            value={getRangeInputValue('targetCashOnCashMax', pendingFilters.targetCashOnCashMax)}
                            onChange={(e) => updateLocalRangeInput('targetCashOnCashMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('targetCashOnCashMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('targetCashOnCashMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Hold Period Years Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Hold Period Years Range</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min Hold Period (years)"
                            value={getRangeInputValue('minHoldPeriodYearsMin', pendingFilters.minHoldPeriodYearsMin)}
                            onChange={(e) => updateLocalRangeInput('minHoldPeriodYearsMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minHoldPeriodYearsMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minHoldPeriodYearsMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max Hold Period (years)"
                            value={getRangeInputValue('maxHoldPeriodYearsMax', pendingFilters.maxHoldPeriodYearsMax)}
                            onChange={(e) => updateLocalRangeInput('maxHoldPeriodYearsMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('maxHoldPeriodYearsMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxHoldPeriodYearsMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Admin-only Equity Fields */}
                      {fieldVisibility.isAdmin && (
                        <>
                          {/* Ownership Requirement */}
                          <div className="space-y-3">
                            <Label className="text-sm font-medium text-gray-700">Ownership Requirement</Label>
                            <ReactMultiSelect
                              options={investmentCriteriaOptions.ownershipRequirements || []}
                              selected={pendingFilters.ownershipRequirement || []}
                              onChange={(selected: string[]) => updatePendingFilters({ ownershipRequirement: selected })}
                              placeholder={loadingInvestmentCriteriaOptions ? "Loading ownership requirements..." : "Select ownership requirements..."}
                              disabled={isLoading || loadingInvestmentCriteriaOptions}
                            />
                          </div>

                          {/* Max Leverage Tolerance Range */}
                          <div className="space-y-3">
                            <Label className="text-sm font-medium text-gray-700">Max Leverage Tolerance Range (%)</Label>
                            <div className="grid grid-cols-2 gap-4">
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="Min leverage (%)"
                                value={getRangeInputValue('maxLeverageToleranceMin', pendingFilters.maxLeverageToleranceMin)}
                                onChange={(e) => updateLocalRangeInput('maxLeverageToleranceMin', e.target.value)}
                                onBlur={(e) => applyRangeFilter('maxLeverageToleranceMin', e.target.value)}
                                onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxLeverageToleranceMin', e.currentTarget.value)}
                                disabled={isLoading}
                                className="bg-white border-gray-200"
                              />
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="Max leverage (%)"
                                value={getRangeInputValue('maxLeverageToleranceMax', pendingFilters.maxLeverageToleranceMax)}
                                onChange={(e) => updateLocalRangeInput('maxLeverageToleranceMax', e.target.value)}
                                onBlur={(e) => applyRangeFilter('maxLeverageToleranceMax', e.target.value)}
                                onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxLeverageToleranceMax', e.currentTarget.value)}
                                disabled={isLoading}
                                className="bg-white border-gray-200"
                              />
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  )}

                  {/* Debt Investment Criteria Section - Only visible when debt capital position is selected */}
                  {fieldVisibility.showDebtSection && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 pb-2 border-b border-amber-200 bg-amber-50 p-3 rounded-lg">
                        <DollarSign className="h-5 w-5 text-amber-600" />
                        <h4 className="font-medium text-amber-800">Debt Investment Criteria</h4>
                        <Badge className="bg-amber-100 text-amber-800 border border-amber-200 text-xs">
                          {fieldVisibility.isAdmin ? 'All Fields' : 'Limited Fields'}
                        </Badge>
                      </div>
                      
                      {!fieldVisibility.hasCapitalPosition && (
                        <div className="text-sm text-gray-600 bg-yellow-50 p-3 rounded border-l-4 border-yellow-400">
                          ⚠️ Select debt capital positions above to access debt-specific filters
                        </div>
                      )}

                      {/* Min Loan DSCR Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan DSCR Range</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min DSCR"
                            value={getRangeInputValue('minLoanDscrMin', pendingFilters.minLoanDscrMin)}
                            onChange={(e) => updateLocalRangeInput('minLoanDscrMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minLoanDscrMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minLoanDscrMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max DSCR"
                            value={getRangeInputValue('minLoanDscrMax', pendingFilters.minLoanDscrMax)}
                            onChange={(e) => updateLocalRangeInput('minLoanDscrMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minLoanDscrMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minLoanDscrMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Recourse Loan */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Recourse Loan</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.recourseLoans}
                          selected={pendingFilters.recourseLoan || []}
                          onChange={(selected: string[]) => updatePendingFilters({ recourseLoan: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading recourse options..." : "Select recourse options..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Loan Debt Yield */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Min Debt Yield</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.loanMinDebtYield || []}
                          selected={pendingFilters.loanMinDebtYield || []}
                          onChange={(selected: string[]) => updatePendingFilters({ loanMinDebtYield: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading debt yield options..." : "Select debt yield options..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Loan to Value Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan to Value (LTV) Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min LTV (%)"
                            value={getRangeInputValue('loanToValueMinMin', pendingFilters.loanToValueMinMin)}
                            onChange={(e) => updateLocalRangeInput('loanToValueMinMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanToValueMinMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanToValueMinMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max LTV (%)"
                            value={getRangeInputValue('loanToValueMaxMax', pendingFilters.loanToValueMaxMax)}
                            onChange={(e) => updateLocalRangeInput('loanToValueMaxMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanToValueMaxMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanToValueMaxMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Loan to Cost Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan to Cost (LTC) Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min LTC (%)"
                            value={getRangeInputValue('loanToCostMinMin', pendingFilters.loanToCostMinMin)}
                            onChange={(e) => updateLocalRangeInput('loanToCostMinMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanToCostMinMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanToCostMinMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max LTC (%)"
                            value={getRangeInputValue('loanToCostMaxMax', pendingFilters.loanToCostMaxMax)}
                            onChange={(e) => updateLocalRangeInput('loanToCostMaxMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanToCostMaxMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanToCostMaxMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Loan Term Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Term Range (months)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            placeholder="Min term"
                            value={getRangeInputValue('minLoanTermMin', pendingFilters.minLoanTermMin)}
                            onChange={(e) => updateLocalRangeInput('minLoanTermMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minLoanTermMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minLoanTermMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max term"
                            value={getRangeInputValue('maxLoanTermMax', pendingFilters.maxLoanTermMax)}
                            onChange={(e) => updateLocalRangeInput('maxLoanTermMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('maxLoanTermMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxLoanTermMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Admin-only Debt Fields */}
                      {fieldVisibility.isAdmin && (
                        <>
                          {/* Loan Types */}
                          <div className="space-y-3">
                            <Label className="text-sm font-medium text-gray-700">Loan Types</Label>
                            <ReactMultiSelect
                              options={investmentCriteriaOptions.loanTypes}
                              selected={pendingFilters.loanTypes || []}
                              onChange={(selected: string[]) => updatePendingFilters({ loanTypes: selected })}
                              placeholder={loadingInvestmentCriteriaOptions ? "Loading loan types..." : "Select loan types..."}
                              disabled={isLoading || loadingInvestmentCriteriaOptions}
                            />
                          </div>

                          {/* Structured Loan Tranches */}
                          <div className="space-y-3">
                            <Label className="text-sm font-medium text-gray-700">Structured Loan Tranches</Label>
                            <ReactMultiSelect
                              options={investmentCriteriaOptions.structuredLoanTranches}
                              selected={pendingFilters.structuredLoanTranche || []}
                              onChange={(selected: string[]) => updatePendingFilters({ structuredLoanTranche: selected })}
                              placeholder={loadingInvestmentCriteriaOptions ? "Loading tranches..." : "Select tranches..."}
                              disabled={isLoading || loadingInvestmentCriteriaOptions}
                            />
                          </div>

                          {/* Loan Programs */}
                          <div className="space-y-3">
                            <Label className="text-sm font-medium text-gray-700">Loan Programs</Label>
                            <ReactMultiSelect
                              options={investmentCriteriaOptions.loanPrograms}
                              selected={pendingFilters.loanProgram || []}
                              onChange={(selected: string[]) => updatePendingFilters({ loanProgram: selected })}
                              placeholder={loadingInvestmentCriteriaOptions ? "Loading programs..." : "Select programs..."}
                              disabled={isLoading || loadingInvestmentCriteriaOptions}
                            />
                          </div>

                          {/* Eligible Borrower */}
                          <div className="space-y-3">
                            <Label className="text-sm font-medium text-gray-700">Eligible Borrower</Label>
                            <ReactMultiSelect
                              options={investmentCriteriaOptions.eligibleBorrowers || []}
                              selected={pendingFilters.eligibleBorrower || []}
                              onChange={(selected: string[]) => updatePendingFilters({ eligibleBorrower: selected })}
                              placeholder={loadingInvestmentCriteriaOptions ? "Loading borrower types..." : "Select eligible borrowers..."}
                              disabled={isLoading || loadingInvestmentCriteriaOptions}
                            />
                          </div>

                          {/* Lien Position */}
                          <div className="space-y-3">
                            <Label className="text-sm font-medium text-gray-700">Lien Position</Label>
                            <ReactMultiSelect
                              options={investmentCriteriaOptions.lienPositions || []}
                              selected={pendingFilters.lienPosition || []}
                              onChange={(selected: string[]) => updatePendingFilters({ lienPosition: selected })}
                              placeholder={loadingInvestmentCriteriaOptions ? "Loading lien positions..." : "Select lien positions..."}
                              disabled={isLoading || loadingInvestmentCriteriaOptions}
                            />
                          </div>
                        </>
                      )}
                    </div>
                  )}

                  {/* Additional Investment Criteria - Always visible */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 pb-2 border-b border-gray-200">
                      <FileText className="h-4 w-4 text-gray-600" />
                      <h4 className="font-medium text-gray-800">Additional Criteria</h4>
                    </div>
                    
                    {/* Investment Criteria Notes */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Investment Criteria Notes</Label>
                      <Input
                        type="text"
                        placeholder="Search in investment criteria notes..."
                        value={pendingFilters.investmentCriteriaNotes?.join(',') || ''}
                        onChange={(e) => updatePendingFilters({ 
                          investmentCriteriaNotes: e.target.value ? e.target.value.split(',').map(note => note.trim()).filter(Boolean) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>

                </CardContent>
              )}
            </Card>

          </div>

          {/* Enhanced Panel Footer V2 */}
          <div className="border-t border-gray-200 p-6 bg-gradient-to-r from-gray-50 to-purple-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {activeFilterCount > 0 ? (
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-gray-700">
                      {activeFilterCount} Advanced Filter{activeFilterCount !== 1 ? 's' : ''} Active
                    </span>
                    <Badge className="bg-amber-100 text-amber-800 border border-amber-200 font-semibold">
                      V2 Enhanced Query
                    </Badge>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <span>No filters applied - showing all company data with V2 overview</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-3">
                {/* Quick Reset */}
                {activeFilterCount > 0 && (
                  <Button
                    onClick={handleClearFilters}
                    className="text-gray-600 hover:text-red-600 bg-white border border-gray-200 hover:border-red-200 hover:bg-red-50 px-4 py-2 text-sm transition-colors"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset All
                  </Button>
                )}
                
                {/* Apply Button */}
                <Button
                  onClick={activeFilterCount > 0 ? applyFilters : () => setIsFilterPanelOpen(false)}
                  className={`px-6 py-2 font-medium shadow-md hover:shadow-lg transition-all duration-200 ${
                    activeFilterCount > 0 
                      ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white' 
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                  }`}
                >
                  {activeFilterCount > 0 ? (
                    <>
                      <Database className="h-4 w-4 mr-2" />
                      Apply {activeFilterCount} V2 Filter{activeFilterCount !== 1 ? 's' : ''}
                    </>
                  ) : (
                    <>
                      <ChevronRight className="h-4 w-4 mr-2" />
                      Close Panel
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
