"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, RefreshCw, Play, Database, CheckSquare, Settings, Zap, Sparkles, Settings2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import CompanyCard from "./CompanyCard"
import Pagination from "../../people/list-components/Pagination";
import PaginationSizeSelector from "../../people/list-components/PaginationSizeSelector";
import CompanyUnifiedFiltersV2Component from "./CompanyUnifiedFiltersV2";
import { Company } from "../shared/types";
import { useRouter, useSearchParams } from "next/navigation";
import type { CompanyUnifiedFiltersV2 as CompanyUnifiedFiltersV2Type } from "../../../../types/unified-filters-v2";

type CompanyProcessingStage = 'website_scraping' | 'company_overview_v2' | 'company_investment_criteria';

interface ProcessingJob {
  stage: CompanyProcessingStage;
  isExecuting: boolean;
}

interface UnifiedCompanyData extends Company {
  // Investment criteria data
  criteria_id?: number
  entity_type?: string
  entity_name?: string
  capital_position?: string[]
  loan_types?: string[]
  minimum_deal_size?: number
  maximum_deal_size?: number
  target_return?: number
  historical_irr?: number
  historical_em?: number
  property_types?: string[]
  strategies?: string[]
  regions?: string[]
  states?: string[]
  
  // Additional company extracted data not in shared types
  totaltransactions?: string
  totalsquarefeet?: string
  totalunits?: string
  historicalreturns?: string
  portfoliovalue?: string
  companyindustry?: string
  capitalposition?: string
  
  // Investment strategy fields
  investment_strategy?: string[]
  investmentstrategy?: string[]
}

export default function CompaniesView() {
  const [companies, setCompanies] = useState<UnifiedCompanyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCompanies, setTotalCompanies] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [mappings, setMappings] = useState<any>(null);
  const searchParams = useSearchParams();
  
  // V2 filters state (V2 only - no legacy support) - initialize from URL if available
  const [filters, setFilters] = useState<CompanyUnifiedFiltersV2Type>(() => {
    if (!searchParams) {
      return {
        page: 1,
        limit: 25,
        sortBy: 'updated_at',
        sortOrder: 'desc'
      };
    }
    
    // Initialize from URL parameters
    return {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '25'),
      sortBy: searchParams.get('sortBy') || 'updated_at',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      
      // Company search fields
      companyNameSearch: searchParams.get('companyNameSearch') || undefined,
      companyWebsiteSearch: searchParams.get('companyWebsiteSearch') || undefined,
      
      // Company basic filters
      companyAddress: searchParams.get('companyAddress')?.split(',').filter(Boolean),
      companyCity: searchParams.get('companyCity')?.split(',').filter(Boolean),
      companyState: searchParams.get('companyState')?.split(',').filter(Boolean),
      companyCountry: searchParams.get('companyCountry')?.split(',').filter(Boolean),
      industry: searchParams.get('industry')?.split(',').filter(Boolean),
      source: searchParams.get('source')?.split(',').filter(Boolean),
      
      // Company V2 Overview filters
      companyType: searchParams.get('companyType')?.split(',').filter(Boolean),
      foundedYearMin: searchParams.get('foundedYearMin') ? parseInt(searchParams.get('foundedYearMin')!) : undefined,
      foundedYearMax: searchParams.get('foundedYearMax') ? parseInt(searchParams.get('foundedYearMax')!) : undefined,
      investmentFocus: searchParams.get('investmentFocus')?.split(',').filter(Boolean),
      
      // Financial metrics
      fundSizeMin: searchParams.get('fundSizeMin') ? parseFloat(searchParams.get('fundSizeMin')!) : undefined,
      fundSizeMax: searchParams.get('fundSizeMax') ? parseFloat(searchParams.get('fundSizeMax')!) : undefined,
      aumMin: searchParams.get('aumMin') ? parseFloat(searchParams.get('aumMin')!) : undefined,
      aumMax: searchParams.get('aumMax') ? parseFloat(searchParams.get('aumMax')!) : undefined,
      numberOfPropertiesMin: searchParams.get('numberOfPropertiesMin') ? parseInt(searchParams.get('numberOfPropertiesMin')!) : undefined,
      numberOfPropertiesMax: searchParams.get('numberOfPropertiesMax') ? parseInt(searchParams.get('numberOfPropertiesMax')!) : undefined,
      numberOfEmployeesMin: searchParams.get('numberOfEmployeesMin') ? parseInt(searchParams.get('numberOfEmployeesMin')!) : undefined,
      numberOfEmployeesMax: searchParams.get('numberOfEmployeesMax') ? parseInt(searchParams.get('numberOfEmployeesMax')!) : undefined,
      annualRevenueMin: searchParams.get('annualRevenueMin') ? parseFloat(searchParams.get('annualRevenueMin')!) : undefined,
      annualRevenueMax: searchParams.get('annualRevenueMax') ? parseFloat(searchParams.get('annualRevenueMax')!) : undefined,
      netIncomeMin: searchParams.get('netIncomeMin') ? parseFloat(searchParams.get('netIncomeMin')!) : undefined,
      netIncomeMax: searchParams.get('netIncomeMax') ? parseFloat(searchParams.get('netIncomeMax')!) : undefined,
      ebitdaMin: searchParams.get('ebitdaMin') ? parseFloat(searchParams.get('ebitdaMin')!) : undefined,
      ebitdaMax: searchParams.get('ebitdaMax') ? parseFloat(searchParams.get('ebitdaMax')!) : undefined,
      profitMarginMin: searchParams.get('profitMarginMin') ? parseFloat(searchParams.get('profitMarginMin')!) : undefined,
      profitMarginMax: searchParams.get('profitMarginMax') ? parseFloat(searchParams.get('profitMarginMax')!) : undefined,
      marketCapitalizationMin: searchParams.get('marketCapitalizationMin') ? parseFloat(searchParams.get('marketCapitalizationMin')!) : undefined,
      marketCapitalizationMax: searchParams.get('marketCapitalizationMax') ? parseFloat(searchParams.get('marketCapitalizationMax')!) : undefined,
      marketSharePercentageMin: searchParams.get('marketSharePercentageMin') ? parseFloat(searchParams.get('marketSharePercentageMin')!) : undefined,
      marketSharePercentageMax: searchParams.get('marketSharePercentageMax') ? parseFloat(searchParams.get('marketSharePercentageMax')!) : undefined,
      
      // Processing status filters
      websiteScrapingStatus: searchParams.get('websiteScrapingStatus')?.split(',').filter(Boolean),
      companyOverviewStatus: searchParams.get('companyOverviewStatus')?.split(',').filter(Boolean),
      overviewV2Status: searchParams.get('overviewV2Status')?.split(',').filter(Boolean),
      investmentCriteriaStatus: searchParams.get('investmentCriteriaStatus')?.split(',').filter(Boolean),
      
      // Contact Processor Status Filters
      hasContacts: searchParams.get('hasContacts') === 'true' ? true : searchParams.get('hasContacts') === 'false' ? false : undefined,
      contactsEmailVerificationStatus: searchParams.get('contactsEmailVerificationStatus')?.split(',').filter(Boolean),
      contactsEnrichmentStatus: searchParams.get('contactsEnrichmentStatus')?.split(',').filter(Boolean),
      contactsEnrichmentV2Status: searchParams.get('contactsEnrichmentV2Status')?.split(',').filter(Boolean),
      contactsEmailGenerationStatus: searchParams.get('contactsEmailGenerationStatus')?.split(',').filter(Boolean),
      contactsEmailSendingStatus: searchParams.get('contactsEmailSendingStatus')?.split(',').filter(Boolean),
      contactsInvestmentCriteriaStatus: searchParams.get('contactsInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      
      // V2 Contact Information Filters
      hasMainPhone: searchParams.get('hasMainPhone') === 'true' ? true : searchParams.get('hasMainPhone') === 'false' ? false : undefined,
      hasSecondaryPhone: searchParams.get('hasSecondaryPhone') === 'true' ? true : searchParams.get('hasSecondaryPhone') === 'false' ? false : undefined,
      hasMainEmail: searchParams.get('hasMainEmail') === 'true' ? true : searchParams.get('hasMainEmail') === 'false' ? false : undefined,
      hasSecondaryEmail: searchParams.get('hasSecondaryEmail') === 'true' ? true : searchParams.get('hasSecondaryEmail') === 'false' ? false : undefined,
      hasCompanyLinkedin: searchParams.get('hasCompanyLinkedin') === 'true' ? true : searchParams.get('hasCompanyLinkedin') === 'false' ? false : undefined,
      hasTwitter: searchParams.get('hasTwitter') === 'true' ? true : searchParams.get('hasTwitter') === 'false' ? false : undefined,
      hasFacebook: searchParams.get('hasFacebook') === 'true' ? true : searchParams.get('hasFacebook') === 'false' ? false : undefined,
      hasInstagram: searchParams.get('hasInstagram') === 'true' ? true : searchParams.get('hasInstagram') === 'false' ? false : undefined,
      hasYoutube: searchParams.get('hasYoutube') === 'true' ? true : searchParams.get('hasYoutube') === 'false' ? false : undefined,
      
      // V2 Location Filters
      headquartersAddress: searchParams.get('headquartersAddress')?.split(',').filter(Boolean),
      headquartersCity: searchParams.get('headquartersCity')?.split(',').filter(Boolean),
      headquartersState: searchParams.get('headquartersState')?.split(',').filter(Boolean),
      headquartersCountry: searchParams.get('headquartersCountry')?.split(',').filter(Boolean),
      officeLocations: searchParams.get('officeLocations')?.split(',').filter(Boolean),
      
      // V2 Financial Information Filters
      balanceSheetStrength: searchParams.get('balanceSheetStrength')?.split(',').filter(Boolean),
      fundingSources: searchParams.get('fundingSources')?.split(',').filter(Boolean),
      creditRating: searchParams.get('creditRating')?.split(',').filter(Boolean),
      dryPowderMin: searchParams.get('dryPowderMin') ? parseFloat(searchParams.get('dryPowderMin')!) : undefined,
      dryPowderMax: searchParams.get('dryPowderMax') ? parseFloat(searchParams.get('dryPowderMax')!) : undefined,
      annualDeploymentTargetMin: searchParams.get('annualDeploymentTargetMin') ? parseFloat(searchParams.get('annualDeploymentTargetMin')!) : undefined,
      annualDeploymentTargetMax: searchParams.get('annualDeploymentTargetMax') ? parseFloat(searchParams.get('annualDeploymentTargetMax')!) : undefined,
      
      // V2 Investment & Fund Information Filters
      investmentVehicleType: searchParams.get('investmentVehicleType')?.split(',').filter(Boolean),
      fundraisingStatus: searchParams.get('fundraisingStatus')?.split(',').filter(Boolean),
      lenderType: searchParams.get('lenderType')?.split(',').filter(Boolean),
      annualLoanVolumeMin: searchParams.get('annualLoanVolumeMin') ? parseFloat(searchParams.get('annualLoanVolumeMin')!) : undefined,
      annualLoanVolumeMax: searchParams.get('annualLoanVolumeMax') ? parseFloat(searchParams.get('annualLoanVolumeMax')!) : undefined,
      portfolioHealth: searchParams.get('portfolioHealth')?.split(',').filter(Boolean),
      
      // V2 Partnership & Leadership Filters
      partnerships: searchParams.get('partnerships')?.split(',').filter(Boolean),
      keyEquityPartners: searchParams.get('keyEquityPartners')?.split(',').filter(Boolean),
      keyDebtPartners: searchParams.get('keyDebtPartners')?.split(',').filter(Boolean),
      keyExecutives: searchParams.get('keyExecutives')?.split(',').filter(Boolean),
      
      // V2 Market Positioning Filters
      sustainabilityEsgFocus: searchParams.get('sustainabilityEsgFocus') === 'true' ? true : searchParams.get('sustainabilityEsgFocus') === 'false' ? false : undefined,
      technologyProptechAdoption: searchParams.get('technologyProptechAdoption') === 'true' ? true : searchParams.get('technologyProptechAdoption') === 'false' ? false : undefined,
      adaptiveReuseExperience: searchParams.get('adaptiveReuseExperience') === 'true' ? true : searchParams.get('adaptiveReuseExperience') === 'false' ? false : undefined,
      regulatoryZoningExpertise: searchParams.get('regulatoryZoningExpertise') === 'true' ? true : searchParams.get('regulatoryZoningExpertise') === 'false' ? false : undefined,
      
      // V2 Corporate Structure Filters
      corporateStructure: searchParams.get('corporateStructure')?.split(',').filter(Boolean),
      parentCompany: searchParams.get('parentCompany')?.split(',').filter(Boolean),
      stockTickerSymbol: searchParams.get('stockTickerSymbol')?.split(',').filter(Boolean),
      stockExchange: searchParams.get('stockExchange')?.split(',').filter(Boolean),
      
      // Investment criteria filters
      capitalPosition: searchParams.get('capitalPosition')?.split(',').filter(Boolean),
      propertyTypes: searchParams.get('propertyTypes')?.split(',').filter(Boolean),
      strategies: searchParams.get('strategies')?.split(',').filter(Boolean),
      regions: searchParams.get('regions')?.split(',').filter(Boolean),
      states: searchParams.get('states')?.split(',').filter(Boolean),
      cities: searchParams.get('cities')?.split(',').filter(Boolean),
      countries: searchParams.get('countries')?.split(',').filter(Boolean),
      dealSizeMin: searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : undefined,
      dealSizeMax: searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : undefined,
      
      // Investment Criteria Debt - Borrower & Closing
      closingTimeMin: searchParams.get('closingTimeMin') ? parseInt(searchParams.get('closingTimeMin')!) : undefined,
      closingTimeMax: searchParams.get('closingTimeMax') ? parseInt(searchParams.get('closingTimeMax')!) : undefined,
      eligibleBorrower: searchParams.get('eligibleBorrower')?.split(',').filter(Boolean),
      futureFacilities: searchParams.get('futureFacilities')?.split(',').filter(Boolean),
      occupancyRequirements: searchParams.get('occupancyRequirements')?.split(',').filter(Boolean),
      
      // Investment Criteria Debt - Covenants & Terms
      lienPosition: searchParams.get('lienPosition')?.split(',').filter(Boolean),
      minLoanDscrMin: searchParams.get('minLoanDscrMin') ? parseFloat(searchParams.get('minLoanDscrMin')!) : undefined,
      minLoanDscrMax: searchParams.get('minLoanDscrMax') ? parseFloat(searchParams.get('minLoanDscrMax')!) : undefined,
      maxLoanDscrMin: searchParams.get('maxLoanDscrMin') ? parseFloat(searchParams.get('maxLoanDscrMin')!) : undefined,
      maxLoanDscrMax: searchParams.get('maxLoanDscrMax') ? parseFloat(searchParams.get('maxLoanDscrMax')!) : undefined,
      recourseLoan: searchParams.get('recourseLoan')?.split(',').filter(Boolean),
      loanMinDebtYield: searchParams.get('loanMinDebtYield')?.split(',').filter(Boolean),
      prepayment: searchParams.get('prepayment')?.split(',').filter(Boolean),
      yieldMaintenance: searchParams.get('yieldMaintenance')?.split(',').filter(Boolean),
      
      // Investment Criteria Debt - Program Detail
      loanTypes: searchParams.get('loanTypes')?.split(',').filter(Boolean),
      loanTypeNormalized: searchParams.get('loanTypeNormalized')?.split(',').filter(Boolean),
      structuredLoanTranche: searchParams.get('structuredLoanTranche')?.split(',').filter(Boolean),
      loanProgram: searchParams.get('loanProgram')?.split(',').filter(Boolean),
      
      // Investment Criteria Debt - Term & Amortization
      minLoanTermMin: searchParams.get('minLoanTermMin') ? parseInt(searchParams.get('minLoanTermMin')!) : undefined,
      minLoanTermMax: searchParams.get('minLoanTermMax') ? parseInt(searchParams.get('minLoanTermMax')!) : undefined,
      maxLoanTermMin: searchParams.get('maxLoanTermMin') ? parseInt(searchParams.get('maxLoanTermMin')!) : undefined,
      maxLoanTermMax: searchParams.get('maxLoanTermMax') ? parseInt(searchParams.get('maxLoanTermMax')!) : undefined,
      amortization: searchParams.get('amortization')?.split(',').filter(Boolean),
      
      // Investment Criteria Equity - Target Returns
      targetReturnMin: searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : undefined,
      targetReturnMax: searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : undefined,
      minimumIrrMin: searchParams.get('minimumIrrMin') ? parseFloat(searchParams.get('minimumIrrMin')!) : undefined,
      minimumIrrMax: searchParams.get('minimumIrrMax') ? parseFloat(searchParams.get('minimumIrrMax')!) : undefined,
      minimumYieldOnCostMin: searchParams.get('minimumYieldOnCostMin') ? parseFloat(searchParams.get('minimumYieldOnCostMin')!) : undefined,
      minimumYieldOnCostMax: searchParams.get('minimumYieldOnCostMax') ? parseFloat(searchParams.get('minimumYieldOnCostMax')!) : undefined,
      targetCashOnCashMin: searchParams.get('targetCashOnCashMin') ? parseFloat(searchParams.get('targetCashOnCashMin')!) : undefined,
      targetCashOnCashMax: searchParams.get('targetCashOnCashMax') ? parseFloat(searchParams.get('targetCashOnCashMax')!) : undefined,
      
      // Investment Criteria Equity - Hold Period
      minHoldPeriodYearsMin: searchParams.get('minHoldPeriodYearsMin') ? parseInt(searchParams.get('minHoldPeriodYearsMin')!) : undefined,
      minHoldPeriodYearsMax: searchParams.get('minHoldPeriodYearsMax') ? parseInt(searchParams.get('minHoldPeriodYearsMax')!) : undefined,
      maxHoldPeriodYearsMin: searchParams.get('maxHoldPeriodYearsMin') ? parseInt(searchParams.get('maxHoldPeriodYearsMin')!) : undefined,
      maxHoldPeriodYearsMax: searchParams.get('maxHoldPeriodYearsMax') ? parseInt(searchParams.get('maxHoldPeriodYearsMax')!) : undefined,
      
      // Investment Criteria Equity - Ownership Control
      ownershipRequirement: searchParams.get('ownershipRequirement')?.split(',').filter(Boolean),
      maxLeverageToleranceMin: searchParams.get('maxLeverageToleranceMin') ? parseFloat(searchParams.get('maxLeverageToleranceMin')!) : undefined,
      maxLeverageToleranceMax: searchParams.get('maxLeverageToleranceMax') ? parseFloat(searchParams.get('maxLeverageToleranceMax')!) : undefined,
      
      // Company Websites
      companyWebsites: searchParams.get('companyWebsites')?.split(',').filter(Boolean),
      
      // New filters
      notEmptyCompanyWebsite: searchParams.get('notEmptyCompanyWebsite') === 'true' ? true : searchParams.get('notEmptyCompanyWebsite') === 'false' ? false : undefined,
      companyIds: searchParams.get('companyIds')?.split(',').filter(Boolean),
      
      // NOT filters
      notSource: searchParams.get('notSource')?.split(',').filter(Boolean),
      notIndustry: searchParams.get('notIndustry')?.split(',').filter(Boolean),
      notCompanyType: searchParams.get('notCompanyType')?.split(',').filter(Boolean),
      notWebsiteScrapingStatus: searchParams.get('notWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewStatus: searchParams.get('notCompanyOverviewStatus')?.split(',').filter(Boolean),
      notOverviewV2Status: searchParams.get('notOverviewV2Status')?.split(',').filter(Boolean),
      notInvestmentCriteriaStatus: searchParams.get('notInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      notContactsEmailVerificationStatus: searchParams.get('notContactsEmailVerificationStatus')?.split(',').filter(Boolean),
      notContactsEnrichmentStatus: searchParams.get('notContactsEnrichmentStatus')?.split(',').filter(Boolean),
      notContactsEnrichmentV2Status: searchParams.get('notContactsEnrichmentV2Status')?.split(',').filter(Boolean),
      notContactsEmailGenerationStatus: searchParams.get('notContactsEmailGenerationStatus')?.split(',').filter(Boolean),
      notContactsEmailSendingStatus: searchParams.get('notContactsEmailSendingStatus')?.split(',').filter(Boolean),
      notContactsInvestmentCriteriaStatus: searchParams.get('notContactsInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      
      // V2 NOT filters for new fields
      notInvestmentFocus: searchParams.get('notInvestmentFocus')?.split(',').filter(Boolean),
      notCapitalPosition: searchParams.get('notCapitalPosition')?.split(',').filter(Boolean),
      notPropertyTypes: searchParams.get('notPropertyTypes')?.split(',').filter(Boolean),
      notStrategies: searchParams.get('notStrategies')?.split(',').filter(Boolean),
      notPartnerships: searchParams.get('notPartnerships')?.split(',').filter(Boolean),
      notFundraisingStatus: searchParams.get('notFundraisingStatus')?.split(',').filter(Boolean),
      notLenderType: searchParams.get('notLenderType')?.split(',').filter(Boolean),
      notLoanTypes: searchParams.get('notLoanTypes')?.split(',').filter(Boolean),
      notStructuredLoanTranche: searchParams.get('notStructuredLoanTranche')?.split(',').filter(Boolean),
      notLoanProgram: searchParams.get('notLoanProgram')?.split(',').filter(Boolean),
      notRecourseLoan: searchParams.get('notRecourseLoan')?.split(',').filter(Boolean),
      notEligibleBorrower: searchParams.get('notEligibleBorrower')?.split(',').filter(Boolean),
      notLienPosition: searchParams.get('notLienPosition')?.split(',').filter(Boolean),
      notOwnershipRequirement: searchParams.get('notOwnershipRequirement')?.split(',').filter(Boolean),
      notAmortization: searchParams.get('notAmortization')?.split(',').filter(Boolean),
    };
  });

  // Processing state
  const [selectedCompanies, setSelectedCompanies] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [processingJobs, setProcessingJobs] = useState<ProcessingJob[]>([
    { stage: 'website_scraping', isExecuting: false },
    { stage: 'company_overview_v2', isExecuting: false },
    { stage: 'company_investment_criteria', isExecuting: false }
  ]);
  
  const router = useRouter();

  const { toast } = useToast();

  // Initialize V2 filters from URL parameters on component mount and URL changes
  useEffect(() => {
    if (!searchParams) return;
    
    const urlFilters: CompanyUnifiedFiltersV2Type = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '25'),
      sortBy: searchParams.get('sortBy') || 'updated_at',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      
      // Company search fields
      companyNameSearch: searchParams.get('companyNameSearch') || undefined,
      companyWebsiteSearch: searchParams.get('companyWebsiteSearch') || undefined,
      
      // Company basic filters
      companyAddress: searchParams.get('companyAddress')?.split(',').filter(Boolean),
      companyCity: searchParams.get('companyCity')?.split(',').filter(Boolean),
      companyState: searchParams.get('companyState')?.split(',').filter(Boolean),
      companyCountry: searchParams.get('companyCountry')?.split(',').filter(Boolean),
      industry: searchParams.get('industry')?.split(',').filter(Boolean),
      source: searchParams.get('source')?.split(',').filter(Boolean),
      
      // Company V2 Overview filters
      companyType: searchParams.get('companyType')?.split(',').filter(Boolean),
      foundedYearMin: searchParams.get('foundedYearMin') ? parseInt(searchParams.get('foundedYearMin')!) : undefined,
      foundedYearMax: searchParams.get('foundedYearMax') ? parseInt(searchParams.get('foundedYearMax')!) : undefined,
      investmentFocus: searchParams.get('investmentFocus')?.split(',').filter(Boolean),
      
      // Financial metrics
      fundSizeMin: searchParams.get('fundSizeMin') ? parseFloat(searchParams.get('fundSizeMin')!) : undefined,
      fundSizeMax: searchParams.get('fundSizeMax') ? parseFloat(searchParams.get('fundSizeMax')!) : undefined,
      aumMin: searchParams.get('aumMin') ? parseFloat(searchParams.get('aumMin')!) : undefined,
      aumMax: searchParams.get('aumMax') ? parseFloat(searchParams.get('aumMax')!) : undefined,
      numberOfPropertiesMin: searchParams.get('numberOfPropertiesMin') ? parseInt(searchParams.get('numberOfPropertiesMin')!) : undefined,
      numberOfPropertiesMax: searchParams.get('numberOfPropertiesMax') ? parseInt(searchParams.get('numberOfPropertiesMax')!) : undefined,
      numberOfEmployeesMin: searchParams.get('numberOfEmployeesMin') ? parseInt(searchParams.get('numberOfEmployeesMin')!) : undefined,
      numberOfEmployeesMax: searchParams.get('numberOfEmployeesMax') ? parseInt(searchParams.get('numberOfEmployeesMax')!) : undefined,
      annualRevenueMin: searchParams.get('annualRevenueMin') ? parseFloat(searchParams.get('annualRevenueMin')!) : undefined,
      annualRevenueMax: searchParams.get('annualRevenueMax') ? parseFloat(searchParams.get('annualRevenueMax')!) : undefined,
      netIncomeMin: searchParams.get('netIncomeMin') ? parseFloat(searchParams.get('netIncomeMin')!) : undefined,
      netIncomeMax: searchParams.get('netIncomeMax') ? parseFloat(searchParams.get('netIncomeMax')!) : undefined,
      ebitdaMin: searchParams.get('ebitdaMin') ? parseFloat(searchParams.get('ebitdaMin')!) : undefined,
      ebitdaMax: searchParams.get('ebitdaMax') ? parseFloat(searchParams.get('ebitdaMax')!) : undefined,
      profitMarginMin: searchParams.get('profitMarginMin') ? parseFloat(searchParams.get('profitMarginMin')!) : undefined,
      profitMarginMax: searchParams.get('profitMarginMax') ? parseFloat(searchParams.get('profitMarginMax')!) : undefined,
      marketCapitalizationMin: searchParams.get('marketCapitalizationMin') ? parseFloat(searchParams.get('marketCapitalizationMin')!) : undefined,
      marketCapitalizationMax: searchParams.get('marketCapitalizationMax') ? parseFloat(searchParams.get('marketCapitalizationMax')!) : undefined,
      marketSharePercentageMin: searchParams.get('marketSharePercentageMin') ? parseFloat(searchParams.get('marketSharePercentageMin')!) : undefined,
      marketSharePercentageMax: searchParams.get('marketSharePercentageMax') ? parseFloat(searchParams.get('marketSharePercentageMax')!) : undefined,
      
      // Processing status filters
      websiteScrapingStatus: searchParams.get('websiteScrapingStatus')?.split(',').filter(Boolean),
      companyOverviewStatus: searchParams.get('companyOverviewStatus')?.split(',').filter(Boolean),
      overviewV2Status: searchParams.get('overviewV2Status')?.split(',').filter(Boolean),
      investmentCriteriaStatus: searchParams.get('investmentCriteriaStatus')?.split(',').filter(Boolean),
      
      // Contact Processor Status Filters
      hasContacts: searchParams.get('hasContacts') === 'true' ? true : searchParams.get('hasContacts') === 'false' ? false : undefined,
      contactsEmailVerificationStatus: searchParams.get('contactsEmailVerificationStatus')?.split(',').filter(Boolean),
      contactsEnrichmentStatus: searchParams.get('contactsEnrichmentStatus')?.split(',').filter(Boolean),
      contactsEnrichmentV2Status: searchParams.get('contactsEnrichmentV2Status')?.split(',').filter(Boolean),
      contactsEmailGenerationStatus: searchParams.get('contactsEmailGenerationStatus')?.split(',').filter(Boolean),
      contactsEmailSendingStatus: searchParams.get('contactsEmailSendingStatus')?.split(',').filter(Boolean),
      contactsInvestmentCriteriaStatus: searchParams.get('contactsInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      
      // V2 Contact Information Filters
      hasMainPhone: searchParams.get('hasMainPhone') === 'true' ? true : searchParams.get('hasMainPhone') === 'false' ? false : undefined,
      hasSecondaryPhone: searchParams.get('hasSecondaryPhone') === 'true' ? true : searchParams.get('hasSecondaryPhone') === 'false' ? false : undefined,
      hasMainEmail: searchParams.get('hasMainEmail') === 'true' ? true : searchParams.get('hasMainEmail') === 'false' ? false : undefined,
      hasSecondaryEmail: searchParams.get('hasSecondaryEmail') === 'true' ? true : searchParams.get('hasSecondaryEmail') === 'false' ? false : undefined,
      hasCompanyLinkedin: searchParams.get('hasCompanyLinkedin') === 'true' ? true : searchParams.get('hasCompanyLinkedin') === 'false' ? false : undefined,
      hasTwitter: searchParams.get('hasTwitter') === 'true' ? true : searchParams.get('hasTwitter') === 'false' ? false : undefined,
      hasFacebook: searchParams.get('hasFacebook') === 'true' ? true : searchParams.get('hasFacebook') === 'false' ? false : undefined,
      hasInstagram: searchParams.get('hasInstagram') === 'true' ? true : searchParams.get('hasInstagram') === 'false' ? false : undefined,
      hasYoutube: searchParams.get('hasYoutube') === 'true' ? true : searchParams.get('hasYoutube') === 'false' ? false : undefined,
      
      // V2 Location Filters
      headquartersAddress: searchParams.get('headquartersAddress')?.split(',').filter(Boolean),
      headquartersCity: searchParams.get('headquartersCity')?.split(',').filter(Boolean),
      headquartersState: searchParams.get('headquartersState')?.split(',').filter(Boolean),
      headquartersCountry: searchParams.get('headquartersCountry')?.split(',').filter(Boolean),
      officeLocations: searchParams.get('officeLocations')?.split(',').filter(Boolean),
      
      // V2 Financial Information Filters
      balanceSheetStrength: searchParams.get('balanceSheetStrength')?.split(',').filter(Boolean),
      fundingSources: searchParams.get('fundingSources')?.split(',').filter(Boolean),
      creditRating: searchParams.get('creditRating')?.split(',').filter(Boolean),
      dryPowderMin: searchParams.get('dryPowderMin') ? parseFloat(searchParams.get('dryPowderMin')!) : undefined,
      dryPowderMax: searchParams.get('dryPowderMax') ? parseFloat(searchParams.get('dryPowderMax')!) : undefined,
      annualDeploymentTargetMin: searchParams.get('annualDeploymentTargetMin') ? parseFloat(searchParams.get('annualDeploymentTargetMin')!) : undefined,
      annualDeploymentTargetMax: searchParams.get('annualDeploymentTargetMax') ? parseFloat(searchParams.get('annualDeploymentTargetMax')!) : undefined,
      
      // V2 Investment & Fund Information Filters
      investmentVehicleType: searchParams.get('investmentVehicleType')?.split(',').filter(Boolean),
      fundraisingStatus: searchParams.get('fundraisingStatus')?.split(',').filter(Boolean),
      lenderType: searchParams.get('lenderType')?.split(',').filter(Boolean),
      annualLoanVolumeMin: searchParams.get('annualLoanVolumeMin') ? parseFloat(searchParams.get('annualLoanVolumeMin')!) : undefined,
      annualLoanVolumeMax: searchParams.get('annualLoanVolumeMax') ? parseFloat(searchParams.get('annualLoanVolumeMax')!) : undefined,
      portfolioHealth: searchParams.get('portfolioHealth')?.split(',').filter(Boolean),
      
      // V2 Partnership & Leadership Filters
      partnerships: searchParams.get('partnerships')?.split(',').filter(Boolean),
      keyEquityPartners: searchParams.get('keyEquityPartners')?.split(',').filter(Boolean),
      keyDebtPartners: searchParams.get('keyDebtPartners')?.split(',').filter(Boolean),
      keyExecutives: searchParams.get('keyExecutives')?.split(',').filter(Boolean),
      
      // V2 Market Positioning Filters
      sustainabilityEsgFocus: searchParams.get('sustainabilityEsgFocus') === 'true' ? true : searchParams.get('sustainabilityEsgFocus') === 'false' ? false : undefined,
      technologyProptechAdoption: searchParams.get('technologyProptechAdoption') === 'true' ? true : searchParams.get('technologyProptechAdoption') === 'false' ? false : undefined,
      adaptiveReuseExperience: searchParams.get('adaptiveReuseExperience') === 'true' ? true : searchParams.get('adaptiveReuseExperience') === 'false' ? false : undefined,
      regulatoryZoningExpertise: searchParams.get('regulatoryZoningExpertise') === 'true' ? true : searchParams.get('regulatoryZoningExpertise') === 'false' ? false : undefined,
      
      // V2 Corporate Structure Filters
      corporateStructure: searchParams.get('corporateStructure')?.split(',').filter(Boolean),
      parentCompany: searchParams.get('parentCompany')?.split(',').filter(Boolean),
      stockTickerSymbol: searchParams.get('stockTickerSymbol')?.split(',').filter(Boolean),
      stockExchange: searchParams.get('stockExchange')?.split(',').filter(Boolean),
      
      // Investment criteria filters
      capitalPosition: searchParams.get('capitalPosition')?.split(',').filter(Boolean),
      propertyTypes: searchParams.get('propertyTypes')?.split(',').filter(Boolean),
      strategies: searchParams.get('strategies')?.split(',').filter(Boolean),
      regions: searchParams.get('regions')?.split(',').filter(Boolean),
      states: searchParams.get('states')?.split(',').filter(Boolean),
      cities: searchParams.get('cities')?.split(',').filter(Boolean),
      countries: searchParams.get('countries')?.split(',').filter(Boolean),
      dealSizeMin: searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : undefined,
      dealSizeMax: searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : undefined,
      
      // Investment Criteria Debt - Borrower & Closing
      closingTimeMin: searchParams.get('closingTimeMin') ? parseInt(searchParams.get('closingTimeMin')!) : undefined,
      closingTimeMax: searchParams.get('closingTimeMax') ? parseInt(searchParams.get('closingTimeMax')!) : undefined,
      eligibleBorrower: searchParams.get('eligibleBorrower')?.split(',').filter(Boolean),
      futureFacilities: searchParams.get('futureFacilities')?.split(',').filter(Boolean),
      occupancyRequirements: searchParams.get('occupancyRequirements')?.split(',').filter(Boolean),
      
      // Investment Criteria Debt - Covenants & Terms
      lienPosition: searchParams.get('lienPosition')?.split(',').filter(Boolean),
      minLoanDscrMin: searchParams.get('minLoanDscrMin') ? parseFloat(searchParams.get('minLoanDscrMin')!) : undefined,
      minLoanDscrMax: searchParams.get('minLoanDscrMax') ? parseFloat(searchParams.get('minLoanDscrMax')!) : undefined,
      maxLoanDscrMin: searchParams.get('maxLoanDscrMin') ? parseFloat(searchParams.get('maxLoanDscrMin')!) : undefined,
      maxLoanDscrMax: searchParams.get('maxLoanDscrMax') ? parseFloat(searchParams.get('maxLoanDscrMax')!) : undefined,
      recourseLoan: searchParams.get('recourseLoan')?.split(',').filter(Boolean),
      loanMinDebtYield: searchParams.get('loanMinDebtYield')?.split(',').filter(Boolean),
      prepayment: searchParams.get('prepayment')?.split(',').filter(Boolean),
      yieldMaintenance: searchParams.get('yieldMaintenance')?.split(',').filter(Boolean),
      
      // Investment Criteria Debt - Program Detail
      loanTypes: searchParams.get('loanTypes')?.split(',').filter(Boolean),
      loanTypeNormalized: searchParams.get('loanTypeNormalized')?.split(',').filter(Boolean),
      structuredLoanTranche: searchParams.get('structuredLoanTranche')?.split(',').filter(Boolean),
      loanProgram: searchParams.get('loanProgram')?.split(',').filter(Boolean),
      
      // Investment Criteria Debt - Term & Amortization
      minLoanTermMin: searchParams.get('minLoanTermMin') ? parseInt(searchParams.get('minLoanTermMin')!) : undefined,
      minLoanTermMax: searchParams.get('minLoanTermMax') ? parseInt(searchParams.get('minLoanTermMax')!) : undefined,
      maxLoanTermMin: searchParams.get('maxLoanTermMin') ? parseInt(searchParams.get('maxLoanTermMin')!) : undefined,
      maxLoanTermMax: searchParams.get('maxLoanTermMax') ? parseInt(searchParams.get('maxLoanTermMax')!) : undefined,
      amortization: searchParams.get('amortization')?.split(',').filter(Boolean),
      
      // Investment Criteria Equity - Target Returns
      targetReturnMin: searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : undefined,
      targetReturnMax: searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : undefined,
      minimumIrrMin: searchParams.get('minimumIrrMin') ? parseFloat(searchParams.get('minimumIrrMin')!) : undefined,
      minimumIrrMax: searchParams.get('minimumIrrMax') ? parseFloat(searchParams.get('minimumIrrMax')!) : undefined,
      minimumYieldOnCostMin: searchParams.get('minimumYieldOnCostMin') ? parseFloat(searchParams.get('minimumYieldOnCostMin')!) : undefined,
      minimumYieldOnCostMax: searchParams.get('minimumYieldOnCostMax') ? parseFloat(searchParams.get('minimumYieldOnCostMax')!) : undefined,
      targetCashOnCashMin: searchParams.get('targetCashOnCashMin') ? parseFloat(searchParams.get('targetCashOnCashMin')!) : undefined,
      targetCashOnCashMax: searchParams.get('targetCashOnCashMax') ? parseFloat(searchParams.get('targetCashOnCashMax')!) : undefined,
      
      // Investment Criteria Equity - Hold Period
      minHoldPeriodYearsMin: searchParams.get('minHoldPeriodYearsMin') ? parseInt(searchParams.get('minHoldPeriodYearsMin')!) : undefined,
      minHoldPeriodYearsMax: searchParams.get('minHoldPeriodYearsMax') ? parseInt(searchParams.get('minHoldPeriodYearsMax')!) : undefined,
      maxHoldPeriodYearsMin: searchParams.get('maxHoldPeriodYearsMin') ? parseInt(searchParams.get('maxHoldPeriodYearsMin')!) : undefined,
      maxHoldPeriodYearsMax: searchParams.get('maxHoldPeriodYearsMax') ? parseInt(searchParams.get('maxHoldPeriodYearsMax')!) : undefined,
      
      // Investment Criteria Equity - Ownership Control
      ownershipRequirement: searchParams.get('ownershipRequirement')?.split(',').filter(Boolean),
      maxLeverageToleranceMin: searchParams.get('maxLeverageToleranceMin') ? parseFloat(searchParams.get('maxLeverageToleranceMin')!) : undefined,
      maxLeverageToleranceMax: searchParams.get('maxLeverageToleranceMax') ? parseFloat(searchParams.get('maxLeverageToleranceMax')!) : undefined,
      
      // Company Websites
      companyWebsites: searchParams.get('companyWebsites')?.split(',').filter(Boolean),
      
      // New filters
      notEmptyCompanyWebsite: searchParams.get('notEmptyCompanyWebsite') === 'true' ? true : searchParams.get('notEmptyCompanyWebsite') === 'false' ? false : undefined,
      companyIds: searchParams.get('companyIds')?.split(',').filter(Boolean),
      
      // NOT filters
      notSource: searchParams.get('notSource')?.split(',').filter(Boolean),
      notIndustry: searchParams.get('notIndustry')?.split(',').filter(Boolean),
      notCompanyType: searchParams.get('notCompanyType')?.split(',').filter(Boolean),
      notWebsiteScrapingStatus: searchParams.get('notWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewStatus: searchParams.get('notCompanyOverviewStatus')?.split(',').filter(Boolean),
      notOverviewV2Status: searchParams.get('notOverviewV2Status')?.split(',').filter(Boolean),
      notInvestmentCriteriaStatus: searchParams.get('notInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      notContactsEmailVerificationStatus: searchParams.get('notContactsEmailVerificationStatus')?.split(',').filter(Boolean),
      notContactsEnrichmentStatus: searchParams.get('notContactsEnrichmentStatus')?.split(',').filter(Boolean),
      notContactsEnrichmentV2Status: searchParams.get('notContactsEnrichmentV2Status')?.split(',').filter(Boolean),
      notContactsEmailGenerationStatus: searchParams.get('notContactsEmailGenerationStatus')?.split(',').filter(Boolean),
      notContactsEmailSendingStatus: searchParams.get('notContactsEmailSendingStatus')?.split(',').filter(Boolean),
      notContactsInvestmentCriteriaStatus: searchParams.get('notContactsInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      
      // V2 NOT filters for new fields
      notInvestmentFocus: searchParams.get('notInvestmentFocus')?.split(',').filter(Boolean),
      notCapitalPosition: searchParams.get('notCapitalPosition')?.split(',').filter(Boolean),
      notPropertyTypes: searchParams.get('notPropertyTypes')?.split(',').filter(Boolean),
      notStrategies: searchParams.get('notStrategies')?.split(',').filter(Boolean),
      notPartnerships: searchParams.get('notPartnerships')?.split(',').filter(Boolean),
      notFundraisingStatus: searchParams.get('notFundraisingStatus')?.split(',').filter(Boolean),
      notLenderType: searchParams.get('notLenderType')?.split(',').filter(Boolean),
      notLoanTypes: searchParams.get('notLoanTypes')?.split(',').filter(Boolean),
      notStructuredLoanTranche: searchParams.get('notStructuredLoanTranche')?.split(',').filter(Boolean),
      notLoanProgram: searchParams.get('notLoanProgram')?.split(',').filter(Boolean),
      notRecourseLoan: searchParams.get('notRecourseLoan')?.split(',').filter(Boolean),
      notEligibleBorrower: searchParams.get('notEligibleBorrower')?.split(',').filter(Boolean),
      notLienPosition: searchParams.get('notLienPosition')?.split(',').filter(Boolean),
      notOwnershipRequirement: searchParams.get('notOwnershipRequirement')?.split(',').filter(Boolean),
      notAmortization: searchParams.get('notAmortization')?.split(',').filter(Boolean),
    };
    
    console.log('CompaniesView: Initializing V2 filters from URL:', urlFilters);
    console.log('CompaniesView: contactsEnrichmentV2Status from URL:', urlFilters.contactsEnrichmentV2Status);
    setFilters(urlFilters);
    // Fetch companies immediately with the V2 filters
    fetchCompanies(urlFilters);
  }, [searchParams]);

  // Load companies with V2 filters
  const fetchCompanies = async (currentFilters: CompanyUnifiedFiltersV2Type) => {
    console.log('CompaniesView: Starting to fetch companies with filters:', currentFilters);
    setLoading(true);
    try {
      // Build query string with all filters
      const params = new URLSearchParams();
      
      // Add all filter parameters
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value) && value.length > 0) {
            params.append(key, value.join(','));
          } else if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (typeof value === 'number' || typeof value === 'string') {
            params.append(key, value.toString());
          }
        }
      });

      const apiUrl = `/api/companies/unified-filters-v2?${params.toString()}`;
      console.log('CompaniesView: Making API call to:', apiUrl);
      
      const response = await fetch(apiUrl);
      if (response.ok) {
        const data = await response.json();
        console.log('CompaniesView: API response received:', { 
          companyCount: data.data?.length || 0, 
          total: data.pagination?.total || 0 
        });
        setCompanies(data.data || []);
        setTotalCompanies(data.pagination?.total || 0);
        setTotalPages(data.pagination?.totalPages || 0);
      } else {
        console.error("Failed to fetch companies:", await response.text());
        setCompanies([]);
        setTotalCompanies(0);
        setTotalPages(0);
      }
    } catch (error) {
      console.error("Error fetching companies:", error);
      setCompanies([]);
      setTotalCompanies(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  // Load mappings from investment criteria mappings API
  useEffect(() => {
    async function fetchMappings() {
      try {
        const response = await fetch('/api/investment-criteria/mappings');
        if (response.ok) {
          const data = await response.json();
          setMappings(data || {});
        } else {
          console.error("Failed to fetch mappings:", await response.text());
        }
      } catch (error) {
        console.error("Error fetching mappings:", error);
      }
    }

    fetchMappings();
  }, []);




  // Handle V2 filter changes
  const handleFiltersChange = (newFilters: CompanyUnifiedFiltersV2Type) => {
    console.log('CompaniesView: handleFiltersChange called with V2 filters:', newFilters);
    setFilters(newFilters);
    
    // Fetch companies with new V2 filters immediately
    fetchCompanies(newFilters);
    
    // Update URL with new V2 filters
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'companies');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    console.log('CompaniesView: Updating URL to (V2):', newUrl);
    router.replace(newUrl, { scroll: false });
  };



  // Handle clear filters (V2 only)
  const handleClearFilters = () => {
    const clearedFilters: CompanyUnifiedFiltersV2Type = {
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc'
    };
    setFilters(clearedFilters);
    
    // Fetch companies with cleared V2 filters immediately
    fetchCompanies(clearedFilters);
    
    // Update URL to remove all filter parameters but keep tab
    router.replace('/dashboard/entity?tab=companies', { scroll: false });
  };

  // Handle pagination (V2)
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    
    // Fetch companies with new page immediately using V2
    fetchCompanies(newFilters);
    
    // Update URL with new page
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'companies');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    console.log('CompaniesView: Updating URL to (V2):', newUrl);
    router.replace(newUrl, { scroll: false });
  };

  // Handle pagination size change (V2)
  const handlePaginationSizeChange = (newLimit: number) => {
    const newFilters = { ...filters, limit: newLimit, page: 1 }; // Reset to page 1 when changing size
    setFilters(newFilters);
    
    // Fetch companies with new limit immediately using V2
    fetchCompanies(newFilters);
    
    // Update URL with new limit
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'companies');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    console.log('CompaniesView: Updating URL to (V2):', newUrl);
    router.replace(newUrl, { scroll: false });
  };

  // Handle company selection
  const handleSelectCompany = (companyId: number) => {
    setSelectedCompanies(prev => {
      const newSet = new Set(prev);
      if (newSet.has(companyId)) {
        newSet.delete(companyId);
      } else {
        newSet.add(companyId);
      }
      return newSet;
    });
  };

  const handleToggleSelection = (companyId: number, event: React.MouseEvent | React.ChangeEvent) => {
    event.stopPropagation();
    handleSelectCompany(companyId);
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedCompanies(new Set());
    } else {
      const allCompanyIds = new Set(companies.map(company => company.company_id));
      setSelectedCompanies(allCompanyIds);
    }
    setSelectAll(!selectAll);
  };

  // Processing job execution
  const executeProcessingJob = async (stage: CompanyProcessingStage) => {
    if (selectedCompanies.size === 0) return;

    const companyIds = Array.from(selectedCompanies);
    
    // Update processing state
    setProcessingJobs(prev => 
      prev.map(job => 
        job.stage === stage 
          ? { ...job, isExecuting: true }
          : job
      )
    );

    try {
      const jobOptions: any = {
        limit: companyIds.length, // Use the actual number of selected companies as the limit
        batchSize: Math.min(100, companyIds.length), // Use batchSize for concurrent processing within batches
        filters: buildFilters()
      }

      const response = await fetch('/api/processing/queue/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          processorType: stage,
          options: {
            limit: companyIds.length,
            multiIds: companyIds,
            batchSize: Math.min(100, companyIds.length),
            entityType: 'company',
            ...jobOptions
          }
        })
      });

      const data = await response.json();
      if (data.success) {
        toast({
          title: "Processing Started",
          description: `Successfully triggered ${getStageConfig(stage).title} for ${companyIds.length} company${companyIds.length !== 1 ? 'ies' : 'y'}`,
        });
        
        // Refresh companies after a short delay to show updated status
        setTimeout(async () => {
          const fetchCompanies = async () => {
            setLoading(true);
            try {
              const params = new URLSearchParams();
              Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                  if (Array.isArray(value) && value.length > 0) {
                    params.append(key, value.join(','));
                  } else if (typeof value === 'boolean') {
                    params.append(key, value.toString());
                  } else if (typeof value === 'number' || typeof value === 'string') {
                    params.append(key, value.toString());
                  }
                }
              });

              const response = await fetch(`/api/companies/unified-filters-v2?${params.toString()}`);
              if (response.ok) {
                const data = await response.json();
                setCompanies(data.data || []);
                setTotalCompanies(data.pagination?.total || 0);
                setTotalPages(data.pagination?.totalPages || 0);
              }
            } catch (error) {
              console.error("Error refreshing companies:", error);
            } finally {
              setLoading(false);
            }
          };

          await fetchCompanies();
        }, 2000);
      } else {
        toast({
          title: "Processing Failed",
          description: data.error || 'Failed to start processing job',
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error executing processing job:', error);
      toast({
        title: "Processing Error",
        description: 'An error occurred while starting the processing job',
        variant: "destructive",
      });
    } finally {
      // Reset processing state after a delay
      setTimeout(() => {
        setProcessingJobs(prev => 
          prev.map(job => 
            job.stage === stage 
              ? { ...job, isExecuting: false }
              : job
          )
        );
      }, 3000);
    }
  };

  const buildFilters = () => {
    const filterParams: any = {};
    
    // Search filters
    if (filters.companyNameSearch) filterParams.companyNameSearch = filters.companyNameSearch;
    if (filters.companyWebsiteSearch) filterParams.companyWebsiteSearch = filters.companyWebsiteSearch;
    
    // Basic filters
    if (filters.sortBy && filters.sortBy !== 'updated_at') filterParams.sortBy = filters.sortBy;
    if (filters.sortOrder && filters.sortOrder !== 'desc') filterParams.sortOrder = filters.sortOrder;
    
    // Core company table filters
    if (filters.companyAddress?.length) filterParams.companyAddress = filters.companyAddress;
    if (filters.companyCity?.length) filterParams.companyCity = filters.companyCity;
    if (filters.companyState?.length) filterParams.companyState = filters.companyState;
    if (filters.companyCountry?.length) filterParams.companyCountry = filters.companyCountry;
    if (filters.industry?.length) filterParams.industry = filters.industry;
    if (filters.source?.length) filterParams.source = filters.source;
    if (filters.websiteScrapingStatus?.length) filterParams.websiteScrapingStatus = filters.websiteScrapingStatus;
    if (filters.companyOverviewStatus?.length) filterParams.companyOverviewStatus = filters.companyOverviewStatus;
    if (filters.overviewV2Status?.length) filterParams.overviewV2Status = filters.overviewV2Status;
    if (filters.investmentCriteriaStatus?.length) filterParams.investmentCriteriaStatus = filters.investmentCriteriaStatus;
    
    // New filters
    if (filters.notEmptyCompanyWebsite !== undefined) filterParams.notEmptyCompanyWebsite = filters.notEmptyCompanyWebsite;
    if (filters.companyIds?.length) filterParams.companyIds = filters.companyIds;
    if (filters.companyWebsites?.length) filterParams.companyWebsites = filters.companyWebsites;
    
    // Overview V2 filters
    if (filters.companyType?.length) filterParams.companyType = filters.companyType;
    if (filters.foundedYearMin !== undefined) filterParams.foundedYearMin = filters.foundedYearMin;
    if (filters.foundedYearMax !== undefined) filterParams.foundedYearMax = filters.foundedYearMax;
    if (filters.investmentFocus?.length) filterParams.investmentFocus = filters.investmentFocus;
    
    // V2 Contact Information filters
    if (filters.hasMainPhone !== undefined) filterParams.hasMainPhone = filters.hasMainPhone;
    if (filters.hasSecondaryPhone !== undefined) filterParams.hasSecondaryPhone = filters.hasSecondaryPhone;
    if (filters.hasMainEmail !== undefined) filterParams.hasMainEmail = filters.hasMainEmail;
    if (filters.hasSecondaryEmail !== undefined) filterParams.hasSecondaryEmail = filters.hasSecondaryEmail;
    if (filters.hasCompanyLinkedin !== undefined) filterParams.hasCompanyLinkedin = filters.hasCompanyLinkedin;
    if (filters.hasTwitter !== undefined) filterParams.hasTwitter = filters.hasTwitter;
    if (filters.hasFacebook !== undefined) filterParams.hasFacebook = filters.hasFacebook;
    if (filters.hasInstagram !== undefined) filterParams.hasInstagram = filters.hasInstagram;
    if (filters.hasYoutube !== undefined) filterParams.hasYoutube = filters.hasYoutube;
    
    // V2 Location filters
    if (filters.headquartersAddress?.length) filterParams.headquartersAddress = filters.headquartersAddress;
    if (filters.headquartersCity?.length) filterParams.headquartersCity = filters.headquartersCity;
    if (filters.headquartersState?.length) filterParams.headquartersState = filters.headquartersState;
    if (filters.headquartersCountry?.length) filterParams.headquartersCountry = filters.headquartersCountry;
    if (filters.officeLocations?.length) filterParams.officeLocations = filters.officeLocations;
    
    // V2 Financial metrics filters
    if (filters.fundSizeMin !== undefined) filterParams.fundSizeMin = filters.fundSizeMin;
    if (filters.fundSizeMax !== undefined) filterParams.fundSizeMax = filters.fundSizeMax;
    if (filters.aumMin !== undefined) filterParams.aumMin = filters.aumMin;
    if (filters.aumMax !== undefined) filterParams.aumMax = filters.aumMax;
    if (filters.numberOfPropertiesMin !== undefined) filterParams.numberOfPropertiesMin = filters.numberOfPropertiesMin;
    if (filters.numberOfPropertiesMax !== undefined) filterParams.numberOfPropertiesMax = filters.numberOfPropertiesMax;
    if (filters.numberOfOfficesMin !== undefined) filterParams.numberOfOfficesMin = filters.numberOfOfficesMin;
    if (filters.numberOfOfficesMax !== undefined) filterParams.numberOfOfficesMax = filters.numberOfOfficesMax;
    if (filters.numberOfEmployeesMin !== undefined) filterParams.numberOfEmployeesMin = filters.numberOfEmployeesMin;
    if (filters.numberOfEmployeesMax !== undefined) filterParams.numberOfEmployeesMax = filters.numberOfEmployeesMax;
    if (filters.annualRevenueMin !== undefined) filterParams.annualRevenueMin = filters.annualRevenueMin;
    if (filters.annualRevenueMax !== undefined) filterParams.annualRevenueMax = filters.annualRevenueMax;
    
    // V2 Financial information filters
    if (filters.balanceSheetStrength?.length) filterParams.balanceSheetStrength = filters.balanceSheetStrength;
    if (filters.fundingSources?.length) filterParams.fundingSources = filters.fundingSources;
    if (filters.creditRating?.length) filterParams.creditRating = filters.creditRating;
    if (filters.dryPowderMin !== undefined) filterParams.dryPowderMin = filters.dryPowderMin;
    if (filters.dryPowderMax !== undefined) filterParams.dryPowderMax = filters.dryPowderMax;
    
    // V2 Investment & Fund information filters
    if (filters.investmentVehicleType?.length) filterParams.investmentVehicleType = filters.investmentVehicleType;
    if (filters.fundraisingStatus?.length) filterParams.fundraisingStatus = filters.fundraisingStatus;
    if (filters.lenderType?.length) filterParams.lenderType = filters.lenderType;
    if (filters.annualLoanVolumeMin !== undefined) filterParams.annualLoanVolumeMin = filters.annualLoanVolumeMin;
    if (filters.annualLoanVolumeMax !== undefined) filterParams.annualLoanVolumeMax = filters.annualLoanVolumeMax;
    if (filters.portfolioHealth?.length) filterParams.portfolioHealth = filters.portfolioHealth;
    
    // V2 Partnership & Leadership filters
    if (filters.partnerships?.length) filterParams.partnerships = filters.partnerships;
    if (filters.keyEquityPartners?.length) filterParams.keyEquityPartners = filters.keyEquityPartners;
    if (filters.keyDebtPartners?.length) filterParams.keyDebtPartners = filters.keyDebtPartners;
    if (filters.keyExecutives?.length) filterParams.keyExecutives = filters.keyExecutives;
    
    // V2 Market positioning filters
    if (filters.sustainabilityEsgFocus !== undefined) filterParams.sustainabilityEsgFocus = filters.sustainabilityEsgFocus;
    if (filters.technologyProptechAdoption !== undefined) filterParams.technologyProptechAdoption = filters.technologyProptechAdoption;
    if (filters.adaptiveReuseExperience !== undefined) filterParams.adaptiveReuseExperience = filters.adaptiveReuseExperience;
    if (filters.regulatoryZoningExpertise !== undefined) filterParams.regulatoryZoningExpertise = filters.regulatoryZoningExpertise;
    
    // V2 Corporate structure filters
    if (filters.corporateStructure?.length) filterParams.corporateStructure = filters.corporateStructure;
    if (filters.parentCompany?.length) filterParams.parentCompany = filters.parentCompany;
    if (filters.stockTickerSymbol?.length) filterParams.stockTickerSymbol = filters.stockTickerSymbol;
    if (filters.stockExchange?.length) filterParams.stockExchange = filters.stockExchange;
    
    // Contact processor flags
    if (filters.hasContacts !== undefined) filterParams.hasContacts = filters.hasContacts;
    if (filters.contactsEmailVerificationStatus?.length) filterParams.contactsEmailVerificationStatus = filters.contactsEmailVerificationStatus;
    if (filters.contactsEnrichmentStatus?.length) filterParams.contactsEnrichmentStatus = filters.contactsEnrichmentStatus;
    if (filters.contactsEnrichmentV2Status?.length) filterParams.contactsEnrichmentV2Status = filters.contactsEnrichmentV2Status;
    if (filters.contactsEmailGenerationStatus?.length) filterParams.contactsEmailGenerationStatus = filters.contactsEmailGenerationStatus;
    if (filters.contactsEmailSendingStatus?.length) filterParams.contactsEmailSendingStatus = filters.contactsEmailSendingStatus;
    if (filters.contactsInvestmentCriteriaStatus?.length) filterParams.contactsInvestmentCriteriaStatus = filters.contactsInvestmentCriteriaStatus;
    
    // Investment criteria filters
    if (filters.capitalPosition?.length) filterParams.capitalPosition = filters.capitalPosition;
    if (filters.propertyTypes?.length) filterParams.propertyTypes = filters.propertyTypes;
    if (filters.strategies?.length) filterParams.strategies = filters.strategies;
    if (filters.regions?.length) filterParams.regions = filters.regions;
    if (filters.states?.length) filterParams.states = filters.states;
    if (filters.cities?.length) filterParams.cities = filters.cities;
    if (filters.countries?.length) filterParams.countries = filters.countries;
    if (filters.dealSizeMin !== undefined) filterParams.dealSizeMin = filters.dealSizeMin;
    if (filters.dealSizeMax !== undefined) filterParams.dealSizeMax = filters.dealSizeMax;
    
    // Investment criteria debt fields
    if (filters.loanTypes?.length) filterParams.loanTypes = filters.loanTypes;
    if (filters.loanProgram?.length) filterParams.loanProgram = filters.loanProgram;
    if (filters.structuredLoanTranche?.length) filterParams.structuredLoanTranche = filters.structuredLoanTranche;
    if (filters.recourseLoan?.length) filterParams.recourseLoan = filters.recourseLoan;
    if (filters.eligibleBorrower?.length) filterParams.eligibleBorrower = filters.eligibleBorrower;
    if (filters.lienPosition?.length) filterParams.lienPosition = filters.lienPosition;
    if (filters.rateLock?.length) filterParams.rateLock = filters.rateLock;
    if (filters.rateType?.length) filterParams.rateType = filters.rateType;
    if (filters.amortization?.length) filterParams.amortization = filters.amortization;
    if (filters.loanTypeNormalized?.length) filterParams.loanTypeNormalized = filters.loanTypeNormalized;
    if (filters.loanMinDebtYield?.length) filterParams.loanMinDebtYield = filters.loanMinDebtYield;
    if (filters.futureFacilities?.length) filterParams.futureFacilities = filters.futureFacilities;
    if (filters.occupancyRequirements?.length) filterParams.occupancyRequirements = filters.occupancyRequirements;
    if (filters.prepayment?.length) filterParams.prepayment = filters.prepayment;
    if (filters.yieldMaintenance?.length) filterParams.yieldMaintenance = filters.yieldMaintenance;
    
    // Investment criteria debt range fields
    if (filters.closingTimeMin !== undefined) filterParams.closingTimeMin = filters.closingTimeMin;
    if (filters.closingTimeMax !== undefined) filterParams.closingTimeMax = filters.closingTimeMax;
    if (filters.minLoanDscrMin !== undefined) filterParams.minLoanDscrMin = filters.minLoanDscrMin;
    if (filters.minLoanDscrMax !== undefined) filterParams.minLoanDscrMax = filters.minLoanDscrMax;
    if (filters.maxLoanDscrMin !== undefined) filterParams.maxLoanDscrMin = filters.maxLoanDscrMin;
    if (filters.maxLoanDscrMax !== undefined) filterParams.maxLoanDscrMax = filters.maxLoanDscrMax;
    if (filters.loanOriginationMaxFeeMin !== undefined) filterParams.loanOriginationMaxFeeMin = filters.loanOriginationMaxFeeMin;
    if (filters.loanOriginationMaxFeeMax !== undefined) filterParams.loanOriginationMaxFeeMax = filters.loanOriginationMaxFeeMax;
    if (filters.loanOriginationMinFeeMin !== undefined) filterParams.loanOriginationMinFeeMin = filters.loanOriginationMinFeeMin;
    if (filters.loanOriginationMinFeeMax !== undefined) filterParams.loanOriginationMinFeeMax = filters.loanOriginationMinFeeMax;
    if (filters.loanExitMinFeeMin !== undefined) filterParams.loanExitMinFeeMin = filters.loanExitMinFeeMin;
    if (filters.loanExitMinFeeMax !== undefined) filterParams.loanExitMinFeeMax = filters.loanExitMinFeeMax;
    if (filters.loanExitMaxFeeMin !== undefined) filterParams.loanExitMaxFeeMin = filters.loanExitMaxFeeMin;
    if (filters.loanExitMaxFeeMax !== undefined) filterParams.loanExitMaxFeeMax = filters.loanExitMaxFeeMax;
    if (filters.loanInterestRateSofrMin !== undefined) filterParams.loanInterestRateSofrMin = filters.loanInterestRateSofrMin;
    if (filters.loanInterestRateSofrMax !== undefined) filterParams.loanInterestRateSofrMax = filters.loanInterestRateSofrMax;
    if (filters.loanInterestRateWsjMin !== undefined) filterParams.loanInterestRateWsjMin = filters.loanInterestRateWsjMin;
    if (filters.loanInterestRateWsjMax !== undefined) filterParams.loanInterestRateWsjMax = filters.loanInterestRateWsjMax;
    if (filters.loanInterestRatePrimeMin !== undefined) filterParams.loanInterestRatePrimeMin = filters.loanInterestRatePrimeMin;
    if (filters.loanInterestRatePrimeMax !== undefined) filterParams.loanInterestRatePrimeMax = filters.loanInterestRatePrimeMax;
    if (filters.loanInterestRate3ytMin !== undefined) filterParams.loanInterestRate3ytMin = filters.loanInterestRate3ytMin;
    if (filters.loanInterestRate3ytMax !== undefined) filterParams.loanInterestRate3ytMax = filters.loanInterestRate3ytMax;
    if (filters.loanInterestRate5ytMin !== undefined) filterParams.loanInterestRate5ytMin = filters.loanInterestRate5ytMin;
    if (filters.loanInterestRate5ytMax !== undefined) filterParams.loanInterestRate5ytMax = filters.loanInterestRate5ytMax;
    if (filters.loanInterestRate10ytMin !== undefined) filterParams.loanInterestRate10ytMin = filters.loanInterestRate10ytMin;
    if (filters.loanInterestRate10ytMax !== undefined) filterParams.loanInterestRate10ytMax = filters.loanInterestRate10ytMax;
    if (filters.loanInterestRate30ytMin !== undefined) filterParams.loanInterestRate30ytMin = filters.loanInterestRate30ytMin;
    if (filters.loanInterestRate30ytMax !== undefined) filterParams.loanInterestRate30ytMax = filters.loanInterestRate30ytMax;
    if (filters.loanToValueMinMin !== undefined) filterParams.loanToValueMinMin = filters.loanToValueMinMin;
    if (filters.loanToValueMinMax !== undefined) filterParams.loanToValueMinMax = filters.loanToValueMinMax;
    if (filters.loanToValueMaxMin !== undefined) filterParams.loanToValueMaxMin = filters.loanToValueMaxMin;
    if (filters.loanToValueMaxMax !== undefined) filterParams.loanToValueMaxMax = filters.loanToValueMaxMax;
    if (filters.loanToCostMinMin !== undefined) filterParams.loanToCostMinMin = filters.loanToCostMinMin;
    if (filters.loanToCostMinMax !== undefined) filterParams.loanToCostMinMax = filters.loanToCostMinMax;
    if (filters.loanToCostMaxMin !== undefined) filterParams.loanToCostMaxMin = filters.loanToCostMaxMin;
    if (filters.loanToCostMaxMax !== undefined) filterParams.loanToCostMaxMax = filters.loanToCostMaxMax;
    if (filters.minLoanTermMin !== undefined) filterParams.minLoanTermMin = filters.minLoanTermMin;
    if (filters.minLoanTermMax !== undefined) filterParams.minLoanTermMax = filters.minLoanTermMax;
    if (filters.maxLoanTermMin !== undefined) filterParams.maxLoanTermMin = filters.maxLoanTermMin;
    if (filters.maxLoanTermMax !== undefined) filterParams.maxLoanTermMax = filters.maxLoanTermMax;
    
    // Investment criteria equity fields
    if (filters.ownershipRequirement?.length) filterParams.ownershipRequirement = filters.ownershipRequirement;
    if (filters.minimumYieldOnCostMin !== undefined) filterParams.minimumYieldOnCostMin = filters.minimumYieldOnCostMin;
    if (filters.minimumYieldOnCostMax !== undefined) filterParams.minimumYieldOnCostMax = filters.minimumYieldOnCostMax;
    if (filters.maxLeverageToleranceMin !== undefined) filterParams.maxLeverageToleranceMin = filters.maxLeverageToleranceMin;
    if (filters.maxLeverageToleranceMax !== undefined) filterParams.maxLeverageToleranceMax = filters.maxLeverageToleranceMax;
    
    // Investment criteria equity range fields
    if (filters.targetReturnMin !== undefined) filterParams.targetReturnMin = filters.targetReturnMin;
    if (filters.targetReturnMax !== undefined) filterParams.targetReturnMax = filters.targetReturnMax;
    if (filters.minimumIrrMin !== undefined) filterParams.minimumIrrMin = filters.minimumIrrMin;
    if (filters.minimumIrrMax !== undefined) filterParams.minimumIrrMax = filters.minimumIrrMax;
    if (filters.targetCashOnCashMin !== undefined) filterParams.targetCashOnCashMin = filters.targetCashOnCashMin;
    if (filters.targetCashOnCashMax !== undefined) filterParams.targetCashOnCashMax = filters.targetCashOnCashMax;
    if (filters.minHoldPeriodYearsMin !== undefined) filterParams.minHoldPeriodYearsMin = filters.minHoldPeriodYearsMin;
    if (filters.minHoldPeriodYearsMax !== undefined) filterParams.minHoldPeriodYearsMax = filters.minHoldPeriodYearsMax;
    if (filters.maxHoldPeriodYearsMin !== undefined) filterParams.maxHoldPeriodYearsMin = filters.maxHoldPeriodYearsMin;
    if (filters.maxHoldPeriodYearsMax !== undefined) filterParams.maxHoldPeriodYearsMax = filters.maxHoldPeriodYearsMax;
    
    // Investment criteria additional fields
    if (filters.decisionMakingProcess?.length) filterParams.decisionMakingProcess = filters.decisionMakingProcess;
    if (filters.investmentCriteriaNotes?.length) filterParams.investmentCriteriaNotes = filters.investmentCriteriaNotes;
    
    // NOT filters
    if (filters.notSource?.length) filterParams.notSource = filters.notSource;
    if (filters.notIndustry?.length) filterParams.notIndustry = filters.notIndustry;
    if (filters.notCompanyType?.length) filterParams.notCompanyType = filters.notCompanyType;
    if (filters.notWebsiteScrapingStatus?.length) filterParams.notWebsiteScrapingStatus = filters.notWebsiteScrapingStatus;
    if (filters.notCompanyOverviewStatus?.length) filterParams.notCompanyOverviewStatus = filters.notCompanyOverviewStatus;
    if (filters.notOverviewV2Status?.length) filterParams.notOverviewV2Status = filters.notOverviewV2Status;
    if (filters.notInvestmentCriteriaStatus?.length) filterParams.notInvestmentCriteriaStatus = filters.notInvestmentCriteriaStatus;
    if (filters.notContactsEmailVerificationStatus?.length) filterParams.notContactsEmailVerificationStatus = filters.notContactsEmailVerificationStatus;
    if (filters.notContactsEnrichmentStatus?.length) filterParams.notContactsEnrichmentStatus = filters.notContactsEnrichmentStatus;
    if (filters.notContactsEnrichmentV2Status?.length) filterParams.notContactsEnrichmentV2Status = filters.notContactsEnrichmentV2Status;
    if (filters.notContactsEmailGenerationStatus?.length) filterParams.notContactsEmailGenerationStatus = filters.notContactsEmailGenerationStatus;
    if (filters.notContactsEmailSendingStatus?.length) filterParams.notContactsEmailSendingStatus = filters.notContactsEmailSendingStatus;
    if (filters.notContactsInvestmentCriteriaStatus?.length) filterParams.notContactsInvestmentCriteriaStatus = filters.notContactsInvestmentCriteriaStatus;
    if (filters.notInvestmentFocus?.length) filterParams.notInvestmentFocus = filters.notInvestmentFocus;
    if (filters.notCapitalPosition?.length) filterParams.notCapitalPosition = filters.notCapitalPosition;
    if (filters.notPropertyTypes?.length) filterParams.notPropertyTypes = filters.notPropertyTypes;
    if (filters.notStrategies?.length) filterParams.notStrategies = filters.notStrategies;
    if (filters.notPartnerships?.length) filterParams.notPartnerships = filters.notPartnerships;
    if (filters.notFundraisingStatus?.length) filterParams.notFundraisingStatus = filters.notFundraisingStatus;
    if (filters.notLenderType?.length) filterParams.notLenderType = filters.notLenderType;
    if (filters.notLoanTypes?.length) filterParams.notLoanTypes = filters.notLoanTypes;
    if (filters.notStructuredLoanTranche?.length) filterParams.notStructuredLoanTranche = filters.notStructuredLoanTranche;
    if (filters.notLoanProgram?.length) filterParams.notLoanProgram = filters.notLoanProgram;
    if (filters.notRecourseLoan?.length) filterParams.notRecourseLoan = filters.notRecourseLoan;
    if (filters.notEligibleBorrower?.length) filterParams.notEligibleBorrower = filters.notEligibleBorrower;
    if (filters.notLienPosition?.length) filterParams.notLienPosition = filters.notLienPosition;
    if (filters.notOwnershipRequirement?.length) filterParams.notOwnershipRequirement = filters.notOwnershipRequirement;
    if (filters.notRateType?.length) filterParams.notRateType = filters.notRateType;
    if (filters.notAmortization?.length) filterParams.notAmortization = filters.notAmortization;
    
    return filterParams;
  };

  const getStageConfig = (stage: CompanyProcessingStage) => {
    const configs = {
      website_scraping: {
        title: 'Website Scraping',
        icon: Database,
        color: 'bg-blue-500',
        description: 'Scrape company websites for data'
      },
      company_overview_v2: {
        title: 'Company Overview V2',
        icon: Zap,
        color: 'bg-green-500',
        description: 'Extract structured company overview (V2)'
      },
      company_investment_criteria: {
        title: 'Investment Criteria',
        icon: CheckSquare,
        color: 'bg-orange-500',
        description: 'Extract investment criteria and preferences'
      }
    };
    return configs[stage];
  };

  const getCompanyProcessingStatus = (company: UnifiedCompanyData, stage: CompanyProcessingStage) => {
    switch (stage) {
      case 'website_scraping':
        return (company as any).website_scraping_status;
      case 'company_overview_v2':
        return (company as any).overview_v2_status;
      case 'company_investment_criteria':
        return (company as any).investment_criteria_status;
      default:
        return null;
    }
  };

  const getSelectedCompaniesStatusSummary = (stage: CompanyProcessingStage) => {
    if (selectedCompanies.size === 0) return null;

    const statusCounts: { [key: string]: number } = {};
    let total = 0;

    companies.forEach(company => {
      if (selectedCompanies.has(company.company_id)) {
        const status = getCompanyProcessingStatus(company, stage);
        if (status) {
          statusCounts[status] = (statusCounts[status] || 0) + 1;
        }
        total++;
      }
    });

    return { statusCounts, total };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="w-full px-6 py-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Companies</h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage and explore your company database
            </p>
          </div>
          <Button
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => router.push("/dashboard/companies/add")}
          >
            <PlusCircle className="h-4 w-4" />
            Add Company
          </Button>
        </header>



        {/* V2 Company Filters Only */}
        <CompanyUnifiedFiltersV2Component
          filters={filters}
          mappings={mappings}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
          isLoading={loading}
        />

        {/* Company Selection and Processing */}
        {companies.length > 0 && (
          <Card className="mb-6 border-0 shadow-sm bg-gradient-to-r from-blue-50 via-purple-50 to-indigo-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <CheckSquare className="h-5 w-5 text-blue-600" />
                Company Processing
                <Badge variant="secondary" className="bg-blue-100 text-blue-700 border border-blue-200">
                  Batch Operations
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Select all {companies.length} companies
                    </span>
                  </label>
                  {selectedCompanies.size > 0 && (
                    <Badge className="bg-blue-100 text-blue-700 border border-blue-200">
                      {selectedCompanies.size} selected
                    </Badge>
                  )}
                </div>
              </div>

              {selectedCompanies.size > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {processingJobs.map((job) => {
                    const config = getStageConfig(job.stage);
                    const statusSummary = getSelectedCompaniesStatusSummary(job.stage);
                    
                    return (
                      <Card key={job.stage} className="border border-gray-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <config.icon className="h-4 w-4 text-gray-600" />
                              <span className="font-medium text-gray-900">{config.title}</span>
                            </div>
                            {statusSummary && (
                              <div className="flex items-center gap-1">
                                {Object.entries(statusSummary.statusCounts).map(([status, count]) => (
                                  <Badge key={status} variant="outline" className="text-xs">
                                    {status}: {count}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-3">
                            {config.description}
                          </p>
                          
                          <Button
                            onClick={() => executeProcessingJob(job.stage)}
                            disabled={job.isExecuting}
                            className="w-full"
                            size="sm"
                          >
                            {job.isExecuting ? (
                              <>
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Process {selectedCompanies.size} Company{selectedCompanies.size !== 1 ? 'ies' : 'y'}
                              </>
                            )}
                          </Button>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Results Summary */}
        <div className="mb-4 flex items-center justify-between">
          <div className="text-sm text-gray-600 flex items-center">
            {loading ? (
              <span>Loading companies...</span>
            ) : (
              <span>
                Showing {companies.length} of {totalCompanies.toLocaleString()} companies
                {(filters.companyNameSearch || filters.companyWebsiteSearch) && (
                  <span className="text-blue-600">
                    {filters.companyNameSearch && ` matching name "${filters.companyNameSearch}"`}
                    {filters.companyWebsiteSearch && ` matching website "${filters.companyWebsiteSearch}"`}
                  </span>
                )}
              </span>
            )}
          </div>
          <div className="flex items-center gap-4">
            <PaginationSizeSelector
              currentSize={filters.limit || 25}
              onSizeChange={handlePaginationSizeChange}
            />
            {totalPages > 1 && (
              <Pagination
                currentPage={filters.page || 1}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>

        {/* Companies Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          {loading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))
          ) : companies.length === 0 ? (
            // No results
            <div className="col-span-full text-center py-12">
              <img 
                src="/api/placeholder/200/200" 
                alt="No companies found" 
                className="mx-auto mb-4 opacity-50"
              />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No companies found</h3>
              <p className="text-gray-500 mb-4">
                Try adjusting your filters or search terms to find companies.
              </p>
              <Button
                onClick={handleClearFilters}
                variant="outline"
                className="mx-auto"
              >
                Clear all filters
              </Button>
            </div>
          ) : (
            // Company cards
            companies.map((company) => (
              <CompanyCard 
                key={company.company_id} 
                company={company}
                isSelected={selectedCompanies.has(company.company_id)}
                onToggleSelection={handleToggleSelection}
              />
            ))
          )}
        </div>


      </main>
    </div>
  );
}
