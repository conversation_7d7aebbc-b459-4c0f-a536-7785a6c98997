'use client'

import { useState } from 'react'
import { CompanyDetail } from '../shared/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { 
  Building, 
  Briefcase, 
  Globe, 
  DollarSign, 
  Target, 
  Users, 
  MapPin, 
  Home, 
  TrendingUp, 
  Percent, 
  Calendar, 
  FileText, 
  CreditCard,
  Phone,
  Mail,
  Linkedin,
  ExternalLink,
  Factory,
  Award,
  PieChart,
  BarChart3,
  HandHeart,
  Crown,
  Building2,
  Landmark,
  Info,
  Star,
  Shield,
  Activity,
  Network,
  TrendingDown,
  Eye,
  CheckCircle,
  AlertCircle,
  XCircle,
  Layers,
  BookOpen,
  Banknote,
  Calculator,
  Scale,
  Compass,
  TreePine,
  Cpu,
  Recycle,
  Gavel,
  ChevronDown,
  ChevronUp,
  Twitter,
  Facebook,
  Instagram,
  Youtube,
  Sparkles,
  BarChart,
  Database,
  Target as TargetIcon,
  Building as BuildingIcon,
  DollarSign as DollarSignIcon,
  Users as UsersIcon,
  MapPin as MapPinIcon,
  Briefcase as BriefcaseIcon,
  Globe as GlobeIcon,
  Phone as PhoneIcon,
  Mail as MailIcon,
  Linkedin as LinkedinIcon,
  Twitter as TwitterIcon,
  Facebook as FacebookIcon,
  Instagram as InstagramIcon,
  Youtube as YoutubeIcon,
  Award as AwardIcon,
  Star as StarIcon,
  Activity as ActivityIcon,
  Network as NetworkIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Eye as EyeIcon,
  CheckCircle as CheckCircleIcon,
  AlertCircle as AlertCircleIcon,
  XCircle as XCircleIcon,
  Layers as LayersIcon,
  BookOpen as BookOpenIcon,
  Banknote as BanknoteIcon,
  Calculator as CalculatorIcon,
  Scale as ScaleIcon,
  Compass as CompassIcon,
  TreePine as TreePineIcon,
  Cpu as CpuIcon,
  Recycle as RecycleIcon,
  Gavel as GavelIcon,
  ChevronDown as ChevronDownIcon,
  ChevronUp as ChevronUpIcon,
  Sparkles as SparklesIcon,
  BarChart as BarChartIcon
} from 'lucide-react'
import { useEffect } from 'react'
import { useCompanyWebPages } from '@/hooks/useCompanyWebPages'
import CompanyWebPages from './CompanyWebPages'

interface CompanyOverviewV2Props {
  company: CompanyDetail
}

export default function CompanyOverviewV2({ company }: CompanyOverviewV2Props) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    'company-profile': true, // Company Profile always expanded
    'financial-health': true, // Financial Health & AUM always expanded
  })
  
  // Admin mode state - In production, this should be based on user roles/permissions
  const [isAdminMode, setIsAdminMode] = useState(false)
  
  // Source display state
  const [selectedField, setSelectedField] = useState<string | null>(null)
  const [showSourceModal, setShowSourceModal] = useState(false)
  
  // Fetch web pages data
  const { webPages, topPages, rankingSummary, loading: webPagesLoading, error: webPagesError } = useCompanyWebPages(company.company_id)
  
  // Check if user is admin (this is a simple implementation - replace with actual auth check)
  useEffect(() => {
    const adminMode = localStorage.getItem('admin_mode') === 'true' || process.env.NODE_ENV === 'development'
    setIsAdminMode(adminMode)
  }, [])

  // Helper functions for source display
  const getConfidenceColor = (score?: number) => {
    if (!score) return 'text-gray-500'
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getConfidenceBadgeVariant = (score?: number) => {
    if (!score) return 'secondary'
    if (score >= 0.9) return 'default'
    if (score >= 0.7) return 'secondary'
    return 'destructive'
  }

  const getSourceTypeInfo = (sourceType: string) => {
    switch (sourceType) {
      case 'web_scraped_text':
        return {
          color: 'bg-green-50 text-green-700 border-green-200',
          displayName: 'Web Scraped Text',
          priority: 1
        }
      case 'enrichment_data':
        return {
          color: 'bg-purple-50 text-purple-700 border-purple-200',
          displayName: 'Enrichment Data',
          priority: 2
        }
      case 'company_website':
        return {
          color: 'bg-blue-50 text-blue-700 border-blue-200',
          displayName: 'Company Website',
          priority: 3
        }
      case 'web_search':
        return {
          color: 'bg-cyan-50 text-cyan-700 border-cyan-200',
          displayName: 'Web Search',
          priority: 4
        }
      case 'news_article':
        return {
          color: 'bg-orange-50 text-orange-700 border-orange-200',
          displayName: 'News Article',
          priority: 5
        }
      case 'press_release':
        return {
          color: 'bg-indigo-50 text-indigo-700 border-indigo-200',
          displayName: 'Press Release',
          priority: 6
        }
      default:
        return {
          color: 'bg-gray-50 text-gray-700 border-gray-200',
          displayName: sourceType.replace('_', ' '),
          priority: 7
        }
    }
  }

  // Check if a field has evidence-grounded source information
  const hasSourceInfo = (fieldName: string) => {
    const sources = (company as any).overview_sources
    if (!sources) return false
    
    try {
      const parsedSources = typeof sources === 'string' ? JSON.parse(sources) : sources
      if (parsedSources && parsedSources.all_sources && Array.isArray(parsedSources.all_sources)) {
        return parsedSources.all_sources.some((source: any) => source.field === fieldName)
      }
    } catch (e) {
      console.error('Error parsing sources:', e)
    }
    
    return false
  }

  // Get field source information
  const getFieldSourceInfo = (fieldName: string) => {
    const sources = (company as any).overview_sources
    if (!sources) return null
    
    try {
      const parsedSources = typeof sources === 'string' ? JSON.parse(sources) : sources
      if (parsedSources && parsedSources.all_sources && Array.isArray(parsedSources.all_sources)) {
        const fieldSources = parsedSources.all_sources.filter((source: any) => source.field === fieldName)
        if (fieldSources.length > 0) {
          return {
            sources: fieldSources,
            averageConfidence: fieldSources.reduce((sum: number, s: any) => sum + (s.confidence_score || 0), 0) / fieldSources.length
          }
        }
      }
    } catch (e) {
      console.error('Error parsing field sources:', e)
    }
    
    return null
  }

  // Render all sources in one centralized collapsible section
  const renderAllSources = () => {
    const hasOverviewSummary = !!(company as any).overview_summary && (
      (company as any).overview_summary.financial_metrics_summary_text ||
      (company as any).overview_summary.investment_criteria_summary_text ||
      ((company as any).overview_summary.exact_snippets && (company as any).overview_summary.exact_snippets.length > 0)
    )

    const hasOverviewSources = !!company.overview_sources

    if (!hasOverviewSources && !hasOverviewSummary) return null

    const sources = company.overview_sources || {}
    
    // Handle new consolidated source structure
    if ((sources as any).all_sources && Array.isArray((sources as any).all_sources)) {
      // New evidence-grounded structure
      const allSources = (sources as any).all_sources as Array<{
        field: string;
        url: string;
        page_section: string;
        evidence_quote: string;
        source_type: string;
        date_found: string;
        confidence_score: number;
      }>

      if (allSources.length === 0 && !hasOverviewSummary) return null

      // Group sources by field for better organization
      const groupedSources = allSources.reduce((groups, source) => {
        const fieldName = source.field
        let category = 'General'
        
        // Define categories based on field names
        if (fieldName.includes('company_name') || fieldName.includes('company_type') || fieldName.includes('industry') || fieldName.includes('business_model') || fieldName.includes('founded_year') || fieldName.includes('corporate_structure') || fieldName.includes('parent_company')) {
          category = 'Company Profile'
        } else if (fieldName.includes('investment') || fieldName.includes('market_cycle') || fieldName.includes('urban_vs_suburban') || fieldName.includes('sustainability')) {
          category = 'Investment Strategy'
        } else if (fieldName.includes('fund_size') || fieldName.includes('aum') || fieldName.includes('balance_sheet') || fieldName.includes('dry_powder') || fieldName.includes('annual_deployment')) {
          category = 'Financial Health'
        } else if (fieldName.includes('stock') || fieldName.includes('market_capitalization') || fieldName.includes('revenue') || fieldName.includes('income') || fieldName.includes('ebitda') || fieldName.includes('profit_margin') || fieldName.includes('credit_rating')) {
          category = 'Public Company Data'
        } else if (fieldName.includes('website') || fieldName.includes('phone') || fieldName.includes('email') || fieldName.includes('address') || fieldName.includes('city') || fieldName.includes('state') || fieldName.includes('country')) {
          category = 'Contact & Location'
        } else if (fieldName.includes('linkedin') || fieldName.includes('twitter') || fieldName.includes('facebook') || fieldName.includes('instagram') || fieldName.includes('youtube')) {
          category = 'Social Media'
        } else if (fieldName.includes('partnership') || fieldName.includes('funding') || fieldName.includes('equity_partner') || fieldName.includes('debt_partner')) {
          category = 'Partnerships & Funding'
        } else if (fieldName.includes('board') || fieldName.includes('executive') || fieldName.includes('founder') || fieldName.includes('history')) {
          category = 'Leadership & History'
        } else if (fieldName.includes('competitor') || fieldName.includes('award') || fieldName.includes('market_share') || fieldName.includes('product') || fieldName.includes('service')) {
          category = 'Market Position'
        } else if (fieldName.includes('property') || fieldName.includes('transaction') || fieldName.includes('portfolio') || fieldName.includes('deal')) {
          category = 'Portfolio & Activity'
        }
        
        if (!groups[category]) {
          groups[category] = []
        }
        
        // Group by field name
        const existingField = groups[category].find(f => f.fieldName === fieldName)
        if (existingField) {
          existingField.sources.push(source)
        } else {
          groups[category].push({ 
            fieldName, 
            sources: [source],
            fieldKey: fieldName
          })
        }
        
        return groups
      }, {} as Record<string, Array<{ fieldKey: string; fieldName: string; sources: any[] }>>)

      return renderExpandableSection(
        "Data Sources",
        <Database className="h-5 w-5 text-purple-600" />,
        <div className="space-y-6">
          {hasOverviewSummary && (
            <div className="border border-purple-200 rounded-lg p-4 bg-purple-50/50">
              <h3 className="text-lg font-semibold text-purple-900 mb-3 flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-purple-700" />
                Overview Summary
              </h3>
              <div className="space-y-3">
                {(company as any).overview_summary?.financial_metrics_summary_text && (
                  <div className="bg-white border border-purple-100 rounded p-3">
                    <div className="text-sm font-medium text-purple-800 mb-1">Financial Metrics (Exact Text)</div>
                    <p className="text-sm text-gray-800 whitespace-pre-line">{(company as any).overview_summary.financial_metrics_summary_text}</p>
                  </div>
                )}
                {(company as any).overview_summary?.investment_criteria_summary_text && (
                  <div className="bg-white border border-purple-100 rounded p-3">
                    <div className="text-sm font-medium text-purple-800 mb-1">Investment Criteria (Exact Text)</div>
                    <p className="text-sm text-gray-800 whitespace-pre-line">{(company as any).overview_summary.investment_criteria_summary_text}</p>
                  </div>
                )}
                {(company as any).overview_summary?.exact_snippets?.length > 0 && (
                  <div className="bg-white border border-purple-100 rounded p-3">
                    <div className="text-sm font-medium text-purple-800 mb-2">Exact Snippets</div>
                    <div className="space-y-2">
                      {(company as any).overview_summary.exact_snippets.map((snip: any, idx: number) => (
                        <div key={idx} className="border border-gray-100 rounded p-2">
                          <div className="flex items-center gap-2 mb-1">
                            {snip.field && (
                              <Badge className="bg-purple-50 text-purple-700 border border-purple-200 text-xxs">{snip.field}</Badge>
                            )}
                            {snip.source && typeof snip.source === 'string' && snip.source.startsWith('http') && (
                              <a href={snip.source} target="_blank" rel="noopener noreferrer" className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1">
                                <ExternalLink className="h-3 w-3" />
                                <span className="truncate max-w-[420px]">{snip.source}</span>
                              </a>
                            )}
                          </div>
                          <div className="text-sm text-gray-800 whitespace-pre-line">{snip.text}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          {Object.entries(groupedSources).map(([category, fields]) => (
            <div key={category} className="border border-purple-200 rounded-lg p-4 bg-purple-50/30">
              <h3 className="text-lg font-semibold text-purple-900 mb-4 flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                {category}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {fields.map(({ fieldKey, sources, fieldName }) => {
                  const displayName = fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                  
                  return (
                    <div key={fieldKey} className="bg-white border border-purple-100 rounded-lg p-3 shadow-sm">
                      <h4 className="text-sm font-medium text-purple-800 mb-2 flex items-center gap-1">
                        <div className="w-1 h-1 bg-purple-400 rounded-full"></div>
                        {displayName}
                      </h4>
                      <div className="space-y-2">
                        {sources.map((source, index) => (
                          <div key={index} className="text-xs text-gray-700 bg-gray-50 rounded px-2 py-1">
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-1">
                                <Badge variant="outline" className="text-xxs">
                                  {source.source_type || 'Unknown'}
                                </Badge>
                                {source.confidence_score && (
                                  <Badge variant="outline" className="text-xxs">
                                    {Math.round(source.confidence_score * 100)}%
                                  </Badge>
                                )}
                              </div>
                            </div>
                            
                            {source.evidence_quote && (
                              <div className="mb-1 text-gray-600 italic">
                                "{source.evidence_quote}"
                              </div>
                            )}
                            
                            {source.page_section && (
                              <div className="text-gray-500 text-xxs mb-1">
                                Section: {source.page_section}
                              </div>
                            )}
                            
                            {source.url && (
                              <div className="flex items-center gap-1">
                                {source.url.startsWith('http') ? (
                                  <a 
                                    href={source.url} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:text-blue-800 break-all flex items-center gap-1 hover:bg-blue-50 rounded px-1 py-0.5 -mx-1 -my-0.5"
                                  >
                                    <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{source.url}</span>
                                  </a>
                                ) : (
                                  <span className="flex items-center gap-1">
                                    <FileText className="h-3 w-3 text-gray-500 flex-shrink-0" />
                                    <span className="truncate">{source.url}</span>
                                  </span>
                                )}
                              </div>
                            )}
                            
                            {source.date_found && (
                              <div className="text-gray-400 text-xxs mt-1">
                                Found: {source.date_found}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </div>,
        'data-sources',
        true,
        {},
        'purple'
      )
    }

    // Fallback for old structure (if any)
    const sourceEntries = Object.entries(sources).filter(([_, value]) => 
      Array.isArray(value) && value.length > 0
    )
    if (sourceEntries.length === 0 && !hasOverviewSummary) return null

    // Group sources by category for better organization
    const groupedSources = sourceEntries.reduce((groups, [fieldKey, fieldSources]) => {
      const fieldName = fieldKey.replace('_sources', '')
      let category = 'General'
      
      // Define categories based on field names
      if (fieldName.includes('company_name') || fieldName.includes('company_type') || fieldName.includes('industry') || fieldName.includes('business_model') || fieldName.includes('founded_year') || fieldName.includes('corporate_structure') || fieldName.includes('parent_company')) {
        category = 'Company Profile'
      } else if (fieldName.includes('investment') || fieldName.includes('market_cycle') || fieldName.includes('urban_vs_suburban') || fieldName.includes('sustainability')) {
        category = 'Investment Strategy'
      } else if (fieldName.includes('fund_size') || fieldName.includes('aum') || fieldName.includes('balance_sheet') || fieldName.includes('dry_powder') || fieldName.includes('annual_deployment')) {
        category = 'Financial Health'
      } else if (fieldName.includes('stock') || fieldName.includes('market_capitalization') || fieldName.includes('revenue') || fieldName.includes('income') || fieldName.includes('ebitda') || fieldName.includes('profit_margin') || fieldName.includes('credit_rating')) {
        category = 'Public Company Data'
      } else if (fieldName.includes('website') || fieldName.includes('phone') || fieldName.includes('email') || fieldName.includes('address') || fieldName.includes('city') || fieldName.includes('state') || fieldName.includes('country')) {
        category = 'Contact & Location'
      } else if (fieldName.includes('linkedin') || fieldName.includes('twitter') || fieldName.includes('facebook') || fieldName.includes('instagram') || fieldName.includes('youtube')) {
        category = 'Social Media'
      } else if (fieldName.includes('partnership') || fieldName.includes('funding') || fieldName.includes('equity_partner') || fieldName.includes('debt_partner')) {
        category = 'Partnerships & Funding'
      } else if (fieldName.includes('board') || fieldName.includes('executive') || fieldName.includes('founder') || fieldName.includes('history')) {
        category = 'Leadership & History'
      } else if (fieldName.includes('competitor') || fieldName.includes('award') || fieldName.includes('market_share') || fieldName.includes('product') || fieldName.includes('service')) {
        category = 'Market Position'
      } else if (fieldName.includes('property') || fieldName.includes('transaction') || fieldName.includes('portfolio') || fieldName.includes('deal')) {
        category = 'Portfolio & Activity'
      }
      
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push({ fieldKey, fieldSources, fieldName })
      return groups
    }, {} as Record<string, Array<{ fieldKey: string, fieldSources: string[], fieldName: string }>>)

    return renderExpandableSection(
      "Data Sources",
      <Database className="h-5 w-5 text-purple-600" />,
      <div className="space-y-6">
        {hasOverviewSummary && (
          <div className="border border-purple-200 rounded-lg p-4 bg-purple-50/50">
            <h3 className="text-lg font-semibold text-purple-900 mb-3 flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-purple-700" />
              Overview Summary
            </h3>
            <div className="space-y-3">
              {(company as any).overview_summary?.financial_metrics_summary_text && (
                <div className="bg-white border border-purple-100 rounded p-3">
                  <div className="text-sm font-medium text-purple-800 mb-1">Financial Metrics (Exact Text)</div>
                  <p className="text-sm text-gray-800 whitespace-pre-line">{(company as any).overview_summary.financial_metrics_summary_text}</p>
                </div>
              )}
              {(company as any).overview_summary?.investment_criteria_summary_text && (
                <div className="bg-white border border-purple-100 rounded p-3">
                  <div className="text-sm font-medium text-purple-800 mb-1">Investment Criteria (Exact Text)</div>
                  <p className="text-sm text-gray-800 whitespace-pre-line">{(company as any).overview_summary.investment_criteria_summary_text}</p>
                </div>
              )}
              {(company as any).overview_summary?.exact_snippets?.length > 0 && (
                <div className="bg-white border border-purple-100 rounded p-3">
                  <div className="text-sm font-medium text-purple-800 mb-2">Exact Snippets</div>
                  <div className="space-y-2">
                    {(company as any).overview_summary.exact_snippets.map((snip: any, idx: number) => (
                      <div key={idx} className="border border-gray-100 rounded p-2">
                        <div className="flex items-center gap-2 mb-1">
                          {snip.field && (
                            <Badge className="bg-purple-50 text-purple-700 border border-purple-200 text-xxs">{snip.field}</Badge>
                          )}
                          {snip.source && typeof snip.source === 'string' && snip.source.startsWith('http') && (
                            <a href={snip.source} target="_blank" rel="noopener noreferrer" className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1">
                              <ExternalLink className="h-3 w-3" />
                              <span className="truncate max-w-[420px]">{snip.source}</span>
                            </a>
                          )}
                        </div>
                        <div className="text-sm text-gray-800 whitespace-pre-line">{snip.text}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        {Object.entries(groupedSources).map(([category, fields]) => (
          <div key={category} className="border border-purple-200 rounded-lg p-4 bg-purple-50/30">
            <h3 className="text-lg font-semibold text-purple-900 mb-4 flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
              {category}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {fields.map(({ fieldKey, fieldSources, fieldName }) => {
                const displayName = fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                
                return (
                  <div key={fieldKey} className="bg-white border border-purple-100 rounded-lg p-3 shadow-sm">
                    <h4 className="text-sm font-medium text-purple-800 mb-2 flex items-center gap-1">
                      <div className="w-1 h-1 bg-purple-400 rounded-full"></div>
                      {displayName}
                    </h4>
                    <div className="space-y-1">
                      {(fieldSources as any[]).map((source, index) => (
                        <div key={index} className="text-xs text-gray-700 bg-gray-50 rounded px-2 py-1">
                          {typeof source === 'string' ? (
                            source.startsWith('http') ? (
                              <a 
                                href={source} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 break-all flex items-center gap-1 hover:bg-blue-50 rounded px-1 py-0.5 -mx-1 -my-0.5"
                              >
                                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                <span className="truncate">{source}</span>
                              </a>
                            ) : (
                              <span className="flex items-center gap-1">
                                <FileText className="h-3 w-3 text-gray-500 flex-shrink-0" />
                                <span className="truncate">{source}</span>
                              </span>
                            )
                          ) : (
                            // Handle new evidence-grounded source objects
                            <div className="space-y-1">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-1">
                                  <Badge variant="outline" className="text-xxs">
                                    {source.source_type || 'Unknown'}
                                  </Badge>
                                  {source.confidence_score && (
                                    <Badge variant="outline" className="text-xxs">
                                      {Math.round(source.confidence_score * 100)}%
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              
                              {source.evidence_quote && (
                                <div className="text-gray-600 italic text-xxs">
                                  "{source.evidence_quote}"
                                </div>
                              )}
                              
                              {source.page_section && (
                                <div className="text-gray-500 text-xxs">
                                  Section: {source.page_section}
                                </div>
                              )}
                              
                              {source.url && (
                                <div className="flex items-center gap-1">
                                  {source.url.startsWith('http') ? (
                                    <a 
                                      href={source.url} 
                                      target="_blank" 
                                      rel="noopener noreferrer"
                                      className="text-blue-600 hover:text-blue-800 break-all flex items-center gap-1 hover:bg-blue-50 rounded px-1 py-0.5 -mx-1 -my-0.5"
                                    >
                                      <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                      <span className="truncate">{source.url}</span>
                                    </a>
                                  ) : (
                                    <span className="flex items-center gap-1">
                                      <FileText className="h-3 w-3 text-gray-500 flex-shrink-0" />
                                      <span className="truncate">{source.url}</span>
                                    </span>
                                  )}
                                </div>
                              )}
                              
                              {source.date_found && (
                                <div className="text-gray-400 text-xxs">
                                  Found: {source.date_found}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        ))}
      </div>,
      'data-sources',
      false,
      {},
      'purple'
    )
  }

  if (!company) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-500">No company data available</div>
      </div>
    )
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  // Helper function to check if a value has meaningful data
  const hasData = (value: any): boolean => {
    if (!value) return false
    if (Array.isArray(value)) return value.length > 0
    if (typeof value === 'string') return value.trim() !== '' && value.trim() !== 'Not specified'
    if (typeof value === 'number') return value > 0
    if (typeof value === 'boolean') return true
    return true
  }

  // Helper function to get available fields in a section
  const getAvailableFields = (fields: Record<string, any>): string[] => {
    return Object.entries(fields)
      .filter(([_, value]) => hasData(value))
      .map(([key, _]) => key)
  }

  // Enhanced currency formatting for large numbers
  const formatCurrency = (value: string | number | null | undefined) => {
    if (!value) return null
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (isNaN(numValue)) return value
    
    if (numValue >= 1000000000) {
      return `$${(numValue / 1000000000).toFixed(1)}B`
    } else if (numValue >= 1000000) {
      return `$${(numValue / 1000000).toFixed(1)}M`
    } else if (numValue >= 1000) {
      return `$${(numValue / 1000).toFixed(1)}K`
    } else {
      return `$${numValue.toLocaleString()}`
    }
  }

  const formatNumber = (value: string | number | null | undefined) => {
    if (!value) return null
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (isNaN(numValue)) return value
    return new Intl.NumberFormat('en-US').format(numValue)
  }

  const formatDecimal = (value: string | number | null | undefined) => {
    if (!value) return null
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (isNaN(numValue)) return value
    return `${(numValue * 100).toFixed(1)}%`
  }

  const renderArrayField = (values: string[] | null | undefined, colorClass: string = 'blue', maxDisplay: number = 5) => {
    if (!values || values.length === 0) return null
    
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-700 border-blue-200',
      green: 'bg-green-50 text-green-700 border-green-200',
      purple: 'bg-purple-50 text-purple-700 border-purple-200',
      orange: 'bg-orange-50 text-orange-700 border-orange-200',
      red: 'bg-red-50 text-red-700 border-red-200',
      yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      gray: 'bg-gray-50 text-gray-700 border-gray-200',
      indigo: 'bg-indigo-50 text-indigo-700 border-indigo-200',
      emerald: 'bg-emerald-50 text-emerald-700 border-emerald-200',
      pink: 'bg-pink-50 text-pink-700 border-pink-200',
      cyan: 'bg-cyan-50 text-cyan-700 border-cyan-200'
    }

    return (
      <div className="flex flex-wrap gap-1 mt-1">
        {values.map((value: string, index: number) => (
          <Badge key={index} className={`${colorClasses[colorClass as keyof typeof colorClasses]} text-xs border`}>
            {value}
          </Badge>
        ))}
      </div>
    )
  }

  const renderInfoField = (label: string, value: any, icon: React.ReactNode, colorClass: string = 'blue', isLink: boolean = false, fieldName?: string) => {
    if (!value) return null

    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600',
      red: 'bg-red-50 text-red-600',
      yellow: 'bg-yellow-50 text-yellow-600',
      gray: 'bg-gray-50 text-gray-600',
      indigo: 'bg-indigo-50 text-indigo-600',
      emerald: 'bg-emerald-50 text-emerald-600',
      pink: 'bg-pink-50 text-pink-600',
      cyan: 'bg-cyan-50 text-cyan-600'
    }

    // Check if field has source information
    const hasSource = fieldName && hasSourceInfo(fieldName)
    const sourceInfo = fieldName ? getFieldSourceInfo(fieldName) : null

    return (
      <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
        <div className={`p-2 rounded-md ${colorClasses[colorClass as keyof typeof colorClasses]}`}>
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <div className="text-sm font-medium text-gray-500">{label}</div>
            {hasSource && sourceInfo && (
              <div className="flex items-center gap-2">
                <Badge 
                  variant={getConfidenceBadgeVariant(sourceInfo.averageConfidence)}
                  className={`text-xs ${getConfidenceColor(sourceInfo.averageConfidence)} px-2 py-0.5`}
                >
                  {Math.round((sourceInfo.averageConfidence || 0) * 100)}%
                </Badge>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-blue-50"
                      onClick={() => setSelectedField(fieldName)}
                    >
                      <Info className="h-3 w-3 text-blue-500 hover:text-blue-700" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Source Information: {label}</DialogTitle>
                      <DialogDescription>
                        Evidence and sources used to extract this information
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h4 className="font-medium text-blue-900 mb-2">Extracted Value</h4>
                        <p className="text-blue-800">
                          {Array.isArray(value) ? value.join(', ') : String(value)}
                        </p>
                      </div>
                      
                      <div className="space-y-3">
                        <h4 className="font-medium text-gray-900">Sources ({sourceInfo.sources.length})</h4>
                        {sourceInfo.sources.map((source: any, idx: number) => {
                          const typeInfo = getSourceTypeInfo(source.source_type)
                          return (
                            <div key={idx} className="border rounded-lg p-3 bg-gray-50">
                              <div className="flex items-center justify-between mb-2">
                                <Badge variant="outline" className={`text-xs ${typeInfo.color}`}>
                                  {typeInfo.displayName}
                                </Badge>
                                {source.confidence_score && (
                                  <Badge 
                                    variant={getConfidenceBadgeVariant(source.confidence_score)}
                                    className="text-xs"
                                  >
                                    {Math.round(source.confidence_score * 100)}%
                                  </Badge>
                                )}
                              </div>
                              
                              {source.evidence_quote && (
                                <div className="mb-2">
                                  <p className="text-sm text-gray-700 italic">"{source.evidence_quote}"</p>
                                </div>
                              )}
                              
                              {source.page_section && (
                                <p className="text-xs text-gray-500 mb-1">Section: {source.page_section}</p>
                              )}
                              
                              {source.url && (
                                <a 
                                  href={source.url} 
                                  target="_blank" 
                                  rel="noopener noreferrer" 
                                  className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                                >
                                  <ExternalLink className="h-3 w-3" />
                                  <span className="truncate">{source.url}</span>
                                </a>
                              )}
                              
                              {source.date_found && (
                                <p className="text-xs text-gray-400 mt-1">Found: {source.date_found}</p>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            )}
          </div>
          <div className="font-medium text-gray-900">
            {Array.isArray(value) ? renderArrayField(value, colorClass) : (
              isLink ? (
                <a href={value} target="_blank" rel="noopener noreferrer" 
                   className="text-blue-600 hover:text-blue-800 flex items-center gap-1">
                  {value}
                  <ExternalLink className="h-3 w-3" />
                </a>
              ) : value
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderExpandableSection = (
    title: string, 
    icon: React.ReactNode, 
    children: React.ReactNode, 
    sectionId: string, 
    defaultExpanded: boolean = false,
    fields: Record<string, any> = {},
    colorClass: string = 'blue'
  ) => {
    const isExpanded = expandedSections[sectionId] ?? defaultExpanded
    const availableFields = getAvailableFields(fields)
    
    return (
      <Card className="border border-gray-200 shadow-sm">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={() => toggleSection(sectionId)}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {icon}
              {title}
              {/* Show available fields chips when collapsed */}
              {!isExpanded && availableFields.length > 0 && (
                <div className="flex items-center gap-1 ml-3">
                  <Badge className="bg-gray-100 text-gray-700 border border-gray-300 font-bold text-xs px-2 py-0.5">
                    {availableFields.length}
                  </Badge>
                  <span className="text-xs text-gray-500">•</span>
                  <div className="flex flex-wrap gap-1">
                    {availableFields.map((field, index) => (
                      <Badge 
                        key={index} 
                        className="bg-gray-50 text-gray-600 border border-gray-200 text-xs px-2 py-0.5"
                      >
                        {field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
            {isExpanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </CardTitle>
        </CardHeader>
        {isExpanded && (
          <CardContent>
            {children}
          </CardContent>
        )}
      </Card>
    )
  }

  // Helper function to check if a section has any meaningful data
  const sectionHasData = (fields: Record<string, any>): boolean => {
    return Object.values(fields).some(value => hasData(value))
  }

  return (
    <div className="space-y-6">
      {/* Header with Admin Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Building2 className="h-6 w-6 text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-900">{company.company_name}</h1>
        </div>
        
        {/* Admin Mode Toggle - Only show if admin permissions are available */}
        {(localStorage.getItem('admin_mode') === 'true' || process.env.NODE_ENV === 'development') && (
          <div className="flex items-center space-x-2">
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={isAdminMode}
                onChange={(e) => setIsAdminMode(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-gray-700">Admin Mode (Show Sources)</span>
            </label>
          </div>
        )}
      </div>

      {/* Company Profile - Always Visible */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-900">
            <Building2 className="h-5 w-5" />
            Company Profile
          </CardTitle>
        </CardHeader>
        <CardContent>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {renderInfoField("Company Name", company.company_name, <Building2 className="h-5 w-5" />, 'blue', false, 'company_name')}
            {renderInfoField("Company Type", company.company_type, <Building className="h-5 w-5" />, 'purple', false, 'company_type')}
            {renderInfoField("Industry", company.industry, <Factory className="h-5 w-5" />, 'indigo', false, 'company_industry')}
            {renderInfoField("Business Model", company.business_model, <Briefcase className="h-5 w-5" />, 'orange', false, 'business_model')}
            {renderInfoField("Founded Year", company.founded_year, <Calendar className="h-5 w-5" />, 'indigo', false, 'founded_year')}
            {renderInfoField("Number of Offices", company.number_of_offices, <Building className="h-5 w-5" />, 'cyan', false, 'number_of_offices')}
            {renderInfoField("Number of Employees", company.number_of_employees, <Users className="h-5 w-5" />, 'pink', false, 'number_of_employees')}
            {renderInfoField("Corporate Structure", company.corporate_structure, <Building2 className="h-5 w-5" />, 'gray', false, 'corporate_structure')}
            {renderInfoField("Parent Company", company.parent_company, <Building className="h-5 w-5" />, 'blue', false, 'parent_company')}
          </div>

          {company.subsidiaries && company.subsidiaries.length > 0 && (
            <div className="mt-6 pt-6 border-t border-blue-100">
              <div className="text-sm font-medium text-gray-500 mb-2">Subsidiaries</div>
              {renderArrayField(company.subsidiaries, 'blue')}
            </div>
          )}

          {company.summary && (
            <div className="mt-4">
              <div className="text-sm font-medium text-gray-500 mb-2">Company Summary</div>
              <p className="text-gray-700 bg-white p-4 rounded-lg border border-gray-200">{company.summary}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Investment Strategy */}
      {(() => {
        const strategyFields = {
          investment_focus: company.investment_focus,
          investment_strategy_mission: company.investment_strategy_mission,
          investment_strategy_approach: company.investment_strategy_approach,
          market_cycle_positioning: company.market_cycle_positioning,
          urban_vs_suburban_preference: company.urban_vs_suburban_preference,
          sustainability_esg_focus: company.sustainability_esg_focus
        }
        
        return sectionHasData(strategyFields) && 
          renderExpandableSection(
            "Investment Strategy", 
            <Target className="h-5 w-5 text-green-600" />,
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Investment Focus", company.investment_focus, <Target className="h-5 w-5" />, 'emerald', false, 'investment_focus')}
                {renderInfoField("Market Cycle Positioning", company.market_cycle_positioning, <Compass className="h-5 w-5" />, 'indigo', false, 'market_cycle_positioning')}
                {renderInfoField("Urban vs Suburban Preference", company.urban_vs_suburban_preference, <MapPin className="h-5 w-5" />, 'gray', false, 'urban_vs_suburban_preference')}
                {renderInfoField("ESG Focus", company.sustainability_esg_focus ? "Yes" : "No", <TreePine className="h-5 w-5" />, 'green', false, 'sustainability_esg_focus')}
              </div>
              
              <div className="space-y-4">
                {renderInfoField("Investment Strategy Mission", company.investment_strategy_mission, <Target className="h-5 w-5" />, 'green', false, 'investment_strategy_mission')}
                {renderInfoField("Investment Strategy Approach", company.investment_strategy_approach, <Compass className="h-5 w-5" />, 'blue', false, 'investment_strategy_approach')}
              </div>
            </div>,
            'strategy',
            true,
            strategyFields,
            'emerald'
          )
      })()}

      {/* Financial Health & AUM */}
      {(() => {
        const financialFields = {
          fund_size: company.fund_size,
          aum: company.aum,
          balance_sheet_strength: company.balance_sheet_strength,
          dry_powder: company.dry_powder,
          annual_deployment_target: company.annual_deployment_target
        }
        
        return sectionHasData(financialFields) && 
          renderExpandableSection(
            "Financial Health & AUM", 
            <DollarSign className="h-5 w-5 text-green-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Fund Size", formatCurrency(company.fund_size), <DollarSign className="h-5 w-5" />, 'green', false, 'fund_size')}
                {renderInfoField("Assets Under Management", formatCurrency(company.aum), <Landmark className="h-5 w-5" />, 'emerald', false, 'aum')}
                {renderInfoField("Balance Sheet Strength", company.balance_sheet_strength, <Shield className="h-5 w-5" />, 'green', false, 'balance_sheet_strength')}
                {renderInfoField("Dry Powder", formatCurrency(company.dry_powder), <DollarSign className="h-5 w-5" />, 'green', false, 'dry_powder')}
                {renderInfoField("Annual Deployment Target", formatCurrency(company.annual_deployment_target), <Target className="h-5 w-5" />, 'emerald', false, 'annual_deployment_target')}
              </div>
            </div>,
            'financial-health',
            true,
            financialFields,
            'green'
          )
      })()}
      
      {/* Public Company Data */}
      {(() => {
        const publicCompanyFields = {
          stock_ticker_symbol: company.stock_ticker_symbol,
          stock_exchange: company.stock_exchange,
          market_capitalization: company.market_capitalization,
          annual_revenue: company.annual_revenue,
          net_income: company.net_income,
          ebitda: company.ebitda,
          profit_margin: company.profit_margin,
          credit_rating: company.credit_rating,
          quarterly_earnings_link: company.quarterly_earnings_link
        }
        
        return sectionHasData(publicCompanyFields) && 
          renderExpandableSection(
            "Public Company Data", 
            <BarChart3 className="h-5 w-5 text-blue-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Stock Ticker", company.stock_ticker_symbol, <TrendingUp className="h-5 w-5" />, 'green', false, 'stock_ticker_symbol')}
                {renderInfoField("Stock Exchange", company.stock_exchange, <BarChart3 className="h-5 w-5" />, 'blue', false, 'stock_exchange')}
                {renderInfoField("Market Capitalization", formatCurrency(company.market_capitalization), <PieChart className="h-5 w-5" />, 'blue', false, 'market_capitalization')}
                {renderInfoField("Annual Revenue", formatCurrency(company.annual_revenue), <TrendingUp className="h-5 w-5" />, 'green', false, 'annual_revenue')}
                {renderInfoField("Net Income", formatCurrency(company.net_income), <DollarSign className="h-5 w-5" />, 'green', false, 'net_income')}
                {renderInfoField("EBITDA", formatCurrency(company.ebitda), <Calculator className="h-5 w-5" />, 'green', false, 'ebitda')}
                {renderInfoField("Profit Margin", formatDecimal(company.profit_margin), <Percent className="h-5 w-5" />, 'emerald', false, 'profit_margin')}
                {renderInfoField("Credit Rating", company.credit_rating, <Star className="h-5 w-5" />, 'yellow', false, 'credit_rating')}
                {renderInfoField("Quarterly Earnings Link", company.quarterly_earnings_link, <ExternalLink className="h-5 w-5" />, 'blue', true, 'quarterly_earnings_link')}
              </div>
            </div>,
            'public-company',
            false,
            publicCompanyFields,
            'blue'
          )
      })()}

            {/* Contact Information */}
      {(() => {
        const contactFields = {
          company_website: company.company_website,
          company_phone: company.company_phone,
          secondary_phone: company.secondary_phone,
          main_email: company.main_email,
          secondary_email: company.secondary_email
        }
        
        return sectionHasData(contactFields) && 
          renderExpandableSection(
            "Contact Information", 
            <Phone className="h-5 w-5 text-green-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Website", company.company_website, <Globe className="h-5 w-5" />, 'blue', true, 'website')}
              {renderInfoField("Primary Phone", company.company_phone ? (
                <a href={`tel:${company.company_phone}`} className="text-green-600 hover:text-green-800">
                  {company.company_phone}
                </a>
              ) : null, <Phone className="h-5 w-5" />, 'green', false, 'main_phone')}
              {renderInfoField("Secondary Phone", company.secondary_phone, <Phone className="h-5 w-5" />, 'green', false, 'secondary_phone')}
              {renderInfoField("Primary Email", company.main_email ? (
                <a href={`mailto:${company.main_email}`} className="text-purple-600 hover:text-purple-800">
                  {company.main_email}
                </a>
              ) : null, <Mail className="h-5 w-5" />, 'purple', false, 'main_email')}
              {renderInfoField("Secondary Email", company.secondary_email, <Mail className="h-5 w-5" />, 'purple', false, 'secondary_email')}
            </div>,
            'contact',
            false,
            contactFields,
            'green'
          )
      })()}
      
      {/* Social Media */}
      {(() => {
        const socialFields = {
          company_linkedin: company.company_linkedin,
          twitter: company.twitter,
          facebook: company.facebook,
          instagram: company.instagram,
          youtube: company.youtube
        }
        
        return sectionHasData(socialFields) && 
          renderExpandableSection(
            "Social Media", 
            <Linkedin className="h-5 w-5 text-blue-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("LinkedIn", company.company_linkedin, <Linkedin className="h-5 w-5" />, 'blue', true, 'linkedin')}
              {renderInfoField("Twitter", company.twitter, <Twitter className="h-5 w-5" />, 'cyan', true, 'twitter')}
              {renderInfoField("Facebook", company.facebook, <Facebook className="h-5 w-5" />, 'blue', true, 'facebook')}
              {renderInfoField("Instagram", company.instagram, <Instagram className="h-5 w-5" />, 'pink', true, 'instagram')}
              {renderInfoField("YouTube", company.youtube, <Youtube className="h-5 w-5" />, 'red', true, 'youtube')}
            </div>,
            'social-media',
            false,
            socialFields,
            'blue'
          )
      })()}

      {/* Headquarters */}
      {(() => {
        const headquartersFields = {
          company_address: company.company_address,
          company_city: company.company_city,
          company_state: company.company_state,
          company_zip: company.company_zip,
          company_country: company.company_country
        }
        
        return sectionHasData(headquartersFields) && 
          renderExpandableSection(
            "Headquarters", 
            <Building2 className="h-5 w-5 text-gray-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Address", company.company_address, <MapPin className="h-5 w-5" />, 'gray', false, 'headquarters_address')}
                {renderInfoField("City", company.company_city, <MapPin className="h-5 w-5" />, 'gray', false, 'headquarters_city')}
                {renderInfoField("State", company.company_state, <MapPin className="h-5 w-5" />, 'gray', false, 'headquarters_state')}
                {renderInfoField("Zip Code", company.company_zip, <MapPin className="h-5 w-5" />, 'gray', false, 'headquarters_zipcode')}
                {renderInfoField("Country", company.company_country, <MapPin className="h-5 w-5" />, 'gray', false, 'headquarters_country')}
              </div>
            </div>,
            'headquarters',
            false,
            headquartersFields,
            'gray'
          )
      })()}

            {/* Additional Locations */}
      {(() => {
        const additionalLocationsFields = {
          additional_address: company.additional_address,
          additional_city: company.additional_city,
          additional_state: company.additional_state,
          additional_zipcode: company.additional_zipcode,
          additional_country: company.additional_country,
          office_locations: company.office_locations
        }
        
        return sectionHasData(additionalLocationsFields) && 
          renderExpandableSection(
            "Additional Locations", 
            <MapPin className="h-5 w-5 text-blue-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Additional Address", company.additional_address, <MapPin className="h-5 w-5" />, 'blue', false, 'additional_address')}
                {renderInfoField("Additional City", company.additional_city, <MapPin className="h-5 w-5" />, 'blue', false, 'additional_city')}
                {renderInfoField("Additional State", company.additional_state, <MapPin className="h-5 w-5" />, 'blue', false, 'additional_state')}
                {renderInfoField("Additional Zip Code", company.additional_zipcode, <MapPin className="h-5 w-5" />, 'blue', false, 'additional_zipcode')}
                {renderInfoField("Additional Country", company.additional_country, <MapPin className="h-5 w-5" />, 'blue', false, 'additional_country')}
                {renderInfoField("Office Locations", company.office_locations, <Building className="h-5 w-5" />, 'green', false, 'office_locations')}
              </div>
            </div>,
            'additional-locations',
            false,
            additionalLocationsFields,
            'blue'
          )
      })()}

      {/* Partners & Funding */}
      {(() => {
        const partnersFundingFields = {
          partnerships: company.partnerships,
          funding_sources: company.funding_sources,
          recent_capital_raises: company.recent_capital_raises,
          key_equity_partners: company.key_equity_partners,
          key_debt_partners: company.key_debt_partners
        }
        
        return sectionHasData(partnersFundingFields) && 
          renderExpandableSection(
            "Partners & Funding", 
            <HandHeart className="h-5 w-5 text-green-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Partnerships", company.partnerships, <HandHeart className="h-5 w-5" />, 'pink', false, 'partnerships')}
                {renderInfoField("Key Equity Partners", company.key_equity_partners, <Users className="h-5 w-5" />, 'green', false, 'key_equity_partners')}
                {renderInfoField("Key Debt Partners", company.key_debt_partners, <Banknote className="h-5 w-5" />, 'blue', false, 'key_debt_partners')}
              </div>

              <div className="space-y-4">
                {renderInfoField("Funding Sources", company.funding_sources, <DollarSign className="h-5 w-5" />, 'green', false, 'funding_sources')}
                {renderInfoField("Recent Capital Raises", company.recent_capital_raises, <TrendingUp className="h-5 w-5" />, 'blue', false, 'recent_capital_raises')}
              </div>
            </div>,
            'partners-funding',
            false,
            partnersFundingFields,
            'pink'
          )
      })()}

      {/* Equity / Fund Profile */}
      {(() => {
        const equityFundFields = {
          investment_vehicle_type: company.investment_vehicle_type,
          active_fund_name_series: company.active_fund_name_series,
          fund_size_active_fund: company.fund_size_active_fund,
          fundraising_status: company.fundraising_status
        }
        
        return sectionHasData(equityFundFields) && 
          renderExpandableSection(
            "Equity / Fund Profile", 
            <Briefcase className="h-5 w-5 text-purple-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Investment Vehicle Type", company.investment_vehicle_type, <Briefcase className="h-5 w-5" />, 'blue', false, 'investment_vehicle_type')}
              {renderInfoField("Active Fund Name/Series", company.active_fund_name_series, <FileText className="h-5 w-5" />, 'blue', false, 'active_fund_name_series')}
              {renderInfoField("Active Fund Size", formatCurrency(company.fund_size_active_fund), <DollarSign className="h-5 w-5" />, 'green', false, 'fund_size_active_fund')}
              {renderInfoField("Fundraising Status", company.fundraising_status, <TrendingUp className="h-5 w-5" />, 'orange', false, 'fundraising_status')}
            </div>,
            'equity-fund-profile',
            false,
            equityFundFields,
            'purple'
          )
      })()}
      
      {/* Debt / Lending Profile */}
      {(() => {
        const debtLendingFields = {
          lender_type: company.lender_type,
          annual_loan_volume: company.annual_loan_volume,
          lending_origin: company.lending_origin,
          typical_debt_to_equity_ratio: company.typical_debt_to_equity_ratio
        }
        
        return sectionHasData(debtLendingFields) && 
          renderExpandableSection(
            "Debt / Lending Profile", 
            <Banknote className="h-5 w-5 text-blue-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Lender Type", company.lender_type, <Banknote className="h-5 w-5" />, 'blue', false, 'lender_type')}
              {renderInfoField("Annual Loan Volume", formatCurrency(company.annual_loan_volume), <Calculator className="h-5 w-5" />, 'green', false, 'annual_loan_volume')}
              {renderInfoField("Lending Origin", company.lending_origin, <MapPin className="h-5 w-5" />, 'gray', false, 'lending_origin')}
              {renderInfoField("Debt to Equity Ratio", formatDecimal(company.typical_debt_to_equity_ratio), <Scale className="h-5 w-5" />, 'orange', false, 'typical_debt_to_equity_ratio')}
            </div>,
            'debt-lending-profile',
            false,
            debtLendingFields,
            'blue'
          )
      })()}

      {/* People & History */}
      {(() => {
        const peopleHistoryFields = {
          board_of_directors: company.board_of_directors,
          key_executives: company.key_executives,
          founder_background: company.founder_background,
          company_history: company.company_history
        }
        
        return sectionHasData(peopleHistoryFields) && 
          renderExpandableSection(
            "People & History", 
            <Users className="h-5 w-5 text-blue-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Board of Directors", company.board_of_directors, <Crown className="h-5 w-5" />, 'purple', false, 'board_of_directors')}
                {renderInfoField("Key Executives", company.key_executives, <Users className="h-5 w-5" />, 'orange', false, 'key_executives')}
              </div>

              <div className="space-y-4">
                {renderInfoField("Founder Background", company.founder_background, <Crown className="h-5 w-5" />, 'purple', false, 'founder_background')}
                {renderInfoField("Company History", company.company_history, <Calendar className="h-5 w-5" />, 'blue', false, 'company_history')}
              </div>
            </div>,
            'people-history',
            false,
            peopleHistoryFields,
            'blue'
          )
      })()}

      {/* Expertise */}
      {(() => {
        const expertiseFields = {
          development_fee_structure: company.development_fee_structure,
          technology_proptech_adoption: company.technology_proptech_adoption,
          adaptive_reuse_experience: company.adaptive_reuse_experience,
          regulatory_zoning_expertise: company.regulatory_zoning_expertise
        }
        
        return sectionHasData(expertiseFields) && 
          renderExpandableSection(
            "Expertise", 
            <Star className="h-5 w-5 text-yellow-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Development Fee Structure", company.development_fee_structure, <Calculator className="h-5 w-5" />, 'blue', false, 'development_fee_structure')}
              {renderInfoField("PropTech Adoption", company.technology_proptech_adoption ? "Yes" : "No", <Cpu className="h-5 w-5" />, 'blue', false, 'technology_proptech_adoption')}
              {renderInfoField("Adaptive Reuse Experience", company.adaptive_reuse_experience ? "Yes" : "No", <Recycle className="h-5 w-5" />, 'emerald', false, 'adaptive_reuse_experience')}
              {renderInfoField("Regulatory/Zoning Expertise", company.regulatory_zoning_expertise ? "Yes" : "No", <Gavel className="h-5 w-5" />, 'purple', false, 'regulatory_zoning_expertise')}
            </div>,
            'expertise',
            false,
            expertiseFields,
            'yellow'
          )
      })()}



      {/* Operational Profile */}
      {(() => {
        const operationalProfileFields = {
          products_services_description: company.products_services_description,
          target_customer_profile: company.target_customer_profile,
          major_competitors: company.major_competitors,
          unique_selling_proposition: company.unique_selling_proposition,
          industry_awards_recognitions: company.industry_awards_recognitions,
          market_share_percentage: company.market_share_percentage
        }
        
        return sectionHasData(operationalProfileFields) && 
          renderExpandableSection(
            "Operational Profile", 
            <Briefcase className="h-5 w-5 text-orange-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Major Competitors", company.major_competitors, <Activity className="h-5 w-5" />, 'red', false, 'major_competitors')}
                {renderInfoField("Industry Awards", company.industry_awards_recognitions, <Award className="h-5 w-5" />, 'yellow', false, 'industry_awards_recognitions')}
                {renderInfoField("Market Share", formatDecimal(company.market_share_percentage), <PieChart className="h-5 w-5" />, 'blue', false, 'market_share_percentage')}
              </div>

              <div className="space-y-4">
                {renderInfoField("Products & Services Description", company.products_services_description, <Briefcase className="h-5 w-5" />, 'orange', false, 'products_services_description')}
                {renderInfoField("Target Customer Profile", company.target_customer_profile, <Users className="h-5 w-5" />, 'blue', false, 'target_customer_profile')}
                {renderInfoField("Unique Selling Proposition", company.unique_selling_proposition, <Star className="h-5 w-5" />, 'green', false, 'unique_selling_proposition')}
              </div>
            </div>,
            'operational-profile',
            false,
            operationalProfileFields,
            'orange'
          )
      })()}

      {/* Portfolio & Activity */}
      {(() => {
        const portfolioActivityFields = {
          number_of_properties: company.number_of_properties,
          portfolio_health: company.portfolio_health,
          transactions_completed_last_12m: company.transactions_completed_last_12m,
          total_transaction_volume_ytd: company.total_transaction_volume_ytd,
          deal_count_ytd: company.deal_count_ytd,
          average_deal_size: company.average_deal_size,
          portfolio_size_sqft: company.portfolio_size_sqft,
          portfolio_asset_count: company.portfolio_asset_count
        }
        
        return sectionHasData(portfolioActivityFields) && 
          renderExpandableSection(
            "Portfolio & Activity", 
            <Activity className="h-5 w-5 text-green-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Number of Properties", formatNumber(company.number_of_properties), <Building className="h-5 w-5" />, 'orange', false, 'number_of_properties')}
              {renderInfoField("Portfolio Health", company.portfolio_health, <Activity className="h-5 w-5" />, 'green', false, 'portfolio_health')}
              {renderInfoField("Transactions (Last 12M)", formatNumber(company.transactions_completed_last_12m), <TrendingUp className="h-5 w-5" />, 'green', false, 'transactions_completed_last_12m')}
              {renderInfoField("Transaction Volume (YTD)", formatCurrency(company.total_transaction_volume_ytd), <DollarSign className="h-5 w-5" />, 'green', false, 'total_transaction_volume_ytd')}
              {renderInfoField("Deal Count (YTD)", formatNumber(company.deal_count_ytd), <BarChart3 className="h-5 w-5" />, 'blue', false, 'deal_count_ytd')}
              {renderInfoField("Average Deal Size", formatCurrency(company.average_deal_size), <Calculator className="h-5 w-5" />, 'orange', false, 'average_deal_size')}
              {renderInfoField("Portfolio Size (SqFt)", formatNumber(company.portfolio_size_sqft), <Building className="h-5 w-5" />, 'purple', false, 'portfolio_size_sqft')}
              {renderInfoField("Portfolio Asset Count", formatNumber(company.portfolio_asset_count), <Home className="h-5 w-5" />, 'indigo', false, 'portfolio_asset_count')}
            </div>,
            'portfolio-activity',
            false,
            portfolioActivityFields,
            'green'
          )
      })()}

      {/* Internal Relationship */}
      {(() => {
        const internalRelationshipFields = {
          internal_relationship_manager: company.internal_relationship_manager,
          last_contact_date: company.last_contact_date,
          pipeline_status: company.pipeline_status,
          role_in_previous_deal: company.role_in_previous_deal
        }
        
        return sectionHasData(internalRelationshipFields) && 
          renderExpandableSection(
            "Internal Relationship", 
            <Network className="h-5 w-5 text-blue-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Relationship Manager", company.internal_relationship_manager, <Users className="h-5 w-5" />, 'blue', false, 'internal_relationship_manager')}
              {renderInfoField("Last Contact Date", company.last_contact_date, <Calendar className="h-5 w-5" />, 'gray', false, 'last_contact_date')}
              {renderInfoField("Pipeline Status", company.pipeline_status, <Activity className="h-5 w-5" />, 'orange', false, 'pipeline_status')}
              {renderInfoField("Previous Deal Role", company.role_in_previous_deal, <Briefcase className="h-5 w-5" />, 'gray', false, 'role_in_previous_deal')}
            </div>,
            'internal-relationship',
            false,
            internalRelationshipFields,
            'blue'
          )
      })()}
      
      {/* Data Provenance */}
      {(() => {
        const dataProvenanceFields = {
          recent_news_sentiment: company.recent_news_sentiment,
          data_source: company.data_source,
          last_updated_timestamp: company.last_updated_timestamp,
          data_confidence_score: company.data_confidence_score
        }
        
        return sectionHasData(dataProvenanceFields) && 
          renderExpandableSection(
            "Data Provenance", 
            <Info className="h-5 w-5 text-gray-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Recent News Sentiment", company.recent_news_sentiment, <Eye className="h-5 w-5" />, 'purple')}
              {renderInfoField("Data Source", company.data_source, <Database className="h-5 w-5" />, 'gray')}
              {renderInfoField("Last Updated", company.last_updated_timestamp, <Calendar className="h-5 w-5" />, 'blue')}
              {renderInfoField("Data Confidence Score", company.data_confidence_score, <CheckCircle className="h-5 w-5" />, 'green')}
            </div>,
            'data-provenance',
            false,
            dataProvenanceFields,
            'purple'
          )
      })()}

      {/* Website Pages & Content */}
      {(() => {
        const webPagesFields = {
          web_pages: webPages?.length > 0,
          top_pages: topPages?.length > 0,
          ranking_summary: !!rankingSummary
        }
        
        return (webPages && webPages.length > 0) &&
          renderExpandableSection(
            "Website Pages & Content",
            <GlobeIcon className="h-5 w-5 text-blue-600" />,
            <CompanyWebPages
              companyId={company.company_id}
              webPages={webPages}
              topPages={topPages}
              rankingSummary={rankingSummary}
              loading={webPagesLoading}
              error={webPagesError}
            />,
            'website-pages-content',
            false, // This makes it default to collapsed (off)
            webPagesFields,
            'blue'
          )
      })()}

      {/* Centralized Sources Section */}
      {renderAllSources()}

    </div>
  )
}
