'use client'

import { useState, useEffect } from 'react'
import { CompanyDetail } from '../shared/types'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from "sonner"
import { Button } from '@/components/ui/button'
import { ArrowLeft, UserPlus, MessageSquare, Pencil, Globe, MapPin, Calendar, Target, RefreshCw, FileText, Database, ExternalLink, AlertCircle, DollarSign } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'

// Import our components
import CompanyEditForm from './CompanyEditForm'
import CompanyContacts from './CompanyContacts'
import CompanyOverviewV2 from './CompanyOverviewV2'
import InvestmentCriteriaSliderV2 from '../../investment-criteria/InvestmentCriteriaSliderV2'
import CompanyLinkedDealsTab from './CompanyLinkedDealsTab'
import CompanyDealMatchingTab from '../CompanyDealMatchingTab'
import ArticleTab from '../../shared/ArticleTab'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'


interface CompanyDetailMainProps {
  companyId: string
}

export default function CompanyDetailMain({ companyId }: CompanyDetailMainProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [company, setCompany] = useState<CompanyDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Get current tab from URL parameters
  const currentTab = searchParams?.get('tab') || 'overview'
  
  // Create a V2 company object with all the new fields populated
  const createCompanyV2 = (originalCompany: CompanyDetail | null) => {
    if (!originalCompany) return null;
    
    return {
      ...originalCompany,
      // Core Company Information
      company_type: originalCompany.company_type || 'Unknown',
      business_model: originalCompany.business_model || 'Not specified',
      
      // Investment & Strategy
      capital_position: originalCompany.capital_position || [],
      investment_focus: originalCompany.investment_focus || [],
      investment_strategy_mission: originalCompany.investment_strategy_mission || 'Not specified',
      investment_strategy_approach: originalCompany.investment_strategy_approach || 'Not specified',
      
      // Contact Information
      secondary_phone: originalCompany.secondary_phone || undefined,
      main_email: originalCompany.main_email || undefined,
      secondary_email: originalCompany.secondary_email || undefined,
      twitter: originalCompany.twitter || undefined,
      facebook: originalCompany.facebook || undefined,
      instagram: originalCompany.instagram || undefined,
      youtube: originalCompany.youtube || undefined,
      
      // Address Information
      additional_address: originalCompany.additional_address || undefined,
      additional_city: originalCompany.additional_city || undefined,
      additional_state: originalCompany.additional_state || undefined,
      additional_zipcode: originalCompany.additional_zipcode || undefined,
      additional_country: originalCompany.additional_country || undefined,
      office_locations: originalCompany.office_locations || [],
      
      // Company Metrics
      fund_size: originalCompany.fund_size || undefined,
      aum: originalCompany.aum || undefined,
      number_of_properties: originalCompany.number_of_properties || undefined,
      number_of_offices: originalCompany.number_of_offices || undefined,
      number_of_employees: originalCompany.number_of_employees || undefined,
      annual_revenue: originalCompany.annual_revenue || undefined,
      net_income: originalCompany.net_income || undefined,
      ebitda: originalCompany.ebitda || undefined,
      profit_margin: originalCompany.profit_margin || undefined,
      market_capitalization: originalCompany.market_capitalization || undefined,
      market_share_percentage: originalCompany.market_share_percentage || undefined,
      
      // Financial Information
      balance_sheet_strength: originalCompany.balance_sheet_strength || undefined,
      funding_sources: originalCompany.funding_sources || [],
      recent_capital_raises: originalCompany.recent_capital_raises || undefined,
      typical_debt_to_equity_ratio: originalCompany.typical_debt_to_equity_ratio || undefined,
      development_fee_structure: originalCompany.development_fee_structure || undefined,
      credit_rating: originalCompany.credit_rating || undefined,
      dry_powder: originalCompany.dry_powder || undefined,
      annual_deployment_target: originalCompany.annual_deployment_target || undefined,
      
      // Investment & Fund Information
      investment_vehicle_type: originalCompany.investment_vehicle_type || undefined,
      active_fund_name_series: originalCompany.active_fund_name_series || undefined,
      fund_size_active_fund: originalCompany.fund_size_active_fund || undefined,
      fundraising_status: originalCompany.fundraising_status || undefined,
      lender_type: originalCompany.lender_type || undefined,
      annual_loan_volume: originalCompany.annual_loan_volume || undefined,
      lending_origin: originalCompany.lending_origin || undefined,
      portfolio_health: originalCompany.portfolio_health || undefined,
      
      // Partnership & Leadership
      partnerships: originalCompany.partnerships || [],
      key_equity_partners: originalCompany.key_equity_partners || [],
      key_debt_partners: originalCompany.key_debt_partners || [],
      board_of_directors: originalCompany.board_of_directors || [],
      key_executives: originalCompany.key_executives || [],
      founder_background: originalCompany.founder_background || undefined,
      
      // Market Positioning & Strategy
      market_cycle_positioning: originalCompany.market_cycle_positioning || undefined,
      urban_vs_suburban_preference: originalCompany.urban_vs_suburban_preference || undefined,
      sustainability_esg_focus: originalCompany.sustainability_esg_focus || false,
      technology_proptech_adoption: originalCompany.technology_proptech_adoption || false,
      adaptive_reuse_experience: originalCompany.adaptive_reuse_experience || false,
      regulatory_zoning_expertise: originalCompany.regulatory_zoning_expertise || false,
      
      // Corporate Structure
      corporate_structure: originalCompany.corporate_structure || undefined,
      parent_company: originalCompany.parent_company || undefined,
      subsidiaries: originalCompany.subsidiaries || [],
      stock_ticker_symbol: originalCompany.stock_ticker_symbol || undefined,
      stock_exchange: originalCompany.stock_exchange || undefined,
      
      // Business Information
      products_services_description: originalCompany.products_services_description || undefined,
      target_customer_profile: originalCompany.target_customer_profile || undefined,
      major_competitors: originalCompany.major_competitors || [],
      unique_selling_proposition: originalCompany.unique_selling_proposition || undefined,
      industry_awards_recognitions: originalCompany.industry_awards_recognitions || [],
      company_history: originalCompany.company_history || undefined,
      
      // Transaction & Portfolio Data
      transactions_completed_last_12m: originalCompany.transactions_completed_last_12m || undefined,
      total_transaction_volume_ytd: originalCompany.total_transaction_volume_ytd || undefined,
      deal_count_ytd: originalCompany.deal_count_ytd || undefined,
      average_deal_size: originalCompany.average_deal_size || undefined,
      portfolio_size_sqft: originalCompany.portfolio_size_sqft || undefined,
      portfolio_asset_count: originalCompany.portfolio_asset_count || undefined,
      role_in_previous_deal: originalCompany.role_in_previous_deal || undefined,
      
      // Relationship & Pipeline Data
      internal_relationship_manager: originalCompany.internal_relationship_manager || undefined,
      last_contact_date: originalCompany.last_contact_date || undefined,
      pipeline_status: originalCompany.pipeline_status || undefined,
      recent_news_sentiment: originalCompany.recent_news_sentiment || undefined,
      
      // Data Quality & Processing
      data_source: originalCompany.data_source || 'Manual Entry',
      last_updated_timestamp: originalCompany.last_updated_timestamp || new Date().toISOString(),
      data_confidence_score: originalCompany.data_confidence_score || 0,
      llm_response: originalCompany.llm_response || undefined,
      llm_token_usage: originalCompany.llm_token_usage || undefined,
      llm_used: originalCompany.llm_used || undefined,
      quarterly_earnings_link: originalCompany.quarterly_earnings_link || undefined,
    };
  };
  
  const companyV2 = createCompanyV2(company);

  // Add state for company processing
  type CompanyProcessingStage = 'website_scraping' | 'company_overview_v2' | 'company_investment_criteria';
  
  const [processingJobs, setProcessingJobs] = useState([
    { stage: 'website_scraping' as CompanyProcessingStage, isExecuting: false },
    { stage: 'company_overview_v2' as CompanyProcessingStage, isExecuting: false },
    { stage: 'company_investment_criteria' as CompanyProcessingStage, isExecuting: false },
  ]);

  // Add state for article matching
  const [matchingNews, setMatchingNews] = useState<any[]>([]);
  const [matchingNewsLoading, setMatchingNewsLoading] = useState(false);
  const [matchingNewsError, setMatchingNewsError] = useState<string | null>(null);
  const [fetchedNewsForCompany, setFetchedNewsForCompany] = useState<string | null>(null);
  const [expandedNewsId, setExpandedNewsId] = useState<string | number | null>(null);
  const [matchingCriteria, setMatchingCriteria] = useState<any>(null);

  const fetchCompanyDetails = async () => {
    try {
      const response = await fetch(`/api/companies/${companyId}`)
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Fetch error response:', errorText);
        throw new Error(`Failed to fetch company details: ${response.status} ${response.statusText}`)
      }
      
      const data = await response.json()
      setCompany(data)
    } catch (error) {
      console.error('Error fetching company details:', error)
      setError('Failed to load company details. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCompanyDetails()
  }, [companyId])

  // Add useEffect to fetch articles when Article Matching tab is active
  useEffect(() => {
    if (currentTab === 'articles' && companyId && fetchedNewsForCompany !== companyId) {
      setMatchingNewsLoading(true);
      setMatchingNewsError(null);
      fetch(`/api/matching-v2/articles-for-company/${companyId}`)
        .then(res => res.json())
        .then(data => {
          if (data.matches && data.matches.length > 0) {
            setMatchingNews(data.matches);
          } else {
            setMatchingNews([]);
          }
          // Store matching criteria for display
          if (data.matching_criteria) {
            setMatchingCriteria({
              ...data.matching_criteria,
              criteria_source: data.criteria_source,
              criteria_description: data.criteria_description
            });
          }
          setFetchedNewsForCompany(companyId);
        })
        .catch(err => {
          setMatchingNewsError('Error loading matching articles.');
        })
        .finally(() => {
          setMatchingNewsLoading(false);
        });
    }
  }, [currentTab, companyId, fetchedNewsForCompany])

  // Handle tab navigation
  const handleTabChange = (newTab: string) => {
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.set('tab', newTab);
    router.push(`/dashboard/companies/${companyId}?${params.toString()}`);
  }

  const handleEditCompany = () => {
    handleTabChange('edit')
  }

  const cancelEditing = () => {
    handleTabChange('overview')
  }

  // Company processing function
  const executeCompanyProcessing = async (stage: CompanyProcessingStage) => {
    if (!companyId) return;

    const companyIdNumber = typeof companyId === 'string' ? parseInt(companyId) : companyId;
    
    // Update processing state
    setProcessingJobs(prev => 
      prev.map(job => 
        job.stage === stage 
          ? { ...job, isExecuting: true }
          : job
      )
    );

    try {
      const response = await fetch('/api/processing/queue/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          processorType: stage,
          options: {
            limit: 1,
            singleId: companyIdNumber,
            batchSize: 1,
            entityType: 'company',
            filters: {}
          }
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success(`Successfully triggered ${getCompanyStageConfig(stage).title} for this company`);
      } else {
        toast.error(`Failed to trigger ${getCompanyStageConfig(stage).title}: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Failed to execute company processing:', error);
      toast.error(`Failed to trigger ${getCompanyStageConfig(stage).title}: ${(error as Error).message}`);
    } finally {
      setProcessingJobs(prev => 
        prev.map(job => 
          job.stage === stage 
            ? { ...job, isExecuting: false }
            : job
        )
      );
    }
  };

  const getCompanyStageConfig = (stage: CompanyProcessingStage) => {
    switch (stage) {
      case 'website_scraping':
        return { title: 'Web Crawler', description: 'Crawl company website for detailed information' };
      case 'company_overview_v2':
        return { title: 'Company Overview', description: 'Generate enhanced company overview with comprehensive AI extraction' };
      case 'company_investment_criteria':
        return { title: 'Investment Criteria', description: 'Extract company investment criteria and preferences' };
      default:
        return { title: 'Unknown', description: 'Unknown processing stage' };
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading company details...</p>
        </div>
      </div>
    )
  }

  if (error || !company) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="bg-red-100 text-red-700 p-4 rounded-lg">
            {error || 'Company not found'}
          </div>
          <Button 
            variant="outline" 
            onClick={() => router.back()}
            className="mt-4"
          >
            Back
          </Button>
        </div>
      </div>
    )
  }

  // Extract company data with fallbacks
  const companyName = company.company_name || "Company Name";
  const industry = company.industry || "Not specified";
  const location = company.company_city && company.company_state ? 
    `${company.company_city}, ${company.company_state}` : 
    company.company_city || company.company_state || "Location not specified";
  const website = company.company_website || "#";
  const websiteDomain = website !== "#" ? website.replace(/^https?:\/\//, '').replace(/\/$/, '') : "website";
  const foundedYear = company.founded_year || null;
  const contactCount = company.contacts?.length || 0;
  const initials = companyName.substring(0, 2).toUpperCase();

  return (
    <div className="min-h-screen bg-white">
      {/* Top Navigation Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="w-full px-6 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="text-gray-600 hover:text-gray-900 flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </div>
      </div>

      {/* Company Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="w-full px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Avatar className="h-16 w-16">
                <AvatarFallback className="bg-blue-600 text-white">{initials}</AvatarFallback>
              </Avatar>
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-gray-900">{companyName}</h1>
                <div className="flex flex-wrap items-center gap-3 mt-1 text-sm text-gray-500">
                  {location && (
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                      <span>{location}</span>
                    </div>
                  )}
                  {foundedYear && (
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                      <span>Founded {foundedYear}</span>
                    </div>
                  )}
                  {/* Website - show link if valid, otherwise show "not specified" text */}
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 mr-1 text-gray-400" />
                    {website && website !== "#" ? (
                      <a 
                        href={website.startsWith('http') ? website : `https://${website}`} 
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className="text-blue-600 hover:underline"
                        onClick={(e) => {
                          // Ensure the link opens in a new tab and doesn't navigate within the app
                          e.preventDefault();
                          window.open(website.startsWith('http') ? website : `https://${website}`, '_blank', 'noopener,noreferrer');
                        }}
                      >
                        {websiteDomain}
                      </a>
                    ) : (
                      <span className="text-gray-500">Website not specified</span>
                    )}
                  </div>
                  {contactCount > 0 && (
                    <Badge variant="outline" className="ml-2">
                      {contactCount} Contacts
                    </Badge>
                  )}
                  
                  {/* Status Badges */}
                  <div className="flex gap-1.5 ml-2">
                    {/* Company Overview V2 Status */}
                    {(company as any).overview_v2_status && (
                      <Badge 
                        variant="outline" 
                        className={`text-xs px-2 py-0.5 rounded-full ${
                          (company as any).overview_v2_status === 'completed' 
                            ? 'bg-green-50 text-green-700 border-green-200' 
                            : (company as any).overview_v2_status === 'pending' 
                            ? 'bg-amber-50 text-amber-700 border-amber-200' 
                            : (company as any).overview_v2_status === 'failed' 
                            ? 'bg-red-50 text-red-700 border-red-200' 
                            : (company as any).overview_v2_status === 'running'
                            ? 'bg-blue-50 text-blue-700 border-blue-200'
                            : 'bg-gray-50 text-gray-700 border-gray-200'
                        }`}
                      >
                        Overview: {(company as any).overview_v2_status}
                      </Badge>
                    )}
                    
                    {/* Website Scraping Status */}
                    {company.website_scraping_status && (
                      <Badge 
                        variant="outline" 
                        className={`text-xs px-2 py-0.5 rounded-full ${
                          company.website_scraping_status === 'completed' 
                            ? 'bg-green-50 text-green-700 border-green-200' 
                            : company.website_scraping_status === 'pending' 
                            ? 'bg-amber-50 text-amber-700 border-amber-200' 
                            : company.website_scraping_status === 'failed' 
                            ? 'bg-red-50 text-red-700 border-red-200' 
                            : company.website_scraping_status === 'running'
                            ? 'bg-blue-50 text-blue-700 border-blue-200'
                            : 'bg-gray-50 text-gray-700 border-gray-200'
                        }`}
                      >
                        Scraping: {company.website_scraping_status}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Company Processing Buttons */}
            <div className="flex items-center gap-2">
              {processingJobs.map((job) => {
                const config = getCompanyStageConfig(job.stage);
                return (
                  <Button
                    key={job.stage}
                    onClick={() => executeCompanyProcessing(job.stage)}
                    disabled={job.isExecuting}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    {job.isExecuting ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : job.stage === 'website_scraping' ? (
                      <Database className="h-4 w-4" />
                    ) : job.stage === 'company_investment_criteria' ? (
                      <Target className="h-4 w-4" />
                    ) : (
                      <FileText className="h-4 w-4" />
                    )}
                    {job.isExecuting ? 'Processing...' : config.title}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content with URL-based Navigation */}
      <div className="w-full px-6">
        <div className="mt-6">
          {/* Navigation Tabs */}
          <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-200">
            <button
              onClick={() => handleTabChange('overview')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                currentTab === 'overview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => handleTabChange('contacts')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                currentTab === 'contacts'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Contacts
            </button>
            <button
              onClick={() => handleTabChange('investment-criteria')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors flex items-center ${
                currentTab === 'investment-criteria'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Target className="h-4 w-4 mr-2" />
              Investment Criteria
            </button>
            <button
              onClick={() => handleTabChange('linked-deals')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                currentTab === 'linked-deals'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Linked Deals
            </button>
            <button
              onClick={() => handleTabChange('deal-matching')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                currentTab === 'deal-matching'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Deal Matching
            </button>
            <button
              onClick={() => handleTabChange('articles')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors flex items-center ${
                currentTab === 'articles'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <FileText className="h-4 w-4 mr-2" />
              Articles
            </button>
            <button
              onClick={() => handleTabChange('edit')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                currentTab === 'edit'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Edit Company
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
            {/* Main Content Area */}
            <div className={`${currentTab === 'edit' ? 'lg:col-span-5' : 'lg:col-span-5'}`}>
              {/* Overview Tab */}
              {currentTab === 'overview' && (
                <div className="space-y-6">
                  {companyV2 ? (
                    <CompanyOverviewV2 company={companyV2} />
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-gray-500">Loading company data...</div>
                    </div>
                  )}
                </div>
              )}

              {/* Contacts Tab */}
              {currentTab === 'contacts' && (
                <CompanyContacts company={company} />
              )}

              {/* Investment Criteria Tab */}
              {currentTab === 'investment-criteria' && (
                <div className="p-6">
                  <InvestmentCriteriaSliderV2
                    entityType="company"
                    entityId={companyId}
                    entityName={company?.company_name}
                  />
                </div>
              )}

              {/* Linked Deals Tab */}
              {currentTab === 'linked-deals' && (
                <CompanyLinkedDealsTab companyId={companyId} />
              )}

             {currentTab === 'deal-matching'?
                <CompanyDealMatchingTab companyId={companyId} company={company} />
             :null}

              {/* Articles Tab */}
              {currentTab === 'articles' && (
                <ArticleTab
                  entityType="company"
                  entityId={companyId}
                  entityName={company?.company_name}
                  linkedArticlesApiEndpoint={`/api/companies/${companyId}/linked-articles`}
                  matchedArticlesApiEndpoint={`/api/matching-v2/articles-for-company/${companyId}`}
                />
              )}

              {/* Edit Tab */}
              {currentTab === 'edit' && (
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-6">Edit Company Information</h2>
                  <CompanyEditForm
                    key={`company-edit-${company.company_id}`}
                    company={company}
                    onChange={(updatedCompany) => {
                      // Just update the local state, don't save to backend yet
                      setCompany(updatedCompany as CompanyDetail);
                    }}
                  />
                  
                  {/* Save/Cancel Buttons */}
                  <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
                    <Button
                      variant="outline"
                      onClick={cancelEditing}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={async () => {
                        try {
                          // Save to backend
                          const response = await fetch(`/api/companies/${company.company_id}`, {
                            method: 'PUT',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(company),
                          });
                          
                          if (response.ok) {
                            handleTabChange('overview');
                            toast.success("Company updated successfully");
                            
                            // Refresh company data with updated information
                            await fetchCompanyDetails();
                          } else {
                            const errorData = await response.text();
                            console.error('API Error:', errorData);
                            toast.error(`Failed to update company: ${response.status} ${response.statusText}`);
                          }
                        } catch (error) {
                          console.error('Error updating company:', error);
                          toast.error("Failed to update company");
                        }
                      }}
                    >
                      Save Changes
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}