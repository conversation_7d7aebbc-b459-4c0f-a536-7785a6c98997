'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Globe, 
  ExternalLink, 
  Star, 
  TrendingUp, 
  Building, 
  FileText,
  Target,
  MapPin,
  Award,
  ChevronDown,
  ChevronUp,
  Eye,
  Clock,
  Layers,
  BarChart3,
  Sparkles
} from 'lucide-react'

interface WebPageData {
  id: string
  url: string
  parent_url: string | null
  crawl_depth: number
  relevance_rank: number
  rank_factors: {
    url_structure_score: number
    content_quality_score: number
    page_type_score: number
    investment_criteria_score: number
    financial_numbers_score: number
    business_keywords_score: number
    geographic_relevance_score: number
    url_quality_bonus: number
    total_score: number
  }
  extracted_text: string
  last_scraped_at: string
  created_at: string
  page_metadata: string | null

}

interface CompanyWebPagesProps {
  companyId: number
  webPages: WebPageData[]
  topPages: WebPageData[]
  rankingSummary: {
    highest_score: number
    average_score: number
    total_pages: number
  } | null
  loading: boolean
  error: string | null
}

export default function CompanyWebPages({ 
  companyId, 
  webPages, 
  topPages, 
  rankingSummary, 
  loading, 
  error 
}: CompanyWebPagesProps) {
  const [expandedPages, setExpandedPages] = useState<Record<string, boolean>>({})
  const [showAllPages, setShowAllPages] = useState(false)

  const togglePageExpansion = (pageId: string) => {
    setExpandedPages(prev => ({
      ...prev,
      [pageId]: !prev[pageId]
    }))
  }

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 80) return 'text-green-600 bg-green-50 border-green-200'
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    if (percentage >= 40) return 'text-orange-600 bg-orange-50 border-orange-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getRankingReason = (factors: WebPageData['rank_factors']) => {
    const reasons: string[] = []
    
    // New priority order: Investment Criteria > Financial Numbers > Business Keywords
    if (factors.investment_criteria_score > 40) {
      reasons.push('High investment criteria relevance')
    }
    if (factors.financial_numbers_score > 20) {
      reasons.push('Rich financial content')
    }
    if (factors.business_keywords_score > 15) {
      reasons.push('Strong business keyword match')
    }
    if (factors.url_structure_score > 20) {
      reasons.push('Strong URL structure')
    }
    if (factors.content_quality_score > 15) {
      reasons.push('High content quality')
    }
    if (factors.page_type_score > 10) {
      reasons.push('Important page type')
    }
    if (factors.geographic_relevance_score > 5) {
      reasons.push('Geographic relevance')
    }
    
    return reasons.length > 0 ? reasons.join(', ') : 'Standard page ranking'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }


  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }


  if (loading) {
    return (
      <Card className="border border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5 text-blue-600" />
            Website Pages & Content
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="text-gray-500">Loading website pages...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="border border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5 text-blue-600" />
            Website Pages & Content
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-red-600 p-4 bg-red-50 rounded-lg border border-red-200">
            Error loading website pages: {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!webPages || webPages.length === 0) {
    return (
      <Card className="border border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5 text-blue-600" />
            Website Pages & Content
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-gray-500 p-4 bg-gray-50 rounded-lg border border-gray-200">
            No website pages have been crawled for this company yet.
          </div>
        </CardContent>
      </Card>
    )
  }

  const displayPages = showAllPages ? webPages : topPages

  return (
    <Card className="border border-gray-200 shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5 text-blue-600" />
          Website Pages & Content
          <Badge className="bg-blue-100 text-blue-700 border border-blue-200">
            {webPages.length} pages
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Ranking Summary */}
        {rankingSummary && (
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Ranking Summary
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white p-3 rounded border border-blue-100">
                <div className="text-sm text-blue-600 mb-1">Highest Score</div>
                <div className="text-2xl font-bold text-blue-900">{rankingSummary.highest_score}</div>
              </div>
              <div className="bg-white p-3 rounded border border-blue-100">
                <div className="text-sm text-blue-600 mb-1">Average Score</div>
                <div className="text-2xl font-bold text-blue-900">{rankingSummary.average_score}</div>
              </div>
              <div className="bg-white p-3 rounded border border-blue-100">
                <div className="text-sm text-blue-600 mb-1">Total Pages</div>
                <div className="text-2xl font-bold text-blue-900">{rankingSummary.total_pages}</div>
              </div>
            </div>
          </div>
        )}

        {/* Top Pages */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-600" />
              {showAllPages ? 'All Pages' : 'Top Pages'}
            </h3>
            {webPages.length > 5 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAllPages(!showAllPages)}
                className="text-blue-600 hover:text-blue-800"
              >
                {showAllPages ? 'Show Top 5' : `Show All ${webPages.length}`}
              </Button>
            )}
          </div>

          {displayPages.map((page, index) => {
            const isExpanded = expandedPages[page.id]
            const factors = page.rank_factors
            
            return (
              <div key={page.id} className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge className="bg-blue-100 text-blue-700 border border-blue-200">
                        Rank #{page.relevance_rank}
                      </Badge>
                      <Badge className={`${getScoreColor(factors.total_score, 200)} border`}>
                        Score: {factors.total_score}/200
                      </Badge>
                      <Badge className="bg-gray-100 text-gray-700 border border-gray-200">
                        Depth: {page.crawl_depth}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-2 mb-2">
                      <Globe className="h-4 w-4 text-gray-500 flex-shrink-0" />
                      <a 
                        href={page.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 font-medium truncate flex items-center gap-1"
                      >
                        {page.url}
                        <ExternalLink className="h-3 w-3 flex-shrink-0" />
                      </a>
                    </div>

                    <div className="text-sm text-gray-600 mb-3">
                      {getRankingReason(factors)}
                    </div>

                    {/* Score Breakdown - New Improved Format */}
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 mb-3">
                      <div className="text-xs bg-red-50 p-2 rounded border border-red-200">
                        <div className="font-medium text-red-700">Investment Criteria</div>
                        <div className="text-red-600 font-semibold">{factors.investment_criteria_score}/80</div>
                      </div>
                      <div className="text-xs bg-orange-50 p-2 rounded border border-orange-200">
                        <div className="font-medium text-orange-700">Financial Numbers</div>
                        <div className="text-orange-600 font-semibold">{factors.financial_numbers_score}/40</div>
                      </div>
                      <div className="text-xs bg-blue-50 p-2 rounded border border-blue-200">
                        <div className="font-medium text-blue-700">Business Keywords</div>
                        <div className="text-blue-600">{factors.business_keywords_score}/30</div>
                      </div>
                      <div className="text-xs bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">URL Structure</div>
                        <div className="text-gray-600">{factors.url_structure_score}/25</div>
                      </div>
                      <div className="text-xs bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Content Quality</div>
                        <div className="text-gray-600">{factors.content_quality_score}/20</div>
                      </div>
                      <div className="text-xs bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Page Type</div>
                        <div className="text-gray-600">{factors.page_type_score}/15</div>
                      </div>
                      <div className="text-xs bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">Geographic</div>
                        <div className="text-gray-600">{factors.geographic_relevance_score}/15</div>
                      </div>
                      <div className="text-xs bg-gray-50 p-2 rounded">
                        <div className="font-medium text-gray-700">URL Quality</div>
                        <div className="text-gray-600">{factors.url_quality_bonus}/10</div>
                      </div>
                    </div>

                    {/* Page Metadata */}
                    {page.page_metadata && (
                      <div className="text-sm text-gray-700 bg-blue-50 p-3 rounded border border-blue-200 mb-3">
                        <div className="flex items-center gap-2 mb-3">
                          <Sparkles className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-blue-800">AI Classification Metadata</span>
                          <Badge className="bg-blue-100 text-blue-700 border border-blue-200">
                            {(page as any).classifier_version || 'v1.0'}
                          </Badge>
                        </div>
                        <pre className="text-xs text-gray-600 bg-white p-3 rounded border border-blue-100 overflow-x-auto whitespace-pre-wrap">
                          {page.page_metadata}
                        </pre>
                      </div>
                    )}

                    {/* Content Preview */}
                    <div className="text-sm text-gray-700 bg-gray-50 p-3 rounded border">
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">Content Preview</span>
                        <Badge className="bg-gray-100 text-gray-600 border border-gray-200">
                          {page.extracted_text.length} chars
                        </Badge>
                      </div>
                      <p className="text-gray-600 leading-relaxed">
                        {isExpanded ? page.extracted_text : truncateText(page.extracted_text)}
                      </p>
                      {page.extracted_text.length > 200 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => togglePageExpansion(page.id)}
                          className="mt-2 text-blue-600 hover:text-blue-800 p-0 h-auto"
                        >
                          {isExpanded ? (
                            <>
                              <ChevronUp className="h-3 w-3 mr-1" />
                              Show Less
                            </>
                          ) : (
                            <>
                              <ChevronDown className="h-3 w-3 mr-1" />
                              Show More
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    {/* Metadata */}
                    <div className="flex items-center gap-4 mt-3 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Scraped: {formatDate(page.last_scraped_at)}
                      </div>
                      {page.parent_url && (
                        <div className="flex items-center gap-1">
                          <Layers className="h-3 w-3" />
                          Parent: <a href={page.parent_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">{page.parent_url}</a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
