// Company list types
export interface Company {
  company_id: number
  company_name: string
  company_linkedin?: string
  linkedin_url?: string
  company_address?: string
  company_city?: string
  company_state?: string
  company_zip?: string
  company_website?: string
  company_country?: string
  industry?: string
  founded_year?: number
  foundedyear?: number
  summary?: string
  contact_count?: number
  created_at?: string
  updated_at?: string
  
  // Processing status fields
  company_overview_status?: string
  website_scraping_status?: string
  
  // Extracted data fields
  companytype?: string
  businessmodel?: string
  fundsize?: string
  aum?: string
  numberofproperties?: number
  headquarters?: string
  numberofoffices?: number
  numberofemployees?: string
  investmentfocus?: string[]
  geographicfocus?: string[]
  recentdeals?: any[]
  mission?: string
  approach?: string
  targetreturn?: string
  propertytypes?: string[]
  strategies?: string[]
  assetclasses?: string[]
  valuecreation?: string[]
  targetmarkets?: string[]
  dealsize?: string
  minimumdealsize?: string
  maximumdealsize?: string
  holdperiod?: string
  riskprofile?: string
  capitalsources?: string[]
  financialproducts?: any[]
  totaltransactions?: string
  totalsquarefeet?: string
  totalunits?: string
  historicalreturns?: string
  portfoliovalue?: string
  partnerships?: any[]
  mainphone?: string
  mainemail?: string
  socialmedia?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
}

export interface PaginatedResponse {
  companies: Company[]
  total: number
  page: number
  totalPages: number
}

export interface Source {
  source: string
  count: number
}

// Company detail types
export interface InvestmentCriteriaDetail {
  criteria_id: number
  target_return?: number
  property_types?: string[]
  property_sub_categories?: string[]
  strategies?: string[]
  minimum_deal_size?: number
  maximum_deal_size?: number
  min_hold_period?: number
  max_hold_period?: number
  financial_products?: string[]
  historical_irr?: number
  historical_em?: number
  country?: string[]
  region?: string[]
  state?: string[]
  city?: string[]
  loan_program?: string[]
  loan_type?: string[]
  capital_source?: string
  structured_loan_tranche?: string[]
  min_loan_term?: number
  max_loan_term?: number
  interest_rate?: number
  interest_rate_sofr?: number
  interest_rate_wsj?: number
  interest_rate_prime?: number
  loan_to_value_max?: number
  loan_to_value_min?: number
  loan_to_cost_max?: number
  loan_to_cost_min?: number
  loan_origination_fee_max?: number
  loan_origination_fee_min?: number
  loan_exit_fee_max?: number
  loan_exit_fee_min?: number
  min_loan_dscr?: number
  max_loan_dscr?: number
  recourse_loan?: string[]
  capital_position?: string[]
  loan_type_normalized?: string[]
  closing_time_weeks?: number
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

export interface CompanyDetail {
  company_id: number
  company_name: string
  company_linkedin?: string
  company_address?: string
  company_city?: string
  company_state?: string
  company_zip?: string
  company_country?: string
  company_website?: string
  company_phone?: string
  industry?: string
  founded_year?: number
  foundedyear?: number
  canonical_handle?: string
  summary?: string
  key_people?: string[]
  recent_developments?: string[]
  investment_interests?: string[]
  risk_factors?: string[]
  engagement_opportunities?: string[]
  headquarters?: string
  investment_criteria?: InvestmentCriteriaDetail[]
  
  // Processing status fields
  company_overview_status?: string
  website_scraping_status?: string
  
  // New comprehensive schema fields
  // Core Company Information
  company_type?: string
  business_model?: string
  
  // Investment & Strategy
  capital_position?: string[]
  investment_focus?: string[]
  investment_strategy_mission?: string
  investment_strategy_approach?: string
  
  // Contact Information
  secondary_phone?: string
  main_email?: string
  secondary_email?: string
  twitter?: string
  facebook?: string
  instagram?: string
  youtube?: string
  
  // Address Information
  additional_address?: string
  additional_city?: string
  additional_state?: string
  additional_zipcode?: string
  additional_country?: string
  office_locations?: string[]
  
  // Company Metrics
  fund_size?: number
  aum?: number
  number_of_properties?: number
  number_of_offices?: number
  number_of_employees?: number
  annual_revenue?: number
  net_income?: number
  ebitda?: number
  profit_margin?: number
  market_capitalization?: number
  market_share_percentage?: number
  
  // Financial Information
  balance_sheet_strength?: string
  funding_sources?: string[]
  recent_capital_raises?: string
  typical_debt_to_equity_ratio?: number
  development_fee_structure?: string
  credit_rating?: string
  dry_powder?: number
  annual_deployment_target?: number
  
  // Investment & Fund Information
  investment_vehicle_type?: string
  active_fund_name_series?: string
  fund_size_active_fund?: number
  fundraising_status?: string
  lender_type?: string
  annual_loan_volume?: number
  lending_origin?: string
  portfolio_health?: string
  
  // Partnership & Leadership
  partnerships?: string[]
  key_equity_partners?: string[]
  key_debt_partners?: string[]
  board_of_directors?: string[]
  key_executives?: string[]
  founder_background?: string
  
  // Market Positioning & Strategy
  market_cycle_positioning?: string
  urban_vs_suburban_preference?: string
  sustainability_esg_focus?: boolean
  technology_proptech_adoption?: boolean
  adaptive_reuse_experience?: boolean
  regulatory_zoning_expertise?: boolean
  
  // Corporate Structure
  corporate_structure?: string
  parent_company?: string
  subsidiaries?: string[]
  stock_ticker_symbol?: string
  stock_exchange?: string
  
  // Business Information
  products_services_description?: string
  target_customer_profile?: string
  major_competitors?: string[]
  unique_selling_proposition?: string
  industry_awards_recognitions?: string[]
  company_history?: string
  
  // Transaction & Portfolio Data
  transactions_completed_last_12m?: number
  total_transaction_volume_ytd?: number
  deal_count_ytd?: number
  average_deal_size?: number
  portfolio_size_sqft?: number
  portfolio_asset_count?: number
  role_in_previous_deal?: string
  
  // Relationship & Pipeline Data
  internal_relationship_manager?: string
  last_contact_date?: string
  pipeline_status?: string
  recent_news_sentiment?: string
  
  // Data Quality & Processing
  data_source?: string
  last_updated_timestamp?: string
  data_confidence_score?: number
  llm_response?: string
  llm_token_usage?: any
  llm_used?: string
  quarterly_earnings_link?: string
  overview_sources?: {
    company_name_sources?: string[]
    company_type_sources?: string[]
    company_industry_sources?: string[]
    business_model_sources?: string[]
    investment_focus_sources?: string[]
    investment_strategy_mission_sources?: string[]
    investment_strategy_approach_sources?: string[]
    website_sources?: string[]
    main_phone_sources?: string[]
    secondary_phone_sources?: string[]
    main_email_sources?: string[]
    secondary_email_sources?: string[]
    linkedin_sources?: string[]
    twitter_sources?: string[]
    facebook_sources?: string[]
    instagram_sources?: string[]
    youtube_sources?: string[]
    headquarters_address_sources?: string[]
    headquarters_city_sources?: string[]
    headquarters_state_sources?: string[]
    headquarters_zipcode_sources?: string[]
    headquarters_country_sources?: string[]
    additional_address_sources?: string[]
    additional_city_sources?: string[]
    additional_state_sources?: string[]
    additional_zipcode_sources?: string[]
    additional_country_sources?: string[]
    fund_size_sources?: string[]
    aum_sources?: string[]
    number_of_properties_sources?: string[]
    number_of_offices_sources?: string[]
    office_locations_sources?: string[]
    founded_year_sources?: string[]
    number_of_employees_sources?: string[]
    partnerships_sources?: string[]
    balance_sheet_strength_sources?: string[]
    funding_sources_sources?: string[]
    recent_capital_raises_sources?: string[]
    typical_debt_to_equity_ratio_sources?: string[]
    development_fee_structure_sources?: string[]
    credit_rating_sources?: string[]
    dry_powder_sources?: string[]
    annual_deployment_target_sources?: string[]
    investment_vehicle_type_sources?: string[]
    active_fund_name_series_sources?: string[]
    fund_size_active_fund_sources?: string[]
    fundraising_status_sources?: string[]
    lender_type_sources?: string[]
    annual_loan_volume_sources?: string[]
    lending_origin_sources?: string[]
    portfolio_health_sources?: string[]
    key_equity_partners_sources?: string[]
    key_debt_partners_sources?: string[]
    board_of_directors_sources?: string[]
    key_executives_sources?: string[]
    founder_background_sources?: string[]
    market_cycle_positioning_sources?: string[]
    urban_vs_suburban_preference_sources?: string[]
    sustainability_esg_focus_sources?: string[]
    technology_proptech_adoption_sources?: string[]
    adaptive_reuse_experience_sources?: string[]
    regulatory_zoning_expertise_sources?: string[]
    corporate_structure_sources?: string[]
    parent_company_sources?: string[]
    subsidiaries_sources?: string[]
    stock_ticker_symbol_sources?: string[]
    stock_exchange_sources?: string[]
    products_services_description_sources?: string[]
    target_customer_profile_sources?: string[]
    major_competitors_sources?: string[]
    unique_selling_proposition_sources?: string[]
    industry_awards_recognitions_sources?: string[]
    company_history_sources?: string[]
    transactions_completed_last_12m_sources?: string[]
    total_transaction_volume_ytd_sources?: string[]
    deal_count_ytd_sources?: string[]
    average_deal_size_sources?: string[]
    portfolio_size_sqft_sources?: string[]
    portfolio_asset_count_sources?: string[]
    role_in_previous_deal_sources?: string[]
    internal_relationship_manager_sources?: string[]
    last_contact_date_sources?: string[]
    pipeline_status_sources?: string[]
    recent_news_sentiment_sources?: string[]
    data_source_sources?: string[]
    data_confidence_score_sources?: string[]
    quarterly_earnings_link_sources?: string[]
    general_sources?: string[]
  }
  
  overview?: {
    structure_history?: string
    capital_position?: string[]
    primary_industry?: string
    cre_ecosystem_role?: string
    executive_contacts?: Array<{
      name: string
      email: string
      phone: string
      title: string
    }>
    investment_strategy?: string[]
    recent_transactions?: Array<{
      asset_type: string
      location: string
      close_date: string
      capital_deployed: string
    }>
    hold_horizon?: string
    lending_program?: {
      program_name: string
      loan_size_range: string
      loan_types: string[]
      geographic_focus: string
      lien_position: string[]
      interest_rate_structure: string
      max_ltv_ltc: string
      fees: string
      term_amortization: string
      property_types: string[]
      risk_profile: string[]
    }
    capital_commitments?: {
      debt_range?: string
      equity_range?: string
    }
    investment_program?: {
      equity_size_range: string
      asset_type: string[]
      deal_structure: string[]
      property_types: string[]
      geography: string
      hold_period: string
    }
  }
  news: Array<{
    title: string
    content: string
    published_date: string
    url: string
    publisher: string
    sentiment_score: number
  }>
  contacts: Array<CompanyContact>
  contact_count: number
  // Fields from extraction schema
  companyProfile?: {
    companyType: string
    businessModel: string
    fundSize: string
    aum: string
    numberOfProperties: number
    headquarters: string
    numberOfOffices: number
    officeLocations: string[]
    numberOfEmployees: string
    investmentFocus: string[]
    geographicFocus: string[]
  }
  executiveTeam?: Array<{
    name: string
    title: string
    email: string
    phone: string
    bio: string
  }>
  recentDeals?: Array<{
    property: string
    location: string
    dealType: string
    amount: string
    date: string
    propertyType: string
    squareFeet: string
    units: number
  }>
  investmentStrategy?: {
    mission: string
    approach: string
    targetReturn: string
    propertyTypes: string[]
    strategies: string[]
    assetClasses: string[]
    valueCreation: string[]
  }
  investmentCriteria?: {
    targetMarkets: string[]
    dealSize: string
    minimumDealSize: string
    maximumDealSize: string
    holdPeriod: string
    riskProfile: string
    propertyTypes: string[]
    propertySubcategories: string[]
    assetTypes: string[]
    loanTypes: string[]
  }
  capitalSources?: string[]
  financialProducts?: Array<{
    productType: string
    terms: string[]
    typicalAmount: string
    description: string
  }>
  trackRecord?: {
    totalTransactions: string
    totalSquareFeet: string
    totalUnits: string
    historicalReturns: string
    portfolioValue: string
  }
  contactInfo?: {
    mainPhone: string
    mainEmail: string
    socialMedia: {
      linkedin: string
      twitter: string
      facebook: string
      instagram: string
    }
  }
  scraped_data?: ScrapedData
  scraped_contacts?: ScrapedContact[]
}

export interface CompanyContact {
  contact_id: number
  first_name: string
  last_name: string
  full_name: string
  title: string
  headline: string
  seniority: string
  email: string
  personal_email: string
  email_status: string
  linkedin_url: string
  contact_city: string
  contact_state: string
  contact_country: string
  email_generated?: boolean
  smartlead_lead_id?: string
}

export interface ScrapedData {
  // Contact information
  phone_number?: string
  contact_phone?: string
  email?: string
  contact_email?: string
  
  // Social media
  linkedin_url?: string
  social_linkedin?: string
  twitter_url?: string
  social_twitter?: string
  facebook_url?: string
  social_facebook?: string
  
  // Investment criteria
  deal_size?: string
  investment_size?: string
  min_deal_size?: string
  min_investment?: string
  max_deal_size?: string
  max_investment?: string
  target_markets?: string[]
  markets?: string[]
  
  // Add any other fields that might be needed
  [key: string]: any
}

export interface ScrapedContact {
  contact_id: number;
  first_name: string;
  last_name: string;
  full_name: string;
  title: string;
  headline: string;
  seniority: string;
  email: string;
  personal_email: string;
  email_status: string;
  linkedin_url: string;
  contact_city: string;
  contact_state: string;
  contact_country: string;
  phone?: string;
  bio?: string;
  category?: string;
  extra_attrs?: Record<string, any>;
  email_generated?: boolean;
  smartlead_lead_id?: string;
  contact_enrichment_v2_status?: string;  
  
} 