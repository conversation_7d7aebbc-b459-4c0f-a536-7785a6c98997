import { Company, CompanyDetail, ScrapedData } from './types';

/**
 * Get a color class based on industry
 */
export const getIndustryColor = (industry: string): string => {
  const colors: Record<string, string> = {
    'Technology': 'bg-blue-500 text-white',
    'Finance': 'bg-green-500 text-white',
    'Healthcare': 'bg-red-500 text-white',
    'Manufacturing': 'bg-orange-500 text-white',
    'Retail': 'bg-purple-500 text-white',
    'Real Estate': 'bg-indigo-500 text-white',
    'Insurance': 'bg-teal-500 text-white',
    'Banking': 'bg-emerald-500 text-white',
    'Energy': 'bg-yellow-500 text-white',
    'Telecommunications': 'bg-sky-500 text-white',
    'default': 'bg-gray-500 text-white'
  }
  
  return colors[industry] || colors.default;
}

/**
 * Format location string from company data
 */
export const formatLocation = (company: Company | CompanyDetail): string => {
  const parts: string[] = [];
  
  // Try to use headquarters first if available
  if (company.headquarters) return company.headquarters;
  
  // Fall back to address components
  if (company.company_city) parts.push(company.company_city);
  if (company.company_state) parts.push(company.company_state);
  if (company.company_country && (!company.company_city || !company.company_state)) {
    parts.push(company.company_country);
  }
  
  return parts.join(', ');
}

/**
 * Get company initials from company name
 */
export const getCompanyInitials = (companyName: string): string => {
  if (!companyName) return 'CO';
  
  const words = companyName.split(' ');
  if (words.length === 1) {
    return words[0].substring(0, 2).toUpperCase();
  }
  
  return (words[0][0] + words[1][0]).toUpperCase();
}

/**
 * Normalize scraped data to a consistent format
 */
export const normalizeScrapedData = (scrapedData?: ScrapedData): {
  contactInfo: {
    mainPhone?: string
    mainEmail?: string
    socialMedia?: {
      linkedin?: string
      twitter?: string
      facebook?: string
    }
  },
  investmentCriteria: {
    dealSize?: string
    minimumDealSize?: string
    maximumDealSize?: string
    targetMarkets?: string[]
  }
} => {
  if (!scrapedData) {
    return {
      contactInfo: {},
      investmentCriteria: {}
    };
  }

  return {
    contactInfo: {
      mainPhone: scrapedData.phone_number || scrapedData.contact_phone,
      mainEmail: scrapedData.email || scrapedData.contact_email,
      socialMedia: {
        linkedin: scrapedData.linkedin_url || scrapedData.social_linkedin,
        twitter: scrapedData.twitter_url || scrapedData.social_twitter,
        facebook: scrapedData.facebook_url || scrapedData.social_facebook
      }
    },
    investmentCriteria: {
      dealSize: scrapedData.deal_size || scrapedData.investment_size,
      minimumDealSize: scrapedData.min_deal_size || scrapedData.min_investment,
      maximumDealSize: scrapedData.max_deal_size || scrapedData.max_investment,
      targetMarkets: scrapedData.target_markets || scrapedData.markets || []
    }
  };
} 