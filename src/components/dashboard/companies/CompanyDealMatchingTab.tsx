import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { 
  Target, 
  Users, 
  Search, 
  Filter, 
  RefreshCw, 
  TrendingUp, 
  MapPin, 
  DollarSign, 
  Building2, 
  Clock,
  Percent,
  Zap,
  AlertCircle,
  CheckCircle,
  ChevronRight,
  XCircle,
  Info
} from "lucide-react";
import { toast } from "sonner";

// Collapsible component for non-matched fields
const CollapsibleNonMatchedFields: React.FC<{ fields: any[] }> = ({ fields }) => {
  const [showNonMatched, setShowNonMatched] = useState(false);
  
  if (fields.length === 0) return null;
  
  return (
    <div>
      <button
        onClick={() => setShowNonMatched(!showNonMatched)}
        className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 mb-2 transition-colors"
      >
        <ChevronRight className={`h-4 w-4 transform transition-transform ${showNonMatched ? 'rotate-90' : ''}`} />
        {fields.length} fields didn't match - click to {showNonMatched ? 'collapse' : 'expand'}
      </button>
      
      {showNonMatched && (
        <div className="space-y-2">
          {fields.map((field: any, index: number) => (
            <div key={index} className="p-2 bg-gray-50 rounded border border-gray-200">
              <div className="flex justify-between items-center mb-1">
                <span className="text-gray-500 capitalize">{field.field_name.replace(/_/g, ' ')}:</span>
                <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600 border-gray-300">
                  0% (weight: {field.weight_percentage}%)
                </Badge>
              </div>
              <div className="text-xs text-gray-400">
                {field.reason}
              </div>
              <div className="text-xs text-gray-400">
                Confidence: {Math.round(field.confidence * 100)}%
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Collapsible component for Match Reasons & Scoring
const CollapsibleMatchReasons: React.FC<{ deal: any; formatScore: (score: number) => string }> = ({ deal, formatScore }) => {
  const [showMatchReasons, setShowMatchReasons] = useState(false);
  
  return (
    <div className="p-6 pb-4 border-b border-gray-100">
      <button
        onClick={() => setShowMatchReasons(!showMatchReasons)}
        className="w-full flex items-center justify-between mb-3 hover:bg-gray-50 p-2 rounded transition-colors"
      >
        <h5 className="font-semibold text-gray-900 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          Match Reasons & Scoring
        </h5>
        <ChevronRight className={`h-5 w-5 transform transition-transform ${showMatchReasons ? 'rotate-90' : ''} text-gray-400`} />
      </button>
      
      {showMatchReasons && (
        <div className="space-y-4">
          {/* Score Breakdown - Use Actual V2 Data */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h6 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Score Breakdown: {formatScore(deal.match_score)}
            </h6>
            {deal.scoring_summary ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                {deal.scoring_summary.field_breakdown
                  .filter((field: any) => field.score_percentage > 0)
                  .map((field: any, index: number) => (
                    <div key={index} className="bg-white p-3 rounded border border-blue-100">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-blue-800 capitalize font-medium">
                          {field.field_name.replace(/_/g, ' ')}
                        </span>
                        <Badge className="bg-green-100 text-green-800 border-green-200">
                          {field.score_percentage}%
                        </Badge>
                      </div>
                      <div className="text-xs text-blue-600 mb-1">
                        {field.reason}
                      </div>
                      <div className="text-xs text-blue-500">
                        Weight: {field.weight_percentage}% | Confidence: {Math.round(field.confidence * 100)}%
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-sm text-blue-700">
                No detailed scoring breakdown available
              </div>
            )}
          </div>

          {/* Top Matches */}
          {deal.scoring_summary?.top_matches && deal.scoring_summary.top_matches.length > 0 && (
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h6 className="font-medium text-green-900 mb-2 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Top Matches
              </h6>
              <div className="space-y-2">
                {deal.scoring_summary.top_matches.map((match: any, index: number) => (
                  <div key={index} className="bg-white p-2 rounded border border-green-100">
                    <div className="flex justify-between items-center">
                      <span className="text-green-800 capitalize font-medium">
                        {match.field.replace(/_/g, ' ')}
                      </span>
                      <Badge className="bg-green-100 text-green-800 border-green-200">
                        {match.score}%
                      </Badge>
                    </div>
                    <div className="text-xs text-green-600 mt-1">
                      {match.reason}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Non-matched Fields */}
          {deal.scoring_summary?.field_breakdown && (
            <CollapsibleNonMatchedFields 
              fields={deal.scoring_summary.field_breakdown.filter((field: any) => field.score_percentage === 0)} 
            />
          )}

          {/* Match Analysis */}
          {deal.scoring_summary?.match_analysis && (
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h6 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                <Info className="h-4 w-4" />
                Match Analysis
              </h6>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-900">
                    {deal.scoring_summary.match_analysis.total_fields_evaluated}
                  </div>
                  <div className="text-xs text-gray-600">Total Fields</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">
                    {deal.scoring_summary.match_analysis.fields_with_matches}
                  </div>
                  <div className="text-xs text-gray-600">Matched</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-red-600">
                    {deal.scoring_summary.match_analysis.fields_without_matches}
                  </div>
                  <div className="text-xs text-gray-600">Not Matched</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-blue-600">
                    {Math.round(deal.scoring_summary.match_analysis.average_confidence * 100)}%
                  </div>
                  <div className="text-xs text-gray-600">Avg Confidence</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

interface CompanyDealMatchingTabProps {
  companyId: string;
  company?: any;
}

const CompanyDealMatchingTab: React.FC<CompanyDealMatchingTabProps> = ({ companyId, company }) => {
  const router = useRouter();
  const [matchingDeals, setMatchingDeals] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [isCrmMode, setIsCrmMode] = useState(false);
  const [expandedDealId, setExpandedDealId] = useState<string | null>(null);

  // Filter states
  const [filters, setFilters] = useState({
    minScore: 50,
    maxScore: 100,
    capitalPosition: '',
    dealStage: '',
    propertyType: '',
    strategy: '',
    searchTerm: ''
  });

  const [filteredDeals, setFilteredDeals] = useState<any[]>([]);

  // Fetch matching deals for this company
  const fetchMatchingDeals = async () => {
    if (!companyId) return;
    
    console.log('🔍 Fetching matching deals for company:', companyId);
    setLoading(true);
    setError(null);
    
    try {
      const crmModeParam = isCrmMode ? '?crm_mode=true' : '';
      const response = await fetch(`/api/matching-v2/deals-for-contact/${companyId}?entity_type=company${crmModeParam}`);
      console.log('📡 API Response status:', response.status);
      console.log('🔧 CRM mode enabled:', isCrmMode);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch matching deals: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('📊 API Response data:', data);
      console.log('💼 Deals array:', data.matches);
      console.log('📈 Deals count:', data.matches ? data.matches.length : 'undefined');
      
      // Check if matches is an array and has content
      if (Array.isArray(data.matches)) {
        console.log('✅ Matches is an array with length:', data.matches.length);
        if (data.matches.length > 0) {
          console.log('📋 First deal sample:', data.matches[0]);
          console.log('🔑 First deal keys:', Object.keys(data.matches[0]));
        }
      } else {
        console.log('❌ Matches is not an array:', typeof data.matches);
        console.log('📋 Matches value:', data.matches);
      }
      
      setMatchingDeals(data.matches || []);
      
      if (data.matches && data.matches.length > 0) {
        console.log('🎉 Successfully loaded deals:', data.matches.length);
        toast.success(`Found ${data.matches.length} matching deals`);
      } else {
        console.log('ℹ️ No matching deals found');
        toast.info('No matching deals found for this company');
      }
    } catch (error) {
      console.error('❌ Error fetching matching deals:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch matching deals');
      toast.error('Failed to load matching deals');
    } finally {
      setLoading(false);
    }
  };

  // Apply filters to deals
  useEffect(() => {
    let filtered = [...matchingDeals];

    // Score filter
    filtered = filtered.filter(deal => 
      deal.match_score >= filters.minScore && deal.match_score <= filters.maxScore
    );

    // Capital position filter
    if (filters.capitalPosition) {
      filtered = filtered.filter(deal => 
        deal.capital_positions && deal.capital_positions.includes(filters.capitalPosition)
      );
    }

    // Deal stage filter
    if (filters.dealStage) {
      filtered = filtered.filter(deal => 
        deal.deal_data?.deal_stage === filters.dealStage
      );
    }

    // Property type filter
    if (filters.propertyType) {
      filtered = filtered.filter(deal => 
        deal.deal_data?.property_type === filters.propertyType
      );
    }

    // Strategy filter
    if (filters.strategy) {
      filtered = filtered.filter(deal => 
        deal.deal_data?.strategy === filters.strategy
      );
    }

    // Search term filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(deal => 
        deal.deal_name?.toLowerCase().includes(searchLower) ||
        deal.deal_data?.summary?.toLowerCase().includes(searchLower) ||
        deal.deal_data?.property_address?.toLowerCase().includes(searchLower)
      );
    }

    setFilteredDeals(filtered);
  }, [matchingDeals, filters]);

  // Load deals on component mount
  useEffect(() => {
    fetchMatchingDeals();
  }, [companyId, isCrmMode]);

  // Debug logging
  useEffect(() => {
    if (matchingDeals.length > 0) {
      console.log('🔍 Filter settings:', filters);
    }
  }, [matchingDeals, loading, error, filteredDeals, filters]);

  const formatScore = (score: number) => {
    return `${Math.round(score)}%`;
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(1)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  const navigateToDeal = (dealId: string) => {
    router.push(`/dashboard/deals/${dealId}`);
  };

  return (
    <div className="space-y-6">
      {/* Header with action buttons */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Deal Matching (V2)</h3>
          <p className="text-sm text-gray-600">
            Find deals that match this company's investment criteria
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="crm-mode-company-deal-matching"
              checked={isCrmMode}
              onCheckedChange={(checked) => setIsCrmMode(checked as boolean)}
            />
            <label htmlFor="crm-mode-company-deal-matching" className="text-sm font-medium text-gray-700">
              CRM Mode
            </label>
          </div>
          {isCrmMode && (
            <div className="text-xs text-gray-600 bg-blue-50 px-2 py-1 rounded">
              Internal deals only, within 1.6 years
            </div>
          )}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </Button>
            <Button
              onClick={fetchMatchingDeals}
              disabled={loading}
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Debug Info */}
      <Card className="bg-gray-50 border-gray-200">
        <CardContent className="p-4">
          <div className="text-sm text-gray-600">
            <div><strong>Company ID:</strong> {companyId}</div>
            <div><strong>Total Deals:</strong> {matchingDeals.length}</div>
            <div><strong>Filtered Deals:</strong> {filteredDeals.length}</div>
            <div><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</div>
            <div><strong>Error:</strong> {error || 'None'}</div>
            <div><strong>CRM Mode:</strong> {isCrmMode ? 'Yes' : 'No'}</div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Score Range */}
              <div className="space-y-2">
                <Label>Min Score</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={filters.minScore}
                  onChange={(e) => setFilters(prev => ({ ...prev, minScore: parseInt(e.target.value) || 0 }))}
                />
              </div>
              <div className="space-y-2">
                <Label>Max Score</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={filters.maxScore}
                  onChange={(e) => setFilters(prev => ({ ...prev, maxScore: parseInt(e.target.value) || 100 }))}
                />
              </div>

              {/* Search */}
              <div className="space-y-2">
                <Label>Search</Label>
                <Input
                  placeholder="Search deals..."
                  value={filters.searchTerm}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Loading matching deals...</p>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Error loading deals</span>
            </div>
            <p className="text-red-600 mt-2">{error}</p>
            <Button 
              onClick={fetchMatchingDeals} 
              variant="outline" 
              size="sm" 
              className="mt-4"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {!loading && !error && (
        <div className="space-y-4">
          {filteredDeals.length > 0 ? (
            filteredDeals.map((deal) => (
              <Card key={deal.deal_id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  {/* Main Deal Info */}
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="text-lg font-semibold text-gray-900">
                            {deal.deal_name}
                          </h4>
                          <Badge 
                            className={`px-3 py-1 text-sm font-medium ${
                              deal.match_score >= 80 
                                ? 'bg-green-100 text-green-800 border-green-200' 
                                : deal.match_score >= 60 
                                ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                                : 'bg-red-100 text-red-800 border-red-200'
                            }`}
                          >
                            {formatScore(deal.match_score)}
                          </Badge>
                        </div>
                        
                        {deal.deal_data?.summary && (
                          <p className="text-gray-600 mb-3 line-clamp-2">
                            {deal.deal_data.summary}
                          </p>
                        )}

                        {/* Deal Details Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                          {deal.deal_data?.ask_amount && (
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {formatCurrency(deal.deal_data.ask_amount)}
                              </span>
                            </div>
                          )}
                          
                          {deal.deal_data?.property_address && (
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {deal.deal_data.property_address}
                              </span>
                            </div>
                          )}
                          
                          {deal.deal_data?.property_type && (
                            <div className="flex items-center gap-2">
                              <Building2 className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {deal.deal_data.property_type}
                              </span>
                            </div>
                          )}
                          
                          {deal.capital_positions && deal.capital_positions.length > 0 && (
                            <div className="flex items-center gap-2">
                              <Target className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {deal.capital_positions.join(', ')}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Capital Positions */}
                        {deal.capital_positions && deal.capital_positions.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-4">
                            {deal.capital_positions.map((position: string, index: number) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {position}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setExpandedDealId(
                            expandedDealId === deal.deal_id ? null : deal.deal_id
                          )}
                        >
                          {expandedDealId === deal.deal_id ? 'Hide Details' : 'Show Details'}
                        </Button>
                        <Button
                          onClick={() => navigateToDeal(deal.deal_id)}
                          size="sm"
                        >
                          View Deal
                        </Button>
                      </div>
                    </div>

                    {/* Expanded Details */}
                    {expandedDealId === deal.deal_id && (
                      <CollapsibleMatchReasons deal={deal} formatScore={formatScore} />
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Target className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Matching Deals Found</h3>
                <p className="text-gray-600 mb-4">
                  No deals match this company's investment criteria with the current filters.
                </p>
                <div className="flex justify-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setFilters({
                      minScore: 0,
                      maxScore: 100,
                      capitalPosition: '',
                      dealStage: '',
                      propertyType: '',
                      strategy: '',
                      searchTerm: ''
                    })}
                  >
                    Clear Filters
                  </Button>
                  <Button onClick={fetchMatchingDeals}>
                    Refresh
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default CompanyDealMatchingTab;
