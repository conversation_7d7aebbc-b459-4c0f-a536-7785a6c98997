'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Building2, Building, Search, Users, Newspaper, Briefcase, Mail, Phone, Globe, MapPin, 
         ChevronRight, MessageSquare, Plus, ExternalLink, Sparkles, Target, 
         UserPlus, History, Zap, Loader2, RefreshCw, Trash2 } from 'lucide-react';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import CompanyForm from './detail-components/CompanyForm';
import { useRouter, useSearchParams } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import InvestmentCriteriaSliderV2 from '../investment-criteria/InvestmentCriteriaSliderV2';

interface MetricItemProps {
  label: string;
  value: string;
  change?: string;
}

const MetricItem = ({ label, value, change }: MetricItemProps) => (
  <div className="bg-gray-50/50 p-4 rounded-lg border border-gray-100">
    <p className="text-sm font-medium text-gray-500">{label}</p>
    <h4 className="text-2xl font-semibold mt-1">{value}</h4>
    {change && (
      <p className="text-xs font-medium text-green-600 mt-1">
        {change}
      </p>
    )}
  </div>
);

interface ActivityItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  time: string;
}

const ActivityItem = ({ icon, title, description, time }: ActivityItemProps) => (
  <div className="flex items-start space-x-3">
    <div className="h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center">
      {icon}
    </div>
    <div className="flex-1">
      <div className="font-medium">{title}</div>
      <div className="text-sm text-gray-600">{description}</div>
    </div>
    <div className="text-sm text-gray-500">{time}</div>
  </div>
);

interface ContactItemProps {
  name: string;
  title: string;
  email: string;
  phone?: string;
  relationship?: number;
}

const ContactItem = ({ name, title, email, phone, relationship }: ContactItemProps) => (
  <div className="p-4 border border-gray-100 rounded-lg hover:bg-gray-50">
    <div className="flex justify-between items-start">
      <div>
        <div 
          className="font-medium cursor-pointer hover:text-blue-600 transition-colors"
          onClick={() => {
            // Extract contact ID from the name or use a different identifier
            // For now, we'll search for the contact by name and email
            if (email) {
              // Open a search for this contact in a new tab
              const searchQuery = encodeURIComponent(`${name} ${email}`);
              window.open(`/dashboard/entity?tab=contacts&searchTerm=${searchQuery}`, '_blank');
            }
          }}
          title="Click to view contact details"
        >
          {name}
        </div>
        {title && <div className="text-sm text-gray-500">{title}</div>}
      </div>
      <Button size="sm" variant="outline">Contact</Button>
    </div>
    {(email || phone) && (
      <div className="flex mt-2 text-sm text-gray-500 space-x-4">
        {email && (
          <div>
            Email: <a href={`mailto:${email}`} className="text-blue-600 hover:underline">{email}</a>
          </div>
        )}
        {phone && (
          <div>
            Phone: <a href={`tel:${phone}`} className="text-blue-600 hover:underline">{phone}</a>
          </div>
        )}
      </div>
    )}
  </div>
 );

interface DealItemProps {
  title: string;
  type: string;
  value: string;
  stage: string;
  progress: number;
}

const DealItem = ({ title, type, value, stage, progress }: DealItemProps) => (
  <div className="p-4 border rounded-lg">
    <div className="flex justify-between items-start mb-3">
      <div>
        <div className="font-medium">{title}</div>
        <div className="text-sm text-gray-500">{type}</div>
      </div>
      <Badge>{stage}</Badge>
    </div>
    <div className="flex items-center justify-between">
      <div className="text-lg font-medium">{value}</div>
      <Progress value={progress} className="w-32" />
    </div>
  </div>
);

interface NewsItemProps {
  title: string;
  source: string;
  date: string;
  sentiment: 'positive' | 'neutral' | 'negative';
}

const NewsItem = ({ title, source, date, sentiment }: NewsItemProps) => {
  const sentimentColors = {
    positive: 'text-green-600 bg-green-50',
    neutral: 'text-gray-600 bg-gray-50',
    negative: 'text-red-600 bg-red-50'
  };

  return (
    <div className="p-4 border rounded-lg">
      <div className="flex items-start justify-between">
        <div>
          <div className="font-medium">{title}</div>
          <div className="flex items-center space-x-3 mt-1">
            <span className="text-sm text-gray-500">{source}</span>
            <span className="text-sm text-gray-500">{date}</span>
          </div>
        </div>
        <Badge className={sentimentColors[sentiment]}>
          {sentiment.charAt(0).toUpperCase() + sentiment.slice(1)}
        </Badge>
      </div>
    </div>
  );
};

interface InsightItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const InsightItem = ({ icon, title, description }: InsightItemProps) => (
  <div className="flex space-x-3">
    <div className="flex-shrink-0 h-9 w-9 rounded-full flex items-center justify-center bg-blue-50 text-blue-600">
      {icon}
    </div>
    <div>
      <h4 className="text-sm font-medium">{title}</h4>
      <p className="text-sm text-gray-600 mt-1">{description}</p>
    </div>
  </div>
);



interface ActionItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

const ActionItem = ({ icon, title, description, priority }: ActionItemProps) => (
  <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
    <div className={`h-8 w-8 rounded-full flex items-center justify-center
      ${priority === 'high' ? 'bg-red-100 text-red-600' : 
        priority === 'medium' ? 'bg-yellow-100 text-yellow-600' : 
        'bg-blue-100 text-blue-600'}`}>
      {icon}
    </div>
    <div>
      <div className="font-medium">{title}</div>
      <div className="text-sm text-gray-600">{description}</div>
    </div>
    <ChevronRight className="h-5 w-5 text-gray-400 ml-auto" />
  </div>
);

interface CompanyData {
  // Core company information
  company_id?: number;
  company_name?: string;
  name?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  company_website?: string;
  company_linkedin?: string;
  company_phone?: string;
  main_email?: string;
  secondary_email?: string;
  
  // V2 Company Overview fields
  company_type?: string;
  business_model?: string;
  founded_year?: number;
  investment_focus?: string[];
  investment_strategy_mission?: string;
  investment_strategy_approach?: string;
  
  // Financial metrics
  fund_size?: number;
  aum?: number;
  annual_revenue?: number;
  market_capitalization?: number;
  number_of_properties?: number;
  number_of_offices?: number;
  number_of_employees?: number;
  dry_powder?: number;
  annual_deployment_target?: number;
  
  // Additional V2 fields
  partnerships?: string[];
  key_executives?: string[];
  major_competitors?: string[];
  company_history?: string;
  products_services_description?: string;
  target_customer_profile?: string;
  office_locations?: string[];
  transactions_completed_last_12m?: number;
  portfolio_size_sqft?: number;
  portfolio_asset_count?: number;
  data_confidence_score?: number;
  
  // Processing status
  website_scraping_status?: string;
  company_overview_status?: string;
  overview_v2_status?: string;
  investment_criteria_status?: string;
  
  // Relations - counts only (full data loaded separately)
  contact_count?: string | number;
  has_investment_criteria?: boolean;
  investment_criteria_count?: number;
  deals_count?: number;
  
  // Investment criteria data
  minimum_deal_size?: string;
}

interface ContactData {
  contact_id: number;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  title?: string;
  headline?: string;
  email?: string;
  personal_email?: string;
  phone_number?: string;
  linkedin_url?: string;
  // ... other contact fields
}

interface ContactsResponse {
  success: boolean;
  contacts: ContactData[];
  stats: {
    total_contacts: number;
    contacts_with_email: number;
    contacts_with_linkedin: number;
    verified_emails: number;
    enriched_contacts: number;
  };
}

interface DealsResponse {
  success: boolean;
  deals: any[];
  count: number;
}

interface CompanyViewProps {
  company?: CompanyData;
  isEmbedded?: boolean;
  onAddContact?: () => void;
}

const CompanyView: React.FC<CompanyViewProps> = ({ 
  company = {}, 
  isEmbedded = false,
  onAddContact
}) => {
  const [isAddingCompany, setIsAddingCompany] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStage, setProcessingStage] = useState<string>('');
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  // Lazy loading state for different tabs
  const [contacts, setContacts] = useState<ContactData[]>([]);
  const [contactsStats, setContactsStats] = useState<ContactsResponse['stats'] | null>(null);
  const [deals, setDeals] = useState<any[]>([]);
  const [loadingContacts, setLoadingContacts] = useState(false);
  const [loadingDeals, setLoadingDeals] = useState(false);
  const [contactsLoaded, setContactsLoaded] = useState(false);
  const [dealsLoaded, setDealsLoaded] = useState(false);

  // Get current tab from URL, default to 'overview'
  const currentTab = searchParams?.get('tab') || 'overview';

  // Handle tab changes and lazy load data
  const handleTabChange = (newTab: string) => {
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.set('tab', newTab);
    router.push(`?${params.toString()}`);

    // Trigger lazy loading based on tab
    if (newTab === 'contacts' && !contactsLoaded && !loadingContacts) {
      loadContacts();
    } else if (newTab === 'deals' && !dealsLoaded && !loadingDeals) {
      loadDeals();
    }
  };

  // Lazy loading functions - memoized to avoid useEffect dependency issues
  const loadContacts = useCallback(async () => {
    if (!company.company_id || loadingContacts || contactsLoaded) return;
    
    setLoadingContacts(true);
    try {
      const response = await fetch(`/api/companies/${company.company_id}/contacts`);
      const data: ContactsResponse = await response.json();
      
      if (data.success) {
        setContacts(data.contacts);
        setContactsStats(data.stats);
        setContactsLoaded(true);
      } else {
        toast({
          title: "Error",
          description: "Failed to load contacts",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
      toast({
        title: "Error",
        description: "Failed to load contacts",
        variant: "destructive",
      });
    } finally {
      setLoadingContacts(false);
    }
  }, [company.company_id, loadingContacts, contactsLoaded, toast]);

  const loadDeals = useCallback(async () => {
    if (!company.company_id || loadingDeals || dealsLoaded) return;
    
    setLoadingDeals(true);
    try {
      const response = await fetch(`/api/companies/${company.company_id}/deals`);
      const data: DealsResponse = await response.json();
      
      if (data.success) {
        setDeals(data.deals);
        setDealsLoaded(true);
      } else {
        toast({
          title: "Error", 
          description: "Failed to load deals",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading deals:', error);
      toast({
        title: "Error",
        description: "Failed to load deals", 
        variant: "destructive",
      });
    } finally {
      setLoadingDeals(false);
    }
  }, [company.company_id, loadingDeals, dealsLoaded, toast]);

  // Load data when current tab is accessed (for direct URL access)
  useEffect(() => {
    if (currentTab === 'contacts' && !contactsLoaded && !loadingContacts) {
      loadContacts();
    } else if (currentTab === 'deals' && !dealsLoaded && !loadingDeals) {
      loadDeals();
    }
  }, [currentTab, contactsLoaded, loadingContacts, dealsLoaded, loadingDeals, loadContacts, loadDeals]);
  
  // Extract company data with fallbacks (using V2 fields)
  const companyName = company.company_name || company.name || "Company Name";
  const industry = company.industry || "Not specified";
  const location = company.company_city && company.company_state ? 
    `${company.company_city}, ${company.company_state}` : 
    company.company_city || company.company_state || "Location not specified";
  const website = company.company_website || "#";
  const websiteDomain = website !== "#" ? website.replace(/^https?:\/\//, '').replace(/\/$/, '') : "website";
  const summary = company.products_services_description || company.company_history || "No company description available.";
  const contactCount = company.contact_count || 0;
  const initials = companyName.substring(0, 2).toUpperCase();

  // Function to handle processing triggers
  const handleProcessingTrigger = async (stage: string, stageName: string) => {
    if (!company.company_id) {
      toast({
        title: "Error",
        description: "Company ID is required to trigger processing",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setProcessingStage(stage);

    try {
      const response = await fetch('/api/processing/queue/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          processorType: stage,
          options: {
            limit: 1,
            singleId: company.company_id,
            batchSize: 1,
            entityType: 'company',
            filters: {}
          }
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Processing Started",
          description: `${stageName} has been triggered successfully for ${companyName}`,
        });
        
        // Refresh after a short delay
        setTimeout(() => {
          router.refresh();
        }, 2000);
      } else {
        throw new Error(data.error || 'Failed to trigger processing');
      }
    } catch (error) {
      console.error(`Error triggering ${stageName}:`, error);
      toast({
        title: "Processing Error",
        description: error instanceof Error ? error.message : 'Failed to trigger processing',
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProcessingStage('');
    }
  };



  // Function to handle creating a new company
  const handleAddCompany = () => {
    setIsAddingCompany(true);
  };

  const handleCancelAddCompany = () => {
    setIsAddingCompany(false);
  };

  // Function to handle deleting a company
  const handleDeleteCompany = async () => {
    if (!company.company_id) {
      toast({
        title: "Error",
        description: "Company ID is required to delete company",
        variant: "destructive",
      });
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/companies?companyId=${company.company_id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Company Deleted",
          description: `"${companyName}" has been successfully deleted`,
        });
        
        // Redirect to companies list
        router.push('/dashboard/entity?tab=companies');
      } else {
        throw new Error(data.error || 'Failed to delete company');
      }
    } catch (error) {
      console.error('Error deleting company:', error);
      toast({
        title: "Delete Error",
        description: error instanceof Error ? error.message : 'Failed to delete company',
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Determine if we should show the header and sidebar based on if this is embedded
  const showHeader = !isEmbedded;
  const containerClass = isEmbedded ? "" : "h-screen bg-gray-50 overflow-auto";
  
  // If we're adding a company, show the company form
  if (isAddingCompany) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Add New Company</h1>
          <Button variant="ghost" onClick={handleCancelAddCompany}>Cancel</Button>
        </div>
        <CompanyForm 
          isEmbedded={true}
          onCancel={handleCancelAddCompany}
          onSuccess={(companyId) => {
            setIsAddingCompany(false);
            router.push(`/dashboard/companies/${companyId}`);
            router.refresh();
          }}
        />
      </div>
    );
  }
  
  return (
    <div className={containerClass}>
      {/* Company Header - only show if not embedded */}
      {showHeader && (
        <div className="bg-white border-b border-gray-200 px-8 py-6">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarFallback className="bg-blue-600">{initials}</AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center space-x-3">
                  <h1 className="text-2xl font-bold">{companyName}</h1>
                  {company.company_type && (
                    <Badge className="bg-blue-100 text-blue-800">
                      {company.company_type}
                    </Badge>
                  )}
                  {company.overview_v2_status === 'completed' && (
                    <Badge className="bg-green-100 text-green-800">
                      V2 Processed
                    </Badge>
                  )}
                </div>
                <div className="flex items-center mt-1 text-gray-500 space-x-4">
                  <div className="flex items-center">
                    <Building2 className="h-4 w-4 mr-2" />
                    <span>{industry}</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span>{location}</span>
                  </div>
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 mr-2" />
                    <a href={website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      {websiteDomain}
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={handleAddCompany}>
                <Plus className="h-4 w-4 mr-2" />
                Add Company
              </Button>
              {onAddContact && (
                <Button variant="outline" onClick={onAddContact}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Contact
                </Button>
              )}
              <Button>
                <MessageSquare className="h-4 w-4 mr-2" />
                Start Engagement
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" disabled={isDeleting}>
                    {isDeleting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4 mr-2" />
                    )}
                    Delete Company
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete the company 
                      "{companyName}" and remove all associated data including:
                      <ul className="list-disc list-inside mt-2 space-y-1">
                        <li>Company overview and extracted data</li>
                        <li>Investment criteria records</li>
                        <li>All related processing data</li>
                      </ul>
                      <strong className="text-red-600 mt-2 block">
                        Note: Associated contacts will be preserved but their company association will be removed.
                      </strong>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteCompany}
                      className="bg-red-600 hover:bg-red-700"
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Deleting...
                        </>
                      ) : (
                        'Delete Company'
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={isEmbedded ? "" : "px-8 py-6"}>
        <div className="grid grid-cols-3 gap-6">
          <div className="col-span-2">
            <Tabs value={currentTab} onValueChange={handleTabChange}>
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="contacts">Contacts ({contactCount})</TabsTrigger>
                <TabsTrigger value="investment-criteria">Investment Criteria</TabsTrigger>
                <TabsTrigger value="deals">Deals & News</TabsTrigger>
                <TabsTrigger value="insights">AI Insights</TabsTrigger>
                <TabsTrigger value="processing">V2 Processing</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Company Overview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-gray-600">
                        {summary}
                      </p>
                      <div className="grid grid-cols-3 gap-4">
                        {company.aum && (
                          <MetricItem 
                            label="AUM" 
                            value={typeof company.aum === 'number' ? `$${(company.aum / 1000000).toFixed(0)}M` : String(company.aum)} 
                            change="" 
                          />
                        )}
                        {company.founded_year && (
                          <MetricItem 
                            label="Founded" 
                            value={String(company.founded_year)} 
                            change="" 
                          />
                        )}
                        {company.number_of_employees && (
                          <MetricItem 
                            label="Employees" 
                            value={String(company.number_of_employees)} 
                            change="" 
                          />
                        )}
                        {company.fund_size && (
                          <MetricItem 
                            label="Fund Size" 
                            value={typeof company.fund_size === 'number' ? `$${(company.fund_size / 1000000).toFixed(0)}M` : String(company.fund_size)} 
                            change="" 
                          />
                        )}
                        {company.annual_revenue && (
                          <MetricItem 
                            label="Annual Revenue" 
                            value={typeof company.annual_revenue === 'number' ? `$${(company.annual_revenue / 1000000).toFixed(0)}M` : String(company.annual_revenue)} 
                            change="" 
                          />
                        )}
                        {company.number_of_properties && (
                          <MetricItem 
                            label="Properties" 
                            value={String(company.number_of_properties)} 
                            change="" 
                          />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contacts" className="space-y-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>
                      Company Contacts 
                      {contactsStats && (
                        <span className="text-sm font-normal text-gray-500 ml-2">
                          ({contactsStats.total_contacts} total, {contactsStats.verified_emails} verified)
                        </span>
                      )}
                    </CardTitle>
                    <Button size="sm" onClick={onAddContact}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Contact
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {loadingContacts ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        <span>Loading contacts...</span>
                      </div>
                    ) : contacts.length > 0 ? (
                      <>
                        {contactsStats && (
                          <div className="grid grid-cols-4 gap-4 mb-4">
                            <div className="bg-blue-50 p-3 rounded-lg text-center">
                              <div className="text-2xl font-bold text-blue-600">{contactsStats.total_contacts}</div>
                              <div className="text-sm text-blue-700">Total</div>
                            </div>
                            <div className="bg-green-50 p-3 rounded-lg text-center">
                              <div className="text-2xl font-bold text-green-600">{contactsStats.contacts_with_email}</div>
                              <div className="text-sm text-green-700">With Email</div>
                            </div>
                            <div className="bg-purple-50 p-3 rounded-lg text-center">
                              <div className="text-2xl font-bold text-purple-600">{contactsStats.contacts_with_linkedin}</div>
                              <div className="text-sm text-purple-700">LinkedIn</div>
                            </div>
                            <div className="bg-orange-50 p-3 rounded-lg text-center">
                              <div className="text-2xl font-bold text-orange-600">{contactsStats.enriched_contacts}</div>
                              <div className="text-sm text-orange-700">Enriched</div>
                            </div>
                          </div>
                        )}
                        <div className="space-y-3">
                          {contacts.map((contact, index) => (
                            <ContactItem 
                              key={contact.contact_id || index}
                              name={contact.full_name || `${contact.first_name || ""} ${contact.last_name || ""}`.trim()}
                              title={contact.title || contact.headline || ""}
                              email={contact.email || contact.personal_email || ""}
                              phone={contact.phone_number}
                            />
                          ))}
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>No contacts found for this company</p>
                        <p className="text-xs">Use the V2 Web Crawler to discover contacts from company website</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="investment-criteria" className="space-y-6">
                <InvestmentCriteriaSliderV2
                  entityType="company"
                  entityId={company.company_id || 0}
                  entityName={companyName}
                />
              </TabsContent>

              <TabsContent value="deals" className="space-y-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>
                      Deals & Transactions
                      {company.deals_count !== undefined && (
                        <span className="text-sm font-normal text-gray-500 ml-2">
                          ({company.deals_count} deals)
                        </span>
                      )}
                    </CardTitle>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Deal
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {loadingDeals ? (
                        <div className="flex items-center justify-center py-8">
                          <Loader2 className="h-6 w-6 animate-spin mr-2" />
                          <span>Loading deals...</span>
                        </div>
                      ) : (
                        <>
                          {company.transactions_completed_last_12m && company.transactions_completed_last_12m > 0 && (
                            <div className="bg-blue-50 p-4 rounded-lg">
                              <div className="text-sm text-blue-700">
                                <strong>{company.transactions_completed_last_12m}</strong> transactions completed in the last 12 months
                              </div>
                            </div>
                          )}
                          
                          {deals.length > 0 ? (
                            <div className="space-y-3">
                              {deals.map((deal, index) => (
                                <div key={deal.deal_id || index} className="p-4 border rounded-lg">
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <div className="font-medium">{deal.deal_name || deal.property_type}</div>
                                      <div className="text-sm text-gray-500">{deal.location}</div>
                                      {deal.company_role && (
                                        <Badge variant="outline" className="mt-1">
                                          {deal.company_role}
                                        </Badge>
                                      )}
                                    </div>
                                    <div className="text-right">
                                      {deal.deal_size && (
                                        <div className="font-medium text-green-600">
                                          ${(parseFloat(deal.deal_size) / 1000000).toFixed(1)}M
                                        </div>
                                      )}
                                      <div className="text-xs text-gray-500">
                                        {deal.close_date ? new Date(deal.close_date).toLocaleDateString() : deal.status}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="text-center py-8 text-gray-500">
                              <Building2 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                              <p>No deals found for this company</p>
                              <p className="text-xs">Deal data will be populated from our database and news sources</p>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="insights" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>AI Generated Insights</CardTitle>
                    <CardDescription>
                      Insights based on V2 company data and investment criteria
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Investment Focus Insights */}
                    {company.investment_focus && company.investment_focus.length > 0 && (
                      <InsightItem 
                        icon={<Target className="h-4 w-4" />}
                        title="Investment Focus"
                        description={`Focuses on: ${company.investment_focus.join(', ')}`}
                      />
                    )}
                    
                    {/* Strategy Insights */}
                    {company.investment_strategy_mission && (
                      <InsightItem 
                        icon={<Sparkles className="h-4 w-4" />}
                        title="Investment Mission"
                        description={company.investment_strategy_mission}
                      />
                    )}
                    
                    {/* Partnership Insights */}
                    {company.partnerships && company.partnerships.length > 0 && (
                      <InsightItem 
                        icon={<Users className="h-4 w-4" />}
                        title="Key Partnerships"
                        description={`Active partnerships with: ${company.partnerships.slice(0, 3).join(', ')}`}
                      />
                    )}
                    
                    {/* Data Quality Insight */}
                    {company.data_confidence_score && (
                      <InsightItem 
                        icon={<Zap className="h-4 w-4" />}
                        title="Data Quality Score"
                        description={`${(company.data_confidence_score * 100).toFixed(0)}% data completeness and confidence`}
                      />
                    )}
                    
                    {!company.investment_focus && !company.investment_strategy_mission && !company.partnerships && (
                      <div className="text-center py-8 text-gray-500">
                        <Sparkles className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>No AI insights available yet</p>
                        <p className="text-xs">Run V2 Company Overview to generate insights</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="processing" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>V2 Company Processing</CardTitle>
                    <CardDescription>
                      Trigger AI-powered processing to extract and analyze company data
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Processing Status */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-3">Processing Status</h4>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div className="flex items-center justify-between">
                            <span>Website Scraping:</span>
                            <Badge variant={company.website_scraping_status === 'completed' ? 'default' : 'secondary'}>
                              {company.website_scraping_status || 'pending'}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Company Overview:</span>
                            <Badge variant={company.company_overview_status === 'completed' ? 'default' : 'secondary'}>
                              {company.company_overview_status || 'pending'}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Company Overview V2:</span>
                            <Badge variant={company.overview_v2_status === 'completed' ? 'default' : 'secondary'}>
                              {company.overview_v2_status || 'pending'}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Investment Criteria:</span>
                            <Badge variant={company.investment_criteria_status === 'completed' ? 'default' : 'secondary'}>
                              {company.investment_criteria_status || 'pending'}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      {/* Processing Actions */}
                      <div>
                        <h4 className="font-medium mb-3">Processing Actions</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Button 
                            className="w-full justify-start" 
                            variant="outline"
                            onClick={() => handleProcessingTrigger('website_scraping', 'V2 Web Crawler')}
                            disabled={isProcessing && processingStage === 'website_scraping'}
                          >
                            {isProcessing && processingStage === 'website_scraping' ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Search className="h-4 w-4 mr-2" />
                            )}
                            {isProcessing && processingStage === 'website_scraping' ? 'Processing...' : 'Run V2 Web Crawler'}
                          </Button>
                          
                          <Button 
                            className="w-full justify-start" 
                            variant="outline"
                            onClick={() => handleProcessingTrigger('company_overview_v2', 'V2 Company Overview')}
                            disabled={isProcessing && processingStage === 'company_overview_v2'}
                          >
                            {isProcessing && processingStage === 'company_overview_v2' ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Building className="h-4 w-4 mr-2" />
                            )}
                            {isProcessing && processingStage === 'company_overview_v2' ? 'Processing...' : 'Run V2 Company Overview'}
                          </Button>

                          <Button 
                            className="w-full justify-start" 
                            variant="outline"
                            onClick={() => handleProcessingTrigger('company_investment_criteria', 'Investment Criteria')}
                            disabled={isProcessing && processingStage === 'company_investment_criteria'}
                          >
                            {isProcessing && processingStage === 'company_investment_criteria' ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Target className="h-4 w-4 mr-2" />
                            )}
                            {isProcessing && processingStage === 'company_investment_criteria' ? 'Processing...' : 'Extract Investment Criteria'}
                          </Button>

                          <Button 
                            className="w-full justify-start" 
                            variant="outline"
                            onClick={() => handleProcessingTrigger('company_overview', 'Company Overview')}
                            disabled={isProcessing && processingStage === 'company_overview'}
                          >
                            {isProcessing && processingStage === 'company_overview' ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Briefcase className="h-4 w-4 mr-2" />
                            )}
                            {isProcessing && processingStage === 'company_overview' ? 'Processing...' : 'Run Company Overview'}
                          </Button>
                        </div>
                      </div>
                      
                      <div className="text-sm text-gray-600">
                        <p className="mb-2">V2 processing uses enhanced AI models and comprehensive data extraction to provide more detailed company insights.</p>
                        <div className="space-y-1 text-xs">
                          <p><strong>Web Crawler:</strong> Scrapes company website for contact information and company details</p>
                          <p><strong>Company Overview V2:</strong> Enhanced AI analysis of company structure, strategy, and investment criteria</p>
                          <p><strong>Investment Criteria:</strong> Extracts detailed investment parameters and deal preferences</p>
                          <p><strong>Company Overview:</strong> Standard company analysis and profile extraction</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card className="bg-gradient-to-br from-blue-50 to-white border-blue-100">
              <CardHeader>
                <CardTitle>AI Research Agent</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button className="w-full justify-start" variant="ghost">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Research recent deals
                  </Button>
                  <Button className="w-full justify-start" variant="ghost">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Find key contacts
                  </Button>
                  <Button className="w-full justify-start" variant="ghost">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Analyze strategy
                  </Button>
                </div>
              </CardContent>
            </Card>


          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyView;