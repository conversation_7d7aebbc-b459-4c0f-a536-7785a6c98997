"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Users, TrendingUp, Home } from "lucide-react";
import MetricsManagement from "@/components/dashboard/homepage/MetricsManagement";
import InteractionsManagement from "@/components/dashboard/homepage/InteractionsManagement";
import DealsManagement from "@/components/dashboard/homepage/DealsManagement";

export default function HomepageView() {
  const [activeTab, setActiveTab] = useState("metrics");

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <Home className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Homepage Management</h1>
          <p className="text-gray-600">Manage homepage metrics, user interactions, and featured deals</p>
        </div>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">Dashboard Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="metrics" className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Metrics
              </TabsTrigger>
              <TabsTrigger value="interactions" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                User Interactions
              </TabsTrigger>
              <TabsTrigger value="deals" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Deals Management
              </TabsTrigger>
            </TabsList>

            <TabsContent value="metrics" className="mt-6">
              <MetricsManagement />
            </TabsContent>

            <TabsContent value="interactions" className="mt-6">
              <InteractionsManagement />
            </TabsContent>

            <TabsContent value="deals" className="mt-6">
              <DealsManagement />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
