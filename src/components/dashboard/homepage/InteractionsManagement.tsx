"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RefreshCw, Users, CheckCircle, XCircle, AlertCircle, ExternalLink, Phone, Mail, Linkedin, ChevronDown, ChevronRight, MessageSquare, Building, MapPin, Globe, Eye, HandHeart } from "lucide-react";

interface Interaction {
  id: number;
  username?: string;
  email: string;
  linkedin?: string;
  phonenumber?: string;
  status?: string;
  created_at: string;
  updated_at: string;
  // Additional fields from the API
  first_name?: string;
  last_name?: string;
  job_title?: string;
  company?: string;
  company_website?: string;
  message?: string;
  utm_campaign?: string;
  utm_medium?: string;
  utm_term?: string;
  utm_content?: string;
  utm_adgroup_id?: string;
  utm_adgroup?: string;
  utm_placement?: string;
  utm?: Record<string, string>; // New UTM JSON field
  ip_address?: string;
  country?: string;
  region?: string;
  city?: string;
  gclid?: string;
  fbclid?: string;
  slug?: string;
  recaptcha_token?: string;
  recaptcha_verified?: boolean;
}

type InteractionStatus = 'pending' | 'valid' | 'invalid' | 'pending_deal';

export default function InteractionsManagement() {
  const router = useRouter();
  const [interactions, setInteractions] = useState<Interaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState<number | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dealRequestFilter, setDealRequestFilter] = useState<string>('all');
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  const fetchInteractions = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/homepage/interaction');
      const data = await response.json();
      
      if (data.success) {
        setInteractions(data.data || []);
      } else {
        setMessage({ type: 'error', text: 'Failed to fetch interactions' });
      }
    } catch (error) {
      console.error('Error fetching interactions:', error);
      setMessage({ type: 'error', text: 'Failed to fetch interactions' });
    } finally {
      setLoading(false);
    }
  };

  const updateInteractionStatus = async (id: number, newStatus: InteractionStatus) => {
    setUpdating(id);
    try {
      const response = await fetch(`/api/homepage/interaction/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });
      
      if (response.ok) {
        // Update the local state
        setInteractions(prev => 
          prev.map(interaction => 
            interaction.id === id 
              ? { ...interaction, status: newStatus, updated_at: new Date().toISOString() }
              : interaction
          )
        );
        setMessage({ type: 'success', text: `Contact marked as ${newStatus}` });
        // Auto-hide success message after 3 seconds
        setTimeout(() => setMessage(null), 3000);
      } else {
        setMessage({ type: 'error', text: 'Failed to update status' });
      }
    } catch (error) {
      console.error('Error updating interaction status:', error);
      setMessage({ type: 'error', text: 'Failed to update status' });
    } finally {
      setUpdating(null);
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'valid':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Valid</Badge>;
      case 'invalid':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Invalid</Badge>;
      case 'pending_deal':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Pending Deal</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'valid':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'invalid':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending_deal':
        return <HandHeart className="h-4 w-4 text-blue-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
    }
  };

  const hasDealRequest = (interaction: Interaction) => {
    // Check if utm_deal_requested is true in the JSON field
    if (interaction.utm && interaction.utm.deal_requested === 'true') {
      return true;
    }
    // Fallback: check individual UTM columns for backward compatibility
    return false;
  };

  const getDealRequestId = (interaction: Interaction) => {
    // Get deal request ID from JSON field
    if (interaction.utm && interaction.utm.deal_requested_id) {
      return interaction.utm.deal_requested_id;
    }
    return null;
  };

  const setDealRequestStatus = async (interaction: Interaction) => {
    if (hasDealRequest(interaction) && interaction.status !== 'pending_deal') {
      await updateInteractionStatus(interaction.id, 'pending_deal');
    }
  };

  const sendWelcomeEmail = async (interaction: Interaction, immediate = false) => {
    try {
      const response = await fetch(`/api/homepage/interaction/${interaction.id}/send-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: `Welcome email sent via Gmail API to ${interaction.email}${result.messageId ? ` (Message ID: ${result.messageId})` : ''}` 
        });
        setTimeout(() => setMessage(null), 5000);
      } else {
        setMessage({ 
          type: 'error', 
          text: `Failed to send email via Gmail API: ${result.error}` 
        });
        setTimeout(() => setMessage(null), 5000);
      }
    } catch (error) {
      console.error('Error sending welcome email via Gmail API:', error);
      setMessage({ 
        type: 'error', 
        text: 'Failed to send welcome email via Gmail API' 
      });
      setTimeout(() => setMessage(null), 5000);
    }
  };

  const navigateToDeal = (dealId: string) => {
    router.push(`/dashboard/deals/v2/${dealId}`);
  };

  const filteredInteractions = interactions.filter(interaction => {
    const statusMatch = statusFilter === 'all' || (interaction.status || 'pending') === statusFilter;
    const dealRequestMatch = dealRequestFilter === 'all' || 
      (dealRequestFilter === 'requested' && hasDealRequest(interaction)) ||
      (dealRequestFilter === 'not_requested' && !hasDealRequest(interaction));
    return statusMatch && dealRequestMatch;
  });

  const getStatusCounts = () => {
    const counts = { pending: 0, pending_deal: 0, valid: 0, invalid: 0, total: interactions.length, dealRequests: 0 };
    interactions.forEach(interaction => {
      const status = interaction.status || 'pending';
      counts[status as keyof typeof counts]++;
      if (hasDealRequest(interaction)) {
        counts.dealRequests++;
      }
    });
    return counts;
  };

  const statusCounts = getStatusCounts();

  const toggleRowExpansion = (id: number) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const getDisplayName = (interaction: Interaction) => {
    if (interaction.first_name && interaction.last_name) {
      return `${interaction.first_name} ${interaction.last_name}`;
    }
    if (interaction.username) {
      return interaction.username;
    }
    return 'Unknown';
  };

  useEffect(() => {
    fetchInteractions();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header with stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="h-5 w-5 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">User Interactions</h2>
            <p className="text-sm text-gray-600">
              Manage and review contact form submissions from homepage
            </p>
          </div>
        </div>
        <Button onClick={fetchInteractions} disabled={loading} variant="outline" size="sm">
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{statusCounts.total}</div>
            <div className="text-sm text-gray-500">Total Contacts</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{statusCounts.pending}</div>
            <div className="text-sm text-gray-500">Pending Review</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{statusCounts.valid}</div>
            <div className="text-sm text-gray-500">Valid Leads</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{statusCounts.invalid}</div>
            <div className="text-sm text-gray-500">Invalid/Spam</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 flex items-center justify-center gap-1">
              <HandHeart className="h-5 w-5" />
              {statusCounts.dealRequests}
            </div>
            <div className="text-sm text-gray-500">Deal Requests</div>
          </CardContent>
        </Card>
      </div>

      {/* Alert messages */}
      {message && (
        <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Filters and Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Contact Submissions ({filteredInteractions.length})
            </CardTitle>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Status:</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All ({statusCounts.total})</SelectItem>
                    <SelectItem value="pending">Pending ({statusCounts.pending})</SelectItem>
                    <SelectItem value="pending_deal">Pending Deal ({statusCounts.pending_deal})</SelectItem>
                    <SelectItem value="valid">Valid ({statusCounts.valid})</SelectItem>
                    <SelectItem value="invalid">Invalid ({statusCounts.invalid})</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Deal Request:</label>
                <Select value={dealRequestFilter} onValueChange={setDealRequestFilter}>
                  <SelectTrigger className="w-36">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="requested">Deal Requested</SelectItem>
                    <SelectItem value="not_requested">No Deal Request</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
              <span className="ml-3 text-gray-600">Loading interactions...</span>
            </div>
          ) : filteredInteractions.length === 0 ? (
            <div className="text-center p-8 text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No interactions found</p>
              {statusFilter !== 'all' && (
                <Button 
                  variant="outline" 
                  onClick={() => setStatusFilter('all')} 
                  className="mt-2"
                >
                  Show All Interactions
                </Button>
              )}
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-8"></TableHead>
                    <TableHead className="w-12">Status</TableHead>
                    <TableHead className="w-20">Deal Request</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInteractions.map((interaction) => {
                    const isExpanded = expandedRows.has(interaction.id);
                    return (
                      <React.Fragment key={interaction.id}>
                        <TableRow>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleRowExpansion(interaction.id)}
                              className="h-6 w-6 p-0"
                            >
                              {isExpanded ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </Button>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getStatusIcon(interaction.status)}
                              {getStatusBadge(interaction.status)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {hasDealRequest(interaction) ? (
                              <div className="flex items-center gap-1">
                                <HandHeart className="h-4 w-4 text-green-600" />
                                <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">
                                  Deal Requested
                                </Badge>
                                {getDealRequestId(interaction) && (
                                  <Button
                                    variant="link"
                                    size="sm"
                                    onClick={() => navigateToDeal(getDealRequestId(interaction)!)}
                                    className="text-xs h-4 px-1 text-blue-600 hover:text-blue-800"
                                  >
                                    #{getDealRequestId(interaction)}
                                  </Button>
                                )}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-sm">-</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="font-medium text-gray-900">
                              {getDisplayName(interaction)}
                            </div>
                            {interaction.job_title && (
                              <div className="text-sm text-gray-500">{interaction.job_title}</div>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1 text-sm text-blue-600">
                              <Mail className="h-3 w-3" />
                              <a href={`mailto:${interaction.email}`} className="hover:underline">
                                {interaction.email}
                              </a>
                            </div>
                            {interaction.phonenumber && (
                              <div className="flex items-center gap-1 text-sm text-gray-600 mt-1">
                                <Phone className="h-3 w-3" />
                                <a href={`tel:${interaction.phonenumber}`} className="hover:underline">
                                  {interaction.phonenumber}
                                </a>
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {interaction.company || 'N/A'}
                            </div>
                            {interaction.company_website && (
                              <div className="flex items-center gap-1 text-xs text-blue-600 mt-1">
                                <Globe className="h-3 w-3" />
                                <a 
                                  href={interaction.company_website} 
                                  target="_blank" 
                                  rel="noopener noreferrer" 
                                  className="hover:underline"
                                >
                                  Website
                                </a>
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {interaction.message ? (
                              <div className="max-w-xs">
                                <div className="text-sm text-gray-700 line-clamp-2">
                                  {interaction.message}
                                </div>
                              </div>
                            ) : (
                              <span className="text-gray-400 text-sm">No message</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-gray-600">
                              {interaction.city && interaction.region && interaction.country ? (
                                <div className="flex items-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  {interaction.city}, {interaction.region}, {interaction.country}
                                </div>
                              ) : (
                                <span className="text-gray-400">Unknown</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-gray-500">
                              {new Date(interaction.created_at).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Select
                                value={interaction.status || 'pending'}
                                onValueChange={(value) => updateInteractionStatus(interaction.id, value as InteractionStatus)}
                                disabled={updating === interaction.id}
                              >
                                <SelectTrigger className="w-24 h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="pending">Pending</SelectItem>
                                  <SelectItem value="pending_deal">Pending Deal</SelectItem>
                                  <SelectItem value="valid">Valid</SelectItem>
                                  <SelectItem value="invalid">Invalid</SelectItem>
                                </SelectContent>
                              </Select>
                              {updating === interaction.id && (
                                <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                        {isExpanded && (
                          <TableRow>
                            <TableCell colSpan={10} className="bg-gray-50 p-4">
                              <div className="space-y-4">
                                <h4 className="font-medium text-gray-900 flex items-center gap-2">
                                  <Eye className="h-4 w-4" />
                                  Full Details
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                  {/* Personal Information */}
                                  <div className="space-y-2">
                                    <h5 className="font-medium text-gray-700">Personal Information</h5>
                                    <div className="space-y-1 text-sm">
                                      {interaction.first_name && (
                                        <div><span className="font-medium">First Name:</span> {interaction.first_name}</div>
                                      )}
                                      {interaction.last_name && (
                                        <div><span className="font-medium">Last Name:</span> {interaction.last_name}</div>
                                      )}
                                      {interaction.job_title && (
                                        <div><span className="font-medium">Job Title:</span> {interaction.job_title}</div>
                                      )}
                                      {interaction.linkedin && (
                                        <div className="flex items-center gap-1">
                                          <span className="font-medium">LinkedIn:</span>
                                          <a 
                                            href={interaction.linkedin} 
                                            target="_blank" 
                                            rel="noopener noreferrer" 
                                            className="text-blue-600 hover:underline flex items-center gap-1"
                                          >
                                            Profile <ExternalLink className="h-3 w-3" />
                                          </a>
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Company Information */}
                                  <div className="space-y-2">
                                    <h5 className="font-medium text-gray-700">Company Information</h5>
                                    <div className="space-y-1 text-sm">
                                      {interaction.company && (
                                        <div><span className="font-medium">Company:</span> {interaction.company}</div>
                                      )}
                                      {interaction.company_website && (
                                        <div className="flex items-center gap-1">
                                          <span className="font-medium">Website:</span>
                                          <a 
                                            href={interaction.company_website} 
                                            target="_blank" 
                                            rel="noopener noreferrer" 
                                            className="text-blue-600 hover:underline flex items-center gap-1"
                                          >
                                            Visit <ExternalLink className="h-3 w-3" />
                                          </a>
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Message */}
                                  {interaction.message && (
                                    <div className="space-y-2">
                                      <h5 className="font-medium text-gray-700">Message</h5>
                                      <div className="text-sm text-gray-600 bg-white p-3 rounded border">
                                        {interaction.message}
                                      </div>
                                    </div>
                                  )}

                                  {/* Deal Request Details */}
                                  {hasDealRequest(interaction) && (
                                    <div className="space-y-2">
                                      <h5 className="font-medium text-gray-700 flex items-center gap-2">
                                        <HandHeart className="h-4 w-4 text-green-600" />
                                        Deal Request Details
                                      </h5>
                                      <div className="space-y-2 text-sm bg-green-50 p-3 rounded border border-green-200">
                                        <div className="flex items-center justify-between">
                                          <Badge className="bg-green-100 text-green-800 border-green-200">
                                            Deal Requested
                                          </Badge>
                                          {interaction.status !== 'pending_deal' && (
                                            <Button
                                              size="sm"
                                              variant="outline"
                                              onClick={() => setDealRequestStatus(interaction)}
                                              className="text-xs h-6 px-2"
                                            >
                                              Set as Pending Deal
                                            </Button>
                                          )}
                                        </div>
                                        {getDealRequestId(interaction) && (
                                          <div className="flex items-center gap-2">
                                            <span className="font-medium">Deal Request ID:</span> 
                                            <Button
                                              variant="link"
                                              size="sm"
                                              onClick={() => navigateToDeal(getDealRequestId(interaction)!)}
                                              className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                                            >
                                              #{getDealRequestId(interaction)}
                                            </Button>
                                          </div>
                                        )}
                                        {interaction.status === 'pending_deal' && (
                                          <div className="text-blue-600 text-xs font-medium">
                                            ✓ Status set to "Pending Deal"
                                          </div>
                                        )}
                                        <div className="flex gap-2 mt-2">
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => sendWelcomeEmail(interaction, false)}
                                            className="text-xs h-6 px-2"
                                          >
                                            Send via Gmail API
                                          </Button>
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => sendWelcomeEmail(interaction, true)}
                                            className="text-xs h-6 px-2"
                                          >
                                            Send via Gmail API
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  )}

                                  {/* UTM Tracking - Always show for debugging */}
                                  <div className="space-y-2">
                                    <h5 className="font-medium text-gray-700">Campaign Tracking</h5>
                                    <div className="space-y-1 text-sm bg-blue-50 p-3 rounded border border-blue-200">
                                      {/* Debug: Show if UTM data exists */}
                                      <div className="text-xs text-gray-500 mb-2">
                                        UTM Data Available: {interaction.utm ? 'Yes (JSON)' : 'No'} | 
                                        Individual UTM: {interaction.utm_campaign || interaction.utm_medium || interaction.utm_term ? 'Yes' : 'No'}
                                      </div>
                                      
                                      {/* Display UTM parameters from JSON field (new format) */}
                                      {interaction.utm && Object.keys(interaction.utm).length > 0 ? (
                                        Object.entries(interaction.utm)
                                          .map(([key, value]) => (
                                            <div key={key} className="flex justify-between">
                                              <span className="font-medium capitalize">{key.replace('_', ' ')}:</span> 
                                              <span className="text-gray-700">{value}</span>
                                            </div>
                                          ))
                                      ) : (
                                        /* Fallback to individual columns for backward compatibility */
                                        <>
                                          {interaction.utm_campaign && (
                                            <div className="flex justify-between">
                                              <span className="font-medium">Campaign:</span> 
                                              <span className="text-gray-700">{interaction.utm_campaign}</span>
                                            </div>
                                          )}
                                          {interaction.utm_medium && (
                                            <div className="flex justify-between">
                                              <span className="font-medium">Medium:</span> 
                                              <span className="text-gray-700">{interaction.utm_medium}</span>
                                            </div>
                                          )}
                                          {interaction.utm_term && (
                                            <div className="flex justify-between">
                                              <span className="font-medium">Term:</span> 
                                              <span className="text-gray-700">{interaction.utm_term}</span>
                                            </div>
                                          )}
                                          {interaction.utm_content && (
                                            <div className="flex justify-between">
                                              <span className="font-medium">Content:</span> 
                                              <span className="text-gray-700">{interaction.utm_content}</span>
                                            </div>
                                          )}
                                          {interaction.utm_adgroup_id && (
                                            <div className="flex justify-between">
                                              <span className="font-medium">Ad Group ID:</span> 
                                              <span className="text-gray-700">{interaction.utm_adgroup_id}</span>
                                            </div>
                                          )}
                                          {interaction.utm_adgroup && (
                                            <div className="flex justify-between">
                                              <span className="font-medium">Ad Group:</span> 
                                              <span className="text-gray-700">{interaction.utm_adgroup}</span>
                                            </div>
                                          )}
                                          {interaction.utm_placement && (
                                            <div className="flex justify-between">
                                              <span className="font-medium">Placement:</span> 
                                              <span className="text-gray-700">{interaction.utm_placement}</span>
                                            </div>
                                          )}
                                        </>
                                      )}
                                      {interaction.gclid && (
                                        <div className="flex justify-between">
                                          <span className="font-medium">Google Click ID:</span> 
                                          <span className="text-gray-700">{interaction.gclid}</span>
                                        </div>
                                      )}
                                      {interaction.fbclid && (
                                        <div className="flex justify-between">
                                          <span className="font-medium">Facebook Click ID:</span> 
                                          <span className="text-gray-700">{interaction.fbclid}</span>
                                        </div>
                                      )}
                                      
                                      {/* Show if no UTM data at all */}
                                      {!interaction.utm && !interaction.utm_campaign && !interaction.utm_medium && !interaction.utm_term && !interaction.gclid && !interaction.fbclid && (
                                        <div className="text-gray-500 italic">No UTM tracking data available</div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Technical Details */}
                                  <div className="space-y-2">
                                    <h5 className="font-medium text-gray-700">Technical Details</h5>
                                    <div className="space-y-1 text-sm">
                                      {interaction.ip_address && (
                                        <div><span className="font-medium">IP Address:</span> {interaction.ip_address}</div>
                                      )}
                                      {interaction.slug && (
                                        <div><span className="font-medium">Page:</span> {interaction.slug}</div>
                                      )}
                                      {interaction.recaptcha_verified !== undefined && (
                                        <div>
                                          <span className="font-medium">reCAPTCHA:</span> 
                                          <Badge className={`ml-2 ${interaction.recaptcha_verified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                            {interaction.recaptcha_verified ? 'Verified' : 'Not Verified'}
                                          </Badge>
                                        </div>
                                      )}
                                      <div><span className="font-medium">Updated:</span> {new Date(interaction.updated_at).toLocaleString()}</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </React.Fragment>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
