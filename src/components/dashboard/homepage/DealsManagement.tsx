"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RefreshCw, ExternalLink, Building, DollarSign, MapPin, Calendar, CheckCircle, AlertCircle } from "lucide-react";

interface HomepageDeal {
  id: number;
  position: string;
  financing_type: string;
  amount_required: string;
  ltv?: string;
  property_type?: string;
  location?: string;
  description?: string;
  image_url?: string;
  external_link?: string;
  created_at: string;
  updated_at: string;
}


export default function DealsManagement() {
  const router = useRouter();
  const [homepageDeals, setHomepageDeals] = useState<HomepageDeal[]>([]);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const fetchHomepageDeals = async () => {
    try {
      const response = await fetch('/api/homepage/deals');
      const data = await response.json();
      
      if (data.success) {
        setHomepageDeals(data.data || []);
      } else {
        setMessage({ type: 'error', text: 'Failed to fetch homepage deals' });
      }
    } catch (error) {
      console.error('Error fetching homepage deals:', error);
    }
  };

  const handleDealClick = (deal: HomepageDeal) => {
    // Navigate to the v2 deal detail page
    // The deal.id should correspond to the deal_id in dealsv2 table
    router.push(`/dashboard/deals/v2/${deal.id}`);
  };








  useEffect(() => {
    fetchHomepageDeals();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Building className="h-5 w-5 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Deals Management</h2>
            <p className="text-sm text-gray-600">
              View and manage published deals displayed on homepage
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={fetchHomepageDeals} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Alert messages */}
      {message && (
        <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs defaultValue="published-deals" className="w-full">
        <TabsList>
          <TabsTrigger value="published-deals">Published Deals ({homepageDeals.length})</TabsTrigger>
        </TabsList>

        {/* Published Deals */}
        <TabsContent value="published-deals">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Published Deals on Homepage
              </CardTitle>
              <p className="text-sm text-gray-600">
                These deals are currently published and visible on the homepage. Maximum 10 deals can be published at once.
              </p>
            </CardHeader>
            <CardContent>
              {homepageDeals.length === 0 ? (
                <div className="text-center p-8 text-gray-500">
                  <Building className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No deals currently published on homepage</p>
                  <p className="text-sm mt-2">Go to the Deals section to publish deals to the homepage</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {homepageDeals.map((deal) => (
                    <div 
                      key={deal.id} 
                      className="border rounded-lg p-4 cursor-pointer hover:bg-gray-50 hover:border-gray-300 transition-colors"
                      onClick={() => handleDealClick(deal)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium text-lg">{deal.position}</h4>
                            <Badge variant="secondary">{deal.financing_type}</Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              {deal.amount_required}
                            </span>
                            {deal.ltv && (
                              <span>LTV: {deal.ltv}</span>
                            )}
                            {deal.property_type && (
                              <Badge variant="outline">{deal.property_type}</Badge>
                            )}
                            {deal.location && (
                              <span className="flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                {deal.location}
                              </span>
                            )}
                          </div>
                          {deal.description && (
                            <p className="text-sm text-gray-700 mt-2">{deal.description}</p>
                          )}
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              Created: {new Date(deal.created_at).toLocaleDateString()}
                            </span>
                            {deal.external_link && (
                              <a
                                href={deal.external_link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1 text-blue-600 hover:underline"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <ExternalLink className="h-3 w-3" />
                                View External Link
                              </a>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          <span className="text-sm text-gray-500 italic">
                            Published deals are managed from the main deals page
                          </span>
                          <span className="text-xs text-blue-600 font-medium">
                            Click to view details →
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>

    </div>
  );
}
