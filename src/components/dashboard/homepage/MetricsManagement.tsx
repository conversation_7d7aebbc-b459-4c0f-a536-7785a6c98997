"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { RefreshCw, Save, TrendingUp, AlertCircle, CheckCircle } from "lucide-react";

interface Metrics {
  sofr?: number;
  sofr_30_day_avg?: number;
  wsj_prime_rate?: number;
  treasury_5_year?: number;
  treasury_10_year?: number;
  effective_date: string;
  created_at?: string;
}

export default function MetricsManagement() {
  const [metrics, setMetrics] = useState<Metrics | null>(null);
  const [formData, setFormData] = useState<Metrics>({
    sofr: 0,
    sofr_30_day_avg: 0,
    wsj_prime_rate: 0,
    treasury_5_year: 0,
    treasury_10_year: 0,
    effective_date: new Date().toISOString().split('T')[0]
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const fetchMetrics = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/homepage/metrics');
      const data = await response.json();
      
      if (data.success && data.data) {
        setMetrics(data.data);
        setFormData({
          sofr: data.data.sofr || 0,
          sofr_30_day_avg: data.data.sofr_30_day_avg || 0,
          wsj_prime_rate: data.data.wsj_prime_rate || 0,
          treasury_5_year: data.data.treasury_5_year || 0,
          treasury_10_year: data.data.treasury_10_year || 0,
          effective_date: data.data.effective_date || new Date().toISOString().split('T')[0]
        });
      }
    } catch (error) {
      console.error('Error fetching metrics:', error);
      setMessage({ type: 'error', text: 'Failed to fetch metrics data' });
    } finally {
      setLoading(false);
    }
  };

  const saveMetrics = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/homepage/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMetrics(data.data);
        setMessage({ type: 'success', text: 'Metrics updated successfully' });
        // Auto-hide success message after 3 seconds
        setTimeout(() => setMessage(null), 3000);
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to save metrics' });
      }
    } catch (error) {
      console.error('Error saving metrics:', error);
      setMessage({ type: 'error', text: 'Failed to save metrics' });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof Metrics, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'effective_date' ? value : parseFloat(value as string) || 0
    }));
  };

  useEffect(() => {
    fetchMetrics();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header with current metrics status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Market Metrics</h2>
            <p className="text-sm text-gray-600">
              Manage interest rates and financial metrics displayed on homepage
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {metrics && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Last updated: {new Date(metrics.created_at || '').toLocaleDateString()}
            </Badge>
          )}
          <Button onClick={fetchMetrics} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Alert messages */}
      {message && (
        <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Metrics Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Interest Rate Metrics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* SOFR */}
            <div className="space-y-2">
              <Label htmlFor="sofr">SOFR (%)</Label>
              <Input
                id="sofr"
                type="number"
                step="0.001"
                value={formData.sofr}
                onChange={(e) => handleInputChange('sofr', e.target.value)}
                placeholder="4.360"
                className="font-mono"
              />
              <p className="text-xs text-gray-500">Secured Overnight Financing Rate</p>
            </div>

            {/* SOFR 30-Day Average */}
            <div className="space-y-2">
              <Label htmlFor="sofr_30_day_avg">SOFR 30-Day Avg (%)</Label>
              <Input
                id="sofr_30_day_avg"
                type="number"
                step="0.001"
                value={formData.sofr_30_day_avg}
                onChange={(e) => handleInputChange('sofr_30_day_avg', e.target.value)}
                placeholder="4.345"
                className="font-mono"
              />
              <p className="text-xs text-gray-500">30-day average of SOFR</p>
            </div>

            {/* WSJ Prime Rate */}
            <div className="space-y-2">
              <Label htmlFor="wsj_prime_rate">WSJ Prime Rate (%)</Label>
              <Input
                id="wsj_prime_rate"
                type="number"
                step="0.001"
                value={formData.wsj_prime_rate}
                onChange={(e) => handleInputChange('wsj_prime_rate', e.target.value)}
                placeholder="7.500"
                className="font-mono"
              />
              <p className="text-xs text-gray-500">Wall Street Journal Prime Rate</p>
            </div>

            {/* 5-Year Treasury */}
            <div className="space-y-2">
              <Label htmlFor="treasury_5_year">5-Year Treasury (%)</Label>
              <Input
                id="treasury_5_year"
                type="number"
                step="0.001"
                value={formData.treasury_5_year}
                onChange={(e) => handleInputChange('treasury_5_year', e.target.value)}
                placeholder="3.920"
                className="font-mono"
              />
              <p className="text-xs text-gray-500">5-Year U.S. Treasury Yield</p>
            </div>

            {/* 10-Year Treasury */}
            <div className="space-y-2">
              <Label htmlFor="treasury_10_year">10-Year Treasury (%)</Label>
              <Input
                id="treasury_10_year"
                type="number"
                step="0.001"
                value={formData.treasury_10_year}
                onChange={(e) => handleInputChange('treasury_10_year', e.target.value)}
                placeholder="4.346"
                className="font-mono"
              />
              <p className="text-xs text-gray-500">10-Year U.S. Treasury Yield</p>
            </div>

            {/* Effective date is handled automatically in the backend */}
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={saveMetrics} disabled={saving} className="min-w-32">
              {saving ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Metrics
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current Values Display */}
      {metrics && (
        <Card className="bg-gray-50">
          <CardHeader>
            <CardTitle className="text-lg">Current Live Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 text-center">
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-blue-600">{metrics.sofr ? Number(metrics.sofr).toFixed(3) : '0.000'}%</div>
                <div className="text-xs text-gray-500 mt-1">SOFR</div>
              </div>
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-blue-600">{metrics.sofr_30_day_avg ? Number(metrics.sofr_30_day_avg).toFixed(3) : '0.000'}%</div>
                <div className="text-xs text-gray-500 mt-1">SOFR 30-Day</div>
              </div>
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-green-600">{metrics.wsj_prime_rate ? Number(metrics.wsj_prime_rate).toFixed(3) : '0.000'}%</div>
                <div className="text-xs text-gray-500 mt-1">WSJ Prime</div>
              </div>
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-orange-600">{metrics.treasury_5_year ? Number(metrics.treasury_5_year).toFixed(3) : '0.000'}%</div>
                <div className="text-xs text-gray-500 mt-1">5Y Treasury</div>
              </div>
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-purple-600">{metrics.treasury_10_year ? Number(metrics.treasury_10_year).toFixed(3) : '0.000'}%</div>
                <div className="text-xs text-gray-500 mt-1">10Y Treasury</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
