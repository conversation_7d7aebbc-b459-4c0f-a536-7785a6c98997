/**
 * Phone number utility functions for formatting and validation
 */

/**
 * Formats a phone number to a standardized display format
 * Handles multiple input formats:
 * - 10-digit: 4155550199 -> (*************
 * - With dashes: ************ -> (*************
 * - With parentheses: (************* -> (*************
 * - With country code: 1-************ -> +1 (*************
 * - E.164 format: +14155550199 -> +1 (*************
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber) return '';
  
  // Remove all non-digit characters except + at the beginning
  const cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  // If it starts with +1, handle E.164 format
  if (cleaned.startsWith('+1') && cleaned.length === 12) {
    const digits = cleaned.slice(2); // Remove +1
    return `+1 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  }
  
  // If it starts with 1 and is 11 digits, handle US format with country code
  if (cleaned.startsWith('1') && cleaned.length === 11) {
    const digits = cleaned.slice(1); // Remove leading 1
    return `+1 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  }
  
  // If it's a 10-digit number, format as (XXX) XXX-XXXX
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  // For other formats, try to detect and format accordingly
  // Handle cases like "************" or "(*************"
  if (cleaned.length >= 10) {
    // Take the last 10 digits if it's longer
    const last10Digits = cleaned.slice(-10);
    return `(${last10Digits.slice(0, 3)}) ${last10Digits.slice(3, 6)}-${last10Digits.slice(6)}`;
  }
  
  // For other lengths, return the cleaned version (let validation handle it)
  return cleaned;
};

/**
 * Validates a phone number against multiple accepted formats
 * @param value - The phone number string to validate
 * @returns true if valid, false otherwise
 */
export const validatePhoneNumber = (value: string): boolean => {
  if (!value || value.trim() === '') return true; // Allow empty values
  
  // Remove all non-digit characters except + at the beginning
  const cleaned = value.replace(/[^\d+]/g, '');
  
  // Check various phone number formats
  const patterns = [
    /^\+1\d{10}$/,           // +14155550199 (E.164 format)
    /^1\d{10}$/,             // 14155550199 (US format with country code)
    /^\d{10}$/,              // 4155550199 (10-digit US format)
  ];
  
  return patterns.some(pattern => pattern.test(cleaned));
};

/**
 * Converts a phone number to E.164 format for API storage
 * @param phoneNumber - The phone number to convert
 * @returns E.164 formatted phone number or null if invalid
 */
export const toE164Format = (phoneNumber: string): string | null => {
  if (!phoneNumber) return null;
  
  // Remove all non-digit characters except + at the beginning
  const cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  // If it's already in E.164 format
  if (cleaned.startsWith('+1') && cleaned.length === 12) {
    return cleaned;
  }
  
  // If it starts with 1 and is 11 digits, add + prefix
  if (cleaned.startsWith('1') && cleaned.length === 11) {
    return `+${cleaned}`;
  }
  
  // If it's a 10-digit number, add +1 prefix
  if (cleaned.length === 10) {
    return `+1${cleaned}`;
  }
  
  // For other lengths, try to extract 10 digits and add +1
  if (cleaned.length >= 10) {
    const last10Digits = cleaned.slice(-10);
    return `+1${last10Digits}`;
  }
  
  return null;
};

/**
 * Gets the error message for phone number validation
 */
export const getPhoneValidationMessage = (): string => {
  return 'Please enter a valid phone number (formats: ************, (*************, ******-555-0199, +14155550199)';
};
