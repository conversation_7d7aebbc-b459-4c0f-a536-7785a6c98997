import { ContactFormData, CompanySuggestion } from '../shared/types';
import { toast } from 'sonner';

export interface ContactSubmissionData {
  first_name: string;
  last_name: string;
  job_title: string;
  job_tier: string;
  email?: string;
  personal_email: string;
  phone_number: string;
  phone_number_secondary: string;
  linkedin_url: string;
  contact_address: string;
  contact_city: string;
  contact_state: string;
  contact_zip_code: string;
  contact_country: string;
  last_contact_date: string;
  source_of_introduction: string;
  company_id?: number;
  // Company information fields
  company_name?: string;
  company_website?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  company_zip?: string;
}

export class ContactSubmissionService {
  static async saveContact(
    formData: ContactFormData,
    companyId?: string,
    selectedCompany?: CompanySuggestion | null,
    investmentCriteria?: any[]
  ): Promise<{ success: boolean; contact_id?: number; error?: string }> {
    try {
      const contactData: ContactSubmissionData = {
        // Map form data to database schema
        first_name: formData.first_name,
        last_name: formData.last_name,
        job_title: formData.job_title,
        job_tier: formData.job_tier,
        email: formData.email,
        personal_email: formData.personal_email,
        phone_number: formData.phone_number,
        phone_number_secondary: formData.phone_number_secondary,
        linkedin_url: formData.linkedin_url,
        contact_address: formData.contact_address,
        contact_city: formData.contact_city,
        contact_state: formData.contact_state,
        contact_zip_code: formData.contact_zip_code,
        contact_country: formData.contact_country,
        last_contact_date: formData.last_contact_date,
        source_of_introduction: formData.source_of_introduction,
        // Company information - include all fields the API expects
        company_id: companyId ? parseInt(companyId) : selectedCompany?.company_id,
        company_name: formData.company_name,
        company_website: formData.company_website,
        industry: formData.industry,
        company_address: formData.company_address,
        company_city: formData.company_city,
        company_state: formData.company_state,
        company_country: formData.company_country,
        company_zip: formData.company_zip,
      };

      const response = await fetch('/api/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API Error:', errorData);
        throw new Error(errorData.error || 'Failed to add contact');
      }
      
      const data = await response.json();
      console.log('Contact saved successfully:', data);

      // Save investment criteria if provided
      if (investmentCriteria && investmentCriteria.length > 0 && data.contact_id) {
        try {
          await this.saveInvestmentCriteria(data.contact_id, investmentCriteria);
          console.log('Investment criteria saved successfully');
        } catch (icError) {
          console.error('Error saving investment criteria:', icError);
          // Don't fail the entire contact creation if IC saving fails
          toast.error('Contact created but failed to save investment criteria');
        }
      }

      toast.success('Contact added successfully', {
        description: `${formData.first_name} ${formData.last_name} has been added to your contacts`,
        duration: 4000,
      });

      return { success: true, contact_id: data.contact_id };
    } catch (error) {
      console.error('Error saving contact:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to add contact';
      toast.error('Failed to add contact', {
        description: 'Please check your information and try again',
        duration: 5000,
      });

      return { success: false, error: errorMessage };
    }
  }

  static async searchCompanies(searchTerm: string): Promise<CompanySuggestion[]> {
    if (searchTerm.length < 2) {
      return [];
    }

    try {
      const response = await fetch(`/api/companies/search?q=${encodeURIComponent(searchTerm)}`);
      
      if (response.ok) {
        const suggestions = await response.json();
        return suggestions;
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error searching companies:', error);
      return [];
    }
  }

  static async fetchCompanyData(companyId: string): Promise<CompanySuggestion | null> {
    try {
      const response = await fetch(`/api/companies/${companyId}`);
      if (response.ok) {
        const companyData = await response.json();
        return {
          company_id: companyData.company_id,
          company_name: companyData.company_name || '',
          company_website: companyData.company_website || '',
          industry: companyData.industry || '',
          company_address: companyData.company_address || '',
          company_city: companyData.company_city || '',
          company_state: companyData.company_state || '',
          company_country: companyData.company_country || '',
          company_zip: companyData.company_zip || ''
        };
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
      toast.error('Failed to load company data');
    }
    return null;
  }

  static async fetchFilterOptions(): Promise<{
    jobTiers: string[];
  }> {
    try {
      const jobTiersRes = await fetch('/api/contacts/filter-options?type=job_tiers');
      const jobTiersData = await jobTiersRes.json();
      
      return {
        jobTiers: jobTiersData.success ? jobTiersData.data.map((item: any) => item.value) : []
      };
    } catch (error) {
      console.error('Error fetching filter options:', error);
      return {
        jobTiers: []
      };
    }
  }

  static async saveInvestmentCriteria(contactId: number, investmentCriteria: any[]): Promise<void> {
    for (const criteria of investmentCriteria) {
      // Determine if this is a debt or equity position
      const isDebtPosition = ['Senior Debt', 'Stretch Senior', 'Mezzanine'].includes(criteria.capital_position);
      const isEquityPosition = ['Preferred Equity', 'Common Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP'].includes(criteria.capital_position);

      // Build request body with central table fields only
      const requestBody: any = {
        capital_position: criteria.capital_position,
        minimum_deal_size: criteria.minimum_deal_size,
        maximum_deal_size: criteria.maximum_deal_size,
        // Convert string fields to arrays as the API expects
        country: criteria.country ? [criteria.country] : null,
        region: criteria.region ? [criteria.region] : null,
        state: criteria.state ? [criteria.state] : null,
        city: criteria.city ? [criteria.city] : null,
        property_types: criteria.property_types ? (Array.isArray(criteria.property_types) ? criteria.property_types : [criteria.property_types]) : null,
        property_sub_categories: criteria.property_subcategories ? (Array.isArray(criteria.property_subcategories) ? criteria.property_subcategories : [criteria.property_subcategories]) : null,
        strategies: criteria.strategies ? (Array.isArray(criteria.strategies) ? criteria.strategies : [criteria.strategies]) : null,
        decision_making_process: criteria.decision_making_process,
        notes: criteria.notes
      };

      // For copied company IC, always use the company's debt/equity IDs
      if (criteria.isCopiedFromCompany) {
        // Reference the company's debt/equity tables
        requestBody.investment_criteria_debt_id = criteria.investment_criteria_debt_id;
        requestBody.investment_criteria_equity_id = criteria.investment_criteria_equity_id;
        
        // DEBUG: Log the debt/equity IDs being sent
        console.log('🔍 Copying company IC with IDs:', {
          originalCriteriaId: criteria.investment_criteria_id,
          debtId: criteria.investment_criteria_debt_id,
          equityId: criteria.investment_criteria_equity_id,
          requestBody: requestBody
        });
      } else {
        // For new IC, include debt/equity data to create new entries
        if (isDebtPosition) {
          Object.assign(requestBody, {
            loan_type: criteria.loan_type,
            loan_program: criteria.loan_program,
            min_loan_term: criteria.debt_min_loan_term,
            max_loan_term: criteria.debt_max_loan_term,
            loan_interest_rate: criteria.debt_loan_interest_rate,
            loan_to_value_min: criteria.debt_loan_to_value_min,
            loan_to_value_max: criteria.debt_loan_to_value_max,
            loan_to_cost_min: criteria.debt_loan_to_cost_min,
            loan_to_cost_max: criteria.debt_loan_to_cost_max,
            min_loan_dscr: criteria.debt_min_loan_dscr,
            max_loan_dscr: criteria.debt_max_loan_dscr,
            structured_loan_tranche: criteria.structured_loan_tranche,
            recourse_loan: criteria.debt_recourse_loan,
            lien_position: criteria.debt_lien_position,
            debt_notes: criteria.debt_notes,
            loan_origination_min_fee: criteria.debt_loan_origination_min_fee,
            loan_origination_max_fee: criteria.debt_loan_origination_max_fee,
            loan_exit_min_fee: criteria.debt_loan_exit_min_fee,
            loan_exit_max_fee: criteria.debt_loan_exit_max_fee
          });
        }

        if (isEquityPosition) {
          Object.assign(requestBody, {
            target_return: criteria.equity_target_return,
            minimum_internal_rate_of_return: criteria.equity_minimum_internal_rate_of_return,
            min_hold_period_years: criteria.equity_min_hold_period_years,
            max_hold_period_years: criteria.equity_max_hold_period_years,
            minimum_yield_on_cost: criteria.equity_minimum_yield_on_cost,
            minimum_equity_multiple: criteria.equity_minimum_equity_multiple,
            target_cash_on_cash_min: criteria.equity_target_cash_on_cash_min,
            ownership_requirement: criteria.equity_ownership_requirement,
            attachment_point: criteria.equity_attachment_point,
            max_leverage_tolerance: criteria.equity_max_leverage_tolerance,
            typical_closing_timeline_days: criteria.equity_typical_closing_timeline_days,
            proof_of_funds_requirement: criteria.equity_proof_of_funds_requirement,
            equity_notes: criteria.equity_notes,
            occupancy_requirements: criteria.equity_occupancy_requirements
          });
        }
      }

      const response = await fetch(`/api/investment-criteria-entity/contact/${contactId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save investment criteria');
      }
    }
  }

  static transformFormDataForSubmission(formData: ContactFormData): ContactSubmissionData {
    return {
      first_name: formData.first_name,
      last_name: formData.last_name,
      job_title: formData.job_title,
      job_tier: formData.job_tier,
      personal_email: formData.personal_email,
      phone_number: formData.phone_number,
      phone_number_secondary: formData.phone_number_secondary,
      linkedin_url: formData.linkedin_url,
      contact_address: formData.contact_address,
      contact_city: formData.contact_city,
      contact_state: formData.contact_state,
      contact_zip_code: formData.contact_zip_code,
      contact_country: formData.contact_country,
      last_contact_date: formData.last_contact_date,
      source_of_introduction: formData.source_of_introduction,
    };
  }

  static getInitialFormData(preSelectedCompany?: CompanySuggestion): ContactFormData {
    return {
      // Personal Information
      first_name: '',
      last_name: '',
      job_title: '',
      job_tier: '',
      
      // Contact Information
      email: '',
      personal_email: '',
      phone_number: '',
      phone_number_secondary: '',
      
      // Social Media
      linkedin_url: '',
      
      // Contact Location
      contact_address: '',
      contact_city: '',
      contact_state: '',
      contact_zip_code: '',
      contact_country: '',
      last_contact_date: '',
      source_of_introduction: '',
      
      // Company Information
      company_name: preSelectedCompany?.company_name || '',
      company_website: preSelectedCompany?.company_website || '',
      industry: preSelectedCompany?.industry || '',
      company_address: preSelectedCompany?.company_address || '',
      company_city: preSelectedCompany?.company_city || '',
      company_state: preSelectedCompany?.company_state || '',
      company_country: preSelectedCompany?.company_country || '',
      company_zip: preSelectedCompany?.company_zip || '',
    };
  }
}
