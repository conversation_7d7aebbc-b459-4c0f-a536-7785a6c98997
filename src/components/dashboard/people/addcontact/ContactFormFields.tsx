"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Users, Phone, Globe, Loader2, CheckCircle, X, ExternalLink, AlertTriangle } from "lucide-react"
import Link from 'next/link'
import { ContactFormData, CONTACT_FIELD_GROUPS, ValidationState } from '../shared/types'
import { formatPhoneNumber } from '../shared/phoneUtils'

interface ContactFormFieldsProps {
  formData: ContactFormData;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSelectChange: (name: string, value: string) => void;
  onPhoneChange?: (field: string, value: string) => void;
  validationStates: {
    email?: ValidationState;
    personal_email: ValidationState;
    additional_email?: Val<PERSON>tionState;
    linkedin_url: ValidationState;
    full_name: ValidationState;
  };
  filterOptions: {
    jobTiers: string[];
  };
  companyId?: string;
  selectedCompany?: any;
  // Search props
  showEmailSuggestions?: boolean;
  emailSuggestions?: any[];
  selectedEmailSuggestionIndex?: number;
  isSearchingEmail?: boolean;
  activeEmailField?: 'email' | 'personal_email' | 'additional_email' | 'name_search' | null;
  onEmailKeyDown?: (e: React.KeyboardEvent) => void;
  onEmailContactSelect?: (contact: any) => void;
  emailSuggestionsRef?: React.RefObject<HTMLDivElement>;
}

const ContactFormFields: React.FC<ContactFormFieldsProps> = ({
  formData,
  onChange,
  onSelectChange,
  onPhoneChange,
  validationStates,
  filterOptions,
  companyId,
  selectedCompany,
  // Search props
  showEmailSuggestions = false,
  emailSuggestions = [],
  selectedEmailSuggestionIndex = -1,
  isSearchingEmail = false,
  activeEmailField = null,
  onEmailKeyDown,
  onEmailContactSelect,
  emailSuggestionsRef
}) => {
  const getValidationIcon = (fieldName: string, value: string) => {
    const state = validationStates[fieldName as keyof typeof validationStates];
    if (!state) return null;

    if (state.isValidating) {
      return <Loader2 className="h-4 w-4 animate-spin text-gray-400" />;
    }
    if (state.isDuplicate) {
      return <X className="h-4 w-4 text-red-600" />;
    }
    if (value && !state.isValidating) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    return null;
  };

  const getFieldClassName = (fieldName: string) => {
    const state = validationStates[fieldName as keyof typeof validationStates];
    const baseClass = "h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 hover:border-slate-300";
    
    if (state?.isDuplicate) {
      return `${baseClass} border-red-300 focus:border-red-500`;
    }
    return `${baseClass} border-slate-200 focus:border-blue-500`;
  };

  const renderField = (field: any) => {
    const { name, label, type, required, placeholder, validation } = field;
    const value = formData[name as keyof ContactFormData] as string;

    // Handle phone number fields with special formatting
    if ((name === 'phone_number' || name === 'phone_number_secondary') && type === 'tel') {
      return (
        <div key={name} className="space-y-2">
          <Label htmlFor={name} className="text-sm font-medium text-slate-700">
            {label} {required && <span className="text-red-500">*</span>}
            {validation && <span className="text-xs text-gray-500 ml-1">({validation})</span>}
          </Label>
          <div className="relative">
            <Input
              id={name}
              name={name}
              type={type}
              value={value}
              onChange={(e) => {
                const formattedValue = formatPhoneNumber(e.target.value);
                if (onPhoneChange) {
                  onPhoneChange(name, formattedValue);
                } else {
                  onChange(e);
                }
              }}
              className={getFieldClassName(name)}
              placeholder={placeholder}
              required={required}
            />
          </div>
        </div>
      );
    }

    if (type === 'select' && name === 'job_tier') {
      return (
        <div key={name} className="space-y-2">
          <Label htmlFor={name} className="text-sm font-medium text-slate-700">
            {label} {required && <span className="text-red-500">*</span>}
          </Label>
          <Select value={value} onValueChange={(val) => onSelectChange(name, val)}>
            <SelectTrigger className="h-12 rounded-xl border-2 border-slate-200 focus:border-blue-500">
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {filterOptions.jobTiers.map((tier) => (
                <SelectItem key={tier} value={tier}>
                  {tier}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
    }

    // Special handling for email fields with search functionality
    if ((name === 'email' || name === 'personal_email' || name === 'additional_email') && type === 'email') {
      return (
        <div key={name} className="space-y-2">
          <Label htmlFor={name} className="text-sm font-medium text-slate-700">
            {label} {required && <span className="text-red-500">*</span>}
            {validation && <span className="text-xs text-gray-500 ml-1">({validation})</span>}
          </Label>
          <div className="relative">
            <Input
              id={name}
              name={name}
              type={type}
              value={value}
              onChange={onChange}
              onKeyDown={onEmailKeyDown}
              className={getFieldClassName(name)}
              placeholder={placeholder}
              required={required}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {isSearchingEmail ? (
                <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
              ) : value && !isSearchingEmail ? (
                <div className="h-4 w-4 rounded-full bg-blue-100" />
              ) : null}
            </div>
          </div>
          {/* Show validation error message */}
          {validationStates[name as keyof typeof validationStates]?.isDuplicate && (
            <p className="text-sm text-red-600">
              {validationStates[name as keyof typeof validationStates]?.duplicateMessage}
            </p>
          )}
          
          {/* Email Search Results - Show under email fields when searching by email */}
          {showEmailSuggestions && activeEmailField === name && (
            <div className="space-y-2" ref={emailSuggestionsRef}>
              <Label className="text-sm font-medium text-slate-700">
                Existing Contacts Found ({emailSuggestions.length})
                <span className="text-xs text-slate-500 ml-2">
                  {name === 'email' ? '(for main email)' : name === 'personal_email' ? '(for personal email)' : '(for additional email)'}
                </span>
              </Label>
              <div className="space-y-2 max-h-60 overflow-y-auto border rounded-xl p-2 bg-orange-50" data-email-search-results>
                {emailSuggestions.map((contact, index) => (
                  <div
                    key={contact.contact_id}
                    className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                      index === selectedEmailSuggestionIndex 
                        ? 'bg-orange-100 border-orange-300 shadow-sm' 
                        : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                    }`}
                    onClick={() => onEmailContactSelect?.(contact)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 text-sm flex items-center">
                          {contact.first_name} {contact.last_name}
                          <Link 
                            href={`/dashboard/people/${contact.contact_id}`}
                            className="ml-2 p-1 rounded-full hover:bg-blue-100 transition-colors"
                            onClick={(e) => e.stopPropagation()}
                            title="View contact details"
                          >
                            <ExternalLink className="h-3 w-3 text-blue-600" />
                          </Link>
                        </div>
                        <div className="text-xs text-slate-600 mt-1">
                          {contact.email} {contact.personal_email && contact.personal_email !== contact.email && `• ${contact.personal_email}`}
                        </div>
                        {contact.title && (
                          <div className="text-xs text-slate-500">
                            {contact.title} {contact.company_name && `at ${contact.company_name}`}
                          </div>
                        )}
                        <div className="flex items-center text-xs text-orange-600 mt-2">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          <span>Contact already exists</span>
                        </div>
                      </div>
                      <div className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                        ID: {contact.contact_id}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    }

    return (
      <div key={name} className="space-y-2">
        <Label htmlFor={name} className="text-sm font-medium text-slate-700">
          {label} {required && <span className="text-red-500">*</span>}
          {validation && <span className="text-xs text-gray-500 ml-1">({validation})</span>}
        </Label>
        <div className="relative">
          <Input
            id={name}
            name={name}
            type={type}
            value={value}
            onChange={onChange}
            className={getFieldClassName(name)}
            placeholder={placeholder}
            required={required}
          />
          {/* Show validation icons for specific fields */}
          {(name === 'email' || name === 'personal_email' || name === 'additional_email' || name === 'linkedin_url' || 
            (name === 'first_name' && (selectedCompany || companyId)) ||
            (name === 'last_name' && (selectedCompany || companyId))) && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {name === 'first_name' || name === 'last_name' 
                ? getValidationIcon('full_name', `${formData.first_name} ${formData.last_name}`)
                : getValidationIcon(name, value)
              }
            </div>
          )}
        </div>
        {/* Show validation error message */}
        {validationStates[name as keyof typeof validationStates]?.isDuplicate && (
          <p className="text-sm text-red-600">
            {validationStates[name as keyof typeof validationStates]?.duplicateMessage}
          </p>
        )}
      </div>
    );
  };

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'Users': return <Users className="h-5 w-5 text-blue-600" />;
      case 'Phone': return <Phone className="h-5 w-5 text-blue-600" />;
      case 'Globe': return <Globe className="h-5 w-5 text-blue-600" />;
      default: return <Users className="h-5 w-5 text-blue-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {Object.entries(CONTACT_FIELD_GROUPS).map(([groupKey, group]) => (
        <Card key={groupKey} className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
            <CardTitle className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-xl mr-3">
                {getIcon(group.icon)}
              </div>
              <div>
                <div className="text-lg font-semibold text-slate-900">{group.title}</div>
                <div className="text-sm text-slate-600">
                  {groupKey === 'personalInfo' && '(Required fields marked with *)'}
                  {groupKey === 'contactInfo' && 'Contact details and location'}
                  {groupKey === 'socialMedia' && 'Social media profiles'}
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {group.fields.map(renderField)}
              
              {/* Name Search Results - Show in right column of Personal Information */}
              {groupKey === 'personalInfo' && showEmailSuggestions && activeEmailField === 'name_search' && (
                <div className="space-y-2" ref={emailSuggestionsRef}>
                  <Label className="text-sm font-medium text-slate-700">
                    Existing Contacts Found ({emailSuggestions.length})
                    <span className="text-xs text-slate-500 ml-2">(by name search)</span>
                  </Label>
                  <div className="space-y-2 max-h-60 overflow-y-auto border rounded-xl p-2 bg-orange-50" data-email-search-results>
                    {emailSuggestions.map((contact, index) => (
                      <div
                        key={contact.contact_id}
                        className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                          index === selectedEmailSuggestionIndex 
                            ? 'bg-orange-100 border-orange-300 shadow-sm' 
                            : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                        }`}
                        onClick={() => onEmailContactSelect?.(contact)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="font-medium text-gray-900 text-sm flex items-center">
                              {contact.first_name} {contact.last_name}
                              <Link 
                                href={`/dashboard/people/${contact.contact_id}`}
                                className="ml-2 p-1 rounded-full hover:bg-blue-100 transition-colors"
                                onClick={(e) => e.stopPropagation()}
                                title="View contact details"
                              >
                                <ExternalLink className="h-3 w-3 text-blue-600" />
                              </Link>
                            </div>
                            <div className="text-xs text-slate-600 mt-1">
                              {contact.email} {contact.personal_email && contact.personal_email !== contact.email && `• ${contact.personal_email}`}
                            </div>
                            {contact.title && (
                              <div className="text-xs text-slate-500">
                                {contact.title} {contact.company_name && `at ${contact.company_name}`}
                              </div>
                            )}
                            <div className="flex items-center text-xs text-orange-600 mt-2">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              <span>Contact already exists</span>
                            </div>
                          </div>
                          <div className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                            ID: {contact.contact_id}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ContactFormFields;
