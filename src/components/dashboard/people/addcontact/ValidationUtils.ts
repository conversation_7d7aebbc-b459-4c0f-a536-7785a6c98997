import { ContactFormData, ValidationState, ExistingContact } from '../shared/types';

export interface ValidationStates {
  email: ValidationState;
  personal_email: ValidationState;
  linkedin_url: ValidationState;
  full_name: ValidationState;
}

export class ContactValidationService {
  static async validateEmail(email: string, field: 'email' | 'personal_email' ): Promise<ValidationState> {
    if (!email || email.length < 3) {
      return { isValidating: false, isDuplicate: false };
    }

    try {
      const response = await fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(email)}`);
      if (response.ok) {
        const result = await response.json();
        const isDuplicate = result?.contact || (result?.contacts && result.contacts.length > 0);
        const duplicateMessage = isDuplicate 
          ? `Email already exists for ${result?.contact?.first_name || 'another contact'}` 
          : undefined;
        
        return { isValidating: false, isDuplicate, duplicateMessage };
      }
    } catch (error) {
      console.error('Email validation error:', error);
    }
    
    return { isValidating: false, isDuplicate: false };
  }

  static async validateLinkedIn(linkedin: string): Promise<ValidationState> {
    if (!linkedin || linkedin.length < 10) {
      return { isValidating: false, isDuplicate: false };
    }

    try {
      const response = await fetch(`/api/contacts?linkedin_url=${encodeURIComponent(linkedin)}&limit=1`);
      if (response.ok) {
        const result = await response.json();
        const isDuplicate = result?.contacts && result.contacts.length > 0;
        const duplicateMessage = isDuplicate 
          ? `LinkedIn URL already exists for ${result.contacts[0]?.first_name || 'another contact'}` 
          : undefined;
        
        return { isValidating: false, isDuplicate, duplicateMessage };
      }
    } catch (error) {
      console.error('LinkedIn validation error:', error);
    }
    
    return { isValidating: false, isDuplicate: false };
  }

  static async validateFullName(
    firstName: string, 
    lastName: string, 
    companyId?: string | number
  ): Promise<ValidationState> {
    if (!firstName || !lastName || (firstName.length + lastName.length < 4)) {
      return { isValidating: false, isDuplicate: false };
    }
    
    if (!companyId) {
      return { isValidating: false, isDuplicate: false };
    }

    try {
      const fullName = `${firstName} ${lastName}`.trim();
      const response = await fetch(
        `/api/contacts?search=${encodeURIComponent(fullName)}&company_id=${companyId}&limit=1`
      );
      
      if (response.ok) {
        const result = await response.json();
        const isDuplicate = result?.contacts && result.contacts.length > 0;
        const duplicateMessage = isDuplicate ? `Contact already exists in this company` : undefined;
        
        return { isValidating: false, isDuplicate, duplicateMessage };
      }
    } catch (error) {
      console.error('Name validation error:', error);
    }
    
    return { isValidating: false, isDuplicate: false };
  }

  static async checkForDuplicates(
    formData: ContactFormData,
    companyId?: string,
    selectedCompanyId?: string | number
  ): Promise<ExistingContact[]> {
    try {
      const checkPromises: Promise<any>[] = [];
      
      // Check by personal email (exact match)
      if (formData.personal_email) {
        checkPromises.push(
          fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(formData.personal_email)}`)
            .then(res => res.ok ? res.json() : null)
        );
      }

      // Check by LinkedIn URL (exact match)
      if (formData.linkedin_url) {
        checkPromises.push(
          fetch(`/api/contacts?linkedin_url=${encodeURIComponent(formData.linkedin_url)}&limit=5`)
            .then(res => res.ok ? res.json() : null)
        );
      }

      // Check by name + company (exact match)
      if (formData.first_name && formData.last_name && (companyId || selectedCompanyId)) {
        const companyIdToUse = companyId ? parseInt(companyId) : selectedCompanyId;
        const fullName = `${formData.first_name} ${formData.last_name}`.trim();
        checkPromises.push(
          fetch(`/api/contacts?search=${encodeURIComponent(fullName)}&company_id=${companyIdToUse}&limit=5`)
            .then(res => res.ok ? res.json() : null)
        );
      }

      if (checkPromises.length === 0) {
        return [];
      }

      const results = await Promise.all(checkPromises);
      const duplicates: ExistingContact[] = [];
      
      results.forEach((result) => {
        if (result?.contacts) {
          duplicates.push(...result.contacts);
        } else if (result?.contact) {
          duplicates.push(result.contact);
        } else if (Array.isArray(result)) {
          duplicates.push(...result);
        }
      });

      // Remove duplicates by contact_id
      const uniqueDuplicates = duplicates.filter((contact, index, self) =>
        index === self.findIndex(c => c.contact_id === contact.contact_id)
      );

      // Only return exact matches
      const exactMatches = uniqueDuplicates.filter(contact => {
        // Personal email match
        if (formData.personal_email && contact.personal_email && 
            contact.personal_email.toLowerCase() === formData.personal_email.toLowerCase()) {
          return true;
        }
        
        // LinkedIn URL match
        if (formData.linkedin_url && contact.linkedin_url && 
            contact.linkedin_url.toLowerCase() === formData.linkedin_url.toLowerCase()) {
          return true;
        }
        
        // Name + company match
        if (formData.first_name && formData.last_name && contact.first_name && contact.last_name) {
          const formFullName = `${formData.first_name} ${formData.last_name}`.toLowerCase().trim();
          const contactFullName = `${contact.first_name} ${contact.last_name}`.toLowerCase().trim();
          
          if (formFullName === contactFullName && (companyId || selectedCompanyId)) {
            return true;
          }
        }
        
        return false;
      });

      return exactMatches;
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      return [];
    }
  }

  static validateFormData(formData: ContactFormData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields validation
    if (!formData.first_name?.trim()) {
      errors.push('First name is required');
    }
    
    if (!formData.last_name?.trim()) {
      errors.push('Last name is required');
    }
    
    if (!formData.company_name?.trim()) {
      errors.push('Company name is required');
    }

    // Email validation
    if (formData.personal_email && !this.isValidEmail(formData.personal_email)) {
      errors.push('Please enter a valid personal email address');
    }

    // URL validation
    if (formData.linkedin_url && !this.isValidUrl(formData.linkedin_url)) {
      errors.push('Please enter a valid LinkedIn URL');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}
