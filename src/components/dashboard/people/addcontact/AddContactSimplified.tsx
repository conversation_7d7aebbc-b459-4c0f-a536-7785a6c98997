"use client"

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { ArrowLeft, CheckCircle, Loader2, Search, ExternalLink, AlertTriangle, X } from "lucide-react"
import { debounce } from 'lodash'
import Link from 'next/link'
import { ContactFormData, CompanySuggestion, ExistingContact } from '../shared/types'
import { DuplicateContactDialog } from '../components/DuplicateContactDialog'
import ContactFormFields from './ContactFormFields'
import CompanySearchField from './CompanySearchField'
import InvestmentAdditionSection from './InvestmentAdditionSection'
import { ContactValidationService, ValidationStates } from './ValidationUtils'
import { ContactSubmissionService } from './ContactSubmissionService'
import CompanyForm from '@/components/dashboard/companies/detail-components/CompanyForm'

interface AddContactSimplifiedProps {
  onBack: () => void;
  companyId?: string;
  preSelectedCompany?: CompanySuggestion;
  onSuccess?: (contactId: number) => void;
}

const AddContactSimplified: React.FC<AddContactSimplifiedProps> = ({ 
  onBack, 
  companyId, 
  preSelectedCompany,
  onSuccess
}) => {
  const [formData, setFormData] = useState<ContactFormData>(
    ContactSubmissionService.getInitialFormData(preSelectedCompany)
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [duplicateContacts, setDuplicateContacts] = useState<ExistingContact[]>([]);
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [investmentCriteria, setInvestmentCriteria] = useState<any[]>([]);
  const [selectedCompanyIC, setSelectedCompanyIC] = useState<any[]>([]);
  
  // Company search state
  const [isSearching, setIsSearching] = useState(false);
  const [companySuggestions, setCompanySuggestions] = useState<CompanySuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanySuggestion | null>(preSelectedCompany || null);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  
  // Company creation state
  const [showCompanyForm, setShowCompanyForm] = useState(false);
  const [companyFormData, setCompanyFormData] = useState<any>(null);
  
  // URL parameters
  const searchParams = useSearchParams();
  
  // Email and name search state
  const [emailSuggestions, setEmailSuggestions] = useState<any[]>([]);
  const [showEmailSuggestions, setShowEmailSuggestions] = useState(false);
  const [selectedEmailSuggestionIndex, setSelectedEmailSuggestionIndex] = useState(-1);
  const [isSearchingEmail, setIsSearchingEmail] = useState(false);
  const [activeEmailField, setActiveEmailField] = useState<'email' | 'personal_email' | 'name_search' | null>(null);
  const emailSuggestionsRef = useRef<HTMLDivElement>(null);
  
  // Validation state
  const [validationStates, setValidationStates] = useState<ValidationStates>({
    email: { isValidating: false, isDuplicate: false },
    personal_email: { isValidating: false, isDuplicate: false },
    linkedin_url: { isValidating: false, isDuplicate: false },
    full_name: { isValidating: false, isDuplicate: false }
  });

  // Filter options
  const [filterOptions, setFilterOptions] = useState<{
    jobTiers: string[];
  }>({
    jobTiers: []
  });

  // Fetch filter options on component mount
  useEffect(() => {
    const fetchFilterOptions = async () => {
      const options = await ContactSubmissionService.fetchFilterOptions();
      setFilterOptions(options);
    };

    fetchFilterOptions();
  }, []);

  // Fetch company data if companyId is provided
  useEffect(() => {
    const fetchCompanyData = async () => {
      if (companyId && !preSelectedCompany) {
        const transformedCompany = await ContactSubmissionService.fetchCompanyData(companyId);
        if (transformedCompany) {
          setSelectedCompany(transformedCompany);
          setFormData(prev => ({
            ...prev,
            company_name: transformedCompany.company_name,
            company_website: transformedCompany.company_website || '',
            industry: transformedCompany.industry || '',
            company_address: transformedCompany.company_address || '',
            company_city: transformedCompany.company_city || '',
            company_state: transformedCompany.company_state || '',
            company_country: transformedCompany.company_country || '',
            company_zip: transformedCompany.company_zip || ''
          }));
        }
      }
    };

    fetchCompanyData();
  }, [companyId, preSelectedCompany]);

  // Handle URL parameters for company preloading
  useEffect(() => {
    const handleUrlParams = async () => {
      const urlCompanyId = searchParams?.get('company_id');
      const urlCompanyName = searchParams?.get('company_name');
      
      if (urlCompanyId && !companyId && !preSelectedCompany) {
        // Fetch company by ID from URL
        try {
          const transformedCompany = await ContactSubmissionService.fetchCompanyData(urlCompanyId);
          if (transformedCompany) {
            setSelectedCompany(transformedCompany);
            setFormData(prev => ({
              ...prev,
              company_name: transformedCompany.company_name,
              company_website: transformedCompany.company_website || '',
              industry: transformedCompany.industry || '',
              company_address: transformedCompany.company_address || '',
              company_city: transformedCompany.company_city || '',
              company_state: transformedCompany.company_state || '',
              company_country: transformedCompany.company_country || '',
              company_zip: transformedCompany.company_zip || ''
            }));
            
            // Update URL to remove the parameters (clean URL without reload)
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('company_id');
            newUrl.searchParams.delete('company_name');
            window.history.replaceState({}, '', newUrl.pathname);
          }
        } catch (error) {
          console.error('Error fetching company from URL params:', error);
        }
      } else if (urlCompanyName && !urlCompanyId && !companyId && !preSelectedCompany) {
        // If only company name is provided, set it in the form
        setFormData(prev => ({
          ...prev,
          company_name: decodeURIComponent(urlCompanyName)
        }));
        
        // Update URL to remove the parameters (clean URL without reload)
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('company_name');
        window.history.replaceState({}, '', newUrl.pathname);
      }
    };

    handleUrlParams();
  }, [searchParams, companyId, preSelectedCompany]);

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle email suggestions
      if (emailSuggestionsRef.current && !emailSuggestionsRef.current.contains(event.target as Node)) {
        const emailSearchResults = document.querySelector('[data-email-search-results]');
        if (emailSearchResults && emailSearchResults.contains(event.target as Node)) {
          return;
        }
        setShowEmailSuggestions(false);
        setSelectedEmailSuggestionIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Debounced name search function
  const debouncedSearchByName = useCallback(
    debounce(async (firstName: string, lastName: string) => {
      console.log('Name search called with:', firstName, lastName);
      
      if (!firstName || !lastName || (firstName.length + lastName.length < 4)) {
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
        setIsSearchingEmail(false);
        return;
      }

      setIsSearchingEmail(true);
      setActiveEmailField('name_search');
      
      try {
        const fullName = `${firstName} ${lastName}`;
        const response = await fetch(`/api/contacts?search=${encodeURIComponent(fullName)}&limit=5`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Name search results:', data);
          setEmailSuggestions(data.contacts || []);
          setShowEmailSuggestions(data.contacts && data.contacts.length > 0);
        } else {
          setEmailSuggestions([]);
          setShowEmailSuggestions(false);
        }
      } catch (error) {
        console.error('Error during name search:', error);
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
      } finally {
        setIsSearchingEmail(false);
      }
    }, 500),
    []
  );

  // Debounced email search function
  const debouncedSearchEmails = useCallback(
    debounce(async (email: string, fieldName: 'email' | 'personal_email') => {
      console.log('Email search called with:', email, 'Field:', fieldName);
      
      if (email.length < 3) {
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
        setIsSearchingEmail(false);
        return;
      }

      setIsSearchingEmail(true);
      setActiveEmailField(fieldName);
      
      try {
        const response = await fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(email)}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Email search results:', data);
          setEmailSuggestions(data.contacts || []);
          setShowEmailSuggestions(data.contacts && data.contacts.length > 0);
        } else {
          setEmailSuggestions([]);
          setShowEmailSuggestions(false);
        }
      } catch (error) {
        console.error('Error during email search:', error);
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
      } finally {
        setIsSearchingEmail(false);
      }
    }, 500),
    []
  );

  // Handle email contact selection
  const handleEmailContactSelect = (contact: any) => {
    // Show duplicate warning and ask user what they want to do
    const message = `A contact with email "${contact.email}" already exists:\n\n` +
      `Name: ${contact.first_name} ${contact.last_name}\n` +
      `Company: ${contact.company_name || 'N/A'}\n` +
      `Title: ${contact.title || 'N/A'}\n\n` +
      `Would you like to:\n` +
      `- Edit the existing contact instead\n` +
      `- Continue editing current contact`;

    if (confirm(message)) {
      // User wants to edit existing contact - redirect to contact detail page
      window.open(`/dashboard/people/${contact.contact_id}`, '_blank');
    }
    
    // Clear email search suggestions
    setShowEmailSuggestions(false);
    setEmailSuggestions([]);
    setSelectedEmailSuggestionIndex(-1);
  };

  // Keyboard navigation for email suggestions
  const handleEmailKeyDown = (e: React.KeyboardEvent) => {
    if (!showEmailSuggestions || emailSuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedEmailSuggestionIndex((prev) => 
          prev < emailSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedEmailSuggestionIndex((prev) => 
          prev > 0 ? prev - 1 : emailSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedEmailSuggestionIndex >= 0) {
          handleEmailContactSelect(emailSuggestions[selectedEmailSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowEmailSuggestions(false);
        setSelectedEmailSuggestionIndex(-1);
        break;
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Trigger company search when company_name changes
    if (name === 'company_name') {
      if (selectedCompany && value !== selectedCompany.company_name) {
        setSelectedCompany(null);
      }
      
      setSelectedSuggestionIndex(-1);
      if (value.trim() && value.trim().length >= 2) {
        debouncedSearchCompanies(value);
      } else if (value.trim().length < 2) {
        setShowSuggestions(false);
        setCompanySuggestions([]);
      }
    }

    // Trigger email search when email fields change
    if (name === 'email' || name === 'personal_email') {
      debouncedSearchEmails(value, name as 'email' | 'personal_email');
      debouncedValidateEmail(value, name as 'email' | 'personal_email');
    } else if (name === 'linkedin_url') {
      debouncedValidateLinkedIn(value);
    } else if (name === 'first_name' || name === 'last_name') {
      const firstName = name === 'first_name' ? value : formData.first_name;
      const lastName = name === 'last_name' ? value : formData.last_name;
      
      if (firstName && lastName) {
        debouncedSearchByName(firstName, lastName);
        debouncedValidateFullName(firstName, lastName);
      }
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle phone number changes with formatting
  const handlePhoneChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Debounced validation functions
  const debouncedValidateEmail = useCallback(
    debounce(async (email: string, field: 'email' | 'personal_email') => {
      if (!email || email.length < 3) return;

      setValidationStates(prev => ({
        ...prev,
        [field]: { ...prev[field], isValidating: true, isDuplicate: false }
      }));

      const result = await ContactValidationService.validateEmail(email, field);
      setValidationStates(prev => ({
        ...prev,
        [field]: result
      }));
    }, 500),
    []
  );

  const debouncedValidateLinkedIn = useCallback(
    debounce(async (linkedin: string) => {
      if (!linkedin || linkedin.length < 10) return;

      setValidationStates(prev => ({
        ...prev,
        linkedin_url: { ...prev.linkedin_url, isValidating: true, isDuplicate: false }
      }));

      const result = await ContactValidationService.validateLinkedIn(linkedin);
      setValidationStates(prev => ({
        ...prev,
        linkedin_url: result
      }));
    }, 500),
    []
  );

  const debouncedValidateFullName = useCallback(
    debounce(async (firstName: string, lastName: string) => {
      if (!firstName || !lastName || (firstName.length + lastName.length < 4)) return;

      if (!selectedCompany?.company_id && !companyId) return;

      setValidationStates(prev => ({
        ...prev,
        full_name: { ...prev.full_name, isValidating: true, isDuplicate: false }
      }));

      const companyIdToUse = companyId ? parseInt(companyId) : selectedCompany?.company_id;
      const result = await ContactValidationService.validateFullName(firstName, lastName, companyIdToUse);
      setValidationStates(prev => ({
        ...prev,
        full_name: result
      }));
    }, 700),
    [selectedCompany, companyId]
  );

  // Debounced company search function
  const debouncedSearchCompanies = useCallback(
    debounce(async (searchTerm: string) => {
      if (searchTerm.length < 2) {
        setCompanySuggestions([]);
        setShowSuggestions(false);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      const suggestions = await ContactSubmissionService.searchCompanies(searchTerm);
      setCompanySuggestions(suggestions);
      // Always show suggestions dropdown when searching (even if no results)
      setShowSuggestions(true);
      setIsSearching(false);
    }, 300),
    []
  );

  // Check for duplicates function
  const checkForDuplicates = async (): Promise<ExistingContact[]> => {
    return ContactValidationService.checkForDuplicates(
      formData,
      companyId,
      selectedCompany?.company_id
    );
  };

  // Save contact function
  const saveContact = async () => {
    try {
      // Consolidate all investment criteria into a single array
      const allInvestmentCriteria = [
        ...investmentCriteria, // New IC added via InvestmentAdditionSection
        ...selectedCompanyIC.map(ic => ({
          ...ic,
          isCopiedFromCompany: true, // Flag to indicate this is copied from company
          originalCompanyCriteriaId: ic.investment_criteria_id
        }))
      ];

      // DEBUG: Log the consolidated IC data
      console.log('🚀 Saving contact with IC data:', {
        totalIC: allInvestmentCriteria.length,
        newIC: investmentCriteria.length,
        companyIC: selectedCompanyIC.length,
        allInvestmentCriteria: allInvestmentCriteria
      });

      const result = await ContactSubmissionService.saveContact(formData, companyId, selectedCompany, allInvestmentCriteria);
      if (result.success) {
        // Show appropriate success message based on what was saved
        const totalIC = allInvestmentCriteria.length;
        if (totalIC > 0) {
          toast.success(`Contact created successfully with ${totalIC} investment criteria`);
        } else {
          toast.success('Contact created successfully');
        }
        
        // Call onSuccess callback if provided
        if (onSuccess && result.contact_id) {
          onSuccess(result.contact_id);
        } else {
          onBack();
        }
      } else {
        throw new Error(result.error || 'Failed to save contact');
      }
    } catch (error) {
      console.error('Error saving contact:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check for validation errors before submitting
    const hasValidationErrors = Object.values(validationStates).some(state => state.isDuplicate);
    if (hasValidationErrors) {
      toast.error('Please resolve validation errors before submitting');
      return;
    }

    setIsSubmitting(true);

    try {
      // Check for duplicates first
      const duplicates = await checkForDuplicates();

      if (duplicates.length > 0) {
        setDuplicateContacts(duplicates);
        setShowDuplicateDialog(true);
        setIsSubmitting(false);
        return;
      }

      await saveContact();
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      toast.error('Failed to add contact');
      setIsSubmitting(false);
    }
  };

  const handleProceedWithDuplicate = async () => {
    setShowDuplicateDialog(false);
    setIsSubmitting(true);
    try {
      await saveContact();
    } catch (error) {
      // Error handling is done in saveContact
    }
  };

  const handleCancelDuplicate = () => {
    setShowDuplicateDialog(false);
    setDuplicateContacts([]);
  };

  // Handle create new company
  const handleCreateNewCompany = (companyName: string) => {
    setCompanyFormData({ company_name: companyName });
    setShowCompanyForm(true);
    setShowSuggestions(false);
  };

  // Handle company creation success
  const handleCompanyCreationSuccess = (companyId: number) => {
    // Fetch the created company data and auto-select it
    const fetchCreatedCompany = async () => {
      try {
        // Fetch company data and investment criteria count in parallel
        const [companyResponse, criteriaResponse] = await Promise.all([
          fetch(`/api/companies/${companyId}`),
          fetch(`/api/companies/${companyId}/investment-criteria`)
        ]);
        
        if (companyResponse.ok) {
          const companyData = await companyResponse.json();
          
          // Get investment criteria count
          let investmentCriteriaCount = 0;
          if (criteriaResponse.ok) {
            const criteriaData = await criteriaResponse.json();
            investmentCriteriaCount = criteriaData.length || 0;
          }
          
          // Transform to CompanySuggestion format
          const transformedCompany: CompanySuggestion = {
            company_id: companyData.company_id,
            company_name: companyData.company_name,
            company_website: companyData.company_website,
            industry: companyData.industry,
            company_address: companyData.company_address,
            company_city: companyData.company_city,
            company_state: companyData.company_state,
            company_country: companyData.company_country,
            company_zip: companyData.company_zip,
            company_phone: companyData.company_phone,
            company_email: companyData.main_email,
            investment_criteria_count: investmentCriteriaCount
          };
          
          setSelectedCompany(transformedCompany);
          setFormData(prev => ({
            ...prev,
            company_name: companyData.company_name,
            company_website: companyData.company_website || '',
            industry: companyData.industry || '',
            company_address: companyData.company_address || '',
            company_city: companyData.company_city || '',
            company_state: companyData.company_state || '',
            company_country: companyData.company_country || '',
            company_zip: companyData.company_zip || ''
          }));
          
          // Hide company form and show success message
          setShowCompanyForm(false);
          setCompanyFormData(null);
          
          if (investmentCriteriaCount > 0) {
            toast.success(`Company created with ${investmentCriteriaCount} investment criteria and selected`);
          } else {
            toast.success('Company created successfully and selected');
          }
        }
      } catch (error) {
        console.error('Error fetching created company:', error);
        toast.error('Company created but failed to select it');
      }
    };

    fetchCreatedCompany();
  };

  // Handle company creation cancel
  const handleCompanyCreationCancel = () => {
    setShowCompanyForm(false);
    setCompanyFormData(null);
  };

  // Handle keyboard navigation for company suggestions
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || companySuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < companySuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : companySuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0 && selectedSuggestionIndex < companySuggestions.length) {
          const selectedCompany = companySuggestions[selectedSuggestionIndex];
          setSelectedCompany(selectedCompany);
          setShowSuggestions(false);
          setSelectedSuggestionIndex(-1);
          
          setFormData(prev => ({
            ...prev,
            company_name: selectedCompany.company_name,
            company_website: selectedCompany.company_website || '',
            industry: selectedCompany.industry || '',
            company_address: selectedCompany.company_address || '',
            company_city: selectedCompany.company_city || '',
            company_state: selectedCompany.company_state || '',
            company_country: selectedCompany.company_country || '',
            company_zip: selectedCompany.company_zip || ''
          }));
          
          toast.success('Company selected successfully');
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  const handleCompanyICEdit = (editedIC: any) => {
    // Update the selectedCompanyIC array with the edited IC
    setSelectedCompanyIC(prev => 
      prev.map(ic => 
        ic.investment_criteria_id === editedIC.originalCompanyCriteriaId 
          ? editedIC 
          : ic
      )
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="w-full p-6 space-y-8">
        {/* Header Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={onBack} 
                className="hover:bg-slate-100 rounded-xl transition-all duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  Add New Contact
                </h1>
                <p className="text-slate-600 mt-1">
                  {companyId ? `Create contact for ${selectedCompany?.company_name || 'selected company'}` : 'Create a contact profile with essential information'}
                </p>
              </div>
            </div>
            
            {/* Top Action Buttons */}
            <div className="flex items-center space-x-3">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onBack}
                className="h-11 px-6 rounded-xl border-2 border-slate-200 hover:bg-slate-50 transition-all duration-200 font-medium"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="h-11 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Save Contact
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Company Creation Modal/Form - Outside main form to avoid nesting */}
        {showCompanyForm ? (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl shadow-2xl border border-slate-200 w-full max-w-4xl max-h-[90vh] overflow-hidden">
              <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900">Create New Company</h3>
                    <p className="text-sm text-slate-600 mt-1">Fill in the company details and it will be automatically selected</p>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={handleCompanyCreationCancel}
                    className="hover:bg-white/50 rounded-lg p-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="overflow-y-auto max-h-[calc(90vh-100px)]">
                <CompanyForm
                  isEmbedded={true}
                  isInAddContactMode={true}
                  initialData={companyFormData}
                  onSuccess={handleCompanyCreationSuccess}
                  onCancel={handleCompanyCreationCancel}
                />
              </div>
            </div>
          </div>
        ) : null}

        {/* Main Contact Form */}
        <form onSubmit={handleSubmit}>
          <div className="grid gap-8 transition-all duration-300 grid-cols-1 lg:grid-cols-2">
            {/* Left Column: Contact Information + Investment Criteria */}
            <div className="space-y-6">
              <ContactFormFields
                formData={formData}
                onChange={handleChange}
                onSelectChange={handleSelectChange}
                onPhoneChange={handlePhoneChange}
                validationStates={validationStates}
                filterOptions={filterOptions}
                companyId={companyId}
                selectedCompany={selectedCompany}
                // Pass search props
                showEmailSuggestions={showEmailSuggestions}
                emailSuggestions={emailSuggestions}
                selectedEmailSuggestionIndex={selectedEmailSuggestionIndex}
                isSearchingEmail={isSearchingEmail}
                activeEmailField={activeEmailField}
                onEmailKeyDown={handleEmailKeyDown}
                onEmailContactSelect={handleEmailContactSelect}
                emailSuggestionsRef={emailSuggestionsRef}
              />
              
              {/* Investment Criteria Section - Left Side */}
              <InvestmentAdditionSection
                onInvestmentCriteriaChange={setInvestmentCriteria}
                selectedCompanyIC={selectedCompanyIC}
                onCompanyICEdit={handleCompanyICEdit}
              />
            </div>

            {/* Right Column: Company Information */}
            <div className="space-y-6">
              <CompanySearchField
                companyName={formData.company_name}
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                suggestions={companySuggestions}
                showSuggestions={showSuggestions}
                isSearching={isSearching}
                selectedSuggestionIndex={selectedSuggestionIndex}
                onCompanySelect={(company) => {
                  setSelectedCompany(company);
                  setShowSuggestions(false);
                  setSelectedSuggestionIndex(-1);
                  
                  setFormData(prev => ({
                    ...prev,
                    company_name: company.company_name,
                    company_website: company.company_website || '',
                    industry: company.industry || '',
                    company_address: company.company_address || '',
                    company_city: company.company_city || '',
                    company_state: company.company_state || '',
                    company_country: company.company_country || '',
                    company_zip: company.company_zip || ''
                  }));
                  
                  toast.success('Company selected successfully');
                }}
                onInvestmentCriteriaSelect={(selectedCriteria) => {
                  setSelectedCompanyIC(selectedCriteria);
                  console.log('Selected company IC:', selectedCriteria);
                }}
                onCreateNewCompany={handleCreateNewCompany}
                selectedCompany={selectedCompany}
                companyId={companyId}
              />
            </div>
          </div>
        </form>

        {/* Duplicate Contact Dialog */}
        <DuplicateContactDialog
          isOpen={showDuplicateDialog}
          onClose={handleCancelDuplicate}
          duplicateContacts={duplicateContacts}
          onAddAnyway={handleProceedWithDuplicate}
        />
      </div>
    </div>
  );
};

export default AddContactSimplified;
