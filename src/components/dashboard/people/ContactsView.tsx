"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserPlus, CheckSquare, Square, Play, RefreshCw, Sparkles, Mail, PenTool, AlertTriangle, CheckCircle, XCircle, Send, Settings2, Activity } from "lucide-react";
import ContactCard from "./list-components/ContactCard";
import Pagination from "./list-components/Pagination";
import PaginationSizeSelector from "./list-components/PaginationSizeSelector";
import ContactUnifiedFiltersV2Component from "./ContactUnifiedFiltersV2";
import { UnifiedContactData } from "./shared/types";
import type { ContactUnifiedFiltersV2 } from "../../../types/unified-filters-v2";
import { useRouter, useSearchParams } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, <PERSON>I<PERSON>, <PERSON>Trigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";




// Processing stages for contacts
type ContactProcessingStage = 'email_validation' | 'contact_enrichment' | 'contact_enrichment_v2' | 'contact_investment_criteria' | 'email_generation' | 'smartlead_sync';

interface ProcessingJob {
  stage: ContactProcessingStage;
  isExecuting: boolean;
}

export default function ContactsView() {
  const [contacts, setContacts] = useState<UnifiedContactData[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalContacts, setTotalContacts] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [mappings, setMappings] = useState<any>(null);

  
  // V2 filters state (V2 only - no legacy support)
  const [filters, setFilters] = useState<ContactUnifiedFiltersV2>({
    page: 1,
    limit: 25,
    sortBy: 'updated_at',
    sortOrder: 'desc'
  });

  // Selection state
  const [selectedContacts, setSelectedContacts] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  
  // Processing state
  const [processingJobs, setProcessingJobs] = useState<ProcessingJob[]>([
    { stage: 'email_validation', isExecuting: false },
    { stage: 'contact_enrichment_v2', isExecuting: false },
    { stage: 'contact_investment_criteria', isExecuting: false },
    { stage: 'email_generation', isExecuting: false },
    { stage: 'smartlead_sync', isExecuting: false }
  ]);
  
  // Smartlead campaign state
  const [campaigns, setCampaigns] = useState<Array<{id: string, name: string}>>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('');
  const [loadingCampaigns, setLoadingCampaigns] = useState(false);
  const [loadingSelectedStats, setLoadingSelectedStats] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  // Handle adding new contact
  const handleAddContact = () => {
    router.push('/dashboard/contacts/add');
  };



  // Initialize V2 filters from URL parameters on component mount
  useEffect(() => {
    if (!searchParams) return;
    
    const urlFilters: ContactUnifiedFiltersV2 = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '25'),
      sortBy: searchParams.get('sortBy') || 'updated_at',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      
      // Separate search fields
      fullNameSearch: searchParams.get('fullNameSearch') || undefined,
      emailSearch: searchParams.get('emailSearch') || undefined,
      companyNameSearch: searchParams.get('companyNameSearch') || undefined,
      

      
      // Investment criteria filters
      capitalPosition: searchParams.get('capitalPosition')?.split(',').filter(Boolean),
      propertyTypes: searchParams.get('propertyTypes')?.split(',').filter(Boolean),
      propertySubcategories: searchParams.get('propertySubcategories')?.split(',').filter(Boolean),
      strategies: searchParams.get('strategies')?.split(',').filter(Boolean),
      decisionMakingProcess: searchParams.get('decisionMakingProcess')?.split(',').filter(Boolean),
      loanTypes: searchParams.get('loanTypes')?.split(',').filter(Boolean),
      loanProgram: searchParams.get('loanProgram')?.split(',').filter(Boolean),
      structuredLoanTranche: searchParams.get('structuredLoanTranche')?.split(',').filter(Boolean),
      recourseLoan: searchParams.get('recourseLoan')?.split(',').filter(Boolean),
      
      // Deal size and return filters
      dealSizeMin: searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : undefined,
      dealSizeMax: searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : undefined,
      targetReturnMin: searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : undefined,
      targetReturnMax: searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : undefined,
      minLoanTermMin: searchParams.get('minLoanTermMin') ? parseInt(searchParams.get('minLoanTermMin')!) : undefined,
      maxLoanTermMax: searchParams.get('maxLoanTermMax') ? parseInt(searchParams.get('maxLoanTermMax')!) : undefined,
      minLoanTermMax: searchParams.get('minLoanTermMax') ? parseInt(searchParams.get('minLoanTermMax')!) : undefined,
      maxLoanTermMin: searchParams.get('maxLoanTermMin') ? parseInt(searchParams.get('maxLoanTermMin')!) : undefined,
      
      // V2 Interest rate and LTV filters with proper naming
      loanInterestRateSofrMin: searchParams.get('loanInterestRateSofrMin') ? parseFloat(searchParams.get('loanInterestRateSofrMin')!) : undefined,
      loanInterestRateSofrMax: searchParams.get('loanInterestRateSofrMax') ? parseFloat(searchParams.get('loanInterestRateSofrMax')!) : undefined,
      loanInterestRateWsjMin: searchParams.get('loanInterestRateWsjMin') ? parseFloat(searchParams.get('loanInterestRateWsjMin')!) : undefined,
      loanInterestRateWsjMax: searchParams.get('loanInterestRateWsjMax') ? parseFloat(searchParams.get('loanInterestRateWsjMax')!) : undefined,
      loanInterestRatePrimeMin: searchParams.get('loanInterestRatePrimeMin') ? parseFloat(searchParams.get('loanInterestRatePrimeMin')!) : undefined,
      loanInterestRatePrimeMax: searchParams.get('loanInterestRatePrimeMax') ? parseFloat(searchParams.get('loanInterestRatePrimeMax')!) : undefined,
      loanInterestRate3ytMin: searchParams.get('loanInterestRate3ytMin') ? parseFloat(searchParams.get('loanInterestRate3ytMin')!) : undefined,
      loanInterestRate3ytMax: searchParams.get('loanInterestRate3ytMax') ? parseFloat(searchParams.get('loanInterestRate3ytMax')!) : undefined,
      loanInterestRate5ytMin: searchParams.get('loanInterestRate5ytMin') ? parseFloat(searchParams.get('loanInterestRate5ytMin')!) : undefined,
      loanInterestRate5ytMax: searchParams.get('loanInterestRate5ytMax') ? parseFloat(searchParams.get('loanInterestRate5ytMax')!) : undefined,
      loanInterestRate10ytMin: searchParams.get('loanInterestRate10ytMin') ? parseFloat(searchParams.get('loanInterestRate10ytMin')!) : undefined,
      loanInterestRate10ytMax: searchParams.get('loanInterestRate10ytMax') ? parseFloat(searchParams.get('loanInterestRate10ytMax')!) : undefined,
      loanInterestRate30ytMin: searchParams.get('loanInterestRate30ytMin') ? parseFloat(searchParams.get('loanInterestRate30ytMin')!) : undefined,
      loanInterestRate30ytMax: searchParams.get('loanInterestRate30ytMax') ? parseFloat(searchParams.get('loanInterestRate30ytMax')!) : undefined,
      loanToValueMinMin: searchParams.get('loanToValueMinMin') ? parseFloat(searchParams.get('loanToValueMinMin')!) : undefined,
      loanToValueMaxMax: searchParams.get('loanToValueMaxMax') ? parseFloat(searchParams.get('loanToValueMaxMax')!) : undefined,
      loanToValueMinMax: searchParams.get('loanToValueMinMax') ? parseFloat(searchParams.get('loanToValueMinMax')!) : undefined,
      loanToValueMaxMin: searchParams.get('loanToValueMaxMin') ? parseFloat(searchParams.get('loanToValueMaxMin')!) : undefined,
      loanToCostMinMin: searchParams.get('loanToCostMinMin') ? parseFloat(searchParams.get('loanToCostMinMin')!) : undefined,
      loanToCostMaxMax: searchParams.get('loanToCostMaxMax') ? parseFloat(searchParams.get('loanToCostMaxMax')!) : undefined,
      loanToCostMinMax: searchParams.get('loanToCostMinMax') ? parseFloat(searchParams.get('loanToCostMinMax')!) : undefined,
      loanToCostMaxMin: searchParams.get('loanToCostMaxMin') ? parseFloat(searchParams.get('loanToCostMaxMin')!) : undefined,
      loanOriginationMaxFeeMin: searchParams.get('loanOriginationMaxFeeMin') ? parseFloat(searchParams.get('loanOriginationMaxFeeMin')!) : undefined,
      loanOriginationMaxFeeMax: searchParams.get('loanOriginationMaxFeeMax') ? parseFloat(searchParams.get('loanOriginationMaxFeeMax')!) : undefined,
      loanOriginationMinFeeMin: searchParams.get('loanOriginationMinFeeMin') ? parseFloat(searchParams.get('loanOriginationMinFeeMin')!) : undefined,
      loanOriginationMinFeeMax: searchParams.get('loanOriginationMinFeeMax') ? parseFloat(searchParams.get('loanOriginationMinFeeMax')!) : undefined,
      loanExitMinFeeMin: searchParams.get('loanExitMinFeeMin') ? parseFloat(searchParams.get('loanExitMinFeeMin')!) : undefined,
      loanExitMinFeeMax: searchParams.get('loanExitMinFeeMax') ? parseFloat(searchParams.get('loanExitMinFeeMax')!) : undefined,
      loanExitMaxFeeMin: searchParams.get('loanExitMaxFeeMin') ? parseFloat(searchParams.get('loanExitMaxFeeMin')!) : undefined,
      loanExitMaxFeeMax: searchParams.get('loanExitMaxFeeMax') ? parseFloat(searchParams.get('loanExitMaxFeeMax')!) : undefined,
      
      // V2 DSCR filters
      minLoanDscrMin: searchParams.get('minLoanDscrMin') ? parseFloat(searchParams.get('minLoanDscrMin')!) : undefined,
      maxLoanDscrMax: searchParams.get('maxLoanDscrMax') ? parseFloat(searchParams.get('maxLoanDscrMax')!) : undefined,
      minLoanDscrMax: searchParams.get('minLoanDscrMax') ? parseFloat(searchParams.get('minLoanDscrMax')!) : undefined,
      maxLoanDscrMin: searchParams.get('maxLoanDscrMin') ? parseFloat(searchParams.get('maxLoanDscrMin')!) : undefined,
      
      // V2 Closing time
      closingTimeMin: searchParams.get('closingTimeMin') ? parseInt(searchParams.get('closingTimeMin')!) : undefined,
      closingTimeMax: searchParams.get('closingTimeMax') ? parseInt(searchParams.get('closingTimeMax')!) : undefined,
      
      // Additional Investment Criteria filters
      eligibleBorrower: searchParams.get('eligibleBorrower')?.split(',').filter(Boolean),
      lienPosition: searchParams.get('lienPosition')?.split(',').filter(Boolean),
      rateLock: searchParams.get('rateLock')?.split(',').filter(Boolean),
      rateType: searchParams.get('rateType')?.split(',').filter(Boolean),
      amortization: searchParams.get('amortization')?.split(',').filter(Boolean),
      loanTypeNormalized: searchParams.get('loanTypeNormalized')?.split(',').filter(Boolean),
      loanMinDebtYield: searchParams.get('loanMinDebtYield')?.split(',').filter(Boolean),
      futureFacilities: searchParams.get('futureFacilities')?.split(',').filter(Boolean),
      occupancyRequirements: searchParams.get('occupancyRequirements')?.split(',').filter(Boolean),
      prepayment: searchParams.get('prepayment')?.split(',').filter(Boolean),
      yieldMaintenance: searchParams.get('yieldMaintenance')?.split(',').filter(Boolean),
      ownershipRequirement: searchParams.get('ownershipRequirement')?.split(',').filter(Boolean),
      minimumYieldOnCostMin: searchParams.get('minimumYieldOnCostMin') ? parseFloat(searchParams.get('minimumYieldOnCostMin')!) : undefined,
      minimumYieldOnCostMax: searchParams.get('minimumYieldOnCostMax') ? parseFloat(searchParams.get('minimumYieldOnCostMax')!) : undefined,
      maxLeverageToleranceMin: searchParams.get('maxLeverageToleranceMin') ? parseFloat(searchParams.get('maxLeverageToleranceMin')!) : undefined,
      maxLeverageToleranceMax: searchParams.get('maxLeverageToleranceMax') ? parseFloat(searchParams.get('maxLeverageToleranceMax')!) : undefined,
      minimumIrrMin: searchParams.get('minimumIrrMin') ? parseFloat(searchParams.get('minimumIrrMin')!) : undefined,
      minimumIrrMax: searchParams.get('minimumIrrMax') ? parseFloat(searchParams.get('minimumIrrMax')!) : undefined,
      targetCashOnCashMin: searchParams.get('targetCashOnCashMin') ? parseFloat(searchParams.get('targetCashOnCashMin')!) : undefined,
      targetCashOnCashMax: searchParams.get('targetCashOnCashMax') ? parseFloat(searchParams.get('targetCashOnCashMax')!) : undefined,
      minHoldPeriodYearsMin: searchParams.get('minHoldPeriodYearsMin') ? parseFloat(searchParams.get('minHoldPeriodYearsMin')!) : undefined,
      minHoldPeriodYearsMax: searchParams.get('minHoldPeriodYearsMax') ? parseFloat(searchParams.get('minHoldPeriodYearsMax')!) : undefined,
      maxHoldPeriodYearsMin: searchParams.get('maxHoldPeriodYearsMin') ? parseFloat(searchParams.get('maxHoldPeriodYearsMin')!) : undefined,
      maxHoldPeriodYearsMax: searchParams.get('maxHoldPeriodYearsMax') ? parseFloat(searchParams.get('maxHoldPeriodYearsMax')!) : undefined,
      investmentCriteriaNotes: searchParams.get('investmentCriteriaNotes')?.split(',').filter(Boolean),
      
      // Contact filters
      source: searchParams.get('source')?.split(',').filter(Boolean),
      emailStatus: searchParams.get('emailStatus')?.split(',').filter(Boolean),
      jobTier: searchParams.get('jobTier')?.split(',').filter(Boolean),
      
      // V2 Enrichment filters
      contactType: searchParams.get('contactType')?.split(',').filter(Boolean),
      relationshipOwner: searchParams.get('relationshipOwner')?.split(',').filter(Boolean),
      roleInDecisionMaking: searchParams.get('roleInDecisionMaking')?.split(',').filter(Boolean),
      sourceOfIntroduction: searchParams.get('sourceOfIntroduction')?.split(',').filter(Boolean),
      educationCollege: searchParams.get('educationCollege')?.split(',').filter(Boolean),
      educationCollegeYearGraduated: searchParams.get('educationCollegeYearGraduated')?.split(',').filter(Boolean),
      ageRange: searchParams.get('ageRange')?.split(',').filter(Boolean),
      
      // V2 Boolean flags
      accreditedInvestorStatus: searchParams.get('accreditedInvestorStatus') === 'true' ? true : searchParams.get('accreditedInvestorStatus') === 'false' ? false : undefined,
      hasExecutiveSummary: searchParams.get('hasExecutiveSummary') === 'true' ? true : searchParams.get('hasExecutiveSummary') === 'false' ? false : undefined,
      hasCareerTimeline: searchParams.get('hasCareerTimeline') === 'true' ? true : searchParams.get('hasCareerTimeline') === 'false' ? false : undefined,
      hasAdditionalEmail: searchParams.get('hasAdditionalEmail') === 'true' ? true : searchParams.get('hasAdditionalEmail') === 'false' ? false : undefined,
      hasSecondaryPhone: searchParams.get('hasSecondaryPhone') === 'true' ? true : searchParams.get('hasSecondaryPhone') === 'false' ? false : undefined,
      hasTwitter: searchParams.get('hasTwitter') === 'true' ? true : searchParams.get('hasTwitter') === 'false' ? false : undefined,
      hasFacebook: searchParams.get('hasFacebook') === 'true' ? true : searchParams.get('hasFacebook') === 'false' ? false : undefined,
      hasInstagram: searchParams.get('hasInstagram') === 'true' ? true : searchParams.get('hasInstagram') === 'false' ? false : undefined,
      hasYoutube: searchParams.get('hasYoutube') === 'true' ? true : searchParams.get('hasYoutube') === 'false' ? false : undefined,
      hasHonorableAchievements: searchParams.get('hasHonorableAchievements') === 'true' ? true : searchParams.get('hasHonorableAchievements') === 'false' ? false : undefined,
      hasHobbies: searchParams.get('hasHobbies') === 'true' ? true : searchParams.get('hasHobbies') === 'false' ? false : undefined,
      hasContactAddress: searchParams.get('hasContactAddress') === 'true' ? true : searchParams.get('hasContactAddress') === 'false' ? false : undefined,
      hasContactZipCode: searchParams.get('hasContactZipCode') === 'true' ? true : searchParams.get('hasContactZipCode') === 'false' ? false : undefined,
      
      // Geographic filters (Contact location)
      contactCountries: searchParams.get('contactCountries')?.split(',').filter(Boolean),
      contactStates: searchParams.get('contactStates')?.split(',').filter(Boolean),
      contactCities: searchParams.get('contactCities')?.split(',').filter(Boolean),
      
      // Geographic filters (Investment Criteria)
      regions: searchParams.get('regions')?.split(',').filter(Boolean),
      states: searchParams.get('states')?.split(',').filter(Boolean),
      cities: searchParams.get('cities')?.split(',').filter(Boolean),
      countries: searchParams.get('countries')?.split(',').filter(Boolean),
      
      // Processing status filters
      emailVerificationStatus: searchParams.get('emailVerificationStatus')?.split(',').filter(Boolean),
      contactEnrichmentV2Status: searchParams.get('contactEnrichmentV2Status')?.split(',').filter(Boolean),
      contactInvestmentCriteriaStatus: searchParams.get('contactInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      emailGenerationStatus: searchParams.get('emailGenerationStatus')?.split(',').filter(Boolean),
      emailSendingStatus: searchParams.get('emailSendingStatus')?.split(',').filter(Boolean),
      smartleadSyncStatus: searchParams.get('smartleadSyncStatus')?.split(',').filter(Boolean),
      
      // Company processing status filters (from company table)
      companyWebsiteScrapingStatus: searchParams.get('companyWebsiteScrapingStatus')?.split(',').filter(Boolean),
      companyOverviewV2Status: searchParams.get('companyOverviewV2Status')?.split(',').filter(Boolean),
      companyInvestmentCriteriaStatus: searchParams.get('companyInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      companyHasInvestmentCriteria: searchParams.get('companyHasInvestmentCriteria') === 'true' ? true : searchParams.get('companyHasInvestmentCriteria') === 'false' ? false : undefined,
      
      // Boolean flags
      extracted: searchParams.get('extracted') === 'true' ? true : searchParams.get('extracted') === 'false' ? false : undefined,
      searched: searchParams.get('searched') === 'true' ? true : searchParams.get('searched') === 'false' ? false : undefined,
      emailGenerated: searchParams.get('emailGenerated') === 'true' ? true : searchParams.get('emailGenerated') === 'false' ? false : undefined,
      enriched: searchParams.get('enriched') === 'true' ? true : searchParams.get('enriched') === 'false' ? false : undefined,
      hasSmartleadId: searchParams.get('hasSmartleadId') === 'true' ? true : searchParams.get('hasSmartleadId') === 'false' ? false : undefined,
      
      // Email outreach filters
      hasBeenReachedOut: searchParams.get('hasBeenReachedOut') === 'true' ? true : searchParams.get('hasBeenReachedOut') === 'false' ? false : undefined,
      
      // Additional filters
      notEmptyEmail: searchParams.get('notEmptyEmail') === 'true' ? true : searchParams.get('notEmptyEmail') === 'false' ? false : undefined,
      contactIds: searchParams.get('contactIds')?.split(',').filter(Boolean),
      contactEmails: searchParams.get('contactEmails')?.split(',').filter(Boolean),
      

      
      // NOT filters
      notCapitalPosition: searchParams.get('notCapitalPosition')?.split(',').filter(Boolean),
      notPropertyTypes: searchParams.get('notPropertyTypes')?.split(',').filter(Boolean),
      notStrategies: searchParams.get('notStrategies')?.split(',').filter(Boolean),
      notLoanTypes: searchParams.get('notLoanTypes')?.split(',').filter(Boolean),
      notStructuredLoanTranche: searchParams.get('notStructuredLoanTranche')?.split(',').filter(Boolean),
      notLoanProgram: searchParams.get('notLoanProgram')?.split(',').filter(Boolean),
      notRecourseLoan: searchParams.get('notRecourseLoan')?.split(',').filter(Boolean),
      notSource: searchParams.get('notSource')?.split(',').filter(Boolean),
      notEmailStatus: searchParams.get('notEmailStatus')?.split(',').filter(Boolean),
      notEmailVerificationStatus: searchParams.get('notEmailVerificationStatus')?.split(',').filter(Boolean),
      notContactEnrichmentV2Status: searchParams.get('notContactEnrichmentV2Status')?.split(',').filter(Boolean),
      notContactInvestmentCriteriaStatus: searchParams.get('notContactInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      notEmailGenerationStatus: searchParams.get('notEmailGenerationStatus')?.split(',').filter(Boolean),
      notEmailSendingStatus: searchParams.get('notEmailSendingStatus')?.split(',').filter(Boolean),
      notSmartleadSyncStatus: searchParams.get('notSmartleadSyncStatus')?.split(',').filter(Boolean),
      notContactType: searchParams.get('notContactType')?.split(',').filter(Boolean),
      notEligibleBorrower: searchParams.get('notEligibleBorrower')?.split(',').filter(Boolean),
      notLienPosition: searchParams.get('notLienPosition')?.split(',').filter(Boolean),
      notOwnershipRequirement: searchParams.get('notOwnershipRequirement')?.split(',').filter(Boolean),
      notRateType: searchParams.get('notRateType')?.split(',').filter(Boolean),
      notAmortization: searchParams.get('notAmortization')?.split(',').filter(Boolean),
      
      // NOT filters for company processing status
      notCompanyWebsiteScrapingStatus: searchParams.get('notCompanyWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewV2Status: searchParams.get('notCompanyOverviewV2Status')?.split(',').filter(Boolean),
      notCompanyInvestmentCriteriaStatus: searchParams.get('notCompanyInvestmentCriteriaStatus')?.split(',').filter(Boolean),
    };
    
    console.log('ContactsView: Initializing V2 filters from URL:', urlFilters);
    setFilters(urlFilters);
    // Fetch contacts immediately with the V2 filters
    fetchContacts(urlFilters);
  }, [searchParams]);

  // Load mappings from investment criteria mappings API
  useEffect(() => {
    async function fetchMappings() {
      try {
        const response = await fetch('/api/investment-criteria/mappings');
        if (response.ok) {
          const data = await response.json();
          setMappings(data || {});
        } else {
          console.error("Failed to fetch mappings:", await response.text());
        }
      } catch (error) {
        console.error("Error fetching mappings:", error);
      }
    }

    fetchMappings();
  }, []);

  // Load campaigns from Smartlead
  useEffect(() => {
    const loadCampaigns = async () => {
      setLoadingCampaigns(true);
      try {
        const response = await fetch('/api/smartlead/campaigns');
        if (!response.ok) throw new Error('Failed to fetch campaigns');
        
        const data = await response.json();
        const campaignsList = Array.isArray(data.campaigns) ? data.campaigns : (Array.isArray(data) ? data : []);
        setCampaigns(campaignsList.map((campaign: any, index: number) => {
          // Safely convert id to string
          let campaignId = '';
          if (campaign.id !== null && campaign.id !== undefined && typeof campaign.id !== 'object') {
            campaignId = String(campaign.id);
          } else {
            campaignId = `campaign-${index}`;
          }
          
          return {
            id: campaignId,
            name: (typeof campaign.name === 'string' || typeof campaign.name === 'number') ? String(campaign.name) : `Campaign ${index + 1}`
          };
        }));
      } catch (error) {
        console.error('Error loading campaigns:', error);
        toast({
          title: "Campaign Loading Failed",
          description: "Failed to load Smartlead campaigns",
          variant: "destructive",
        });
      } finally {
        setLoadingCampaigns(false);
      }
    };

    loadCampaigns();
  }, [toast]);

    // Load contacts with V2 filters
  const fetchContacts = async (currentFilters: ContactUnifiedFiltersV2) => {
    console.log('ContactsView: Starting to fetch contacts with V2 filters:', currentFilters);
    setLoading(true);
    try {
      // Build query string with all V2 filter parameters
      const params = new URLSearchParams();
      
      // Add all V2 filter parameters
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value) && value.length > 0) {
            params.append(key, value.join(','));
          } else if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (typeof value === 'number' || typeof value === 'string') {
            params.append(key, value.toString());
          }
        }
      });

      const apiUrl = `/api/contacts/unified-filters-v2?${params.toString()}`;
      console.log('ContactsView: Making V2 API call to:', apiUrl);
      
      const response = await fetch(apiUrl);
      if (response.ok) {
        const data = await response.json();
        // console.log('ContactsView: V2 API response received:', { 
        //   contactCount: data.data?.length || 0, 
        //   total: data.pagination?.total || 0
        // });
        setContacts(data.data || []);
        setTotalContacts(data.pagination?.total || 0);
        setTotalPages(data.pagination?.totalPages || 0);
      } else {
        console.error("ContactsView: Failed to fetch contacts with V2 filters:", await response.text());
        setContacts([]);
        setTotalContacts(0);
        setTotalPages(0);
      }
    } catch (error) {
      console.error("ContactsView: Error fetching contacts with V2 filters:", error);
      setContacts([]);
      setTotalContacts(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  // Handle V2 filter changes
  const handleFiltersChange = (newFilters: ContactUnifiedFiltersV2) => {
    console.log('ContactsView: handleFiltersChange called with V2 filters:', newFilters);
    setFilters(newFilters);
    
    // Fetch contacts with new V2 filters immediately
    fetchContacts(newFilters);
    
    // Update URL with new V2 filters
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'contacts');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    console.log('ContactsView: Updating URL to (V2):', newUrl);
    router.replace(newUrl, { scroll: false });
  };



  

  // Handle clear filters (V2 only)
  const handleClearFilters = () => {
    const clearedFilters: ContactUnifiedFiltersV2 = {
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc'
    };
    setFilters(clearedFilters);
    
    // Fetch contacts with cleared V2 filters immediately
    fetchContacts(clearedFilters);
    
    // Update URL to remove all filter parameters but keep tab
    router.replace('/dashboard/entity?tab=contacts', { scroll: false });
  };

  // Handle pagination (V2)
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    
    // Fetch contacts with new page immediately using V2
    fetchContacts(newFilters);
    
    // Update URL with new page
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'contacts');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    router.replace(newUrl, { scroll: false });
  };

  // Handle pagination size change (V2)
  const handlePaginationSizeChange = (newLimit: number) => {
    const newFilters = { ...filters, limit: newLimit, page: 1 }; // Reset to page 1 when changing size
    setFilters(newFilters);
    
    // Fetch contacts with new limit immediately using V2
    fetchContacts(newFilters);
    
    // Update URL with new limit
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'contacts');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    router.replace(newUrl, { scroll: false });
  };

  // Handle contact selection
  const handleSelectContact = (contactId: number) => {
    // Open contact detail page in a new tab
    window.open(`/dashboard/people/${contactId}`, '_blank');
  };

  // Handle contact selection toggle
  const handleToggleSelection = (contactId: number, event: React.MouseEvent | React.ChangeEvent) => {
    event.stopPropagation();
    setSelectedContacts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(contactId)) {
        newSet.delete(contactId);
      } else {
        newSet.add(contactId);
      }
      return newSet;
    });
  };

  // Handle select all toggle
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedContacts(new Set());
      setSelectAll(false);
    } else {
      const allContactIds = contacts.map(contact => contact.contact_id).filter((id): id is number => id !== undefined);
      setSelectedContacts(new Set(allContactIds));
      setSelectAll(true);
    }
  };

  // Handle contact deletion
  const handleDeleteContact = async (contactId: number) => {
    try {
      const response = await fetch(`/api/contacts?contactId=${contactId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Remove from contacts list
        setContacts(prev => prev.filter(contact => contact.contact_id !== contactId));
        // Remove from selected contacts
        setSelectedContacts(prev => {
          const newSelected = new Set(prev);
          newSelected.delete(contactId);
          return newSelected;
        });
        // Update total count
        setTotalContacts(prev => prev - 1);
        toast({
          title: "Contact deleted",
          description: "The contact has been successfully deleted.",
        });
      } else {
        throw new Error('Failed to delete contact');
      }
    } catch (error) {
      console.error('Error deleting contact:', error);
      toast({
        title: "Error",
        description: "Failed to delete contact. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Update selectAll state based on current selection
  useEffect(() => {
    if (contacts.length === 0) {
      setSelectAll(false);
      return;
    }
    
    const allContactIds = contacts.map(contact => contact.contact_id).filter((id): id is number => id !== undefined);
    const allSelected = allContactIds.every(id => selectedContacts.has(id));
    setSelectAll(allSelected);
  }, [selectedContacts, contacts]);

    // Handle processing operations
  const executeProcessingJob = async (stage: ContactProcessingStage) => {
    if (selectedContacts.size === 0) return;

    const contactIds = Array.from(selectedContacts);
    
    // Update processing state
    setProcessingJobs(prev => 
      prev.map(job => 
        job.stage === stage 
          ? { ...job, isExecuting: true }
          : job
      )
    );

    try {
      // Check for campaign requirement for stages that need it
      if ((stage === 'smartlead_sync' || stage === 'email_generation') && !selectedCampaignId) {
        toast({
          title: "Campaign Required",
          description: `Please select a campaign for ${getStageConfig(stage).title}`,
          variant: "destructive",
        });
        return;
      }

      // Handle all processing stages through the processing trigger API
      const jobOptions: any = {
        multiIds: contactIds,
        limit: contactIds.length, // Use the actual number of selected contacts as the limit
        batchSize: Math.min(100, contactIds.length), // Use batchSize for concurrent processing within batches
        filters: buildFilters()
      }

      // Add campaign ID for stages that need it
      if ((stage === 'email_generation' || stage === 'smartlead_sync') && selectedCampaignId) {
        jobOptions.campaignId = selectedCampaignId
      }

      const response = await fetch('/api/processing/queue/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          processorType: stage,
          options: {
            limit: contactIds.length,
            multiIds: contactIds,
            batchSize: Math.min(100, contactIds.length),
            entityType: 'contact',
            ...jobOptions
          }
        })
      });

      const data = await response.json();
      if (data.success) {
        // Refresh contacts to show updated status
        toast({
          title: "Processing Started",
          description: `Successfully triggered ${getStageConfig(stage).title} for ${contactIds.length} contact${contactIds.length !== 1 ? 's' : ''}`,
        });
        // Refresh contacts after a short delay to show updated status
        setTimeout(() => {
          fetchContacts(filters);
        }, 2000); // Wait 2 seconds for processing to start
      } else {
        console.error(`Failed to trigger ${stage}:`, data.error);
        toast({
          title: "Processing Failed",
          description: `Failed to trigger ${getStageConfig(stage).title}: ${data.error}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(`Error executing ${stage}:`, error);
      toast({
        title: "Processing Error",
        description: `Error executing ${getStageConfig(stage).title}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      // Reset processing state
      setProcessingJobs(prev => 
        prev.map(job => 
          job.stage === stage 
            ? { ...job, isExecuting: false }
            : job
      )
      );
    }
  };

  // Build filters for processing - comprehensive mapping of all V2 filters
  const buildFilters = () => {
    const filterObject: any = {};
    
    // Search filters
    if (filters.fullNameSearch) filterObject.fullNameSearch = filters.fullNameSearch;
    if (filters.emailSearch) filterObject.emailSearch = filters.emailSearch;
    if (filters.companyNameSearch) filterObject.companyNameSearch = filters.companyNameSearch;
    
    // Core Contact Table Filters
    if (filters.source?.length) filterObject.source = filters.source;
    if (filters.emailStatus?.length) filterObject.emailStatus = filters.emailStatus;
    if (filters.jobTier?.length) filterObject.jobTier = filters.jobTier;
    if (filters.contactCountries?.length) filterObject.contactCountries = filters.contactCountries;
    if (filters.contactStates?.length) filterObject.contactStates = filters.contactStates;
    if (filters.contactCities?.length) filterObject.contactCities = filters.contactCities;
    
    // Processing Status Filters
    if (filters.emailVerificationStatus?.length) filterObject.emailVerificationStatus = filters.emailVerificationStatus;
    if (filters.contactEnrichmentV2Status?.length) filterObject.contactEnrichmentV2Status = filters.contactEnrichmentV2Status;
    if (filters.contactInvestmentCriteriaStatus?.length) filterObject.contactInvestmentCriteriaStatus = filters.contactInvestmentCriteriaStatus;
    if (filters.emailGenerationStatus?.length) filterObject.emailGenerationStatus = filters.emailGenerationStatus;
    if (filters.emailSendingStatus?.length) filterObject.emailSendingStatus = filters.emailSendingStatus;
    if (filters.smartleadSyncStatus?.length) filterObject.smartleadSyncStatus = filters.smartleadSyncStatus;
    
    // Boolean Processing Flags
    if (filters.extracted !== undefined) filterObject.extracted = filters.extracted;
    if (filters.searched !== undefined) filterObject.searched = filters.searched;
    if (filters.emailGenerated !== undefined) filterObject.emailGenerated = filters.emailGenerated;
    if (filters.enriched !== undefined) filterObject.enriched = filters.enriched;
    if (filters.hasSmartleadId !== undefined) filterObject.hasSmartleadId = filters.hasSmartleadId;
    
    // Contact Enrichment V2 Fields
    if (filters.contactType?.length) filterObject.contactType = filters.contactType;
    if (filters.relationshipOwner?.length) filterObject.relationshipOwner = filters.relationshipOwner;
    if (filters.roleInDecisionMaking?.length) filterObject.roleInDecisionMaking = filters.roleInDecisionMaking;
    if (filters.sourceOfIntroduction?.length) filterObject.sourceOfIntroduction = filters.sourceOfIntroduction;
    if (filters.accreditedInvestorStatus !== undefined) filterObject.accreditedInvestorStatus = filters.accreditedInvestorStatus;
    if (filters.hasExecutiveSummary !== undefined) filterObject.hasExecutiveSummary = filters.hasExecutiveSummary;
    if (filters.hasCareerTimeline !== undefined) filterObject.hasCareerTimeline = filters.hasCareerTimeline;
    if (filters.hasAdditionalEmail !== undefined) filterObject.hasAdditionalEmail = filters.hasAdditionalEmail;
    if (filters.hasSecondaryPhone !== undefined) filterObject.hasSecondaryPhone = filters.hasSecondaryPhone;
    
    // Social Media Filters
    if (filters.hasTwitter !== undefined) filterObject.hasTwitter = filters.hasTwitter;
    if (filters.hasFacebook !== undefined) filterObject.hasFacebook = filters.hasFacebook;
    if (filters.hasInstagram !== undefined) filterObject.hasInstagram = filters.hasInstagram;
    if (filters.hasYoutube !== undefined) filterObject.hasYoutube = filters.hasYoutube;
    
    // Education Filters
    if (filters.educationCollege?.length) filterObject.educationCollege = filters.educationCollege;
    if (filters.educationCollegeYearGraduated?.length) filterObject.educationCollegeYearGraduated = filters.educationCollegeYearGraduated;
    
    // Personal Details
    if (filters.hasHonorableAchievements !== undefined) filterObject.hasHonorableAchievements = filters.hasHonorableAchievements;
    if (filters.hasHobbies !== undefined) filterObject.hasHobbies = filters.hasHobbies;
    if (filters.ageRange?.length) filterObject.ageRange = filters.ageRange;
    if (filters.hasContactAddress !== undefined) filterObject.hasContactAddress = filters.hasContactAddress;
    if (filters.hasContactZipCode !== undefined) filterObject.hasContactZipCode = filters.hasContactZipCode;
    
    // Company Processor Flags
    if (filters.companyWebsiteScrapingStatus?.length) filterObject.companyWebsiteScrapingStatus = filters.companyWebsiteScrapingStatus;
    if (filters.companyOverviewV2Status?.length) filterObject.companyOverviewV2Status = filters.companyOverviewV2Status;
    if (filters.companyInvestmentCriteriaStatus?.length) filterObject.companyInvestmentCriteriaStatus = filters.companyInvestmentCriteriaStatus;
    if (filters.companyHasInvestmentCriteria !== undefined) filterObject.companyHasInvestmentCriteria = filters.companyHasInvestmentCriteria;
    
    // Investment Criteria Central - Deal Scope
    if (filters.capitalPosition?.length) filterObject.capitalPosition = filters.capitalPosition;
    if (filters.dealSizeMin !== undefined) filterObject.dealSizeMin = filters.dealSizeMin;
    if (filters.dealSizeMax !== undefined) filterObject.dealSizeMax = filters.dealSizeMax;
    
    // Investment Criteria Central - Geography
    if (filters.regions?.length) filterObject.regions = filters.regions;
    if (filters.states?.length) filterObject.states = filters.states;
    if (filters.cities?.length) filterObject.cities = filters.cities;
    if (filters.countries?.length) filterObject.countries = filters.countries;
    
    // Investment Criteria Central - Property & Strategy
    if (filters.propertyTypes?.length) filterObject.propertyTypes = filters.propertyTypes;
    if (filters.propertySubcategories?.length) filterObject.propertySubcategories = filters.propertySubcategories;
    if (filters.strategies?.length) filterObject.strategies = filters.strategies;
    if (filters.decisionMakingProcess?.length) filterObject.decisionMakingProcess = filters.decisionMakingProcess;
    
    // Investment Criteria Central - Return Targets
    if (filters.targetReturnMin !== undefined) filterObject.targetReturnMin = filters.targetReturnMin;
    if (filters.targetReturnMax !== undefined) filterObject.targetReturnMax = filters.targetReturnMax;
    
    // Investment Criteria Debt - Loan Types & Programs
    if (filters.loanTypes?.length) filterObject.loanTypes = filters.loanTypes;
    if (filters.loanProgram?.length) filterObject.loanProgram = filters.loanProgram;
    if (filters.structuredLoanTranche?.length) filterObject.structuredLoanTranche = filters.structuredLoanTranche;
    if (filters.recourseLoan?.length) filterObject.recourseLoan = filters.recourseLoan;
    
    // Investment Criteria Debt - Borrower & Terms
    if (filters.eligibleBorrower?.length) filterObject.eligibleBorrower = filters.eligibleBorrower;
    if (filters.lienPosition?.length) filterObject.lienPosition = filters.lienPosition;
    if (filters.rateLock?.length) filterObject.rateLock = filters.rateLock;
    if (filters.rateType?.length) filterObject.rateType = filters.rateType;
    if (filters.amortization?.length) filterObject.amortization = filters.amortization;
    if (filters.loanTypeNormalized?.length) filterObject.loanTypeNormalized = filters.loanTypeNormalized;
    if (filters.loanMinDebtYield?.length) filterObject.loanMinDebtYield = filters.loanMinDebtYield;
    if (filters.futureFacilities?.length) filterObject.futureFacilities = filters.futureFacilities;
    if (filters.occupancyRequirements?.length) filterObject.occupancyRequirements = filters.occupancyRequirements;
    if (filters.prepayment?.length) filterObject.prepayment = filters.prepayment;
    if (filters.yieldMaintenance?.length) filterObject.yieldMaintenance = filters.yieldMaintenance;
    
    // Investment Criteria Debt - Loan Terms (Ranges)
    if (filters.minLoanTermMin !== undefined) filterObject.minLoanTermMin = filters.minLoanTermMin;
    if (filters.minLoanTermMax !== undefined) filterObject.minLoanTermMax = filters.minLoanTermMax;
    if (filters.maxLoanTermMin !== undefined) filterObject.maxLoanTermMin = filters.maxLoanTermMin;
    if (filters.maxLoanTermMax !== undefined) filterObject.maxLoanTermMax = filters.maxLoanTermMax;
    
    // Investment Criteria Debt - Interest Rate Ranges
    if (filters.loanInterestRateSofrMin !== undefined) filterObject.loanInterestRateSofrMin = filters.loanInterestRateSofrMin;
    if (filters.loanInterestRateSofrMax !== undefined) filterObject.loanInterestRateSofrMax = filters.loanInterestRateSofrMax;
    if (filters.loanInterestRateWsjMin !== undefined) filterObject.loanInterestRateWsjMin = filters.loanInterestRateWsjMin;
    if (filters.loanInterestRateWsjMax !== undefined) filterObject.loanInterestRateWsjMax = filters.loanInterestRateWsjMax;
    if (filters.loanInterestRatePrimeMin !== undefined) filterObject.loanInterestRatePrimeMin = filters.loanInterestRatePrimeMin;
    if (filters.loanInterestRatePrimeMax !== undefined) filterObject.loanInterestRatePrimeMax = filters.loanInterestRatePrimeMax;
    if (filters.loanInterestRate3ytMin !== undefined) filterObject.loanInterestRate3ytMin = filters.loanInterestRate3ytMin;
    if (filters.loanInterestRate3ytMax !== undefined) filterObject.loanInterestRate3ytMax = filters.loanInterestRate3ytMax;
    if (filters.loanInterestRate5ytMin !== undefined) filterObject.loanInterestRate5ytMin = filters.loanInterestRate5ytMin;
    if (filters.loanInterestRate5ytMax !== undefined) filterObject.loanInterestRate5ytMax = filters.loanInterestRate5ytMax;
    if (filters.loanInterestRate10ytMin !== undefined) filterObject.loanInterestRate10ytMin = filters.loanInterestRate10ytMin;
    if (filters.loanInterestRate10ytMax !== undefined) filterObject.loanInterestRate10ytMax = filters.loanInterestRate10ytMax;
    if (filters.loanInterestRate30ytMin !== undefined) filterObject.loanInterestRate30ytMin = filters.loanInterestRate30ytMin;
    if (filters.loanInterestRate30ytMax !== undefined) filterObject.loanInterestRate30ytMax = filters.loanInterestRate30ytMax;
    
    // Investment Criteria Debt - LTV & LTC Ranges
    if (filters.loanToValueMinMin !== undefined) filterObject.loanToValueMinMin = filters.loanToValueMinMin;
    if (filters.loanToValueMinMax !== undefined) filterObject.loanToValueMinMax = filters.loanToValueMinMax;
    if (filters.loanToValueMaxMin !== undefined) filterObject.loanToValueMaxMin = filters.loanToValueMaxMin;
    if (filters.loanToValueMaxMax !== undefined) filterObject.loanToValueMaxMax = filters.loanToValueMaxMax;
    if (filters.loanToCostMinMin !== undefined) filterObject.loanToCostMinMin = filters.loanToCostMinMin;
    if (filters.loanToCostMinMax !== undefined) filterObject.loanToCostMinMax = filters.loanToCostMinMax;
    if (filters.loanToCostMaxMin !== undefined) filterObject.loanToCostMaxMin = filters.loanToCostMaxMin;
    if (filters.loanToCostMaxMax !== undefined) filterObject.loanToCostMaxMax = filters.loanToCostMaxMax;
    
    // Investment Criteria Debt - Fee Ranges
    if (filters.loanOriginationMaxFeeMin !== undefined) filterObject.loanOriginationMaxFeeMin = filters.loanOriginationMaxFeeMin;
    if (filters.loanOriginationMaxFeeMax !== undefined) filterObject.loanOriginationMaxFeeMax = filters.loanOriginationMaxFeeMax;
    if (filters.loanOriginationMinFeeMin !== undefined) filterObject.loanOriginationMinFeeMin = filters.loanOriginationMinFeeMin;
    if (filters.loanOriginationMinFeeMax !== undefined) filterObject.loanOriginationMinFeeMax = filters.loanOriginationMinFeeMax;
    if (filters.loanExitMinFeeMin !== undefined) filterObject.loanExitMinFeeMin = filters.loanExitMinFeeMin;
    if (filters.loanExitMinFeeMax !== undefined) filterObject.loanExitMinFeeMax = filters.loanExitMinFeeMax;
    if (filters.loanExitMaxFeeMin !== undefined) filterObject.loanExitMaxFeeMin = filters.loanExitMaxFeeMin;
    if (filters.loanExitMaxFeeMax !== undefined) filterObject.loanExitMaxFeeMax = filters.loanExitMaxFeeMax;
    
    // Investment Criteria Debt - DSCR Ranges
    if (filters.minLoanDscrMin !== undefined) filterObject.minLoanDscrMin = filters.minLoanDscrMin;
    if (filters.minLoanDscrMax !== undefined) filterObject.minLoanDscrMax = filters.minLoanDscrMax;
    if (filters.maxLoanDscrMin !== undefined) filterObject.maxLoanDscrMin = filters.maxLoanDscrMin;
    if (filters.maxLoanDscrMax !== undefined) filterObject.maxLoanDscrMax = filters.maxLoanDscrMax;
    
    // Investment Criteria Debt - Closing Time
    if (filters.closingTimeMin !== undefined) filterObject.closingTimeMin = filters.closingTimeMin;
    if (filters.closingTimeMax !== undefined) filterObject.closingTimeMax = filters.closingTimeMax;
    
    // Investment Criteria Equity - Ownership & Returns
    if (filters.ownershipRequirement?.length) filterObject.ownershipRequirement = filters.ownershipRequirement;
    if (filters.minimumYieldOnCostMin !== undefined) filterObject.minimumYieldOnCostMin = filters.minimumYieldOnCostMin;
    if (filters.minimumYieldOnCostMax !== undefined) filterObject.minimumYieldOnCostMax = filters.minimumYieldOnCostMax;
    if (filters.maxLeverageToleranceMin !== undefined) filterObject.maxLeverageToleranceMin = filters.maxLeverageToleranceMin;
    if (filters.maxLeverageToleranceMax !== undefined) filterObject.maxLeverageToleranceMax = filters.maxLeverageToleranceMax;
    if (filters.minimumIrrMin !== undefined) filterObject.minimumIrrMin = filters.minimumIrrMin;
    if (filters.minimumIrrMax !== undefined) filterObject.minimumIrrMax = filters.minimumIrrMax;
    if (filters.targetCashOnCashMin !== undefined) filterObject.targetCashOnCashMin = filters.targetCashOnCashMin;
    if (filters.targetCashOnCashMax !== undefined) filterObject.targetCashOnCashMax = filters.targetCashOnCashMax;
    
    // Investment Criteria Equity - Hold Period
    if (filters.minHoldPeriodYearsMin !== undefined) filterObject.minHoldPeriodYearsMin = filters.minHoldPeriodYearsMin;
    if (filters.minHoldPeriodYearsMax !== undefined) filterObject.minHoldPeriodYearsMax = filters.minHoldPeriodYearsMax;
    if (filters.maxHoldPeriodYearsMin !== undefined) filterObject.maxHoldPeriodYearsMin = filters.maxHoldPeriodYearsMin;
    if (filters.maxHoldPeriodYearsMax !== undefined) filterObject.maxHoldPeriodYearsMax = filters.maxHoldPeriodYearsMax;
    
    // Investment Criteria - Additional Fields
    if (filters.investmentCriteriaNotes?.length) filterObject.investmentCriteriaNotes = filters.investmentCriteriaNotes;
    
    // Gmail Outreach
    if (filters.hasBeenReachedOut !== undefined) filterObject.hasBeenReachedOut = filters.hasBeenReachedOut;
    
    // Additional Filters
    if (filters.notEmptyEmail !== undefined) filterObject.notEmptyEmail = filters.notEmptyEmail;
    if (filters.contactIds?.length) filterObject.contactIds = filters.contactIds;
    if (filters.contactEmails?.length) filterObject.contactEmails = filters.contactEmails;
    
    // NOT filters - only include most commonly used ones for processing
    if (filters.notSource?.length) filterObject.notSource = filters.notSource;
    if (filters.notEmailStatus?.length) filterObject.notEmailStatus = filters.notEmailStatus;
    if (filters.notEmailVerificationStatus?.length) filterObject.notEmailVerificationStatus = filters.notEmailVerificationStatus;
    if (filters.notContactEnrichmentV2Status?.length) filterObject.notContactEnrichmentV2Status = filters.notContactEnrichmentV2Status;
    if (filters.notContactInvestmentCriteriaStatus?.length) filterObject.notContactInvestmentCriteriaStatus = filters.notContactInvestmentCriteriaStatus;
    if (filters.notSmartleadSyncStatus?.length) filterObject.notSmartleadSyncStatus = filters.notSmartleadSyncStatus;
    if (filters.notCapitalPosition?.length) filterObject.notCapitalPosition = filters.notCapitalPosition;
    if (filters.notPropertyTypes?.length) filterObject.notPropertyTypes = filters.notPropertyTypes;
    if (filters.notStrategies?.length) filterObject.notStrategies = filters.notStrategies;
    if (filters.notLoanTypes?.length) filterObject.notLoanTypes = filters.notLoanTypes;
    if (filters.notContactType?.length) filterObject.notContactType = filters.notContactType;
    if (filters.notCompanyWebsiteScrapingStatus?.length) filterObject.notCompanyWebsiteScrapingStatus = filters.notCompanyWebsiteScrapingStatus;
    if (filters.notCompanyOverviewV2Status?.length) filterObject.notCompanyOverviewV2Status = filters.notCompanyOverviewV2Status;
    if (filters.notCompanyInvestmentCriteriaStatus?.length) filterObject.notCompanyInvestmentCriteriaStatus = filters.notCompanyInvestmentCriteriaStatus;
    
    return filterObject;
  };

  // Get stage configuration
  const getStageConfig = (stage: ContactProcessingStage) => {
    const configs = {
      email_validation: {
        title: 'Email Validation',
        icon: Mail,
        color: 'bg-blue-500',
        description: 'Verify email addresses'
      },
      contact_enrichment: {
        title: 'Contact Enrichment',
        icon: Sparkles,
        color: 'bg-purple-500',
        description: 'Enrich contact data'
      },
      contact_enrichment_v2: {
        title: 'Contact Enrichment V2',
        icon: Sparkles,
        color: 'bg-purple-600',
        description: 'Enhanced contact enrichment with additional fields'
      },
      contact_investment_criteria: {
        title: 'Investment Criteria',
        icon: AlertTriangle,
        color: 'bg-rose-500',
        description: 'Extract personalized investment criteria'
      },
      email_generation: {
        title: 'Email Generation',
        icon: PenTool,
        color: 'bg-pink-500',
        description: 'Generate personalized emails'
      },
      smartlead_sync: {
        title: 'Smartlead Sync',
        icon: Send,
        color: 'bg-green-500',
        description: 'Sync Smartlead IDs'
      }
    };
    return configs[stage];
  };

  // Get processing status for a contact
  const getContactProcessingStatus = (contact: UnifiedContactData, stage: ContactProcessingStage) => {
    switch (stage) {
      case 'email_validation':
        return contact.email_verification_status;
      case 'contact_enrichment_v2':
        return contact.contact_enrichment_v2_status || 'pending';
      case 'contact_investment_criteria':
        return contact.contact_investment_criteria_status || 'pending';
      case 'email_generation':
        return contact.email_generation_status;
      case 'smartlead_sync':
        // For Smartlead sync, we check the smartlead_status field, fallback to pending if null
        return contact.smartlead_status || 'pending';
      default:
        return null;
    }
  };

  // Get status summary for selected contacts
  const getSelectedContactsStatusSummary = (stage: ContactProcessingStage) => {
    if (selectedContacts.size === 0) return null;

    const statusCounts: { [key: string]: number } = {};
    let total = 0;

    contacts.forEach(contact => {
      if (contact.contact_id && selectedContacts.has(contact.contact_id)) {
        const status = getContactProcessingStatus(contact, stage);
        if (status) {
          statusCounts[status] = (statusCounts[status] || 0) + 1;
        }
        total++;
      }
    });

    return { statusCounts, total };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="w-full px-6 py-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Contacts</h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage and explore your contact database
            </p>
          </div>
          <Button
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
            onClick={handleAddContact}
          >
            <UserPlus className="h-4 w-4" />
            Add Contact
          </Button>
        </header>

        {/* V2 Contact Filters Only */}
        <ContactUnifiedFiltersV2Component
          filters={filters}
          mappings={mappings}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
          isLoading={loading}
        />

        {/* Selection and Processing Controls */}
        {contacts.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={() => handleSelectAll()}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium">
                      {selectedContacts.size > 0 
                        ? `${selectedContacts.size} contact${selectedContacts.size !== 1 ? 's' : ''} selected`
                        : 'Select contacts for processing'
                      }
                    </span>
                  </div>
                  {selectedContacts.size > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedContacts(new Set())}
                    >
                      Clear Selection
                    </Button>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            
            {selectedContacts.size > 0 && (
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    Processing operations for {selectedContacts.size} selected contact{selectedContacts.size !== 1 ? 's' : ''}
                    {loadingSelectedStats && (
                      <span className="ml-2 text-blue-600">
                        <RefreshCw className="h-3 w-3 inline animate-spin mr-1" />
                        Loading stats...
                      </span>
                    )}
                  </div>
                  
                  {/* Campaign Selection for Smartlead Sync and Email Generation */}
                  <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Send className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">Campaign Selection</span>
                    </div>
                    <Select
                      value={selectedCampaignId}
                      onValueChange={setSelectedCampaignId}
                      disabled={loadingCampaigns}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={loadingCampaigns ? "Loading campaigns..." : "Select campaign for Smartlead sync and Email Generation"} />
                      </SelectTrigger>
                      <SelectContent>
                        {campaigns.map(campaign => (
                          <SelectItem key={campaign.id} value={campaign.id}>
                            {campaign.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {selectedCampaignId && (
                      <p className="text-xs text-green-600 mt-1">
                        Campaign selected: {campaigns.find(c => c.id === selectedCampaignId)?.name}
                      </p>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-5 gap-4">
                    {processingJobs.map((job) => {
                      const config = getStageConfig(job.stage);
                      const IconComponent = config.icon;
                      const statusSummary = getSelectedContactsStatusSummary(job.stage);
                      
                      return (
                        <Card key={job.stage} className="border-l-4" style={{ borderLeftColor: config.color.replace('bg-', '') }}>
                          <CardContent className="p-4">
                            <div className="flex items-center gap-2 mb-3">
                              <IconComponent className="h-4 w-4" />
                              <h3 className="font-medium">{config.title}</h3>
                            </div>
                            
                            {statusSummary && job.stage !== 'smartlead_sync' && (
                              <div className="mb-3 text-xs text-gray-600">
                                <div className="flex flex-wrap gap-1 mb-2">
                                  {Object.entries(statusSummary.statusCounts).map(([status, count]) => (
                                    <Badge key={status} variant="outline" className="text-xs">
                                      {status}: {count}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {job.stage === 'smartlead_sync' && (
                              <div className="mb-3 text-xs text-gray-600">
                                {!selectedCampaignId && (
                                  <p className="text-amber-600 mt-1">⚠️ Select campaign above</p>
                                )}
                              </div>
                            )}
                            
                            {job.stage === 'email_generation' && (
                              <div className="mb-3 text-xs text-gray-600">
                                {!selectedCampaignId && (
                                  <p className="text-amber-600 mt-1">⚠️ Select campaign above</p>
                                )}
                              </div>
                            )}
                            
                            <Button
                              onClick={() => executeProcessingJob(job.stage)}
                              disabled={job.isExecuting || ((job.stage === 'smartlead_sync' || job.stage === 'email_generation') && !selectedCampaignId)}
                              className="w-full"
                              size="sm"
                            >
                              {job.isExecuting ? (
                                <>
                                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                <>
                                  <Play className="h-4 w-4 mr-2" />
                                  Process {selectedContacts.size} Contact{selectedContacts.size !== 1 ? 's' : ''}
                                </>
                              )}
                            </Button>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        )}

        {/* Results Summary */}
          <div className="mb-4 flex items-center justify-between">
          <div className="text-sm text-gray-600 flex items-center">
            {loading ? (
              <span>Loading contacts...</span>
            ) : (
              <span>
                Showing {contacts.length} of {totalContacts.toLocaleString()} contacts
                {(filters.fullNameSearch || filters.emailSearch || filters.companyNameSearch) && (
                  <span className="text-blue-600">
                    {filters.fullNameSearch && ` matching name "${filters.fullNameSearch}"`}
                    {filters.emailSearch && ` matching email "${filters.emailSearch}"`}
                    {filters.companyNameSearch && ` matching company "${filters.companyNameSearch}"`}
                  </span>
                )}  
              </span>
            )}
          </div>
          <div className="flex items-center gap-4">
            <PaginationSizeSelector
              currentSize={filters.limit || 25}
              onSizeChange={handlePaginationSizeChange}
            />
            {totalPages > 1 && (
              <Pagination
                currentPage={filters.page || 1}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>

        {/* Contacts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          {loading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))
          ) : contacts.length === 0 ? (
            // No results
            <div className="col-span-full text-center py-12">
              <img 
                src="/api/placeholder/200/200" 
                alt="No contacts found" 
                className="mx-auto mb-4 opacity-50"
              />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No contacts found</h3>
              <p className="text-gray-500 mb-4">
                Try adjusting your filters or search terms to find contacts.
              </p>
              <Button
                onClick={handleClearFilters}
                variant="outline"
                className="mx-auto"
              >
                Clear all filters
              </Button>
            </div>
          ) : (
            // Contact cards
            contacts.map((contact) => (
              <ContactCard 
                key={contact.contact_id} 
                contact={contact} 
                onSelectContact={handleSelectContact}
                isSelected={contact.contact_id ? selectedContacts.has(contact.contact_id) : false}
                onToggleSelection={handleToggleSelection}
                onDeleteContact={handleDeleteContact}
                showDeleteButton={true}
              />
            ))
          )}
        </div>


      </main>
    </div>
  );
}
