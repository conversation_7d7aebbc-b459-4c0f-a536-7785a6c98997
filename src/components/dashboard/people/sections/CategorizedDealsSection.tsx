"use client"

import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON> 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  RefreshCw, 
  ExternalLink,
  DollarSign,
  MapPin,
  Building2,
  TrendingUp,
  Calendar,
  FileText,
  Newspaper
} from 'lucide-react';
import { toast } from "sonner";
import Link from "next/link";

interface CategorizedCriteria {
  deal_id: string;
  criteria_id: number;
  capital_position: string[];
  state: string[];
  property_type: string[];
  strategy: string[];
  deal_size: string;
  target_return: string | null;
  entity_type: string;
  score?: number;
  best_score?: number;
  irr?: number;
  equity_multiple?: number;
  deal_type?: string;
  buyer_name?: string;
  seller_name?: string;
  closing_date?: string;
}

interface CategorizedNews {
  news_id: string | number;
  headline?: string;
  summary?: string;
  deal_size?: number;
  publication_date?: string;
  source_name?: string;
  source_url?: string;
  location_city?: string;
  location_state?: string;
  property_type?: string;
  deal_type?: string;
  buyer_name?: string;
  seller_name?: string;
  score?: number;
}

interface CategorizedData {
  analysis: {
    totalDeals: number;
    totalMatchedCriteria: number;
    totalCriteria: number;
  };
  categorizedCriteria: CategorizedCriteria[];
  categorizedNews?: CategorizedNews[];
}

interface CategorizedDealsSectionProps {
  contactId: string | number;
}

const CategorizedDealsSection: React.FC<CategorizedDealsSectionProps> = ({
  contactId
}) => {
  const [data, setData] = useState<CategorizedData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadCategorizedData = async () => {
    if (!contactId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Use V2 matching API for deals
      const dealsResponse = await fetch(`/api/matching-v2/deals-for-contact/${contactId}`);
      if (!dealsResponse.ok) {
        throw new Error('Failed to fetch V2 matching deals');
      }
      
      const dealsResult = await dealsResponse.json();
      
      // Use existing articles API for news
      const newsResponse = await fetch(`/api/matching/articles-for-contact/${contactId}`);
      if (!newsResponse.ok) {
        throw new Error('Failed to fetch matching articles');
      }
      
      const newsResult = await newsResponse.json();
      
      // Transform V2 deals data to match our component's expected format
      const transformedData: CategorizedData = {
        analysis: {
          totalDeals: dealsResult.matches?.length || 0,
          totalMatchedCriteria: dealsResult.matches?.filter((deal: any) => deal.score > 0)?.length || 0,
          totalCriteria: dealsResult.matches?.length || 0
        },
        categorizedCriteria: dealsResult.matches?.map((deal: any) => ({
          deal_id: deal.deal_id?.toString() || '',
          criteria_id: deal.investment_criteria_id || 0,
          capital_position: deal.ask_capital_position ? [deal.ask_capital_position] : [],
          state: deal.state ? [deal.state] : [],
          property_type: deal.property_type ? [deal.property_type] : [],
          strategy: deal.strategy ? [deal.strategy] : [],
          deal_size: deal.ask_amount ? `$${(deal.ask_amount / 1000000).toFixed(1)}M` : 'N/A',
          target_return: deal.common_equity_internal_rate_of_return_irr ? (deal.common_equity_internal_rate_of_return_irr * 100).toFixed(1) + '%' : null,
          entity_type: 'deal',
          score: deal.score || 0,
          best_score: deal.best_score || 0,
          irr: deal.total_internal_rate_of_return_irr || 0,
          equity_multiple: deal.total_equity_multiple || 0,
          deal_type: deal.deal_stage || '',
          buyer_name: deal.buyer_name || '',
          seller_name: deal.seller_name || '',
          closing_date: deal.date_received || ''
        })) || [],
        categorizedNews: newsResult.matches?.map((news: any) => ({
          news_id: news.article_id || news.news_id || '',
          headline: news.headline || news.title || '',
          summary: news.summary || news.description || '',
          deal_size: news.deal_size || undefined,
          publication_date: news.publication_date || news.published_date || '',
          source_name: news.source_name || news.source || '',
          source_url: news.source_url || news.url || '',
          location_city: news.location_city || news.city || '',
          location_state: news.location_state || news.state || '',
          property_type: news.property_type || '',
          deal_type: news.deal_type || '',
          buyer_name: news.buyer_name || '',
          seller_name: news.seller_name || '',
          score: news.score || 0
        })) || []
      };
      
      setData(transformedData);
    } catch (error) {
      console.error('Error loading V2 categorized data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setError(errorMessage);
      toast.error('Failed to load V2 categorized deals and news');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategorizedData();
  }, [contactId]);

  const formatDealSize = (dealSize: string) => {
    if (!dealSize) return 'N/A';
    return dealSize.startsWith('$') ? dealSize : `$${dealSize}`;
  };

  const formatTargetReturn = (targetReturn: string | null) => {
    if (!targetReturn || targetReturn === 'null') return 'N/A';
    const returnValue = parseFloat(targetReturn);
    if (isNaN(returnValue)) return 'N/A';
    return `${(returnValue * 100).toFixed(1)}%`;
  };

  if (loading) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl font-semibold text-gray-900">
            <Building2 className="h-5 w-5" />
            AI Email Context - V2 Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-3" />
              <p className="text-gray-600 font-medium">Loading V2 categorized data...</p>
              <p className="text-sm text-gray-500 mt-1">Fetching enhanced matching data from V2 APIs</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl font-semibold text-gray-900">
            <Building2 className="h-5 w-5" />
            AI Email Context - V2 Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="p-4 bg-red-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
              <Building2 className="h-10 w-10 text-red-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load V2 Data</h3>
            <p className="text-red-600 mb-6 max-w-md mx-auto">{error}</p>
            <Button onClick={loadCategorizedData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Loading V2 Data
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl font-semibold text-gray-900">
            <Building2 className="h-5 w-5" />
            AI Email Context - V2 Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
              <Building2 className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No V2 Data Available</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              No categorized data available yet. V2 APIs will populate this section when data is processed.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2 text-xl font-semibold text-gray-900">
              <Building2 className="h-5 w-5" />
              AI Email Context - V2 Data
            </CardTitle>
            <p className="text-sm text-gray-500 mt-1">
              Enhanced matching data from V2 APIs for personalized email generation
            </p>
          </div>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-6 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{data.analysis.totalDeals}</div>
                <div className="text-gray-600 font-medium">Total Deals</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{data.analysis.totalMatchedCriteria}</div>
                <div className="text-gray-600 font-medium">High Matches</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{data.analysis.totalCriteria}</div>
                <div className="text-gray-600 font-medium">Criteria</div>
              </div>
            </div>
            <Button 
              onClick={loadCategorizedData} 
              variant="outline" 
              size="sm"
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh V2 Data
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Deals Section */}
        {data.categorizedCriteria && data.categorizedCriteria.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Building2 className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Matching Deals</h3>
                  <p className="text-sm text-gray-500">AI-curated deals based on contact criteria</p>
                </div>
              </div>
              <Badge variant="outline" className="text-sm px-3 py-1">
                {data.categorizedCriteria.length} Deals
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {data.categorizedCriteria.map((deal, index) => (
                <Link 
                  key={`${deal.deal_id}-${index}`} 
                  href={`/dashboard/deals/${deal.deal_id}`}
                  className="block group"
                >
                  <Card className="h-full hover:shadow-lg transition-all duration-200 cursor-pointer border border-gray-200 hover:border-blue-300 group-hover:scale-[1.02]">
                    <CardContent className="p-5">
                      {/* Header with Score and Deal ID */}
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 bg-blue-100 rounded-md">
                            <Building2 className="h-4 w-4 text-blue-600" />
                          </div>
                          <span className="font-semibold text-sm text-gray-900">Deal #{deal.deal_id}</span>
                        </div>
                        {deal.score && (
                          <div className="flex flex-col items-end gap-1">
                            <Badge 
                              variant={deal.score >= 80 ? "default" : deal.score >= 60 ? "secondary" : "outline"}
                              className={`text-xs px-2 py-1 ${
                                deal.score >= 80 ? 'bg-green-600 hover:bg-green-700' : 
                                deal.score >= 60 ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-100 text-gray-700'
                              }`}
                            >
                              {deal.score}% Match
                            </Badge>
                            {deal.best_score && deal.best_score > deal.score && (
                              <span className="text-xs text-gray-500">Best: {deal.best_score}%</span>
                            )}
                          </div>
                        )}
                      </div>
                      
                      {/* Deal Size and Financial Metrics */}
                      <div className="mb-4">
                        <div className="flex items-center gap-2 mb-3">
                          <DollarSign className="h-4 w-4 text-green-600" />
                          <span className="text-lg font-bold text-gray-900">{formatDealSize(deal.deal_size)}</span>
                        </div>
                        
                        <div className="flex flex-wrap gap-2">
                          {deal.target_return && (
                            <Badge variant="secondary" className="text-xs bg-green-50 text-green-700 border-green-200">
                              {formatTargetReturn(deal.target_return)} Return
                            </Badge>
                          )}
                          {deal.irr && (
                            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                              {(deal.irr * 100).toFixed(1)}% IRR
                            </Badge>
                          )}
                          {deal.equity_multiple && (
                            <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700 border-purple-200">
                              {deal.equity_multiple.toFixed(1)}x Multiple
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      {/* Location and Deal Type */}
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3 text-red-500" />
                          <span className="text-sm text-gray-700 font-medium">
                            {deal.state.join(', ')}
                          </span>
                        </div>
                        
                        {deal.deal_type && (
                          <div className="flex items-center gap-2">
                            <TrendingUp className="h-3 w-3 text-purple-600" />
                            <span className="text-sm text-gray-700 capitalize font-medium">
                              {deal.deal_type}
                            </span>
                          </div>
                        )}
                      </div>
                      
                      {/* Parties Information */}
                      {(deal.buyer_name || deal.seller_name) && (
                        <div className="bg-gray-50 rounded-lg p-3 mb-4">
                          <div className="text-xs text-gray-600 space-y-1">
                            {deal.buyer_name && (
                              <div className="flex items-center gap-2">
                                <span className="font-semibold text-green-700">Buyer:</span> 
                                <span className="text-gray-800">{deal.buyer_name}</span>
                              </div>
                            )}
                            {deal.seller_name && (
                              <div className="flex items-center gap-2">
                                <span className="font-semibold text-red-700">Seller:</span> 
                                <span className="text-gray-800">{deal.seller_name}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                      
                      {/* Criteria Tags */}
                      <div className="space-y-3">
                        {deal.capital_position.length > 0 && (
                          <div>
                            <div className="text-xs font-medium text-gray-600 mb-2">Capital Position</div>
                            <div className="flex flex-wrap gap-1">
                              {deal.capital_position.map((pos, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                  {pos}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {deal.property_type.length > 0 && (
                          <div>
                            <div className="text-xs font-medium text-gray-600 mb-2">Property Types</div>
                            <div className="flex flex-wrap gap-1">
                              {deal.property_type.slice(0, 3).map((type, idx) => (
                                <Badge key={idx} variant="secondary" className="text-xs bg-green-50 text-green-700">
                                  {type}
                                </Badge>
                              ))}
                              {deal.property_type.length > 3 && (
                                <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-600">
                                  +{deal.property_type.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                        
                        {deal.strategy.length > 0 && (
                          <div>
                            <div className="text-xs font-medium text-gray-600 mb-2">Strategies</div>
                            <div className="flex flex-wrap gap-1">
                              {deal.strategy.slice(0, 2).map((strat, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs bg-purple-50 text-purple-700 border-purple-200">
                                  {strat}
                                </Badge>
                              ))}
                              {deal.strategy.length > 2 && (
                                <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600">
                                  +{deal.strategy.length - 2} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* View Deal Link */}
                      <div className="mt-4 pt-3 border-t border-gray-100">
                        <div className="flex items-center justify-between text-xs text-blue-600 group-hover:text-blue-700">
                          <span className="font-medium">View Deal Details</span>
                          <ExternalLink className="h-3 w-3 group-hover:scale-110 transition-transform" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        )}
        
        {/* News Section */}
        {data.categorizedNews && data.categorizedNews.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Newspaper className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Related News</h3>
                  <p className="text-sm text-gray-500">Market insights and deal-related articles</p>
                </div>
              </div>
              <Badge variant="outline" className="text-sm px-3 py-1">
                {data.categorizedNews.length} Articles
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {data.categorizedNews.map((news, index) => (
                <Card key={`${news.news_id}-${index}`} className="border border-gray-200 hover:border-purple-300 hover:shadow-lg transition-all duration-200 group">
                  <CardContent className="p-5">
                    {/* Header with Score */}
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center gap-2">
                        <div className="p-1.5 bg-purple-100 rounded-md">
                          <Newspaper className="h-4 w-4 text-purple-600" />
                        </div>
                        <span className="text-xs text-gray-500">News #{news.news_id}</span>
                      </div>
                      {news.score && (
                        <Badge 
                          variant={news.score >= 80 ? "default" : news.score >= 60 ? "secondary" : "outline"}
                          className={`text-xs px-2 py-1 ${
                            news.score >= 80 ? 'bg-green-600 hover:bg-green-700' : 
                            news.score >= 60 ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-100 text-gray-700'
                          }`}
                        >
                          {news.score}% Match
                        </Badge>
                      )}
                    </div>
                    
                    {/* Headline and Summary */}
                    <div className="mb-4">
                      {news.headline && (
                        <h4 className="font-semibold text-gray-900 text-base line-clamp-2 mb-2 group-hover:text-purple-700 transition-colors">
                          {news.headline}
                        </h4>
                      )}
                      
                      {news.summary && (
                        <p className="text-sm text-gray-600 line-clamp-3 leading-relaxed">
                          {news.summary}
                        </p>
                      )}
                    </div>
                    
                    {/* Deal Information */}
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2">
                        {news.property_type && (
                          <Badge variant="secondary" className="text-xs bg-green-50 text-green-700">
                            {news.property_type}
                          </Badge>
                        )}
                        {news.deal_type && (
                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                            {news.deal_type}
                          </Badge>
                        )}
                        {news.deal_size && (
                          <Badge variant="secondary" className="text-xs bg-orange-50 text-orange-700">
                            ${news.deal_size}M
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    {/* Parties Information */}
                    {(news.buyer_name || news.seller_name) && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        <div className="text-xs text-gray-600 space-y-1">
                          {news.buyer_name && (
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-green-700">Buyer:</span> 
                              <span className="text-gray-800">{news.buyer_name}</span>
                            </div>
                          )}
                          {news.seller_name && (
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-red-700">Seller:</span> 
                              <span className="text-gray-800">{news.seller_name}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* Source and Date */}
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                      <div className="flex items-center gap-4">
                        {news.source_name && (
                          <span className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            {news.source_name}
                          </span>
                        )}
                        
                        {news.publication_date && (
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(news.publication_date).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      
                      {news.location_city && news.location_state && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          <span>{news.location_city}, {news.location_state}</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Read Article Link */}
                    {news.source_url && (
                      <div className="pt-3 border-t border-gray-100">
                        <a 
                          href={news.source_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="flex items-center justify-between text-xs text-purple-600 hover:text-purple-700 group-hover:font-medium transition-all"
                        >
                          <span>Read Full Article</span>
                          <ExternalLink className="h-3 w-3 group-hover:scale-110 transition-transform" />
                        </a>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
        
        {data.categorizedCriteria.length === 0 && (!data.categorizedNews || data.categorizedNews.length === 0) && (
          <div className="text-center py-12">
            <div className="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
              <Building2 className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Matching Data</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              No matching deals or news found for this contact. The AI will analyze available data when processing begins.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CategorizedDealsSection; 