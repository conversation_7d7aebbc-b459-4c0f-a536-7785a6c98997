"use client"

import React, { useState, useRef, useEffect } from 'react';
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  CardHeader, 
  CardTitle,
  CardDescription 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import RichTextEditor, { RichTextEditorRef } from '@/components/common/RichTextEditor';
import { toast } from "sonner";
import { 
  RefreshCw, 
  Send,
  CheckCircle,
  Eye,
  FileText,
  Variable,
  Plus,
  X,
  Code2,
  Type,
  MessageCircle,
  ArrowDownToLine,
  Filter,
  Zap
} from 'lucide-react';
import { Message, Contact, CampaignSequence } from '../types';
import { replaceVariables } from '../utils';
import { cn } from '@/lib/utils';
import { fetchCampaignSequence, syncToSmartlead } from '../services/campaignService';
import CategorizedDealsSection from './CategorizedDealsSection';

interface UnifiedCampaignSequenceSectionProps {
  contactId: string | number;
  contact?: Contact;
  onContactUpdate?: (updatedContact: Contact) => void;
}

const UnifiedCampaignSequenceSection: React.FC<UnifiedCampaignSequenceSectionProps> = ({
  contactId,
  contact,
  onContactUpdate
}) => {
  // State management
  const [messages, setMessages] = useState<Message[]>([]);
  const [campaigns, setCampaigns] = useState<Array<{id: string, name: string}>>([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [loadingCampaigns, setLoadingCampaigns] = useState(false);
  const [loadingSequence, setLoadingSequence] = useState(false);
  const [saving, setSaving] = useState(false);
  
  // Campaign and sequence state
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('');
  const [currentSequence, setCurrentSequence] = useState<CampaignSequence | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<string>('');
  
  // Template and variable state
  const [templateSubject, setTemplateSubject] = useState('');
  const [templateBody, setTemplateBody] = useState('');
  const [variables, setVariables] = useState<Record<string, string>>({});
  const [variableRichModes, setVariableRichModes] = useState<Record<string, boolean>>({});
  
  // UI state
  const [activeTab, setActiveTab] = useState('template');
  const [newVariableKey, setNewVariableKey] = useState('');
  const [newVariableValue, setNewVariableValue] = useState('');
  const [newVariableRichMode, setNewVariableRichMode] = useState(false);
  
  // Refs
  const variableEditorsRef = useRef<Record<string, RichTextEditorRef>>({});
  const newVariableEditorRef = useRef<RichTextEditorRef>(null);
  const previewIframeRef = useRef<HTMLIFrameElement>(null);

  // Load messages for the contact
  const loadMessages = async () => {
    if (!contactId) return;
    
    setLoadingMessages(true);
    try {
      const response = await fetch(`/api/messages?contact_id=${contactId}&limit=100`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      
      const messagesData = await response.json();
      // API now returns messages array directly, not wrapped in data.messages
      const messagesList = Array.isArray(messagesData) ? messagesData : [];
      setMessages(messagesList);
      
      // Extract variables from all messages
      extractVariablesFromMessages(messagesList);
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error('Failed to load contact messages');
    } finally {
      setLoadingMessages(false);
    }
  };

  // Load available campaigns
  const loadCampaigns = async () => {
    setLoadingCampaigns(true);
    try {
      const response = await fetch('/api/smartlead/campaigns');
      if (!response.ok) throw new Error('Failed to fetch campaigns');
      
      const data = await response.json();
      const campaignsList = Array.isArray(data.campaigns) ? data.campaigns : (Array.isArray(data) ? data : []);
      setCampaigns(campaignsList.map((campaign: any, index: number) => {
        // Safely convert id to string
        let campaignId = '';
        if (campaign.id !== null && campaign.id !== undefined && typeof campaign.id !== 'object') {
          campaignId = String(campaign.id);
        } else {
          campaignId = `campaign-${index}`;
        }
        
        return {
          id: campaignId,
          name: (typeof campaign.name === 'string' || typeof campaign.name === 'number') ? String(campaign.name) : `Campaign ${index + 1}`
        };
      }));
    } catch (error) {
      console.error('Error loading campaigns:', error);
      toast.error('Failed to load campaigns');
    } finally {
      setLoadingCampaigns(false);
    }
  };

  // Load campaign sequence
  const loadCampaignSequence = async (campaignId: string) => {
    if (!campaignId || campaignId === 'none') {
      setCurrentSequence(null);
      setTemplateSubject('');
      setTemplateBody('');
      setSelectedVariant('');
      return;
    }
    
    setLoadingSequence(true);
    try {
      const sequenceData = await fetchCampaignSequence(campaignId);
      setCurrentSequence(sequenceData);
      
      if (sequenceData?.sequence_variants?.length) {
        const firstVariant = sequenceData.sequence_variants[0];
        setSelectedVariant(firstVariant.variant_label);
        setTemplateSubject(firstVariant.subject || '');
        setTemplateBody(firstVariant.email_body || '');
        
        // Extract variables from sequence and merge with existing variables
        const sequenceVars = extractAllVariablesFromAllSequences(sequenceData.sequence_variants);
        const templateVars = sequenceData.variables?.map(v => v.name) || [];
        const allSequenceVars = [...new Set([...sequenceVars, ...templateVars])];
        
        // Initialize sequence variables with defaults, but preserve existing values
        const updatedVariables = { ...variables };
        allSequenceVars.forEach(variable => {
          if (updatedVariables[variable] === undefined) {
            const sequenceVar = sequenceData.variables?.find(v => v.name === variable);
            updatedVariables[variable] = sequenceVar?.default_value || getVariableValue(variable, contact) || `[${variable}]`;
          }
        });
        
        setVariables(updatedVariables);
      }
    } catch (error) {
      console.error('Error loading sequence:', error);
      toast.error('Failed to load campaign sequence');
    } finally {
      setLoadingSequence(false);
    }
  };

  // Extract variables from messages
  const extractVariablesFromMessages = (messagesList: Message[]) => {
    const allVariables: Record<string, string> = {};
    
    messagesList.forEach(message => {
      if (message.metadata?.variables) {
        Object.entries(message.metadata.variables).forEach(([key, value]) => {
          if (!allVariables[key] && value) {
            allVariables[key] = String(value);
          }
        });
      }
      
      // Extract variables from subject and body
      const subjectVars = extractVariables(message.subject || '');
      const bodyVars = extractVariables(message.body || '');
      
      [...subjectVars, ...bodyVars].forEach(variable => {
        if (!allVariables[variable]) {
          allVariables[variable] = getVariableValue(variable, contact) || `[${variable}]`;
        }
      });
    });
    
    setVariables(prev => ({ ...allVariables, ...prev }));
  };

  // Extract variables from template strings
  const extractVariables = (template: string): string[] => {
    if (!template) return [];
    
    const matches = template.match(/{{\s*([^}]+)\s*}}/g) || [];
    return matches
      .map(match => match.replace(/^{{\s*|\s*}}$/g, ''))
      .filter((value, index, self) => self.indexOf(value) === index);
  };

  // Extract variables from all sequence variants
  const extractAllVariablesFromAllSequences = (sequenceVariants: any[]): string[] => {
    const allVariables: string[] = [];
    
    sequenceVariants.forEach(variant => {
      const subjectVars = extractVariables(variant.subject || '');
      const bodyVars = extractVariables(variant.email_body || '');
      allVariables.push(...subjectVars, ...bodyVars);
    });
    
    return [...new Set(allVariables)];
  };

  // Get variable value with contact info priority
  const getVariableValue = (variable: string, contactInfo?: Contact | null): string => {
    if (!contactInfo) return '';
    
    const variableMap: Record<string, string> = {
      first_name: contactInfo.first_name || '',
      last_name: contactInfo.last_name || '',
      email: contactInfo.email || '',
      company_name: contactInfo.company_name || '',
      job_title: contactInfo.job_title || '',
      phone_number: contactInfo.phone_number || '',
      company_website: contactInfo.company_website || '',
      industry: contactInfo.industry || ''
    };
    
    return variableMap[variable] || '';
  };

  // Group variables by type
  const groupVariables = (variableList: string[]): { contactVars: string[], customVars: string[] } => {
    const contactVarNames = ['first_name', 'last_name', 'email', 'company_name', 'job_title', 'phone_number', 'company_website', 'industry'];
    const contactVars: string[] = [];
    const customVars: string[] = [];
    
    variableList.forEach(variable => {
      if (contactVarNames.includes(variable)) {
        contactVars.push(variable);
      } else {
        customVars.push(variable);
      }
    });
    
    return { contactVars, customVars };
  };

  // Handle campaign selection
  const handleCampaignChange = (campaignId: string) => {
    setSelectedCampaignId(campaignId);
    loadCampaignSequence(campaignId);
  };

  // Handle variant change
  const handleVariantChange = (variantLabel: string) => {
    if (!currentSequence) return;
    
    const variant = currentSequence.sequence_variants.find(v => v.variant_label === variantLabel);
    if (variant) {
      setSelectedVariant(variantLabel);
      setTemplateSubject(variant.subject || '');
      setTemplateBody(variant.email_body || '');
    }
  };

  // Handle variable change
  const handleVariableChange = (variable: string, value: string) => {
    setVariables(prev => ({
      ...prev,
      [variable]: value
    }));
  };

  // Toggle variable rich mode
  const toggleVariableRichMode = (variableKey: string, richMode: boolean) => {
    setVariableRichModes(prev => ({
      ...prev,
      [variableKey]: richMode
    }));
  };

  // Add new variable
  const addNewVariable = () => {
    if (!newVariableKey.trim()) {
      toast.error('Variable key is required');
      return;
    }
    
    if (variables[newVariableKey]) {
      toast.error('Variable already exists');
      return;
    }
    
    let variableValue = newVariableValue;
    if (newVariableRichMode && newVariableEditorRef.current) {
      try {
        variableValue = newVariableEditorRef.current.getContent();
      } catch (error) {
        console.error('Error getting new variable editor content:', error);
        variableValue = newVariableValue;
      }
    }
    
    setVariables(prev => ({
      ...prev,
      [newVariableKey]: variableValue
    }));
    
    if (newVariableRichMode) {
      setVariableRichModes(prev => ({
        ...prev,
        [newVariableKey]: true
      }));
    }
    
    setNewVariableKey('');
    setNewVariableValue('');
    setNewVariableRichMode(false);
    toast.success('Variable added');
  };

  // Remove variable
  const removeVariable = (key: string) => {
    setVariables(prev => {
      const updated = { ...prev };
      delete updated[key];
      return updated;
    });
    
    setVariableRichModes(prev => {
      const updated = { ...prev };
      delete updated[key];
      return updated;
    });
    
    if (variableEditorsRef.current[key]) {
      delete variableEditorsRef.current[key];
    }
    
    toast.success('Variable removed');
  };

  // Get preview content with variables replaced
  const getPreviewContent = (template: string): string => {
    if (!template) return '';
    
    // First, replace variables
    let processedContent = template.replace(/{{\s*([^}]+)\s*}}/g, (match, variable) => {
      const trimmedVar = variable.trim();
      
      // Prioritize contact variables
      const contactValue = getVariableValue(trimmedVar, contact);
      if (contactValue) return contactValue;
      
      // Use custom variables
      return variables[trimmedVar] || `[${trimmedVar}]`;
    });
    
    // Preserve line breaks by converting \n to <br> tags
    processedContent = processedContent.replace(/\n/g, '<br>');
    
    return processedContent;
  };

  // Sync to Smartlead
  const handleSyncToSmartlead = async () => {
    if (!selectedCampaignId || !contactId) {
      toast.error('Please select a campaign first');
      return;
    }
    
    setSaving(true);
    try {
      // Prepare custom fields with all variables
      const customFields: Record<string, string> = {
        variant_label: selectedVariant,
        campaign_name: campaigns.find(c => c.id === selectedCampaignId)?.name || '',
        ...variables
      };
      
      const syncData = await syncToSmartlead(
        contactId,
        selectedCampaignId,
        templateSubject,
        templateBody,
        customFields
      );
      
      // Update contact data if available
      if (syncData.contact && syncData.contact.smartlead_lead_id && contact && onContactUpdate) {
        const updatedContact = {
          ...contact,
          smartlead_lead_id: syncData.contact.smartlead_lead_id,
          smartlead_status: syncData.contact.smartlead_status,
          last_email_sent_at: new Date().toISOString()
        };
        onContactUpdate(updatedContact);
      }
      
      toast.success('Successfully synced to Smartlead');
    } catch (error) {
      console.error('Error syncing to Smartlead:', error);
      toast.error(`Failed to sync: ${(error as Error).message}`);
    } finally {
      setSaving(false);
    }
  };

  // Save variables to message metadata
  const handleSaveVariables = async () => {
    if (!contactId) return;
    
    setSaving(true);
    try {
      const response = await fetch(`/api/messages?contact_id=${contactId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: templateSubject,
          body: templateBody,
          direction: 'outbound',
          from_email: '<EMAIL>',
          to_email: contact?.email,
          role: 'user',
          smartlead_campaign_id: selectedCampaignId,
          metadata: {
            campaign_id: selectedCampaignId,
            campaign_name: campaigns.find(c => c.id === selectedCampaignId)?.name || '',
            variant_label: selectedVariant,
            variables,
            is_html: Object.values(variableRichModes).some(mode => mode),
            variable_rich_modes: variableRichModes,
            last_edited_at: new Date().toISOString()
          }
        }),
      });

      if (!response.ok) throw new Error('Failed to save message');
      
      toast.success('Variables saved successfully');
      
      // Reload messages to show the updated data
      loadMessages();
    } catch (error) {
      console.error('Error saving variables:', error);
      toast.error('Failed to save variables');
    } finally {
      setSaving(false);
    }
  };

  // Update preview iframe
  useEffect(() => {
    if (previewIframeRef.current && previewIframeRef.current.contentDocument) {
      const doc = previewIframeRef.current.contentDocument;
      
      let content = '';
      if (activeTab === 'preview') {
        content = getPreviewContent(templateBody);
      } else {
        // Template mode - highlight variables and preserve line breaks
        content = templateBody
          .replace(/\n/g, '<br>') // Preserve line breaks
          .replace(/{{\s*([^}]+)\s*}}/g, (match, variable) => {
            return `<span style="background-color: #f0f9ff; border-bottom: 1px dashed #3b82f6; padding: 1px 2px;">${match}</span>`;
          });
      }
      
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
              font-size: 14px;
              line-height: 1.6;
              padding: 16px;
              margin: 0;
              color: #374151;
              background: white;
            }
            p { margin: 0 0 1em 0; }
            ul, ol { margin: 0 0 1em 1.5em; padding: 0; }
            img { max-width: 100%; height: auto; }
            a { color: #2563eb; }
          </style>
        </head>
        <body>${content || '<p style="color: #999; font-style: italic;">No content to display</p>'}</body>
        </html>
      `;
      
      try {
        doc.open();
        doc.write(htmlContent);
        doc.close();
      } catch (error) {
        console.error('Error updating preview:', error);
      }
    }
  }, [templateBody, activeTab, variables, contact]);

  // Load initial data
  useEffect(() => {
    loadMessages();
    loadCampaigns();
  }, [contactId]);

  const currentTemplateVariables = [
    ...extractVariables(templateSubject),
    ...extractVariables(templateBody)
  ];
  const uniqueVariables = [...new Set(currentTemplateVariables)];
  const { contactVars, customVars } = groupVariables(Object.keys(variables));

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg font-medium">Campaign Sequence & Variables</CardTitle>
            <CardDescription className="text-sm text-gray-500">
              Manage campaign sequences, variables, and sync with Smartlead
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={selectedCampaignId}
              onValueChange={handleCampaignChange}
              disabled={loadingCampaigns}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select Campaign" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No Campaign</SelectItem>
                {campaigns.map(campaign => (
                  <SelectItem key={campaign.id} value={campaign.id}>
                    {campaign.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {currentSequence?.sequence_variants?.length && (
              <Select
                value={selectedVariant}
                onValueChange={handleVariantChange}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Variant" />
                </SelectTrigger>
                <SelectContent>
                  {currentSequence.sequence_variants.map(variant => (
                    <SelectItem key={variant.variant_label} value={variant.variant_label}>
                      Step {variant.variant_label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="flex">
          {/* Left Panel: Variables */}
          <div className="w-1/3 p-4 border-r border-gray-200">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Variables ({Object.keys(variables).length})</h3>
                <p className="text-xs text-gray-500">
                  {loadingMessages ? 'Loading...' : `Found from ${messages.length} messages`}
                </p>
              </div>
              
              {/* Contact Variables */}
              {contactVars.length > 0 && (
                <div>
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Contact Variables</h4>
                  <div className="space-y-2">
                    {contactVars.map(key => (
                      <div key={key} className="p-2 bg-green-50 rounded-lg border border-green-200">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-mono text-xs text-gray-800">{key}</span>
                          <Badge className="text-[10px] px-1.5 py-0.5 h-4 bg-green-100 text-green-600">
                            Contact
                          </Badge>
                        </div>
                        <Input
                          value={getVariableValue(key, contact)}
                          readOnly
                          className="text-xs h-7 bg-white border-green-200"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Custom Variables */}
              {customVars.length > 0 && (
                <div>
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Custom Variables</h4>
                  <div className="space-y-2">
                    {customVars.map(key => {
                      const isUsed = uniqueVariables.includes(key);
                      const isRichMode = variableRichModes[key] || false;
                      const currentValue = variables[key] || '';
                      
                      return (
                        <div key={key} className={cn(
                          "p-2 rounded-lg border",
                          isUsed ? "bg-blue-50 border-blue-200" : "bg-gray-50 border-gray-200"
                        )}>
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-1">
                              <span className="font-mono text-xs text-gray-800">{key}</span>
                              <Badge className={cn(
                                "text-[10px] px-1.5 py-0.5 h-4",
                                isUsed ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-600"
                              )}>
                                {isUsed ? 'Used' : 'Unused'}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                variant={isRichMode ? "default" : "outline"}
                                size="sm"
                                onClick={() => toggleVariableRichMode(key, true)}
                                className="h-5 w-5 p-0"
                              >
                                <Code2 className="h-2.5 w-2.5" />
                              </Button>
                              <Button
                                variant={!isRichMode ? "default" : "outline"}
                                size="sm"
                                onClick={() => toggleVariableRichMode(key, false)}
                                className="h-5 w-5 p-0"
                              >
                                <Type className="h-2.5 w-2.5" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeVariable(key)}
                                className="h-5 w-5 p-0 text-red-500 hover:text-red-700"
                              >
                                <X className="h-2.5 w-2.5" />
                              </Button>
                            </div>
                          </div>
                          
                          {isRichMode ? (
                            <RichTextEditor
                              ref={(ref) => {
                                if (ref) variableEditorsRef.current[key] = ref;
                              }}
                              value={currentValue}
                              onChange={(content: string) => handleVariableChange(key, content)}
                              height={80}
                              minimalControls={true}
                            />
                          ) : (
                            <Input
                              value={currentValue}
                              onChange={(e) => handleVariableChange(key, e.target.value)}
                              className="text-xs h-7"
                              placeholder={`Value for ${key}`}
                            />
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
              
              {/* Add New Variable */}
              <div className="p-3 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                <h4 className="text-xs font-medium text-gray-700 mb-2">Add Variable</h4>
                <div className="space-y-2">
                  <Input
                    value={newVariableKey}
                    onChange={(e) => setNewVariableKey(e.target.value)}
                    placeholder="Variable name"
                    className="text-xs h-7"
                  />
                  
                  <div className="flex items-center justify-between mb-1">
                    <Label className="text-xs">Value</Label>
                    <div className="flex items-center gap-1">
                      <Button
                        variant={newVariableRichMode ? "default" : "outline"}
                        size="sm"
                        onClick={() => setNewVariableRichMode(true)}
                        className="h-5 w-5 p-0"
                      >
                        <Code2 className="h-2.5 w-2.5" />
                      </Button>
                      <Button
                        variant={!newVariableRichMode ? "default" : "outline"}
                        size="sm"
                        onClick={() => setNewVariableRichMode(false)}
                        className="h-5 w-5 p-0"
                      >
                        <Type className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </div>
                  
                  {newVariableRichMode ? (
                    <RichTextEditor
                      ref={newVariableEditorRef}
                      value={newVariableValue}
                      onChange={setNewVariableValue}
                      height={80}
                      minimalControls={true}
                    />
                  ) : (
                    <Input
                      value={newVariableValue}
                      onChange={(e) => setNewVariableValue(e.target.value)}
                      placeholder="Variable value"
                      className="text-xs h-7"
                    />
                  )}
                  
                  <Button
                    onClick={addNewVariable}
                    size="sm"
                    className="w-full h-7 text-xs"
                    disabled={!newVariableKey.trim()}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add Variable
                  </Button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Panel: Template and Preview */}
          <div className="flex-1">
            {loadingSequence ? (
              <div className="flex items-center justify-center h-96">
                <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
                <span className="ml-2 text-gray-600">Loading sequence...</span>
              </div>
            ) : !currentSequence ? (
              <div className="flex items-center justify-center h-96 text-center">
                <div>
                  <MessageCircle className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                  <p className="text-lg font-medium text-gray-600 mb-2">Select a Campaign</p>
                  <p className="text-gray-500 max-w-md">
                    Choose a campaign from the dropdown to load its sequence templates and variables.
                  </p>
                </div>
              </div>
            ) : (
              <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
                <div className="border-b border-gray-200">
                  <TabsList className="grid grid-cols-2 h-10 w-full rounded-none bg-transparent">
                    <TabsTrigger value="template" className="rounded-none">
                      <FileText className="h-4 w-4 mr-2" />
                      Template
                    </TabsTrigger>
                    <TabsTrigger value="preview" className="rounded-none">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </TabsTrigger>
                  </TabsList>
                </div>
                
                <div className="p-4">
                  <TabsContent value="template" className="mt-0">
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium">Subject Template</Label>
                        <div className="p-3 bg-gray-50 border rounded-md font-mono text-sm">
                          {templateSubject || 'No subject template'}
                        </div>
                      </div>
                      
                      <div>
                        <Label className="text-sm font-medium">Message Template</Label>
                        <div className="border rounded-md bg-gray-50 h-[500px] overflow-hidden">
                          <iframe 
                            ref={previewIframeRef}
                            className="w-full h-full border-none" 
                            title="Template View"
                          />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="preview" className="mt-0">
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium">Subject Preview</Label>
                        <div className="p-3 bg-white border rounded-md">
                          {getPreviewContent(templateSubject) || 'No subject'}
                        </div>
                      </div>
                      
                      <div>
                        <Label className="text-sm font-medium">Message Preview</Label>
                        <div className="border rounded-md bg-white h-[500px] overflow-hidden">
                          <iframe 
                            ref={previewIframeRef}
                            className="w-full h-full border-none" 
                            title="Message Preview"
                          />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </div>
              </Tabs>
            )}
          </div>
        </div>
        
        {/* Action Buttons */}
        {currentSequence && (
          <div className="flex justify-end gap-2 p-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={handleSaveVariables}
              disabled={saving}
              size="sm"
            >
              {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <CheckCircle className="h-4 w-4 mr-2" />}
              Save Variables
            </Button>
            <Button
              onClick={handleSyncToSmartlead}
              disabled={saving || !selectedCampaignId}
              size="sm"
              className="bg-green-600 hover:bg-green-700"
            >
              {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Zap className="h-4 w-4 mr-2" />}
              Sync to Smartlead
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

const UnifiedCampaignSequenceSectionWithDeals: React.FC<UnifiedCampaignSequenceSectionProps> = (props) => {
  return (
    <div className="space-y-0">
      <UnifiedCampaignSequenceSection {...props} />
    </div>
  );
};

export default UnifiedCampaignSequenceSectionWithDeals; 