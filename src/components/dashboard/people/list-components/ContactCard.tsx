import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { 
  Mail, 
  MapPin, 
  Building2, 
  Briefcase,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Loader2,
  UserCheck,
  MessageSquare,
  Send,
  Search,
  Brain,
  Trash2,
  Sparkles,
  Target,
  DollarSign
} from 'lucide-react';
import { Contact, UnifiedContactData } from '../shared/types';
import { getInitials } from '../shared/utils';

interface ContactCardProps {
  contact: UnifiedContactData;
  onSelectContact: (contactId: number) => void;
  isSelected: boolean;
  onToggleSelection: (contactId: number, event: React.MouseEvent | React.ChangeEvent) => void;
  onDeleteContact?: (contactId: number) => void;
  showDeleteButton?: boolean;
}

// Processor status badge configuration
const getProcessorStatusBadge = (status: string, type: 'email_verification' | 'enrichment_v2' | 'email_generation' | 'investment_criteria') => {
  const configs = {
    email_verification: {
      pending: { icon: Clock, label: 'Email Pending', className: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
      running: { icon: Loader2, label: 'Verifying Email', className: 'bg-blue-50 border-blue-200 text-blue-700' },
      completed: { icon: CheckCircle, label: 'Email Verified', className: 'bg-green-50 border-green-200 text-green-700' },
      failed: { icon: XCircle, label: 'Email Failed', className: 'bg-red-50 border-red-200 text-red-700' }
    },
    enrichment_v2: {
      pending: { icon: Clock, label: 'Enrichment Pending', className: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
      running: { icon: Loader2, label: 'Enriching', className: 'bg-blue-50 border-blue-200 text-blue-700' },
      completed: { icon: Sparkles, label: 'Enriched', className: 'bg-indigo-50 border-indigo-200 text-indigo-700' },
      failed: { icon: XCircle, label: 'Enrichment Failed', className: 'bg-red-50 border-red-200 text-red-700' }
    },
    email_generation: {
      pending: { icon: Clock, label: 'Email Gen Pending', className: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
      running: { icon: Loader2, label: 'Generating Email', className: 'bg-blue-50 border-blue-200 text-blue-700' },
      completed: { icon: MessageSquare, label: 'Email Generated', className: 'bg-emerald-50 border-emerald-200 text-emerald-700' },
      failed: { icon: XCircle, label: 'Email Gen Failed', className: 'bg-red-50 border-red-200 text-red-700' }
    },
    investment_criteria: {
      pending: { icon: Clock, label: 'IC Pending', className: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
      running: { icon: Loader2, label: 'Processing IC', className: 'bg-blue-50 border-blue-200 text-blue-700' },
      completed: { icon: Target, label: 'IC Complete', className: 'bg-indigo-50 border-indigo-200 text-indigo-700' },
      failed: { icon: XCircle, label: 'IC Failed', className: 'bg-red-50 border-red-200 text-red-700' },
      error: { icon: XCircle, label: 'IC Error', className: 'bg-red-50 border-red-200 text-red-700' }
    }   
  };

  const config = configs[type]?.[status as keyof typeof configs[typeof type]];
  if (!config) return null;

  const IconComponent = config.icon;
  
  return (
    <Badge variant="outline" className={`text-xs py-0.5 px-2 ${config.className} flex items-center gap-1`}>
      <IconComponent className={`h-3 w-3 ${status === 'running' ? 'animate-spin' : ''}`} />
      {config.label}
    </Badge>
  );
};

const ContactCard: React.FC<ContactCardProps> = ({ 
  contact, 
  onSelectContact, 
  isSelected, 
  onToggleSelection,
  onDeleteContact,
  showDeleteButton = false
}) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  const fullName = contact.full_name || `${contact.first_name || ''} ${contact.last_name || ''}`.trim();
  const location = [contact.contact_city, contact.contact_state, contact.contact_country].filter(Boolean).join(', ');

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!onDeleteContact) {
      console.warn("No onDeleteContact handler provided to ContactCard");
      return;
    }

    setIsDeleting(true);
    try {
      onDeleteContact(contact.contact_id!);
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting contact:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Determine processing errors
  const processingErrors = [
    contact.email_verification_error,
    contact.contact_enrichment_v2_error,
    contact.email_generation_error,
    contact.contact_investment_criteria_error
  ].filter(Boolean);

  // Get all processor status badges - prioritize V2 enrichment over old enrichment
  const statusBadges = [
    contact.email_verification_status && getProcessorStatusBadge(contact.email_verification_status, 'email_verification'),
    contact.contact_enrichment_v2_status && getProcessorStatusBadge(contact.contact_enrichment_v2_status, 'enrichment_v2'),
    contact.email_generation_status && getProcessorStatusBadge(contact.email_generation_status, 'email_generation'),
    contact.contact_investment_criteria_status && getProcessorStatusBadge(contact.contact_investment_criteria_status, 'investment_criteria'),
  ].filter(Boolean).map((badge, index) => 
    badge ? React.cloneElement(badge, { key: `processor-${index}` }) : null
  );

  // Additional status badges
  const additionalBadges: React.ReactElement[] = [];
  
  // Smartlead sync status
  if (contact.smartlead_lead_id) {
    additionalBadges.push(
      <Badge key="smartlead" variant="outline" className="text-xs py-0.5 bg-blue-50 border-blue-200 text-blue-700">
        Smartlead Synced
      </Badge>
    );
  }

  // High error count warning
  if (contact.processing_error_count && contact.processing_error_count > 2) {
    additionalBadges.push(
      <Badge key="high-errors" variant="outline" className="text-xs py-0.5 bg-red-50 border-red-200 text-red-700 flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        {contact.processing_error_count} Errors
      </Badge>
    );
  }

  // Email status badge (from email validation)
  if (contact.email_status) {
    const emailStatusConfig = {
      'Verified': { className: 'bg-green-50 border-green-200 text-green-700', icon: CheckCircle },
      'Invalid': { className: 'bg-red-50 border-red-200 text-red-700', icon: XCircle },
      'Unknown': { className: 'bg-gray-50 border-gray-200 text-gray-700', icon: AlertCircle },
      'Failed': { className: 'bg-red-50 border-red-200 text-red-700', icon: XCircle }
    };
    
    const emailConfig = emailStatusConfig[contact.email_status as keyof typeof emailStatusConfig];
    if (emailConfig) {
      const EmailIcon = emailConfig.icon;
      additionalBadges.push(
        <Badge key="email-status" variant="outline" className={`text-xs py-0.5 ${emailConfig.className} flex items-center gap-1`}>
          <EmailIcon className="h-3 w-3" />
          {contact.email_status}
        </Badge>
      );
    }
  }



  // Show conflicts if any
  if (contact.conflict_status && contact.conflict_status !== 'none') {
    additionalBadges.push(
      <Badge key="conflicts" variant="outline" className="text-xs py-0.5 bg-orange-50 border-orange-200 text-orange-700 flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        Conflicts
      </Badge>
    );
  }

  return (
    <>
      <Card 
        className={`cursor-pointer transition-all duration-200 ${isSelected ? 'border-blue-500 shadow-md' : 'hover:shadow-md hover:border-gray-300'}`}
        onClick={(e) => {
          // Don't navigate if clicking on checkbox area
          const target = e.target as HTMLElement;
          if (target.closest('input[type="checkbox"]') || target.closest('[data-checkbox-container]')) {
            return;
          }
          onSelectContact(contact.contact_id!);
        }}
      >
      <CardContent className="p-4 overflow-hidden">
        <div className="flex items-start justify-between mb-3 min-w-0">
          <div className="flex items-start gap-3 min-w-0 flex-1">
            <div onClick={(e) => e.stopPropagation()} data-checkbox-container>
              <input 
                type="checkbox"
                checked={isSelected}
                onChange={(e) => {
                  e.stopPropagation();
                  onToggleSelection(contact.contact_id!, e as any);
                }}
                onClick={(e) => e.stopPropagation()}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <Avatar className="h-12 w-12 bg-gray-100">
              <AvatarFallback className="bg-gray-200 text-gray-600 font-semibold">
                {getInitials(fullName)}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <h3 className="font-bold text-gray-900 break-words leading-tight">{fullName || 'Unknown Contact'}</h3>
              
              <div className="flex items-center mt-1">
                <Building2 className="h-4 w-4 mr-1.5 text-gray-400 flex-shrink-0" />
                <span className="text-sm text-gray-600 break-words">{contact.company_name || 'N/A'}</span>
              </div>

              {contact.title && (
                <div className="flex items-start mt-1">
                  <Briefcase className="h-4 w-4 mr-1.5 text-gray-400 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600 leading-relaxed break-words">{contact.title}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Delete Button */}
          {showDeleteButton && onDeleteContact && (
            <div className="flex-shrink-0 ml-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsDeleteDialogOpen(true);
                }}
                className="p-1 text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                title="Delete contact"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>

        {contact.email && (
          <div className="flex items-start text-gray-600 mb-1.5">
            <Mail className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0 mt-0.5" />
            <a 
              href={`mailto:${contact.email}`} 
              className="text-sm hover:underline break-all" 
              onClick={e => e.stopPropagation()}
            >
              {contact.email}
            </a>
          </div>
        )}
        
        {location && (
          <div className="flex items-start text-gray-600 mb-3">
            <MapPin className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0 mt-0.5" />
            <span className="text-sm break-words">{location}</span>
          </div>
        )}

        {(contact as any).minimum_deal_size && (
          <div className="flex items-start text-gray-600 mb-3">
            <DollarSign className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0 mt-0.5" />
            <span className="text-sm">Min Deal: ${(parseFloat((contact as any).minimum_deal_size) / 1000000).toFixed(1)}M</span>
          </div>
        )}

        {/* Processor Status Badges */}
        {statusBadges.length > 0 && (
          <div className="mb-2">
            <div className="text-xs text-gray-500 mb-1 font-medium">Processing Status</div>
            <div className="flex flex-wrap gap-1 max-w-full">
              {statusBadges}
            </div>
          </div>
        )}

        {/* Additional Status Badges */}
        {additionalBadges.length > 0 && (
          <div className="flex flex-wrap gap-1 max-w-full">
            {additionalBadges}
          </div>
        )}

        {/* Processing errors summary */}
        {processingErrors.length > 0 && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
            <div className="flex items-center gap-1 text-red-700 font-medium mb-1">
              <AlertCircle className="h-3 w-3" />
              Processing Issues ({processingErrors.length})
            </div>
            <div className="text-red-600 text-xs break-words">
              {processingErrors[0]?.substring(0, 100)}
              {processingErrors[0]?.length && processingErrors[0]?.length > 100 && '...'}
            </div>
          </div>
        )}
      </CardContent>
    </Card>

    {/* Delete Confirmation Dialog */}
    <AlertDialog
      open={isDeleteDialogOpen}
      onOpenChange={setIsDeleteDialogOpen}
    >
      <AlertDialogContent onClick={(e) => e.stopPropagation()}>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Contact</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete "{fullName}"? This action
            will permanently remove the contact and all associated data.
            This cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isDeleting ? "Deleting..." : "Delete Contact"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </>
  );
};

export default ContactCard; 