'use client'

import { DetailedContact } from "../ContactDetail"
import CompanyOverviewV2 from "../../companies/detail-components/CompanyOverviewV2"
import { CompanyDetail } from "../../companies/shared/types"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ExternalLink, Target, Building2, Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

interface ContactCompanyTabProps {
  contact: DetailedContact
}

export default function ContactCompanyTab({ contact }: ContactCompanyTabProps) {
  const router = useRouter();
  const companyData = contact.company_data;
  const [showICSlider, setShowICSlider] = useState(false);

  const handleGoToCompany = () => {
    if (contact.company_id) {
      router.push(`/dashboard/companies/${contact.company_id}`);
    }
  };

  const handleAttachICToContact = async (selectedCriteria: any[]) => {
    try {
      // Create API call to attach IC to contact
      const response = await fetch(`/api/contacts/${contact.contact_id}/attach-investment-criteria`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          investment_criteria_ids: selectedCriteria.map(ic => ic.investment_criteria_id)
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to attach investment criteria');
      }

      toast.success(`Successfully attached ${selectedCriteria.length} investment criteria to contact`);
      setShowICSlider(false);
    } catch (error) {
      console.error('Error attaching IC to contact:', error);
      throw error; // Re-throw to let the slider component handle the error display
    }
  };

  if (!companyData) {
    return (
      <div className="mt-6 text-center text-gray-500">
        <p>No company information available for this contact.</p>
        {contact.company_name && (
          <p className="mt-2">Company name: {contact.company_name}</p>
        )}
        {contact.company_id && (
          <div className="mt-4">
            <p className="mt-2">Company ID: {contact.company_id}</p>
            <Button 
              onClick={handleGoToCompany}
              className="mt-3 flex items-center gap-2"
              variant="outline"
            >
              <ExternalLink className="h-4 w-4" />
              Go to Company Details
            </Button>
          </div>
        )}
      </div>
    )
  }

  // Cast the company data to CompanyDetail type
  // This is the same approach used in CompanyDetailMain.tsx
  const company = companyData as unknown as CompanyDetail;

  return (
    <div className="mt-6 space-y-6">
      <CompanyOverviewV2 company={company} />

      {/* Investment Criteria Section */}
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Target className="h-5 w-5 text-blue-600" />
              <div>
                <CardTitle className="text-lg font-medium">Investment Criteria</CardTitle>
                <p className="text-sm text-slate-500">
                  {companyData.has_investment_criteria
                    ? `${companyData.investment_criteria_count || 0} criteria available`
                    : 'No investment criteria found'
                  }
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {companyData.has_investment_criteria && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <Target className="h-3 w-3 mr-1" />
                  IC Available
                </Badge>
              )}

              {companyData.has_investment_criteria && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowICSlider(!showICSlider)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {showICSlider ? 'Hide' : 'View & Attach'} IC
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>
    </div>
  )
}
