'use client'

import { Contact } from '../shared/types'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  Mail, 
  Briefcase, 
  MapPin, 
  Linkedin, 
  ExternalLink,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Loader2,
  <PERSON>r<PERSON>heck,
  MessageSquare,
  Target
} from 'lucide-react'
import { getInitials } from '../shared/utils'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import React from 'react'

interface ContactHeaderProps {
  contact: Contact
}

// Processor status badge configuration
const getProcessorStatusBadge = (status: string, type: 'email_verification' | 'enrichment' | 'enrichment_v2' | 'email_generation') => {
  const configs = {
    email_verification: {
      pending: { icon: Clock, label: 'Email Pending', className: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
      running: { icon: Loader2, label: 'Verifying Email', className: 'bg-blue-50 border-blue-200 text-blue-700' },
      completed: { icon: CheckCircle, label: 'Email Verified', className: 'bg-green-50 border-green-200 text-green-700' },
      failed: { icon: XCircle, label: 'Email Failed', className: 'bg-red-50 border-red-200 text-red-700' }
    },
    enrichment: {
      pending: { icon: Clock, label: 'Enrichment Pending', className: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
      running: { icon: Loader2, label: 'Enriching', className: 'bg-blue-50 border-blue-200 text-blue-700' },
      completed: { icon: UserCheck, label: 'Enriched', className: 'bg-purple-50 border-purple-200 text-purple-700' },
      failed: { icon: XCircle, label: 'Enrichment Failed', className: 'bg-red-50 border-red-200 text-red-700' }
    },
    enrichment_v2: {
      pending: { icon: Clock, label: 'Enrichment V2 Pending', className: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
      running: { icon: Loader2, label: 'Enriching V2', className: 'bg-blue-50 border-blue-200 text-blue-700' },
      completed: { icon: UserCheck, label: 'Enriched V2', className: 'bg-purple-50 border-purple-200 text-purple-700' },
      failed: { icon: XCircle, label: 'Enrichment V2 Failed', className: 'bg-red-50 border-red-200 text-red-700' }
    },
    email_generation: {
      pending: { icon: Clock, label: 'Email Gen Pending', className: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
      running: { icon: Loader2, label: 'Generating Email', className: 'bg-blue-50 border-blue-200 text-blue-700' },
      completed: { icon: MessageSquare, label: 'Email Generated', className: 'bg-emerald-50 border-emerald-200 text-emerald-700' },
      failed: { icon: XCircle, label: 'Email Gen Failed', className: 'bg-red-50 border-red-200 text-red-700' }
    }   
  };

  const config = configs[type]?.[status as keyof typeof configs[typeof type]];
  if (!config) return null;

  const IconComponent = config.icon;
  
  return (
    <Badge variant="outline" className={`text-xs py-1 px-2 ${config.className} flex items-center gap-1`}>
      <IconComponent className={`h-3 w-3 ${status === 'running' ? 'animate-spin' : ''}`} />
      {config.label}
    </Badge>
  );
};

export default function ContactHeader({ contact }: ContactHeaderProps) {
  const router = useRouter()
  const fullName = contact.full_name || `${contact.first_name} ${contact.last_name}`
  const location = [contact.contact_city, contact.contact_state, contact.contact_country].filter(Boolean).join(', ')
  const contactInitials = getInitials(fullName)

  // Get all processor status badges - prioritize V2 enrichment over old enrichment
  const statusBadges = [
    contact.email_verification_status && getProcessorStatusBadge(contact.email_verification_status, 'email_verification'),
    contact.contact_enrichment_v2_status && getProcessorStatusBadge(contact.contact_enrichment_v2_status, 'enrichment_v2'),
    contact.email_generation_status && getProcessorStatusBadge(contact.email_generation_status, 'email_generation'),
  ].filter(Boolean).map((badge, index) => 
    badge ? React.cloneElement(badge, { key: `processor-${index}` }) : null
  );

  // Additional status badges
  const additionalBadges: React.ReactElement[] = [];
  
  // Smartlead sync status
  if (contact.smartlead_lead_id) {
    additionalBadges.push(
      <Badge key="smartlead" variant="outline" className="text-xs py-1 px-2 bg-blue-50 border-blue-200 text-blue-700">
        Smartlead Synced
      </Badge>
    );
  }

  // Email status badge (from email validation)
  if (contact.email_status) {
    const emailStatusConfig = {
      'Verified': { className: 'bg-green-50 border-green-200 text-green-700', icon: CheckCircle },
      'Invalid': { className: 'bg-red-50 border-red-200 text-red-700', icon: XCircle },
      'Unknown': { className: 'bg-gray-50 border-gray-200 text-gray-700', icon: AlertCircle },
      'Failed': { className: 'bg-red-50 border-red-200 text-red-700', icon: XCircle }
    };
    
    const emailConfig = emailStatusConfig[contact.email_status as keyof typeof emailStatusConfig];
    if (emailConfig) {
      const EmailIcon = emailConfig.icon;
      additionalBadges.push(
        <Badge key="email-status" variant="outline" className={`text-xs py-1 px-2 ${emailConfig.className} flex items-center gap-1`}>
          <EmailIcon className="h-3 w-3" />
          {contact.email_status}
        </Badge>
      );
    }
  }

  // Show conflicts if any
  if (contact.conflict_status && contact.conflict_status !== 'none') {
    additionalBadges.push(
      <Badge key="conflicts" variant="outline" className="text-xs py-1 px-2 bg-orange-50 border-orange-200 text-orange-700 flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        Conflicts
      </Badge>
    );
  }

  // Investment criteria indicator
  if ((contact as any).contact_investment_criteria_status) {
    additionalBadges.push(
      <Badge key="has-criteria" variant="outline" className="text-xs py-1 px-2 bg-indigo-50 border-indigo-200 text-indigo-700 flex items-center gap-1">
        <Target className="h-3 w-3" />
        Has IC
      </Badge>
    );
  }

  return (
    <div className="flex items-start">
      <Avatar className="h-20 w-20 rounded-lg mr-6 shadow-md">
        <AvatarFallback className={`bg-gray-200 text-gray-700 text-2xl font-bold`}>
          {contactInitials}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <h1 className="text-3xl font-bold text-gray-900">{fullName}</h1>
        <p className="text-lg text-gray-600 mt-1">
          {contact.company_name && contact.title 
            ? `${contact.company_name} – ${contact.title}`
            : contact.company_name || contact.title || ''
          }
        </p>
        <div className="mt-4 flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-gray-700">
          {contact.email && (
            <a href={`mailto:${contact.email}`} className="flex items-center hover:text-blue-600">
              <Mail className="h-4 w-4 mr-1.5 text-gray-400" />
              <span>{contact.email}</span>
            </a>
          )}
          {location && (
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-1.5 text-gray-400" />
              <span>{location}</span>
            </div>
          )}
          {contact.linkedin_url && (
            <a href={contact.linkedin_url} target="_blank" rel="noopener noreferrer" className="flex items-center hover:text-blue-600">
              <Linkedin className="h-4 w-4 mr-1.5 text-gray-400" />
              <span>LinkedIn</span>
            </a>
          )} 
          {contact.company_id && (
            <a 
              href={`/dashboard/companies/${contact.company_id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-2 rounded-md transition-colors"
            >
              <ExternalLink className="h-4 w-4 mr-1.5" />
              Go to Company
            </a>
          )}
        </div>
        
        {/* Status Badges */}
        {(statusBadges.length > 0 || additionalBadges.length > 0) && (
          <div className="mt-4">
            <div className="text-sm text-gray-500 mb-2 font-medium">Processing Status</div>
            <div className="flex flex-wrap gap-2">
              {statusBadges}
              {additionalBadges}
            </div>
          </div>
        )}
        
        {contact.source && <Badge className="mt-4" variant="outline">Source: {contact.source}</Badge>}
      </div>
    </div>
  )
} 