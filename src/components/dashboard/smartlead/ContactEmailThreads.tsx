import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardDescription,
  CardFooter 
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

// Helper to get initials from name
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();
};

// Status color mapping
const statusColors: Record<string, string> = {
  SENT: '#9333ea',        // Purple
  DELIVERED: '#2563eb',   // Blue
  OPENED: '#16a34a',      // Green
  REPLIED: '#15803d',     // Dark Green
  BOUNCED: '#dc2626',     // Red
  UNSUBSCRIBED: '#9ca3af', // Gray
  ACTIVE: '#22c55e',      // <PERSON> (actively engaged)
  ADDED: '#f59e0b',       // Amber (newly added)
};

type Message = {
  message_id: string;
  thread_id: string;
  from_email: string;
  to_email: string;
  subject: string;
  body: string;
  direction: 'inbound' | 'outbound';
  role: string;
  created_at: string;
};

type Thread = {
  thread_id: string;
  subject: string;
  created_at: string;
  updated_at: string;
  messages: Message[];
};

type Contact = {
  contact_id: number;
  first_name: string;
  last_name: string;
  email: string;
  smartlead_lead_id: string;
  smartlead_status: string;
  last_email_sent_at: string;
};

interface ContactEmailThreadsProps {
  contactId: number | string;
}

export function ContactEmailThreads({ contactId }: ContactEmailThreadsProps) {
  const [data, setData] = useState<{ contact: Contact; threads: Thread[] } | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/smartlead/contacts/${contactId}/threads`);
        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.status}`);
        }
        const result = await response.json();
        setData(result);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [contactId]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-red-500">Error loading threads: {error.message}</div>
        </CardContent>
      </Card>
    );
  }

  const contact: Contact | null = data?.contact ?? null;
  const threads: Thread[] = data?.threads || [];

  if (!contact) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-red-500">Contact not found</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center gap-4">
          <Avatar className="h-12 w-12">
            <AvatarFallback>
              {getInitials(`${contact.first_name} ${contact.last_name}`)}
            </AvatarFallback>
          </Avatar>
          <div>
            <CardTitle>
              {contact.first_name} {contact.last_name}
            </CardTitle>
            <CardDescription>{contact.email}</CardDescription>
          </div>
        </div>
        <div className="flex gap-2 mt-2">
          <Badge 
            variant="outline"
            style={{
              backgroundColor: statusColors[contact.smartlead_status] || '#9ca3af',
              color: 'white'
            }}
          >
            {contact.smartlead_status}
          </Badge>
          {contact.smartlead_lead_id && (
            <Badge variant="outline">
              {contact.smartlead_lead_id}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {threads.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No email threads found for this contact.
          </div>
        ) : (
          <Tabs defaultValue={threads[0]?.thread_id}>
            <TabsList className="mb-4">
              {threads.map((thread) => (
                <TabsTrigger key={thread.thread_id} value={thread.thread_id}>
                  {thread.subject.substring(0, 20)}
                  {thread.subject.length > 20 ? '...' : ''}
                </TabsTrigger>
              ))}
            </TabsList>
            
            {threads.map((thread) => (
              <TabsContent 
                key={thread.thread_id} 
                value={thread.thread_id}
                className="border rounded-md p-4"
              >
                <div className="mb-4">
                  <h3 className="text-lg font-medium">{thread.subject}</h3>
                  <p className="text-sm text-muted-foreground">
                    Started on {new Date(thread.created_at).toLocaleString()}
                  </p>
                </div>
                
                <div className="my-4" />
                
                <div className="space-y-6">
                  {thread.messages.map((message, index) => (
                    <div 
                      key={message.message_id} 
                      className={`flex ${
                        message.direction === 'outbound' 
                          ? 'justify-end' 
                          : 'justify-start'
                      }`}
                    >
                      <div 
                        className={`max-w-[80%] rounded-lg p-4 ${
                          message.direction === 'outbound'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback>
                              {message.direction === 'outbound' 
                                ? 'AN' 
                                : getInitials(`${contact.first_name} ${contact.last_name}`)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm font-medium">
                            {message.direction === 'outbound' 
                              ? 'ANAX' 
                              : `${contact.first_name} ${contact.last_name}`}
                          </span>
                          <span className="text-xs ml-auto">
                            {new Date(message.created_at).toLocaleString()}
                          </span>
                        </div>
                        
                        <div 
                          className="prose prose-sm max-w-none"
                          dangerouslySetInnerHTML={{ __html: message.body }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
      <CardFooter className="border-t pt-4 flex justify-between">
        <p className="text-xs text-muted-foreground">
          Last contact: {contact.last_email_sent_at 
            ? new Date(contact.last_email_sent_at).toLocaleString() 
            : 'Never'}
        </p>
        {contact.smartlead_lead_id && (
          <p className="text-xs text-muted-foreground">
            Smartlead ID: {contact.smartlead_lead_id}
          </p>
        )}
      </CardFooter>
    </Card>
  );
} 