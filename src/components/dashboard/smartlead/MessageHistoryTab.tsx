"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>fresh<PERSON>w, ArrowLeft, Mail, User, Calendar, Eye, MousePointer, <PERSON><PERSON>, MessageCircle, Filter, Search } from 'lucide-react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";

interface Message {
  id: string;
  type: string;
  subject?: string;
  body?: string;
  html_body?: string;
  sent_at?: string;
  status?: string;
  from?: string;
  to?: string;
  opens?: number;
  clicks?: number;
  reply?: boolean;
  sequence_number?: number;
  email_sequence_id?: string;
}

interface Campaign {
  id: string;
  name: string;
  status?: string;
}

interface Lead {
  campaign_lead_map_id: string;
  lead_category_id?: string;
  status: string;
  created_at: string;
  lead?: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    phone_number?: string;
    company_name?: string;
    website?: string;
    location?: string;
    custom_fields?: any;
  };
  // Support for transformed format from LeadsTab
  lead_data?: {
    id: number;
    first_name?: string;
    last_name?: string;
    email: string;
    phone_number?: string;
    company_name?: string;
    website?: string;
    location?: string;
    custom_fields?: Record<string, any>;
    linkedin_profile?: string;
    company_url?: string;
    is_unsubscribed?: boolean;
    unsubscribed_client_id_map?: Record<string, any>;
  };
  contact_info?: {
    contact_id: number;
    company_id: number;
    contact_first_name: string;
    contact_last_name: string;
    contact_title: string;
    company_name: string;
    company_website: string;
    industry: string;
  };
}

interface MessageHistoryTabProps {
  propSelectedCampaignId?: string;
}

const MessageHistoryTab: React.FC<MessageHistoryTabProps> = ({ propSelectedCampaignId }) => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('');
  const [selectedLeadId, setSelectedLeadId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [loadingLeads, setLoadingLeads] = useState(false);
  const [loadingCampaigns, setLoadingCampaigns] = useState(false);
  const [messageTab, setMessageTab] = useState<string>('all');
  const [showOnlyRepliedLeads, setShowOnlyRepliedLeads] = useState(false);
  const [leadsWithReplies, setLeadsWithReplies] = useState<Set<string>>(new Set());
  const [checkingLeadsForReplies, setCheckingLeadsForReplies] = useState(false);
  const [leadSearchTerm, setLeadSearchTerm] = useState('');




  useEffect(() => {
    fetchCampaigns();
    
    // Check if campaign and lead were selected from leads tab
    const storedCampaignId = localStorage.getItem('viewMessageCampaignId');
    const storedLeadId = localStorage.getItem('viewMessageLeadId');
    
    if (storedCampaignId && storedLeadId) {
      setSelectedCampaignId(storedCampaignId);
      setSelectedLeadId(storedLeadId);
      
      // Clear after use
      localStorage.removeItem('viewMessageCampaignId');
      localStorage.removeItem('viewMessageLeadId');
    }
  }, []);

  // If parent provides a default/selected campaign, honor it
  useEffect(() => {
    if (propSelectedCampaignId && propSelectedCampaignId !== selectedCampaignId) {
      setSelectedCampaignId(propSelectedCampaignId);
    }
  }, [propSelectedCampaignId]);

  useEffect(() => {
    if (selectedCampaignId) {
      fetchLeads(selectedCampaignId);
      setLeadSearchTerm(''); // Clear search when changing campaigns
    } else {
      setLeads([]);
      setSelectedLeadId('');
      setMessages([]);
      setLeadSearchTerm('');
    }
  }, [selectedCampaignId]);

  useEffect(() => {
    if (selectedCampaignId && selectedLeadId) {
      fetchMessageHistory(selectedCampaignId, selectedLeadId);
    } else {
      setMessages([]);
    }
  }, [selectedCampaignId, selectedLeadId]);

  useEffect(() => {
    if (showOnlyRepliedLeads && selectedCampaignId && leads.length > 0) {
      checkAllLeadsForReplies();
    }
  }, [showOnlyRepliedLeads, selectedCampaignId, leads.length]);




  const fetchCampaigns = async () => {
    setLoadingCampaigns(true);
    try {
      const response = await fetch('/api/smartlead/campaigns');
      if (!response.ok) {
        throw new Error(`Failed to fetch campaigns: ${response.status}`);
      }
      
      const data = await response.json();
      if (data && data.campaigns) {
        setCampaigns(data.campaigns);
        
        // Prefer campaign id passed from parent if available and valid
        if (!selectedCampaignId) {
          if (propSelectedCampaignId && data.campaigns.some((c: Campaign) => c.id === propSelectedCampaignId)) {
            setSelectedCampaignId(propSelectedCampaignId);
          } else if (data.campaigns.length > 0) {
            // Fallback to first campaign
            setSelectedCampaignId(data.campaigns[0].id);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      toast.error(`Failed to fetch campaigns: ${(error as Error).message}`);
    } finally {
      setLoadingCampaigns(false);
    }
  };

  const fetchLeads = async (campaignId: string, page = 1, limit = 100) => {
    setLoadingLeads(true);
    try {
      // Calculate offset for pagination (Smartlead uses 1-based page numbering)
      const offset = (page - 1) * limit;
      
      const response = await fetch(
        `/api/smartlead/campaigns/${campaignId}/leads?page=${page}&limit=${limit}&offset=${offset}`
      );
      if (!response.ok) {
        throw new Error(`Failed to fetch leads: ${response.status}`);
      }
      
      const data = await response.json();
      console.log(`Leads fetched for campaign ${campaignId}, page ${page}:`, data);
      
      let leadsArray: any[] = [];
      if (data && typeof data === 'object') {
        if (Array.isArray(data)) {
          leadsArray = data;
        } else if (data.leads && Array.isArray(data.leads)) {
          leadsArray = data.leads;
        } else if (data.data && Array.isArray(data.data)) {
          leadsArray = data.data;
        } else if (data.results && Array.isArray(data.results)) {
          leadsArray = data.results;
        }
      }
      
      if (leadsArray.length > 0) {
        // If this is the first page, replace leads; otherwise, append
        if (page === 1) {
          setLeads(leadsArray);
        } else {
          setLeads(prev => [...prev, ...leadsArray]);
        }
        
        // If no lead is selected yet but we have leads, select the first one
        if (!selectedLeadId && page === 1) {
          setSelectedLeadId(leadsArray[0].campaign_lead_map_id);
        }
      } else if (page === 1) {
        // Clear leads if no data on first page
        setLeads([]);
        setSelectedLeadId('');
        setMessages([]);
      }
    } catch (error) {
      console.error('Error fetching leads:', error);
      toast.error(`Failed to fetch leads: ${(error as Error).message}`);
      if (page === 1) {
        setLeads([]);
        setSelectedLeadId('');
        setMessages([]);
      }
    } finally {
      setLoadingLeads(false);
    }
  };

  const fetchMessageHistory = async (campaignId: string, campaignLeadMapId: string) => {
    setLoading(true);
    try {
      // Find the actual lead ID from the campaign_lead_map_id
      let selectedLead = leads.find(l => l.campaign_lead_map_id === campaignLeadMapId);
      
      // If lead not found in current leads array, fetch leads first
      if (!selectedLead) {
        console.log('Lead not found in current leads array, fetching leads...');
        const leadsResponse = await fetch(`/api/smartlead/campaigns/${campaignId}/leads`);
        if (leadsResponse.ok) {
          const leadsData = await leadsResponse.json();
          if (leadsData && leadsData.data && Array.isArray(leadsData.data)) {
            selectedLead = leadsData.data.find((l: any) => l.campaign_lead_map_id === campaignLeadMapId);
          } else if (leadsData && Array.isArray(leadsData)) {
            selectedLead = leadsData.find((l: any) => l.campaign_lead_map_id === campaignLeadMapId);
          }
        }
      }
      
      if (!selectedLead) {
        throw new Error(`Lead with campaign_lead_map_id ${campaignLeadMapId} not found`);
      }
      
      // Handle both raw API format and transformed format from LeadsTab
      let actualLeadId: number;
      if (selectedLead.lead && selectedLead.lead.id) {
        // Raw API format from MessageHistoryTab's own fetch
        actualLeadId = selectedLead.lead.id;
      } else if (selectedLead.lead_data && selectedLead.lead_data.id) {
        // Transformed format from LeadsTab (via localStorage)
        actualLeadId = selectedLead.lead_data.id;
      } else {
        throw new Error(`Could not find actual lead ID for campaign_lead_map_id ${campaignLeadMapId}`);
      }
      console.log(`Fetching message history for actual lead ID ${actualLeadId} (campaign_lead_map_id: ${campaignLeadMapId})`);
      
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}/leads/${actualLeadId}/message-history`);
      if (!response.ok) {
        throw new Error(`Failed to fetch message history: ${response.status}`);
      }
      
      const data = await response.json();
      console.log(`Message history for lead ${actualLeadId}:`, data);
      console.log(`Frontend - Data type: ${typeof data}, Array: ${Array.isArray(data)}, Length: ${Array.isArray(data) ? data.length : 'N/A'}`);
      
      // Handle different possible response formats
      let messagesArray: any[] = [];
      if (Array.isArray(data)) {
        messagesArray = data;
      } else if (data && typeof data === 'object') {
        // Check if data has a history property (Smartlead API format)
        if (Array.isArray(data.history)) {
          messagesArray = data.history;
        } else if (Array.isArray(data.messages)) {
          messagesArray = data.messages;
        } else if (Array.isArray(data.data)) {
          messagesArray = data.data;
        } else if (Array.isArray(data.results)) {
          messagesArray = data.results;
        } else {
          // If it's an object but not an array, wrap it in an array
          messagesArray = [data];
        }
      }
      
      console.log(`Final messages array length: ${messagesArray.length}`);
      
      // Transform the messages to match our interface
      const transformedMessages = messagesArray.map((msg: any, index: number) => {
        // Determine status based on engagement
        let status = 'sent';
        if (msg.open_count > 0) {
          status = 'opened';
        }
        if (msg.click_count > 0) {
          status = 'clicked';
        }
        
        const transformedMessage = {
          id: msg.stats_id || msg.message_id || `msg-${index}`,
          type: msg.type === 'SENT' ? 'outbound' : 'inbound',
          subject: msg.subject,
          body: msg.email_body ? stripHtmlTags(msg.email_body) : undefined,
          html_body: msg.email_body,
          sent_at: msg.time,
          status: status,
          from: msg.from,
          to: msg.to,
          opens: msg.open_count,
          clicks: msg.click_count,
          reply: msg.type !== 'SENT', // Inbound messages are replies
          sequence_number: msg.email_seq_number ? parseInt(msg.email_seq_number) : undefined
        };
        
        return transformedMessage;
      });
      
      setMessages(transformedMessages);
      
      // Check if this lead has replies and update our tracking
      const hasReplies = transformedMessages.some(msg => msg.reply === true);
      if (hasReplies) {
        setLeadsWithReplies(prev => new Set([...prev, campaignLeadMapId]));
      }
    } catch (error) {
      console.error('Error fetching message history:', error);
      toast.error(`Failed to fetch message history: ${(error as Error).message}`);
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const getLeadDisplayName = (lead: Lead) => {
    // Handle both raw API format and transformed format from LeadsTab
    const leadData = lead.lead || lead.lead_data;
    if (!leadData) return 'Unknown Lead';
    
    const { first_name, last_name, email } = leadData;
    if (first_name || last_name) {
      return `${first_name || ''} ${last_name || ''}`.trim();
    }
    return email;
  };

  const getLeadEmail = (campaignLeadMapId: string) => {
    const lead = leads.find(l => l.campaign_lead_map_id === campaignLeadMapId);
    return (lead?.lead || lead?.lead_data)?.email || 'Unknown Email';
  };

  const getCampaignName = (campaignId: string) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    return campaign ? campaign.name : 'Unknown Campaign';
  };

  const getMessageStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'opened':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'clicked':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'replied':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'bounced':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'sent':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'outbound':
        return <Mail className="h-4 w-4 text-blue-600" />;
      case 'inbound':
        return <Mail className="h-4 w-4 text-green-600" />;
      default:
        return <Mail className="h-4 w-4 text-gray-600" />;
    }
  };

  const stripHtmlTags = (html: string) => {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.body.textContent || "";
  };

  const getMessagePreview = (message: Message) => {
    if (message.html_body) {
      const textContent = stripHtmlTags(message.html_body);
      return textContent.length > 150 ? textContent.substring(0, 150) + '...' : textContent;
    } else if (message.body) {
      return message.body.length > 150 ? message.body.substring(0, 150) + '...' : message.body;
    }
    return 'No content available';
  };

  const messageHasReplies = (message: Message) => {
    if (message.type !== 'outbound') return false;
    return messages.some(reply => 
      reply.type === 'inbound' && 
      reply.subject && message.subject &&
      (reply.subject.toLowerCase().includes('re:') || 
       reply.subject.toLowerCase().replace('re:', '').trim() === message.subject.toLowerCase().trim())
    );
  };

  const isRecentMessage = (message: Message) => {
    if (!message.sent_at) return false;
    const messageDate = new Date(message.sent_at);
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    return messageDate >= threeDaysAgo;
  };

  const checkAllLeadsForReplies = async () => {
    if (!selectedCampaignId || leads.length === 0) return;
    
    setCheckingLeadsForReplies(true);
    const leadsToCheck = leads.filter(lead => !leadsWithReplies.has(lead.campaign_lead_map_id));
    
    try {
      // Process leads in batches to avoid overwhelming the API
      const batchSize = 5;
      const batches: Lead[][] = [];
      for (let i = 0; i < leadsToCheck.length; i += batchSize) {
        batches.push(leadsToCheck.slice(i, i + batchSize));
      }
      
      let totalChecked = 0;
      const foundReplies: string[] = [];
      
      for (const batch of batches) {
        const batchPromises = batch.map(async (lead) => {
          try {
            // Handle both raw API format and transformed format from LeadsTab
            const leadData = lead.lead || lead.lead_data;
            if (!leadData) {
              console.error(`No lead data found for campaign_lead_map_id ${lead.campaign_lead_map_id}`);
              return lead.campaign_lead_map_id;
            }
            const actualLeadId = leadData.id;
            const response = await fetch(`/api/smartlead/campaigns/${selectedCampaignId}/leads/${actualLeadId}/message-history`);
            
            if (response.ok) {
              const data = await response.json();
              
              // Handle different possible response formats
              let messagesArray: any[] = [];
              if (Array.isArray(data)) {
                messagesArray = data;
              } else if (data && typeof data === 'object') {
                if (Array.isArray(data.history)) {
                  messagesArray = data.history;
                } else if (Array.isArray(data.messages)) {
                  messagesArray = data.messages;
                } else if (Array.isArray(data.data)) {
                  messagesArray = data.data;
                } else if (Array.isArray(data.results)) {
                  messagesArray = data.results;
                } else {
                  messagesArray = [data];
                }
              }
              
              // Check if this lead has replies
              const hasReplies = messagesArray.some((msg: any) => msg.type !== 'SENT');
              if (hasReplies) {
                return lead.campaign_lead_map_id;
              }
            }
          } catch (error) {
            console.error(`Error checking replies for lead ${lead.campaign_lead_map_id}:`, error);
          }
          return null;
        });
        
        const batchResults = await Promise.all(batchPromises);
        const batchReplies = batchResults.filter(Boolean) as string[];
        foundReplies.push(...batchReplies);
        
        totalChecked += batch.length;
        console.log(`Checked ${totalChecked}/${leadsToCheck.length} leads for replies`);
        
        // Add small delay between batches to be API-friendly
        if (batches.indexOf(batch) < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      if (foundReplies.length > 0) {
        setLeadsWithReplies(prev => new Set([...prev, ...foundReplies]));
        toast.success(`Found ${foundReplies.length} leads with replies!`);
      } else {
        toast.info('No leads with replies found in this campaign');
      }
    } catch (error) {
      console.error('Error checking leads for replies:', error);
      toast.error('Failed to check leads for replies');
    } finally {
      setCheckingLeadsForReplies(false);
    }
  };

  const filteredMessages = messages.filter(message => {
    if (messageTab === 'all') return true;
    if (messageTab === 'sent' && message.type === 'outbound') return true;
    if (messageTab === 'received' && message.type === 'inbound') return true;
    if (messageTab === 'replies' && message.reply === true) return true;
    if (messageTab === 'has-replies') {
      // Show outbound messages that have corresponding inbound replies
      if (message.type === 'outbound') {
        return messages.some(m => 
          m.type === 'inbound' && 
          m.subject && message.subject &&
          (m.subject.toLowerCase().includes('re:') || 
           m.subject.toLowerCase().replace('re:', '').trim() === message.subject.toLowerCase().trim())
        );
      }
      return false;
    }
    if (messageTab === 'recent-replies') {
      if (message.reply === true && message.sent_at) {
        const messageDate = new Date(message.sent_at);
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return messageDate >= sevenDaysAgo;
      }
      return false;
    }
    return false;
  });



  const filteredLeads = leads
    .filter(lead => {
      // Filter by replied leads if option is enabled
      if (showOnlyRepliedLeads && !leadsWithReplies.has(lead.campaign_lead_map_id)) {
        return false;
      }
      
      // Filter by search term
      if (leadSearchTerm.trim()) {
        const searchLower = leadSearchTerm.toLowerCase();
        const leadName = getLeadDisplayName(lead).toLowerCase();
        const leadEmail = (lead.lead || lead.lead_data)?.email?.toLowerCase() || '';
        const companyName = (lead.lead || lead.lead_data)?.company_name?.toLowerCase() || '';
        
        return leadName.includes(searchLower) || 
               leadEmail.includes(searchLower) || 
               companyName.includes(searchLower);
      }
      
      return true;
    });

  const selectedLead = leads.find(l => l.campaign_lead_map_id === selectedLeadId);

  // Auto-select lead when search results in exactly one match
  useEffect(() => {
    if (leadSearchTerm.trim() && filteredLeads.length === 1) {
      const singleMatch = filteredLeads[0];
      if (selectedLeadId !== singleMatch.campaign_lead_map_id) {
        setSelectedLeadId(singleMatch.campaign_lead_map_id);
      }
    }
  }, [leadSearchTerm, filteredLeads, selectedLeadId]);

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Message History
            </CardTitle>
            <div className="flex space-x-2">
              <Select 
                value={selectedCampaignId} 
                onValueChange={setSelectedCampaignId}
                disabled={loadingCampaigns}
              >
                <SelectTrigger className="w-[250px]">
                  <SelectValue placeholder={loadingCampaigns ? "Loading campaigns..." : "Select a campaign"} />
                </SelectTrigger>
                <SelectContent>
                  {campaigns.map((campaign) => (
                    <SelectItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search leads..."
                    value={leadSearchTerm}
                    onChange={(e) => setLeadSearchTerm(e.target.value)}
                    className="pl-10 w-[200px]"
                    disabled={!selectedCampaignId || loadingLeads}
                  />
                </div>
                <Select 
                  value={selectedLeadId} 
                  onValueChange={setSelectedLeadId}
                  disabled={loadingLeads || filteredLeads.length === 0 || !selectedCampaignId}
                >
                  <SelectTrigger className="w-[250px]">
                    <SelectValue placeholder={
                      !selectedCampaignId ? "Select campaign first" :
                      loadingLeads ? "Loading leads..." : 
                      filteredLeads.length === 0 ? (leadSearchTerm ? "No matches found" : "No leads available") : 
                      "Select a lead"
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredLeads.map((lead) => (
                      <SelectItem key={lead.campaign_lead_map_id} value={lead.campaign_lead_map_id}>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>{getLeadDisplayName(lead)}</span>
                          {lead.contact_info && (
                            <Badge variant="secondary" className="text-xs">DB</Badge>
                          )}
                          {leadsWithReplies.has(lead.campaign_lead_map_id) && (
                            <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                              <Reply className="h-3 w-3 mr-1" />
                              Replied
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => {
                  if (selectedCampaignId && selectedLeadId) {
                    fetchMessageHistory(selectedCampaignId, selectedLeadId);
                  }
                }}
                disabled={loading || !selectedCampaignId || !selectedLeadId}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
              

            </div>
          </div>
          
          {/* Lead Filter Controls */}
          {selectedCampaignId && leads.length > 0 && (
            <div className="flex items-center gap-4 mt-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Checkbox 
                  id="show-replied-leads"
                  checked={showOnlyRepliedLeads}
                  onCheckedChange={(checked) => setShowOnlyRepliedLeads(checked === true)}
                  disabled={loadingLeads || checkingLeadsForReplies}
                />
                <label 
                  htmlFor="show-replied-leads" 
                  className="text-sm font-medium cursor-pointer flex items-center gap-1"
                >
                  <Filter className="h-4 w-4" />
                  Show only leads with replies ({leadsWithReplies.size})
                  {checkingLeadsForReplies && (
                    <RefreshCw className="h-3 w-3 animate-spin ml-1" />
                  )}
                </label>
              </div>
              
              {showOnlyRepliedLeads && leadsWithReplies.size === 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={checkAllLeadsForReplies}
                  disabled={checkingLeadsForReplies || !selectedCampaignId}
                >
                  {checkingLeadsForReplies ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                      Checking leads...
                    </>
                  ) : (
                    <>
                      <Reply className="h-4 w-4 mr-2" />
                      Find Leads with Replies
                    </>
                  )}
                </Button>
              )}
              
              {(filteredLeads.length !== leads.length || leadSearchTerm.trim()) && (
                <div className="text-sm text-gray-600">
                  Showing {filteredLeads.length} of {leads.length} leads
                  {leadSearchTerm.trim() && (
                    <span className="ml-2 text-blue-600">
                      (filtered by "{leadSearchTerm}")
                    </span>
                  )}
                </div>
              )}
            </div>
          )}
          
          {/* Lead Information */}
          {selectedLead && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Contact:</span>
                  <p className="text-gray-900">{getLeadDisplayName(selectedLead)}</p>
                  <p className="text-gray-600">{(selectedLead.lead || selectedLead.lead_data)?.email || 'N/A'}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Company:</span>
                  <p className="text-gray-900">{(selectedLead.lead || selectedLead.lead_data)?.company_name || 'N/A'}</p>
                  {(selectedLead.lead || selectedLead.lead_data)?.location && (
                    <p className="text-gray-600">{(selectedLead.lead || selectedLead.lead_data)?.location}</p>
                  )}
                </div>
                <div>
                  <span className="font-medium text-gray-600">Status:</span>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className={getMessageStatusColor(selectedLead.status)}>
                      {selectedLead.status}
                    </Badge>
                    {selectedLead.contact_info && (
                      <Badge variant="secondary">
                        Connected to DB
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardHeader>
        <CardContent>
          {selectedCampaignId && selectedLeadId ? (
            <>

              
              <div className="mb-4">
                <Tabs defaultValue="all" value={messageTab} onValueChange={setMessageTab} className="w-full">
                  <TabsList className="grid grid-cols-6 w-full max-w-4xl">
                    <TabsTrigger value="all" className="text-xs">
                      All ({messages.length})
                    </TabsTrigger>
                    <TabsTrigger value="sent" className="text-xs">
                      Sent ({messages.filter(m => m.type === 'outbound').length})
                    </TabsTrigger>
                    <TabsTrigger value="received" className="text-xs">
                      Received ({messages.filter(m => m.type === 'inbound').length})
                    </TabsTrigger>
                    <TabsTrigger value="replies" className="text-xs">
                      Replies ({messages.filter(m => m.reply === true).length})
                    </TabsTrigger>
                    <TabsTrigger value="has-replies" className="text-xs">
                      Has Replies ({messages.filter(m => {
                        if (m.type === 'outbound') {
                          return messages.some(reply => 
                            reply.type === 'inbound' && 
                            reply.subject && m.subject &&
                            (reply.subject.toLowerCase().includes('re:') || 
                             reply.subject.toLowerCase().replace('re:', '').trim() === m.subject.toLowerCase().trim())
                          );
                        }
                        return false;
                      }).length})
                    </TabsTrigger>
                    <TabsTrigger value="recent-replies" className="text-xs">
                      Recent Replies ({messages.filter(m => {
                        if (m.reply === true && m.sent_at) {
                          const messageDate = new Date(m.sent_at);
                          const sevenDaysAgo = new Date();
                          sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                          return messageDate >= sevenDaysAgo;
                        }
                        return false;
                      }).length})
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
                
                {/* Filter descriptions */}
                <div className="mb-3 text-xs text-gray-600">
                  {messageTab === 'all' && "Showing all messages in this conversation"}
                  {messageTab === 'sent' && "Showing outbound messages sent from your campaigns"}
                  {messageTab === 'received' && "Showing inbound messages received from leads"}
                  {messageTab === 'replies' && "Showing only inbound reply messages"}
                  {messageTab === 'has-replies' && "Showing outbound messages that have received replies"}
                  {messageTab === 'recent-replies' && "Showing replies received in the last 7 days"}
                </div>
              </div>
            
              <ScrollArea className="h-[600px] rounded-md border">
                {loading ? (
                  <div className="flex items-center justify-center h-40">
                    <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                    <span className="ml-2 text-gray-600">Loading messages...</span>
                  </div>
                ) : (messages.length === 0 || filteredMessages.length === 0) ? (
                  <div className="flex flex-col items-center justify-center h-40 text-gray-500">
                    <Mail className="h-12 w-12 text-gray-300 mb-2" />
                    <p className="text-lg font-medium">No messages found</p>
                    <p className="text-sm">No message history available for this lead</p>

                  </div>
                ) : (
                  <div className="space-y-4 p-4">
                    {filteredMessages.map((message, index) => (
                      <div 
                        key={message.id || index} 
                        className={`p-4 rounded-lg border transition-all hover:shadow-md ${
                          message.type === 'outbound' 
                            ? 'bg-blue-50 border-blue-200 ml-8' 
                            : 'bg-green-50 border-green-200 mr-8'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center gap-2">
                            {getMessageTypeIcon(message.type)}
                            <div>
                              <div className="flex items-center gap-2">
                                <p className="text-sm font-semibold text-gray-900">
                                  {message.type === 'outbound' ? 'Sent to:' : 'Received from:'} 
                                  <span className="ml-1 text-blue-600">
                                    {message.type === 'outbound' ? message.to : message.from}
                                  </span>
                                </p>
                                {message.reply && (
                                  <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                    <Reply className="h-3 w-3 mr-1" />
                                    Reply
                                  </Badge>
                                )}
                                {messageHasReplies(message) && (
                                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                    <MessageCircle className="h-3 w-3 mr-1" />
                                    Has Replies
                                  </Badge>
                                )}
                                {isRecentMessage(message) && (
                                  <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                                    New
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center gap-2 text-xs text-gray-500">
                                <Calendar className="h-3 w-3" />
                                {formatDate(message.sent_at)}
                                {message.sequence_number && (
                                  <>
                                    <span>•</span>
                                    <span>Sequence #{message.sequence_number}</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {message.status && (
                              <Badge className={getMessageStatusColor(message.status)}>
                                {message.status}
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        {message.subject && (
                          <div className="mb-3">
                            <p className="text-sm font-medium text-gray-700">
                              <strong>Subject:</strong> {message.subject}
                            </p>
                          </div>
                        )}
                        
                        <div className="mb-3">
                          <div className="text-sm text-gray-700 bg-white p-3 rounded border">
                            {message.html_body ? (
                              <div 
                                dangerouslySetInnerHTML={{ __html: message.html_body }} 
                                className="prose prose-sm max-w-none"
                              />
                            ) : message.body ? (
                              <p className="whitespace-pre-wrap">{message.body}</p>
                            ) : (
                              <p className="italic text-gray-500">No message content</p>
                            )}
                          </div>
                        </div>
                        
                        {/* Engagement metrics */}
                        {(message.opens !== undefined || message.clicks !== undefined) && (
                          <div className="flex items-center gap-4 text-xs text-gray-600 bg-white p-2 rounded border">
                            {message.opens !== undefined && (
                              <div className="flex items-center gap-1">
                                <Eye className="h-3 w-3" />
                                <span>Opens: {message.opens}</span>
                              </div>
                            )}
                            {message.clicks !== undefined && (
                              <div className="flex items-center gap-1">
                                <MousePointer className="h-3 w-3" />
                                <span>Clicks: {message.clicks}</span>
                              </div>
                            )}
                            {message.reply && (
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                <Reply className="h-3 w-3 mr-1" />
                                Reply
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-40 text-gray-500">
              <Mail className="h-12 w-12 text-gray-300 mb-2" />
              <p className="text-lg font-medium">Select Campaign and Lead</p>
              <p className="text-sm">Please select both a campaign and a lead to view message history</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MessageHistoryTab; 