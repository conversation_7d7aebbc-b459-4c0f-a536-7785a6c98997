import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  Card<PERSON><PERSON>er, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Square,
  Settings,
  Users,
  Mail,
  Eye,
  MessageSquare,
  TrendingUp,
  Calendar,
  Clock,
  Target,
  Zap,
  Save,
  X
} from 'lucide-react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter 
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import LeadsTab from './LeadsTab';
import MessageHistoryTab from './MessageHistoryTab';

interface Campaign {
  id: string;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  lead_count?: number;
  max_leads_per_day?: number;
  min_time_btwn_emails?: number;
  follow_up_percentage?: number;
  enable_ai_esp_matching?: boolean;
  send_as_plain_text?: boolean;
  stop_lead_settings?: any;
  track_settings?: string;
  scheduler_cron_value?: string;
  unsubscribe_text?: string;
  client_id?: number;
}

interface CampaignSequence {
  id: string;
  seq_number: number;
  subject: string;
  email_body: string;
  seq_delay_details?: {
    delayInDays: number;
  };
  created_at: string;
  updated_at: string;
}

interface CampaignManagerProps {
  onCampaignSelect?: (campaignId: string) => void;
  selectedCampaignId?: string;
  onContactSelect?: (contactId: number) => void;
  defaultTab?: string;
}

export function CampaignManager({ 
  onCampaignSelect, 
  selectedCampaignId, 
  onContactSelect,
  defaultTab = 'campaigns'
}: CampaignManagerProps) {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [campaignSequences, setCampaignSequences] = useState<CampaignSequence[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  
  // Tab management
  const [activeTab, setActiveTab] = useState(defaultTab);
  
  // Form states
  const [newCampaignName, setNewCampaignName] = useState('');
  const [editCampaignName, setEditCampaignName] = useState('');
  const [maxLeadsPerDay, setMaxLeadsPerDay] = useState('');
  const [minTimeBetweenEmails, setMinTimeBetweenEmails] = useState('');
  const [followUpPercentage, setFollowUpPercentage] = useState('');
  const [enableAiEspMatching, setEnableAiEspMatching] = useState(false);
  const [sendAsPlainText, setSendAsPlainText] = useState(false);
  const [trackSettings, setTrackSettings] = useState<string[]>([]);
  const [stopLeadSettings, setStopLeadSettings] = useState('');
  const [unsubscribeText, setUnsubscribeText] = useState('');
  
  // Schedule form states
  const [timezone, setTimezone] = useState('America/New_York');
  const [daysOfWeek, setDaysOfWeek] = useState<number[]>([1, 2, 3, 4, 5]);
  const [startHour, setStartHour] = useState('09:00');
  const [endHour, setEndHour] = useState('18:00');
  const [scheduleMinTimeBetweenEmails, setScheduleMinTimeBetweenEmails] = useState('');
  const [maxNewLeadsPerDay, setMaxNewLeadsPerDay] = useState('');
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [isSequenceDialogOpen, setIsSequenceDialogOpen] = useState(false);

  // Sequence form states
  const [sequenceSubject, setSequenceSubject] = useState('');
  const [sequenceBody, setSequenceBody] = useState('');
  const [sequenceNumber, setSequenceNumber] = useState('');
  const [sequenceDelayDays, setSequenceDelayDays] = useState('1');
  const [editingSequence, setEditingSequence] = useState<CampaignSequence | null>(null);
  const [isCreatingSequence, setIsCreatingSequence] = useState(false);
  const [isUpdatingSequence, setIsUpdatingSequence] = useState(false);
  const [isDeletingSequence, setIsDeletingSequence] = useState(false);

  // Fetch campaigns
  useEffect(() => {
    fetchCampaigns();
  }, []);

  // Fetch selected campaign details
  useEffect(() => {
    if (selectedCampaignId) {
      fetchCampaignDetails(selectedCampaignId);
      fetchCampaignSequences(selectedCampaignId);
    }
  }, [selectedCampaignId]);

  const fetchCampaigns = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/smartlead/campaigns');
      if (!response.ok) {
        throw new Error(`Failed to fetch campaigns: ${response.status}`);
      }
      const data = await response.json();
      console.log(`Campaigns: ${JSON.stringify(data, null, 2)}`);
      setCampaigns(data.campaigns || []);
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      toast.error('Failed to load campaigns');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCampaignDetails = async (campaignId: string) => {
    try {
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch campaign details: ${response.status}`);
      }
      const data = await response.json();
      setSelectedCampaign(data);
      
      // Set form values for editing
      setEditCampaignName(data.name || '');
      setMaxLeadsPerDay(data.max_leads_per_day?.toString() || '');
      setMinTimeBetweenEmails(data.min_time_btwn_emails?.toString() || '');
      setFollowUpPercentage(data.follow_up_percentage?.toString() || '');
      setEnableAiEspMatching(data.enable_ai_esp_matching || false);
      setSendAsPlainText(data.send_as_plain_text || false);
      setTrackSettings(data.track_settings ? [data.track_settings] : []);
      setStopLeadSettings(data.stop_lead_settings || '');
      setUnsubscribeText(data.unsubscribe_text || '');
    } catch (error) {
      console.error('Error fetching campaign details:', error);
      toast.error('Failed to load campaign details');
    }
  };

  const fetchCampaignSequences = async (campaignId: string) => {
    try {
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}/sequences`);
      if (!response.ok) {
        throw new Error(`Failed to fetch sequences: ${response.status}`);
      }
      const data = await response.json();
      setCampaignSequences(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching sequences:', error);
      toast.error('Failed to load campaign sequences');
    }
  };

  const handleCreateCampaign = async () => {
    if (!newCampaignName.trim()) {
      toast.error('Campaign name is required');
      return;
    }

    try {
      setIsCreating(true);
      const response = await fetch('/api/smartlead/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCampaignName.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create campaign: ${response.status}`);
      }

      const data = await response.json();
      toast.success('Campaign created successfully');
      setNewCampaignName('');
      setIsCreateDialogOpen(false);
      fetchCampaigns();
    } catch (error) {
      console.error('Error creating campaign:', error);
      toast.error('Failed to create campaign');
    } finally {
      setIsCreating(false);
    }
  };

  const handleUpdateCampaign = async () => {
    if (!selectedCampaign || !editCampaignName.trim()) {
      toast.error('Campaign name is required');
      return;
    }

    try {
      setIsEditing(true);
      const response = await fetch(`/api/smartlead/campaigns/${selectedCampaign.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editCampaignName.trim(),
          max_leads_per_day: parseInt(maxLeadsPerDay) || null,
          min_time_btwn_emails: parseInt(minTimeBetweenEmails) || null,
          follow_up_percentage: parseInt(followUpPercentage) || null,
          enable_ai_esp_matching: enableAiEspMatching,
          send_as_plain_text: sendAsPlainText,
          track_settings: trackSettings,
          stop_lead_settings: stopLeadSettings,
          unsubscribe_text: unsubscribeText,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update campaign: ${response.status}`);
      }

      toast.success('Campaign updated successfully');
      setIsEditDialogOpen(false);
      fetchCampaigns();
      if (selectedCampaign) {
        fetchCampaignDetails(selectedCampaign.id);
      }
    } catch (error) {
      console.error('Error updating campaign:', error);
      toast.error('Failed to update campaign');
    } finally {
      setIsEditing(false);
    }
  };

  const handleDeleteCampaign = async (campaignId: string) => {
    if (!confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete campaign: ${response.status}`);
      }

      toast.success('Campaign deleted successfully');
      fetchCampaigns();
      if (selectedCampaign?.id === campaignId) {
        setSelectedCampaign(null);
        setCampaignSequences([]);
      }
    } catch (error) {
      console.error('Error deleting campaign:', error);
      toast.error('Failed to delete campaign');
    }
  };

  const handleCampaignStatusChange = async (campaignId: string, newStatus: string) => {
    try {
      setIsUpdatingStatus(true);
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update campaign status: ${response.status}`);
      }

      toast.success(`Campaign ${newStatus.toLowerCase()} successfully`);
      fetchCampaigns();
      if (selectedCampaign?.id === campaignId) {
        fetchCampaignDetails(campaignId);
      }
    } catch (error) {
      console.error('Error updating campaign status:', error);
      toast.error('Failed to update campaign status');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handleUpdateSchedule = async () => {
    if (!selectedCampaign) return;

    try {
      const response = await fetch(`/api/smartlead/campaigns/${selectedCampaign.id}/schedule`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timezone,
          days_of_the_week: daysOfWeek,
          start_hour: startHour,
          end_hour: endHour,
          min_time_btw_emails: parseInt(scheduleMinTimeBetweenEmails) || 10,
          max_new_leads_per_day: parseInt(maxNewLeadsPerDay) || 20,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update campaign schedule: ${response.status}`);
      }

      toast.success('Campaign schedule updated successfully');
      setIsScheduleDialogOpen(false);
      fetchCampaignDetails(selectedCampaign.id);
    } catch (error) {
      console.error('Error updating campaign schedule:', error);
      toast.error('Failed to update campaign schedule');
    }
  };

  const handleCreateSequence = async () => {
    if (!selectedCampaign || !sequenceSubject.trim() || !sequenceBody.trim() || !sequenceNumber) {
      toast.error('All sequence fields are required');
      return;
    }

    try {
      setIsCreatingSequence(true);
      const response = await fetch(`/api/smartlead/campaigns/${selectedCampaign.id}/sequences/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: sequenceSubject.trim(),
          email_body: sequenceBody.trim(),
          seq_number: parseInt(sequenceNumber),
          delay_in_days: parseInt(sequenceDelayDays),
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create sequence: ${response.status}`);
      }

      toast.success('Sequence created successfully');
      setSequenceSubject('');
      setSequenceBody('');
      setSequenceNumber('');
      setSequenceDelayDays('1');
      setIsSequenceDialogOpen(false);
      fetchCampaignSequences(selectedCampaign.id);
    } catch (error) {
      console.error('Error creating sequence:', error);
      toast.error('Failed to create sequence');
    } finally {
      setIsCreatingSequence(false);
    }
  };

  const handleUpdateSequence = async () => {
    if (!selectedCampaign || !editingSequence || !sequenceSubject.trim() || !sequenceBody.trim() || !sequenceNumber) {
      toast.error('All sequence fields are required');
      return;
    }

    try {
      setIsUpdatingSequence(true);
      
      // Note: Smartlead API doesn't support individual sequence updates
      // We'll show a message to the user about this limitation
      toast.error('Sequence updates are not supported by the Smartlead API. Please delete and recreate the sequence.');
      setEditingSequence(null);
      setSequenceSubject('');
      setSequenceBody('');
      setSequenceNumber('');
      setSequenceDelayDays('1');
      setIsSequenceDialogOpen(false);
    } catch (error) {
      console.error('Error updating sequence:', error);
      toast.error('Failed to update sequence');
    } finally {
      setIsUpdatingSequence(false);
    }
  };

  const handleDeleteSequence = async (sequenceId: string) => {
    if (!selectedCampaign || !confirm('Are you sure you want to delete this sequence? This action cannot be undone.')) {
      return;
    }

    try {
      setIsDeletingSequence(true);
      
      // Note: Smartlead API doesn't support individual sequence deletion
      // We'll show a message to the user about this limitation
      toast.error('Sequence deletion is not supported by the Smartlead API. Please contact support for assistance.');
    } catch (error) {
      console.error('Error deleting sequence:', error);
      toast.error('Failed to delete sequence');
    } finally {
      setIsDeletingSequence(false);
    }
  };

  const handleEditSequence = (sequence: CampaignSequence) => {
    setEditingSequence(sequence);
    setSequenceSubject(sequence.subject);
    setSequenceBody(sequence.email_body);
    setSequenceNumber(sequence.seq_number.toString());
    setSequenceDelayDays(sequence.seq_delay_details?.delayInDays?.toString() || '1');
    setIsSequenceDialogOpen(true);
  };

  const handleAddSequence = () => {
    setEditingSequence(null);
    setSequenceSubject('');
    setSequenceBody('');
    setSequenceNumber((campaignSequences.length + 1).toString());
    setSequenceDelayDays('1');
    setIsSequenceDialogOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800 border-green-300';
      case 'PAUSED': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'COMPLETED': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'STOPPED': return 'bg-red-100 text-red-800 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return <Play className="h-4 w-4" />;
      case 'PAUSED': return <Pause className="h-4 w-4" />;
      case 'COMPLETED': return <Target className="h-4 w-4" />;
      case 'STOPPED': return <Square className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getDayName = (day: number) => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[day];
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Campaign Management
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Manage campaigns, leads, and message history
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="campaigns" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Campaigns
              </TabsTrigger>
              <TabsTrigger value="leads" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Leads
              </TabsTrigger>
              <TabsTrigger value="messages" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Messages
              </TabsTrigger>
            </TabsList>

            <TabsContent value="campaigns" className="mt-6">
              <div className="space-y-4">
                <div className="flex justify-end">
                  <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Campaign
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Campaign</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="campaign-name">Campaign Name</Label>
                          <Input
                            id="campaign-name"
                            value={newCampaignName}
                            onChange={(e) => setNewCampaignName(e.target.value)}
                            placeholder="Enter campaign name"
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleCreateCampaign} disabled={isCreating}>
                          {isCreating ? 'Creating...' : 'Create Campaign'}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>

                {isLoading ? (
                  <div className="flex justify-center items-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : campaigns.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Mail className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p>No campaigns found</p>
                    <p className="text-sm">Create your first campaign to get started</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {campaigns.map((campaign) => (
                      <div
                        key={campaign.id}
                        className={`flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedCampaignId === campaign.id ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                        }`}
                        onClick={() => {
                          onCampaignSelect?.(campaign.id);
                          // Auto-switch to leads tab when a campaign is selected
                          setActiveTab('leads');
                        }}
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Mail className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-medium">{campaign.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              Created {new Date(campaign.created_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            variant="outline"
                            className={getStatusColor(campaign.status)}
                          >
                            <div className="flex items-center space-x-1">
                              {getStatusIcon(campaign.status)}
                              <span>{campaign.status}</span>
                            </div>
                          </Badge>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedCampaign(campaign);
                                setEditCampaignName(campaign.name);
                                setIsEditDialogOpen(true);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteCampaign(campaign.id);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="leads" className="mt-6">
              {selectedCampaignId ? (
                <LeadsTab 
                  key={`leads-${selectedCampaignId}`}
                  onContactSelect={onContactSelect}
                  selectedCampaignId={selectedCampaignId}
                  onMessageHistoryOpen={(leadId) => {
                    // Store the lead ID for message history tab
                    localStorage.setItem('viewMessageLeadId', leadId);
                    localStorage.setItem('viewMessageCampaignId', selectedCampaignId);
                    setActiveTab('messages');
                  }}
                />
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>No campaign selected</p>
                  <p className="text-sm">Select a campaign from the Campaigns tab to view leads</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="messages" className="mt-6">
              {selectedCampaignId ? (
                <MessageHistoryTab key={`messages-${selectedCampaignId}`} />
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>No campaign selected</p>
                  <p className="text-sm">Select a campaign to view message history</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Edit Campaign Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Campaign</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-campaign-name">Campaign Name</Label>
              <Input
                id="edit-campaign-name"
                value={editCampaignName}
                onChange={(e) => setEditCampaignName(e.target.value)}
                placeholder="Enter campaign name"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="max-leads">Max Leads Per Day</Label>
                <Input
                  id="max-leads"
                  type="number"
                  value={maxLeadsPerDay}
                  onChange={(e) => setMaxLeadsPerDay(e.target.value)}
                  placeholder="100"
                />
              </div>
              <div>
                <Label htmlFor="min-time">Min Time Between Emails (minutes)</Label>
                <Input
                  id="min-time"
                  type="number"
                  value={minTimeBetweenEmails}
                  onChange={(e) => setMinTimeBetweenEmails(e.target.value)}
                  placeholder="60"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="follow-up">Follow-up Percentage</Label>
              <Input
                id="follow-up"
                type="number"
                value={followUpPercentage}
                onChange={(e) => setFollowUpPercentage(e.target.value)}
                placeholder="80"
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="ai-esp-matching"
                  checked={enableAiEspMatching}
                  onCheckedChange={(checked) => setEnableAiEspMatching(checked as boolean)}
                />
                <Label htmlFor="ai-esp-matching">Enable AI ESP Matching</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="plain-text"
                  checked={sendAsPlainText}
                  onCheckedChange={(checked) => setSendAsPlainText(checked as boolean)}
                />
                <Label htmlFor="plain-text">Send as Plain Text</Label>
              </div>
            </div>
            <div>
              <Label htmlFor="track-settings">Track Settings</Label>
              <Select value={trackSettings[0] || ''} onValueChange={(value) => setTrackSettings([value])}>
                <SelectTrigger>
                  <SelectValue placeholder="Select track settings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DONT_TRACK_EMAIL_OPEN">Don't Track Email Open</SelectItem>
                  <SelectItem value="DONT_TRACK_LINK_CLICK">Don't Track Link Click</SelectItem>
                  <SelectItem value="DONT_TRACK_REPLY_TO_AN_EMAIL">Don't Track Reply to Email</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="stop-lead-settings">Stop Lead Settings</Label>
              <Select value={stopLeadSettings} onValueChange={setStopLeadSettings}>
                <SelectTrigger>
                  <SelectValue placeholder="Select stop lead settings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CLICK_ON_A_LINK">Click on a Link</SelectItem>
                  <SelectItem value="OPEN_AN_EMAIL">Open an Email</SelectItem>
                  <SelectItem value="REPLY_TO_AN_EMAIL">Reply to an Email</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="unsubscribe-text">Unsubscribe Text</Label>
              <Input
                id="unsubscribe-text"
                value={unsubscribeText}
                onChange={(e) => setUnsubscribeText(e.target.value)}
                placeholder="Don't Contact Me"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateCampaign} disabled={isEditing}>
              {isEditing ? 'Updating...' : 'Update Campaign'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Schedule Dialog */}
      <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Campaign Schedule</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="timezone">Timezone</Label>
              <Select value={timezone} onValueChange={setTimezone}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="America/New_York">Eastern Time</SelectItem>
                  <SelectItem value="America/Chicago">Central Time</SelectItem>
                  <SelectItem value="America/Denver">Mountain Time</SelectItem>
                  <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="Europe/London">London</SelectItem>
                  <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                  <SelectItem value="Australia/Sydney">Sydney</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Days of the Week</Label>
              <div className="grid grid-cols-7 gap-2 mt-2">
                {[0, 1, 2, 3, 4, 5, 6].map((day) => (
                  <div key={day} className="flex items-center space-x-2">
                    <Checkbox
                      id={`day-${day}`}
                      checked={daysOfWeek.includes(day)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setDaysOfWeek([...daysOfWeek, day]);
                        } else {
                          setDaysOfWeek(daysOfWeek.filter(d => d !== day));
                        }
                      }}
                    />
                    <Label htmlFor={`day-${day}`} className="text-xs">
                      {getDayName(day).slice(0, 3)}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start-hour">Start Hour</Label>
                <Input
                  id="start-hour"
                  type="time"
                  value={startHour}
                  onChange={(e) => setStartHour(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="end-hour">End Hour</Label>
                <Input
                  id="end-hour"
                  type="time"
                  value={endHour}
                  onChange={(e) => setEndHour(e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="schedule-min-time">Min Time Between Emails (minutes)</Label>
                <Input
                  id="schedule-min-time"
                  type="number"
                  value={scheduleMinTimeBetweenEmails}
                  onChange={(e) => setScheduleMinTimeBetweenEmails(e.target.value)}
                  placeholder="10"
                />
              </div>
              <div>
                <Label htmlFor="max-new-leads">Max New Leads Per Day</Label>
                <Input
                  id="max-new-leads"
                  type="number"
                  value={maxNewLeadsPerDay}
                  onChange={(e) => setMaxNewLeadsPerDay(e.target.value)}
                  placeholder="20"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsScheduleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateSchedule}>
              <Save className="h-4 w-4 mr-2" />
              Update Schedule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Sequence Dialog */}
      <Dialog open={isSequenceDialogOpen} onOpenChange={setIsSequenceDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingSequence ? 'Edit Sequence' : 'Add New Sequence'}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sequence-number">Sequence Number</Label>
                <Input
                  id="sequence-number"
                  type="number"
                  value={sequenceNumber}
                  onChange={(e) => setSequenceNumber(e.target.value)}
                  placeholder="1"
                />
              </div>
              <div>
                <Label htmlFor="sequence-delay">Delay (Days)</Label>
                <Input
                  id="sequence-delay"
                  type="number"
                  value={sequenceDelayDays}
                  onChange={(e) => setSequenceDelayDays(e.target.value)}
                  placeholder="1"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="sequence-subject">Subject Line</Label>
              <Input
                id="sequence-subject"
                value={sequenceSubject}
                onChange={(e) => setSequenceSubject(e.target.value)}
                placeholder="Enter email subject"
              />
            </div>
            <div>
              <Label htmlFor="sequence-body">Email Body</Label>
              <Textarea
                id="sequence-body"
                value={sequenceBody}
                onChange={(e) => setSequenceBody(e.target.value)}
                placeholder="Enter email content (HTML supported)"
                rows={10}
                className="font-mono text-sm"
              />
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <Label className="text-sm font-medium">Preview</Label>
              <div 
                className="mt-2 text-sm text-gray-600 max-h-32 overflow-y-auto"
                dangerouslySetInnerHTML={{ __html: sequenceBody }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSequenceDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={editingSequence ? handleUpdateSequence : handleCreateSequence}
              disabled={isCreatingSequence || isUpdatingSequence}
            >
              {isCreatingSequence || isUpdatingSequence ? 'Saving...' : (editingSequence ? 'Update Sequence' : 'Create Sequence')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 