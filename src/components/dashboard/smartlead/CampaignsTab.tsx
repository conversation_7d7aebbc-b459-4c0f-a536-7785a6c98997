"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search, RefreshCw } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";

interface Campaign {
  id: number | string;
  name: string;
  status: string;
  created_at: string;
  sent_count?: string | number;
  open_count?: string | number;
  click_count?: string | number;
  reply_count?: string | number;
  total_count?: string | number;
  lead_count?: number;
  user_id?: number;
  sequence_count?: string | number;
  drafted_count?: string | number;
  campaign_lead_stats?: {
    total: number;
    paused: number;
    blocked: number;
    revenue: number;
    stopped: number;
    completed: number;
    inprogress: number;
    interested: number;
    notStarted: number;
  };
}

const CampaignsTab: React.FC = () => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [newCampaignName, setNewCampaignName] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [leadCounts, setLeadCounts] = useState<Record<string, number>>({});
  const [loadingCounts, setLoadingCounts] = useState(false);

  useEffect(() => {
    fetchCampaigns();
  }, []);

  const fetchCampaigns = async (nameFilter: string = '') => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams();
      if (nameFilter) {
        queryParams.append('name', nameFilter);
      }

      const response = await fetch(`/api/smartlead/campaigns?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch campaigns: ${response.status}`);
      }
      
      const data = await response.json();
      if (data && data.campaigns) {
        setCampaigns(data.campaigns);
      } else {
        setCampaigns([]);
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      toast.error(`Failed to fetch campaigns: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchLeadCounts = async (campaignIds: string[]) => {
    if (!campaignIds.length) return;
    
    setLoadingCounts(true);
    try {
      const counts: Record<string, number> = {};
      
      // Create promises for all campaigns to fetch in parallel
      const countPromises = campaignIds.map(async (campaignId) => {
        try {
          const response = await fetch(`/api/smartlead/campaigns/${campaignId}/lead-count`);
          if (response.ok) {
            const data = await response.json();
            counts[campaignId] = data.count || 0;
          } else {
            counts[campaignId] = 0;
          }
        } catch (error) {
          console.error(`Error fetching lead count for campaign ${campaignId}:`, error);
          counts[campaignId] = 0;
        }
      });
      
      // Wait for all fetch operations to complete
      await Promise.all(countPromises);
      
      setLeadCounts(counts);
    } catch (error) {
      console.error('Error fetching lead counts:', error);
    } finally {
      setLoadingCounts(false);
    }
  };

  useEffect(() => {
    if (campaigns.length > 0) {
      fetchLeadCounts(campaigns.map(campaign => String(campaign.id)));
    }
  }, [campaigns]);

  const handleSearch = () => {
    fetchCampaigns(searchQuery);
  };

  const handleCreateCampaign = async () => {
    if (!newCampaignName.trim()) {
      toast.error('Campaign name is required');
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch('/api/smartlead/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCampaignName,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create campaign: ${response.status}`);
      }

      toast.success('Campaign created successfully');
      setNewCampaignName('');
      setIsCreateDialogOpen(false);
      fetchCampaigns(searchQuery); // Refresh the list
    } catch (error) {
      console.error('Error creating campaign:', error);
      toast.error(`Failed to create campaign: ${(error as Error).message}`);
    } finally {
      setIsCreating(false);
    }
  };

  const refreshLeadCount = async (campaignId: string) => {
    try {
      // Show loading for just this campaign
      setLeadCounts(prev => ({
        ...prev,
        [campaignId]: -1 // Use -1 to indicate loading state for this specific campaign
      }));
      
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}/lead-count`);
      if (response.ok) {
        const data = await response.json();
        setLeadCounts(prev => ({
          ...prev,
          [campaignId]: data.count || 0
        }));
      } else {
        setLeadCounts(prev => ({
          ...prev,
          [campaignId]: 0
        }));
      }
    } catch (error) {
      console.error(`Error refreshing lead count for campaign ${campaignId}:`, error);
      setLeadCounts(prev => ({
        ...prev,
        [campaignId]: 0
      }));
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle>Email Campaigns</CardTitle>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Campaign
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Campaign</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="campaign-name">Campaign Name</Label>
                    <Input
                      id="campaign-name"
                      placeholder="Enter campaign name"
                      value={newCampaignName}
                      onChange={(e) => setNewCampaignName(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsCreateDialogOpen(false)}
                    disabled={isCreating}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleCreateCampaign}
                    disabled={isCreating}
                  >
                    {isCreating ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : 'Create Campaign'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between mb-4">
            <div className="flex gap-2 items-center">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search campaigns..."
                  className="pl-9 w-[260px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <Button variant="outline" size="sm" onClick={handleSearch}>
                Search
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => fetchCampaigns(searchQuery)}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          <ScrollArea className="h-[500px] rounded-md border">
            {loading ? (
              <div className="flex items-center justify-center h-40">
                <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : campaigns.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-gray-500">
                <p>No campaigns found</p>
                {searchQuery && (
                  <Button variant="ghost" className="mt-2" onClick={() => {
                    setSearchQuery('');
                    fetchCampaigns('');
                  }}>
                    Clear search
                  </Button>
                )}
              </div>
            ) : (
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">ID</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Name</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <div className="flex items-center space-x-1">
                        <span>Leads</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 opacity-50 hover:opacity-100"
                          onClick={() => fetchLeadCounts(campaigns.map(campaign => String(campaign.id)))}
                          disabled={loadingCounts}
                        >
                          <RefreshCw className={`h-3 w-3 ${loadingCounts ? 'animate-spin' : ''}`} />
                        </Button>
                      </div>
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Created</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {campaigns.map((campaign) => (
                    <tr key={campaign.id} className="border-b hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900">{campaign.id}</td>
                      <td className="px-4 py-3 text-sm font-medium text-gray-900">{campaign.name}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        <div className="flex items-center space-x-1">
                          {leadCounts[String(campaign.id)] === -1 ? (
                            <RefreshCw className="h-3 w-3 animate-spin text-gray-400" />
                          ) : (
                            <span>{leadCounts[String(campaign.id)] !== undefined ? leadCounts[String(campaign.id)] : (campaign.lead_count || 0)}</span>
                          )}
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-6 w-6 ml-1 opacity-50 hover:opacity-100" 
                            onClick={(e) => {
                              e.stopPropagation();
                              refreshLeadCount(String(campaign.id));
                            }}
                          >
                            <RefreshCw className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          campaign.status === 'STARTED' || campaign.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                          campaign.status === 'PAUSED' ? 'bg-yellow-100 text-yellow-800' :
                          campaign.status === 'STOPPED' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {campaign.status === 'STARTED' ? 'Active' :
                           campaign.status === 'STOPPED' ? 'Stopped' :
                           campaign.status === 'PAUSED' ? 'Paused' :
                           campaign.status || 'Unknown'}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        {campaign.created_at ? new Date(campaign.created_at).toLocaleDateString() : 'Unknown'}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => {
                            // Navigate to leads for this campaign
                            const tabsElement = document.querySelector('[role="tablist"]');
                            const leadsTab = tabsElement?.querySelector('[value="leads"]') as HTMLElement;
                            if (leadsTab) leadsTab.click();
                            // Set selected campaign in local storage for the leads tab
                            localStorage.setItem('selectedCampaignId', String(campaign.id));
                          }}
                        >
                          View Leads
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignsTab; 