"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardHeader, CardTitle, CardContent } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../ui/tabs'
import { Input } from '../../ui/input'
import { Label } from '../../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select'
import { 
  Play, 
  Pause, 
  Building, 
  Mail, 
  Search, 
  Sparkles,
  Target, 
  PenTool,
  CheckCircle2,
  Clock,
  Activity,
  RefreshCw,
  Filter,
  X,
  Upload,
  AlertTriangle,
  Users,
  TrendingUp,
  AlertCircle,
  Download,
  Globe,
  FileText,
  Inbox,
  Send,
  RotateCcw,
  List,
  Zap,
  CheckCircle,
  XCircle,
  Copy,
  Trash2,
  <PERSON><PERSON><PERSON>,
  <PERSON>r,
  Loader2,
  Database,
  Cloud,
  Server
} from 'lucide-react'
import { PROCESSOR_REGISTRY, getEnabledProcessors } from '../../../lib/queue/config/processor-registry'
import { ProcessingStage } from '../../../types/processing'

interface QueueStatus {
  name: string
  waiting: number
  active: number
  completed: number
  failed: number
  delayed: number
  paused: boolean
}

interface JobStatus {
  id: string
  name: string
  data: any
  progress: number
  state: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed'
  createdAt: string
  processedAt?: string
  failedReason?: string
}

interface ProcessorStats {
  totalJobs: number
  completedJobs: number
  failedJobs: number
  activeJobs: number
  waitingJobs: number
  queues: QueueStatus[]
  // Add per-processor stats
  processorStats?: {
    [processorType: string]: {
      total: number
      pending: number
      running: number
      completed: number
      failed: number
      error: number
      success_rate: number
    }
  }
  // Add cron job info
  cronJobs?: {
    [processorType: string]: {
      enabled: boolean
      schedule: string
      lastRun?: string
      nextRun?: string
    }
  }
}

const NewProcessingDashboard = () => {
  const [stats, setStats] = useState<ProcessorStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [executing, setExecuting] = useState<Set<string>>(new Set())
  const [processingLimit, setProcessingLimit] = useState(100)
  const [singleEntityId, setSingleEntityId] = useState('')
  const [selectedProcessor, setSelectedProcessor] = useState<ProcessingStage | ''>('')
  const [selectedQueue, setSelectedQueue] = useState<string>('')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [jobs, setJobs] = useState<JobStatus[]>([])
  const [showJobDetails, setShowJobDetails] = useState<string | null>(null)
  const [repeatableJobStatus, setRepeatableJobStatus] = useState<Record<string, boolean>>({})
  const [cronSchedules, setCronSchedules] = useState<Record<string, string>>({})
  const [editingCron, setEditingCron] = useState<string | null>(null)

  // Get enabled processors from registry
  const enabledProcessors = getEnabledProcessors()
  
  // Categorize processors by category
  const backgroundSyncProcessors = enabledProcessors.filter(p => p.category === 'BACKGROUND_SYNC')
  const standardProcessors = enabledProcessors.filter(p => p.category === 'STANDARD')
  
  // Further categorize standard processors by type for better organization
  const contactProcessors = standardProcessors.filter(p => 
    ['email_validation', 'contact_enrichment_v2', 'contact_investment_criteria', 'email_generation', 'smartlead_sync', 'job_tiering'].includes(p.processorType)
  )
  
  const companyProcessors = standardProcessors.filter(p => 
    ['website_scraping', 'company_overview_v2', 'company_investment_criteria', 'company_web_crawl'].includes(p.processorType)
  )
  
  const articleProcessors = standardProcessors.filter(p => 
    ['article_html_fetch', 'article_link_fetch', 'article_enrichment'].includes(p.processorType)
  )
  
  const storageProcessors = standardProcessors.filter(p => 
    ['storage_migration_single', 'storage_migration_bulk'].includes(p.processorType)
  )

  useEffect(() => {
    fetchStats()
    fetchJobs()
    fetchRepeatableJobStatus()
  }, [])

  useEffect(() => {
    if (!autoRefreshEnabled) return

    const interval = setInterval(() => {
      if (!isRefreshing) {
        setIsRefreshing(true)
        Promise.all([fetchStats(), fetchJobs(), fetchRepeatableJobStatus()])
          .finally(() => setIsRefreshing(false))
      }
    }, 10000) // Refresh every 10 seconds

    return () => clearInterval(interval)
  }, [autoRefreshEnabled, isRefreshing])

  const fetchStats = async () => {
    try {
      setError(null)
      
      // Fetch both queue stats and cron job info in parallel
      const [queueResponse, cronResponse] = await Promise.all([
        fetch('/api/processing/queue/stats', {
          signal: AbortSignal.timeout(15000)
        }),
        fetch('/api/processors/cron?action=status', {
          signal: AbortSignal.timeout(15000)
        })
      ])

      if (!queueResponse.ok) {
        throw new Error(`Queue stats HTTP ${queueResponse.status}`)
      }

      const queueData = await queueResponse.json()
      let cronData = { jobs: [] }
      
      if (cronResponse.ok) {
        cronData = await cronResponse.json()
      }

      if (queueData.success) {
        // Merge queue stats with cron job info
        const enhancedStats = {
          ...queueData.stats,
          cronJobs: {},
          processorStats: {}
        }

        // Add cron job info for each processor
        if (cronData.jobs) {
          cronData.jobs.forEach((job: any) => {
            const processorType = job.processorType
            enhancedStats.cronJobs[processorType] = {
              enabled: job.enabled,
              schedule: job.cronExpression,
              lastRun: job.lastRun,
              nextRun: job.nextRun
            }
          })
        }

        // Add real processor stats based on actual data
        for (const processor of enabledProcessors) {
          if (processor.processorType === 'email_worker') {
            // Gmail sync stats - show actual sync data
            const gmailStats = await getGmailSyncStats()
            enhancedStats.processorStats[processor.processorType] = gmailStats
          } else if (processor.processorType === 'fireflies_worker') {
            // Fireflies sync stats - show actual sync data
            const firefliesStats = await getFirefliesSyncStats()
            enhancedStats.processorStats[processor.processorType] = firefliesStats
          } else {
            // For other processors, use mock data for now
            enhancedStats.processorStats[processor.processorType] = {
              total: Math.floor(Math.random() * 1000) + 100,
              pending: Math.floor(Math.random() * 100),
              running: Math.floor(Math.random() * 10),
              completed: Math.floor(Math.random() * 800) + 50,
              failed: Math.floor(Math.random() * 20),
              error: Math.floor(Math.random() * 5),
              success_rate: Math.floor(Math.random() * 30) + 70
            }
          }
        }

        setStats(enhancedStats)
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch stats:', error)
        setError(error.message)
      }
    } finally {
      setLoading(false)
    }
  }

  const fetchJobs = async () => {
    try {
      const response = await fetch('/api/processing/queue/jobs', {
        signal: AbortSignal.timeout(15000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      if (data.success) {
        setJobs(data.jobs || [])
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch jobs:', error)
      }
    }
  }

  const fetchRepeatableJobStatus = async () => {
    try {
      const response = await fetch('/api/processing/queue/repeatable/status', {
        signal: AbortSignal.timeout(15000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      if (data.success) {
        setRepeatableJobStatus(data.status || {})
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch repeatable job status:', error)
      }
    }
  }

  const executeProcessor = async (processorType: ProcessingStage, options: any = {}) => {
    const jobKey = `${processorType}_${Date.now()}`
    setExecuting(prev => new Set([...prev, jobKey]))

    try {
      const response = await fetch('/api/processing/queue/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          processorType,
          options: {
            limit: processingLimit,
            singleId: singleEntityId ? parseInt(singleEntityId) : undefined,
            batchSize: Math.min(50, processingLimit),
            ...options
          }
        })
      })

      const data = await response.json()
      if (data.success) {
        await fetchStats()
        await fetchJobs()
      } else {
        setError(data.message || 'Failed to execute processor')
      }
    } catch (error) {
      console.error('Failed to execute processor:', error)
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setExecuting(prev => {
        const newSet = new Set(prev)
        newSet.delete(jobKey)
        return newSet
      })
    }
  }

  const pauseRepeatableJob = async (processorType: ProcessingStage) => {
    try {
      const response = await fetch('/api/processing/queue/repeatable/pause', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ processorType })
      })

      if (response.ok) {
        await fetchStats()
        await fetchRepeatableJobStatus()
      }
    } catch (error) {
      console.error('Failed to pause repeatable job:', error)
    }
  }

  const resumeRepeatableJob = async (processorType: ProcessingStage) => {
    try {
      const response = await fetch('/api/processing/queue/repeatable/resume', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ processorType })
      })

      if (response.ok) {
        await fetchStats()
        await fetchRepeatableJobStatus()
      }
    } catch (error) {
      console.error('Failed to resume repeatable job:', error)
    }
  }

  const pauseAllRepeatableJobs = async () => {
    try {
      const response = await fetch('/api/processing/queue/repeatable/pause', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pauseAll: true })
      })

      if (response.ok) {
        await fetchStats()
        await fetchRepeatableJobStatus()
      }
    } catch (error) {
      console.error('Failed to pause all repeatable jobs:', error)
    }
  }

  const resumeAllRepeatableJobs = async () => {
    try {
      const response = await fetch('/api/processing/queue/repeatable/resume', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ resumeAll: true })
      })

      if (response.ok) {
        await fetchStats()
        await fetchRepeatableJobStatus()
      }
    } catch (error) {
      console.error('Failed to resume all repeatable jobs:', error)
    }
  }

  const clearQueue = async (queueName: string) => {
    try {
      const response = await fetch('/api/processing/queue/clear', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ queueName })
      })

      if (response.ok) {
        await fetchStats()
        await fetchJobs()
      }
    } catch (error) {
      console.error('Failed to clear queue:', error)
    }
  }

  const toggleRepeatableJob = async (processorType: ProcessingStage) => {
    const isRunning = repeatableJobStatus[processorType]
    if (isRunning) {
      await pauseRepeatableJob(processorType)
    } else {
      await resumeRepeatableJob(processorType)
    }
  }

  const getProcessorIcon = (processorType: ProcessingStage) => {
    switch (processorType) {
      case 'email_validation':
      case 'email_generation':
        return <Mail className="h-5 w-5" />
      case 'contact_enrichment_v2':
      case 'contact_investment_criteria':
        return <Users className="h-5 w-5" />
      case 'company_overview_v2':
      case 'company_investment_criteria':
        return <Building className="h-5 w-5" />
      case 'website_scraping':
      case 'company_web_crawl':
        return <Globe className="h-5 w-5" />
      case 'article_html_fetch':
      case 'article_enrichment':
      case 'article_link_fetch':
        return <FileText className="h-5 w-5" />
      case 'storage_migration_single':
      case 'storage_migration_bulk':
        return <Cloud className="h-5 w-5" />
      case 'smartlead_sync':
        return <Send className="h-5 w-5" />
      case 'job_tiering':
        return <Target className="h-5 w-5" />
      default:
        return <Settings className="h-5 w-5" />
    }
  }

  const getQueueIcon = (queueName: string) => {
    if (queueName.includes('short')) return <Zap className="h-4 w-4" />
    if (queueName.includes('medium')) return <Clock className="h-4 w-4" />
    if (queueName.includes('long')) return <Database className="h-4 w-4" />
    return <Server className="h-4 w-4" />
  }

  // Get processor stats for a specific processor
  const getProcessorStats = (processorType: string) => {
    if (!stats?.processorStats?.[processorType]) {
      return { total: 0, pending: 0, running: 0, completed: 0, failed: 0, error: 0, success_rate: 0 }
    }
    return stats.processorStats[processorType]
  }

  // Get cron info for a specific processor
  const getCronInfo = (processorType: string) => {
    if (!stats?.cronJobs?.[processorType]) {
      return { enabled: false, schedule: '', lastRun: '', nextRun: '' }
    }
    return stats.cronJobs[processorType]
  }

  // Handle cron schedule update
  const handleCronScheduleUpdate = async (processorType: string, newSchedule: string) => {
    try {
      // First disable the current cron job
      await fetch(`/api/processors/cron?jobId=${processorType}_cron`, {
        method: 'DELETE'
      })

      // Then create a new one with the updated schedule
      const response = await fetch('/api/processors/cron', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobId: `${processorType}_cron`,
          schedule: newSchedule
        })
      })

      if (response.ok) {
        setCronSchedules(prev => ({ ...prev, [processorType]: newSchedule }))
        setEditingCron(null)
        // Refresh stats to get updated cron info
        fetchStats()
      } else {
        console.error('Failed to update cron schedule')
      }
    } catch (error) {
      console.error('Error updating cron schedule:', error)
    }
  }

  // Get real Gmail sync stats
  const getGmailSyncStats = async () => {
    try {
      const response = await fetch('/api/sync-stats/gmail')
      if (response.ok) {
        const data = await response.json()
        return data.stats
      }
    } catch (error) {
      console.error('Error fetching Gmail sync stats:', error)
    }
    
    // Fallback to basic stats
    return {
      total: 0,
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      error: 0,
      success_rate: 0
    }
  }

  // Get real Fireflies sync stats
  const getFirefliesSyncStats = async () => {
    try {
      const response = await fetch('/api/sync-stats/fireflies')
      if (response.ok) {
        const data = await response.json()
        return data.stats
      }
    } catch (error) {
      console.error('Error fetching Fireflies sync stats:', error)
    }
    
    // Fallback to basic stats
    return {
      total: 0,
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      error: 0,
      success_rate: 0
    }
  }

  const renderProcessorCard = (processor: any) => {
    const isExecuting = Array.from(executing).some(key => key.startsWith(processor.processorType))
    const isCronRunning = repeatableJobStatus[processor.processorType] ?? false
    const isBackgroundSync = processor.category === 'BACKGROUND_SYNC'
    const processorStats = getProcessorStats(processor.processorType)
    const cronInfo = getCronInfo(processor.processorType)
    const isEditingCron = editingCron === processor.processorType
    
    return (
      <Card key={processor.processorType} className={`hover:shadow-md transition-shadow ${isBackgroundSync ? 'border-green-200 bg-green-50' : ''}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            {getProcessorIcon(processor.processorType)}
            {processor.processorType.replace(/_/g, ' ').toUpperCase()}
            {isBackgroundSync && (
              <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
                Always On
              </Badge>
            )}
          </CardTitle>
          <p className="text-sm text-gray-600">{processor.description}</p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Stats Section */}
          {processorStats.total > 0 && (
            <div className="space-y-3">
              {/* Progress Bar */}
              <div>
                <div className="flex justify-between text-xs text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>{Math.round(processorStats.success_rate)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${processorStats.success_rate}%` }}
                  />
                </div>
              </div>

              {/* Status breakdown */}
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="flex items-center gap-1 text-yellow-600">
                  <Clock className="h-3 w-3" />
                  <span>P: {processorStats.pending}</span>
                </div>
                <div className="flex items-center gap-1 text-blue-600">
                  <Activity className="h-3 w-3" />
                  <span>R: {processorStats.running}</span>
                </div>
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span>C: {processorStats.completed}</span>
                </div>
                <div className="flex items-center gap-1 text-red-600">
                  <XCircle className="h-3 w-3" />
                  <span>F: {processorStats.failed}</span>
                </div>
                <div className="flex items-center gap-1 text-red-600">
                  <AlertCircle className="h-3 w-3" />
                  <span>E: {processorStats.error}</span>
                </div>
                <div className="flex items-center gap-1 text-purple-600">
                  <TrendingUp className="h-3 w-3" />
                  <span>{Math.round(processorStats.success_rate)}%</span>
                </div>
              </div>
            </div>
          )}

          {/* Queue and Limit Info */}
          <div className="flex items-center justify-between text-sm">
            <span>Queue: {processor.queueType}</span>
            <Badge variant="outline">
              Limit: {processor.defaultOptions.limit}
            </Badge>
          </div>
          
          {/* Execute Button */}
          <Button
            onClick={() => executeProcessor(processor.processorType)}
            disabled={isExecuting || processorStats.pending === 0}
            className="w-full"
            size="sm"
          >
            {isExecuting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                {processorStats.pending === 0 ? 'No items to process' : `Process ${Math.min(processingLimit, processorStats.pending)}`}
              </>
            )}
          </Button>
          
          {/* Cron Controls */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Cron Status:</span>
              <Badge variant={isCronRunning ? "default" : "secondary"}>
                {isCronRunning ? "Running" : "Paused"}
              </Badge>
            </div>
            
            {isBackgroundSync ? (
              <div className="text-center">
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Always Running
                </Badge>
                <p className="text-xs text-gray-500 mt-1">Cannot be disabled</p>
              </div>
            ) : (
              <div className="space-y-2">
                {/* Cron Schedule Display/Edit */}
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-600">Schedule:</span>
                  {isEditingCron ? (
                    <div className="flex gap-1 flex-1">
                      <Input
                        value={cronSchedules[processor.processorType] || cronInfo.schedule}
                        onChange={(e) => setCronSchedules(prev => ({ ...prev, [processor.processorType]: e.target.value }))}
                        placeholder="*/30 * * * *"
                        className="text-xs h-6"
                      />
                      <Button
                        size="sm"
                        onClick={() => handleCronScheduleUpdate(processor.processorType, cronSchedules[processor.processorType] || cronInfo.schedule)}
                        className="h-6 px-2"
                      >
                        <CheckCircle className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingCron(null)}
                        className="h-6 px-2"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 flex-1">
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded flex-1">
                        {cronInfo.schedule || 'Not scheduled'}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setEditingCron(processor.processorType)}
                        className="h-6 px-1"
                      >
                        <Settings className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>

                {/* Cron Toggle Button */}
                <Button
                  onClick={() => toggleRepeatableJob(processor.processorType)}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  {isCronRunning ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause Cron
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Resume Cron
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  const renderQueueCard = (queue: QueueStatus) => {
    return (
      <Card key={queue.name} className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getQueueIcon(queue.name)}
              {queue.name.toUpperCase()}
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => clearQueue(queue.name)}
                size="sm"
                variant="outline"
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="flex justify-between">
                <span>Waiting:</span>
                <Badge variant="outline">{queue.waiting}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Active:</span>
                <Badge variant="outline">{queue.active}</Badge>
              </div>
            </div>
            <div>
              <div className="flex justify-between">
                <span>Completed:</span>
                <Badge variant="outline" className="text-green-600">{queue.completed}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Failed:</span>
                <Badge variant="outline" className="text-red-600">{queue.failed}</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading processing dashboard...</span>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Processing Dashboard</h1>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="limit">Batch Limit:</Label>
            <Input
              id="limit"
              type="number"
              value={processingLimit}
              onChange={(e) => setProcessingLimit(parseInt(e.target.value) || 100)}
              className="w-24"
              min="1"
              max="1000"
            />
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="entityId">Entity ID:</Label>
            <Input
              id="entityId"
              type="number"
              value={singleEntityId}
              onChange={(e) => setSingleEntityId(e.target.value)}
              className="w-24"
              placeholder="Optional"
            />
          </div>
          <Button
            onClick={() => {
              setIsRefreshing(true)
              Promise.all([fetchStats(), fetchJobs(), fetchRepeatableJobStatus()])
                .finally(() => setIsRefreshing(false))
            }}
            variant="outline"
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button
            onClick={() => setAutoRefreshEnabled(!autoRefreshEnabled)}
            variant={autoRefreshEnabled ? "default" : "outline"}
          >
            {autoRefreshEnabled ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            Auto Refresh
          </Button>
          <Button
            onClick={pauseAllRepeatableJobs}
            variant="outline"
            className="text-orange-600 hover:text-orange-700"
          >
            <Pause className="h-4 w-4 mr-2" />
            Pause All Cron
          </Button>
          <Button
            onClick={resumeAllRepeatableJobs}
            variant="outline"
            className="text-green-600 hover:text-green-700"
          >
            <Play className="h-4 w-4 mr-2" />
            Resume All Cron
          </Button>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
              <Button
                onClick={() => setError(null)}
                size="sm"
                variant="ghost"
                className="ml-auto"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Jobs</p>
                  <p className="text-2xl font-bold">{stats.totalJobs}</p>
                </div>
                <Activity className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-green-600">{stats.completedJobs}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Failed</p>
                  <p className="text-2xl font-bold text-red-600">{stats.failedJobs}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.activeJobs}</p>
                </div>
                <Loader2 className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="processors" className="space-y-4">
        <TabsList>
          <TabsTrigger value="processors">Processors</TabsTrigger>
          <TabsTrigger value="queues">Queues</TabsTrigger>
          <TabsTrigger value="jobs">Jobs</TabsTrigger>
        </TabsList>

        {/* Processors Tab */}
        <TabsContent value="processors" className="space-y-6">
          <div className="space-y-6">
            {/* Background Sync Processors - Always Running */}
            {backgroundSyncProcessors.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Zap className="h-5 w-5 text-green-600" />
                  Background Sync
                  <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
                    Always Running
                  </Badge>
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {backgroundSyncProcessors.map(renderProcessorCard)}
                </div>
              </div>
            )}

            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Users className="h-5 w-5" />
                Contact Processors
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {contactProcessors.map(renderProcessorCard)}
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Building className="h-5 w-5" />
                Company Processors
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {companyProcessors.map(renderProcessorCard)}
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Article Processors
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {articleProcessors.map(renderProcessorCard)}
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Cloud className="h-5 w-5" />
                Storage Processors
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {storageProcessors.map(renderProcessorCard)}
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Queues Tab */}
        <TabsContent value="queues" className="space-y-4">
          <h2 className="text-xl font-semibold mb-4">Queue Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {stats?.queues.map(renderQueueCard)}
          </div>
        </TabsContent>

        {/* Jobs Tab */}
        <TabsContent value="jobs" className="space-y-4">
          <h2 className="text-xl font-semibold mb-4">Recent Jobs</h2>
          <div className="space-y-2">
            {jobs.map(job => (
              <Card key={job.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Badge variant={
                        job.state === 'completed' ? 'default' :
                        job.state === 'failed' ? 'destructive' :
                        job.state === 'active' ? 'secondary' : 'outline'
                      }>
                        {job.state}
                      </Badge>
                      <span className="font-medium">{job.name}</span>
                      <span className="text-sm text-gray-500">
                        {new Date(job.createdAt).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      {job.progress > 0 && (
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all"
                            style={{ width: `${job.progress}%` }}
                          />
                        </div>
                      )}
                      <Button
                        onClick={() => setShowJobDetails(showJobDetails === job.id ? null : job.id)}
                        size="sm"
                        variant="outline"
                      >
                        {showJobDetails === job.id ? 'Hide' : 'Details'}
                      </Button>
                    </div>
                  </div>
                  {showJobDetails === job.id && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <pre className="text-sm overflow-auto">
                        {JSON.stringify(job.data, null, 2)}
                      </pre>
                      {job.failedReason && (
                        <div className="mt-2 text-red-600 text-sm">
                          <strong>Error:</strong> {job.failedReason}
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default NewProcessingDashboard
