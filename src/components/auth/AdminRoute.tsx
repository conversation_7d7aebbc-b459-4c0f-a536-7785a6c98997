"use client"

import React from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Lock, ArrowLeft, Loader2 } from 'lucide-react';

interface ExtendedSession {
  user: {
    id: string;
    name: string;
    email: string;
    user_id: number;
    role: string;
    is_admin: boolean;
    permissions?: string[];
  };
}

interface AdminRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  fallbackPath?: string;
}

export default function AdminRoute({ 
  children, 
  requiredPermission,
  fallbackPath = '/dashboard'
}: AdminRouteProps) {
  const { data: session, status } = useSession() as { 
    data: ExtendedSession | null; 
    status: 'loading' | 'authenticated' | 'unauthenticated' 
  };
  const router = useRouter();

  // Show loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (status === 'unauthenticated' || !session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="flex flex-col items-center space-y-4">
              <Lock className="h-12 w-12 text-red-500" />
              <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-900">
                  Authentication Required
                </h2>
                <p className="text-gray-600 mt-2">
                  Please sign in to access this page.
                </p>
              </div>
              <Button onClick={() => router.push('/login')}>
                Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check admin permissions - simplified: just check if role is admin
  const isAdmin = session.user.role === 'admin';
  const userPermissions = session.user.permissions || [];
  const hasRequiredPermission = isAdmin;

  if (!hasRequiredPermission) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-lg">
          <CardContent className="p-6">
            <div className="flex flex-col items-center space-y-4">
              <Shield className="h-12 w-12 text-red-500" />
              <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-900">
                  Access Denied
                </h2>
                <p className="text-gray-600 mt-2">
                  {requiredPermission 
                    ? `You don't have the required permission: ${requiredPermission}`
                    : 'You need administrator privileges to access this page.'
                  }
                </p>
                
                <Alert className="mt-4 text-left">
                  <AlertDescription>
                    <strong>Current Role:</strong> {session.user.role}<br />
                    <strong>Admin Access:</strong> {isAdmin ? 'Yes' : 'No'}<br />
                    <strong>Required:</strong> Role must be "admin"
                  </AlertDescription>
                </Alert>
              </div>
              
              <div className="flex space-x-2">
                <Button 
                  variant="outline"
                  onClick={() => router.push(fallbackPath)}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Go Back
                </Button>
                <Button onClick={() => router.push('/dashboard')}>
                  Go to Dashboard
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // User has required permissions - render the protected content
  return <>{children}</>;
}
