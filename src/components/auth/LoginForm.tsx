"use client"

import React, { useState } from 'react';
import { signIn, getSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Rocket, Eye, EyeOff, AlertTriangle, Loader2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface LoginFormProps {
  onSuccess?: () => void;
}

export default function LoginForm({ onSuccess }: LoginFormProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/dashboard';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Check for impersonation token first
      const impersonationToken = localStorage.getItem('impersonation_token');
      
      if (impersonationToken) {
        // Authenticate with impersonation token
        const result = await signIn('credentials', {
          email: 'impersonation',
          impersonation_token: impersonationToken,
          redirect: false
        });

        if (result?.ok) {
          localStorage.removeItem('impersonation_token');
          toast({
            title: "Impersonation Active",
            description: "You are now logged in as the impersonated user",
          });
          
          if (onSuccess) {
            onSuccess();
          } else {
            router.push(callbackUrl);
          }
          return;
        } else {
          localStorage.removeItem('impersonation_token');
          setError('Invalid impersonation session. Please login normally.');
        }
      }

      // Regular login
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false
      });

      if (result?.ok) {
        // Check if password change is required
        const session = await getSession();
        if (session?.user && 'force_password_change' in session.user && session.user.force_password_change) {
          toast({
            title: "Password Change Required",
            description: "You must change your password before continuing",
            variant: "destructive"
          });
          router.push('/change-password');
          return;
        }

        toast({
          title: "Login Successful",
          description: `Welcome back, ${session?.user?.name}!`,
        });

        if (onSuccess) {
          onSuccess();
        } else {
          router.push(callbackUrl);
        }
      } else {
        if (result?.error === 'CredentialsSignin') {
          setError('Invalid email or password. Please check your credentials and try again.');
        } else {
          setError(result?.error || 'An error occurred during login. Please try again.');
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-4">
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-2">
              <Rocket className="h-8 w-8 text-blue-600" />
              <span className="text-2xl font-bold text-gray-900">Anax</span>
            </div>
          </div>
          <CardTitle className="text-center text-xl">
            Sign in to your account
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div>
              <Label htmlFor="email">Email address</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={loading}
                placeholder="Enter your email"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="password">Password</Label>
              <div className="relative mt-1">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={loading}
                  placeholder="Enter your password"
                  className="pr-10"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-3"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                'Sign in'
              )}
            </Button>

            <div className="text-center text-sm">
              <a 
                href="#" 
                className="text-blue-600 hover:text-blue-500"
                onClick={(e) => {
                  e.preventDefault();
                  toast({
                    title: "Password Reset",
                    description: "Please contact your administrator to reset your password.",
                  });
                }}
              >
                Forgot your password?
              </a>
            </div>

            <div className="text-center text-xs text-gray-500">
              <p>
                Default admin credentials: <EMAIL> / admin123
                <br />
                <span className="text-orange-600">
                  Please change default passwords in production!
                </span>
              </p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
