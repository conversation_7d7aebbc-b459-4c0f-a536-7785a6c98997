"use client"

import { useState, useEffect } from 'react'
import { <PERSON> } from 'lucide-react'

// Define access levels and their corresponding passwords
const ACCESS_LEVELS = {
  admin: ['anaxadmin2025'],    
  restricted: ['palmese', 'stern', 'andrea']  // Add your new password here
} as const

type AccessLevel = keyof typeof ACCESS_LEVELS

export const PasswordCheck = ({ onSuccess }: { onSuccess: (level: AccessLevel) => void }) => {
  const [password, setPassword] = useState('')
  const [error, setError] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Ensure hydration consistency by only rendering icons on client
  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Find which access level the password belongs to
    const accessLevel = Object.entries(ACCESS_LEVELS).find(
      ([_, passwords]) => (passwords as readonly string[]).includes(password)
    )?.[0] as AccessLevel | undefined

    if (accessLevel) {
      onSuccess(accessLevel)
      localStorage.setItem('dashboardAccess', accessLevel)
      window.location.reload() // Force a reload to ensure proper initialization
    } else {
      setError(true)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-white p-8 rounded-xl shadow-lg w-full max-w-md">
        <div className="flex items-center justify-center mb-8" suppressHydrationWarning>
          {isClient ? (
            <Rocket className="h-12 w-12 text-blue-600" />
          ) : (
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <div className="w-8 h-8 bg-blue-600 rounded" 
                   style={{
                     clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)'
                   }}>
              </div>
            </div>
          )}
        </div>
        <h2 className="text-2xl font-bold text-center mb-8">Enter Password</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <input
              type="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value)
                setError(false)
              }}
              className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                error ? 'border-red-500' : 'border-gray-200'
              }`}
              placeholder="Enter password"
            />
            {error && (
              <p className="text-red-500 text-sm mt-1">Incorrect password</p>
            )}
          </div>
          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Access Dashboard
          </button>
        </form>
      </div>
    </div>
  )
} 