"use client"

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  LogOut, 
  User, 
  Shield,
  AlertTriangle,
  X
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface ExtendedSession {
  user: {
    id: string;
    name: string;
    email: string;
    user_id: number;
    role: string;
    is_admin: boolean;
    permissions: string[];
    impersonated_by?: number;
  };
  isImpersonating?: boolean;
  originalAdmin?: {
    id: string;
    name: string;
    email: string;
    user_id: number;
    role: string;
    is_admin: boolean;
  };
}

export default function ImpersonationBanner() {
  const { data: session } = useSession() as { data: ExtendedSession | null };
  const [loading, setLoading] = useState(false);

  if (!session?.isImpersonating || !session?.originalAdmin) {
    return null;
  }

  const handleStopImpersonation = async () => {
    setLoading(true);

    try {
      const response = await fetch('/api/admin/impersonate', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          targetUserId: session.user.user_id 
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Impersonation Ended",
          description: "You have been returned to your admin account",
        });
        
        // Refresh the page to end the session
        window.location.reload();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to stop impersonation",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to stop impersonation",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Alert className="border-orange-200 bg-orange-50 text-orange-800 mb-4">
      <Eye className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="font-medium">
            You are impersonating {session.user.name}
          </span>
          <Badge variant="outline" className="bg-orange-100 text-orange-700 border-orange-300">
            <User className="w-3 h-3 mr-1" />
            {session.user.email}
          </Badge>
          <span className="text-sm">
            as admin {session.originalAdmin.name}
          </span>
          <Badge variant="outline" className="bg-red-100 text-red-700 border-red-300">
            <Shield className="w-3 h-3 mr-1" />
            {session.originalAdmin.email}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center text-xs text-orange-600">
            <AlertTriangle className="w-3 h-3 mr-1" />
            <span>All actions are logged</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleStopImpersonation}
            disabled={loading}
            className="bg-white hover:bg-gray-50 text-orange-700 border-orange-300"
          >
            {loading ? (
              <>
                <div className="w-3 h-3 mr-1 animate-spin rounded-full border-b-2 border-orange-600" />
                Ending...
              </>
            ) : (
              <>
                <X className="w-3 h-3 mr-1" />
                Stop Impersonation
              </>
            )}
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
