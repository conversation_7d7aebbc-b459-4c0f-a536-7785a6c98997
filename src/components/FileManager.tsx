'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { FileDetailsModal } from './FileDetailsModal';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { toast } from 'sonner';
import { 
  CloudUpload, 
  Download, 
  Trash2, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Link,
  ChevronL<PERSON><PERSON>,
  ChevronRight
} from 'lucide-react';

interface FileWithStorage {
  file_id: string;
  original_name: string;
  file_size_bytes: number;
  storage_provider: string;
  storage_provider_name: string;
  storage_url: string;
  uploaded_at: string;
  migration_status?: 'pending' | 'processing' | 'completed' | 'failed';
  relationship_count?: number;
  is_public?: boolean;
  access_level?: 'private' | 'team' | 'public';
}

interface FileRelationship {
  relationship_id: string;
  file_id: string;
  target_table_name: string;
  target_column_name: string;
  target_row_id: string;
  relationship_type?: string;
  relationship_title?: string;
  relationship_notes?: string;
  display_order?: number;
  is_primary?: boolean;
  created_at: string;
  updated_at: string;
  // File details (when expanded)
  file?: {
    file_id: string;
    original_name: string;
    file_size_bytes: number;
    storage_provider: string;
    is_public?: boolean;
    access_level?: 'private' | 'team' | 'public';
    uploaded_at: string;
  };
}

interface MigrationStats {
  source_provider: string;
  target_provider: string;
  status: string;
  count: number;
}

export function FileManager() {
  const [files, setFiles] = useState<FileWithStorage[]>([]);
  const [relationships, setRelationships] = useState<FileRelationship[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [selectedRelationships, setSelectedRelationships] = useState<string[]>([]);
  const [migrationStats, setMigrationStats] = useState<MigrationStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [relationshipsLoading, setRelationshipsLoading] = useState(false);
  const [migrating, setMigrating] = useState(false);
  const [targetProvider, setTargetProvider] = useState('azure');
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [relationshipsPage, setRelationshipsPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [relationshipsTotalPages, setRelationshipsTotalPages] = useState(1);
  const [totalFiles, setTotalFiles] = useState(0);
  const [totalRelationships, setTotalRelationships] = useState(0);
  const [showOrphanedOnly, setShowOrphanedOnly] = useState(false);
  const [expandedRelationships, setExpandedRelationships] = useState<Set<string>>(new Set());
  const pageSize = 20;
  
  const { showDialog, ConfirmationDialog } = useConfirmationDialog();

  // Load files and stats
  useEffect(() => {
    loadFiles();
    loadMigrationStats();
  }, [currentPage, showOrphanedOnly]);

  // Load relationships
  useEffect(() => {
    loadRelationships();
  }, [relationshipsPage]);

  const loadFiles = async () => {
    try {
      setLoading(true);
      const url = showOrphanedOnly 
        ? `/api/files?withStorage=true&page=${currentPage}&pageSize=${pageSize}&orphanedOnly=true`
        : `/api/files?withStorage=true&page=${currentPage}&pageSize=${pageSize}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.success) {
        setFiles(data.files || []);
        setTotalFiles(data.total || data.files.length);
        setTotalPages(Math.ceil((data.total || data.files.length) / pageSize));
      } else {
        console.error('Failed to load files:', data.message);
      }
    } catch (error) {
      console.error('Error loading files:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRelationships = async () => {
    try {
      setRelationshipsLoading(true);
      const response = await fetch(`/api/files/relationships?page=${relationshipsPage}&pageSize=${pageSize}`);
      const data = await response.json();
      
      if (data.success) {
        setRelationships(data.relationships || []);
        setTotalRelationships(data.total || data.relationships.length);
        setRelationshipsTotalPages(Math.ceil((data.total || data.relationships.length) / pageSize));
      } else {
        console.error('Failed to load relationships:', data.message);
      }
    } catch (error) {
      console.error('Error loading relationships:', error);
    } finally {
      setRelationshipsLoading(false);
    }
  };

  const loadMigrationStats = async () => {
    try {
      const response = await fetch('/api/migration');
      const data = await response.json();
      if (data.success) {
        // Get migration job stats
        const migrationJobs = data.jobs || [];
        const stats = migrationJobs.reduce((acc: any, job: any) => {
          const key = `${job.data?.metadata?.sourceProvider || 'unknown'}_${job.data?.metadata?.targetProvider || 'unknown'}`;
          if (!acc[key]) {
            acc[key] = {
              source_provider: job.data?.metadata?.sourceProvider || 'unknown',
              target_provider: job.data?.metadata?.targetProvider || 'unknown',
              status: job.state || 'unknown',
              count: 0
            };
          }
          acc[key].count++;
          return acc;
        }, {});
        setMigrationStats(Object.values(stats));
      }
    } catch (error) {
      console.error('Error loading migration stats:', error);
    }
  };

  const handleFileSelect = (fileId: string, checked: boolean) => {
    if (checked) {
      setSelectedFiles([...selectedFiles, fileId]);
    } else {
      setSelectedFiles(selectedFiles.filter(id => id !== fileId));
    }
  };

  const handleRelationshipSelect = (relationshipId: string, checked: boolean) => {
    if (checked) {
      setSelectedRelationships([...selectedRelationships, relationshipId]);
    } else {
      setSelectedRelationships(selectedRelationships.filter(id => id !== relationshipId));
    }
  };

  const handleExpandRelationship = async (relationshipId: string) => {
    const isExpanded = expandedRelationships.has(relationshipId);
    const newExpanded = new Set(expandedRelationships);
    
    if (isExpanded) {
      newExpanded.delete(relationshipId);
    } else {
      newExpanded.add(relationshipId);
      // Load file details if not already loaded
      const relationship = relationships.find(r => r.relationship_id === relationshipId);
      if (relationship && !relationship.file) {
        try {
          const response = await fetch(`/api/files/${relationship.file_id}`);
          const data = await response.json();
          if (data.success) {
            relationship.file = data.file;
            setRelationships([...relationships]);
          }
        } catch (error) {
          console.error('Error loading file details:', error);
        }
      }
    }
    
    setExpandedRelationships(newExpanded);
  };

  const handleDeleteRelationship = async (relationshipId: string) => {
    const relationship = relationships.find(r => r.relationship_id === relationshipId);
    if (!relationship) return;

    const confirmed = await new Promise<boolean>((resolve) => {
      showDialog({
        title: 'Delete Relationship',
        description: `Are you sure you want to delete this relationship? This will remove the file from ${relationship.target_table_name}.${relationship.target_column_name}.${relationship.target_row_id}`,
        onConfirm: () => resolve(true),
        variant: 'warning'
      });
    });

    if (!confirmed) return;

    try {
      const response = await fetch(`/api/files/relationships/${relationshipId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.fileDeleted) {
          toast.success(`Relationship deleted and file permanently removed: ${result.message}`);
        } else if (result.fileOrphaned) {
          toast.warning(`Relationship deleted and file orphaned: ${result.message}`);
        } else {
          toast.success(`Relationship deleted: ${result.message}`);
        }
        
        // Remove from local state
        setRelationships(relationships.filter(r => r.relationship_id !== relationshipId));
        setSelectedRelationships(selectedRelationships.filter(id => id !== relationshipId));
        loadFiles(); // Refresh files in case counts changed
      } else {
        const errorData = await response.json();
        toast.error(`Failed to delete relationship: ${errorData.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error deleting relationship:', error);
      toast.error('An error occurred while deleting the relationship. Please try again.');
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(files.map(f => f.file_id));
    } else {
      setSelectedFiles([]);
    }
  };

  const handleSelectAllRelationships = (checked: boolean) => {
    if (checked) {
      setSelectedRelationships(relationships.map(r => r.relationship_id));
    } else {
      setSelectedRelationships([]);
    }
  };

  const handleMigrate = async () => {
    if (selectedFiles.length === 0) return;

    const confirmed = await new Promise<boolean>((resolve) => {
      showDialog({
        title: 'Confirm Migration',
        description: `Are you sure you want to migrate ${selectedFiles.length} files from local storage to ${targetProvider}? This action cannot be undone.`,
        onConfirm: () => resolve(true),
        variant: 'warning'
      });
    });

    if (!confirmed) return;

    try {
      setMigrating(true);
      const response = await fetch('/api/migration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sourceProvider: 'local',
          targetProvider,
          fileIds: selectedFiles,
          batchSize: 10
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success(`Migration job created: ${data.jobId}. Check the processing queue for progress.`);
        setSelectedFiles([]);
        loadFiles();
        loadMigrationStats();
      } else {
        toast.error(`Migration failed: ${data.message}`);
      }
    } catch (error) {
      console.error('Migration error:', error);
      toast.error('Migration failed');
    } finally {
      setMigrating(false);
    }
  };

  const handleMigrateAll = async () => {
    const confirmed = await new Promise<boolean>((resolve) => {
      showDialog({
        title: 'Confirm Bulk Migration',
        description: `Are you sure you want to migrate ALL local files to ${targetProvider}? This will process ${totalFiles} files and cannot be undone.`,
        onConfirm: () => resolve(true),
        variant: 'destructive'
      });
    });

    if (!confirmed) return;

    try {
      setMigrating(true);
      const response = await fetch('/api/migration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sourceProvider: 'local',
          targetProvider,
          batchSize: 10
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success(`Bulk migration job created: ${data.jobId}. Check the processing queue for progress.`);
        loadFiles();
        loadMigrationStats();
      } else {
        toast.error(`Migration failed: ${data.message}`);
      }
    } catch (error) {
      console.error('Migration error:', error);
      toast.error('Migration failed');
    } finally {
      setMigrating(false);
    }
  };

  const handleDryRun = async () => {
    try {
      const response = await fetch('/api/migration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sourceProvider: 'local',
          targetProvider,
          fileIds: selectedFiles.length > 0 ? selectedFiles : undefined,
          dryRun: true
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.info(data.message);
      } else {
        toast.error(`Dry run failed: ${data.message}`);
      }
    } catch (error) {
      console.error('Dry run error:', error);
      toast.error('Dry run failed');
    }
  };

  const handleFileClick = (fileId: string) => {
    setSelectedFileId(fileId);
    setShowDetailsModal(true);
  };

  const handleCloseDetails = () => {
    setSelectedFileId(null);
    setShowDetailsModal(false);
  };

  const handleFileDelete = (fileId: string) => {
    const file = files.find(f => f.file_id === fileId);
    if (!file) return;

    showDialog({
      title: 'Delete File',
      description: `Are you sure you want to delete "${file.original_name}"?\n\nThis will remove the file and all its relationships. If this file is not used elsewhere, it will be permanently deleted.`,
      confirmText: 'Delete File',
      cancelText: 'Cancel',
      variant: 'destructive',
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/files/${fileId}`, {
            method: 'DELETE'
          });

          if (response.ok) {
            const result = await response.json();
            
            // Show appropriate success message based on what happened
            if (result.fileDeleted) {
              toast.success(`File "${file.original_name}" has been permanently deleted: ${result.message}`);
            } else if (result.fileOrphaned) {
              toast.warning(`File "${file.original_name}" has been kept as orphaned: ${result.message}`);
            } else {
              toast.success(`File deletion result: ${result.message}`);
            }
            
            // Remove from local state
            setFiles(files.filter(f => f.file_id !== fileId));
            setSelectedFiles(selectedFiles.filter(id => id !== fileId));
          } else {
            const errorData = await response.json();
            toast.error(`Failed to delete file: ${errorData.message || 'Unknown error'}`);
          }
        } catch (error) {
          console.error('Error deleting file:', error);
          toast.error('An error occurred while deleting the file. Please try again.');
        }
      },
    });
  };

  const handleRelationshipDelete = (relationshipId: string) => {
    // Refresh the file details by reloading files
    loadFiles();
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleRefresh = () => {
    loadFiles();
    loadMigrationStats();
  };

  const handleRefreshRelationships = () => {
    loadRelationships();
  };

  const handleOrphanedToggle = () => {
    setShowOrphanedOnly(!showOrphanedOnly);
    setCurrentPage(1);
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getProviderBadgeColor = (provider: string) => {
    switch (provider) {
      case 'azure':
        return 'bg-blue-100 text-blue-800';
      case 'local':
        return 'bg-gray-100 text-gray-800';
      case 's3':
        return 'bg-orange-100 text-orange-800';
      case 'gdrive':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">File Manager</h1>
      </div>

      <Tabs defaultValue="relationships" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="relationships">Relationships ({totalRelationships})</TabsTrigger>
          <TabsTrigger value="files">Files ({totalFiles})</TabsTrigger>
        </TabsList>

        <TabsContent value="relationships" className="space-y-4">
          {/* Relationships Tab */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>File Relationships</CardTitle>
                <div className="flex gap-2">
                  <Button onClick={handleRefreshRelationships} variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {relationshipsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading relationships...</span>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedRelationships.length === relationships.length && relationships.length > 0}
                          onCheckedChange={handleSelectAllRelationships}
                        />
                      </TableHead>
                      <TableHead>Target</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {relationships.map((relationship) => (
                      <>
                        <TableRow key={relationship.relationship_id} className="hover:bg-gray-50">
                          <TableCell>
                            <Checkbox
                              checked={selectedRelationships.includes(relationship.relationship_id)}
                              onCheckedChange={(checked) => 
                                handleRelationshipSelect(relationship.relationship_id, checked as boolean)
                              }
                            />
                          </TableCell>
                          <TableCell 
                            className="font-medium cursor-pointer hover:text-blue-600"
                            onClick={() => handleExpandRelationship(relationship.relationship_id)}
                          >
                            <div className="flex items-center gap-2">
                              {relationship.target_table_name}.{relationship.target_column_name}.{relationship.target_row_id}
                              <Eye className="h-4 w-4 text-gray-400" />
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {relationship.relationship_type || 'attachment'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {relationship.relationship_title || 'Untitled'}
                          </TableCell>
                          <TableCell>
                            {new Date(relationship.created_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => handleDeleteRelationship(relationship.relationship_id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                        {expandedRelationships.has(relationship.relationship_id) && relationship.file && (
                          <TableRow className="bg-gray-50">
                            <TableCell colSpan={6}>
                              <div className="p-4 border-l-4 border-blue-200 bg-blue-50">
                                <h4 className="font-semibold text-sm mb-2">File Details</h4>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                  <div>
                                    <span className="font-medium">Name:</span>
                                    <p>{relationship.file.original_name}</p>
                                  </div>
                                  <div>
                                    <span className="font-medium">Size:</span>
                                    <p>{formatFileSize(relationship.file.file_size_bytes)}</p>
                                  </div>
                                  <div>
                                    <span className="font-medium">Storage:</span>
                                    <p>{relationship.file.storage_provider}</p>
                                  </div>
                                  <div>
                                    <span className="font-medium">Access:</span>
                                    <Badge 
                                      className={
                                        relationship.file.is_public 
                                          ? 'bg-green-100 text-green-800' 
                                          : relationship.file.access_level === 'team'
                                          ? 'bg-blue-100 text-blue-800'
                                          : 'bg-red-100 text-red-800'
                                      }
                                    >
                                      {relationship.file.is_public ? 'Public' : relationship.file.access_level || 'Private'}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
            
            {/* Relationships Pagination */}
            {relationshipsTotalPages > 1 && (
              <div className="flex items-center justify-between px-6 py-4 border-t">
                <div className="text-sm text-gray-500">
                  Showing {((relationshipsPage - 1) * pageSize) + 1} to {Math.min(relationshipsPage * pageSize, totalRelationships)} of {totalRelationships} relationships
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setRelationshipsPage(relationshipsPage - 1)}
                    disabled={relationshipsPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                  
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, relationshipsTotalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      const isActive = pageNum === relationshipsPage;
                      return (
                        <Button
                          key={pageNum}
                          variant={isActive ? "default" : "outline"}
                          size="sm"
                          onClick={() => setRelationshipsPage(pageNum)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                    {relationshipsTotalPages > 5 && (
                      <>
                        <span className="text-gray-500">...</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setRelationshipsPage(relationshipsTotalPages)}
                          className="w-8 h-8 p-0"
                        >
                          {relationshipsTotalPages}
                        </Button>
                      </>
                    )}
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setRelationshipsPage(relationshipsPage + 1)}
                    disabled={relationshipsPage === relationshipsTotalPages}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="files" className="space-y-4">
          {/* Files Tab */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="orphaned-only"
                  checked={showOrphanedOnly}
                  onCheckedChange={handleOrphanedToggle}
                />
                <label htmlFor="orphaned-only" className="text-sm font-medium">
                  Show orphaned files only ({showOrphanedOnly ? totalFiles : '0'})
                </label>
              </div>
            </div>
            <div className="flex gap-2">
              <Button 
                onClick={handleDryRun} 
                variant="outline"
                className="flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                Dry Run
              </Button>
              <Select value={targetProvider} onValueChange={setTargetProvider}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Target Provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="azure">Azure Blob</SelectItem>
                  <SelectItem value="local">Local Storage</SelectItem>
                  <SelectItem value="s3">AWS S3</SelectItem>
                  <SelectItem value="gdrive">Google Drive</SelectItem>
                </SelectContent>
              </Select>
              <Button 
                onClick={handleMigrate} 
                disabled={selectedFiles.length === 0 || migrating}
                className="flex items-center gap-2"
              >
                <CloudUpload className="h-4 w-4" />
                {migrating ? 'Migrating...' : `Migrate ${selectedFiles.length} Files`}
              </Button>
              <Button 
                onClick={handleMigrateAll} 
                disabled={migrating}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <CloudUpload className="h-4 w-4" />
                {migrating ? 'Migrating...' : 'Migrate All Local Files'}
              </Button>
            </div>
          </div>

          {/* Migration Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Migration Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {migrationStats.map((stat, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">
                  {stat.source_provider} → {stat.target_provider}
                </div>
                <div className="text-2xl font-bold">{stat.count}</div>
                <Badge variant={stat.status === 'completed' ? 'default' : 'secondary'}>
                  {stat.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Files Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Files ({totalFiles} total)</CardTitle>
            <div className="flex gap-2">
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading files...</span>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedFiles.length === files.length && files.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Storage Provider</TableHead>
                  <TableHead>Access</TableHead>
                  <TableHead>
                    Relationships
                    {showOrphanedOnly && (
                      <Badge variant="destructive" className="ml-2 text-xs">
                        Orphaned (0)
                      </Badge>
                    )}
                  </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Uploaded</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {files.map((file) => (
                  <TableRow key={file.file_id} className="hover:bg-gray-50">
                    <TableCell>
                      <Checkbox
                        checked={selectedFiles.includes(file.file_id)}
                        onCheckedChange={(checked) => 
                          handleFileSelect(file.file_id, checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell 
                      className="font-medium cursor-pointer hover:text-blue-600"
                      onClick={() => handleFileClick(file.file_id)}
                    >
                      <div className="flex items-center gap-2">
                        {file.original_name}
                        <Eye className="h-4 w-4 text-gray-400" />
                      </div>
                    </TableCell>
                    <TableCell>{formatFileSize(file.file_size_bytes)}</TableCell>
                    <TableCell>
                      <Badge className={getProviderBadgeColor(file.storage_provider)}>
                        {file.storage_provider_name}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Badge 
                          className={
                            file.is_public 
                              ? 'bg-green-100 text-green-800' 
                              : file.access_level === 'team'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-red-100 text-red-800'
                          }
                        >
                          {file.is_public ? 'Public' : file.access_level || 'Private'}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Link className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">
                          {file.relationship_count || 0}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(file.migration_status)}
                        {file.migration_status || 'Ready'}
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(file.uploaded_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.open(`/api/files/${file.file_id}/download`, '_blank')}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleFileClick(file.file_id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-gray-500">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalFiles)} of {totalFiles} files
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = i + 1;
                  const isActive = pageNum === currentPage;
                  return (
                    <Button
                      key={pageNum}
                      variant={isActive ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                      className="w-8 h-8 p-0"
                    >
                      {pageNum}
                    </Button>
                  );
                })}
                {totalPages > 5 && (
                  <>
                    <span className="text-gray-500">...</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(totalPages)}
                      className="w-8 h-8 p-0"
                    >
                      {totalPages}
                    </Button>
                  </>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </Card>
        </TabsContent>
      </Tabs>

      {/* File Details Modal */}
      <FileDetailsModal
        fileId={selectedFileId}
        isOpen={showDetailsModal}
        onClose={handleCloseDetails}
        onDelete={handleFileDelete}
        onDeleteRelationship={handleRelationshipDelete}
      />
      <ConfirmationDialog />
    </div>
  );
}
