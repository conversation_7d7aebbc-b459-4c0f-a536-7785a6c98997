'use client'

import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Building2,
  User,
  Merge,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react'
import { DuplicateRecord } from '@/types/duplicate'
import { MergePreview, MergeOptions } from '@/lib/services/duplicateResolutionService'

interface DuplicateResolutionModalProps {
  isOpen: boolean
  onClose: () => void
  duplicate: DuplicateRecord & { primary_name: string; duplicate_name: string; primary_data?: any; duplicate_data?: any }
  onResolve: () => Promise<void>
}

interface FieldSelection {
  [fieldName: string]: 'primary' | 'duplicate' | 'custom'
}

function DuplicateResolutionModal({
  isOpen,
  onClose,
  duplicate,
  onResolve
}: DuplicateResolutionModalProps) {
  const [loading, setLoading] = useState(false)
  const [mergePreview, setMergePreview] = useState<MergePreview | null>(null)
  const [fieldSelections, setFieldSelections] = useState<FieldSelection>({})
  const [customValues, setCustomValues] = useState<Record<string, any>>({})
  const [mergeRelatedRecords] = useState(true)
  const [activeTab, setActiveTab] = useState('compare')
  const [banner, setBanner] = useState<{
    show: boolean
    type: 'success' | 'error'
    title: string
    message: string
  }>({ show: false, type: 'success', title: '', message: '' })

  useEffect(() => {
    if (isOpen && duplicate) {
      loadMergePreview()
    }
  }, [isOpen, duplicate])

  const showBanner = (type: 'success' | 'error', title: string, message: string) => {
    setBanner({ show: true, type, title, message })
    // Auto-hide banner after 5 seconds
    setTimeout(() => {
      setBanner(prev => ({ ...prev, show: false }))
    }, 5000)
  }

  const loadMergePreview = async () => {
    try {
      const response = await fetch('/api/duplicates/merge-preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          primaryId: duplicate.primary_record_id,
          duplicateId: duplicate.duplicate_record_id,
          recordType: duplicate.record_type
        })
      })
      
      const data = await response.json()
      if (data.success) {
        setMergePreview(data.preview)
        
        // Initialize field selections - default to primary for all fields
        const initialSelections: FieldSelection = {}
        if (data.preview.conflictingFields) {
          data.preview.conflictingFields.forEach((field: string) => {
            initialSelections[field] = 'primary'
          })
        }
        setFieldSelections(initialSelections)
      }
    } catch (error) {
      console.error('Error loading merge preview:', error)
    }
  }

  const handleFieldSelectionChange = (field: string, selection: 'primary' | 'duplicate' | 'custom') => {
    setFieldSelections(prev => ({
      ...prev,
      [field]: selection
    }))
  }

  const handleCustomValueChange = (field: string, value: any) => {
    setCustomValues(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleMerge = async () => {
    setLoading(true)
    try {
      const mergeOptions: MergeOptions = {
        primaryRecordId: duplicate.primary_record_id,
        duplicateRecordId: duplicate.duplicate_record_id,
        recordType: duplicate.record_type,
        fieldSelections,
        customValues,
        mergeRelatedRecords
      }

      const response = await fetch('/api/duplicates/resolve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duplicateId: duplicate.id,
          action: 'merge',
          mergeStrategy: mergeOptions,
          resolvedBy: 'user' // You might want to get this from auth context
        })
      })

      const data = await response.json()
      if (data.success) {
        await onResolve()
        onClose()
      } else {
        showBanner('error', 'Merge Failed', data.error)
      }
    } catch (error) {
      console.error('Error merging records:', error)
      showBanner('error', 'Error', 'Error merging records')
    } finally {
      setLoading(false)
    }
  }

  const handleKeepSeparate = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/duplicates/resolve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duplicateId: duplicate.id,
          action: 'keep_separate',
          resolvedBy: 'user'
        })
      })

      const data = await response.json()
      if (data.success) {
        await onResolve()
        onClose()
      }
    } catch (error) {
      console.error('Error keeping separate:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleMarkFalsePositive = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/duplicates/resolve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duplicateId: duplicate.id,
          action: 'mark_false_positive',
          resolvedBy: 'user'
        })
      })

      const data = await response.json()
      if (data.success) {
        await onResolve()
        onClose()
      }
    } catch (error) {
      console.error('Error marking false positive:', error)
    } finally {
      setLoading(false)
    }
  }

  const renderFieldComparison = (field: string, primaryValue: any, duplicateValue: any) => {
    const isConflicting = mergePreview?.conflictingFields.includes(field)
    
    return (
      <div key={field} className={`border rounded-lg p-4 ${isConflicting ? 'border-orange-200 bg-orange-50' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium capitalize">{field.replace(/_/g, ' ')}</h4>
          {isConflicting && (
            <Badge variant="outline" className="text-orange-600 border-orange-600">
              Conflict
            </Badge>
          )}
        </div>
        
        {isConflicting && (
          <RadioGroup
            value={fieldSelections[field] || 'primary'}
            onValueChange={(value) => handleFieldSelectionChange(field, value as any)}
            className="space-y-3"
          >
            <div className="flex items-start space-x-3 p-3 rounded-lg border border-blue-200 bg-blue-50">
              <RadioGroupItem value="primary" id={`${field}-primary`} className="mt-1" />
              <div className="flex-1">
                <Label htmlFor={`${field}-primary`} className="text-blue-800 font-medium cursor-pointer">
                  Use Primary Value
                </Label>
                <div className="mt-1 p-2 bg-white rounded border text-sm">
                  {primaryValue || <span className="text-gray-400">Empty</span>}
                </div>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-3 rounded-lg border border-green-200 bg-green-50">
              <RadioGroupItem value="duplicate" id={`${field}-duplicate`} className="mt-1" />
              <div className="flex-1">
                <Label htmlFor={`${field}-duplicate`} className="text-green-800 font-medium cursor-pointer">
                  Use Duplicate Value
                </Label>
                <div className="mt-1 p-2 bg-white rounded border text-sm">
                  {duplicateValue || <span className="text-gray-400">Empty</span>}
                </div>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-3 rounded-lg border border-purple-200 bg-purple-50">
              <RadioGroupItem value="custom" id={`${field}-custom`} className="mt-1" />
              <div className="flex-1">
                <Label htmlFor={`${field}-custom`} className="text-purple-800 font-medium cursor-pointer">
                  Use Custom Value
                </Label>
                <div className="mt-1">
                  <Input
                    value={customValues[field] || ''}
                    onChange={(e) => handleCustomValueChange(field, e.target.value)}
                    placeholder="Enter custom value"
                    className="bg-white"
                  />
                </div>
              </div>
            </div>
          </RadioGroup>
        )}
        
        {!isConflicting && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-blue-600">Primary</Label>
              <div className="mt-1 p-2 bg-blue-50 rounded border text-sm">
                {primaryValue || <span className="text-gray-400">Empty</span>}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-green-600">Duplicate</Label>
              <div className="mt-1 p-2 bg-green-50 rounded border text-sm">
                {duplicateValue || <span className="text-gray-400">Empty</span>}
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  if (!mergePreview) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {duplicate.record_type === 'company' ? (
              <Building2 className="h-5 w-5 text-blue-600" />
            ) : (
              <User className="h-5 w-5 text-green-600" />
            )}
            Resolve Duplicate {duplicate.record_type === 'company' ? 'Company' : 'Contact'}
          </DialogTitle>
          <DialogDescription>
            Choose how to resolve this duplicate. You can merge the records, keep them separate, or mark as false positive.
          </DialogDescription>
        </DialogHeader>

        {/* Banner */}
        {banner.show && (
          <Alert variant={banner.type === 'error' ? 'destructive' : 'default'} className="mb-4">
            {banner.type === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertTriangle className="h-4 w-4" />
            )}
            <AlertDescription>
              <strong>{banner.title}:</strong> {banner.message}
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="compare">Compare</TabsTrigger>
            <TabsTrigger value="merge">Merge Options</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
          
          <TabsContent value="compare" className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Match Type: <strong>{duplicate.match_type}</strong> | 
                Confidence: <strong>{Math.round(duplicate.confidence_score * 100)}%</strong>
              </AlertDescription>
            </Alert>
            
            <div className="grid grid-cols-1 gap-4">
              {Object.keys(mergePreview.primaryRecord)
                .filter(key => !key.includes('_id') && !key.includes('created_at') && !key.includes('updated_at'))
                .map(field => renderFieldComparison(
                  field,
                  mergePreview.primaryRecord[field],
                  mergePreview.duplicateRecord[field]
                ))}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleMarkFalsePositive} disabled={loading}>
              <XCircle className="h-4 w-4 mr-2" />
              False Positive
            </Button>
            <Button variant="outline" onClick={handleKeepSeparate} disabled={loading}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Keep Separate
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button onClick={handleMerge} disabled={loading} className="bg-blue-600 hover:bg-blue-700">
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Merging...
                </>
              ) : (
                <>
                  <Merge className="h-4 w-4 mr-2" />
                  Merge Records
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default DuplicateResolutionModal
