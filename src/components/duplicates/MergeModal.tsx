'use client'

import React, { useState, useEffect, useMemo, useC<PERSON>back, memo } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { Building2, User, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'

interface FieldComparison {
  field: string
  label: string
  primaryValue: any
  duplicateValue: any
  selectedValue: 'primary' | 'duplicate' | 'custom'
  customValue?: any
  fieldType: 'text' | 'email' | 'phone' | 'url' | 'number' | 'date' | 'textarea' | 'array' | 'boolean'
  category: string
  hasData: boolean
}

interface FieldMapping {
  field: string
  label: string
  type: 'text' | 'email' | 'phone' | 'url' | 'number' | 'date' | 'textarea' | 'array' | 'boolean'
  category: string
}

interface MergeModalProps {
  isOpen: boolean
  onClose: () => void
  onMerge: (mergeData: any, autoMerge?: boolean) => void
  primaryRecord: any
  duplicateRecord: any
  recordType: 'company' | 'contact'
}

// Field mappings for companies - using actual database field names
const COMPANY_FIELDS: FieldMapping[] = [
  { field: 'company_name', label: 'Company Name', type: 'text', category: 'Basic Information' },
  { field: 'company_website', label: 'Website', type: 'url', category: 'Basic Information' },
  { field: 'summary', label: 'Summary', type: 'textarea', category: 'Basic Information' },
  { field: 'industry', label: 'Industry', type: 'text', category: 'Basic Information' },
  { field: 'founded_year', label: 'Founded Year', type: 'number', category: 'Basic Information' },
  { field: 'number_of_employees', label: 'Employee Count', type: 'number', category: 'Basic Information' },
  { field: 'annual_revenue', label: 'Annual Revenue', type: 'number', category: 'Financial' },
  { field: 'fund_size', label: 'Fund Size', type: 'number', category: 'Financial' },
  { field: 'aum', label: 'AUM', type: 'number', category: 'Financial' },
  { field: 'company_address', label: 'Address', type: 'textarea', category: 'Location' },
  { field: 'company_city', label: 'City', type: 'text', category: 'Location' },
  { field: 'company_state', label: 'State', type: 'text', category: 'Location' },
  { field: 'company_country', label: 'Country', type: 'text', category: 'Location' },
  { field: 'company_zip', label: 'Postal Code', type: 'text', category: 'Location' },
  { field: 'company_phone', label: 'Phone', type: 'phone', category: 'Contact' },
  { field: 'main_phone', label: 'Main Phone', type: 'phone', category: 'Contact' },
  { field: 'main_email', label: 'Main Email', type: 'email', category: 'Contact' },
  { field: 'secondary_email', label: 'Secondary Email', type: 'email', category: 'Contact' },
  { field: 'company_linkedin', label: 'LinkedIn URL', type: 'url', category: 'Social' },
  { field: 'twitter', label: 'Twitter', type: 'url', category: 'Social' },
  { field: 'facebook', label: 'Facebook', type: 'url', category: 'Social' },
  { field: 'instagram', label: 'Instagram', type: 'url', category: 'Social' },
  { field: 'youtube', label: 'YouTube', type: 'url', category: 'Social' },
  { field: 'business_model', label: 'Business Model', type: 'textarea', category: 'Business' },
  { field: 'investment_focus', label: 'Investment Focus', type: 'array', category: 'Investment' },
  { field: 'investment_strategy_mission', label: 'Investment Strategy Mission', type: 'textarea', category: 'Investment' },
  { field: 'investment_strategy_approach', label: 'Investment Strategy Approach', type: 'textarea', category: 'Investment' },
  { field: 'fund_size_active_fund', label: 'Active Fund Size', type: 'number', category: 'Investment' },
  { field: 'active_fund_name_series', label: 'Active Fund Name', type: 'text', category: 'Investment' },
  { field: 'fundraising_status', label: 'Fundraising Status', type: 'text', category: 'Investment' },
  { field: 'dry_powder', label: 'Dry Powder', type: 'number', category: 'Investment' },
  { field: 'annual_deployment_target', label: 'Annual Deployment Target', type: 'number', category: 'Investment' },
  { field: 'transactions_completed_last_12m', label: 'Transactions Last 12M', type: 'number', category: 'Investment' },
  { field: 'total_transaction_volume_ytd', label: 'Transaction Volume YTD', type: 'number', category: 'Investment' },
  { field: 'deal_count_ytd', label: 'Deal Count YTD', type: 'number', category: 'Investment' },
  { field: 'average_deal_size', label: 'Average Deal Size', type: 'number', category: 'Investment' },
  { field: 'portfolio_size_sqft', label: 'Portfolio Size (sqft)', type: 'number', category: 'Portfolio' },
  { field: 'portfolio_asset_count', label: 'Portfolio Asset Count', type: 'number', category: 'Portfolio' },
  { field: 'number_of_properties', label: 'Number of Properties', type: 'number', category: 'Portfolio' },
  { field: 'number_of_offices', label: 'Number of Offices', type: 'number', category: 'Portfolio' },
  { field: 'office_locations', label: 'Office Locations', type: 'array', category: 'Portfolio' },
  { field: 'partnerships', label: 'Partnerships', type: 'array', category: 'Business' },
  { field: 'key_equity_partners', label: 'Key Equity Partners', type: 'array', category: 'Business' },
  { field: 'key_debt_partners', label: 'Key Debt Partners', type: 'array', category: 'Business' },
  { field: 'major_competitors', label: 'Major Competitors', type: 'array', category: 'Business' },
  { field: 'board_of_directors', label: 'Board of Directors', type: 'array', category: 'Leadership' },
  { field: 'key_executives', label: 'Key Executives', type: 'array', category: 'Leadership' },
  { field: 'founder_background', label: 'Founder Background', type: 'textarea', category: 'Leadership' },
  { field: 'company_history', label: 'Company History', type: 'textarea', category: 'Business' },
  { field: 'products_services_description', label: 'Products/Services', type: 'textarea', category: 'Business' },
  { field: 'target_customer_profile', label: 'Target Customer Profile', type: 'textarea', category: 'Business' },
  { field: 'unique_selling_proposition', label: 'Unique Selling Proposition', type: 'textarea', category: 'Business' },
  { field: 'market_share_percentage', label: 'Market Share %', type: 'number', category: 'Business' },
  { field: 'industry_awards_recognitions', label: 'Awards & Recognitions', type: 'array', category: 'Business' },
  { field: 'corporate_structure', label: 'Corporate Structure', type: 'text', category: 'Business' },
  { field: 'parent_company', label: 'Parent Company', type: 'text', category: 'Business' },
  { field: 'subsidiaries', label: 'Subsidiaries', type: 'array', category: 'Business' },
  { field: 'stock_ticker_symbol', label: 'Stock Ticker', type: 'text', category: 'Financial' },
  { field: 'stock_exchange', label: 'Stock Exchange', type: 'text', category: 'Financial' },
  { field: 'market_capitalization', label: 'Market Cap', type: 'number', category: 'Financial' },
  { field: 'net_income', label: 'Net Income', type: 'number', category: 'Financial' },
  { field: 'ebitda', label: 'EBITDA', type: 'number', category: 'Financial' },
  { field: 'profit_margin', label: 'Profit Margin', type: 'number', category: 'Financial' },
  { field: 'credit_rating', label: 'Credit Rating', type: 'text', category: 'Financial' },
  { field: 'quarterly_earnings_link', label: 'Quarterly Earnings Link', type: 'url', category: 'Financial' },
  { field: 'headquarters_address', label: 'HQ Address', type: 'textarea', category: 'Location' },
  { field: 'headquarters_city', label: 'HQ City', type: 'text', category: 'Location' },
  { field: 'headquarters_state', label: 'HQ State', type: 'text', category: 'Location' },
  { field: 'headquarters_zipcode', label: 'HQ Zipcode', type: 'text', category: 'Location' },
  { field: 'headquarters_country', label: 'HQ Country', type: 'text', category: 'Location' },
  { field: 'additional_address', label: 'Additional Address', type: 'textarea', category: 'Location' },
  { field: 'additional_city', label: 'Additional City', type: 'text', category: 'Location' },
  { field: 'additional_state', label: 'Additional State', type: 'text', category: 'Location' },
  { field: 'additional_zipcode', label: 'Additional Zipcode', type: 'text', category: 'Location' },
  { field: 'additional_country', label: 'Additional Country', type: 'text', category: 'Location' },
  { field: 'sustainability_esg_focus', label: 'ESG Focus', type: 'boolean', category: 'Business' },
  { field: 'technology_proptech_adoption', label: 'Proptech Adoption', type: 'boolean', category: 'Business' },
  { field: 'adaptive_reuse_experience', label: 'Adaptive Reuse Experience', type: 'boolean', category: 'Business' },
  { field: 'regulatory_zoning_expertise', label: 'Regulatory Expertise', type: 'boolean', category: 'Business' },
  { field: 'market_cycle_positioning', label: 'Market Cycle Positioning', type: 'text', category: 'Business' },
  { field: 'urban_vs_suburban_preference', label: 'Urban vs Suburban Preference', type: 'text', category: 'Business' },
  { field: 'lender_type', label: 'Lender Type', type: 'text', category: 'Business' },
  { field: 'annual_loan_volume', label: 'Annual Loan Volume', type: 'number', category: 'Business' },
  { field: 'lending_origin', label: 'Lending Origin', type: 'text', category: 'Business' },
  { field: 'portfolio_health', label: 'Portfolio Health', type: 'textarea', category: 'Portfolio' },
  { field: 'recent_news_sentiment', label: 'Recent News Sentiment', type: 'text', category: 'Business' },
  { field: 'data_source', label: 'Data Source', type: 'text', category: 'Metadata' },
  { field: 'internal_relationship_manager', label: 'Relationship Manager', type: 'text', category: 'Metadata' },
  { field: 'last_contact_date', label: 'Last Contact Date', type: 'date', category: 'Metadata' },
  { field: 'pipeline_status', label: 'Pipeline Status', type: 'text', category: 'Metadata' },
  { field: 'role_in_previous_deal', label: 'Role in Previous Deal', type: 'text', category: 'Metadata' },
]

// Field mappings for contacts - using actual database field names
const CONTACT_FIELDS: FieldMapping[] = [
  { field: 'first_name', label: 'First Name', type: 'text', category: 'Basic Information' },
  { field: 'last_name', label: 'Last Name', type: 'text', category: 'Basic Information' },
  { field: 'full_name', label: 'Full Name', type: 'text', category: 'Basic Information' },
  { field: 'email', label: 'Email', type: 'email', category: 'Contact' },
  { field: 'phone', label: 'Phone', type: 'phone', category: 'Contact' },
  { field: 'job_title', label: 'Job Title', type: 'text', category: 'Professional' },
  { field: 'department', label: 'Department', type: 'text', category: 'Professional' },
  { field: 'linkedin_url', label: 'LinkedIn URL', type: 'url', category: 'Social' },
  { field: 'website', label: 'Website', type: 'url', category: 'Social' },
  { field: 'address', label: 'Address', type: 'textarea', category: 'Location' },
  { field: 'city', label: 'City', type: 'text', category: 'Location' },
  { field: 'state', label: 'State', type: 'text', category: 'Location' },
  { field: 'country', label: 'Country', type: 'text', category: 'Location' },
  { field: 'zip_code', label: 'Postal Code', type: 'text', category: 'Location' },
  { field: 'timezone', label: 'Timezone', type: 'text', category: 'Location' },
  { field: 'language_preference', label: 'Language Preference', type: 'text', category: 'Personal' },
  { field: 'communication_preference', label: 'Communication Preference', type: 'text', category: 'Personal' },
  { field: 'industry', label: 'Industry', type: 'text', category: 'Professional' },
  { field: 'company_size', label: 'Company Size', type: 'text', category: 'Professional' },
  { field: 'annual_revenue', label: 'Annual Revenue', type: 'text', category: 'Professional' },
  { field: 'funding_stage', label: 'Funding Stage', type: 'text', category: 'Professional' },
  { field: 'technologies_used', label: 'Technologies Used', type: 'array', category: 'Professional' },
  { field: 'pain_points', label: 'Pain Points', type: 'textarea', category: 'Professional' },
  { field: 'decision_making_role', label: 'Decision Making Role', type: 'text', category: 'Professional' },
  { field: 'budget_authority', label: 'Budget Authority', type: 'text', category: 'Professional' },
  { field: 'purchase_timeline', label: 'Purchase Timeline', type: 'text', category: 'Professional' },
  { field: 'previous_interactions', label: 'Previous Interactions', type: 'textarea', category: 'Professional' },
  { field: 'referral_source', label: 'Referral Source', type: 'text', category: 'Professional' },
  { field: 'campaign_id', label: 'Campaign ID', type: 'text', category: 'Professional' },
  { field: 'lead_owner', label: 'Lead Owner', type: 'text', category: 'Professional' },
  { field: 'lead_source', label: 'Lead Source', type: 'text', category: 'Professional' },
  { field: 'lead_type', label: 'Lead Type', type: 'text', category: 'Professional' },
  { field: 'conversion_date', label: 'Conversion Date', type: 'date', category: 'Professional' },
  { field: 'lost_reason', label: 'Lost Reason', type: 'text', category: 'Professional' },
  { field: 'win_probability', label: 'Win Probability', type: 'number', category: 'Professional' },
  { field: 'expected_value', label: 'Expected Value', type: 'number', category: 'Professional' },
  { field: 'actual_value', label: 'Actual Value', type: 'number', category: 'Professional' },
  { field: 'sales_cycle_length', label: 'Sales Cycle Length', type: 'number', category: 'Professional' },
  { field: 'touchpoint_count', label: 'Touchpoint Count', type: 'number', category: 'Professional' },
  { field: 'last_activity_date', label: 'Last Activity Date', type: 'date', category: 'Professional' },
  { field: 'next_activity_date', label: 'Next Activity Date', type: 'date', category: 'Professional' },
  { field: 'activity_history', label: 'Activity History', type: 'textarea', category: 'Professional' },
  { field: 'communication_history', label: 'Communication History', type: 'textarea', category: 'Professional' },
  { field: 'preferences', label: 'Preferences', type: 'textarea', category: 'Personal' },
  { field: 'restrictions', label: 'Restrictions', type: 'textarea', category: 'Personal' },
  { field: 'opt_out_status', label: 'Opt Out Status', type: 'text', category: 'Personal' },
  { field: 'gdpr_consent', label: 'GDPR Consent', type: 'text', category: 'Personal' },
  { field: 'data_quality_score', label: 'Data Quality Score', type: 'number', category: 'Metadata' },
  { field: 'enrichment_status', label: 'Enrichment Status', type: 'text', category: 'Metadata' },
  { field: 'enrichment_date', label: 'Enrichment Date', type: 'date', category: 'Metadata' },
  { field: 'enrichment_source', label: 'Enrichment Source', type: 'text', category: 'Metadata' },
  { field: 'verification_status', label: 'Verification Status', type: 'text', category: 'Metadata' },
  { field: 'verification_date', label: 'Verification Date', type: 'date', category: 'Metadata' },
  { field: 'verification_method', label: 'Verification Method', type: 'text', category: 'Metadata' },
  { field: 'status', label: 'Status', type: 'text', category: 'Metadata' },
  { field: 'lead_status', label: 'Lead Status', type: 'text', category: 'Metadata' },
  { field: 'lead_score', label: 'Lead Score', type: 'number', category: 'Metadata' },
  { field: 'last_contact_date', label: 'Last Contact Date', type: 'date', category: 'Metadata' },
  { field: 'next_follow_up_date', label: 'Next Follow Up Date', type: 'date', category: 'Metadata' },
  { field: 'assigned_to', label: 'Assigned To', type: 'text', category: 'Metadata' },
  { field: 'tags', label: 'Tags', type: 'array', category: 'Metadata' },
  { field: 'custom_fields', label: 'Custom Fields', type: 'textarea', category: 'Metadata' },
  { field: 'social_media_links', label: 'Social Media Links', type: 'textarea', category: 'Social' },
  { field: 'notes', label: 'Notes', type: 'textarea', category: 'Metadata' },
  { field: 'source', label: 'Source', type: 'text', category: 'Metadata' },
]

// Memoized field value renderer component
const FieldValueRenderer = memo(({ value, fieldType }: { value: any; fieldType: string }) => {
  if (value === null || value === undefined || value === '') {
    return <span className="text-muted-foreground">No value</span>
  }

  if (fieldType === 'boolean') {
    return (
      <div className="flex items-center gap-2">
        {value ? (
          <CheckCircle className="h-4 w-4 text-green-600" />
        ) : (
          <XCircle className="h-4 w-4 text-red-600" />
        )}
        <span className="text-sm">{value ? 'Yes' : 'No'}</span>
      </div>
    )
  }

  if (fieldType === 'array' && Array.isArray(value)) {
    return (
      <div className="flex flex-wrap gap-1">
        {value.map((item: any, index: number) => (
          <Badge key={index} variant="secondary" className="text-xs">
            {item}
          </Badge>
        ))}
      </div>
    )
  }

  if (fieldType === 'textarea') {
    return (
      <div className="text-sm max-h-20 overflow-y-auto">
        {value || <span className="text-muted-foreground">No value</span>}
      </div>
    )
  }

  return (
    <span className="text-sm">
      {value || <span className="text-muted-foreground">No value</span>}
    </span>
  )
})

FieldValueRenderer.displayName = 'FieldValueRenderer'

function MergeModal({
  isOpen,
  onClose,
  onMerge,
  primaryRecord,
  duplicateRecord,
  recordType
}: MergeModalProps) {
  const [fieldComparisons, setFieldComparisons] = useState<FieldComparison[]>([])
  const [loading, setLoading] = useState(false)
  const [actualPrimaryRecord, setActualPrimaryRecord] = useState<any>({})
  const [actualDuplicateRecord, setActualDuplicateRecord] = useState<any>({})
  const [autoMerge, setAutoMerge] = useState(false)

  // Memoized field comparisons - only process fields that have data
  const optimizedFieldComparisons = useMemo(() => {
    return fieldComparisons.filter(field => field.hasData)
  }, [fieldComparisons])

  // Memoized categories - only include categories that have fields with data
  const categories = useMemo(() => {
    return [...new Set(optimizedFieldComparisons.map(field => field.category))]
  }, [optimizedFieldComparisons])

  // Fetch actual record data when modal opens
  useEffect(() => {
    if (isOpen && primaryRecord && duplicateRecord) {
      const primaryId = primaryRecord.company_id || primaryRecord.contact_id
      const duplicateId = duplicateRecord.company_id || duplicateRecord.contact_id
      if (primaryId && duplicateId) {
        fetchMergePreview()
      }
    }
  }, [isOpen, primaryRecord, duplicateRecord])

  const fetchMergePreview = useCallback(async () => {
    setLoading(true)
    try {
      const requestBody = {
        primaryId: primaryRecord.company_id || primaryRecord.contact_id,
        duplicateId: duplicateRecord.company_id || duplicateRecord.contact_id,
        recordType
      }
      
      const response = await fetch('/api/duplicates/merge-preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })

      const data = await response.json()
      
      if (data.success && data.preview) {
        setActualPrimaryRecord(data.preview.primaryRecord || {})
        setActualDuplicateRecord(data.preview.duplicateRecord || {})
        
        // Create field comparisons - only include fields that have data
        const fields = recordType === 'company' ? COMPANY_FIELDS : CONTACT_FIELDS
        const comparisons: FieldComparison[] = fields.map(field => {
          const primaryValue = data.preview.primaryRecord?.[field.field] || null
          const duplicateValue = data.preview.duplicateRecord?.[field.field] || null
          const hasData = (primaryValue !== null && primaryValue !== undefined && primaryValue !== '') ||
                         (duplicateValue !== null && duplicateValue !== undefined && duplicateValue !== '')
          
          return {
            field: field.field,
            label: field.label,
            primaryValue,
            duplicateValue,
                         selectedValue: 'primary' as const,
            fieldType: field.type,
            category: field.category,
            hasData
          }
        }).filter(field => field.hasData) // Only include fields with actual data
        
        setFieldComparisons(comparisons)
      }
    } catch (error) {
      console.error('Error fetching merge preview:', error)
    } finally {
      setLoading(false)
    }
  }, [primaryRecord, duplicateRecord, recordType])

  const handleFieldSelectionChange = useCallback((fieldName: string, value: 'primary' | 'duplicate' | 'custom') => {
    setFieldComparisons(prev => 
      prev.map(field => 
        field.field === fieldName 
          ? { ...field, selectedValue: value }
          : field
      )
    )
  }, [])

  const handleCustomValueChange = useCallback((fieldName: string, value: any) => {
    setFieldComparisons(prev => 
      prev.map(field => 
        field.field === fieldName 
          ? { ...field, customValue: value }
          : field
      )
    )
  }, [])

  const handleMerge = useCallback(() => {
    const fieldsToMerge: Record<string, any> = {}
    const customValues: Record<string, any> = {}

    fieldComparisons.forEach(field => {
      if (field.selectedValue === 'primary') {
        fieldsToMerge[field.field] = field.primaryValue
      } else if (field.selectedValue === 'duplicate') {
        fieldsToMerge[field.field] = field.duplicateValue
      } else if (field.selectedValue === 'custom') {
        fieldsToMerge[field.field] = field.customValue
        customValues[field.field] = field.customValue
      }
    })

    onMerge(fieldsToMerge, autoMerge)
  }, [fieldComparisons, onMerge, autoMerge])



  const renderCustomInput = (field: FieldComparison) => {
    const value = field.customValue || ''

    if (field.fieldType === 'textarea') {
      return (
        <Textarea
          value={value}
          onChange={(e) => handleCustomValueChange(field.field, e.target.value)}
          placeholder={`Enter custom ${field.label.toLowerCase()}`}
          className="min-h-[80px]"
        />
      )
    }

    if (field.fieldType === 'boolean') {
      return (
        <div className="flex items-center space-x-2">
          <Checkbox
            id={`custom-${field.field}`}
            checked={value}
            onCheckedChange={(checked) => handleCustomValueChange(field.field, checked)}
          />
          <Label htmlFor={`custom-${field.field}`}>Yes</Label>
        </div>
      )
    }

    if (field.fieldType === 'number') {
      return (
        <Input
          type="number"
          value={value}
          onChange={(e) => handleCustomValueChange(field.field, e.target.value)}
          placeholder={`Enter custom ${field.label.toLowerCase()}`}
        />
      )
    }

    if (field.fieldType === 'email') {
      return (
        <Input
          type="email"
          value={value}
          onChange={(e) => handleCustomValueChange(field.field, e.target.value)}
          placeholder={`Enter custom ${field.label.toLowerCase()}`}
        />
      )
    }

    if (field.fieldType === 'url') {
      return (
        <Input
          type="url"
          value={value}
          onChange={(e) => handleCustomValueChange(field.field, e.target.value)}
          placeholder={`Enter custom ${field.label.toLowerCase()}`}
        />
      )
    }

    return (
      <Input
        value={value}
        onChange={(e) => handleCustomValueChange(field.field, e.target.value)}
        placeholder={`Enter custom ${field.label.toLowerCase()}`}
      />
    )
  }



  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading merge preview...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {recordType === 'company' ? <Building2 className="h-5 w-5" /> : <User className="h-5 w-5" />}
            Merge {recordType === 'company' ? 'Companies' : 'Contacts'}
          </DialogTitle>
        </DialogHeader>

        {/* Auto Merge Option */}
        <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="auto-merge" 
              checked={autoMerge}
              onCheckedChange={(checked) => setAutoMerge(checked === true)}
            />
            <Label htmlFor="auto-merge" className="text-sm font-medium">
              🤖 Smart Auto-Merge
            </Label>
          </div>
          <p className="text-xs text-blue-600 mt-1 ml-6">
            Automatically combine data from both records by keeping the most complete information. 
            This will fill empty fields, prefer longer descriptions, and merge arrays intelligently.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Primary Record */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Primary Record
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm font-medium">
                {actualPrimaryRecord.company_name || actualPrimaryRecord.full_name || 'Unnamed Record'}
              </div>
              {actualPrimaryRecord.company_website && (
                <div className="text-sm text-muted-foreground">{actualPrimaryRecord.company_website}</div>
              )}
              {actualPrimaryRecord.email && (
                <div className="text-sm text-muted-foreground">{actualPrimaryRecord.email}</div>
              )}
            </CardContent>
          </Card>

          {/* Duplicate Record */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                Duplicate Record
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm font-medium">
                {actualDuplicateRecord.company_name || actualDuplicateRecord.full_name || 'Unnamed Record'}
              </div>
              {actualDuplicateRecord.company_website && (
                <div className="text-sm text-muted-foreground">{actualDuplicateRecord.company_website}</div>
              )}
              {actualDuplicateRecord.email && (
                <div className="text-sm text-muted-foreground">{actualDuplicateRecord.email}</div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Manual Field Selection - Hidden when auto merge is enabled */}
        {!autoMerge && (
          <Tabs defaultValue={categories[0]} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            {categories.map(category => (
              <TabsTrigger key={category} value={category}>
                {category}
              </TabsTrigger>
            ))}
          </TabsList>

          {categories.map(category => (
            <TabsContent key={category} value={category} className="space-y-4">
                             {optimizedFieldComparisons
                 .filter(field => field.category === category)
                 .map(field => (
                  <Card key={field.field}>
                    <CardContent className="pt-6">
                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        {/* Field Label */}
                        <div className="lg:col-span-1">
                          <Label className="text-sm font-medium">{field.label}</Label>
                        </div>

                        {/* Primary Record Value */}
                        <div className="lg:col-span-1">
                          <div className="space-y-2">
                            <div className="text-xs text-muted-foreground">Primary Record</div>
                                                         <div className="p-2 bg-green-50 border border-green-200 rounded">
                               <FieldValueRenderer value={field.primaryValue} fieldType={field.fieldType} />
                             </div>
                          </div>
                        </div>

                        {/* Duplicate Record Value */}
                        <div className="lg:col-span-1">
                          <div className="space-y-2">
                            <div className="text-xs text-muted-foreground">Duplicate Record</div>
                                                         <div className="p-2 bg-orange-50 border border-orange-200 rounded">
                               <FieldValueRenderer value={field.duplicateValue} fieldType={field.fieldType} />
                             </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 space-y-3">
                        <RadioGroup
                          value={field.selectedValue}
                          onValueChange={(value: 'primary' | 'duplicate' | 'custom') => 
                            handleFieldSelectionChange(field.field, value)
                          }
                          className="flex flex-col space-y-2"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="primary" id={`${field.field}-primary`} />
                            <Label htmlFor={`${field.field}-primary`} className="text-sm">
                              Keep primary record value
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="duplicate" id={`${field.field}-duplicate`} />
                            <Label htmlFor={`${field.field}-duplicate`} className="text-sm">
                              Use duplicate record value
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="custom" id={`${field.field}-custom`} />
                            <Label htmlFor={`${field.field}-custom`} className="text-sm">
                              Enter custom value
                            </Label>
                          </div>
                        </RadioGroup>

                        {field.selectedValue === 'custom' && (
                          <div className="mt-3 p-3 bg-gray-50 border rounded">
                            <Label className="text-sm font-medium mb-2 block">
                              Custom {field.label}
                            </Label>
                            {renderCustomInput(field)}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </TabsContent>
          ))}
          </Tabs>
        )}

        {/* Auto Merge Preview */}
        {autoMerge && (
          <div className="p-6 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-4">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <h3 className="font-medium text-green-800">Smart Auto-Merge Enabled</h3>
            </div>
            <p className="text-sm text-green-700 mb-4">
              The system will automatically combine data from both records using intelligent merging rules:
            </p>
            <ul className="text-xs text-green-600 space-y-1 ml-4">
              <li>• Fill empty fields from the other record</li>
              <li>• Prefer longer, more descriptive text values</li>
              <li>• Choose higher numeric values (e.g., employee count, revenue)</li>
              <li>• Merge arrays and lists without duplicates</li>
              <li>• Prefer formatted contact information (phone numbers, emails)</li>
              <li>• Preserve all unique data while avoiding conflicts</li>
            </ul>
          </div>
        )}

        <Separator className="my-6" />

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleMerge}>
            Merge Records
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default memo(MergeModal)