'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  AlertTriangle,
  Building2,
  User,
  Filter,
  CheckCircle,
  XCircle,
  RefreshCw,
  Search,
  Database,
  Settings,
  Merge,
  ExternalLink,
  X
} from 'lucide-react'
import { DuplicateRecord, DuplicateStatus, DuplicateRecordType } from '@/types/duplicate'
import OptimizedMergeModal from './OptimizedMergeModal'
import { batchMergeWithBottleneck, throttledMerge } from '@/lib/duplicate-merge-utils'

interface DuplicateDashboardProps {
  initialType?: 'company' | 'contact'
}

interface DuplicateWithDetails extends DuplicateRecord {
  primary_name: string
  duplicate_name: string
  primary_data?: any
  duplicate_data?: any
}

interface PaginationInfo {
  page: number
  pageSize: number
  total: number
  totalPages: number
}

export default function DuplicateDashboard({ initialType }: DuplicateDashboardProps) {
  const [duplicates, setDuplicates] = useState<DuplicateWithDetails[]>([])
  const [loading, setLoading] = useState(false)
  const [scanning, setScanning] = useState(false)
  const [normalizing, setNormalizing] = useState(false)
  const [selectedDuplicates, setSelectedDuplicates] = useState<Set<number>>(new Set())
  const [selectedDuplicate, setSelectedDuplicate] = useState<DuplicateWithDetails | null>(null)
  const [showMergeModal, setShowMergeModal] = useState(false)
  const [filter, setFilter] = useState<DuplicateRecordType | 'all'>(initialType || 'all')
  const [statusFilter, setStatusFilter] = useState<DuplicateStatus>('pending')
  const [confidenceFilter, setConfidenceFilter] = useState({ min: 0, max: 1 })
  const [matchTypeFilter, setMatchTypeFilter] = useState<string>('all')
  const [scanOnly, setScanOnly] = useState(false)
  const [normalizeFirst, setNormalizeFirst] = useState(true)
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 100,
    total: 0,
    totalPages: 0
  })

  // Add page size options
  const pageSizeOptions = [50, 100, 250, 500, 1000]

  // Add state for processing large batches
  const [processingLargeBatch, setProcessingLargeBatch] = useState(false)
  const [batchProgress, setBatchProgress] = useState<{
    current: number
    total: number
    status: string
  } | null>(null)

  // Add state for match type auto-merge
  const [processingMatchTypeMerge, setProcessingMatchTypeMerge] = useState(false)

  const [stats, setStats] = useState<any[]>([])
  const [banner, setBanner] = useState<{
    show: boolean
    type: 'success' | 'error' | 'info'
    title: string
    message: string
  }>({ show: false, type: 'success', title: '', message: '' })

  useEffect(() => {
    fetchDuplicates()
  }, [pagination.page, pagination.pageSize, filter, statusFilter, confidenceFilter, matchTypeFilter])

  const showBanner = (type: 'success' | 'error' | 'info', title: string, message: string) => {
    setBanner({ show: true, type, title, message })
    // Auto-hide banner after 5 seconds
    setTimeout(() => {
      setBanner(prev => ({ ...prev, show: false }))
    }, 5000)
  }

  const fetchDuplicates = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
        status: statusFilter
      })
      
      if (filter !== 'all') {
        params.append('type', filter)
      }
      
      if (confidenceFilter.min > 0) {
        params.append('minConfidence', confidenceFilter.min.toString())
      }
      
      if (confidenceFilter.max < 1) {
        params.append('maxConfidence', confidenceFilter.max.toString())
      }
      
      if (matchTypeFilter && matchTypeFilter !== 'all') {
        params.append('matchType', matchTypeFilter)
      }

      console.log('Fetching duplicates with params:', params.toString())
      const response = await fetch(`/api/duplicates?${params}`)
      const data = await response.json()

      if (data.success) {
        console.log('Fetched duplicates:', data.data?.length || 0, 'with filters:', {
          type: filter,
          status: statusFilter,
          confidence: confidenceFilter,
          matchType: matchTypeFilter
        })
        setDuplicates(data.data || [])
        setPagination(data.pagination || {
          page: 1,
          pageSize: pagination.pageSize,
          total: 0,
          totalPages: 0
        })
        setStats(data.stats || [])
      } else {
        console.error('Failed to fetch duplicates:', data.error)
        setDuplicates([])
        setPagination(prev => ({ ...prev, total: 0, totalPages: 0 }))
        setStats([])
      }
    } catch (error) {
      console.error('Error fetching duplicates:', error)
      setDuplicates([])
      setPagination(prev => ({ ...prev, total: 0, totalPages: 0 }))
      setStats([])
    } finally {
      setLoading(false)
    }
  }

  // Reset pagination when filters change
  useEffect(() => {
    setPagination(prev => ({ ...prev, page: 1 }))
  }, [filter, statusFilter, confidenceFilter, matchTypeFilter])

  // Enhanced filter change handlers
  const handleFilterChange = (newFilter: DuplicateRecordType | 'all') => {
    setFilter(newFilter)
    console.log('Filter changed to:', newFilter)
  }

  const handleStatusFilterChange = (newStatus: DuplicateStatus) => {
    setStatusFilter(newStatus)
    console.log('Status filter changed to:', newStatus)
  }

  const handleConfidenceFilterChange = (field: 'min' | 'max', value: number) => {
    setConfidenceFilter(prev => ({ ...prev, [field]: value }))
    console.log('Confidence filter changed:', { ...confidenceFilter, [field]: value })
  }

  const handleMatchTypeFilterChange = (newMatchType: string) => {
    setMatchTypeFilter(newMatchType)
    console.log('Match type filter changed to:', newMatchType)
  }

  // Clear all filters
  const clearAllFilters = () => {
    setFilter('all')
    setStatusFilter('pending')
    setConfidenceFilter({ min: 0, max: 1 })
    setMatchTypeFilter('all')
    console.log('All filters cleared')
  }

  // Check if any filters are active
  const hasActiveFilters = () => {
    return filter !== 'all' || 
           statusFilter !== 'pending' || 
           confidenceFilter.min > 0 || 
           confidenceFilter.max < 1 || 
           matchTypeFilter !== 'all'
  }

  const startScan = async (type: 'company' | 'contact' | 'both' = 'both') => {
    setScanning(true)
    try {
      const response = await fetch('/api/duplicates/scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          type, 
          normalizeFirst: normalizeFirst && !scanOnly,
          scanOnly 
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        await fetchDuplicates() // Refresh the list
        const message = `Scan completed! Found ${data.stats.duplicatesFound} duplicates in ${data.stats.processingTime}ms`
        const normMessage = data.stats.normalizationTime ? ` (Normalization: ${data.stats.normalizationTime}ms)` : ''
        showBanner('success', 'Scan Completed', message + normMessage)
      } else {
        showBanner('error', 'Scan Failed', data.error)
      }
    } catch (error) {
      console.error('Error starting scan:', error)
      showBanner('error', 'Error', 'Error starting scan')
    } finally {
      setScanning(false)
    }
  }

  const startNormalization = async (type: 'company' | 'contact' | 'both' = 'both') => {
    setNormalizing(true)
    try {
      const response = await fetch('/api/duplicates/normalize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type })
      })
      
      const data = await response.json()
      
      if (data.success) {
        const companyMsg = data.companies ? `Companies: ${data.companies.processed} processed, ${data.companies.errors} errors` : ''
        const contactMsg = data.contacts ? `Contacts: ${data.contacts.processed} processed, ${data.contacts.errors} errors` : ''
        const message = `Normalization completed in ${data.processingTime}ms\n${companyMsg}\n${contactMsg}`
        showBanner('success', 'Normalization Completed', message)
      } else {
        showBanner('error', 'Normalization Failed', data.error)
      }
    } catch (error) {
      console.error('Error starting normalization:', error)
      showBanner('error', 'Error', 'Error starting normalization')
    } finally {
      setNormalizing(false)
    }
  }

  const handleDuplicateSelect = (duplicateId: number, checked: boolean) => {
    const newSelected = new Set(selectedDuplicates)
    if (checked) {
      newSelected.add(duplicateId)
    } else {
      newSelected.delete(duplicateId)
    }
    setSelectedDuplicates(newSelected)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDuplicates(new Set(duplicates.map(d => d.id)))
    } else {
      setSelectedDuplicates(new Set())
    }
  }

  const handleMerge = async (duplicate: DuplicateWithDetails) => {
    console.log('🔍 handleMerge called with duplicate:', duplicate)
    console.log('🔍 primary_data:', duplicate.primary_data)
    console.log('🔍 duplicate_data:', duplicate.duplicate_data)
    
    setSelectedDuplicate(duplicate)
    setShowMergeModal(true)
  }

  const handleAutoMerge = async (duplicate: DuplicateWithDetails) => {
    try {
      setLoading(true)
      
      // Use throttled merge for individual operations
      const result = await throttledMerge({
        duplicateId: duplicate.id,
        primaryRecordId: duplicate.primary_record_id,
        autoMerge: true,
        resolvedBy: 'automated_system'
      })
      
      if (result.success) {
        showBanner('success', 'Auto Merge Successful', `Successfully merged records using smart auto-merge!`)
        await fetchDuplicates() // Refresh the list
      } else {
        showBanner('error', 'Auto Merge Failed', result.error || 'Failed to perform auto merge')
      }
    } catch (error) {
      console.error('Auto merge error:', error)
      showBanner('error', 'Auto Merge Error', 'An error occurred during auto merge')
    } finally {
      setLoading(false)
    }
  }

  const handleMergeSubmit = async (mergeData: any, autoMerge?: boolean) => {
    try {
      // Extract the primary record ID from the selected duplicate
      const primaryRecordId = selectedDuplicate?.primary_record_id
      
      // Convert the mergeData to the expected format
      const fieldsToMerge = autoMerge ? [] : Object.keys(mergeData) // Empty array for auto merge
      const customValues = autoMerge ? {} : mergeData // Empty object for auto merge
      
      const response = await fetch('/api/duplicates/resolve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duplicateId: selectedDuplicate?.id,
          action: 'merge',
          mergeStrategy: {
            primaryRecordId: primaryRecordId,
            fieldsToMerge: fieldsToMerge,
            customValues: customValues,
            autoMerge: autoMerge || false
          },
          resolvedBy: autoMerge ? 'automated_system' : 'user'
        })
      })

      const data = await response.json()
      
      if (data.success) {
        await fetchDuplicates() // Refresh the list
        setShowMergeModal(false)
        setSelectedDuplicate(null)
        showBanner('success', 'Merge Successful', 'Records merged successfully!')
      } else {
        showBanner('error', 'Merge Failed', data.error || 'Unknown error occurred during merge')
      }
    } catch (error) {
      console.error('Error merging records:', error)
      showBanner('error', 'Merge Error', 'An error occurred while merging records')
    }
  }

  const handleAutoMergeAll = async () => {
    try {
      setLoading(true)
      const selectedIds = Array.from(selectedDuplicates)
      
      showBanner('success', 'Smart Auto-Merge All Started', `Processing ${selectedIds.length} duplicate records with decision tree validation...`)
      
      // Prepare duplicates data for batch processing
      const duplicatesToProcess = selectedIds.map(id => {
        const duplicate = duplicates.find(d => d.id === id)
        return duplicate ? {
          id: duplicate.id,
          primary_record_id: duplicate.primary_record_id,
          record_type: duplicate.record_type,
          match_type: duplicate.match_type
        } : null
      }).filter(Boolean) as any[]
      
      // Use bottlenecked batch merge with smart filtering and blocking
      const result = await batchMergeWithBottleneck({
        duplicates: duplicatesToProcess,
        resolvedBy: 'smart_auto_merge_all',
        smartFilter: true,
        markBlocked: true
      })
      
      // Show final results
      const blockedCount = result.blockedCount || 0
      if (result.successCount > 0 && result.errorCount === 0 && result.skippedCount === 0) {
        const message = blockedCount > 0 
          ? `Successfully auto-merged ${result.successCount} duplicate records! ${blockedCount} records marked as blocked for manual review.`
          : `Successfully auto-merged ${result.successCount} duplicate records!`
        showBanner('success', 'Smart Auto-Merge All Complete', message)
      } else if (result.successCount > 0 && (result.errorCount > 0 || result.skippedCount > 0)) {
        const blockedMsg = blockedCount > 0 ? `, ${blockedCount} blocked for manual review` : ''
        showBanner('success', 'Smart Auto-Merge All Partial', `Auto-merged ${result.successCount} records, ${result.errorCount} failed, ${result.skippedCount} skipped${blockedMsg}`)
      } else if (result.successCount === 0 && (result.skippedCount > 0 || blockedCount > 0)) {
        const message = blockedCount > 0 
          ? `No records were auto-merged. ${result.skippedCount} records skipped, ${blockedCount} marked as blocked for manual review.`
          : `No records were auto-merged. ${result.skippedCount} records didn't meet the strict auto-merge criteria.`
        showBanner('info', 'Smart Auto-Merge All Results', message)
      } else {
        showBanner('error', 'Smart Auto-Merge All Failed', `Failed to auto-merge any records (${result.errorCount} errors)`)
      }
      
      // Clear selection and refresh
      setSelectedDuplicates(new Set())
      await fetchDuplicates()
      
    } catch (error) {
      console.error('Smart auto merge all error:', error)
      showBanner('error', 'Smart Auto-Merge All Error', 'An error occurred during smart auto merge all')
    } finally {
      setLoading(false)
    }
  }

  const handleBatchAutoMerge = async () => {
    try {
      setLoading(true)
      const selectedIds = Array.from(selectedDuplicates)
      
      showBanner('success', 'Bulk Auto Merge Started', `Processing ${selectedIds.length} duplicate records...`)
      
      // Prepare duplicates data for batch processing
      const duplicatesToProcess = selectedIds.map(id => {
        const duplicate = duplicates.find(d => d.id === id)
        return duplicate ? {
          id: duplicate.id,
          primary_record_id: duplicate.primary_record_id,
          record_type: duplicate.record_type,
          match_type: duplicate.match_type
        } : null
      }).filter(Boolean) as any[]
      
      // Use bottlenecked batch merge without smart filtering
      const result = await batchMergeWithBottleneck({
        duplicates: duplicatesToProcess,
        resolvedBy: 'bulk_automated_system',
        smartFilter: false
      })
      
      // Show final results
      if (result.successCount > 0 && result.errorCount === 0) {
        showBanner('success', 'Bulk Auto Merge Complete', `Successfully auto-merged ${result.successCount} duplicate records!`)
      } else if (result.successCount > 0 && result.errorCount > 0) {
        showBanner('success', 'Bulk Auto Merge Partial', `Auto-merged ${result.successCount} records, ${result.errorCount} failed`)
      } else {
        showBanner('error', 'Bulk Auto Merge Failed', `Failed to auto-merge any records (${result.errorCount} errors)`)
      }
      
      // Clear selection and refresh
      setSelectedDuplicates(new Set())
      await fetchDuplicates()
      
    } catch (error) {
      console.error('Bulk auto merge error:', error)
      showBanner('error', 'Bulk Auto Merge Error', 'An error occurred during bulk auto merge')
    } finally {
      setLoading(false)
    }
  }

  const handleBatchAction = async (action: 'keep_separate') => {
    if (selectedDuplicates.size === 0) return

    try {
      setLoading(true)
      const selectedIds = Array.from(selectedDuplicates)
      
      showBanner('info', 'Processing Batch Action', `Removing ${selectedIds.length} duplicate records from the duplicates table...`)
      
      const promises = selectedIds.map(duplicateId => {
        const requestBody = {
          duplicateId,
          action: action,
          resolvedBy: 'user',
          resolutionNotes: `Batch ${action} action`
        }
        
        console.log('🔍 Sending request body:', requestBody)
        
        return fetch('/api/duplicates/resolve', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        })
      })

      const responses = await Promise.all(promises)
      const results = await Promise.all(responses.map(r => r.json()))
      
      // Count successes and failures
      const successCount = results.filter(r => r.success).length
      const errorCount = results.filter(r => !r.success).length
      
      if (errorCount === 0) {
        showBanner('success', 'Batch Action Complete', 
          `Successfully removed ${successCount} duplicate records from the duplicates table`)
      } else if (successCount > 0) {
        showBanner('success', 'Batch Action Partial', 
          `Removed ${successCount} records successfully, ${errorCount} failed`)
      } else {
        showBanner('error', 'Batch Action Failed', 
          `Failed to remove any records (${errorCount} errors)`)
      }
      
      setSelectedDuplicates(new Set())
      await fetchDuplicates()
    } catch (error) {
      console.error('Error in batch action:', error)
      showBanner('error', 'Batch Action Error', 'An error occurred during batch processing')
    } finally {
      setLoading(false)
    }
  }

  // const openResolutionModal = (duplicate: DuplicateWithDetails) => {
  //   setSelectedDuplicate(duplicate)
  //   setShowResolutionModal(true)
  // }

  // const handleResolutionComplete = async () => {
  //   setShowResolutionModal(false)
  //   setSelectedDuplicate(null)
  //   await fetchDuplicates()
  // }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'bg-red-100 text-red-800'
    if (confidence >= 0.8) return 'bg-orange-100 text-orange-800'
    if (confidence >= 0.7) return 'bg-yellow-100 text-yellow-800'
    return 'bg-gray-100 text-gray-800'
  }

  const getMatchTypeLabel = (matchType: string) => {
    const labels: Record<string, string> = {
      exact_domain: 'Exact Domain',
      exact_name: 'Exact Name',
      exact_email: 'Exact Email',
      linkedin_match: 'LinkedIn',
      phone_match: 'Phone',
      similarity: 'Name Similarity',
      email_domain_name: 'Email Domain + Name',
      name_company: 'Name + Company'
    }
    return labels[matchType] || matchType
  }

  const totalDuplicates = stats.reduce((sum, stat) => sum + parseInt(stat.count), 0)
  const companyDuplicates = stats.filter(s => s.record_type === 'company').reduce((sum, stat) => sum + parseInt(stat.count), 0)
  const contactDuplicates = stats.filter(s => s.record_type === 'contact').reduce((sum, stat) => sum + parseInt(stat.count), 0)

  // Enhanced pagination handlers
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handlePageSizeChange = (newPageSize: number) => {
    setPagination(prev => ({ 
      ...prev, 
      pageSize: newPageSize, 
      page: 1 // Reset to first page when changing page size
    }))
  }

  const goToFirstPage = () => handlePageChange(1)
  const goToLastPage = () => handlePageChange(pagination.totalPages)
  const goToPreviousPage = () => handlePageChange(pagination.page - 1)
  const goToNextPage = () => handlePageChange(pagination.page + 1)

  // New function to auto-merge all entities by match type filter
  const handleAutoMergeAllByMatchType = async () => {
    if (!matchTypeFilter || matchTypeFilter === 'all') {
      showBanner('error', 'Match Type Required', 'Please select a specific match type filter to use this feature')
      return
    }

    try {
      setProcessingMatchTypeMerge(true)
      
      // First, get the total count of duplicates matching this match type
      const countParams = new URLSearchParams({
        status: statusFilter,
        matchType: matchTypeFilter,
        countOnly: 'true'
      })
      
      if (filter !== 'all') {
        countParams.append('type', filter)
      }
      
      if (confidenceFilter.min > 0) {
        countParams.append('minConfidence', confidenceFilter.min.toString())
      }
      
      if (confidenceFilter.max < 1) {
        countParams.append('maxConfidence', confidenceFilter.max.toString())
      }

      const countResponse = await fetch(`/api/duplicates?${countParams}`)
      const countData = await countResponse.json()
      
      if (!countData.success) {
        throw new Error('Failed to get duplicate count')
      }

      const totalMatchingDuplicates = countData.total || 0
      
      if (totalMatchingDuplicates === 0) {
        showBanner('info', 'No Matches', `No duplicates found matching the "${matchTypeFilter}" filter`)
        return
      }

      // Confirm with user
      const confirmed = window.confirm(
        `This will auto-merge ALL ${totalMatchingDuplicates} duplicates matching the "${matchTypeFilter}" filter.\n\n` +
        `This action cannot be undone. Are you sure you want to continue?`
      )
      
      if (!confirmed) return

      showBanner('success', 'Match Type Auto-Merge Started', 
        `Processing ${totalMatchingDuplicates} duplicates matching "${matchTypeFilter}" filter...`)

      // Process in batches to avoid overwhelming the server
      const batchSize = 100
      let processedCount = 0
      let successCount = 0
      let errorCount = 0
      let skippedCount = 0
      
      for (let offset = 0; offset < totalMatchingDuplicates; offset += batchSize) {
        // Get batch of duplicates
        const batchParams = new URLSearchParams({
          page: '1',
          pageSize: batchSize.toString(),
          status: statusFilter,
          matchType: matchTypeFilter,
          offset: offset.toString()
        })
        
        if (filter !== 'all') {
          batchParams.append('type', filter)
        }
        
        if (confidenceFilter.min > 0) {
          batchParams.append('minConfidence', confidenceFilter.min.toString())
        }
        
        if (confidenceFilter.max < 1) {
          batchParams.append('maxConfidence', confidenceFilter.max.toString())
        }

        const batchResponse = await fetch(`/api/duplicates?${batchParams}`)
        const batchData = await batchResponse.json()
        
        if (!batchData.success || !batchData.data) {
          console.error(`Failed to fetch batch at offset ${offset}`)
          continue
        }

        const batchDuplicates = batchData.data
        
        // Process each duplicate in the batch
        for (const duplicate of batchDuplicates) {
          try {
            // Check if this duplicate can be auto-merged based on match type and decision tree
            let canAutoMerge = false
            
            if (duplicate.record_type === 'company') {
              // For companies: ONLY auto-merge if domains match exactly
              // Company names are unreliable (legal names, DBA, abbreviations vary)
              if (duplicate.match_type === 'exact_domain') {
                canAutoMerge = true
              }
            } else if (duplicate.record_type === 'contact') {
              // For contacts: only auto-merge if emails match or LinkedIn matches
              // Domain alone is not sufficient for contact merging
              if (duplicate.match_type === 'exact_email' || duplicate.match_type === 'linkedin_match') {
                canAutoMerge = true
              }
            }
            
            if (!canAutoMerge) {
              skippedCount++
              continue
            }
            
            const response = await fetch('/api/duplicates/resolve', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                duplicateId: duplicate.id,
                action: 'merge',
                mergeStrategy: {
                  primaryRecordId: duplicate.primary_record_id,
                  fieldsToMerge: [],
                  customValues: {},
                  autoMerge: true
                },
                resolvedBy: 'match_type_bulk_auto_merge'
              })
            })

            const data = await response.json()
            
            if (data.success) {
              successCount++
            } else {
              errorCount++
              console.error(`Failed to auto merge duplicate ${duplicate.id}:`, data.error)
            }
            
            processedCount++
            
            // Update progress
            setBatchProgress({
              current: processedCount,
              total: totalMatchingDuplicates,
              status: `Processing batch ${Math.floor(offset / batchSize) + 1}/${Math.ceil(totalMatchingDuplicates / batchSize)}`
            })
            
            // Small delay to prevent overwhelming the server
            await new Promise(resolve => setTimeout(resolve, 50))
            
          } catch (error) {
            errorCount++
            console.error(`Error processing duplicate ${duplicate.id}:`, error)
            processedCount++
          }
        }
        
        // Delay between batches
        if (offset + batchSize < totalMatchingDuplicates) {
          await new Promise(resolve => setTimeout(resolve, 200))
        }
      }
      
      // Show final results
      if (successCount > 0 && errorCount === 0 && skippedCount === 0) {
        showBanner('success', 'Match Type Auto-Merge Complete', 
          `Successfully auto-merged ${successCount} duplicates matching "${matchTypeFilter}" filter!`)
      } else if (successCount > 0 && (errorCount > 0 || skippedCount > 0)) {
        showBanner('success', 'Match Type Auto-Merge Partial', 
          `Auto-merged ${successCount} duplicates, ${errorCount} failed, ${skippedCount} skipped (didn't meet criteria)`)
      } else if (successCount === 0 && skippedCount > 0) {
        showBanner('error', 'Match Type Auto-Merge Skipped', 
          `No duplicates were auto-merged. ${skippedCount} didn't meet the strict auto-merge criteria.`)
      } else {
        showBanner('error', 'Match Type Auto-Merge Failed', 
          `Failed to auto-merge any duplicates (${errorCount} errors)`)
      }
      
      // Refresh the list
      await fetchDuplicates()
      
    } catch (error) {
      console.error('Match type auto merge error:', error)
      showBanner('error', 'Match Type Auto-Merge Error', 
        `An error occurred during match type auto merge: ${(error as Error).message}`)
    } finally {
      setProcessingMatchTypeMerge(false)
      setBatchProgress(null)
    }
  }

  return (
    <div className="space-y-6">
      {/* Banner */}
      {banner.show && (
        <Alert variant={banner.type === 'error' ? 'destructive' : 'default'} className="mb-4">
          {banner.type === 'success' ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertTriangle className="h-4 w-4" />
          )}
          <AlertTitle>{banner.title}</AlertTitle>
          <AlertDescription>{banner.message}</AlertDescription>
        </Alert>
      )}
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Duplicate Management</h2>
          <p className="text-muted-foreground">
          Detect and resolve duplicate companies and contact
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => fetchDuplicates()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>

          
          <Button 
            onClick={() => startScan('both')} 
            disabled={scanning}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {scanning ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Scanning...
              </>
            ) : (
              <>
                <Search className="h-4 w-4 mr-2" />
                Start Scan
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Scan Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Scan Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="scan-only"
                  checked={scanOnly}
                  onCheckedChange={setScanOnly}
                />
                <Label htmlFor="scan-only">Scan Only Mode</Label>
              </div>
              <p className="text-sm text-muted-foreground">
                When enabled, only scans for duplicates without normalizing data first. 
                Use this for faster repeated scans when data is already normalized.
              </p>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="normalize-first"
                  checked={normalizeFirst && !scanOnly}
                  onCheckedChange={(checked) => setNormalizeFirst(checked)}
                  disabled={scanOnly}
                />
                <Label htmlFor="normalize-first">Normalize Before Scan</Label>
              </div>
              <p className="text-sm text-muted-foreground">
                Normalize data before scanning for duplicates. 
                Disabled when scan-only mode is enabled.
              </p>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => startNormalization('both')}
                disabled={normalizing}
                size="sm"
              >
                {normalizing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                    Normalizing...
                  </>
                ) : (
                  <>
                    <Database className="h-4 w-4 mr-2" />
                    Normalize All Data
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => startNormalization('company')}
                disabled={normalizing}
                size="sm"
              >
                <Building2 className="h-4 w-4 mr-2" />
                Normalize Companies
              </Button>
              <Button
                variant="outline"
                onClick={() => startNormalization('contact')}
                disabled={normalizing}
                size="sm"
              >
                <User className="h-4 w-4 mr-2" />
                Normalize Contacts
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Duplicates</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDuplicates}</div>
            <p className="text-xs text-muted-foreground">
              Pending resolution
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Company Duplicates</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{companyDuplicates}</div>
            <p className="text-xs text-muted-foreground">
              Companies with duplicates
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contact Duplicates</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{contactDuplicates}</div>
            <p className="text-xs text-muted-foreground">
              Contacts with duplicates
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Record Type</label>
              <Select value={filter} onValueChange={(value) => handleFilterChange(value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="company">Companies</SelectItem>
                  <SelectItem value="contact">Contacts</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select value={statusFilter} onValueChange={(value) => handleStatusFilterChange(value as DuplicateStatus)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="false_positive">False Positive</SelectItem>
                  <SelectItem value="blocked">Blocked</SelectItem>
                  <SelectItem value="merged">Merged</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Min Confidence</label>
              <Input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={confidenceFilter.min}
                onChange={(e) => handleConfidenceFilterChange('min', parseFloat(e.target.value) || 0)}
                placeholder="0.0"
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Max Confidence</label>
              <Input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={confidenceFilter.max}
                onChange={(e) => handleConfidenceFilterChange('max', parseFloat(e.target.value) || 1)}
                placeholder="1.0"
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Match Type</label>
              <div className="flex gap-2">
                <Select value={matchTypeFilter} onValueChange={handleMatchTypeFilterChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="All match types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="exact_domain">Exact Domain</SelectItem>
                    <SelectItem value="exact_name">Exact Name</SelectItem>
                    <SelectItem value="exact_email">Exact Email</SelectItem>
                    <SelectItem value="linkedin_match">LinkedIn</SelectItem>
                    <SelectItem value="phone_match">Phone</SelectItem>
                    <SelectItem value="similarity">Similarity</SelectItem>
                  </SelectContent>
                </Select>
                {matchTypeFilter !== 'all' && (
                  <Button
                    onClick={handleAutoMergeAllByMatchType}
                    disabled={processingMatchTypeMerge}
                    size="sm"
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                  >
                    {processingMatchTypeMerge ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        🤖 Auto-Merge All
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
          
          {/* Active Filters Display */}
          {hasActiveFilters() && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Active Filters:</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear All Filters
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {filter !== 'all' && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    Type: {filter === 'company' ? 'Companies' : 'Contacts'}
                  </Badge>
                )}
                {statusFilter !== 'pending' && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Status: {statusFilter}
                  </Badge>
                )}
                {confidenceFilter.min > 0 && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                    Min Confidence: {(confidenceFilter.min * 100).toFixed(0)}%
                  </Badge>
                )}
                {confidenceFilter.max < 1 && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                    Max Confidence: {(confidenceFilter.max * 100).toFixed(0)}%
                  </Badge>
                )}
                {matchTypeFilter !== 'all' && (
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                    Match Type: {getMatchTypeLabel(matchTypeFilter)}
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Batch Actions */}
      {selectedDuplicates.size > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {selectedDuplicates.size} duplicate{selectedDuplicates.size > 1 ? 's' : ''} selected
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={() => handleBatchAutoMerge()}
                  size="sm"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                >
                  🤖 Auto Merge All
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleBatchAction('keep_separate')}
                  size="sm"
                  title="Remove these duplicates from the duplicates table (they will remain as separate records)"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Keep Separate
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Duplicates List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Duplicate Records</CardTitle>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedDuplicates.size === duplicates.length && duplicates.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-muted-foreground">Select All</span>
              
              {selectedDuplicates.size > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAutoMergeAll}
                  className="ml-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white"
                >
                  🤖 Auto-Merge All ({selectedDuplicates.size})
                </Button>
              )}
            </div>
          </div>
          
          {/* Pagination Controls - Moved to top */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between pt-4 border-t mt-4">
              <div className="flex items-center gap-4">
                <div className="text-sm text-muted-foreground">
                  Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                  {Math.min(pagination.page * pagination.pageSize, pagination.total)} of{' '}
                  {pagination.total} results
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Page size:</span>
                  <Select value={pagination.pageSize.toString()} onValueChange={(value) => handlePageSizeChange(parseInt(value))}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {pageSizeOptions.map(size => (
                        <SelectItem key={size} value={size.toString()}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={pagination.page === 1}
                  className="px-2"
                >
                  «
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPreviousPage}
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                
                {/* Page numbers */}
                <div className="flex items-center gap-1">
                  {(() => {
                    const pages: React.ReactNode[] = []
                    const currentPage = pagination.page
                    const totalPages = pagination.totalPages
                    
                    // Show first page
                    if (currentPage > 3) {
                      pages.push(
                        <Button
                          key={1}
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(1)}
                          className="px-2"
                        >
                          1
                        </Button>
                      )
                      if (currentPage > 4) {
                        pages.push(<span key="dots1" className="px-2 text-muted-foreground">...</span>)
                      }
                    }
                    
                    // Show pages around current page
                    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                      if (i === currentPage) {
                        pages.push(
                          <Button
                            key={i}
                            size="sm"
                            className="px-2"
                            disabled
                          >
                            {i}
                          </Button>
                        )
                      } else {
                        pages.push(
                          <Button
                            key={i}
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(i)}
                            className="px-2"
                          >
                            {i}
                          </Button>
                        )
                      }
                    }
                    
                    // Show last page
                    if (currentPage < totalPages - 2) {
                      if (currentPage < totalPages - 3) {
                        pages.push(<span key="dots2" className="px-2 text-muted-foreground">...</span>)
                      }
                      pages.push(
                        <Button
                          key={totalPages}
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className="px-2"
                        >
                          {totalPages}
                        </Button>
                      )
                    }
                    
                    return pages
                  })()}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={pagination.page === pagination.totalPages}
                >
                  Next
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={pagination.page === pagination.totalPages}
                  className="px-2"
                >
                  »
                </Button>
              </div>
            </div>
          )}
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : duplicates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No duplicates found. Try running a scan or adjusting your filters.
            </div>
          ) : (
            <div className="space-y-4">
              {duplicates.map((duplicate) => (
                <div
                  key={duplicate.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={selectedDuplicates.has(duplicate.id)}
                        onCheckedChange={(checked) => handleDuplicateSelect(duplicate.id, checked as boolean)}
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {duplicate.record_type === 'company' ? (
                            <Building2 className="h-4 w-4 text-blue-600" />
                          ) : (
                            <User className="h-4 w-4 text-green-600" />
                          )}
                          <span className="font-medium">
                            {duplicate.primary_name} ↔ {duplicate.duplicate_name}
                          </span>
                          <Badge className={getConfidenceColor(duplicate.confidence_score)}>
                            {Math.round(duplicate.confidence_score * 100)}% confidence
                          </Badge>
                          <Badge variant="outline">
                            {getMatchTypeLabel(duplicate.match_type)}
                          </Badge>
                        </div>

                        <div className="text-sm text-muted-foreground space-y-1">
                          <div className="flex items-center gap-2">
                            <span>Primary: {duplicate.primary_name}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const entityType = duplicate.record_type === 'company' ? 'companies' : 'people'
                                const url = `/dashboard/${entityType}/${duplicate.primary_record_id}`
                                window.open(url, '_blank')
                              }}
                              className="h-6 px-2 text-xs hover:bg-gray-100"
                            >
                              <ExternalLink className="h-3 w-3 mr-1" />
                              View
                            </Button>
                          </div>
                          <div className="flex items-center gap-2">
                            <span>Duplicate: {duplicate.duplicate_name}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const entityType = duplicate.record_type === 'company' ? 'companies' : 'people'
                                const url = `/dashboard/${entityType}/${duplicate.duplicate_record_id}`
                                window.open(url, '_blank')
                              }}
                              className="h-6 px-2 text-xs hover:bg-gray-100"
                            >
                              <ExternalLink className="h-3 w-3 mr-1" />
                              View
                            </Button>
                          </div>
                          {duplicate.match_details && (
                            <div className="mt-1">
                              Match details: {JSON.stringify(duplicate.match_details, null, 2).slice(0, 100)}...
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <span>Created: {new Date(duplicate.created_at).toLocaleDateString()}</span>
                          <span>Status: {duplicate.status}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleMerge(duplicate)}
                      >
                        <Merge className="h-4 w-4 mr-2" />
                        Merge
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleAutoMerge(duplicate)}
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                      >
                        🤖 Auto
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Batch Progress Indicator */}
          {processingLargeBatch && batchProgress && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-sm font-medium text-blue-800">Processing Batch</span>
                </div>
                <span className="text-sm text-blue-600">
                  {batchProgress.current} / {batchProgress.total}
                </span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(batchProgress.current / batchProgress.total) * 100}%` }}
                ></div>
              </div>
              <div className="text-xs text-blue-600 mt-1">{batchProgress.status}</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Merge Modal */}
      {selectedDuplicate && showMergeModal && (
        <OptimizedMergeModal
          isOpen={showMergeModal}
          onClose={() => {
            setShowMergeModal(false)
            setSelectedDuplicate(null)
          }}
          onMerge={handleMergeSubmit}
          primaryRecord={{
            [selectedDuplicate.record_type === 'company' ? 'company_id' : 'contact_id']: selectedDuplicate.primary_record_id,
            name: selectedDuplicate.primary_name,
            // Fallback data if primary_data is empty
            ...(selectedDuplicate.primary_data || {
              [selectedDuplicate.record_type === 'company' ? 'company_name' : 'full_name']: selectedDuplicate.primary_name
            })
          }}
          duplicateRecord={{
            [selectedDuplicate.record_type === 'company' ? 'company_id' : 'contact_id']: selectedDuplicate.duplicate_record_id,
            name: selectedDuplicate.duplicate_name,
            // Fallback data if duplicate_data is empty
            ...(selectedDuplicate.duplicate_data || {
              [selectedDuplicate.record_type === 'company' ? 'company_name' : 'full_name']: selectedDuplicate.duplicate_name
            })
          }}
          recordType={selectedDuplicate.record_type}
        />
      )}
    </div>
  )
}
