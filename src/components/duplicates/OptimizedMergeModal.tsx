'use client'

import React, { useState, useEffect, useMemo, useC<PERSON>back, memo } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { Building2, User, AlertTriangle, CheckCircle, XCircle, ExternalLink } from 'lucide-react'

interface FieldComparison {
  field: string
  label: string
  primaryValue: any
  duplicateValue: any
  selectedValue: 'primary' | 'duplicate' | 'custom'
  customValue?: any
  fieldType: 'text' | 'email' | 'phone' | 'url' | 'number' | 'date' | 'textarea' | 'array' | 'boolean'
  category: string
  hasData: boolean
}

interface MergeModalProps {
  isOpen: boolean
  onClose: () => void
  onMerge: (mergeData: any, autoMerge?: boolean) => void
  primaryRecord: any
  duplicateRecord: any
  recordType: 'company' | 'contact'
}

interface MergeDecisionResult {
  canAutoMerge: boolean
  reason: string
  conflictingFields: string[]
  riskLevel: 'low' | 'medium' | 'high'
}

// Simplified field mappings - only most important fields
const COMPANY_FIELDS = [
  { field: 'company_name', label: 'Company Name', type: 'text', category: 'Basic Information' },
  { field: 'company_website', label: 'Website', type: 'url', category: 'Basic Information' },
  { field: 'industry', label: 'Industry', type: 'text', category: 'Basic Information' },
  { field: 'company_phone', label: 'Phone', type: 'phone', category: 'Contact' },
  { field: 'main_email', label: 'Main Email', type: 'email', category: 'Contact' },
  { field: 'company_address', label: 'Address', type: 'textarea', category: 'Location' },
  { field: 'company_city', label: 'City', type: 'text', category: 'Location' },
  { field: 'company_state', label: 'State', type: 'text', category: 'Location' },
  { field: 'company_country', label: 'Country', type: 'text', category: 'Location' },
  { field: 'annual_revenue', label: 'Annual Revenue', type: 'number', category: 'Financial' },
  { field: 'fund_size', label: 'Fund Size', type: 'number', category: 'Financial' },
  { field: 'number_of_employees', label: 'Employee Count', type: 'number', category: 'Basic Information' },
]

const CONTACT_FIELDS = [
  { field: 'full_name', label: 'Full Name', type: 'text', category: 'Basic Information' },
  { field: 'first_name', label: 'First Name', type: 'text', category: 'Basic Information' },
  { field: 'last_name', label: 'Last Name', type: 'text', category: 'Basic Information' },
  { field: 'email', label: 'Primary Email', type: 'email', category: 'Contact' },
  { field: 'personal_email', label: 'Personal Email', type: 'email', category: 'Contact' },
  { field: 'additional_email', label: 'Additional Email', type: 'email', category: 'Contact' },
  { field: 'phone_number', label: 'Phone Number', type: 'phone', category: 'Contact' },
  { field: 'contact_phone', label: 'Contact Phone', type: 'phone', category: 'Contact' },
  { field: 'phone_number_secondary', label: 'Secondary Phone', type: 'phone', category: 'Contact' },
  { field: 'title', label: 'Job Title', type: 'text', category: 'Professional' },
  { field: 'headline', label: 'Headline', type: 'text', category: 'Professional' },
  { field: 'seniority', label: 'Seniority', type: 'text', category: 'Professional' },
  { field: 'linkedin_url', label: 'LinkedIn URL', type: 'url', category: 'Social' },
  { field: 'twitter', label: 'Twitter', type: 'url', category: 'Social' },
  { field: 'facebook', label: 'Facebook', type: 'url', category: 'Social' },
  { field: 'instagram', label: 'Instagram', type: 'url', category: 'Social' },
  { field: 'youtube', label: 'YouTube', type: 'url', category: 'Social' },
  { field: 'contact_address', label: 'Address', type: 'textarea', category: 'Location' },
  { field: 'contact_city', label: 'City', type: 'text', category: 'Location' },
  { field: 'contact_state', label: 'State', type: 'text', category: 'Location' },
  { field: 'contact_country', label: 'Country', type: 'text', category: 'Location' },
  { field: 'contact_zip_code', label: 'Zip Code', type: 'text', category: 'Location' },
  { field: 'company_name', label: 'Company Name', type: 'text', category: 'Professional' },
  { field: 'relationship_owner', label: 'Relationship Owner', type: 'text', category: 'Professional' },
  { field: 'role_in_decision_making', label: 'Decision Making Role', type: 'text', category: 'Professional' },
  { field: 'source_of_introduction', label: 'Source of Introduction', type: 'text', category: 'Professional' },
  { field: 'executive_summary', label: 'Executive Summary', type: 'textarea', category: 'Details' },
  { field: 'education_college', label: 'College', type: 'text', category: 'Education' },
  { field: 'education_college_year_graduated', label: 'College Graduation Year', type: 'text', category: 'Education' },
  { field: 'education_high_school', label: 'High School', type: 'text', category: 'Education' },
  { field: 'education_high_school_year_graduated', label: 'High School Graduation Year', type: 'text', category: 'Education' },
  { field: 'age', label: 'Age', type: 'text', category: 'Details' },
  { field: 'job_tier', label: 'Job Tier', type: 'text', category: 'Professional' },
  { field: 'region', label: 'Region', type: 'text', category: 'Location' },
  { field: 'notes', label: 'Notes', type: 'textarea', category: 'Details' },
]

// Memoized field value renderer
const FieldValueRenderer = memo(({ value, fieldType }: { value: any; fieldType: string }) => {
  if (value === null || value === undefined || value === '') {
    return <span className="text-muted-foreground">No value</span>
  }

  if (fieldType === 'boolean') {
    return (
      <div className="flex items-center gap-2">
        {value ? <CheckCircle className="h-4 w-4 text-green-600" /> : <XCircle className="h-4 w-4 text-red-600" />}
        <span className="text-sm">{value ? 'Yes' : 'No'}</span>
      </div>
    )
  }

  if (fieldType === 'array' && Array.isArray(value)) {
    return (
      <div className="flex flex-wrap gap-1">
        {value.map((item: any, index: number) => (
          <Badge key={index} variant="secondary" className="text-xs">{item}</Badge>
        ))}
      </div>
    )
  }

  return <span className="text-sm">{value}</span>
})

FieldValueRenderer.displayName = 'FieldValueRenderer'

// Decision tree for evaluating merge compatibility
function evaluateMergeDecision(primaryRecord: any, duplicateRecord: any, recordType: 'company' | 'contact'): MergeDecisionResult {
  const conflictingFields: string[] = []
  let reason = ''
  let riskLevel: 'low' | 'medium' | 'high' = 'low'
  
  if (recordType === 'contact') {
    const primaryEmail = primaryRecord.email || primaryRecord.personal_email || primaryRecord.additional_email
    const duplicateEmail = duplicateRecord.email || duplicateRecord.personal_email || duplicateRecord.additional_email
    const primaryLinkedIn = primaryRecord.linkedin_url
    const duplicateLinkedIn = duplicateRecord.linkedin_url
    
    // Helper function to normalize LinkedIn URLs for comparison
    const normalizeLinkedInUrl = (url: string | null | undefined): string => {
      if (!url) return ''
      return url.toLowerCase()
        .replace(/\/$/, '') // Remove trailing slash
        .replace(/^https?:\/\/(www\.)?linkedin\.com\/in\//, '') // Remove base URL
        .replace(/\/.*$/, '') // Remove everything after the username
    }
    
    // Helper function to check if emails are from same domain
    const isSameDomain = (email1: string, email2: string): boolean => {
      if (!email1 || !email2) return false
      const domain1 = email1.split('@')[1]?.toLowerCase()
      const domain2 = email2.split('@')[1]?.toLowerCase()
      return domain1 === domain2
    }
    
    // Helper function to check if names are similar
    const areNamesSimilar = (name1: string, name2: string): boolean => {
      if (!name1 || !name2) return false
      const normalize = (name: string) => name.toLowerCase().replace(/[^a-z]/g, '')
      const n1 = normalize(name1)
      const n2 = normalize(name2)
      
      // Check if one name contains the other or if they're very similar
      return n1.includes(n2) || n2.includes(n1) || 
             Math.abs(n1.length - n2.length) <= 2 // Allow for minor differences
    }
    
    const normalizedPrimaryLinkedIn = normalizeLinkedInUrl(primaryLinkedIn)
    const normalizedDuplicateLinkedIn = normalizeLinkedInUrl(duplicateLinkedIn)
    
    // STRICT REQUIREMENT: For contacts, we need either SAME EMAIL or SAME LINKEDIN to auto-merge
    
    // Case 1: Both have LinkedIn URLs
    if (normalizedPrimaryLinkedIn && normalizedDuplicateLinkedIn) {
      if (normalizedPrimaryLinkedIn === normalizedDuplicateLinkedIn) {
        // Same LinkedIn URL - this is likely the same person
        if (primaryEmail && duplicateEmail) {
          if (primaryEmail.toLowerCase() === duplicateEmail.toLowerCase()) {
            // Same LinkedIn, same email - definitely same person
            return {
              canAutoMerge: true,
              reason: 'Same LinkedIn URL and email address - clearly the same person',
              conflictingFields: [],
              riskLevel: 'low'
            }
          } else if (isSameDomain(primaryEmail, duplicateEmail)) {
            // Same LinkedIn, different emails but same domain - likely work/personal emails
            return {
              canAutoMerge: true,
              reason: 'Same LinkedIn URL with different emails from same domain - likely work/personal email variations',
              conflictingFields: ['email'],
              riskLevel: 'low'
            }
          } else {

              // Different emails, different names - suspicious
              conflictingFields.push('email')
              reason = 'Same LinkedIn URL but different email domains - potential data integrity issue'
              riskLevel = 'high'
            }
        } else {
          // Same LinkedIn, one or both missing emails
          return {
            canAutoMerge: true,
            reason: 'Same LinkedIn URL - merging to complete email information',
            conflictingFields: [],
            riskLevel: 'low'
          }
        }
      } else {
        // Different LinkedIn URLs - different people
        conflictingFields.push('linkedin_url')
        reason = 'Different LinkedIn URLs - these appear to be different people'
        riskLevel = 'high'
      }
    }
    
    // Case 2: Only one has LinkedIn URL
    else if (normalizedPrimaryLinkedIn || normalizedDuplicateLinkedIn) {
      if (primaryEmail && duplicateEmail) {
        if (primaryEmail.toLowerCase() === duplicateEmail.toLowerCase()) {
          // Same email, one has LinkedIn - safe to merge
          return {
            canAutoMerge: true,
            reason: 'Same email address - merging to complete LinkedIn information',
            conflictingFields: [],
            riskLevel: 'low'
          }
        } else if (isSameDomain(primaryEmail, duplicateEmail) && 
                   areNamesSimilar(primaryRecord.full_name, duplicateRecord.full_name)) {
          // Same domain, similar names - probably same person
          return {
            canAutoMerge: true,
            reason: 'Similar names and same email domain - likely the same person',
            conflictingFields: ['email'],
            riskLevel: 'medium'
          }
        } else {
          // Different emails - need manual review
          conflictingFields.push('email')
          reason = 'Different email addresses with only one LinkedIn profile - requires manual review'
          riskLevel = 'high'
        }
      } else {
        // CONSERVATIVE POLICY: Allow merge if one has email/LinkedIn and other doesn't
        // This completes missing information without conflicts
        return {
          canAutoMerge: true,
          reason: 'One record has email/LinkedIn, other missing - safe to merge and complete information',
          conflictingFields: [],
          riskLevel: 'low'
        }
      }
    }
    
    // Case 3: No LinkedIn URLs for either record
    else {
      if (primaryEmail && duplicateEmail) {
        if (primaryEmail.toLowerCase() === duplicateEmail.toLowerCase()) {
          // Same email, no LinkedIn - safe to merge if names are similar
          if (areNamesSimilar(primaryRecord.full_name, duplicateRecord.full_name)) {
            return {
              canAutoMerge: true,
              reason: 'Same email address and similar names - likely the same person',
              conflictingFields: [],
              riskLevel: 'low'
            }
          } else {
            conflictingFields.push('name')
            reason = 'Same email but different names - potential shared email account'
            riskLevel = 'high'
          }
        } else if (isSameDomain(primaryEmail, duplicateEmail) && 
                   areNamesSimilar(primaryRecord.full_name, duplicateRecord.full_name)) {
          return {
            canAutoMerge: true,
            reason: 'Similar names and same email domain - likely the same person with multiple emails',
            conflictingFields: ['email'],
            riskLevel: 'medium'
          }
        } else {
          conflictingFields.push('email')
          reason = 'Different emails and no LinkedIn profiles - requires manual review'
          riskLevel = 'high'
        }
      } else {
        // CONSERVATIVE POLICY: Allow merge if one has email and other doesn't
        // This completes missing information without conflicts
        if (primaryEmail || duplicateEmail) {
          return {
            canAutoMerge: true,
            reason: 'One record has email, other missing - safe to merge and complete information',
            conflictingFields: [],
            riskLevel: 'low'
          }
        } else {
          // No emails or LinkedIn - only merge if names are very similar
          if (areNamesSimilar(primaryRecord.full_name, duplicateRecord.full_name)) {
            return {
              canAutoMerge: true,
              reason: 'Similar names with missing contact information - merging to consolidate data',
              conflictingFields: [],
              riskLevel: 'medium'
            }
          } else {
            conflictingFields.push('name')
            reason = 'Different names with insufficient identifying information - requires manual review'
            riskLevel = 'high'
          }
        }
      }
    }
    
    // Check for additional red flags
    if (primaryRecord.company_name && duplicateRecord.company_name) {
      const company1 = primaryRecord.company_name.toLowerCase()
      const company2 = duplicateRecord.company_name.toLowerCase()
      if (company1 !== company2 && !company1.includes(company2) && !company2.includes(company1)) {
        conflictingFields.push('company_name')
        reason += ' Additionally, different company names suggest these may be different people.'
        riskLevel = 'high'
      }
    }
  } else if (recordType === 'company') {
    // Company merge decision logic - STRICT REQUIREMENT: domains must be the same
    const primaryWebsite = primaryRecord.company_website
    const duplicateWebsite = duplicateRecord.company_website
    const primaryName = primaryRecord.company_name
    const duplicateName = duplicateRecord.company_name
    
    // Helper function to extract domain from URL
    const extractDomain = (url: string | null | undefined): string => {
      if (!url) return ''
      return url.toLowerCase()
        .replace(/^https?:\/\//, '') // Remove protocol
        .replace(/^www\./, '') // Remove www prefix
        .replace(/\/.*$/, '') // Remove everything after domain
        .replace(/^\*\./, '') // Remove wildcard subdomain prefix
        .split('/')[0] // Take only the domain part
    }
    
    const primaryDomain = extractDomain(primaryWebsite)
    const duplicateDomain = extractDomain(duplicateWebsite)
    
    // STRICT REQUIREMENT: For companies, domains must be the same to auto-merge
    if (primaryDomain && duplicateDomain) {
      if (primaryDomain === duplicateDomain) {
        return {
          canAutoMerge: true,
          reason: 'Same website domain - clearly the same company',
          conflictingFields: [],
          riskLevel: 'low'
        }
      } else {
        // Check if domains are subdomains of each other (e.g., app.company.com vs company.com)
        const isSubdomain = (domain1: string, domain2: string): boolean => {
          const parts1 = domain1.split('.')
          const parts2 = domain2.split('.')
          
          // Check if one is a subdomain of the other
          if (parts1.length > parts2.length) {
            return parts1.slice(-parts2.length).join('.') === domain2
          } else if (parts2.length > parts1.length) {
            return parts2.slice(-parts1.length).join('.') === domain1
          }
          return false
        }
        
        if (isSubdomain(primaryDomain, duplicateDomain) || isSubdomain(duplicateDomain, primaryDomain)) {
          return {
            canAutoMerge: true,
            reason: 'Related website domains (subdomain relationship) - likely the same company',
            conflictingFields: ['company_website'],
            riskLevel: 'low'
          }
        } else {
          conflictingFields.push('company_website')
          reason = 'Different website domains - likely different companies'
          riskLevel = 'high'
        }
      }
    } else {
      return {
        canAutoMerge: true,
        reason: 'One record has website domain, other missing - safe to merge and complete information',
        conflictingFields: [],
        riskLevel: 'low'
      }
    }
  }
  
  return {
    canAutoMerge: false,
    reason,
    conflictingFields,
    riskLevel
  }
}

function OptimizedMergeModal({
  isOpen,
  onClose,
  onMerge,
  primaryRecord,
  duplicateRecord,
  recordType
}: MergeModalProps) {
  const [fieldComparisons, setFieldComparisons] = useState<FieldComparison[]>([])
  const [loading, setLoading] = useState(false)
  const [actualPrimaryRecord, setActualPrimaryRecord] = useState<any>({})
  const [actualDuplicateRecord, setActualDuplicateRecord] = useState<any>({})
  const [autoMerge, setAutoMerge] = useState(false)
  const [mergeDecision, setMergeDecision] = useState<MergeDecisionResult | null>(null)
  const [lastSelectionAction, setLastSelectionAction] = useState<'primary' | 'duplicate' | null>(null)

  // Memoized field comparisons - only process fields that have data
  const optimizedFieldComparisons = useMemo(() => {
    return fieldComparisons.filter(field => field.hasData)
  }, [fieldComparisons])

  // Memoized categories
  const categories = useMemo(() => {
    return [...new Set(optimizedFieldComparisons.map(field => field.category))]
  }, [optimizedFieldComparisons])

  // Fetch data when modal opens
  useEffect(() => {
    if (isOpen && primaryRecord && duplicateRecord) {
      const primaryId = primaryRecord.company_id || primaryRecord.contact_id
      const duplicateId = duplicateRecord.company_id || duplicateRecord.contact_id
      if (primaryId && duplicateId) {
        fetchMergePreview()
      }
    }
  }, [isOpen, primaryRecord, duplicateRecord])

  const fetchMergePreview = useCallback(async () => {
    setLoading(true)
    try {
      const requestBody = {
        primaryId: primaryRecord.company_id || primaryRecord.contact_id,
        duplicateId: duplicateRecord.company_id || duplicateRecord.contact_id,
        recordType
      }
      
      const response = await fetch('/api/duplicates/merge-preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })

      const data = await response.json()
      
      if (data.success && data.preview) {
        setActualPrimaryRecord(data.preview.primaryRecord || {})
        setActualDuplicateRecord(data.preview.duplicateRecord || {})
        
        // Create field comparisons - only include fields that have data
        const fields = recordType === 'company' ? COMPANY_FIELDS : CONTACT_FIELDS
        const comparisons: FieldComparison[] = fields.map(field => {
          const primaryValue = data.preview.primaryRecord?.[field.field] || null
          const duplicateValue = data.preview.duplicateRecord?.[field.field] || null
          const hasData = (primaryValue !== null && primaryValue !== undefined && primaryValue !== '') ||
                         (duplicateValue !== null && duplicateValue !== undefined && duplicateValue !== '')
          
          return {
            field: field.field,
            label: field.label,
            primaryValue,
            duplicateValue,
            selectedValue: 'primary' as const,
            fieldType: field.type as FieldComparison['fieldType'],
            category: field.category,
            hasData
          }
        }).filter(field => field.hasData) // Only include fields with actual data
        
        setFieldComparisons(comparisons)

        // Evaluate merge decision using the decision tree
        const decision = evaluateMergeDecision(
          data.preview.primaryRecord,
          data.preview.duplicateRecord,
          recordType
        )
        setMergeDecision(decision)
        
        // If decision tree says we can't auto-merge, disable auto merge by default
        if (!decision.canAutoMerge && autoMerge) {
          setAutoMerge(false)
        }
      }
    } catch (error) {
      console.error('Error fetching merge preview:', error)
    } finally {
      setLoading(false)
    }
  }, [primaryRecord, duplicateRecord, recordType])

  const handleFieldSelectionChange = useCallback((fieldName: string, value: 'primary' | 'duplicate' | 'custom') => {
    setFieldComparisons(prev => 
      prev.map(field => 
        field.field === fieldName 
          ? { ...field, selectedValue: value }
          : field
      )
    )
  }, [])

  const handleCustomValueChange = useCallback((fieldName: string, value: any) => {
    setFieldComparisons(prev => 
      prev.map(field => 
        field.field === fieldName 
          ? { ...field, customValue: value }
          : field
      )
    )
  }, [])

  const handleSelectAllPrimary = useCallback(() => {
    setFieldComparisons(prev => 
      prev.map(field => ({ ...field, selectedValue: 'primary' as const }))
    )
    setLastSelectionAction('primary')
    // Clear the action indicator after 2 seconds
    setTimeout(() => setLastSelectionAction(null), 2000)
  }, [])

  const handleSelectAllDuplicate = useCallback(() => {
    setFieldComparisons(prev => 
      prev.map(field => ({ ...field, selectedValue: 'duplicate' as const }))
    )
    setLastSelectionAction('duplicate')
    // Clear the action indicator after 2 seconds
    setTimeout(() => setLastSelectionAction(null), 2000)
  }, [])

  const handleMerge = useCallback(() => {
    // If auto merge is selected but decision tree says we can't, show warning
    if (autoMerge && mergeDecision && !mergeDecision.canAutoMerge) {
      alert(`Auto-merge is not recommended for this record pair:\n\n${mergeDecision.reason}\n\nPlease use manual merge instead.`)
      return
    }

    const fieldsToMerge: Record<string, any> = {}
    const customValues: Record<string, any> = {}

    fieldComparisons.forEach(field => {
      if (field.selectedValue === 'primary') {
        fieldsToMerge[field.field] = field.primaryValue
      } else if (field.selectedValue === 'duplicate') {
        fieldsToMerge[field.field] = field.duplicateValue
      } else if (field.selectedValue === 'custom') {
        fieldsToMerge[field.field] = field.customValue
        customValues[field.field] = field.customValue
      }
    })

    onMerge(fieldsToMerge, autoMerge)
  }, [fieldComparisons, onMerge, autoMerge, mergeDecision])

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading merge preview...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {recordType === 'company' ? <Building2 className="h-5 w-5" /> : <User className="h-5 w-5" />}
            Merge {recordType === 'company' ? 'Companies' : 'Contacts'}
          </DialogTitle>
        </DialogHeader>

        {/* Auto Merge Option */}
        <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="auto-merge" 
              checked={autoMerge}
              onCheckedChange={(checked) => setAutoMerge(checked === true)}
              disabled={mergeDecision ? !mergeDecision.canAutoMerge : false}
            />
            <Label htmlFor="auto-merge" className="text-sm font-medium">
              🤖 Smart Auto-Merge
            </Label>
          </div>
          <p className="text-xs text-blue-600 mt-1 ml-6">
            Automatically combine data from both records by keeping the most complete information. 
            This will fill empty fields, prefer longer descriptions, and merge arrays intelligently.
          </p>
        </div>

        {/* Merge Decision Tree Results */}
        {mergeDecision && (
          <div className={`mb-4 p-4 rounded-lg border ${
            mergeDecision.canAutoMerge 
              ? mergeDecision.riskLevel === 'low' 
                ? 'bg-green-50 border-green-200' 
                : 'bg-yellow-50 border-yellow-200'
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-start gap-3">
              {mergeDecision.canAutoMerge ? (
                mergeDecision.riskLevel === 'low' ? (
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                )
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
              )}
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className={`font-medium ${
                    mergeDecision.canAutoMerge 
                      ? mergeDecision.riskLevel === 'low' 
                        ? 'text-green-800' 
                        : 'text-yellow-800'
                      : 'text-red-800'
                  }`}>
                    {mergeDecision.canAutoMerge ? 'Auto-Merge Approved' : 'Manual Review Required'}
                  </h3>
                  <Badge variant="outline" className={
                    mergeDecision.riskLevel === 'low' ? 'border-green-300 text-green-700' :
                    mergeDecision.riskLevel === 'medium' ? 'border-yellow-300 text-yellow-700' :
                    'border-red-300 text-red-700'
                  }>
                    {mergeDecision.riskLevel.toUpperCase()} RISK
                  </Badge>
                </div>
                <p className={`text-sm ${
                  mergeDecision.canAutoMerge 
                    ? mergeDecision.riskLevel === 'low' 
                      ? 'text-green-700' 
                      : 'text-yellow-700'
                    : 'text-red-700'
                }`}>
                  {mergeDecision.reason}
                </p>
                {mergeDecision.conflictingFields.length > 0 && (
                  <div className="mt-2">
                    <p className="text-xs font-medium text-gray-600 mb-1">Conflicting fields:</p>
                    <div className="flex flex-wrap gap-1">
                      {mergeDecision.conflictingFields.map(field => (
                        <Badge key={field} variant="secondary" className="text-xs">
                          {field}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Primary Record */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-lg">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Primary Record
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const recordId = recordType === 'contact' ? actualPrimaryRecord.contact_id : actualPrimaryRecord.company_id
                    const entityType = recordType === 'company' ? 'companies' : 'people'
                    const url = `/dashboard/${entityType}/${recordId}`
                    window.open(url, '_blank')
                  }}
                  className="flex items-center gap-1 text-xs"
                >
                  <ExternalLink className="h-3 w-3" />
                  View
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm font-medium">
                {actualPrimaryRecord.company_name || actualPrimaryRecord.full_name || 'Unnamed Record'}
              </div>
              <div className="text-xs text-muted-foreground">
                ID: {recordType === 'contact' ? actualPrimaryRecord.contact_id : actualPrimaryRecord.company_id}
              </div>
              {actualPrimaryRecord.company_website && (
                <div className="text-sm text-muted-foreground">{actualPrimaryRecord.company_website}</div>
              )}
              {actualPrimaryRecord.email && (
                <div className="text-sm text-muted-foreground">{actualPrimaryRecord.email}</div>
              )}
            </CardContent>
          </Card>

          {/* Duplicate Record */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-lg">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                  Duplicate Record
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const recordId = recordType === 'contact' ? actualDuplicateRecord.contact_id : actualDuplicateRecord.company_id
                    const entityType = recordType === 'company' ? 'companies' : 'people'
                    const url = `/dashboard/${entityType}/${recordId}`
                    window.open(url, '_blank')
                  }}
                  className="flex items-center gap-1 text-xs"
                >
                  <ExternalLink className="h-3 w-3" />
                  View
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm font-medium">
                {actualDuplicateRecord.company_name || actualDuplicateRecord.full_name || 'Unnamed Record'}
              </div>
              <div className="text-xs text-muted-foreground">
                ID: {recordType === 'contact' ? actualDuplicateRecord.contact_id : actualDuplicateRecord.company_id}
              </div>
              {actualDuplicateRecord.company_website && (
                <div className="text-sm text-muted-foreground">{actualDuplicateRecord.company_website}</div>
              )}
              {actualDuplicateRecord.email && (
                <div className="text-sm text-muted-foreground">{actualDuplicateRecord.email}</div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Manual Field Selection - Hidden when auto merge is enabled */}
        {!autoMerge && categories.length > 0 ? (
          <div className="w-full">
            {/* Quick Select All Options */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
              <h3 className="text-sm font-medium mb-3">Quick Selection Options</h3>
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAllPrimary}
                  className={`flex items-center gap-2 transition-all duration-200 ${
                    lastSelectionAction === 'primary' 
                      ? 'bg-green-100 border-green-300 text-green-800 shadow-md scale-105' 
                      : 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
                  }`}
                >
                  <CheckCircle className="h-4 w-4" />
                  Select All Primary
                  {lastSelectionAction === 'primary' && (
                    <span className="ml-1 text-xs">✓ Applied</span>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAllDuplicate}
                  className={`flex items-center gap-2 transition-all duration-200 ${
                    lastSelectionAction === 'duplicate' 
                      ? 'bg-orange-100 border-orange-300 text-orange-800 shadow-md scale-105' 
                      : 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100'
                  }`}
                >
                  <AlertTriangle className="h-4 w-4" />
                  Select All Duplicate
                  {lastSelectionAction === 'duplicate' && (
                    <span className="ml-1 text-xs">✓ Applied</span>
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-600 mt-2">
                Use these buttons to quickly select all fields from either the primary or duplicate record, then customize individual fields as needed.
              </p>
              {lastSelectionAction && (
                <div className={`mt-3 p-2 rounded text-xs font-medium ${
                  lastSelectionAction === 'primary' 
                    ? 'bg-green-100 text-green-800 border border-green-200' 
                    : 'bg-orange-100 text-orange-800 border border-orange-200'
                }`}>
                  ✓ All fields have been set to use {lastSelectionAction} record values
                </div>
              )}
            </div>

            <Tabs defaultValue={categories[0]} className="w-full">
              <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
                {categories.map(category => (
                  <TabsTrigger key={category} value={category} className="text-xs px-2">
                    {category}
                  </TabsTrigger>
                ))}
              </TabsList>

            {categories.map(category => (
              <TabsContent key={category} value={category} className="space-y-4">
                {optimizedFieldComparisons
                  .filter(field => field.category === category)
                  .map(field => (
                    <Card key={field.field}>
                      <CardContent className="pt-6">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                          {/* Field Label */}
                          <div className="lg:col-span-1">
                            <Label className="text-sm font-medium">{field.label}</Label>
                          </div>

                          {/* Primary Record Value */}
                          <div className="lg:col-span-1">
                            <div className="space-y-2">
                              <div className="text-xs text-muted-foreground">Primary Record</div>
                              <div className="p-2 bg-green-50 border border-green-200 rounded">
                                <FieldValueRenderer value={field.primaryValue} fieldType={field.fieldType} />
                              </div>
                            </div>
                          </div>

                          {/* Duplicate Record Value */}
                          <div className="lg:col-span-1">
                            <div className="space-y-2">
                              <div className="text-xs text-muted-foreground">Duplicate Record</div>
                              <div className="p-2 bg-orange-50 border border-orange-200 rounded">
                                <FieldValueRenderer value={field.duplicateValue} fieldType={field.fieldType} />
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="mt-4 space-y-3">
                          <RadioGroup
                            value={field.selectedValue}
                            onValueChange={(value: 'primary' | 'duplicate' | 'custom') => 
                              handleFieldSelectionChange(field.field, value)
                            }
                            className="flex flex-col space-y-2"
                          >
                            <div className={`flex items-center space-x-2 p-2 rounded transition-colors ${
                              field.selectedValue === 'primary' ? 'bg-green-50 border border-green-200' : ''
                            }`}>
                              <RadioGroupItem value="primary" id={`${field.field}-primary`} />
                              <Label htmlFor={`${field.field}-primary`} className="text-sm">
                                Keep primary record value
                                {field.selectedValue === 'primary' && (
                                  <span className="ml-2 text-xs text-green-600 font-medium">✓ Selected</span>
                                )}
                              </Label>
                            </div>
                            <div className={`flex items-center space-x-2 p-2 rounded transition-colors ${
                              field.selectedValue === 'duplicate' ? 'bg-orange-50 border border-orange-200' : ''
                            }`}>
                              <RadioGroupItem value="duplicate" id={`${field.field}-duplicate`} />
                              <Label htmlFor={`${field.field}-duplicate`} className="text-sm">
                                Use duplicate record value
                                {field.selectedValue === 'duplicate' && (
                                  <span className="ml-2 text-xs text-orange-600 font-medium">✓ Selected</span>
                                )}
                              </Label>
                            </div>
                            <div className={`flex items-center space-x-2 p-2 rounded transition-colors ${
                              field.selectedValue === 'custom' ? 'bg-blue-50 border border-blue-200' : ''
                            }`}>
                              <RadioGroupItem value="custom" id={`${field.field}-custom`} />
                              <Label htmlFor={`${field.field}-custom`} className="text-sm">
                                Enter custom value
                                {field.selectedValue === 'custom' && (
                                  <span className="ml-2 text-xs text-blue-600 font-medium">✓ Selected</span>
                                )}
                              </Label>
                            </div>
                          </RadioGroup>

                          {field.selectedValue === 'custom' && (
                            <div className="mt-3 p-3 bg-gray-50 border rounded">
                              <Label className="text-sm font-medium mb-2 block">
                                Custom {field.label}
                              </Label>
                              {field.fieldType === 'textarea' ? (
                                <Textarea
                                  value={field.customValue || ''}
                                  onChange={(e) => handleCustomValueChange(field.field, e.target.value)}
                                  placeholder={`Enter custom ${field.label.toLowerCase()}`}
                                  className="min-h-[80px]"
                                />
                              ) : (
                                <Input
                                  value={field.customValue || ''}
                                  onChange={(e) => handleCustomValueChange(field.field, e.target.value)}
                                  placeholder={`Enter custom ${field.label.toLowerCase()}`}
                                />
                              )}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </TabsContent>
            ))}
            </Tabs>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No fields with data to compare
          </div>
        )}

        {/* Auto Merge Preview */}
        {autoMerge && (
          <div className="p-6 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-4">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <h3 className="font-medium text-green-800">Smart Auto-Merge Enabled</h3>
            </div>
            <p className="text-sm text-green-700 mb-4">
              The system will automatically combine data from both records using intelligent merging rules:
            </p>
            <ul className="text-xs text-green-600 space-y-1 ml-4">
              <li>• Fill empty fields from the other record</li>
              <li>• Prefer longer, more descriptive text values</li>
              <li>• Choose higher numeric values (e.g., employee count, revenue)</li>
              <li>• Merge arrays and lists without duplicates</li>
              <li>• Prefer formatted contact information (phone numbers, emails)</li>
              <li>• Preserve all unique data while avoiding conflicts</li>
            </ul>
          </div>
        )}

        <Separator className="my-6" />

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => {
                setAutoMerge(false)
                handleMerge()
              }}
              disabled={autoMerge}
              className={autoMerge ? "opacity-50" : ""}
            >
              Manual Merge
            </Button>
            <Button 
              onClick={() => {
                setAutoMerge(true)
                handleMerge()
              }}
              disabled={mergeDecision ? !mergeDecision.canAutoMerge : false}
              className={
                mergeDecision && !mergeDecision.canAutoMerge
                  ? "opacity-50 bg-gray-400 cursor-not-allowed"
                  : "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              }
              title={
                mergeDecision && !mergeDecision.canAutoMerge
                  ? `Auto-merge disabled: ${mergeDecision.reason}`
                  : undefined
              }
            >
              🤖 Smart Auto-Merge
              {mergeDecision && !mergeDecision.canAutoMerge && (
                <XCircle className="h-4 w-4 ml-2" />
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default memo(OptimizedMergeModal)
