'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { RefreshCw, CloudUpload, CheckCircle, XCircle, Clock } from 'lucide-react';

interface MigrationJob {
  id: string;
  name: string;
  data: {
    type: string;
    metadata: {
      sourceProvider: string;
      targetProvider: string;
      fileIds?: string[];
      batchSize?: number;
    };
  };
  state: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed';
  progress: number;
  createdAt: number;
  processedAt?: number;
  failedReason?: string;
}

export function MigrationStatusCard() {
  const [migrationJobs, setMigrationJobs] = useState<MigrationJob[]>([]);
  const [loading, setLoading] = useState(false);

  const loadMigrationJobs = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/migration');
      const data = await response.json();
      
      if (data.success) {
        setMigrationJobs(data.jobs || []);
      }
    } catch (error) {
      console.error('Error loading migration jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMigrationJobs();
    // Refresh every 5 seconds
    const interval = setInterval(loadMigrationJobs, 5000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (state: string) => {
    switch (state) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'active':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'waiting':
      case 'delayed':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <CloudUpload className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (state: string) => {
    const variants: Record<string, 'secondary' | 'default' | 'destructive'> = {
      waiting: 'secondary',
      active: 'default',
      completed: 'default',
      failed: 'destructive',
      delayed: 'secondary'
    };

    return (
      <Badge variant={variants[state] || 'secondary'}>
        {state.charAt(0).toUpperCase() + state.slice(1)}
      </Badge>
    );
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getDuration = (createdAt: number, processedAt?: number) => {
    const start = new Date(createdAt);
    const end = processedAt ? new Date(processedAt) : new Date();
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);
    
    if (diffMins > 0) {
      return `${diffMins}m ${diffSecs}s`;
    }
    return `${diffSecs}s`;
  };

  if (migrationJobs.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CloudUpload className="h-5 w-5" />
            Migration Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">
            No migration jobs found
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <CloudUpload className="h-5 w-5" />
            Migration Status
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={loadMigrationJobs}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {migrationJobs.map((job) => (
          <div key={job.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getStatusIcon(job.state)}
                {getStatusBadge(job.state)}
              </div>
              <div className="text-sm text-muted-foreground">
                {formatDate(job.createdAt)}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm">
                <span className="font-medium">
                  {job.data.metadata.sourceProvider} → {job.data.metadata.targetProvider}
                </span>
                {job.data.metadata.fileIds && (
                  <span className="text-muted-foreground ml-2">
                    ({job.data.metadata.fileIds.length} files)
                  </span>
                )}
              </div>
              
              {job.state === 'active' && (
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{job.progress || 0}%</span>
                  </div>
                  <Progress value={job.progress || 0} className="h-2" />
                </div>
              )}
              
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Duration: {getDuration(job.createdAt, job.processedAt)}</span>
                {job.failedReason && (
                  <span className="text-red-500">Error: {job.failedReason}</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
