/**
 * Page Metadata Classification System
 * 
 * This addresses the data loss and context issues when combining scraped content
 * by providing semantic categorization of each page before processing.
 */

// Primary content categories - what is this page fundamentally about?
export type PageContentCategory = 
  | 'company_overview'          // About us, company story, mission
  | 'investment_criteria'       // Investment focus, criteria, philosophy
  | 'fund_information'          // Fund details, strategy, performance
  | 'services_capabilities'     // What we do, services offered
  | 'team_leadership'           // Team members, leadership, bios
  | 'portfolio_properties'      // Properties owned/managed
  | 'financial_information'     // Financial metrics, performance data
  | 'loan_products'            // Lending products, loan terms
  | 'deal_case_studies'        // Specific deals, transactions
  | 'market_insights'          // Market analysis, research
  | 'contact_application'      // Contact info, application process
  | 'legal_compliance'         // Terms, privacy, legal docs
  | 'news_press'              // Press releases, news
  | 'other';                  // Fallback category

// Entity focus - which business entity is this content about?
export type EntityFocus = 
  | 'company_entity'           // The company itself
  | 'fund_entity'             // Specific fund
  | 'individual_person'       // Specific person/team member
  | 'property_asset'          // Specific property
  | 'deal_transaction'        // Specific deal/transaction
  | 'market_general'          // General market info
  | 'multiple_entities'       // Multiple entities discussed
  | 'unclear';               // Cannot determine focus

// Business function - what business function does this relate to?
export type BusinessFunction = 
  | 'investment_management'    // Investment decisions, portfolio management
  | 'lending_financing'       // Loan origination, financing
  | 'property_management'     // Asset management, operations
  | 'development_construction' // Development, construction management
  | 'capital_raising'         // Fundraising, investor relations
  | 'business_operations'     // General business operations
  | 'marketing_sales'         // Marketing, business development
  | 'compliance_legal'        // Legal, regulatory compliance
  | 'multiple_functions'      // Multiple functions
  | 'unclear';               // Cannot determine function

// Data reliability indicators
export type DataReliability = 
  | 'high'                    // Official company content, clear statements
  | 'medium'                  // Generally reliable but may need verification
  | 'low'                     // Unclear, potentially outdated, or ambiguous
  | 'mixed';                 // Mix of reliable and unreliable content

// Financial data presence - what types of financial info are present?
export interface FinancialDataPresence {
  loan_amounts: boolean;           // Loan sizes, amounts
  interest_rates: boolean;         // Interest rates, pricing
  investment_amounts: boolean;     // Investment sizes, equity amounts  
  returns_performance: boolean;    // Returns, IRR, performance metrics
  fund_sizes: boolean;            // Fund size information
  property_values: boolean;       // Property valuations, prices
  fees_costs: boolean;            // Fee structures, costs
  financial_ratios: boolean;      // LTV, DSCR, cap rates, etc.
}

// Investment criteria presence - what criteria info is present?
export interface InvestmentCriteriaPresence {
  deal_size_range: boolean;        // Min/max deal sizes
  property_types: boolean;         // Asset class preferences
  geographic_focus: boolean;       // Target markets/regions
  investment_strategy: boolean;    // Core, value-add, opportunistic
  hold_period: boolean;           // Investment timeline
  return_targets: boolean;        // Target returns, yield requirements
  leverage_preferences: boolean;   // Debt/equity preferences
  sector_focus: boolean;          // Industry/sector focus
}

// Extracted data values with ontological context
export interface ExtractedDataValues {
  // Financial amounts with context (who/what/for whom)
  loan_amounts?: Array<{
    amount: string;                    // e.g., "$1M-$5M", "$500K minimum"
    context: string;                   // e.g., "Company provides to borrowers", "Typical deal size"
    entity_role: string;              // e.g., "lender", "borrower", "sponsor"
  }>;
  
  interest_rates?: Array<{
    rate: string;                     // e.g., "8-12%", "Prime + 3%"
    context: string;                  // e.g., "Charged to borrowers", "Expected returns to LPs"
    entity_role: string;             // e.g., "lender", "investor"
  }>;
  
  investment_amounts?: Array<{
    amount: string;                   // e.g., "$10M fund", "$2M minimum"
    context: string;                  // e.g., "Fund size", "Minimum LP commitment"
    entity_role: string;             // e.g., "fund_manager", "limited_partner"
  }>;
  
  geographic_locations?: Array<{
    location: string;                 // e.g., "East Coast", "New York Metro"
    context: string;                  // e.g., "Primary lending area", "Target investment markets"
    entity_role: string;             // e.g., "lender_focus", "investment_target"
  }>;
  
  property_types?: Array<{
    type: string;                     // e.g., "Multifamily", "Single-family"
    context: string;                  // e.g., "Properties we finance", "Portfolio holdings"
    entity_role: string;             // e.g., "financing_target", "owned_asset"
  }>;
  
  terms_periods?: Array<{
    period: string;                   // e.g., "12-24 months", "3-5 years"
    context: string;                  // e.g., "Loan term", "Hold period", "Fund life"
    entity_role: string;             // e.g., "borrower_obligation", "investment_timeline"
  }>;
  
  returns_performance?: Array<{
    return: string;                   // e.g., "15% IRR", "8% current yield"
    context: string;                  // e.g., "Target returns to LPs", "Historical performance"
    entity_role: string;             // e.g., "investor_return", "fund_performance"
  }>;
  
  requirements_criteria?: Array<{
    requirement: string;              // e.g., "20% down payment", "Accredited investor"
    context: string;                  // e.g., "Borrower requirement", "LP qualification"
    entity_role: string;             // e.g., "borrower_must_have", "investor_must_be"
  }>;
}

// Complete metadata structure for each page
export interface PageMetadata {
  // Core classification
  content_category: PageContentCategory;
  entity_focus: EntityFocus;
  business_function: BusinessFunction;
  
  // Content quality and reliability
  data_reliability: DataReliability;
  content_freshness: 'current' | 'dated' | 'unclear';
  content_specificity: 'highly_specific' | 'general' | 'vague';
  
  // Financial and criteria data presence (boolean flags)
  financial_data: FinancialDataPresence;
  investment_criteria: InvestmentCriteriaPresence;
  
  // ACTUAL EXTRACTED DATA VALUES with ontological context
  extracted_data: ExtractedDataValues;
  
  // Context preservation
  key_entities_mentioned: string[];      // Companies, funds, people mentioned
  primary_topics: string[];              // Main topics discussed
  
  // Classification confidence and notes
  classification_confidence: number;     // 0-1 confidence in classification
  classification_notes: string;          // Why this classification was chosen
  potential_conflicts: string[];         // Potential data conflicts or ambiguities
  
  // Processing metadata
  classified_at: Date;
  classifier_version: string;
}

// Template for LLM classification prompt
export interface ClassificationContext {
  url: string;
  title: string;
  content: string; // Clean extracted text
  screenshot?: Buffer; // Page screenshot for multimodal analysis
  company_name: string;
  page_rank: number;
  ranking_factors: any;
}
