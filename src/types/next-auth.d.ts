import { DefaultSession, DefaultUser } from "next-auth"
import { JWT, DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: string
      username: string
    } & DefaultSession["user"]
  }

  interface User extends De<PERSON>ult<PERSON>ser {
    role: string
    username: string
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    role: string
    username: string
    userId: string
  }
}
