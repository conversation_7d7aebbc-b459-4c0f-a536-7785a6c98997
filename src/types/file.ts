// Generic File Management Types
// This system handles any type of files with content hash deduplication

export interface File {
  file_id: string; // UUID
  file_name: string;
  original_name: string;
  title?: string;
  description?: string;
  content_hash: string;
  content_hash_algorithm: string;
  mime_type: string;
  file_size_bytes: number;
  file_extension?: string;
  uploaded_by?: string;
  uploaded_at: string;
  upload_source?: string;
  is_public: boolean;
  access_level: "private" | "team" | "public";
  tags?: string[];
  metadata?: Record<string, any>;
  custom_fields?: Record<string, any>;
  storage_provider?: string;
  storage_path?: string;
  storage_metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface FileRelationship {
  relationship_id: string; // UUID
  file_id: string; // UUID
  target_table_name: string; // e.g., 'deals', 'contacts', 'companies'
  target_column_name: string; // e.g., 'deal_id', 'contact_id', 'id'
  target_row_id: string; // The actual row ID value as text
  relationship_type?: string; // 'attachment', 'primary', 'secondary', etc.
  relationship_title?: string;
  relationship_notes?: string;
  display_order?: number;
  is_primary?: boolean;
  created_at: string;
  updated_at: string;
}

export interface FileWithRelationships extends File {
  relationship_count?: number;
  linked_table_names?: string[];
}

export interface FileUploadRequest {
  original_name: string;
  file_name?: string;
  title?: string;
  description?: string;
  mime_type?: string;
  uploaded_by?: string;
  upload_source?: string;
  is_public?: boolean;
  access_level?: "private" | "team" | "public";
  tags?: string[];
  metadata?: Record<string, any>;
  custom_fields?: Record<string, any>;
}

export interface FileRelationshipRequest {
  target_table_name: string;
  target_column_name: string;
  target_row_id: string;
  relationship_type?: string;
  relationship_title?: string;
  relationship_notes?: string;
  display_order?: number;
  is_primary?: boolean;
}

export interface FileSearchFilters {
  file_name?: string;
  original_name?: string;
  mime_type?: string;
  uploaded_by?: string;
  access_level?: "private" | "team" | "public";
  tags?: string[];
  uploaded_after?: string;
  uploaded_before?: string;
  file_size_min?: number;
  file_size_max?: number;
  has_relationships?: boolean;
  target_table_name?: string;
  storage_provider?: string;
}

export interface FileSearchResult {
  files: FileWithRelationships[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface DuplicateFile {
  content_hash: string;
  duplicate_count: number;
  file_ids: string[];
  file_names: string[];
  first_uploaded: string;
  last_uploaded: string;
}

export interface TableFileQuery {
  table_name: string;
  column_name: string;
  row_id: string;
}

export interface FileRelationshipQuery {
  file_id: string;
}

// API Response types
export interface FileUploadResponse {
  success: boolean;
  file?: File;
  relationship?: FileRelationship;
  message?: string;
  is_duplicate?: boolean;
  existing_file?: File;
}

export interface FileListResponse {
  success: boolean;
  data?: FileSearchResult;
  message?: string;
}

export interface FileDetailResponse {
  success: boolean;
  file?: File;
  relationships?: FileRelationship[];
  message?: string;
}

export interface FileDeleteResponse {
  success: boolean;
  message?: string;
  deleted_count?: number;
}

// Utility types for validation
export interface TableColumnValidation {
  table_name: string;
  column_name: string;
  is_valid: boolean;
}

// Types for file processing
export interface FileProcessingOptions {
  validate_table_column?: boolean;
  allow_duplicates?: boolean;
  auto_generate_title?: boolean;
  preserve_original_name?: boolean;
}

// Types for bulk operations
export interface BulkFileUploadRequest {
  files: FileUploadRequest[];
  relationships?: FileRelationshipRequest[];
  options?: FileProcessingOptions;
}

export interface BulkFileUploadResponse {
  success: boolean;
  uploaded_files: File[];
  failed_uploads: Array<{
    original_name: string;
    error: string;
  }>;
  total_uploaded: number;
  total_failed: number;
}
