// Types for the duplicate detection system

export interface CompanyNormalizedData {
  id: number;
  company_id: number;
  normalized_name: string | null;
  normalized_domain: string | null;
  name_tokens: string[];
  industry_normalized: string | null;
  phone_normalized: string | null;
  website_normalized: string | null;
  address_normalized: string | null;
  created_at: string;
  updated_at: string;
}

export interface ContactNormalizedData {
  id: number;
  contact_id: number;
  full_name_normalized: string | null;
  first_name_normalized: string | null;
  last_name_normalized: string | null;
  email_normalized: string | null;
  additional_email_normalized: string | null;
  email_domain: string | null;
  linkedin_handle: string | null;
  phone_normalized: string | null;
  name_tokens: string[];
  company_name_normalized: string | null;
  title_normalized: string | null;
  created_at: string;
  updated_at: string;
}

export type DuplicateRecordType = 'company' | 'contact';

export type DuplicateMatchType = 
  | 'exact_domain' 
  | 'exact_name' 
  | 'phone_match'
  | 'exact_email'
  | 'linkedin_match'
  | 'email_domain_name'
  | 'name_company'
  | 'similarity'
  | 'fuzzy_name';

export type DuplicateStatus = 'pending' | 'confirmed' | 'false_positive' | 'merged' | 'blocked';

export interface DuplicateRecord {
  id: number;
  record_type: DuplicateRecordType;
  primary_record_id: number;
  duplicate_record_id: number;
  match_type: DuplicateMatchType;
  confidence_score: number; // 0.0 to 1.0
  match_details: Record<string, any>;
  status: DuplicateStatus;
  created_at: string;
  resolved_at: string | null;
  resolved_by: string | null;
  resolution_notes: string | null;
}

// Extended types with actual record data
export interface CompanyDuplicateData extends DuplicateRecord {
  record_type: 'company';
  primary_company: {
    company_id: number;
    company_name: string;
    company_website?: string;
    industry?: string;
    company_phone?: string;
    company_address?: string;
    created_at: string;
  };
  duplicate_company: {
    company_id: number;
    company_name: string;
    company_website?: string;
    industry?: string;
    company_phone?: string;
    company_address?: string;
    created_at: string;
  };
}

export interface ContactDuplicateData extends DuplicateRecord {
  record_type: 'contact';
  primary_contact: {
    contact_id: number;
    full_name: string;
    email?: string;
    linkedin_url?: string;
    phone_number?: string;
    company_name?: string;
    job_title?: string;
    created_at: string;
  };
  duplicate_contact: {
    contact_id: number;
    full_name: string;
    email?: string;
    linkedin_url?: string;
    phone_number?: string;
    company_name?: string;
    job_title?: string;
    created_at: string;
  };
}

export type DuplicateDataWithRecords = CompanyDuplicateData | ContactDuplicateData;

// Duplicate detection criteria
export interface CompanyMatchCriteria {
  exactDomain?: boolean;
  exactName?: boolean;
  phoneMatch?: boolean;
  nameSimilarity?: boolean;
  minimumConfidence?: number;
  industryMatch?: boolean;
  addressSimilarity?: boolean;
}

export interface ContactMatchCriteria {
  exactEmail?: boolean;
  linkedinMatch?: boolean;
  phoneMatch?: boolean;
  emailDomainNameSimilarity?: boolean;
  nameCompanyMatch?: boolean;
  additionalEmailMatch?: boolean;
  minimumConfidence?: number;
}

// API request/response types
export interface DuplicateScanRequest {
  type: DuplicateRecordType | 'both';
  recordIds?: number[]; // Optional: scan specific records only
  criteria?: CompanyMatchCriteria | ContactMatchCriteria;
  batchSize?: number;
}

export interface DuplicateScanResponse {
  success: boolean;
  duplicates: {
    companies: CompanyDuplicateData[];
    contacts: ContactDuplicateData[];
  };
  stats: {
    scanned: number;
    duplicatesFound: number;
    processingTime: number;
  };
  error?: string;
}

export interface GetDuplicatesRequest {
  type?: DuplicateRecordType;
  status?: DuplicateStatus;
  matchType?: DuplicateMatchType;
  minConfidence?: number;
  maxConfidence?: number;
  page?: number;
  pageSize?: number;
  sortBy?: 'created_at' | 'confidence_score' | 'match_type';
  sortOrder?: 'asc' | 'desc';
}

export interface GetDuplicatesResponse {
  success: boolean;
  data: {
    duplicates: DuplicateDataWithRecords[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasMore: boolean;
    };
  };
  error?: string;
}

// Duplicate resolution types
export type DuplicateResolutionAction = 
  | 'merge' 
  | 'keep_separate' 
  | 'mark_false_positive'
  | 'swap_primary';

export interface MergeStrategy {
  primaryRecordId: number;
  fieldsToMerge?: string[]; // If not provided, merge all non-conflicting fields
  fieldResolutions?: Record<string, {
    action: 'keep_primary' | 'use_duplicate' | 'combine' | 'manual';
    value?: any; // For manual resolution
  }>;
  deleteOriginal?: boolean; // Whether to delete the duplicate record
}

export interface ResolveDuplicatesRequest {
  duplicateId: number;
  action: DuplicateResolutionAction;
  mergeStrategy?: MergeStrategy;
  notes?: string;
  resolvedBy?: string;
}

export interface ResolveDuplicatesResponse {
  success: boolean;
  result: {
    action: DuplicateResolutionAction;
    affectedRecords: number[];
    mergedRecordId?: number;
  };
  logs: string[];
  error?: string;
}

// Batch operations
export interface BatchDuplicateOperation {
  duplicateIds: number[];
  action: DuplicateResolutionAction;
  mergeStrategy?: Partial<MergeStrategy>; // Applied to all
  notes?: string;
}

export interface BatchOperationResponse {
  success: boolean;
  results: Array<{
    duplicateId: number;
    success: boolean;
    error?: string;
    affectedRecords?: number[];
  }>;
  stats: {
    processed: number;
    succeeded: number;
    failed: number;
  };
}

// Duplicate candidate (before being saved to duplicate_records table)
export interface DuplicateCandidate {
  primary_id: number;
  duplicate_id: number;
  match_type: DuplicateMatchType;
  confidence: number;
  match_details: Record<string, any>;
}

// Similarity matching parameters
export interface SimilarityConfig {
  nameThreshold: number; // 0.0 to 1.0 for string similarity
  addressThreshold: number;
  phoneEditDistance: number; // Maximum edit distance for phone numbers
  enablePhonetic: boolean; // Use phonetic matching for names
  enableFuzzy: boolean; // Use fuzzy string matching
  weightings?: {
    name: number;
    domain: number;
    phone: number;
    address: number;
    industry: number;
  };
}

// Preview types for UI
export interface DuplicatePreview {
  duplicate: DuplicateDataWithRecords;
  conflicts: Array<{
    field: string;
    primaryValue: any;
    duplicateValue: any;
    recommended: 'primary' | 'duplicate' | 'combine' | 'manual';
  }>;
  relatedRecords?: {
    contacts?: number; // Number of related contacts for companies
    companies?: number; // Number of related companies for contacts
    deals?: number; // Number of related deals
  };
}

// Statistics and analytics types
export interface DuplicateStats {
  totalDuplicates: number;
  byType: Record<DuplicateRecordType, number>;
  byMatchType: Record<DuplicateMatchType, number>;
  byStatus: Record<DuplicateStatus, number>;
  byConfidenceRange: Array<{
    range: string; // e.g., "0.8-0.9"
    count: number;
  }>;
  averageConfidence: number;
  oldestUnresolved?: string; // ISO date
}

// Job/batch processing types
export interface DuplicateDetectionJob {
  id: string;
  type: DuplicateRecordType | 'both';
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: {
    processed: number;
    total: number;
    duplicatesFound: number;
    currentStep?: string;
  };
  startedAt?: string;
  completedAt?: string;
  estimatedTimeRemaining?: number; // seconds
  error?: string;
  result?: DuplicateScanResponse;
}

// Configuration types
export interface DuplicateDetectionConfig {
  company: CompanyMatchCriteria;
  contact: ContactMatchCriteria;
  similarity: SimilarityConfig;
  processing: {
    batchSize: number;
    maxConcurrentJobs: number;
    timeoutSeconds: number;
  };
  ui: {
    defaultPageSize: number;
    showConfidenceScores: boolean;
    enableAutoMerge: boolean;
    autoMergeThreshold: number;
  };
}
