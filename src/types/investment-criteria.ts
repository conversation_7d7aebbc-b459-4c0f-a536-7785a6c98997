export interface InvestmentCriteria {
  criteria_id: number
  entity_type: string
  entity_id: string
  entity_name?: string
  entity_location?: string
  entity_website?: string
  entity_industry?: string
  
  // Financial Metrics
  target_return?: number
  minimum_deal_size?: number
  maximum_deal_size?: number
  historical_irr?: number
  historical_em?: number
  
  // Hold Period
  min_hold_period?: number
  max_hold_period?: number
  
  // Property & Assets
  property_types?: string[]
  property_sub_categories?: string[]
  strategies?: string[]
  financial_products?: string[]
  
  // Capital & Financing
  capital_position?: string[]
  capital_source?: string
  loan_type?: string[]
  loan_type_normalized?: string[]
  loan_program?: string[]
  structured_loan_tranche?: string[]
  recourse_loan?: string[]
  
  // Loan Terms
  min_loan_term?: number
  max_loan_term?: number
  interest_rate?: number
  interest_rate_sofr?: number
  interest_rate_wsj?: number
  interest_rate_prime?: number
  
  // LTV/LTC
  loan_to_value_min?: number
  loan_to_value_max?: number
  loan_to_cost_min?: number
  loan_to_cost_max?: number
  
  // Fees
  loan_origination_fee_min?: number
  loan_origination_fee_max?: number
  loan_exit_fee_min?: number
  loan_exit_fee_max?: number
  
  // DSCR
  min_loan_dscr?: number
  max_loan_dscr?: number
  
  // Geography
  country?: string[]
  region?: string[]
  state?: string[]
  city?: string[]
  
  // Timeline
  closing_time_weeks?: number
  min_closing_time_weeks?: number
  max_closing_time_weeks?: number
  
  // Additional interest rates
  interest_rate_libor?: number
  interest_rate_5yt?: number
  interest_rate_10yt?: number
  
  // Additional fields
  loan_term_string?: string
  location_focus?: string[]
  notes?: string
  tear_sheet?: any
  
  // Extra
  extra_fields?: any
  
  // Meta
  created_at?: string
  updated_at?: string
  created_by?: string
  updated_by?: string
  is_active?: boolean
}

export interface InvestmentCriteriaFilters {
  // Pagination
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'

  // Universal Search
  searchTerm?: string
  entityType?: string
  entityId?: string
  entityName?: string
  criteriaId?: number

  // Financial Metrics - Deal Size
  dealSizeMin?: number  // minimum_deal_size
  dealSizeMax?: number  // maximum_deal_size

  // Returns & Performance
  targetReturnMin?: number
  targetReturnMax?: number
  historicalIrrMin?: number
  historicalIrrMax?: number
  historicalEmMin?: number
  historicalEmMax?: number

  // Hold Period - single values, not ranges
  minHoldPeriod?: number        // min_hold_period (exact match or >=)
  maxHoldPeriod?: number        // max_hold_period (exact match or <=)

  // Property & Asset Structure
  propertyTypes?: string[]           // property_types array
  propertySubcategories?: string[]   // property_sub_categories array
  strategies?: string[]              // strategies array
  financialProducts?: string[]       // financial_products array

  // Capital & Financing Structure
  capitalPosition?: string[]         // capital_position array
  capitalSource?: string             // capital_source text
  sourceType?: string[]              // source_type array (V2 NSF fields)
  loanTypes?: string[]              // loan_type array
  loanTypesNormalized?: string[]    // loan_type_normalized array
  
  // Deal Status & Stage
  dealStatus?: string[]             // deal status from V1/V2 deals
  dealStage?: string[]              // deal stage from V1/V2 deals
  dealAmount?: number               // single deal amount (replaces min/max)
  loanProgram?: string[]            // loan_program array
  structuredLoanTranche?: string[]  // structured_loan_tranche array
  recourseLoan?: string[]           // recourse_loan array

  // Loan Terms & Conditions - single values, not ranges
  minLoanTerm?: number              // min_loan_term (exact match or >=)
  maxLoanTerm?: number              // max_loan_term (exact match or <=)
  interestRateMin?: number          // Range filter for interest_rate
  interestRateMax?: number
  interestRateSofrMin?: number      // Range filter for interest_rate_sofr
  interestRateSofrMax?: number
  interestRateWsjMin?: number       // Range filter for interest_rate_wsj
  interestRateWsjMax?: number
  interestRatePrimeMin?: number     // Range filter for interest_rate_prime
  interestRatePrimeMax?: number

  // LTV/LTC Ratios - both min and max values
  loanToValueMin?: number           // loan_to_value_min (database field)
  loanToValueMax?: number           // loan_to_value_max (database field)
  loanToCostMin?: number            // loan_to_cost_min (database field) 
  loanToCostMax?: number            // loan_to_cost_max (database field)

  // Fees & Costs - both min and max values
  loanOriginationFeeMin?: number    // loan_origination_fee_min (database field)
  loanOriginationFeeMax?: number    // loan_origination_fee_max (database field)
  loanExitFeeMin?: number           // loan_exit_fee_min (database field)
  loanExitFeeMax?: number           // loan_exit_fee_max (database field)

  // DSCR (Debt Service Coverage Ratio) - single values, not ranges
  minLoanDscr?: number              // min_loan_dscr (exact match or >=)
  maxLoanDscr?: number              // max_loan_dscr (exact match or <=)

  // Geographic Focus
  countries?: string[]              // country array
  regions?: string[]                // region array
  states?: string[]                 // state array
  cities?: string[]                 // city array

  // Timeline & Processing - single value, not range
  closingTimeWeeks?: number         // closing_time_weeks (exact match or <=)

  // Status & Meta
  isActive?: boolean
  isRequested?: boolean
  createdBy?: string
  updatedBy?: string
  createdAtFrom?: string
  createdAtTo?: string
  updatedAtFrom?: string
  updatedAtTo?: string
}

// Hierarchical mapping structure for MCP-based filtering
export interface HierarchicalMapping {
  value: string
  children: Record<string, string[]>
}

export interface HierarchicalMappings {
  [type: string]: {
    flat: string[]
    hierarchical: Record<string, HierarchicalMapping>
  }
}

export interface FilterOptions {
  // Basic Arrays
  entityTypes: string[]
  propertyTypes: string[]
  propertySubcategories: string[]
  strategies: string[]                // All strategies from central mapping + database
  strategiesNormalized: string[]      // Normalized strategies from central mapping
  financialProducts: string[]
  capitalPositions: string[]
  capitalSources: string[]
  loanTypes: string[]
  loanTypesNormalized: string[]
  loanPrograms: string[]
  structuredLoanTranches: string[]
  recourseLoans: string[]
  countries: string[]
  regions: string[]
  states: string[]
  cities: string[]
  createdBy: string[]
  updatedBy: string[]

  // Hierarchical Mappings
  hierarchicalMappings?: {
    [key: string]: {
      hierarchical: {
        [parent: string]: {
          children: {
            [childType: string]: string[]
          }
        }
      }
    }
  }

  // Numerical Ranges (for display/validation)
  dealSizeRange: { min: number; max: number }
  targetReturnRange: { min: number; max: number }
  historicalIrrRange: { min: number; max: number }
  historicalEmRange: { min: number; max: number }
  holdPeriodRange: { min: number; max: number }
  loanTermRange: { min: number; max: number }
  interestRateRange: { min: number; max: number }
  ltvRange: { min: number; max: number }
  ltcRange: { min: number; max: number }
  dscrRange: { min: number; max: number }
  feeRange: { min: number; max: number }
  closingTimeRange: { min: number; max: number }
}

export interface InvestmentCriteriaResponse {
  data: InvestmentCriteria[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  filters: InvestmentCriteriaFilters
}

export interface SortOption {
  value: string
  label: string
}

export const SORT_OPTIONS = [
  // Default sorting
  { label: 'Updated Date (Newest First)', value: 'updated_at' },
  
  // Financial metrics
  { label: 'Target Return (Highest First)', value: 'target_return' },
  { label: 'Historical IRR (Highest First)', value: 'historical_irr' },
  { label: 'Historical EM (Highest First)', value: 'historical_em' },
  
  // Deal size
  { label: 'Min Deal Size (Largest First)', value: 'minimum_deal_size' },
  { label: 'Max Deal Size (Largest First)', value: 'maximum_deal_size' },
  
  // Loan terms
  { label: 'Interest Rate (Highest First)', value: 'interest_rate' },
  { label: 'Min Loan Term (Longest First)', value: 'min_loan_term' },
  { label: 'Max Loan Term (Longest First)', value: 'max_loan_term' },
  
  // LTV/LTC ratios
  { label: 'Max LTV (Highest First)', value: 'loan_to_value_max' },
  { label: 'Min LTV (Lowest First)', value: 'loan_to_value_min' },
  { label: 'Max LTC (Highest First)', value: 'loan_to_cost_max' },
  { label: 'Min LTC (Lowest First)', value: 'loan_to_cost_min' },
  
  // Hold period
  { label: 'Min Hold Period (Shortest First)', value: 'min_hold_period' },
  { label: 'Max Hold Period (Longest First)', value: 'max_hold_period' },
  
  // Timeline
  { label: 'Closing Time (Fastest First)', value: 'closing_time_weeks' },
  
  // DSCR
  { label: 'Min DSCR (Highest First)', value: 'min_loan_dscr' },
  { label: 'Max DSCR (Highest First)', value: 'max_loan_dscr' },
  
  // Fees
  { label: 'Max Origination Fee (Highest First)', value: 'loan_origination_fee_max' },
  { label: 'Max Exit Fee (Highest First)', value: 'loan_exit_fee_max' },
] as const

export const DEFAULT_FILTERS: InvestmentCriteriaFilters = {
  page: 1,
  limit: 25,
  sortBy: 'created_at',
  sortOrder: 'desc'
} 